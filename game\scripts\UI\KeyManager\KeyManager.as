package UI.KeyManager
{
   import flash.display.Stage;
   import flash.events.EventDispatcher;
   import flash.events.KeyboardEvent;
   
   public class KeyManager extends EventDispatcher
   {
      public static const ALPHA_NUMERIC_KEY_DOWN:String = "alpha_numeric_key_down";
      
      private var _combos:Array;
      
      private var _stage:Stage;
      
      public function KeyManager()
      {
         super();
         _combos = [];
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         removeKeyEventListener();
         if(_combos)
         {
            _loc1_ = int(_combos.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(_combos[_loc2_])
               {
                  _combos[_loc2_].clear();
               }
               _combos[_loc2_] = null;
               _loc2_++;
            }
         }
         _stage = null;
      }
      
      public function addKeyEventListener(param1:Stage) : void
      {
         _stage = param1;
         param1.addEventListener("keyDown",keyDown,false,0,true);
         param1.addEventListener("keyUp",keyUp,false,0,true);
      }
      
      public function removeKeyEventListener() : void
      {
         _stage.removeEventListener("keyDown",keyDown,false);
         _stage.removeEventListener("keyUp",keyUp,false);
         _stage = null;
      }
      
      public function createKeyCombo(param1:Function, param2:Array, param3:Function, param4:Array, param5:Function, param6:Array, ... rest) : KeyCombo
      {
         var _loc10_:int = 0;
         var _loc9_:Key = null;
         var _loc8_:KeyCombo = new KeyCombo();
         _loc10_ = 0;
         while(_loc10_ < rest.length)
         {
            _loc9_ = new Key(rest[_loc10_]);
            _loc8_.addKey(_loc9_);
            _loc8_.addComboKeyResponseFun(param1,param2,param3,param4,param5,param6);
            _loc10_++;
         }
         _combos.push(_loc8_);
         return _loc8_;
      }
      
      public function removeKeyCombo(param1:KeyCombo) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < _combos.length)
         {
            if(_combos[_loc2_] == param1)
            {
               _combos[_loc2_].clear();
               _combos.splice(_loc2_,1);
               break;
            }
            _loc2_++;
         }
      }
      
      private function keyDown(param1:KeyboardEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:KeyCombo = null;
         _loc3_ = 0;
         while(_loc3_ < _combos.length)
         {
            _loc2_ = _combos[_loc3_];
            _loc2_.keyDown(param1.keyCode);
            _loc3_++;
         }
         trace("收到按按键事件！");
      }
      
      private function keyUp(param1:KeyboardEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:KeyCombo = null;
         _loc3_ = 0;
         while(_loc3_ < _combos.length)
         {
            _loc2_ = _combos[_loc3_];
            _loc2_.keyUp(param1.keyCode);
            _loc3_++;
         }
         trace("收到释放按键事件！");
      }
   }
}

