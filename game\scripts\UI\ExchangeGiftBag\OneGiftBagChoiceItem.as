package UI.ExchangeGiftBag
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class OneGiftBagChoiceItem
   {
      private var m_getGiftBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      private var m_show:MovieClip;
      
      private var m_giftBagChoicePanel:GiftBagChoicePanel;
      
      public function OneGiftBagChoiceItem()
      {
         super();
         m_getGiftBtn = new ButtonLogicShell2();
         m_equipments = new Vector.<Equipment>();
         m_eqCells = new Vector.<Sprite>();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_getGiftBtn);
         m_getGiftBtn = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         m_show = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         m_giftBagChoicePanel = null;
      }
      
      public function setShow(param1:MovieClip, param2:GiftBagChoicePanel) : void
      {
         m_show = param1;
         m_giftBagChoicePanel = param2;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         initShow();
         initShow2();
      }
      
      public function setEquipmentVOsXML(param1:XML) : void
      {
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,true);
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_getGiftBtn === _loc2_)
         {
            m_giftBagChoicePanel.getOneGiftBag(m_equipmentVOs);
         }
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Equipment = null;
         if(m_show == null || m_equipmentVOs == null)
         {
            return;
         }
         ClearUtil.clearObject(m_equipments);
         m_equipments.length = 0;
         var _loc1_:int = int(m_equipmentVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(m_equipmentVOs[_loc3_]);
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            if(_loc3_ < m_eqCells.length)
            {
               m_eqCells[_loc3_].addChild(_loc2_);
            }
            m_equipments.push(_loc2_);
            _loc3_++;
         }
      }
      
      private function initShow() : void
      {
         m_getGiftBtn.setShow(m_show["getGiftBtn"]);
         m_eqCells.push(m_show["eq1Container1"]);
         m_eqCells.push(m_show["eq1Container2"]);
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

