package UI2.Mount.MountData
{
   public class MountVOListener implements IMountVOListener
   {
      public var changeDataFun:Function;
      
      public function MountVOListener()
      {
         super();
      }
      
      public function clear() : void
      {
         changeDataFun = null;
      }
      
      public function changeData(param1:MountVO) : void
      {
         if(<PERSON><PERSON>an(changeDataFun))
         {
            changeDataFun(param1);
         }
      }
   }
}

