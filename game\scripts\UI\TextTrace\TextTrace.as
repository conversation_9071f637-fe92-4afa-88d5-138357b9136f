package UI.TextTrace
{
   import UI.Button.QuitBtn;
   import UI.GamingUI;
   import UI.MySprite;
   import UI.ScrollBar;
   import com.greensock.TweenLite;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TextTrace extends MySprite
   {
      public var dataLayer:DataLayer;
      
      public var dataMask:Sprite;
      
      public var slider:Sprite;
      
      public var downControl:Sprite;
      
      public var upControl:Sprite;
      
      public var scroll_bg:Sprite;
      
      public var quitBtn:QuitBtn;
      
      private var _myScroll:ScrollBar;
      
      private var _smallRectangle:SmallRectangle;
      
      public function TextTrace()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         dataLayer.clear();
         dataLayer = null;
         dataMask = null;
         slider = null;
         downControl = null;
         upControl = null;
         scroll_bg = null;
         quitBtn.removeEventListener("click",hideTextTrace,false);
         quitBtn = null;
      }
      
      public function appendText(param1:String) : void
      {
         dataLayer.traceText.appendText(param1);
         dataLayer.traceText.height = Math.max(256,dataLayer.traceText.textHeight + 5);
         _myScroll.refresh();
      }
      
      private function init() : void
      {
         _myScroll = new ScrollBar(dataLayer,dataMask,slider,scroll_bg);
         _myScroll.direction = "L";
         _myScroll.tween = 5;
         _myScroll.elastic = false;
         _myScroll.lineAbleClick = true;
         _myScroll.mouseWheel = true;
         _myScroll.UP = upControl;
         _myScroll.DOWN = downControl;
         _myScroll.stepNumber = 15;
         _myScroll.refresh();
         dataLayer.traceText.text = "";
         dataLayer.traceText.height = Math.max(256,dataLayer.traceText.textHeight + 5);
         quitBtn = new QuitBtn();
         quitBtn.addEventListener("click",hideTextTrace,false,0,true);
         quitBtn.x = 390;
         quitBtn.y = -26;
         addChild(quitBtn);
         _smallRectangle = new SmallRectangle();
      }
      
      private function hideTextTrace(param1:MouseEvent) : void
      {
         visible = false;
         _smallRectangle.x = 940;
         _smallRectangle.y = 540;
         if(!GamingUI.getInstance().getChildByName(_smallRectangle.name))
         {
            GamingUI.getInstance().addChild(_smallRectangle);
         }
         _smallRectangle.visible = true;
         TweenLite.from(_smallRectangle,1,{
            "x":GamingUI.getInstance().textTrace.x,
            "y":GamingUI.getInstance().textTrace.y,
            "width":GamingUI.getInstance().textTrace.width,
            "height":GamingUI.getInstance().textTrace.height
         });
      }
   }
}

