package UI.RecaptureGold.Props
{
   import UI.MyFunction2;
   import UI.RecaptureGold.Hook.Hook;
   import UI.RecaptureGold.Hook.HookMachine;
   import UI.RecaptureGold.Parent.Prop;
   import UI.RecaptureGold.RecaptureGoldFunction;
   import UI.SoundManager.SoundManager;
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import flash.utils.getQualifiedClassName;
   
   public class Prop_Bomb extends Prop
   {
      public function Prop_Bomb()
      {
         super();
      }
      
      public function bombHook(param1:XML, param2:HookMachine, param3:Hook, param4:SoundManager, param5:Function, param6:Array, param7:Function) : void
      {
         var _loc10_:String = getQualifiedClassName(this);
         var _loc8_:XML = param1.PropData.item.(@classNameForShow == _loc10_)[0];
         var _loc11_:String = String(_loc8_.@animationBombHook);
         var _loc9_:MovieClip = MyFunction2.returnShowByClassName(_loc8_.@animationFlyToHook) as MovieClip;
         var _loc12_:Point = param3.globalToLocal(param2.parent.localToGlobal(new Point(param2.x,param2.y)));
         param3.addChild(_loc9_);
         TweenLite.from(_loc9_,0.5,{
            "x":_loc12_.x,
            "y":_loc12_.y,
            "ease":Linear.easeNone,
            "onComplete":flyToHookComplete,
            "onCompleteParams":[param1,param3,param4,_loc11_,_loc9_,param5,param6,param7]
         });
         trace("炸弹物品");
      }
      
      private function flyToHookComplete(param1:XML, param2:Hook, param3:SoundManager, param4:String, param5:MovieClip, param6:Function, param7:Array, param8:Function) : void
      {
         var animationBombHook:MovieClip;
         var hookPParent:DisplayObject;
         var point:Point;
         var soundStr:String;
         var soundTransform:SoundTransform;
         var recaptureGoldXML:XML = param1;
         var hook:Hook = param2;
         var soundManager:SoundManager = param3;
         var animationBombHookClassName:String = param4;
         var animationFlyToHook:MovieClip = param5;
         var fun:Function = param6;
         var funParams:Array = param7;
         var shakeFun:Function = param8;
         var playerOver:* = function(param1:Event):void
         {
            param1.target.removeEventListener("playerOver",playerOver,false);
         };
         var shake:* = function(param1:Event):void
         {
            param1.target.removeEventListener("explodeShake",shake,false);
            if(Boolean(fun))
            {
               fun.apply(null,funParams);
            }
            if(Boolean(shakeFun))
            {
               shakeFun();
            }
         };
         if(animationFlyToHook.parent)
         {
            animationFlyToHook.parent.removeChild(animationFlyToHook);
         }
         animationBombHook = MyFunction2.returnShowByClassName(animationBombHookClassName) as MovieClip;
         hook.addChild(animationBombHook);
         animationBombHook.addEventListener("playerOver",playerOver,false);
         animationBombHook.addEventListener("explodeShake",shake,false);
         if(Boolean(hook.parent) && hook.parent.parent)
         {
            hookPParent = hook.parent.parent;
         }
         point = new Point(hook.x,hook.y);
         soundStr = String(recaptureGoldXML.SoundData.@soundBombForPropBomb);
         soundTransform = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,point,hookPParent);
         soundManager.play(soundStr,0,0,soundTransform);
         hook.hookItem = null;
      }
   }
}

