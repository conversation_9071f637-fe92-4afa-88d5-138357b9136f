package UI2.SmallAssistant
{
   import UI.GamingUI;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SmallAssistant.ActiveTask.ActiveTaskRewardSaveData;
   import UI2.SmallAssistant.ActiveTask.HaveActiveValueTaskData;
   import UI2.XydzjsSkipLogic.XydzjsSkipLogic;
   import UI2.XydzjsSkipLogic.XydzjsSkipLogicListener;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class ActiveTaskShowPart
   {
      private var m_activeValueBar:CMSXChangeBarLogicShell;
      
      private var m_activeTaskLines:Vector.<ActiveTaskLine>;
      
      private var m_activeRewardShows:Vector.<ActiveRewardShow>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_xydzjsSkipLogic:XydzjsSkipLogic;
      
      private var m_xydzjsSkipLogicListener:XydzjsSkipLogicListener;
      
      private var m_show:MovieClip;
      
      private var m_smallAssistantData:SmallAssistantData;
      
      private var m_smallAssistantPanel:SmallAssistantPanel;
      
      public function ActiveTaskShowPart()
      {
         super();
         m_activeValueBar = new CMSXChangeBarLogicShell();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_xydzjsSkipLogic = new XydzjsSkipLogic();
         m_xydzjsSkipLogicListener = new XydzjsSkipLogicListener();
         m_xydzjsSkipLogicListener.skipSuccessFun = skipSuccess;
         m_xydzjsSkipLogicListener.skipFailFun = skipFail;
         m_xydzjsSkipLogic.addSkipListener(m_xydzjsSkipLogicListener);
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_activeValueBar);
         m_activeValueBar = null;
         ClearUtil.clearObject(m_activeTaskLines);
         m_activeTaskLines = null;
         ClearUtil.clearObject(m_activeRewardShows);
         m_activeRewardShows = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_xydzjsSkipLogic);
         m_xydzjsSkipLogic = null;
         ClearUtil.clearObject(m_xydzjsSkipLogicListener);
         m_xydzjsSkipLogicListener = null;
         m_show = null;
         m_smallAssistantData = null;
         m_smallAssistantPanel = null;
      }
      
      public function init(param1:MovieClip, param2:SmallAssistantData, param3:SmallAssistantPanel) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_smallAssistantData = param2;
         m_smallAssistantPanel = param3;
         initShow();
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:ActiveTaskRewardSaveData = null;
         var _loc5_:* = param1.button;
         if(m_pageBtnGroup === _loc5_)
         {
            arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_activeTaskLines.length);
         }
         _loc2_ = !!m_activeRewardShows ? m_activeRewardShows.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(param1.button == m_activeRewardShows[_loc4_].getGetRewardBtn())
            {
               if(m_getEquipmentVOsLogic.getEquipmentVOs(m_activeRewardShows[_loc4_].getActiveTaskRewardData(),GamingUI.getInstance().player1))
               {
                  _loc3_ = new ActiveTaskRewardSaveData();
                  _loc3_.setRewardId(m_activeRewardShows[_loc4_].getActiveTaskRewardData().getId());
                  m_smallAssistantData.getActiveTasksSaveData().addGotRewardData(_loc3_);
                  refreshActiveTaskRewardShowsState();
                  m_smallAssistantPanel.refreshNumTipShow();
                  GamingUI.getInstance().showMessageTip("领取成功");
               }
               else
               {
                  GamingUI.getInstance().showMessageTip("背包已满");
               }
               return;
            }
            _loc4_++;
         }
         _loc2_ = !!m_activeTaskLines ? m_activeTaskLines.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(param1.button == m_activeTaskLines[_loc4_].getGotoBtn())
            {
               if(m_activeTaskLines[_loc4_].getHaveActiveValueTaskData().getActiveValueDataOfTask().getSkipStr())
               {
                  m_xydzjsSkipLogic.skip(m_activeTaskLines[_loc4_].getHaveActiveValueTaskData().getActiveValueDataOfTask().getSkipStr());
               }
               return;
            }
            _loc4_++;
         }
      }
      
      private function initShow() : void
      {
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc6_:ActiveTaskLine = null;
         var _loc2_:ActiveRewardShow = null;
         m_activeValueBar.setShow(m_show["activeValueBar"]);
         _loc4_ = m_show.numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc1_ = m_show.getChildAt(_loc7_);
            if(_loc1_.name.substr(0,8) == "taskLine")
            {
               _loc3_++;
            }
            else if(_loc1_.name.substr(0,11) == "rewardShow_")
            {
               _loc5_++;
            }
            _loc7_++;
         }
         ClearUtil.clearObject(m_activeTaskLines);
         m_activeTaskLines = new Vector.<ActiveTaskLine>();
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = new ActiveTaskLine();
            _loc6_.setShow(m_show["taskLine_" + (_loc7_ + 1)]);
            m_activeTaskLines.push(_loc6_);
            _loc7_++;
         }
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         ClearUtil.clearObject(m_activeRewardShows);
         m_activeRewardShows = new Vector.<ActiveRewardShow>();
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc2_ = new ActiveRewardShow();
            _loc2_.setShow(m_show["rewardShow_" + (_loc7_ + 1)]);
            m_activeRewardShows.push(_loc2_);
            _loc7_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(m_show == null || m_smallAssistantData == null)
         {
            return;
         }
         m_activeValueBar.change(m_smallAssistantData.getCurrentActiveValue() / m_smallAssistantData.getTotalActiveValue());
         m_activeValueBar.setDataShow(m_smallAssistantData.getCurrentActiveValue() + "/" + m_smallAssistantData.getTotalActiveValue());
         setPageBtn(1);
         arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_activeTaskLines.length);
         _loc1_ = int(m_activeRewardShows.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_loc2_ < m_smallAssistantData.getActiveTaskRewardDataNum())
            {
               m_activeRewardShows[_loc2_].setEquipmentVOs(m_smallAssistantData.getActiveTaskRewardDataByIndex(_loc2_));
            }
            _loc2_++;
         }
         refreshActiveTaskRewardShowsState();
      }
      
      private function refreshActiveTaskRewardShowsState() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_activeRewardShows.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_activeRewardShows[_loc2_].getActiveTaskRewardData())
            {
               if(m_smallAssistantData.getIsGotActiveRewardbyId(m_activeRewardShows[_loc2_].getActiveTaskRewardData().getId()))
               {
                  m_activeRewardShows[_loc2_].tranToGotReward();
               }
               else if(m_activeRewardShows[_loc2_].getActiveTaskRewardData().getNeedMinActiveValue() <= m_smallAssistantData.getCurrentActiveValue())
               {
                  m_activeRewardShows[_loc2_].tranToAbleGet();
               }
               else
               {
                  m_activeRewardShows[_loc2_].tranToNotAbleGet();
               }
            }
            _loc2_++;
         }
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_smallAssistantData == null || m_smallAssistantData.getHaveActiveValueTaskDataNum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_smallAssistantData.getHaveActiveValueTaskDataNum() % m_activeTaskLines.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_smallAssistantData.getHaveActiveValueTaskDataNum() / m_activeTaskLines.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_smallAssistantData.getHaveActiveValueTaskDataNum() / m_activeTaskLines.length) + 1);
         }
      }
      
      private function arrangeActiveTaskLineShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc2_:HaveActiveValueTaskData = null;
         var _loc5_:int = param1 + m_activeTaskLines.length;
         var _loc3_:int = !!m_smallAssistantData ? m_smallAssistantData.getHaveActiveValueTaskDataNum() : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc3_)
         {
            _loc2_ = m_smallAssistantData.getHaveActiveValueTaskDataByIndex(_loc6_);
            m_activeTaskLines[_loc4_].setHaveActiveValueTaskData(_loc2_);
            m_activeTaskLines[_loc4_].getShow().visible = true;
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_activeTaskLines.length)
         {
            m_activeTaskLines[_loc4_].getShow().visible = false;
            _loc4_++;
         }
      }
      
      private function skipSuccess(param1:XydzjsSkipLogic, param2:String) : void
      {
      }
      
      private function skipFail(param1:XydzjsSkipLogic, param2:String) : void
      {
         GamingUI.getInstance().showMessageTip(param2);
      }
   }
}

