package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddRiotSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addRiot:Number;
      
      private var m_addDodge:Number;
      
      private var m_addValue2:Number;
      
      public function AddRiotSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("riot_mountAdd1",m_targetPlayer.playerVO.get2("riot_mountAdd1") - m_addValue);
         m_targetPlayer.playerVO.set2("dodge_mountAdd1",m_targetPlayer.playerVO.get2("dodge_mountAdd1") - m_addValue2);
         m_addValue = 0;
         m_addValue2 = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addRiot;
         m_addValue2 = addDodge;
         m_targetPlayer.playerVO.set2("riot_mountAdd1",m_targetPlayer.playerVO.get2("riot_mountAdd1") + m_addValue);
         m_targetPlayer.playerVO.set2("dodge_mountAdd1",m_targetPlayer.playerVO.get2("dodge_mountAdd1") + m_addValue2);
      }
      
      public function getAddRiot() : Number
      {
         return addRiot;
      }
      
      public function getAddDodge() : Number
      {
         return addDodge;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addRiot = Number(param1.data.(@att == "addRiot")[0].@value);
         this.addDodge = Number(param1.data.(@att == "addDodge")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.m_addRiot = m_addRiot;
         _antiwear.m_addDodge = m_addDodge;
      }
      
      private function get addRiot() : Number
      {
         return _antiwear.m_addRiot;
      }
      
      private function set addRiot(param1:Number) : void
      {
         _antiwear.m_addRiot = param1;
      }
      
      private function get addDodge() : Number
      {
         return _antiwear.m_addDodge;
      }
      
      private function set addDodge(param1:Number) : void
      {
         _antiwear.m_addDodge = param1;
      }
   }
}

