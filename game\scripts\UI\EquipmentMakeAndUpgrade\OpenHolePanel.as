package UI.EquipmentMakeAndUpgrade
{
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class OpenHolePanel extends MySprite implements IEquipmentProcessPanel
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _oldContainer:Sprite;
      
      private var _materialContainer:Sprite;
      
      private var _luckStoneContainer:Sprite;
      
      private var _newContainer:Sprite;
      
      private var _numBtnGroup:NumberBtnGroupLogicShell;
      
      private var _successRateText:TextField;
      
      private var _moneyText:TextField;
      
      private var _haveNumText:TextField;
      
      private var _needNumText:TextField;
      
      private var _startOpenHoleBtn:ButtonLogicShell2;
      
      private var _successAnimation:MovieClipPlayLogicShell;
      
      private var _failAnimation:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["openHolePanel"];
      
      private var _openHoleXML:XML;
      
      private var _oldEquipment:Equipment;
      
      private var _newEquipment:Equipment;
      
      private var _player:Player;
      
      private var _baseSuccessRate:Number;
      
      private var _successRate:Number;
      
      private var _needMoney:int;
      
      private var _needIndex:int;
      
      private var _openHoleNum:int;
      
      private var _needMaterial:Equipment;
      
      private var _needMaterialNum:int;
      
      private var _currentOpenHoleXML:XML;
      
      private var _luckStoneShow:LuckStoneShow;
      
      private var _buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var _buyLuckStoneGuideBtn:BuyMaterialGuideBtn;
      
      private var _buyGoldPocketBtn:BuyGoldPocketGuideBtn;
      
      private var _MyControlPanel:MyControlPanel;
      
      public function OpenHolePanel(param1:MyControlPanel = null)
      {
         super();
         _MyControlPanel = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         ClearUtil.clearDisplayObjectInContainer(_oldContainer);
         _oldContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_materialContainer);
         _materialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer);
         _luckStoneContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         _successRateText = null;
         _moneyText = null;
         _haveNumText = null;
         _needNumText = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         _failAnimation = null;
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         _openHoleXML = null;
         if(_oldEquipment)
         {
            _oldEquipment.clear();
         }
         _oldEquipment = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         _player = null;
         if(_needMaterial)
         {
            _needMaterial.clear();
         }
         _needMaterial = null;
         _currentOpenHoleXML = null;
         _luckStoneShow = null;
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
      
      private function init() : void
      {
         _luckStoneShow = new LuckStoneShow();
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            MyFunction2.loadXMLFunction("openHole",function(param1:XML):void
            {
               _openHoleXML = param1;
               if(_show == null)
               {
                  _show = MyFunction2.returnShowByClassName("OpenHolePanel") as MovieClip;
               }
               addChild(_show);
               _showMC = new MovieClipPlayLogicShell();
               _showMC.setShow(_show);
               initNotPutInFrame();
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      public function putInEquipmentVO(param1:EquipmentVO, param2:Player) : void
      {
         if(!param1 is AbleEquipmentVO)
         {
            return;
         }
         if(param1 is PreciousEquipmentVO)
         {
            _needIndex = 1;
         }
         else
         {
            _needIndex = 0;
         }
         clearOldEquipment();
         _oldEquipment = MyFunction2.sheatheEquipmentShell(param1);
         _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
         _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
         _player = param2;
         var _loc4_:XML = getCurrentOpenHoleXML(_oldEquipment.equipmentVO);
         _openHoleNum = getOpenHoleIndexAndNum(_oldEquipment.equipmentVO as AbleEquipmentVO);
         _currentOpenHoleXML = _loc4_["hole" + (_openHoleNum + 1)][0];
         if(_currentOpenHoleXML == null)
         {
            _currentOpenHoleXML = _loc4_["hole1"][0];
         }
         _baseSuccessRate = Number(_currentOpenHoleXML.@successRate);
         _successRate = _baseSuccessRate;
         _needMoney = int(_currentOpenHoleXML.@needMoney);
         clearNeedMaterial();
         _needMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[_needIndex].@id,XMLSingle.getInstance().equipmentXML));
         _needMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needMaterialNum = int(_currentOpenHoleXML.material[_needIndex].@num);
         clearNewEquipment();
         _newEquipment = _oldEquipment.clone();
         _newEquipment.addEventListener("rollOver",onOver,false,0,true);
         _newEquipment.addEventListener("rollOut",onOut,false,0,true);
         (_newEquipment.equipmentVO as AbleEquipmentVO).addHole();
         _newEquipment.equipmentVO = _newEquipment.equipmentVO;
         (_newEquipment as DisplayObject).scaleY = 1.5;
         (_newEquipment as DisplayObject).scaleX = 1.5;
         initPutInFrame();
         initShow();
         var _loc3_:uint = Math.ceil((1 - _successRate) / 0.1);
         changeSuccessRate2(_loc3_);
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         initNotPutInFrame();
         _player = null;
         var _loc3_:EquipmentVO = !!_oldEquipment ? _oldEquipment.equipmentVO : null;
         clearOldEquipment();
         clearNewEquipment();
         clearBuyBtn();
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      private function getCurrentOpenHoleXML(param1:EquipmentVO) : XML
      {
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:XML = null;
         var _loc2_:* = null;
         var _loc5_:XMLList = _openHoleXML.equipment;
         _loc3_ = int(_loc5_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc3_)
         {
            _loc4_ = _loc5_[_loc8_];
            _loc6_ = int(_loc4_.@num);
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               if(String(_loc4_["id" + (_loc7_ + 1)]) == param1.id.toString())
               {
                  _loc2_ = _loc4_;
                  break;
               }
               _loc7_++;
            }
            _loc8_++;
         }
         if(_loc2_ == null)
         {
            _loc2_ = _openHoleXML.defau[0];
         }
         return _loc2_;
      }
      
      private function getOpenHoleIndexAndNum(param1:AbleEquipmentVO) : int
      {
         return param1.getHoleNum();
      }
      
      private function getHaveMaterialNum(param1:*, param2:Player) : int
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is Equipment)
         {
            _loc3_ = (param1 as Equipment).equipmentVO.id;
         }
         else
         {
            _loc3_ = param1;
         }
         var _loc5_:Vector.<EquipmentVO> = param2.playerVO.packageEquipmentVOs;
         var _loc4_:int = int(_loc5_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(_loc5_[_loc7_])
            {
               if(_loc5_[_loc7_].id == _loc3_)
               {
                  if(_loc5_[_loc7_] is StackEquipmentVO)
                  {
                     _loc6_ += (_loc5_[_loc7_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc6_++;
                  }
               }
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function initShow() : void
      {
         _oldContainer.addChild(_oldEquipment as DisplayObject);
         _materialContainer.addChild(_needMaterial as DisplayObject);
         _luckStoneContainer.addChild(_luckStoneShow);
         _newContainer.addChild(_newEquipment as DisplayObject);
         _needNumText.text = _needMaterialNum.toString();
         _successRateText.text = int(_successRate * 100) + "%";
         clearBuyLuckStoneBtn();
         _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
         _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
         _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
         addChild(_buyLuckStoneGuideBtn);
         refreshMaterialNumShow();
         refreshMoneyShow();
      }
      
      private function refreshMoneyShow() : void
      {
         if(_moneyText == null)
         {
            return;
         }
         clearBuyGoldPocketBtn();
         var _loc1_:TextFormat = _moneyText.defaultTextFormat;
         if(_player.playerVO.money >= _needMoney)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyGoldPocketBtn = new BuyGoldPocketGuideBtn(11100000,refreshMoneyShow,1);
         }
         _moneyText.defaultTextFormat = _loc1_;
         _moneyText.text = _needMoney.toString();
         _moneyText.width = _moneyText.textWidth + 5;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.x = _moneyText.x + _moneyText.width + 2;
            _buyGoldPocketBtn.y = _moneyText.y;
            addChild(_buyGoldPocketBtn);
         }
      }
      
      private function refreshMaterialNumShow() : void
      {
         if(_needMaterial == null || _haveNumText == null)
         {
            return;
         }
         clearBuyMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMaterial,_player);
         var _loc1_:TextFormat = _haveNumText.defaultTextFormat;
         if(_loc2_ >= _needMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMaterial.equipmentVO.ticketPrice)
            {
               _buyMaterialBtn = new BuyMaterialGuideBtn(_needMaterial.equipmentVO.id,refreshMaterialNumShow,1);
               _buyMaterialBtn.x = _materialContainer.x - _buyMaterialBtn.width / 2;
               _buyMaterialBtn.y = _materialContainer.y + 25;
               addChild(_buyMaterialBtn);
            }
         }
         _haveNumText.defaultTextFormat = _loc1_;
         _haveNumText.text = _loc2_.toString();
      }
      
      private function changeSuccessRate(param1:Number) : void
      {
         _successRate = Math.min(Math.max(0,param1),1);
         _successRateText.text = int(_successRate * 100) + "%";
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGroup.num = param1;
         changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
      }
      
      private function start() : void
      {
         if(_oldEquipment == null)
         {
            return;
         }
         if(_needMaterialNum > getHaveMaterialNum(_needMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return;
         }
         if(_needMoney > _player.playerVO.money)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足！",
               "flag":0
            }));
            return;
         }
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var saveinfo:SaveTaskInfo;
            var saveinfo5:SaveTaskInfo;
            var r:Number = Math.random();
            _player.playerVO.money -= _needMoney;
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMaterialNum,_needMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_numBtnGroup.num,10500000);
            clearBuyBtn();
            clearBuyLuckStoneBtn();
            clearBuyGoldPocketBtn();
            if(r <= _successRate)
            {
               parent.parent.mouseChildren = false;
               parent.parent.mouseEnabled = false;
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_newEquipment.equipmentVO,_oldEquipment.equipmentVO);
               GamingUI.getInstance().addMainLineTaskGoalGameEventStr("openHole");
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               initSuccessFrame();
               equipmentsAnimation(function():void
               {
                  _successAnimation.gotoAndPlay("start",null,null,function():void
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"开孔成功",
                        "flag":0
                     }));
                     TweenLite.to(_newEquipment,2,{
                        "alpha":0.5,
                        "x":600,
                        "y":100,
                        "ease":Back.easeIn,
                        "onComplete":function():void
                        {
                           _player = null;
                           clearNewEquipment();
                           clearOldEquipment();
                           clearNeedMaterial();
                           initNotPutInFrame();
                           dispatchEvent(new UIPassiveEvent("refreshAtt",34));
                           parent.parent.mouseChildren = true;
                           parent.parent.mouseEnabled = true;
                        }
                     });
                  },null);
               });
            }
            else
            {
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_oldEquipment.equipmentVO,_oldEquipment.equipmentVO);
               saveinfo5 = new SaveTaskInfo();
               saveinfo5.type = "4399";
               saveinfo5.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo5);
               MyFunction2.saveGame2();
               initFailFrame();
               equipmentsAnimation(function():void
               {
                  _failAnimation.gotoAndPlay("start",null,null,function():void
                  {
                     initNotPutInFrame();
                     clearOldEquipment();
                     clearNewEquipment();
                     clearNeedMaterial();
                     clearBuyBtn();
                     _player = null;
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"开孔失败！",
                        "flag":0
                     }));
                     dispatchEvent(new UIPassiveEvent("refreshAtt",34));
                  });
               });
            }
         },function():void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"网络连接失败！",
               "flag":0
            }));
         },true);
      }
      
      private function initNotPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("notPutIn");
      }
      
      private function clearNotPutInFrame() : void
      {
      }
      
      private function initPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putIn");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _successRateText = _show["successRateText"];
         _moneyText = _show["moneyText"];
         _haveNumText = _show["haveNumText"];
         _needNumText = _show["needNumText"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_successRateText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_moneyText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needNumText,true);
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.setShow(_show["numBtnGroup"]);
         setNumBtnGroupMaxNum();
         _startOpenHoleBtn = new ButtonLogicShell2();
         _startOpenHoleBtn.setShow(_show["startOpenHoleBtn"]);
         _startOpenHoleBtn.setTipString("点击开始开孔");
      }
      
      private function setNumBtnGroupMaxNum() : void
      {
         if(_numBtnGroup)
         {
            _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         }
      }
      
      private function clearPutInFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
      }
      
      private function initSuccessFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("success");
         _successAnimation = new MovieClipPlayLogicShell();
         _successAnimation.setShow(_show["successAnimation"]);
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearSuccessFrame() : void
      {
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         _successAnimation = null;
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function initFailFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("fail");
         _failAnimation = new MovieClipPlayLogicShell();
         _failAnimation.setShow(_show["failAnimation"]);
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearFailFrame() : void
      {
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         ClearUtil.clearDisplayObjectInContainer(_oldContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_materialContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_newContainer,false,false);
         _failAnimation = null;
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _numBtnGroup:
               changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
               break;
            case _startOpenHoleBtn:
               start();
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function equipmentsAnimation(param1:Function) : void
      {
         if(_oldEquipment)
         {
            TweenLite.to(_oldEquipment,2,{
               "alpha":0,
               "x":84,
               "y":58,
               "ease":Back.easeIn,
               "onComplete":param1
            });
         }
         if(_needMaterial)
         {
            TweenLite.to(_needMaterial,2,{
               "alpha":0,
               "x":-84,
               "y":58,
               "ease":Back.easeIn
            });
         }
      }
      
      private function clearOldEquipment() : void
      {
         if(_oldEquipment)
         {
            if((_oldEquipment as DisplayObject).parent)
            {
               (_oldEquipment as DisplayObject).parent.removeChild(_oldEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_oldEquipment);
            _oldEquipment.clear();
            _oldEquipment = null;
         }
      }
      
      private function clearNewEquipment() : void
      {
         if(_newEquipment)
         {
            if((_newEquipment as DisplayObject).parent)
            {
               (_newEquipment as DisplayObject).parent.removeChild(_newEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_newEquipment);
            _newEquipment.clear();
            _newEquipment = null;
         }
      }
      
      private function clearNeedMaterial() : void
      {
         if(_needMaterial)
         {
            if((_needMaterial as DisplayObject).parent)
            {
               (_needMaterial as DisplayObject).parent.removeChild(_needMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMaterial);
            _needMaterial.clear();
            _needMaterial = null;
         }
      }
      
      private function clearBuyMaterialBtn() : void
      {
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
      }
      
      private function clearBuyLuckStoneBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            if(_buyLuckStoneGuideBtn.parent)
            {
               _buyLuckStoneGuideBtn.parent.removeChild(_buyLuckStoneGuideBtn);
            }
            _buyLuckStoneGuideBtn.clear();
            _buyLuckStoneGuideBtn = null;
         }
      }
      
      private function clearBuyBtn() : void
      {
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
      }
      
      private function clearBuyGoldPocketBtn() : void
      {
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
   }
}

