package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.SocietyDataInList;
   
   public class TEST_SocietyDataInList extends SocietyDataInList
   {
      public function TEST_SocietyDataInList()
      {
         super();
      }
      
      public function initData(param1:int, param2:String, param3:int, param4:int, param5:int, param6:Number, param7:int, param8:String, param9:int, param10:String) : void
      {
         m_societyId = param1;
         m_societyName = param2;
         m_societyLevel = param3;
         m_playerNumInSociety = param4;
         m_maxPlayerNumInSociety = param5;
         m_uid_leader = param6;
         m_idx_leader = param7;
         m_Name_leader = param8;
         m_totalConValue = param9;
         m_announcementOfSociety = param10;
      }
   }
}

