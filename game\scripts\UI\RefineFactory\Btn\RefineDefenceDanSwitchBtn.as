package UI.RefineFactory.Btn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class RefineDefenceDanSwitchBtn extends SwitchBtn
   {
      public function RefineDefenceDanSwitchBtn()
      {
         super();
         setTipString("防御丹");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToRefineDefenceDan"));
      }
   }
}

