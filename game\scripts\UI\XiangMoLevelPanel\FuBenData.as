package UI.XiangMoLevelPanel
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   
   public class FuBenData extends DataManagerParent
   {
      private var m_fuBenId:String;
      
      private var m_maxChNum:uint;
      
      private var m_needLevel:uint;
      
      private var m_lockDescription:String;
      
      private var m_showFrameLabel:String;
      
      private var m_easyNanDuData:NanDuData;
      
      private var m_difficultyNanDuData:NanDuData;
      
      private var m_hellNanDuData:NanDuData;
      
      public function FuBenData()
      {
         super();
         m_easyNanDuData = new NanDuData();
         m_difficultyNanDuData = new NanDuData();
         m_hellNanDuData = new NanDuData();
      }
      
      override public function clear() : void
      {
         m_fuBenId = null;
         m_lockDescription = null;
         m_showFrameLabel = null;
         ClearUtil.clearObject(m_easyNanDuData);
         m_easyNanDuData = null;
         ClearUtil.clearObject(m_difficultyNanDuData);
         m_difficultyNanDuData = null;
         ClearUtil.clearObject(m_hellNanDuData);
         m_hellNanDuData = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         fuBenId = String(param1.@fuBenId);
         maxChNum = uint(param1.@maxChNum);
         needLevel = uint(param1.@needLevel);
         lockDescription = String(param1.@lockDescription);
         showFrameLabel = String(param1.@showFrameLabel);
         m_easyNanDuData.initByXML(param1.easy[0]);
         m_difficultyNanDuData.initByXML(param1.difficulty[0]);
         m_hellNanDuData.initByXML(param1.hell[0]);
      }
      
      public function getFuBenId() : String
      {
         return fuBenId;
      }
      
      public function getMaxChNum() : uint
      {
         return maxChNum;
      }
      
      public function getNeedLevel() : uint
      {
         return needLevel;
      }
      
      public function getLockDescription() : String
      {
         return lockDescription;
      }
      
      public function getShowFrameLabel() : String
      {
         return showFrameLabel;
      }
      
      public function getEasyNanDuData() : NanDuData
      {
         return m_easyNanDuData;
      }
      
      public function getDifficultyNanDuData() : NanDuData
      {
         return m_difficultyNanDuData;
      }
      
      public function getHellNanDuData() : NanDuData
      {
         return m_hellNanDuData;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.fuBenId = m_fuBenId;
         _antiwear.maxChNum = m_maxChNum;
         _antiwear.needLevel = m_needLevel;
      }
      
      private function get fuBenId() : String
      {
         return _antiwear.fuBenId;
      }
      
      private function set fuBenId(param1:String) : void
      {
         _antiwear.fuBenId = param1;
      }
      
      private function get maxChNum() : uint
      {
         return _antiwear.maxChNum;
      }
      
      private function set maxChNum(param1:uint) : void
      {
         _antiwear.maxChNum = param1;
      }
      
      private function get needLevel() : uint
      {
         return _antiwear.needLevel;
      }
      
      private function set needLevel(param1:uint) : void
      {
         _antiwear.needLevel = param1;
      }
      
      private function get lockDescription() : String
      {
         return m_lockDescription;
      }
      
      private function set lockDescription(param1:String) : void
      {
         m_lockDescription = param1;
      }
      
      private function get showFrameLabel() : String
      {
         return m_showFrameLabel;
      }
      
      private function set showFrameLabel(param1:String) : void
      {
         m_showFrameLabel = param1;
      }
   }
}

