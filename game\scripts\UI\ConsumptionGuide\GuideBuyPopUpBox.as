package UI.ConsumptionGuide
{
   import UI.AnalogServiceHoldFunction;
   import UI.ConsumptionGuide.Button.GuideRechargeBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.Players.Player;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.ShopWall.ShopWall_BuyPopUpBox;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GuideBuyPopUpBox extends ShopWall_BuyPopUpBox
   {
      private var _rechargeBtn:GuideRechargeBtn;
      
      private var _pointTicketText:TextField = new TextField();
      
      private var _fun:Function;
      
      private var _reservePositionNum:int;
      
      private var _player:Player;
      
      private var _showWarningFun:Function;
      
      public function GuideBuyPopUpBox(param1:EquipmentVO, param2:Player, param3:String, param4:Function = null)
      {
         _player = param2;
         _showWarningFun = param4;
         super(param1,param3);
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      public function set fun(param1:Function) : void
      {
         _fun = param1;
      }
      
      public function set reservePositionNum(param1:int) : void
      {
         _reservePositionNum = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_rechargeBtn)
         {
            _rechargeBtn.removeEventListener("click",recharge,false);
            _rechargeBtn.clear();
         }
         _rechargeBtn = null;
         _fun = null;
         _pointTicketText = null;
         _showWarningFun = null;
      }
      
      override public function initBox(param1:EquipmentVO, param2:String) : void
      {
         super.initBox(param1,param2);
         if(_sureBtn)
         {
            _sureBtn.x = width - _sureBtn.width - 10 - 260;
         }
         if(Boolean(_cancelBtn) && _cancelBtn.parent)
         {
            _cancelBtn.parent.removeChild(_cancelBtn);
         }
         if(_rechargeBtn == null)
         {
            _rechargeBtn = new GuideRechargeBtn();
         }
         _rechargeBtn.x = _sureBtn.x - _rechargeBtn.width;
         _rechargeBtn.y = _sureBtn.y - 5;
         _rechargeBtn.addEventListener("click",recharge,false,0,true);
         addChild(_rechargeBtn);
         _pointTicketText.x = 10;
         _pointTicketText.y = _rechargeBtn.y + 10;
         _pointTicketText.selectable = false;
         _pointTicketText.embedFonts = true;
         addChild(_pointTicketText);
         if(_sureBtn)
         {
            _sureBtn.showText("购买");
         }
         _pointTicketText.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'12\' color=\'#ff0000\' > 现有点券: </font>" + "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#ffffff\'>" + CurrentTicketPointManager.getInstance().getCurrentTicketPoint().toString() + "</font>";
         _pointTicketText.width = _pointTicketText.textWidth + 10;
         _pointTicketText.height = _pointTicketText.textHeight + 5;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override public function set state(param1:int) : void
      {
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var myParent:*;
         var e:UIBtnEvent = param1;
         var dataObj:Object = {};
         dataObj["propId"] = equipmentCell.child.ticketId;
         dataObj["count"] = _numBtnGroup.num;
         dataObj["price"] = equipmentCell.child.ticketPrice;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         myParent = parent;
         AnalogServiceHoldFunction.getInstance().buyByPayData(dataObj,function(param1:Function):void
         {
            MyFunction.getInstance().buyEquipmentVO(_player,equipmentCell.child,_numBtnGroup.num,!!_showWarningFun ? _showWarningFun : myParent.showWarningBox,"ticketObj",param1,_reservePositionNum,_fun);
         },stage,!!_showWarningFun ? _showWarningFun : myParent.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         parent.removeChild(this);
      }
      
      private function recharge(param1:MouseEvent) : void
      {
         AnalogServiceHoldFunction.getInstance().payMoney_As3();
         parent.removeChild(this);
      }
      
      override public function quit(param1:UIBtnEvent) : void
      {
         super.quit(param1);
      }
   }
}

