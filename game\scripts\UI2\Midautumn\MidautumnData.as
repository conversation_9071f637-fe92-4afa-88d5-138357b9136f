package UI2.Midautumn
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class MidautumnData extends DataManagerParent
   {
      private static var _instance:MidautumnData;
      
      private var m_vectZycjh:Vector.<EquipmentVO>;
      
      private var m_vectZylh:Vector.<EquipmentVO>;
      
      private var m_vectZy:Vector.<EquipmentVO>;
      
      private var m_vectBangyan:Vector.<EquipmentVO>;
      
      private var m_vectSanhong:Vector.<EquipmentVO>;
      
      private var m_vectSijin:Vector.<EquipmentVO>;
      
      private var m_vectErju:Vector.<EquipmentVO>;
      
      private var m_vectYixiu:Vector.<EquipmentVO>;
      
      private var m_vectAnwei:Vector.<EquipmentVO>;
      
      private var m_xml:XML;
      
      private var m_freeNum:int = 0;
      
      private var m_time:String;
      
      private var m_ticketPrice:Number;
      
      private var m_ticketPriceId:Number;
      
      private var m_ticketNum:int;
      
      private var m_startTime1:String;
      
      private var m_endTime1:String;
      
      private var m_startTime2:String;
      
      private var m_endTime2:String;
      
      private var m_wzwMoney:Number;
      
      private var m_ticketId:Number;
      
      private var m_ticket:Number;
      
      private var m_isAdd:Boolean = false;
      
      private var m_isBuyAdd:Boolean = false;
      
      private var m_numZY:int;
      
      private var m_nRid:int;
      
      private var m_bGetTotalMoney:Boolean = true;
      
      public function MidautumnData()
      {
         super();
         m_vectZycjh = new Vector.<EquipmentVO>();
         m_vectZylh = new Vector.<EquipmentVO>();
         m_vectZy = new Vector.<EquipmentVO>();
         m_vectBangyan = new Vector.<EquipmentVO>();
         m_vectSanhong = new Vector.<EquipmentVO>();
         m_vectSijin = new Vector.<EquipmentVO>();
         m_vectErju = new Vector.<EquipmentVO>();
         m_vectYixiu = new Vector.<EquipmentVO>();
         m_vectAnwei = new Vector.<EquipmentVO>();
      }
      
      public static function getInstance() : MidautumnData
      {
         if(_instance == null)
         {
            _instance = new MidautumnData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_vectAnwei);
         m_vectAnwei = null;
         ClearUtil.clearObject(m_vectZycjh);
         m_vectZycjh = null;
         ClearUtil.clearObject(m_vectZylh);
         m_vectZylh = null;
         ClearUtil.clearObject(m_vectZy);
         m_vectZy = null;
         ClearUtil.clearObject(m_vectBangyan);
         m_vectBangyan = null;
         ClearUtil.clearObject(m_vectSanhong);
         m_vectSanhong = null;
         ClearUtil.clearObject(m_vectSijin);
         m_vectSijin = null;
         ClearUtil.clearObject(m_vectErju);
         m_vectErju = null;
         ClearUtil.clearObject(m_vectYixiu);
         m_vectYixiu = null;
         super.clear();
      }
      
      public function getFreeNum() : int
      {
         return m_freeNum;
      }
      
      public function setFreeNum(param1:int) : void
      {
         m_freeNum = param1;
      }
      
      public function getIsAdd() : Boolean
      {
         return m_isAdd;
      }
      
      public function setIsAdd(param1:Boolean) : void
      {
         m_isAdd = param1;
      }
      
      public function getIsBuyAdd() : Boolean
      {
         return m_isBuyAdd;
      }
      
      public function setIsBuyAdd(param1:Boolean) : void
      {
         m_isBuyAdd = param1;
      }
      
      public function getNumZY() : int
      {
         return m_numZY;
      }
      
      public function setNumZY(param1:int) : void
      {
         m_numZY = param1;
      }
      
      public function getRid() : int
      {
         return m_nRid;
      }
      
      public function setRid(param1:int) : void
      {
         m_nRid = param1;
      }
      
      public function setLoadMoney(param1:Boolean) : void
      {
         m_bGetTotalMoney = param1;
      }
      
      public function getLoadMoney() : Boolean
      {
         return m_bGetTotalMoney;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.m_freeNum = m_freeNum;
         _antiwear.m_numZY = m_numZY;
      }
      
      public function getListByType(param1:int) : Vector.<EquipmentVO>
      {
         if(param1 == 1)
         {
            return m_vectZycjh;
         }
         if(param1 == 2)
         {
            return m_vectZylh;
         }
         if(param1 == 3)
         {
            return m_vectZy;
         }
         if(param1 == 4)
         {
            return m_vectBangyan;
         }
         if(param1 == 5)
         {
            return m_vectSanhong;
         }
         if(param1 == 6)
         {
            return m_vectSijin;
         }
         if(param1 == 7)
         {
            return m_vectErju;
         }
         if(param1 == 8)
         {
            return m_vectYixiu;
         }
         if(param1 == 9)
         {
            return m_vectAnwei;
         }
         return null;
      }
      
      public function getItemByType(param1:int, param2:int) : EquipmentVO
      {
         if(param1 == 1 && m_vectZycjh && m_vectZycjh.length > 0)
         {
            if(m_vectZycjh.length > param2 && param2 >= 0)
            {
               return m_vectZycjh[param2];
            }
         }
         else if(param1 == 2 && m_vectZylh && m_vectZylh.length > 0)
         {
            if(m_vectZylh.length > param2 && param2 >= 0)
            {
               return m_vectZylh[param2];
            }
         }
         else if(param1 == 3 && m_vectZy && m_vectZy.length > 0)
         {
            if(m_vectZy.length > param2 && param2 >= 0)
            {
               return m_vectZy[param2];
            }
         }
         else if(param1 == 4 && m_vectBangyan && m_vectBangyan.length > 0)
         {
            if(m_vectBangyan.length > param2 && param2 >= 0)
            {
               return m_vectBangyan[param2];
            }
         }
         else if(param1 == 5 && m_vectSanhong && m_vectSanhong.length > 0)
         {
            if(m_vectSanhong.length > param2 && param2 >= 0)
            {
               return m_vectSanhong[param2];
            }
         }
         else if(param1 == 6 && m_vectSijin && m_vectSijin.length > 0)
         {
            if(m_vectSijin.length > param2 && param2 >= 0)
            {
               return m_vectSijin[param2];
            }
         }
         else if(param1 == 7 && m_vectErju && m_vectErju.length > 0)
         {
            if(m_vectErju.length > param2 && param2 >= 0)
            {
               return m_vectErju[param2];
            }
         }
         else if(param1 == 8 && m_vectYixiu && m_vectYixiu.length > 0)
         {
            if(m_vectYixiu.length > param2 && param2 >= 0)
            {
               return m_vectYixiu[param2];
            }
         }
         else if(param1 == 9 && m_vectAnwei && m_vectAnwei.length > 0)
         {
            if(m_vectAnwei.length > param2 && param2 >= 0)
            {
               return m_vectAnwei[param2];
            }
         }
         return null;
      }
      
      public function initXML(param1:XML) : void
      {
         var _loc2_:XML = null;
         var _loc3_:XML = null;
         m_xml = param1;
         _loc2_ = param1[0].zhongqiu[0];
         m_startTime1 = String(_loc2_.@startTime);
         m_endTime1 = String(_loc2_.@endTime);
         m_ticketPrice = Number(_loc2_.@ticketPrice);
         m_ticketPriceId = Number(_loc2_.@ticketPriceId);
         m_ticketNum = int(_loc2_.@moneytonum);
         initZycjh(_loc2_.zhuangyuanchajinhua[0]);
         initZylh(_loc2_.zhuangyuanliuhong[0]);
         initZy(_loc2_.zhuangyuan[0]);
         initbangyan(_loc2_.bangyan[0]);
         initsanhong(_loc2_.sanhong[0]);
         initsijin(_loc2_.sijin[0]);
         initerju(_loc2_.erju[0]);
         inityixiu(_loc2_.yixiu[0]);
         initAnwei(_loc2_.anwei[0]);
         _loc3_ = param1[0].wangzhongwang[0];
         m_startTime2 = String(_loc3_.@startTime);
         m_endTime2 = String(_loc3_.@endTime);
         m_ticket = Number(_loc3_.@ticketPrice);
         m_ticketId = Number(_loc3_.@ticketPriceId);
         m_wzwMoney = int(_loc3_.@moneytonum);
      }
      
      public function getStartTime1() : String
      {
         return m_startTime1;
      }
      
      public function getEndTime1() : String
      {
         return m_endTime1;
      }
      
      public function getTicketPriceId() : int
      {
         return m_ticketPriceId;
      }
      
      public function getTicketPrice() : int
      {
         return m_ticketPrice;
      }
      
      public function getTicketToNum() : int
      {
         return m_ticketNum;
      }
      
      public function getStartTime2() : String
      {
         return m_startTime2;
      }
      
      public function getEndTime2() : String
      {
         return m_endTime2;
      }
      
      public function getTicketId() : Number
      {
         return m_ticketId;
      }
      
      public function getTicket() : Number
      {
         return m_ticket;
      }
      
      public function getWzwMoney() : Number
      {
         return m_wzwMoney;
      }
      
      private function initZycjh(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectZycjh.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectZycjh.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initZylh(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectZylh.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectZylh.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initZy(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectZy.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectZy.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initbangyan(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectBangyan.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectBangyan.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initsanhong(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectSanhong.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectSanhong.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initsijin(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectSijin.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectSijin.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initerju(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectErju.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectErju.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function inityixiu(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectYixiu.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectYixiu.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
      
      private function initAnwei(param1:XML) : void
      {
         var _loc3_:int = 0;
         m_vectAnwei.length = 0;
         var _loc2_:XMLList = param1.equipment;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            m_vectAnwei.push(XMLSingle.getEquipmentVOByID(int(_loc2_[_loc3_].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc3_++;
         }
      }
   }
}

