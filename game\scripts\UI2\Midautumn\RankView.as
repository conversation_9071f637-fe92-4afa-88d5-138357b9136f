package UI2.Midautumn
{
   import Json.MyJSON;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.text.TextField;
   
   public class RankView extends MySprite
   {
      private var m_strRank:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/wzw/more";
      
      private var m_show:MovieClip;
      
      private var m_view:WzwView;
      
      private var m_btnClose:ButtonLogicShell2;
      
      private var m_btnPrePage:ButtonLogicShell2;
      
      private var m_btnNextPage:ButtonLogicShell2;
      
      private var m_itemlist:Vector.<MovieClip>;
      
      private var m_txtPage:TextField;
      
      private var m_indexPage:int = 0;
      
      public function RankView()
      {
         super();
         m_btnClose = new ButtonLogicShell2();
         m_btnPrePage = new ButtonLogicShell2();
         m_btnNextPage = new ButtonLogicShell2();
         m_itemlist = new Vector.<MovieClip>();
      }
      
      override public function clear() : void
      {
         m_show.removeEventListener("clickButton",clickButton);
         ClearUtil.clearObject(m_btnClose);
         m_btnClose = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         super.clear();
      }
      
      public function init(param1:MovieClip, param2:WzwView) : void
      {
         var _loc3_:int = 0;
         m_show = param1;
         m_view = param2;
         m_btnClose.setShow(m_show["btnClose"]);
         m_btnClose.setTipString("关闭");
         m_itemlist.length = 0;
         _loc3_ = 1;
         while(_loc3_ <= 6)
         {
            m_itemlist.push(m_show["item_" + _loc3_]);
            _loc3_++;
         }
         m_txtPage = m_show["txtPage"] as TextField;
         m_btnPrePage.setShow(m_show["pagepre"]);
         m_btnPrePage.setTipString("上一页");
         m_btnNextPage.setShow(m_show["pagenext"]);
         m_btnNextPage.setTipString("下一页");
         m_show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function hideAll() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_itemlist.length)
         {
            m_itemlist[_loc1_].visible = false;
            _loc1_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_btnClose:
               m_view.hideRank();
               break;
            case m_btnPrePage:
               if(m_indexPage > 0)
               {
                  m_indexPage--;
                  sendRank();
               }
               break;
            case m_btnNextPage:
               m_indexPage++;
               sendRank();
         }
      }
      
      private function refreshList() : void
      {
         var _loc1_:RankItem = null;
         var _loc2_:int = 0;
         hideAll();
         m_txtPage.text = String(m_indexPage + 1);
         _loc2_ = 0;
         while(_loc2_ < 6)
         {
            _loc1_ = WzwData.getInstance().getDataByIndex(_loc2_);
            if(_loc1_)
            {
               refreshItem(_loc2_,_loc1_);
            }
            _loc2_++;
         }
      }
      
      private function refreshItem(param1:int, param2:RankItem) : void
      {
         var _loc3_:int = 0;
         m_itemlist[param1].visible = true;
         if(param2.rank <= 3)
         {
            (m_itemlist[param1]["numrank"] as MovieClip).gotoAndStop(String(param2.rank));
         }
         else
         {
            (m_itemlist[param1]["numrank"] as MovieClip).gotoAndStop("4");
            ((m_itemlist[param1]["numrank"] as MovieClip)["lebrank"] as TextField).text = String(param2.rank) + "th";
         }
         (m_itemlist[param1]["txtName"] as TextField).text = param2.name;
         _loc3_ = 0;
         while(_loc3_ < param2.numlist.length)
         {
            ((m_itemlist[param1]["rankpoint"] as MovieClip)["num_" + (_loc3_ + 1)] as MovieClip).gotoAndStop(String(param2.numlist[_loc3_]));
            _loc3_++;
         }
      }
      
      public function openRank() : void
      {
         m_indexPage = 0;
         sendRank();
      }
      
      public function sendRank() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderRankCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorRankHandler);
         _loc1_.addEventListener("securityError",onErrorRankHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strRank);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.Start = m_indexPage * 6;
         _loc3_.Limit = 6;
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderRankCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRankCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRankHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRankHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         WzwData.getInstance().initList(_loc2_,m_indexPage * 6);
         refreshList();
      }
      
      protected function onErrorRankHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRankCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRankHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRankHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求博饼排行榜数据错误!");
      }
   }
}

