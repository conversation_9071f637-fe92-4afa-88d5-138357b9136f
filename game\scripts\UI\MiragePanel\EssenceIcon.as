package UI.MiragePanel
{
   import UI.MySprite;
   
   public class EssenceIcon extends MySprite
   {
      public var essenceValue:int;
      
      public function EssenceIcon(param1:int)
      {
         super();
         this.essenceValue = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
      }
      
      public function clone() : EssenceIcon
      {
         return new EssenceIcon(essenceValue);
      }
   }
}

