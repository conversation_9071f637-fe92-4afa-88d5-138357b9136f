package UI.MainLineTask.TaskRewardVO
{
   import UI.DataManagerParent;
   
   public class TaskRewardVO extends DataManagerParent
   {
      public var description:String;
      
      public function TaskRewardVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         description = String(param1.@description);
      }
   }
}

