package UI.SocietySystem
{
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.NicknameSystem.NicknameData;
   import UI.SocietySystem.SeverLink.InformationBody;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_AddConValueReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_ApplyJoinReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_CancelApplyJoinReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_CancelDissolveSocietyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_CreateSocietySuccess;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_DecConValueReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_DissolveSocietyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_DissolveTheSociety_Broadcast;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetLogicServerReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_IsSuccessJoin;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_LeaveTheSocietySuccess;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_LogInSuccess;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerTheSocietyData;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyListOfApplyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_VerifyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.InformationBodyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_AddConValue;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_AgreeOrRefuseJoinDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ApplyForJoinDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_CancelApplyForjoin;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_CancelDissolveSociety;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ChangeAnnouncement;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ChatInforUp;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_CreateSocietyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_DecConValue;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_DissovleTheSocietyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_GetLogicServer;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_HeartBeatBagDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_LeaveTheSocietyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_LogInDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_RemoveMember;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowApplyPlayerListDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowPlayerTheSocietyData;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowSocietyList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowSocietyListOfApply;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowTheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_UpdatePlayerData;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_Verify;
   import UI.SocietySystem.SeverLink.SocketListener;
   import UI.SocietySystem.SeverLink.SocketManager;
   import UI.SocietySystem.SeverLink.UP_InformationBody;
   import UI.SocietySystem.SocialChatPanel.NewGotChatInfor;
   import UI.VersionControl;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.system.System;
   
   public class SocietySystem
   {
      public static const HOST:String = "yxkf.lj.aiwan4399.com";
      
      public static const PORT_VERIFY:int = 7050;
      
      public static const PORT_GET_LOGIC_SERVER:int = 7076;
      
      private var m_currentLinkPort:int;
      
      private var m_currentLinkHost:String;
      
      private var m_logicServerHost:String;
      
      private var m_logicServerPort:int;
      
      private var m_clear:ClearHelper;
      
      private var m_socketManager:SocketManager;
      
      private var m_socketListener:SocketListener;
      
      private var m_societySystemListeners:Vector.<ISocietySystemListener>;
      
      private var m_societySystemXML:XML;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_islink:Boolean;
      
      private var m_isGetServerData:Boolean;
      
      private var m_wasteInforBodys:Vector.<InformationBody>;
      
      private var m_isLogIn:Boolean;
      
      private var m_isLogInComplete:Boolean;
      
      private var m_allGetInfor:Vector.<DOWN_GetChatInfor>;
      
      private var m_unableSendChatTime:Number;
      
      private var m_societyListOfApply:DOWN_TheSocietyListOfApplyReturn;
      
      private var m_memberDataListInMySocietyPanels:Vector.<DOWN_TheSocietyMemberList>;
      
      private var m_societyListDataInSocietyListPanels:Vector.<DOWN_TheSocietyList>;
      
      private var m_memberDataListsObjInSocietyListPanel:Object;
      
      private var m_theSocietyListOfApply:DOWN_TheSocietyListOfApplyReturn;
      
      private var m_gamingUI:GamingUI;
      
      private var m_versionControl:VersionControl;
      
      private var m_newEncryValue:int;
      
      public function SocietySystem()
      {
         super();
         m_clear = new ClearHelper();
         m_wasteInforBodys = new Vector.<InformationBody>();
         m_allGetInfor = new Vector.<DOWN_GetChatInfor>();
         m_unableSendChatTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - 60000;
         m_memberDataListsObjInSocietyListPanel = {};
         m_societyListDataInSocietyListPanels = new Vector.<DOWN_TheSocietyList>();
         m_memberDataListInMySocietyPanels = new Vector.<DOWN_TheSocietyMemberList>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         if(m_socketManager)
         {
            m_socketManager.removeSocketListener(m_socketListener);
         }
         ClearUtil.clearObject(m_socketListener);
         m_socketListener = null;
         ClearUtil.clearObject(m_socketManager);
         m_socketManager = null;
         ClearUtil.nullArr(m_societySystemListeners,false,false,false);
         System.disposeXML(m_societySystemXML);
         m_societySystemXML = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.nullArr(m_wasteInforBodys);
         m_wasteInforBodys = null;
         ClearUtil.clearObject(m_societyListOfApply);
         m_societyListOfApply = null;
         ClearUtil.clearObject(m_memberDataListInMySocietyPanels);
         m_memberDataListInMySocietyPanels = null;
         ClearUtil.clearObject(m_societyListDataInSocietyListPanels);
         m_societyListDataInSocietyListPanels = null;
         ClearUtil.clearObject(m_memberDataListsObjInSocietyListPanel);
         m_memberDataListsObjInSocietyListPanel = null;
         ClearUtil.clearObject(m_theSocietyListOfApply);
         m_theSocietyListOfApply = null;
         m_gamingUI = null;
         m_versionControl = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function init() : void
      {
         m_versionControl = m_versionControl;
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/SocietySystem/societySystem.xml",getSocietySystemXMLSuccess,getSocietySystemXMLFail);
         m_myLoader.load();
      }
      
      public function tryReConnectAndLogIn() : void
      {
         if(m_isLogIn)
         {
            return;
         }
         if(m_islink)
         {
            return;
         }
         if(m_isGetServerData)
         {
            return;
         }
         if(Boolean(m_socketManager) && m_socketManager.getSocket().connected == false)
         {
            linkServerForGetLogicServer();
         }
         else
         {
            logIn();
         }
      }
      
      public function addSocietySystemListener(param1:ISocietySystemListener) : void
      {
         if(m_societySystemListeners == null)
         {
            m_societySystemListeners = new Vector.<ISocietySystemListener>();
         }
         m_societySystemListeners.push(param1);
      }
      
      public function removeSocietySystemListener(param1:ISocietySystemListener) : void
      {
         if(m_societySystemListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_societySystemListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_societySystemListeners.splice(_loc2_,1);
            _loc2_ = int(m_societySystemListeners.indexOf(param1));
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_socketManager)
         {
            m_socketManager.render(param1);
         }
      }
      
      public function getSocketManager() : SocketManager
      {
         return m_socketManager;
      }
      
      public function getIsLogIn() : Boolean
      {
         return m_isLogIn;
      }
      
      public function getSocietySystemXML() : XML
      {
         return m_societySystemXML;
      }
      
      public function getUnAbleSendChatTime() : Number
      {
         return m_unableSendChatTime;
      }
      
      public function setUnAbleSendChatTime(param1:Number) : void
      {
         m_unableSendChatTime = param1;
      }
      
      public function getChatHistoryStr() : Vector.<NewGotChatInfor>
      {
         var _loc4_:int = 0;
         var _loc3_:NewGotChatInfor = null;
         var _loc2_:int = int(m_allGetInfor.length);
         var _loc1_:Vector.<NewGotChatInfor> = new Vector.<NewGotChatInfor>();
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = new NewGotChatInfor();
            _loc3_.init(m_allGetInfor[_loc4_]);
            _loc1_.push(_loc3_);
            _loc4_++;
         }
         return _loc1_;
      }
      
      public function clearSocietyBufferData() : void
      {
         ClearUtil.clearObject(m_memberDataListInMySocietyPanels);
         m_memberDataListInMySocietyPanels = new Vector.<DOWN_TheSocietyMemberList>();
         ClearUtil.clearObject(m_societyListDataInSocietyListPanels);
         m_societyListDataInSocietyListPanels = new Vector.<DOWN_TheSocietyList>();
         ClearUtil.clearObject(m_memberDataListsObjInSocietyListPanel);
         m_memberDataListsObjInSocietyListPanel = {};
      }
      
      public function getMemberDataListInMySocietyPanelByIndex(param1:int) : DOWN_TheSocietyMemberList
      {
         if(param1 > m_memberDataListInMySocietyPanels.length - 1)
         {
            return null;
         }
         return m_memberDataListInMySocietyPanels[param1];
      }
      
      public function addMemberDataListInMySocietyPanel(param1:DOWN_TheSocietyMemberList) : void
      {
         if(m_memberDataListInMySocietyPanels.length - 1 < param1.getPageIndex())
         {
            MyFunction2.addLenghForVector(m_memberDataListInMySocietyPanels,param1.getPageIndex() + 1);
         }
         if(m_memberDataListInMySocietyPanels[param1.getPageIndex()])
         {
            ClearUtil.clearObject(m_memberDataListInMySocietyPanels[param1.getPageIndex()]);
         }
         m_memberDataListInMySocietyPanels[param1.getPageIndex()] = param1;
      }
      
      public function getSocietyListDataInSocietyListPanelByIndex(param1:int) : DOWN_TheSocietyList
      {
         if(param1 > m_societyListDataInSocietyListPanels.length - 1)
         {
            return null;
         }
         return m_societyListDataInSocietyListPanels[param1];
      }
      
      public function addSocietyListDataInSocietyListPanel(param1:DOWN_TheSocietyList) : void
      {
         if(m_societyListDataInSocietyListPanels.length - 1 < param1.getPageIndex())
         {
            MyFunction2.addLenghForVector(m_societyListDataInSocietyListPanels,param1.getPageIndex() + 1);
         }
         if(m_societyListDataInSocietyListPanels[param1.getPageIndex()])
         {
            ClearUtil.clearObject(m_societyListDataInSocietyListPanels[param1.getPageIndex()]);
         }
         m_societyListDataInSocietyListPanels[param1.getPageIndex()] = param1;
      }
      
      public function getMemberDataListBySocietyIdAndIndexInSocietyListPanel(param1:String, param2:int) : DOWN_TheSocietyMemberList
      {
         var _loc3_:Vector.<DOWN_TheSocietyMemberList> = m_memberDataListsObjInSocietyListPanel[param1];
         if(_loc3_ == null)
         {
            return null;
         }
         if(param2 > _loc3_.length - 1)
         {
            return null;
         }
         return _loc3_[param2];
      }
      
      public function addMemberDataListInSocietyListPanel(param1:String, param2:DOWN_TheSocietyMemberList) : void
      {
         var _loc3_:Vector.<DOWN_TheSocietyMemberList> = m_memberDataListsObjInSocietyListPanel[param1];
         if(_loc3_ == null)
         {
            _loc3_ = new Vector.<DOWN_TheSocietyMemberList>(param2.getPageIndex() + 1);
            m_memberDataListsObjInSocietyListPanel[param1] = _loc3_;
         }
         if(_loc3_.length - 1 < param2.getPageIndex())
         {
            MyFunction2.addLenghForVector(_loc3_,param2.getPageIndex() + 1);
         }
         if(_loc3_[param2.getPageIndex()])
         {
            ClearUtil.clearObject(_loc3_[param2.getPageIndex()]);
         }
         _loc3_[param2.getPageIndex()] = param2;
      }
      
      public function getTheSocietyListOfApply() : DOWN_TheSocietyListOfApplyReturn
      {
         return m_theSocietyListOfApply;
      }
      
      public function setTheSocietyListOfApply(param1:DOWN_TheSocietyListOfApplyReturn) : void
      {
         ClearUtil.clearObject(m_theSocietyListOfApply);
         m_theSocietyListOfApply = param1;
      }
      
      public function addWasteInforBody(param1:InformationBody) : void
      {
         var _loc2_:int = int(m_wasteInforBodys.indexOf(param1));
         if(_loc2_ == -1)
         {
            m_wasteInforBodys.push(param1);
         }
      }
      
      public function getSocietyListOfApply() : DOWN_TheSocietyListOfApplyReturn
      {
         return m_societyListOfApply;
      }
      
      public function getTheSocietyData() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      获取个人帮会信息");
         var _loc3_:UP_ShowPlayerTheSocietyData = new UP_ShowPlayerTheSocietyData();
         var _loc1_:Number = Number(GameData.getInstance().getLoginReturnData().getUid());
         var _loc2_:int = int(GameData.getInstance().getSaveFileData().index);
         _loc3_.initData(_loc1_,_loc2_);
         writeDetailToBody(3006,_loc3_);
      }
      
      public function getSocietyList(param1:int, param2:int) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      请求帮会列表, pageIndex:" + param1 + "num:" + param2);
         var _loc3_:UP_ShowSocietyList = new UP_ShowSocietyList();
         _loc3_.initData(param1,param2);
         writeDetailToBody(3004,_loc3_);
      }
      
      public function createSociety(param1:String) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      请求创建帮会");
         var _loc2_:UP_CreateSocietyDetail = new UP_CreateSocietyDetail();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1);
         writeDetailToBody(3022,_loc2_);
      }
      
      public function applyJoinSociety(param1:int) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      申请加入帮会");
         var _loc2_:UP_ApplyForJoinDetail = new UP_ApplyForJoinDetail();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1);
         writeDetailToBody(3012,_loc2_);
      }
      
      public function cancelApplyJoinSociety(param1:int) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      取消申请加入帮会");
         var _loc2_:UP_CancelApplyForjoin = new UP_CancelApplyForjoin();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1);
         writeDetailToBody(3017,_loc2_);
      }
      
      public function getTheSocietyMemberList(param1:int, param2:int, param3:int) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      获得帮会成员列表 pageIndex:" + param2 + "num:" + param3);
         var _loc4_:UP_ShowTheSocietyMemberList = new UP_ShowTheSocietyMemberList();
         _loc4_.initData(param1,param2,param3);
         writeDetailToBody(3008,_loc4_);
      }
      
      public function getPlayerListOfApply() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      帮主请求查看申请帮会玩家列表");
         var _loc1_:UP_ShowApplyPlayerListDetail = new UP_ShowApplyPlayerListDetail();
         _loc1_.initData(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId());
         writeDetailToBody(3019,_loc1_);
      }
      
      public function agreeOrRefusePlayerJoin(param1:Boolean, param2:Number, param3:Number) : void
      {
         trace("请求服务器：      帮主同意/拒绝某个玩家加入帮会");
         trace("帮主上方同意或拒绝加入帮会： 是否同意：",param1,"申请人uid",param2,"申请人idx",param3);
         var _loc4_:UP_AgreeOrRefuseJoinDetail = new UP_AgreeOrRefuseJoinDetail();
         _loc4_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param2,param3,int(param1));
         writeDetailToBody(3015,_loc4_);
      }
      
      public function changeAnnouncement(param1:int, param2:String) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      更改帮会公告");
         var _loc3_:UP_ChangeAnnouncement = new UP_ChangeAnnouncement();
         _loc3_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1,param2);
         writeDetailToBody(3031,_loc3_);
      }
      
      public function leaveSociety() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      发送离开帮会请求");
         var _loc1_:UP_LeaveTheSocietyDetail = new UP_LeaveTheSocietyDetail();
         _loc1_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         writeDetailToBody(3010,_loc1_);
      }
      
      public function dissolveSociety() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      发送解散帮会请求");
         var _loc1_:UP_DissovleTheSocietyDetail = new UP_DissovleTheSocietyDetail();
         _loc1_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         writeDetailToBody(3024,_loc1_);
      }
      
      public function cancelDissolveSociety() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      取消解散帮会");
         var _loc1_:UP_CancelDissolveSociety = new UP_CancelDissolveSociety();
         _loc1_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         writeDetailToBody(3038,_loc1_);
      }
      
      public function removeMember(param1:Number, param2:int) : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      帮主踢人");
         var _loc3_:UP_RemoveMember = new UP_RemoveMember();
         _loc3_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1,param2);
         writeDetailToBody(3033,_loc3_);
      }
      
      public function showSocietyListOfApply() : void
      {
         GamingUI.getInstance().lockGamingUI("加载数据中");
         trace("请求服务器：      请求申请帮会的列表");
         var _loc1_:UP_ShowSocietyListOfApply = new UP_ShowSocietyListOfApply();
         _loc1_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         writeDetailToBody(3036,_loc1_);
      }
      
      public function updatePlayerData(param1:int) : void
      {
         var _loc3_:String = !!NicknameData.getInstance().myDataInNicknameRankList ? NicknameData.getInstance().myDataInNicknameRankList.extra : "";
         trace("请求服务器：      向服务器更新玩家数据","option:",param1,"player level:",GamingUI.getInstance().player1.playerVO.level,"nickName:",_loc3_);
         var _loc2_:UP_UpdatePlayerData = new UP_UpdatePlayerData();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1,GamingUI.getInstance().player1.playerVO.level,_loc3_);
         writeDetailToBody(3040,_loc2_);
      }
      
      public function addConValue(param1:int) : void
      {
         trace("请求服务器：      增加玩家帮会贡献值，贡献值id：" + param1);
         GamingUI.getInstance().lockGamingUI("处理中...");
         var _loc2_:UP_AddConValue = new UP_AddConValue();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1);
         writeDetailToBody(3027,_loc2_);
      }
      
      public function decConValue(param1:int) : void
      {
         trace("请求服务器：      增加玩家帮会贡献值，减少贡献值：" + param1);
         GamingUI.getInstance().lockGamingUI("处理中...");
         var _loc2_:UP_DecConValue = new UP_DecConValue();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1);
         writeDetailToBody(3029,_loc2_);
      }
      
      public function upChatInfor(param1:String, param2:int) : void
      {
         trace("请求服务器：    提交聊天信息：",param1,"额外信息：",param2);
         var _loc3_:UP_ChatInforUp = new UP_ChatInforUp();
         _loc3_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,param1,param2);
         writeDetailToBody(3042,_loc3_);
      }
      
      private function writeDetailToBody(param1:int, param2:InformationBodyDetail) : void
      {
         var _loc3_:UP_InformationBody = new UP_InformationBody();
         _loc3_.id_informationBody = param1;
         _loc3_.setDetail(param2);
         m_socketManager.writeInformationBody(_loc3_);
         m_socketManager.flush();
      }
      
      private function getSocketData(param1:InformationBody) : void
      {
         var _loc8_:DOWN_LogInSuccess = null;
         var _loc15_:DOWN_PlayerTheSocietyData = null;
         var _loc4_:DOWN_CreateSocietySuccess = null;
         var _loc11_:DOWN_IsSuccessJoin = null;
         var _loc9_:DOWN_VerifyReturn = null;
         var _loc12_:DOWN_GetLogicServerReturn = null;
         var _loc16_:DOWN_LeaveTheSocietySuccess = null;
         var _loc7_:SaveTaskInfo = null;
         var _loc20_:DOWN_DissolveSocietyReturn = null;
         var _loc17_:DOWN_CancelDissolveSocietyReturn = null;
         var _loc19_:DOWN_DissolveTheSociety_Broadcast = null;
         var _loc14_:SaveTaskInfo = null;
         var _loc13_:SaveTaskInfo = null;
         var _loc3_:SaveTaskInfo = null;
         var _loc18_:DOWN_ApplyJoinReturn = null;
         var _loc21_:DOWN_CancelApplyJoinReturn = null;
         var _loc2_:DOWN_TheSocietyListOfApplyReturn = null;
         var _loc6_:DOWN_AddConValueReturn = null;
         var _loc5_:DOWN_DecConValueReturn = null;
         var _loc10_:DOWN_GetChatInfor = null;
         addWasteInforBody(param1);
         GamingUI.getInstance().unLockGamingUI();
         switch(param1.id_informationBody)
         {
            case 3001:
               trace("获取到了登录返回信息\n");
               m_isGetServerData = false;
               _loc8_ = param1.getDetail() as DOWN_LogInSuccess;
               trace("logInSuccess infor uid:" + _loc8_.getUid() + "  idx:" + _loc8_.getIdx() + "isHaveSociety:" + _loc8_.getIsHaveSociety() + "\n");
               if(String(_loc8_.getUid()) == GameData.getInstance().getLoginReturnData().getUid() && _loc8_.getIdx() == GameData.getInstance().getSaveFileData().index)
               {
                  loginSuccess(_loc8_.getIsHaveSociety());
               }
               break;
            case 3007:
               _loc15_ = param1.getDetail() as DOWN_PlayerTheSocietyData;
               GamingUI.getInstance().player1.getSocietyDataVO().getDataFromDownSocietyData(_loc15_);
               ClearUtil.clearObject(_loc15_);
               _loc8_ = null;
               if(m_isLogInComplete == false)
               {
                  loginComplete();
               }
               break;
            case 3023:
               _loc4_ = param1.getDetail() as DOWN_CreateSocietySuccess;
               if(_loc4_.getIsSuccess())
               {
                  if(m_societyListOfApply)
                  {
                     m_societyListOfApply.clearSocietyIdsOfApply();
                  }
                  GamingUI.getInstance().player1.getSocietyDataVO().clearSocietyData();
                  getTheSocietyData();
                  trace("如果创建成功则重新请求玩家点券数据");
                  AnalogServiceHoldFunction.getInstance().getTotalRechargedFun(null,null,null,null);
                  AnalogServiceHoldFunction.getInstance().getBalance();
                  m_gamingUI.openExSocietyPanel();
               }
               break;
            case 3016:
               _loc11_ = param1.getDetail() as DOWN_IsSuccessJoin;
               if(_loc11_.getIsAgree())
               {
                  if(m_societyListOfApply)
                  {
                     m_societyListOfApply.clearSocietyIdsOfApply();
                  }
                  GamingUI.getInstance().player1.getSocietyDataVO().clearSocietyData();
                  getTheSocietyData();
                  trace("如果加入成功则重新请求玩家点券数据");
                  AnalogServiceHoldFunction.getInstance().getTotalRechargedFun(null,null,null,null);
                  AnalogServiceHoldFunction.getInstance().getBalance();
                  m_gamingUI.openExSocietyPanel();
                  break;
               }
               if(m_societyListOfApply)
               {
                  m_societyListOfApply.decOneSocietyIdOfApply(_loc11_.getSocietyId());
               }
               break;
            case 2001:
               _loc9_ = param1.getDetail() as DOWN_VerifyReturn;
               m_isGetServerData = false;
               if(_loc9_.getIsSuccess())
               {
                  trace("验证成功！");
                  m_newEncryValue = _loc9_.getEncryValue();
                  m_socketManager.setSession(_loc9_.getSession());
                  linkLogicServer();
                  break;
               }
               GamingUI.getInstance().showMessageTip("登录失败");
               trace("验证失败！");
               break;
            case 3045:
               m_isGetServerData = false;
               _loc12_ = param1.getDetail() as DOWN_GetLogicServerReturn;
               m_logicServerHost = _loc12_.getHost();
               m_logicServerPort = _loc12_.getPort();
               trace("m_logicServerHost new value:" + m_logicServerHost + "m_logicServerPort new value:" + m_logicServerPort);
               linkVerifyserver();
               break;
            case 3011:
               _loc16_ = param1.getDetail() as DOWN_LeaveTheSocietySuccess;
               if(_loc16_.getIsSuccess())
               {
                  GamingUI.getInstance().showMessageTip("退出帮会成功");
                  GamingUI.getInstance().player1.getSocietyDataVO().clearSocietyData();
                  _loc7_ = new SaveTaskInfo();
                  _loc7_.type = "4399";
                  _loc7_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc7_);
                  MyFunction2.saveGame();
                  m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
                  m_gamingUI.closeSocietySystem();
                  break;
               }
               GamingUI.getInstance().showMessageTip("退出帮会失败");
               break;
            case 3025:
               _loc20_ = param1.getDetail() as DOWN_DissolveSocietyReturn;
               if(_loc20_.getIsSuccess())
               {
                  getTheSocietyData();
               }
               break;
            case 3039:
               _loc17_ = param1.getDetail() as DOWN_CancelDissolveSocietyReturn;
               if(_loc17_.getIsSuccess())
               {
                  m_gamingUI.player1.getSocietyDataVO().setDissolveRematinTimeToZero();
               }
               break;
            case 3026:
               _loc19_ = param1.getDetail() as DOWN_DissolveTheSociety_Broadcast;
               if(m_gamingUI.player1.getSocietyDataVO().getUid_leader() == Number(GameData.getInstance().getLoginReturnData().getUid()))
               {
                  GamingUI.getInstance().showMessageTip("解散帮会成功");
               }
               else
               {
                  GamingUI.getInstance().showMessageTip("该帮会帮主已解散了该帮会");
               }
               GamingUI.getInstance().player1.getSocietyDataVO().clearSocietyData();
               _loc14_ = new SaveTaskInfo();
               _loc14_.type = "4399";
               _loc14_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc14_);
               MyFunction2.saveGame();
               m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
               m_gamingUI.closeSocietySystem();
               _loc13_ = new SaveTaskInfo();
               _loc13_.type = "4399";
               _loc13_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc13_);
               MyFunction2.saveGame();
               break;
            case 3035:
               GamingUI.getInstance().showMessageTip("您已被帮主踢出帮会");
               GamingUI.getInstance().player1.getSocietyDataVO().clearSocietyData();
               _loc3_ = new SaveTaskInfo();
               _loc3_.type = "4399";
               _loc3_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc3_);
               MyFunction2.saveGame();
               m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
               m_gamingUI.closeSocietySystem();
               break;
            case 3013:
               _loc18_ = param1.getDetail() as DOWN_ApplyJoinReturn;
               if(_loc18_.getData() == 1)
               {
                  if(m_societyListOfApply)
                  {
                     m_societyListOfApply.addOneSocietyIdOfApply(_loc18_.getSocietyId());
                  }
               }
               break;
            case 3018:
               _loc21_ = param1.getDetail() as DOWN_CancelApplyJoinReturn;
               if(_loc21_.getIsSuccess())
               {
                  if(m_societyListOfApply)
                  {
                     m_societyListOfApply.decOneSocietyIdOfApply(_loc21_.getSocietyId());
                  }
               }
               break;
            case 3037:
               _loc2_ = param1.getDetail() as DOWN_TheSocietyListOfApplyReturn;
               m_societyListOfApply = _loc2_;
            case 3028:
               _loc6_ = param1.getDetail() as DOWN_AddConValueReturn;
               break;
            case 3030:
               _loc5_ = param1.getDetail() as DOWN_DecConValueReturn;
               break;
            case 3043:
               _loc10_ = param1.getDetail() as DOWN_GetChatInfor;
               m_allGetInfor.push(_loc10_);
               downGetChatInfor(_loc10_);
         }
      }
      
      private function loginSuccess(param1:Boolean) : void
      {
         trace("登录成功！");
         m_isLogIn = true;
         m_socketManager.setEncryptByteArr(m_newEncryValue);
         trace("是否有帮会 isHaveSociety:",param1);
         if(param1)
         {
            getTheSocietyData();
         }
         else if(m_isLogInComplete == false)
         {
            loginComplete();
         }
         var _loc3_:UP_HeartBeatBagDetail = new UP_HeartBeatBagDetail();
         _loc3_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         var _loc2_:UP_InformationBody = new UP_InformationBody();
         _loc2_.id_informationBody = 3003;
         _loc2_.setDetail(_loc3_);
         m_socketManager.openHeartBeat(_loc2_);
      }
      
      private function loginComplete() : void
      {
         var _loc3_:int = 0;
         if(m_isLogInComplete)
         {
            return;
         }
         m_isLogInComplete = true;
         var _loc2_:Vector.<ISocietySystemListener> = !!m_societySystemListeners ? m_societySystemListeners.slice(0) : null;
         var _loc1_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].loginComplete();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      private function getTheSocietyDataSuccess() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<ISocietySystemListener> = !!m_societySystemListeners ? m_societySystemListeners.slice(0) : null;
         var _loc1_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].getTheSocietyDataSuccess();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      private function downGetChatInfor(param1:DOWN_GetChatInfor) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<ISocietySystemListener> = !!m_societySystemListeners ? m_societySystemListeners.slice(0) : null;
         var _loc2_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_[_loc4_])
            {
               _loc3_[_loc4_].downGetChatInfor(param1);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc3_,false,false,false);
      }
      
      private function logOut() : void
      {
         m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
         m_gamingUI.closeSocietySystem();
      }
      
      private function close() : void
      {
         m_isLogIn = false;
         m_islink = false;
         m_isLogInComplete = false;
         logOut();
         GamingUI.getInstance().showMessageTip("悲剧了，次元空间之间出现断层，无法连接。");
      }
      
      private function connectComplete() : void
      {
         m_islink = false;
         if(m_currentLinkHost == "yxkf.lj.aiwan4399.com" && m_currentLinkPort == 7076)
         {
            trace("获取逻辑服务器端口的服务器连接成功");
            trace("获取逻辑服务器接口 host和port");
            getLogicSever();
         }
         else if(m_currentLinkHost == "yxkf.lj.aiwan4399.com" && m_currentLinkPort == 7050)
         {
            trace("验证服务器连接成功");
            trace("上发验证信息");
            verify();
         }
         else
         {
            if(!(m_currentLinkHost == m_logicServerHost && m_currentLinkPort == m_logicServerPort))
            {
               throw new Error("host and port 出错了！");
            }
            trace("逻辑服务器连接成功");
            trace("尝试登录");
            logIn();
         }
      }
      
      private function linkServerForGetLogicServer() : void
      {
         trace("尝试连接获取逻辑服务器端口的服务器 host:yxkf.lj.aiwan4399.comport:7076");
         m_socketManager.connect("yxkf.lj.aiwan4399.com",7076);
         m_currentLinkHost = "yxkf.lj.aiwan4399.com";
         m_currentLinkPort = 7076;
         m_islink = true;
      }
      
      private function linkVerifyserver() : void
      {
         trace("尝试连接验证服务器：host:yxkf.lj.aiwan4399.comport:7050");
         m_socketManager.connect("yxkf.lj.aiwan4399.com",7050);
         m_currentLinkHost = "yxkf.lj.aiwan4399.com";
         m_currentLinkPort = 7050;
         m_islink = true;
      }
      
      private function linkLogicServer() : void
      {
         trace("尝试连接逻辑服务器：host:" + m_logicServerHost + "port:" + m_logicServerPort);
         m_socketManager.connect(m_logicServerHost,m_logicServerPort);
         m_currentLinkHost = m_logicServerHost;
         m_currentLinkPort = m_logicServerPort;
         m_islink = true;
         trace("尝试连接");
      }
      
      private function getLogicSever() : void
      {
         m_gamingUI.lockGamingUI("载入中。。。");
         var _loc1_:UP_GetLogicServer = new UP_GetLogicServer();
         _loc1_.initData();
         var _loc2_:UP_InformationBody = new UP_InformationBody();
         _loc2_.id_informationBody = 3044;
         _loc2_.setDetail(_loc1_);
         m_socketManager.writeInformationBody(_loc2_);
         m_socketManager.flush();
         m_isGetServerData = true;
      }
      
      private function verify() : void
      {
         m_gamingUI.lockGamingUI("载入中。。。");
         var _loc2_:UP_Verify = new UP_Verify();
         _loc2_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
         var _loc1_:UP_InformationBody = new UP_InformationBody();
         _loc1_.id_informationBody = 2000;
         _loc1_.setDetail(_loc2_);
         m_socketManager.writeInformationBody(_loc1_);
         m_socketManager.flush();
         m_isGetServerData = true;
         trace("发送验证信息！");
      }
      
      private function logIn() : void
      {
         m_gamingUI.lockGamingUI("载入中。。。");
         var _loc5_:UP_LogInDetail = new UP_LogInDetail();
         var _loc1_:Number = Number(GameData.getInstance().getLoginReturnData().getUid());
         var _loc2_:int = int(GameData.getInstance().getSaveFileData().index);
         var _loc3_:String = Boolean(NicknameData.getInstance().myDataInNicknameRankList) && Boolean(NicknameData.getInstance().myDataInNicknameRankList.extra) ? NicknameData.getInstance().myDataInNicknameRankList.extra : "";
         _loc5_.initData(_loc1_,_loc2_,_loc3_,GamingUI.getInstance().player1.playerVO.level);
         var _loc4_:UP_InformationBody = new UP_InformationBody();
         _loc4_.id_informationBody = 3000;
         _loc4_.setDetail(_loc5_);
         m_socketManager.writeInformationBody(_loc4_);
         m_socketManager.flush();
         m_isGetServerData = true;
         trace("发送登录信息， id:3000");
      }
      
      private function ioError(param1:String) : void
      {
         trace("输入/输出流发生错误，导致加载失败");
         GamingUI.getInstance().showMessageTip("悲剧了，次元空间之间出现断层，无法连接。");
         m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
         m_gamingUI.closeSocietySystem();
         m_islink = false;
      }
      
      private function securityError(param1:String) : void
      {
         trace("安全错误，导致加载失败");
         GamingUI.getInstance().showMessageTip("很可惜，您和不同次元空间帮会里的他们失去了联系，无法获取他们的信息。");
         m_gamingUI.player1.getSocietyDataVO().clearSocietyData();
         m_gamingUI.closeSocietySystem();
         m_islink = false;
      }
      
      private function getSocietySystemXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_societySystemXML = param1.resultXML;
         m_socketManager = new SocketManager();
         m_socketManager.setVersionControl(m_versionControl);
         m_socketManager.init();
         m_socketListener = new SocketListener();
         m_socketListener.getSocketDataFun = getSocketData;
         m_socketListener.closeFun = close;
         m_socketListener.connectCompleteFun = connectComplete;
         m_socketListener.securityErrorFun = securityError;
         m_socketListener.ioErrorFun = ioError;
         m_socketManager.addSocketListener(m_socketListener);
         linkServerForGetLogicServer();
      }
      
      private function getSocietySystemXMLFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeSocietySystem();
         GamingUI.getInstance().showMessageTip("加载配置失败");
      }
   }
}

