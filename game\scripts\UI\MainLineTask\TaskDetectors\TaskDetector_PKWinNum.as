package UI.MainLineTask.TaskDetectors
{
   import UI.GamingUI;
   import UI.PKUI.PlayerDataForPK;
   
   public class TaskDetector_PKWinNum extends TaskDetector
   {
      public static const PK_ONE:String = "pkOne";
      
      public static const PK_TWO:String = "pkTwo";
      
      private var m_pkType1:String;
      
      private var m_pkWinNum1:uint;
      
      private var m_pkType2:String;
      
      private var m_pkWinNum2:uint;
      
      public function TaskDetector_PKWinNum()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_pkType1 = String(param1.pk[0].@pkType);
         m_pkWinNum1 = uint(param1.pk[0].@winNum);
         if(param1.pk[1])
         {
            m_pkType2 = String(param1.pk[1].@pkType);
            m_pkWinNum2 = uint(param1.pk[1].@winNum);
         }
      }
      
      override public function detect() : void
      {
         if(Boolean(m_pkType1) && m_pkWinNum1)
         {
            if(Boolean(m_pkType2) == false && detectWinNum(m_pkType1,m_pkWinNum1))
            {
               GamingUI.getInstance().taskEvent(m_eventStr);
            }
            else if(Boolean(m_pkType2) && detectWinNum(m_pkType1,m_pkWinNum1) && detectWinNum(m_pkType2,m_pkWinNum2))
            {
               GamingUI.getInstance().taskEvent(m_eventStr);
            }
         }
      }
      
      private function detectWinNum(param1:String, param2:uint) : Boolean
      {
         var _loc3_:Boolean = false;
         if(param1 == "pkOne")
         {
            if(param2 <= PlayerDataForPK.getInstance().winMatch)
            {
               _loc3_ = true;
            }
         }
         return _loc3_;
      }
   }
}

