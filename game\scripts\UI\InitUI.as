package UI
{
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.BuffData;
   import UI.CheatData.CheatData;
   import UI.DetectionClass.DetectionClass;
   import UI.EquipmentMakeAndUpgrade.EqMagicVO;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.ExchangeGiftBag.ExchangeGiftData;
   import UI.Farm.FarmData;
   import UI.Farm.FarmShowObjectVO;
   import UI.Farm.Land.LandVO;
   import UI.HatchPanel.HatchVO;
   import UI.MiragePanel.MirageData;
   import UI.NicknameSystem.NicknameData;
   import UI.NicknameSystem.NicknameSaveData;
   import UI.PKUI.PlayerDataForPK;
   import UI.Pets.Pet;
   import UI.Players.ChangEVO;
   import UI.Players.DragonVO;
   import UI.Players.ErLangShenVO;
   import UI.Players.FoxVO;
   import UI.Players.HouyiVO;
   import UI.Players.MonkeyVO;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.Players.ShiFuVO;
   import UI.Players.TieShanVO;
   import UI.Players.VipVO;
   import UI.Players.ZiXiaVO;
   import UI.Protect.ProtectData;
   import UI.RefineFactory.LianDanFurnace.LianDanFurnaceVO;
   import UI.RefineFactory.RefineFactoryData;
   import UI.ShiTu.IXiuLianTargetVO;
   import UI.ShiTu.TuDiSkillVO;
   import UI.ShiTu.TuDiVO;
   import UI.ShiTu.XiuLianContent;
   import UI.SignPanel.SignData;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.Task.TaskFunction;
   import UI.Task.TaskGoalManager;
   import UI.Task.TaskGoalVO;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.EveryDayTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.UIInterface.IActiveSkillVO;
   import UI.UIInterface.ILimitEquipmentVO;
   import UI2.medalPanel.MedalFunction;
   import YJFY.API_4399.SaveAPI.SaveAPI;
   import YJFY.GameData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class InitUI
   {
      public static var _instance:InitUI = null;
      
      private var remArr:Array;
      
      public function InitUI()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已存在了吗？！");
      }
      
      public static function getInstance() : InitUI
      {
         if(!_instance)
         {
            _instance = new InitUI();
         }
         return _instance;
      }
      
      public function initMonkey(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc10_:MonkeyVO = new MonkeyVO();
         _loc10_.playerType = "SunWuKong";
         initPlayerVO(_loc10_,param1.Monkey[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc9_:Player = new Player(_loc10_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc9_,0);
         }
         return _loc9_;
      }
      
      public function initDragon(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc9_:DragonVO = new DragonVO();
         _loc9_.playerType = "BaiLongMa";
         initPlayerVO(_loc9_,param1.Dragon[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc10_:Player = new Player(_loc9_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc10_,0);
         }
         return _loc10_;
      }
      
      public function initErLangShen(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc9_:ErLangShenVO = new ErLangShenVO();
         _loc9_.playerType = "ErLangShen";
         initPlayerVO(_loc9_,param1.ErLangShen[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc10_:Player = new Player(_loc9_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc10_,0);
         }
         return _loc10_;
      }
      
      public function initChangE(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc10_:ChangEVO = new ChangEVO();
         _loc10_.playerType = "ChangE";
         initPlayerVO(_loc10_,param1.ChangE[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc9_:Player = new Player(_loc10_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc9_,0);
         }
         return _loc9_;
      }
      
      public function initFox(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc9_:FoxVO = new FoxVO();
         _loc9_.playerType = "Fox";
         initPlayerVO(_loc9_,param1.Fox[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc10_:Player = new Player(_loc9_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc10_,0);
         }
         return _loc10_;
      }
      
      public function initTieShan(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc9_:TieShanVO = new TieShanVO();
         _loc9_.playerType = "TieShan";
         initPlayerVO(_loc9_,param1.TieShan[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc10_:Player = new Player(_loc9_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc10_,0);
         }
         return _loc10_;
      }
      
      public function initHouYi(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc9_:HouyiVO = new HouyiVO();
         _loc9_.playerType = "Houyi";
         initPlayerVO(_loc9_,param1.Houyi[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc10_:Player = new Player(_loc9_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc10_,0);
         }
         return _loc10_;
      }
      
      public function initZiXia(param1:XML, param2:XML, param3:XML, param4:XML, param5:XML, param6:String, param7:int, param8:String = "playerOne") : Player
      {
         var _loc10_:ZiXiaVO = new ZiXiaVO();
         _loc10_.playerType = "ZiXia";
         initPlayerVO(_loc10_,param1.ZiXia[0],param2,param3,param4,param5,param6,param7,param8);
         var _loc9_:Player = new Player(_loc10_);
         if(param7 & 1)
         {
            MyFunction.getInstance().refreshPlayer(_loc9_,0);
         }
         return _loc9_;
      }
      
      public function getCurPlayerXml(param1:PlayerVO) : XML
      {
         if(SaveAPI.saveFileDataBackup)
         {
            if(param1 is HouyiVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].Houyi[0];
            }
            if(param1 is MonkeyVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].Monkey[0];
            }
            if(param1 is DragonVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].Dragon[0];
            }
            if(param1 is TieShanVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].TieShan[0];
            }
            if(param1 is FoxVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].Fox[0];
            }
            if(param1 is ChangEVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].ChangE[0];
            }
            if(param1 is ErLangShenVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].ErLangShen[0];
            }
            if(param1 is ZiXiaVO)
            {
               return SaveAPI.saveFileDataBackup.saveXML.Data[0].ZiXia[0];
            }
         }
         return null;
      }
      
      private function initPlayerVO(param1:PlayerVO, param2:XML, param3:XML, param4:XML, param5:XML, param6:XML, param7:String, param8:int, param9:String = "playerOne") : void
      {
         var _loc14_:int = 0;
         var _loc18_:int = 0;
         var _loc22_:* = null;
         var _loc19_:int = 0;
         var _loc13_:XML = null;
         var _loc23_:XML = null;
         var _loc20_:XMLList = null;
         var _loc21_:* = undefined;
         var _loc17_:XML = null;
         var _loc11_:ShiFuVO = null;
         var _loc10_:HatchVO = null;
         var _loc15_:XML = XMLSingle.getInstance().xiuLianContentXML;
         if(param8 & 1)
         {
            _loc22_ = getCurPlayerXml(param1);
            if(!_loc22_)
            {
               _loc22_ = param2;
            }
            param1.name = String(_loc22_.@name);
            param1.playerID = String(_loc22_.@playerId);
            param1.experiencePercent = Number(_loc22_.@experiencePercent);
            param1.bloodPercent = Number(_loc22_.@bloodPercent);
            param1.magicPercent = Number(_loc22_.@magicPercent);
            param1.level = int(_loc22_.@level);
            param1.money = int(_loc22_.@money);
            param1.nuQiAllValue = int(param6.NuQi[0].@allValue);
            param1.isShowFashionShow = Boolean(int(_loc22_.@sF));
            _loc19_ = int(param6.Money[0].@maxMoney);
            if(param1.money > _loc19_)
            {
               param1.money = _loc19_;
            }
            XMLSingle.setPlayerVO(param1);
         }
         if(param8 & 2)
         {
            _loc22_ = null;
            _loc22_ = getCurPlayerXml(param1);
            if(!_loc22_)
            {
               _loc22_ = param2;
            }
            _loc13_ = _loc22_.Package[0];
            param1.packageEquipmentVOs = getEquipmentVOs(_loc13_,param3,param4,param5,param7);
            _loc22_ = getCurPlayerXml(param1);
            if(_loc22_)
            {
               _loc23_ = _loc22_.Package[0];
               if(_loc23_.toString() != _loc13_.toString())
               {
                  CheatData.getInstance().addCheatDataStr("背包数据被篡改");
               }
            }
         }
         if(param8 & 4)
         {
            _loc22_ = null;
            _loc22_ = getCurPlayerXml(param1);
            if(!_loc22_)
            {
               _loc22_ = param2;
            }
            _loc13_ = _loc22_.Storage[0];
            param1.storageEquipmentXml = _loc13_;
            _loc22_ = getCurPlayerXml(param1);
            if(_loc22_)
            {
               _loc23_ = _loc22_.Storage[0];
               if(_loc23_.toString() != _loc13_.toString())
               {
                  CheatData.getInstance().addCheatDataStr("仓库数据被篡改");
               }
            }
         }
         if(param8 & 2048)
         {
            _loc13_ = param2.Medals[0];
            if(_loc13_)
            {
               param1.medalEquipmentVOs = getEquipmentVOs(_loc13_,param3,param4,param5,param7);
               _loc14_ = param1.medalEquipmentVOs.length - 1;
               while(_loc14_ >= 0)
               {
                  if(!param1.medalEquipmentVOs[_loc14_])
                  {
                     param1.medalEquipmentVOs.splice(_loc14_,1);
                  }
                  else if(param1.medalEquipmentVOs[_loc14_].id == int(param2.@medal))
                  {
                     param1.medal = param1.medalEquipmentVOs[_loc14_];
                  }
                  _loc14_--;
               }
            }
            else
            {
               param1.medalEquipmentVOs = new Vector.<EquipmentVO>();
            }
         }
         if(param8 & 8)
         {
            _loc22_ = null;
            _loc22_ = getCurPlayerXml(param1);
            if(!_loc22_)
            {
               _loc22_ = param2;
            }
            _loc13_ = _loc22_.InformationPanel[0];
            param1.inforEquipmentVOs = getEquipmentVOs(_loc13_,param3,param4,param5,param7);
            _loc18_ = !!param1.inforEquipmentVOs ? param1.inforEquipmentVOs.length : 0;
            _loc14_ = 0;
            while(_loc14_ < _loc18_)
            {
               if(param1.inforEquipmentVOs[_loc14_])
               {
                  if(param1.inforEquipmentVOs[_loc14_].equipmentType == "medal")
                  {
                     MedalFunction.getInstance().addMedalToPlayer(param1.inforEquipmentVOs[_loc14_] as MedalEquipmentVO,param1);
                     param1.inforEquipmentVOs[_loc14_] = null;
                  }
                  else
                  {
                     param1.inforEquipmentVOs[_loc14_].playerVO = param1;
                  }
               }
               _loc14_++;
            }
            _loc22_ = getCurPlayerXml(param1);
            if(_loc22_)
            {
               _loc23_ = _loc22_.InformationPanel[0];
               if(_loc23_.toString() != _loc13_.toString())
               {
                  CheatData.getInstance().addCheatDataStr("装备面板数据被篡改");
               }
            }
         }
         if(param8 & 16)
         {
            _loc22_ = null;
            _loc22_ = getCurPlayerXml(param1);
            if(!_loc22_)
            {
               _loc22_ = param2;
            }
            _loc13_ = _loc22_.PetPanel[0];
            param1.pet = getPet(_loc13_,param3,param4,param5);
            param1.pet.playerVO = param1;
            param1.pet.petEquipmentVO = param1.pet.petEquipmentVO;
         }
         if(param8 & 64)
         {
            initDanMedicine(param1,param2,param3);
         }
         if(param8 & 32)
         {
            _loc13_ = param2.Skill[0];
            param1.skillVOs = getSkillVOs(_loc13_,param4);
         }
         if(param8 & 128)
         {
            if(param2.hasOwnProperty("ShiTu"))
            {
               _loc20_ = param2.ShiTu[0].xiuLian;
            }
            else
            {
               _loc20_ = null;
            }
            _loc17_ = XMLSingle.getInstance().xiuLianContentXML;
            _loc21_ = MyFunction.getInstance().excreteStringToString(_loc15_.@shiFuHaveContents);
            initXiuLianContents(param1,_loc20_,"ShiFu",_loc15_,_loc21_);
            MyFunction.getInstance().refreshXiuLianTargetVO(param1);
            _loc11_ = new ShiFuVO();
            _loc11_.initBySaveXML(param2.ShiTu[0]);
            param1.shiFuVO = _loc11_;
         }
         if(param8 & 1024)
         {
            _loc10_ = new HatchVO();
            if(param2.hasOwnProperty("Hatch"))
            {
               _loc10_.initFromSaveXML(param2.Hatch[0],param7);
            }
            param1.hatchVO = _loc10_;
         }
         var _loc16_:EqMagicVO = new EqMagicVO();
         if(param2.hasOwnProperty("EqMagicT"))
         {
            _loc16_.initFromSaveXML(param2.EqMagicT[0],param7);
         }
         param1.eqMagicVO = _loc16_;
         param1.playerID = param9;
      }
      
      public function initXiuLianContents(param1:IXiuLianTargetVO, param2:XMLList, param3:String, param4:XML, param5:Vector.<String>) : void
      {
         var _loc7_:int = 0;
         var _loc6_:int = int(!!param2 ? param2.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            param1.addXiuLianContent(initXiuLianContent(param2[_loc7_],param4,param3));
            _loc7_++;
         }
         _loc6_ = !!param5 ? param5.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            if(param1.getXiuLianContentByContent(param5[_loc7_]) == null)
            {
               param1.addXiuLianContent(XMLSingle.getInstance().getXiuLianContent0LevelByContent(param5[_loc7_],param4,param3));
            }
            _loc7_++;
         }
      }
      
      private function initXiuLianContent(param1:XML, param2:XML, param3:String) : XiuLianContent
      {
         var _loc4_:XiuLianContent = XMLSingle.getInstance().getXiuLianContent(param1.@id,XMLSingle.getInstance().xiuLianContentXML,param3);
         _loc4_.currentXiuLianValue = int(param1.@xLValue);
         return _loc4_;
      }
      
      public function initTuDi(param1:XML, param2:XML, param3:XML, param4:String) : TuDiVO
      {
         var _loc6_:XML = null;
         var _loc8_:TuDiVO = null;
         var _loc5_:String = null;
         var _loc13_:XMLList = null;
         var _loc9_:int = 0;
         var _loc12_:int = 0;
         var _loc10_:String = null;
         var _loc7_:TuDiSkillVO = null;
         var _loc14_:* = undefined;
         var _loc11_:XML = XMLSingle.getInstance().tuDiSkillXML;
         if(param1.hasOwnProperty("ShiTu"))
         {
            _loc6_ = param1.ShiTu[0];
         }
         else
         {
            _loc6_ = <ShiTu></ShiTu>;
         }
         if(_loc6_.hasOwnProperty("TuDi"))
         {
            _loc8_ = new TuDiVO();
            _loc8_.type = String(_loc6_.TuDi[0].@type);
            _loc8_.bloodPercent = Number(_loc6_.TuDi[0].@bloodPercent);
            _loc8_.energyPercent = Number(_loc6_.TuDi[0].@energyPercent);
            _loc8_.currentUsedXianLianNum = int(_loc6_.TuDi[0].@xLUsedNum);
            _loc8_.buyXiuLianNum = int(_loc6_.TuDi[0].@xLBuyNum);
            _loc5_ = String(_loc6_.TuDi[0].@xLDate);
            if(MyFunction.getInstance().newDateIsNewDay(_loc5_,param4))
            {
               _loc8_.currentUsedXianLianNum = 0;
               _loc8_.buyXiuLianNum = 0;
               _loc8_.xiuLianDate = param4;
            }
            else
            {
               _loc8_.xiuLianDate = _loc5_;
            }
            _loc8_.baseLevel = 1;
            _loc13_ = _loc6_.TuDi[0].skill;
            _loc12_ = int(!!_loc13_ ? _loc13_.length() : 0);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               _loc10_ = String(_loc13_[_loc9_].@id);
               _loc7_ = new TuDiSkillVO();
               _loc7_.id = _loc10_;
               TuDiSkillVO.setTuDiSkillVO(_loc7_,_loc11_);
               _loc8_.addTuDiSkillVO(_loc7_);
               _loc9_++;
            }
            _loc13_ = _loc6_.TuDi[0].xiuLian;
            _loc14_ = MyFunction.getInstance().excreteStringToString(param2.@haveContents);
            initXiuLianContents(_loc8_,_loc13_,"TuDi",param3,_loc14_);
            MyFunction.getInstance().refreshXiuLianTargetVO(_loc8_);
            setTuDiVOBase(_loc8_,param2,_loc11_);
            _loc8_.currentXiuLianValue = int(_loc6_.TuDi[0].@xLValue);
         }
         return _loc8_;
      }
      
      public function setTuDiVOBase(param1:TuDiVO, param2:XML, param3:XML) : void
      {
         var _loc4_:XML = null;
         var _loc8_:int = 0;
         var _loc7_:TuDiSkillVO = null;
         var _loc5_:String = null;
         var _loc9_:XMLList = param2.skill;
         param1.baseXiuLianNum = int(param2.@xiuLianNum);
         _loc4_ = param2.item.(@level == param1.level)[0];
         param1.baseBlood = int(_loc4_.@blood);
         param1.baseEnergy = int(_loc4_.@energy);
         param1.baseXiuLianValue = int(_loc4_.@xiuLianValue);
         param1.baseAttack = int(_loc4_.@attack);
         param1.baseDefence = int(_loc4_.@defence);
         param1.baseCriticalRate = int(_loc4_.@criticalRate);
         param1.baseEnergyRecover = Number(_loc4_.@energyRecover);
         var _loc6_:int = int(!!_loc9_ ? _loc9_.length() : 0);
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            _loc5_ = String(_loc9_[_loc8_].@id2);
            _loc7_ = param1.getTuDiSkillVOById2(_loc5_);
            if(_loc7_ == null)
            {
               param1.addTuDiSkillVO(TuDiSkillVO.createInitLevelTuDiSkillVOById2(_loc5_,param3));
            }
            _loc8_++;
         }
      }
      
      private function initDanMedicine(param1:PlayerVO, param2:XML, param3:XML) : void
      {
         var _loc5_:XML = null;
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         if(param2.hasOwnProperty("DMPanel"))
         {
            _loc5_ = param2.DMPanel[0];
         }
         else
         {
            _loc5_ = <DMPanel>
								<attack />
								<defence />
						 </DMPanel>
			;
         }
         _loc4_ = 7;
         param1.attackDanMedicineEquipmentVOGrid = new Vector.<Vector.<EquipmentVO>>(_loc4_);
         param1.defenceDanMedicineEquipmentVOGrid = new Vector.<Vector.<EquipmentVO>>(_loc4_);
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            param1.attackDanMedicineEquipmentVOGrid[_loc8_] = new Vector.<EquipmentVO>(2 + _loc8_ * 1);
            param1.defenceDanMedicineEquipmentVOGrid[_loc8_] = new Vector.<EquipmentVO>(2 + _loc8_ * 1);
            _loc8_++;
         }
         initDanMedicineOne(param1.attackDanMedicineEquipmentVOGrid,_loc5_.attack[0],param3);
         initDanMedicineOne(param1.defenceDanMedicineEquipmentVOGrid,_loc5_.defence[0],param3);
      }
      
      private function initDanMedicineOne(param1:Vector.<Vector.<EquipmentVO>>, param2:XML, param3:XML) : void
      {
         var _loc4_:String = null;
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:int = 0;
         var _loc5_:* = undefined;
         var _loc7_:XML = <item />;
         _loc6_ = int(param1.length);
         _loc10_ = 0;
         while(_loc10_ < _loc6_)
         {
            _loc4_ = String(param2["l" + (_loc10_ + 1)]);
            if(Boolean(_loc4_))
            {
               _loc5_ = MyFunction.getInstance().excreteString(_loc4_);
               _loc8_ = int(_loc5_.length);
               _loc9_ = 0;
               while(_loc9_ < _loc8_)
               {
                  _loc7_.@id = _loc5_[_loc9_];
                  param1[_loc10_][_loc9_] = getEquipmentVO(_loc7_,param3,null,null,null);
                  _loc9_++;
               }
            }
            _loc10_++;
         }
      }
      
      public function initVIPVO(param1:XML, param2:XML, param3:XML, param4:String) : VipVO
      {
         var _loc7_:String = null;
         var _loc5_:Number = NaN;
         if(param1.VIP[0])
         {
            _loc7_ = param1.VIP[0].@oldDateGetGift;
            _loc5_ = Number(param1.VIP[0].@tPT);
         }
         else
         {
            _loc7_ = MyFunction.getInstance().splitTimeString(param4);
            _loc5_ = 0;
         }
         var _loc6_:VipVO = XMLSingle.getVipVO(_loc5_,param2,param3,param4);
         _loc6_.oldDateGetGift = _loc7_;
         return _loc6_;
      }
      
      public function initPublicStorage(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : Vector.<EquipmentVO>
      {
         var _loc6_:XML = null;
         if(SaveAPI.saveFileDataBackup)
         {
            _loc6_ = SaveAPI.saveFileDataBackup.saveXML.Data[0].PublicStorage[0];
         }
         if(!_loc6_)
         {
            _loc6_ = param1.PublicStorage[0];
         }
         return getEquipmentVOs(_loc6_,param2,param3,param4,param5);
      }
      
      public function initTaskGoals(param1:XML, param2:XML, param3:String) : void
      {
         var _loc4_:TaskGoalVO = null;
         var _loc9_:XMLList = null;
         var _loc8_:int = 0;
         var _loc5_:int = 0;
         if(!param1.hasOwnProperty("TaskGoals"))
         {
            return;
         }
         var _loc7_:XMLList = param1.TaskGoals[0].item;
         var _loc10_:int = 0;
         var _loc6_:int = int(_loc7_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc6_)
         {
            _loc9_ = _loc7_[_loc10_].item;
            _loc8_ = 0;
            _loc5_ = int(_loc9_.length());
            _loc8_ = 0;
            while(_loc8_ < _loc5_)
            {
               _loc4_ = XMLSingle.getTaskGoal(int(_loc7_[_loc10_].@id),param2);
               _loc4_.owerTaskIDs = MyFunction.getInstance().excreteString(String(_loc9_[_loc8_].@ownerTaskID));
               _loc4_.num = int(_loc9_[_loc8_].@num);
               TaskGoalManager.getInstance().taskGoalVOs.push(_loc4_);
               _loc8_++;
            }
            _loc10_++;
         }
      }
      
      public function initTask(param1:XML, param2:XML, param3:String) : void
      {
         var _loc14_:int = 0;
         var _loc12_:MTaskVO = null;
         var _loc5_:Number = NaN;
         var _loc4_:Number = NaN;
         if(!param1.hasOwnProperty("Task"))
         {
            TasksManager.getInstance().resetCount = 0;
            TasksManager.getInstance().resetDate = MyFunction.getInstance().splitTimeString(param3);
            return;
         }
         TasksManager.getInstance().resetCount = int(param1.Task[0].@reCount);
         TasksManager.getInstance().resetDate = String(param1.Task[0].@reDate);
         var _loc18_:XMLList = param1.Task[0].item;
         var _loc11_:XML = XML(param1.Task[0]).copy();
         delete _loc11_.item;
         var _loc21_:Array = [];
         var _loc16_:int = 0;
         var _loc6_:int = 0;
         var _loc15_:Boolean = false;
         var _loc7_:int = int(_loc18_.length());
         _loc16_ = 0;
         while(_loc16_ < _loc7_)
         {
            _loc15_ = false;
            _loc6_ = 0;
            while(_loc6_ < _loc21_.length)
            {
               if(int(_loc21_[_loc6_]) == int(_loc18_[_loc16_].@id))
               {
                  _loc15_ = true;
               }
               _loc6_++;
            }
            if(_loc15_ == false)
            {
               _loc21_.push(int(_loc18_[_loc16_].@id));
               _loc11_.appendChild(_loc18_[_loc16_]);
            }
            _loc16_++;
         }
         ClearUtil.clearObject(_loc21_);
         _loc21_ = null;
         var _loc19_:XMLList = _loc11_.item;
         var _loc13_:int = 0;
         var _loc17_:int = int(_loc19_.length());
         var _loc20_:int = int(TaskGoalManager.getInstance().taskGoalVOs.length);
         _loc13_ = 0;
         for(; _loc13_ < _loc17_; _loc13_++)
         {
            _loc14_ = int(_loc19_[_loc13_].@id);
            if(MyFunction.getInstance().getTaskTypeFromID(_loc14_) == 0)
            {
               if(String(_loc19_[_loc13_].@rDate) != MyFunction.getInstance().splitTimeString(param3))
               {
                  continue;
               }
            }
            else if(MyFunction.getInstance().getTaskTypeFromID(_loc14_) == 1)
            {
               _loc5_ = new TimeUtil().timeInterval(param2.Task.item.(@id == _loc14_)[0].@formerData,param3);
               _loc4_ = new TimeUtil().timeInterval(param3,param2.Task.item.(@id == _loc14_)[0].@latterData);
               if(_loc5_ < 0 || _loc4_ < 0)
               {
                  if(_loc4_ < 0)
                  {
                  }
                  continue;
               }
            }
            else
            {
               if(!(MyFunction.getInstance().getTaskTypeFromID(_loc14_) == 2 || MyFunction.getInstance().getTaskTypeFromID(_loc14_) == 3))
               {
                  throw new Error();
               }
               if(MyFunction.getInstance().getTaskTypeFromID(_loc14_) == 3)
               {
                  _loc5_ = new TimeUtil().timeInterval(param2.Task.item.(@id == _loc14_)[0].@formerData,param3);
                  _loc4_ = new TimeUtil().timeInterval(param3,param2.Task.item.(@id == _loc14_)[0].@latterData);
                  if(_loc5_ < 0 || _loc4_ < 0)
                  {
                     if(_loc4_ < 0)
                     {
                     }
                     continue;
                  }
               }
               if(String(_loc19_[_loc13_].@dDate) != MyFunction.getInstance().splitTimeString(param3))
               {
                  if(int(_loc19_[_loc13_].@state) != 3 && int(_loc19_[_loc13_].@state) != 1)
                  {
                     if(String(param2.Task.item.(@id == _loc14_)[0].@resetType) != "once")
                     {
                        continue;
                     }
                     if(String(param2.Task.item.(@id == _loc14_)[0].@resetType) == "once")
                     {
                        if(int(_loc19_[_loc13_].@state) != 2)
                        {
                           continue;
                        }
                     }
                  }
                  if(int(_loc19_[_loc13_].@state) != 1 && MyFunction.getInstance().splitDateString(MyFunction.getInstance().splitTimeString(param3)) - MyFunction.getInstance().splitDateString(String(_loc19_[_loc13_].@dDate)) > 1)
                  {
                     if(String(param2.Task.item.(@id == _loc14_)[0].@resetType) != "once")
                     {
                        continue;
                     }
                     if(String(param2.Task.item.(@id == _loc14_)[0].@resetType) == "once")
                     {
                        if(int(_loc19_[_loc13_].@state) != 2)
                        {
                           continue;
                        }
                     }
                  }
                  else if(int(_loc19_[_loc13_].@state) == 3)
                  {
                     _loc19_[_loc13_].@state = 0;
                  }
                  _loc19_[_loc13_].@dDate = MyFunction.getInstance().splitTimeString(param3);
               }
            }
            _loc12_ = XMLSingle.getTask(_loc14_,param2);
            if(_loc12_ != null)
            {
               switch(_loc12_.type)
               {
                  case "accumulatedTask":
                  case "limitingTimeAccumulatedTask":
                     (_loc12_ as AccumulatedTaskVO).currentTaskCount = int(_loc19_[_loc13_].@currentTaskCount);
                     (_loc12_ as AccumulatedTaskVO).deteDate = String(_loc19_[_loc13_].@dDate);
                     break;
                  case "everyDayTask":
                     (_loc12_ as EveryDayTaskVO).receiveTaskDate = String(_loc19_[_loc13_].@rDate);
                     break;
                  case "limitingTimeTask":
                     break;
                  default:
                     throw new Error();
               }
               _loc12_.state = int(_loc19_[_loc13_].@state);
               if(_loc12_.state == 2)
               {
                  TasksManager.getInstance().wasteTaskVOs.push(_loc12_);
               }
               else
               {
                  TaskFunction.getInstance().statisticalTaskGoalVONum(_loc12_);
                  if(MyFunction.getInstance().getTaskTypeFromID(_loc14_))
                  {
                     TasksManager.getInstance().acceptedActivityTaskVOs.push(_loc12_);
                  }
                  else
                  {
                     TasksManager.getInstance().acceptedEveryDayTaskVOs.push(_loc12_);
                  }
               }
            }
         }
      }
      
      public function dealInvalidTaskGoalVO(param1:String) : void
      {
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:Boolean = false;
         var _loc2_:int = int(TasksManager.getInstance().acceptedEveryDayTaskVOs.length);
         var _loc5_:int = int(TasksManager.getInstance().acceptedActivityTaskVOs.length);
         _loc9_ = 0;
         while(_loc9_ < TaskGoalManager.getInstance().taskGoalVOs.length)
         {
            _loc7_ = 0;
            while(_loc7_ < TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs.length)
            {
               _loc4_ = false;
               switch(MyFunction.getInstance().getTaskTypeFromID(TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs[_loc7_]))
               {
                  case 0:
                     _loc8_ = 0;
                     while(_loc8_ < _loc2_)
                     {
                        _loc4_ ||= TasksManager.getInstance().acceptedEveryDayTaskVOs[_loc8_].id == TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs[_loc7_];
                        _loc8_++;
                     }
                     break;
                  case 1:
                  case 2:
                  case 3:
                     _loc8_ = 0;
                     while(_loc8_ < _loc5_)
                     {
                        _loc4_ ||= TasksManager.getInstance().acceptedActivityTaskVOs[_loc8_].id == TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs[_loc7_];
                        _loc8_++;
                     }
                     break;
                  default:
                     throw new Error("任务id出错啦！");
               }
               if(!_loc4_)
               {
                  TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs.splice(_loc7_,1);
                  _loc7_--;
               }
               _loc7_++;
            }
            if(!TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs.length)
            {
               TaskGoalManager.getInstance().taskGoalVOs.splice(_loc9_,1);
               _loc9_--;
            }
            _loc9_++;
         }
      }
      
      public function initFarm(param1:XML, param2:XML) : void
      {
         var _loc10_:XML = null;
         var _loc11_:int = 0;
         var _loc6_:* = undefined;
         var _loc5_:FarmShowObjectVO = null;
         var _loc4_:int = 0;
         if(!param1.hasOwnProperty("Farm"))
         {
            _loc10_ = <Farm sunValue="0" sceneID="1000">
					  <land id="100100"   state="0"/>
					  <other id="1000" hv="-6_-3" />
					  <other id="1001" hv="-1_10" />
						<other id="1002" hv="2_5"/>
					  </Farm>;
         }
         else
         {
            _loc10_ = param1.Farm[0];
         }
         FarmData.getInstance().currentSunValue = int(_loc10_.@sunValue);
         FarmData.getInstance().sceneID = !!int(_loc10_.@sceneID) ? int(_loc10_.@sceneID) : 1000;
         var _loc8_:String = String(_loc10_.@bk);
         if(_loc8_)
         {
            FarmData.getInstance().farmBlockIDs = MyFunction.getInstance().excreteString(_loc8_);
         }
         else
         {
            FarmData.getInstance().farmBlockIDs = new Vector.<int>();
            FarmData.getInstance().farmBlockIDs.push(100);
         }
         FarmData.getInstance().recoverLandNum = int(_loc10_.@rLN);
         if(FarmData.getInstance().recoverLandNum)
         {
            FarmData.getInstance().recoverLandDate = String(_loc10_.@rLD);
         }
         else
         {
            FarmData.getInstance().recoverLandDate = "";
         }
         FarmData.getInstance().ownerLands = new Vector.<LandVO>();
         FarmData.getInstance().otherShowObjects = new Vector.<FarmShowObjectVO>();
         var _loc9_:XMLList = _loc10_.land;
         var _loc7_:int = int(_loc9_.length());
         _loc11_ = 0;
         while(_loc11_ < _loc7_)
         {
            FarmData.getInstance().ownerLands.push(initLand(_loc9_[_loc11_],param2));
            _loc11_++;
         }
         _loc9_ = _loc10_.other;
         _loc7_ = int(_loc9_.length());
         var _loc3_:Boolean = false;
         _loc11_ = 0;
         while(_loc11_ < _loc7_)
         {
            _loc6_ = MyFunction.getInstance().excreteString(String(_loc9_[_loc11_].@hv));
            _loc5_ = new FarmShowObjectVO();
            _loc4_ = int(_loc9_[_loc11_].@id);
            if(_loc4_ == 1002)
            {
               _loc3_ = true;
            }
            _loc5_.id = _loc4_;
            param1 = param2.otherItem.(@id == _loc4_)[0];
            _loc5_.H_width = int(param1.@H_width);
            _loc5_.V_height = int(param1.@V_height);
            _loc5_.isEnableMove = Boolean(int(param1.@isEnableMove));
            _loc5_.hI = _loc6_[0];
            _loc5_.vJ = _loc6_[1];
            FarmData.getInstance().otherShowObjects.push(_loc5_);
            _loc11_++;
         }
         if(_loc3_ == false)
         {
            _loc6_ = MyFunction.getInstance().excreteString("2_5");
            _loc5_ = new FarmShowObjectVO();
            _loc5_.id = 1002;
            _loc5_.H_width = 1;
            _loc5_.V_height = 1;
            _loc5_.isEnableMove = false;
            _loc5_.hI = _loc6_[0];
            _loc5_.vJ = _loc6_[1];
            FarmData.getInstance().otherShowObjects.push(_loc5_);
         }
      }
      
      public function initLianDanFactory(param1:XML) : void
      {
         var _loc2_:XML = null;
         if(!param1.hasOwnProperty("LDF"))
         {
            _loc2_ = <LDF id="100100" state="0"></LDF>;
         }
         else
         {
            _loc2_ = param1.LDF[0];
         }
         var _loc3_:LianDanFurnaceVO = new LianDanFurnaceVO();
         _loc3_.id = int(_loc2_.@id);
         _loc3_.state = int(_loc2_.@state);
         RefineFactoryData.getInstance().atOnceCompleteNum = int(_loc2_.@cRN);
         if(RefineFactoryData.getInstance().atOnceCompleteNum)
         {
            RefineFactoryData.getInstance().atOnceCompleteDate = String(_loc2_.@cRD);
         }
         else
         {
            RefineFactoryData.getInstance().atOnceCompleteDate = "";
         }
         switch(_loc3_.state)
         {
            case 0:
               break;
            case 1:
               _loc3_.date = String(_loc2_.@date);
               RefineFactoryData.getInstance().materialID = int(_loc2_.material.@id);
               RefineFactoryData.getInstance().materialNum = int(_loc2_.material.@num);
               RefineFactoryData.getInstance().refineTargetID = int(_loc2_.target.@id);
               RefineFactoryData.getInstance().refineTargetNum = int(_loc2_.target.@num);
               break;
            case 2:
               RefineFactoryData.getInstance().materialID = int(_loc2_.material.@id);
               RefineFactoryData.getInstance().materialNum = int(_loc2_.material.@num);
               RefineFactoryData.getInstance().refineTargetID = int(_loc2_.target.@id);
               RefineFactoryData.getInstance().refineTargetNum = int(_loc2_.target.@num);
               break;
            default:
               throw new Error("状态类型错误!");
         }
         RefineFactoryData.getInstance().lianDanFurnace = _loc3_;
      }
      
      public function initBuffs(param1:XML, param2:String, param3:XML, param4:Player, param5:Player) : void
      {
         BuffData.getInstance().buffDrives = new Vector.<BuffDrive>();
         BuffData.getInstance().buffDrives_playerOne = new Vector.<BuffDrive>();
         BuffData.getInstance().buffDrives_playerTwo = new Vector.<BuffDrive>();
         if(param1.hasOwnProperty("Buff"))
         {
            if(param1.Buff[0].hasOwnProperty("pu"))
            {
               initBuffDrives(param1.Buff[0].pu[0].item,BuffData.getInstance().buffDrives,null,param3,param2);
            }
            if(param1.Buff[0].hasOwnProperty("one"))
            {
               initBuffDrives(param1.Buff[0].one[0].item,BuffData.getInstance().buffDrives_playerOne,param4,param3,param2);
            }
            if(param1.Buff[0].hasOwnProperty("two"))
            {
               initBuffDrives(param1.Buff[0].two[0].item,BuffData.getInstance().buffDrives_playerTwo,param5,param3,param2);
            }
         }
      }
      
      public function initBuffDrives(param1:XMLList, param2:Vector.<BuffDrive>, param3:Player, param4:XML, param5:String) : void
      {
         var _loc6_:BuffVO = null;
         var _loc7_:BuffDrive = null;
         var _loc9_:int = 0;
         var _loc8_:int = int(param1.length());
         _loc9_ = 0;
         for(; _loc9_ < _loc8_; _loc9_++)
         {
            if(int(param1[_loc9_].@id) == 12000)
            {
               continue;
            }
            _loc6_ = XMLSingle.getBuff(param1[_loc9_].@id,param4);
            _loc6_.player = param3;
            switch(_loc6_.type)
            {
               case "allTimeBuff":
                  (_loc6_ as AllTimeBuffVO).totalTime = uint(param1[_loc9_].@time);
                  (_loc6_ as AllTimeBuffVO).startDate = String(param1[_loc9_].@date);
                  _loc6_.remainTime = ((_loc6_ as AllTimeBuffVO).totalTime - new TimeUtil().timeInterval((_loc6_ as AllTimeBuffVO).startDate,param5)) * 3600;
                  if(_loc6_.remainTime > 0)
                  {
                     _loc7_ = new BuffDrive(_loc6_);
                     _loc7_.initBuffDrive();
                     param2.push(_loc7_);
                  }
                  break;
               case "onlyOnLineBuff":
                  throw new Error("还没准备好！");
               case "noTimeBuff":
                  _loc6_.remainTime = -1;
                  _loc7_ = new BuffDrive(_loc6_);
                  _loc7_.initBuffDrive();
                  param2.push(_loc7_);
                  break;
               default:
                  throw new Error("类型错误！");
            }
         }
      }
      
      public function initProtect(param1:XML, param2:XML) : void
      {
         var _loc3_:BuffVO = null;
         if(param1.hasOwnProperty("Protect"))
         {
            ProtectData.getInstance().getGiftDate = String(param1.Protect[0].@gD);
         }
         else
         {
            ProtectData.getInstance().getGiftDate = "2013-01-01 00:00:00";
         }
         var _loc5_:int = 0;
         var _loc4_:int = int(BuffData.getInstance().buffDrives.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = BuffData.getInstance().buffDrives[_loc5_].buffVO;
            if(_loc3_.className == "Buff_ProtectPrivileges")
            {
               ProtectData.getInstance().privilegeVOs = XMLSingle.getPrivilegeVOs(String(param2.item[0].@privileges),XMLSingle.getInstance().privilegeXML);
            }
            _loc5_++;
         }
      }
      
      public function initSign(param1:XML, param2:String) : void
      {
         var _loc3_:XMLList = XMLSingle.getInstance().signXML.Day;
         SignData.getInstance().maxSignNum = int(_loc3_[_loc3_.length() - 1].@num);
         if(!param1.hasOwnProperty("Sign"))
         {
            SignData.getInstance().currentSignDate = "";
            SignData.getInstance().currentSignNum = 0;
            SignData.getInstance().getSignAwardDate = "";
            return;
         }
         SignData.getInstance().currentSignNum = int(param1.Sign[0].@num);
         SignData.getInstance().currentSignDate = String(param1.Sign[0].@signD);
         SignData.getInstance().getSignAwardDate = String(param1.Sign[0].@gAD);
         if(SignData.getInstance().currentSignNum == 0)
         {
            SignData.getInstance().currentSignDate = "";
         }
      }
      
      public function initLand(param1:XML, param2:XML) : LandVO
      {
         var _loc3_:int = int(param1.@id);
         var _loc4_:LandVO = XMLSingle.getInstance().getLandVO(_loc3_,param2);
         _loc4_.state = int(param1.@state);
         switch(_loc4_.state)
         {
            case 0:
               _loc4_.equipmentIDInLand = 0;
               _loc4_.date = "";
               break;
            case 1:
               _loc4_.equipmentIDInLand = int(param1.@eqID);
               _loc4_.date = String(param1.@date);
               break;
            case 2:
               _loc4_.equipmentIDInLand = int(param1.@eqID);
               _loc4_.date = "";
               break;
            case 3:
               _loc4_.equipmentIDInLand = int(param1.@eqID);
               _loc4_.date = "";
               break;
            case 4:
               _loc4_.equipmentIDInLand = 0;
               _loc4_.equipmentIDInLand = int(param1.@eqID);
               _loc4_.date = String(param1.@date);
               break;
            default:
               throw new Error("土地状态类型错误！");
         }
         return _loc4_;
      }
      
      public function initMirageData(param1:XML) : void
      {
         MirageData.getInstance().currentMirageNum = 0;
         if(param1.hasOwnProperty("Mirage"))
         {
            MirageData.getInstance().mirageDate = param1.Mirage[0].@mirageDate;
            MirageData.getInstance().currentMirageNum = int(param1.Mirage[0].@mirageNum);
         }
         else
         {
            MirageData.getInstance().mirageDate = "";
         }
      }
      
      public function initPKData(param1:XML, param2:XML, param3:String) : void
      {
         var _loc7_:String = null;
         var _loc8_:Number = NaN;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc9_:Date = null;
         var _loc10_:TimeUtil = new TimeUtil();
         var _loc6_:Date = _loc10_.stringToDate(param3);
         if(_loc6_.month % 2 != 0)
         {
            GameData.MonthCicly = true;
         }
         if(param1.hasOwnProperty("PK"))
         {
            PlayerDataForPK.getInstance().allMatch = int(param1.PK[0].@allMatch);
            PlayerDataForPK.getInstance().winMatch = int(param1.PK[0].@winMatch);
            PlayerDataForPK.getInstance().winMonthMatch = int(param1.PK[0].@winMonth);
            PlayerDataForPK.getInstance().isSignUpForSingle = Boolean(int(param1.PK[0].@isSignUp));
            PlayerDataForPK.getInstance().monthResetTime = String(param1.PK[0].@monthTime);
            PlayerDataForPK.getInstance().failMatch = int(param1.PK[0].@failMatch);
            PlayerDataForPK.getInstance().pkPoint = int(param1.PK[0].@pkPoint);
            PlayerDataForPK.getInstance().isReseted = int(param1.PK[0].@isReseted);
            PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer = int(param1.PK[0].@mingrenTwoPlayer);
            _loc7_ = String(param2.PKData.@compareDateForTwoPlayerPK);
            _loc8_ = Number(param2.PKData.@resetTime);
            PlayerDataForPK.getInstance().matchDateForTwoPlayer = String(param1.PK[0].@matchD2);
            _loc5_ = _loc10_.timeInterval(_loc7_,param3) / _loc8_;
            _loc4_ = _loc10_.timeInterval(_loc7_,PlayerDataForPK.getInstance().matchDateForTwoPlayer) / _loc8_;
            _loc9_ = _loc10_.stringToDate(PlayerDataForPK.getInstance().monthResetTime);
            if(_loc9_.month != _loc6_.month)
            {
               PlayerDataForPK.getInstance().winMonthMatch = 1;
               PlayerDataForPK.getInstance().monthResetTime = param3;
               PlayerDataForPK.getInstance().isSignUpForSingle = false;
            }
            else if(_loc9_.fullYear != _loc6_.fullYear)
            {
               PlayerDataForPK.getInstance().isSignUpForSingle = false;
               PlayerDataForPK.getInstance().winMonthMatch = 1;
               PlayerDataForPK.getInstance().monthResetTime = param3;
            }
            if(_loc5_ != _loc4_ || MyFunction.getInstance().splitTimeString(PlayerDataForPK.getInstance().matchDateForTwoPlayer) == "2013-03-25" && !PlayerDataForPK.getInstance().isReseted)
            {
               PlayerDataForPK.getInstance().allMatchForTwoPlayer = 1;
               PlayerDataForPK.getInstance().winMatchForTwoPlayer = 1;
               PlayerDataForPK.getInstance().failMatchForTwoPlayer = 0;
               PlayerDataForPK.getInstance().matchDateForTwoPlayer = param3;
               PlayerDataForPK.getInstance().isReseted = 1;
               PlayerDataForPK.getInstance().isSignUpForDouble = false;
            }
            else
            {
               PlayerDataForPK.getInstance().allMatchForTwoPlayer = int(param1.PK[0].@allMatch2);
               PlayerDataForPK.getInstance().winMatchForTwoPlayer = int(param1.PK[0].@winMatch2);
               PlayerDataForPK.getInstance().failMatchForTwoPlayer = int(param1.PK[0].@failMatch2);
               PlayerDataForPK.getInstance().matchDateForTwoPlayer = param3;
               PlayerDataForPK.getInstance().isSignUpForDouble = Boolean(int(param1.PK[0].@isSignUp2));
            }
         }
         else
         {
            PlayerDataForPK.getInstance().allMatch = 1;
            PlayerDataForPK.getInstance().winMatch = 1;
            PlayerDataForPK.getInstance().failMatch = 0;
            PlayerDataForPK.getInstance().allMatchForTwoPlayer = 1;
            PlayerDataForPK.getInstance().winMatchForTwoPlayer = 1;
            PlayerDataForPK.getInstance().failMatchForTwoPlayer = 0;
            PlayerDataForPK.getInstance().matchDateForTwoPlayer = param3;
            PlayerDataForPK.getInstance().pkPoint = 0;
            PlayerDataForPK.getInstance().isSignUpForDouble = false;
            PlayerDataForPK.getInstance().isSignUpForSingle = false;
         }
      }
      
      public function initExchangeGiftData(param1:XML) : void
      {
         if(!param1.hasOwnProperty("EGD"))
         {
            return;
         }
         ExchangeGiftData.getInstance().exchangeGiftNames = MyFunction.getInstance().excreteStringToString(String(param1.EGD.@gGN));
      }
      
      public function initNicknameData(param1:XML) : void
      {
         if(NicknameData.getInstance().myDataInNicknameRankList == null)
         {
            NicknameData.getInstance().myDataInNicknameRankList = {};
         }
         NicknameData.getInstance().myDataInNicknameRankList.extra = GameData.getInstance().getLoginReturnData().getNickname();
         NicknameData.getInstance().myDataInNicknameRankList.nicknameType = "whiteNickname";
         if(!param1.hasOwnProperty("NNameData"))
         {
            return;
         }
         NicknameData.getInstance().myNicknameRankListId = uint(param1.NNameData.@rId);
      }
      
      public function initNicknameErrorData(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc2_:NicknameSaveData = null;
         if(!param1.hasOwnProperty("NNameError"))
         {
            return;
         }
         var _loc4_:XMLList = param1.NNameError[0].NNameData;
         var _loc3_:int = int(_loc4_.length());
         if(!NicknameData.getInstance().nicknameSaveData)
         {
            NicknameData.getInstance().nicknameSaveData = new Vector.<NicknameSaveData>();
         }
         _loc5_ = Math.max(_loc3_ - 1,0);
         while(_loc5_ < _loc3_)
         {
            if(int(_loc4_[_loc5_].@rankId) != 0)
            {
               _loc2_ = new NicknameSaveData();
               _loc2_.currentRankId = int(_loc4_[_loc5_].@rankId);
               _loc2_.currentChangeNicknameNum = int(_loc4_[_loc5_].@cN);
               _loc2_.errorString = String(_loc4_[_loc5_].@error);
               _loc2_.currentNickname = String(_loc4_[_loc5_].@cNN);
               NicknameData.getInstance().nicknameSaveData.push(_loc2_);
            }
            _loc5_++;
         }
      }
      
      public function initOther(param1:XML) : void
      {
         var _loc3_:XMLList = null;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         if(!param1.hasOwnProperty("Other"))
         {
            return;
         }
         UnAbleBuyEquipmentData.getInstance().getIdsFromStr(param1.Other[0].@unAbleBuyEqs);
         if(XML(param1.Other[0]).hasOwnProperty("cheatData"))
         {
            if(XML(param1.Other[0].cheatData[0]).hasOwnProperty("changeNumData"))
            {
               CheatData.getInstance().addChangeNumData(param1.Other[0].cheatData[0].changeNumData[0].@changeNum,param1.Other[0].cheatData[0].changeNumData[0].@originalNum);
            }
            _loc3_ = param1.Other[0].cheatData[0].cheatDataStr;
            _loc2_ = int(!!_loc3_ ? _loc3_.length() : 0);
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               CheatData.getInstance().addCheatDataStr(_loc3_[_loc4_].@str);
               _loc4_++;
            }
         }
      }
      
      public function getEquipmentVOs(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : Vector.<EquipmentVO>
      {
         var _loc6_:EquipmentVO = null;
         var _loc8_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc10_:XMLList = param1.children();
         for each(var _loc9_ in _loc10_)
         {
            _loc6_ = getEquipmentVO(_loc9_,param2,param3,param4,param5);
            if(_loc6_ && _loc6_.isOverdue && _loc6_.isOverdue == 1)
            {
               _loc8_.push(null);
            }
            else
            {
               _loc8_.push(_loc6_);
            }
         }
         MyFunction.getInstance().dealWithDataEquipmentVOFromEquipmentVOs(_loc8_,param5);
         return _loc8_;
      }
      
      public function getSkillVOs(param1:XML, param2:XML) : Vector.<SkillVO>
      {
         var _loc7_:SkillVO = null;
         var _loc3_:int = 0;
         var _loc4_:Vector.<SkillVO> = new Vector.<SkillVO>();
         var _loc8_:XMLList = param1.children();
         for each(var _loc6_ in _loc8_)
         {
            _loc3_ = int(_loc6_.@id);
            if(_loc3_)
            {
               _loc7_ = XMLSingle.getSkill(_loc3_,param2);
               if(_loc7_ is IActiveSkillVO)
               {
                  (_loc7_ as IActiveSkillVO).currentCDTime = int(_loc6_.@currentCDTime);
               }
               _loc4_.push(_loc7_);
            }
            else
            {
               _loc4_.push(null);
            }
         }
         return _loc4_;
      }
      
      private function getPet(param1:XML, param2:XML, param3:XML, param4:XML) : Pet
      {
         var _loc6_:* = null;
         var _loc5_:PetEquipmentVO = null;
         if(!param1)
         {
            return new Pet(null);
         }
         var _loc8_:XMLList = param1.children();
         var _loc10_:int = 0;
         var _loc9_:* = _loc8_;
         for each(var _loc7_ in _loc9_)
         {
            _loc5_ = getEquipmentVO(_loc7_,param2,param3,param4,null) as PetEquipmentVO;
            return new Pet(_loc5_);
         }
         return new Pet(null);
      }
      
      public function getBaseEquipmentVO(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : EquipmentVO
      {
         var _loc7_:EquipmentVO = null;
         var _loc6_:int = int(param1.@id);
         if(_loc6_)
         {
            _loc7_ = XMLSingle.getBaseEquipment(_loc6_,param2,param5);
            _loc7_.isBinding = Boolean(int(param1.@isB));
            _loc7_.isShopItem = true;
            return _loc7_;
         }
         return null;
      }
      
      public function getShopItemVO(param1:EquipmentVO, param2:XML, param3:XML, param4:XML, param5:String) : EquipmentVO
      {
         var _loc6_:XML = <item />;
         _loc6_.@id = param1.id;
         return getEquipmentVO(_loc6_,param2,param3,param4,param5);
      }
      
      private function getEquipmentVO(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : EquipmentVO
      {
         var _loc27_:int = 0;
         var _loc26_:int = 0;
         var _loc17_:int = 0;
         var _loc20_:EquipmentVO = null;
         var _loc24_:PreciousEquipmentVO = null;
         var _loc28_:WeaponEquipmentVO = null;
         var _loc18_:GourdEquipmentVO = null;
         var _loc10_:NecklaceEquipmentVO = null;
         var _loc31_:ClothesEquipmentVO = null;
         var _loc9_:ForeverFashionEquipmentVO = null;
         var _loc7_:int = 0;
         var _loc13_:XMLList = null;
         var _loc29_:XMLList = null;
         var _loc30_:int = 0;
         var _loc21_:* = undefined;
         var _loc25_:int = 0;
         var _loc16_:XMLList = null;
         var _loc19_:InsetGemEquipmentVO = null;
         var _loc6_:* = undefined;
         var _loc11_:PetActiveSkillVO = null;
         var _loc14_:XMLList = null;
         var _loc23_:int = 0;
         var _loc15_:int = 0;
         var _loc8_:String = null;
         var _loc22_:XMLList = null;
         var _loc12_:int = int(param1.@id);
         if(_loc12_)
         {
            _loc20_ = XMLSingle.getEquipment(_loc12_,param2,param5);
            _loc20_.isBinding = Boolean(int(param1.@isB));
            if(param1.@pf)
            {
               _loc20_.prefix = MyBase64.decode(param1.@pf);
            }
            if(param1.@sf)
            {
               _loc20_.suffix = MyBase64.decode(param1.@sf);
            }
            DetectionClass.getInstance().addEquipmentVOFix(_loc20_);
            switch(_loc20_.equipmentType)
            {
               case "undeterminded":
                  break;
               case "precious":
               case "weapon":
               case "gourd":
               case "necklace":
               case "clothes":
               case "forever_fashion":
                  switch(_loc20_.equipmentType)
                  {
                     case "precious":
                        _loc24_ = _loc20_ as PreciousEquipmentVO;
                        if(param1.hasOwnProperty("materialid"))
                        {
                           _loc24_.materialid = int(param1.materialid[0].@value);
                        }
                        _loc22_ = param1.eqmagic;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc24_.addPlayerSaveAttr.push(String(_loc22_[_loc26_].@addPlayerSaveAttr));
                           _loc24_.addPlayerSaveAttrVals.push(Number(_loc22_[_loc26_].@addPlayerSaveAttrVals));
                           _loc26_++;
                        }
                        _loc24_.basisAttr.length = 0;
                        _loc24_.basisAttrValue.length = 0;
                        _loc24_.basisUpValue.length = 0;
                        _loc24_.basisMaxUp.length = 0;
                        _loc24_.basisMinUp.length = 0;
                        _loc24_.basisWeight.length = 0;
                        _loc24_.basisTotalWeight.length = 0;
                        _loc22_ = param1.addAttr;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc24_.basisAttr.push(String(_loc22_[_loc26_].@addAttName));
                           _loc24_.basisAttrValue.push(Number(_loc22_[_loc26_].@addAttValue));
                           _loc24_.basisUpValue.push(Number(_loc22_[_loc26_].@basisUpValue));
                           _loc24_.basisMaxUp.push(Number(_loc22_[_loc26_].@maxupgrade));
                           _loc24_.basisMinUp.push(Number(_loc22_[_loc26_].@minupgrade));
                           _loc24_.basisWeight.push(Number(_loc22_[_loc26_].@weight));
                           _loc24_.basisTotalWeight.push(Number(_loc22_[_loc26_].@totalweight));
                           if("addshanbi" == String(_loc22_[_loc26_].@addAttName))
                           {
                              if(Number(_loc22_[_loc26_].@addAttValue) + Number(_loc22_[_loc26_].@basisUpValue) > 10)
                              {
                                 _loc24_.basisUpValue[_loc26_] = 10 - Number(_loc22_[_loc26_].@addAttValue);
                              }
                           }
                           _loc26_++;
                        }
                        _loc24_.sAttrName.length = 0;
                        _loc24_.sAttrValue.length = 0;
                        _loc24_.sAvgValue.length = 0;
                        _loc24_.sMaxValue.length = 0;
                        _loc24_.sMinValue.length = 0;
                        _loc24_.sWeight.length = 0;
                        _loc24_.sTotalWeight.length = 0;
                        _loc22_ = param1.sAddAttr;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc24_.sAttrName.push(String(_loc22_[_loc26_].@addAttName));
                           _loc24_.sAttrValue.push(Number(_loc22_[_loc26_].@addAttValue));
                           _loc24_.sAvgValue.push(Number(_loc22_[_loc26_].@avgValue));
                           _loc24_.sMaxValue.push(Number(_loc22_[_loc26_].@maxvalue));
                           _loc24_.sMinValue.push(Number(_loc22_[_loc26_].@minvalue));
                           _loc24_.sWeight.push(Number(_loc22_[_loc26_].@weight));
                           _loc24_.sTotalWeight.push(Number(_loc22_[_loc26_].@totalweight));
                           _loc26_++;
                        }
                        break;
                     case "weapon":
                        (_loc20_ as WeaponEquipmentVO).attack = int(param1.@attack);
                        (_loc20_ as WeaponEquipmentVO).rengPin = int(param1.@renPin);
                        _loc28_ = _loc20_ as WeaponEquipmentVO;
                        _loc22_ = param1.eqmagic;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc28_.addPlayerSaveAttr.push(String(_loc22_[_loc26_].@addPlayerSaveAttr));
                           _loc28_.addPlayerSaveAttrVals.push(Number(_loc22_[_loc26_].@addPlayerSaveAttrVals));
                           _loc26_++;
                        }
                        break;
                     case "gourd":
                        (_loc20_ as GourdEquipmentVO).maxMagic = int(param1.@maxMagic);
                        (_loc20_ as GourdEquipmentVO).rengPin = int(param1.@renPin);
                        _loc18_ = _loc20_ as GourdEquipmentVO;
                        _loc22_ = param1.eqmagic;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc18_.addPlayerSaveAttr.push(String(_loc22_[_loc26_].@addPlayerSaveAttr));
                           _loc18_.addPlayerSaveAttrVals.push(Number(_loc22_[_loc26_].@addPlayerSaveAttrVals));
                           _loc26_++;
                        }
                        break;
                     case "necklace":
                        (_loc20_ as NecklaceEquipmentVO).criticalRate = int(param1.@criticalRate);
                        (_loc20_ as NecklaceEquipmentVO).rengPin = int(param1.@renPin);
                        _loc10_ = _loc20_ as NecklaceEquipmentVO;
                        _loc22_ = param1.eqmagic;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc10_.addPlayerSaveAttr.push(String(_loc22_[_loc26_].@addPlayerSaveAttr));
                           _loc10_.addPlayerSaveAttrVals.push(Number(_loc22_[_loc26_].@addPlayerSaveAttrVals));
                           _loc26_++;
                        }
                        break;
                     case "clothes":
                        (_loc20_ as ClothesEquipmentVO).defence = int(param1.@defence);
                        (_loc20_ as ClothesEquipmentVO).riot = Number(param1.@riot);
                        (_loc20_ as ClothesEquipmentVO).rengPin = int(param1.@renPin);
                        _loc31_ = _loc20_ as ClothesEquipmentVO;
                        _loc22_ = param1.eqmagic;
                        _loc17_ = int(!!_loc22_ ? _loc22_.length() : 0);
                        _loc26_ = 0;
                        while(_loc26_ < _loc17_)
                        {
                           _loc31_.addPlayerSaveAttr.push(String(_loc22_[_loc26_].@addPlayerSaveAttr));
                           _loc31_.addPlayerSaveAttrVals.push(Number(_loc22_[_loc26_].@addPlayerSaveAttrVals));
                           _loc26_++;
                        }
                        break;
                     case "forever_fashion":
                        _loc9_ = _loc20_ as ForeverFashionEquipmentVO;
                        _loc7_ = int(param1.@addExtraAttr);
                        _loc9_.addExtraAttrValue = Number(param1.@addExtraAttrValue);
                        _loc9_.addExtraAttrEx = String(param1.@addExtraAttrEx);
                        if(_loc7_ > 0)
                        {
                           _loc13_ = XMLSingle.getInstance().dataXML.ForeverFashionAddAttr;
                           _loc29_ = _loc13_.value;
                           _loc30_ = int(_loc29_.length());
                           _loc21_ = new Vector.<String>();
                           _loc25_ = 0;
                           while(_loc25_ < _loc30_)
                           {
                              _loc21_.push(String(_loc29_[_loc25_].@addPlayerAttribute));
                              _loc25_++;
                           }
                           if(_loc7_ <= _loc30_)
                           {
                              _loc9_.addExtraAttrEx = _loc21_[_loc7_ - 1];
                              _loc9_.addPlayerAttributes.push(_loc9_.addExtraAttrEx);
                              _loc9_.addPlayerAttributeValues.push(_loc9_.addExtraAttrValue);
                           }
                           _loc7_ = 0;
                           break;
                        }
                        if(_loc9_.addExtraAttrEx != "" && _loc9_.addExtraAttrEx != null)
                        {
                           _loc9_.addPlayerAttributes.push(_loc9_.addExtraAttrEx);
                           _loc9_.addPlayerAttributeValues.push(_loc9_.addExtraAttrValue);
                        }
                        break;
                  }
                  _loc16_ = param1.hole;
                  _loc17_ = int(!!_loc16_ ? _loc16_.length() : 0);
                  _loc6_ = new Vector.<InsetGemEquipmentVO>();
                  _loc26_ = 0;
                  while(_loc26_ < _loc17_)
                  {
                     _loc19_ = getEquipmentVO(_loc16_[_loc26_],param2,param3,param4,param5) as InsetGemEquipmentVO;
                     _loc6_.push(_loc19_);
                     _loc26_++;
                  }
                  (_loc20_ as AbleEquipmentVO).addInsetGems(_loc6_);
                  break;
               case "pet":
                  (_loc20_ as PetEquipmentVO).petLevel = int(param1.@petLevel);
                  if((_loc20_ as PetEquipmentVO).petLevel == 0)
                  {
                     (_loc20_ as PetEquipmentVO).petLevel = 1;
                  }
                  if(param1.hasOwnProperty("activeSkill"))
                  {
                     (_loc20_ as PetEquipmentVO).activeSkillVO = XMLSingle.getSkill(int(param1.activeSkill.@id),param3);
                     ((_loc20_ as PetEquipmentVO).activeSkillVO as IActiveSkillVO).currentCDTime = int(param1.activeSkill.@currentCDTime);
                  }
                  _loc11_ = (_loc20_ as PetEquipmentVO).activeSkillVO as PetActiveSkillVO;
                  _loc11_.originalHurt = (_loc20_ as PetEquipmentVO).petLevel * _loc11_.hurCoefficient + _loc11_.additionHurt;
                  _loc11_.originalPkHurt = (_loc20_ as PetEquipmentVO).petLevel * _loc11_.pkHurtCoefficient + _loc11_.additionPkHurt;
                  if(param1.passiveSkill.length())
                  {
                     (_loc20_ as PetEquipmentVO).passiveSkillVOs = getSkillVOsByXMLList(param1.passiveSkill,param3);
                  }
                  else if(<EMAIL>())
                  {
                     (_loc20_ as PetEquipmentVO).passiveSkillVOs = getSkillVOsIDs(MyFunction.getInstance().excreteString(String(param1.@passiveSkills)),param3);
                  }
                  if(param1.hasOwnProperty("awakeSkill"))
                  {
                     ClearUtil.nullArr((_loc20_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs);
                     (_loc20_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs = new Vector.<SkillVO>();
                     _loc14_ = param1.awakeSkill;
                     _loc23_ = 0;
                     _loc15_ = int(!!_loc14_ ? _loc14_.length() : 0);
                     _loc23_ = 0;
                     while(_loc23_ < _loc15_)
                     {
                        _loc8_ = String(_loc14_[_loc23_].@id);
                        (_loc20_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs.push(XMLSingle.getSkill(_loc8_,param3));
                        _loc23_++;
                     }
                  }
                  if(<EMAIL>())
                  {
                     (_loc20_ as PetEquipmentVO).talentVO = XMLSingle.getTalentVO(int(param1.@talent),param4);
                  }
                  XMLSingle.getInstance().setPetData(param1.@id,(_loc20_ as PetEquipmentVO).petLevel,_loc20_ as PetEquipmentVO);
                  if(<EMAIL>())
                  {
                     (_loc20_ as PetEquipmentVO).essentialPercent = Number(param1.@essentialPercent);
                  }
                  else
                  {
                     (_loc20_ as PetEquipmentVO).essentialPercent = 1;
                  }
                  MyFunction.getInstance().refreshPet(_loc20_ as PetEquipmentVO);
                  (_loc20_ as PetEquipmentVO).experiencePercent = Number(param1.@experiencePercent);
                  break;
               case "material":
               case "potion":
               case "pocket":
               case "grass":
               case "buffEquipment":
               case "forceDan":
               case "insetGem":
                  _loc27_ = int(param1.@num);
                  if(_loc27_ > 1)
                  {
                     (_loc20_ as StackEquipmentVO).num = _loc27_;
                     break;
                  }
                  (_loc20_ as StackEquipmentVO).num = 1;
                  break;
               case "scroll":
               case "egg":
               case "danMedicine":
               case "petSkillBook":
               case "contract":
                  break;
               case "fashion":
               case "medal":
                  if(<EMAIL>())
                  {
                     (_loc20_ as ILimitEquipmentVO).initTime = String(param1.@initTime);
                     if((_loc20_ as ILimitEquipmentVO).initTime == "null" || (_loc20_ as ILimitEquipmentVO).initTime == "undefined")
                     {
                        (_loc20_ as ILimitEquipmentVO).initTime = param5;
                     }
                     break;
                  }
                  trace("该时装不存在初始化日期，将时装初始化日期设为现在");
                  (_loc20_ as ILimitEquipmentVO).initTime = param5;
                  break;
               default:
                  throw new Error();
            }
            return _loc20_;
         }
         return null;
      }
      
      private function getSkillVOsIDs(param1:Vector.<int>, param2:XML) : Vector.<SkillVO>
      {
         var _loc5_:SkillVO = null;
         var _loc4_:Vector.<SkillVO> = new Vector.<SkillVO>();
         for each(var _loc3_ in param1)
         {
            if(_loc3_)
            {
               _loc5_ = XMLSingle.getSkill(_loc3_,param2);
               _loc4_.push(_loc5_);
            }
         }
         return _loc4_;
      }
      
      private function getSkillVOsByXMLList(param1:XMLList, param2:XML) : Vector.<SkillVO>
      {
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:SkillVO = null;
         var _loc5_:int = int(param1.length());
         var _loc4_:Vector.<SkillVO> = new Vector.<SkillVO>();
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc3_ = int(param1[_loc7_].@id);
            if(_loc3_)
            {
               _loc6_ = XMLSingle.getSkill(_loc3_,param2);
               (_loc6_ as PetPassiveSkillVO).promoteValue = int(param1[_loc7_].@promoteValue);
               (_loc6_ as PetPassiveSkillVO).value = (_loc6_ as PetPassiveSkillVO).originalValue + (_loc6_ as PetPassiveSkillVO).promoteValue;
               _loc4_.push(_loc6_);
            }
            _loc7_++;
         }
         return _loc4_;
      }
   }
}

