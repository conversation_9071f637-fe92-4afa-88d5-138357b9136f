package UI.PackageAndStorage
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PKPointShow extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_showBtn:ButtonLogicShell2;
      
      private var m_PKText:TextField;
      
      public function PKPointShow()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_show = null;
         ClearUtil.clearObject(m_showBtn);
         m_showBtn = null;
         m_PKText = null;
      }
      
      private function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("PKPointShow") as MovieClip;
         addChild(m_show);
         m_showBtn = new ButtonLogicShell2();
         m_showBtn.setShow(m_show);
         m_PKText = m_show["PKText"];
      }
      
      public function getPKText() : TextField
      {
         return m_PKText;
      }
      
      public function getShowBtn() : ButtonLogicShell2
      {
         return m_showBtn;
      }
   }
}

