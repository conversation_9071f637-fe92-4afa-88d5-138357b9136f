package UI.RefineFactory
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Farm.FarmEquipmentCell.FarmEquipmentCell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class RefineFactoryColume extends SwitchBtn
   {
      private var _nameText:TextField;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _equipmentCell:FarmEquipmentCell;
      
      public function RefineFactoryColume()
      {
         super();
         initColume();
      }
      
      public function set equipmentVO(param1:EquipmentVO) : void
      {
         while(numChildren > 3)
         {
            if(getChildAt(3).hasOwnProperty("clear"))
            {
               getChildAt(2)["clear"]();
            }
            removeChildAt(3);
         }
         _equipmentVO = param1;
         _equipmentCell.addEquipmentVO(param1);
         _nameText.text = param1.name;
         _nameText.height = _nameText.textHeight + 5;
         _nameText.y = (60 - _nameText.height) / 2;
      }
      
      private function initColume() : void
      {
         _nameText = new TextField();
         _nameText.x = 117;
         _nameText.y = 15;
         _nameText.width = 200;
         _nameText.height = 55;
         addChild(_nameText);
         _nameText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,25,16762997);
         _nameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _nameText.embedFonts = true;
         _nameText.selectable = false;
         _nameText.wordWrap = true;
         _nameText.mouseEnabled = false;
         _equipmentCell = new FarmEquipmentCell();
         _equipmentCell.x = 67;
         _equipmentCell.y = 31;
         addChild(_equipmentCell);
      }
      
      override public function clear() : void
      {
         super.clear();
         _equipmentVO = null;
         if(_equipmentCell)
         {
            _equipmentCell.clear();
         }
         _equipmentCell = null;
         _nameText = null;
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchRefineFactoryColume",_equipmentVO));
      }
   }
}

