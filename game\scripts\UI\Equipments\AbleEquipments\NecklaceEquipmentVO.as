package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class NecklaceEquipmentVO extends AbleEquipmentVO
   {
      private var _criticalRate:int;
      
      private var _minCriticalRate:int;
      
      private var _maxCriticalRate:int;
      
      public var addPlayerSaveAttr:Vector.<String> = new Vector.<String>();
      
      public var addPlayerSaveAttrVals:Vector.<Number> = new Vector.<Number>();
      
      public function NecklaceEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.criticalRate = _criticalRate;
         _antiwear.maxCriticalRate = _maxCriticalRate;
         _antiwear.minCriticalRate = _minCriticalRate;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:NecklaceEquipmentVO = new NecklaceEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as NecklaceEquipmentVO).criticalRate = this.criticalRate;
         (param1 as NecklaceEquipmentVO).minCriticalRate = this.minCriticalRate;
         (param1 as NecklaceEquipmentVO).maxCriticalRate = this.maxCriticalRate;
         _loc2_ = int(addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as NecklaceEquipmentVO).addPlayerSaveAttr.push(addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as NecklaceEquipmentVO).addPlayerSaveAttrVals.push(addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
      }
      
      public function get criticalRate() : int
      {
         return _antiwear.criticalRate;
      }
      
      public function set criticalRate(param1:int) : void
      {
         _antiwear.criticalRate = param1;
      }
      
      public function get maxCriticalRate() : int
      {
         return _antiwear.maxCriticalRate;
      }
      
      public function set maxCriticalRate(param1:int) : void
      {
         _antiwear.maxCriticalRate = param1;
      }
      
      public function get minCriticalRate() : int
      {
         return _antiwear.minCriticalRate;
      }
      
      public function set minCriticalRate(param1:int) : void
      {
         _antiwear.minCriticalRate = param1;
      }
   }
}

