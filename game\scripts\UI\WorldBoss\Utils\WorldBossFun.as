package UI.WorldBoss.Utils
{
   import UI.WorldBoss.BossHurtData;
   import UI.WorldBoss.WorldBossSaveData;
   
   public class WorldBossFun
   {
      public function WorldBossFun()
      {
         super();
      }
      
      public function getTotalBossHurt() : Number
      {
         var _loc1_:BossHurtData = null;
         var _loc4_:int = 0;
         var _loc3_:int = WorldBossSaveData.getInstance().getBossHurtDataNum();
         var _loc2_:Number = 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc1_ = WorldBossSaveData.getInstance().getBossHurtDataByIndex(_loc4_);
            _loc2_ += !!_loc1_ ? _loc1_.getHurt() : 0;
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function getTotalBossHurtNum() : int
      {
         var _loc1_:BossHurtData = null;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = WorldBossSaveData.getInstance().getBossHurtDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc1_ = WorldBossSaveData.getInstance().getBossHurtDataByIndex(_loc4_);
            _loc2_ += !!_loc1_ ? _loc1_.getHurtNum() : 0;
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function getTotalBossFightNum() : int
      {
         var _loc2_:BossHurtData = null;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = WorldBossSaveData.getInstance().getBossHurtDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = WorldBossSaveData.getInstance().getBossHurtDataByIndex(_loc4_);
            _loc1_ += !!_loc2_ ? _loc2_.getFigthNum() : 0;
            _loc4_++;
         }
         return _loc1_;
      }
   }
}

