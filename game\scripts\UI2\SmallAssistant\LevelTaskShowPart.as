package UI2.SmallAssistant
{
   import UI.GamingUI;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SmallAssistant.LevelTask.LevelTaskRewardData;
   import UI2.SmallAssistant.LevelTask.LevelTaskRewardSaveData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class LevelTaskShowPart
   {
      private var m_levelTaskLines:Vector.<LevelTaskLine>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_show:MovieClip;
      
      private var m_smallAssistantData:SmallAssistantData;
      
      private var m_smallAssistantPanel:SmallAssistantPanel;
      
      public function LevelTaskShowPart()
      {
         super();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_levelTaskLines);
         m_levelTaskLines = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         m_show = null;
         m_smallAssistantData = null;
         m_smallAssistantPanel = null;
      }
      
      public function init(param1:MovieClip, param2:SmallAssistantData, param3:SmallAssistantPanel) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_smallAssistantData = param2;
         m_smallAssistantPanel = param3;
         initShow();
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:LevelTaskRewardSaveData = null;
         var _loc5_:* = param1.button;
         if(m_pageBtnGroup === _loc5_)
         {
            arrangeLevelTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_levelTaskLines.length);
         }
         _loc2_ = !!m_levelTaskLines ? m_levelTaskLines.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(param1.button == m_levelTaskLines[_loc4_].getGetRewardBtn())
            {
               if(m_getEquipmentVOsLogic.getEquipmentVOs(m_levelTaskLines[_loc4_].getLevelTaskRewardData(),GamingUI.getInstance().player1))
               {
                  _loc3_ = new LevelTaskRewardSaveData();
                  _loc3_.setRewardId(m_levelTaskLines[_loc4_].getLevelTaskRewardData().getId());
                  m_smallAssistantData.getLevelTasksSaveData().addGotRewardData(_loc3_);
                  refreshLevelTaskRewardShowState();
                  m_smallAssistantPanel.refreshNumTipShow();
                  GamingUI.getInstance().showMessageTip("领取成功");
               }
               else
               {
                  GamingUI.getInstance().showMessageTip("背包已满");
               }
               return;
            }
            _loc4_++;
         }
      }
      
      private function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:LevelTaskLine = null;
         _loc4_ = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc1_ = m_show.getChildAt(_loc5_);
            if(_loc1_.name.substr(0,14) == "levelTaskLine_")
            {
               _loc3_++;
            }
            _loc5_++;
         }
         ClearUtil.clearObject(m_levelTaskLines);
         m_levelTaskLines = null;
         m_levelTaskLines = new Vector.<LevelTaskLine>();
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_ = new LevelTaskLine();
            _loc2_.setShow(m_show["levelTaskLine_" + (_loc5_ + 1)]);
            m_levelTaskLines.push(_loc2_);
            _loc5_++;
         }
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_smallAssistantData == null)
         {
            return;
         }
         setPageBtn(1);
         arrangeLevelTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_levelTaskLines.length);
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_smallAssistantData == null || m_smallAssistantData.getLevelTaskRewardDataNum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_smallAssistantData.getLevelTaskRewardDataNum() % m_levelTaskLines.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_smallAssistantData.getLevelTaskRewardDataNum() / m_levelTaskLines.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_smallAssistantData.getLevelTaskRewardDataNum() / m_levelTaskLines.length) + 1);
         }
      }
      
      private function arrangeLevelTaskLineShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc2_:LevelTaskRewardData = null;
         var _loc5_:int = param1 + m_levelTaskLines.length;
         var _loc3_:int = !!m_smallAssistantData ? m_smallAssistantData.getLevelTaskRewardDataNum() : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc3_)
         {
            _loc2_ = m_smallAssistantData.getLevelTaskRewardDataByIndex(_loc6_);
            m_levelTaskLines[_loc4_].setLevelTaskRewardData(_loc2_);
            m_levelTaskLines[_loc4_].getShow().visible = true;
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_levelTaskLines.length)
         {
            m_levelTaskLines[_loc4_].getShow().visible = false;
            _loc4_++;
         }
         refreshLevelTaskRewardShowState();
      }
      
      private function refreshLevelTaskRewardShowState() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:uint = Math.max(GamingUI.getInstance().player1.playerVO.level,Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO) ? GamingUI.getInstance().player2.playerVO.level : 0);
         _loc1_ = int(m_levelTaskLines.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(m_levelTaskLines[_loc3_].getLevelTaskRewardData())
            {
               if(m_smallAssistantData.getIsGotLevelTaskRewardById(m_levelTaskLines[_loc3_].getLevelTaskRewardData().getId()))
               {
                  m_levelTaskLines[_loc3_].tranToUnableGet();
               }
               else if(_loc2_ < m_levelTaskLines[_loc3_].getLevelTaskRewardData().getNeedMinLevel())
               {
                  m_levelTaskLines[_loc3_].tranToUnableGet();
               }
               else
               {
                  m_levelTaskLines[_loc3_].tranToAbleGet();
               }
            }
            _loc3_++;
         }
      }
   }
}

