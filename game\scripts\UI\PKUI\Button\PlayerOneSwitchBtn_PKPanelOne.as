package UI.PKUI.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class PlayerOneSwitchBtn_PKPanelOne extends SwitchBtn
   {
      public function PlayerOneSwitchBtn_PKPanelOne()
      {
         super();
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchPlayerInPKPanelOne"));
      }
   }
}

