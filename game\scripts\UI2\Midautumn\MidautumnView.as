package UI2.Midautumn
{
   import Json.MyJSON;
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.API_4399.PayAPI.DateInfor;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.text.TextField;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class MidautumnView extends MySprite
   {
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/bobing/query";
      
      private var m_getNumUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/bobing/rank";
      
      private var m_show:MovieClip;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_midautumnPanel:MidautumnPanel;
      
      private var m_getPanel:MovieClip;
      
      private var m_rulePanel:MovieClip;
      
      private var m_resultPanel:MovieClip;
      
      private var m_numPanel:MovieClip;
      
      private var m_rankPanel:MovieClip;
      
      private var m_wzwResult:MovieClip;
      
      private var m_wzwMainPanel:MovieClip;
      
      private var m_btnClose:ButtonLogicShell2;
      
      private var m_btnStart:ButtonLogicShell2;
      
      private var m_btnRule:ButtonLogicShell2;
      
      private var m_handLogic:HandLogic;
      
      private var m_vectNum:Vector.<DiceItem>;
      
      private var m_i:int = 0;
      
      private var m_bStartRun:Boolean = false;
      
      private var m_totalNum:int;
      
      private var m_txtFreeTime:TextField;
      
      private var m_txtNumZy:TextField;
      
      private var m_midautumnResult:MidautumnResult;
      
      private var m_ifbuyView:IfBuyView;
      
      private var m_calculate:CalculateLogic;
      
      private var m_rechargedMoney:int;
      
      private var m_useNum:int;
      
      private var m_bGetSixNum:Boolean = false;
      
      private var m_bBtnClick:Boolean = false;
      
      private var m_bRewardShow:Boolean = false;
      
      private var m_timeId:int;
      
      private var m_xlist:Vector.<Number>;
      
      private var m_ylist:Vector.<Number>;
      
      private var m_payAPIForReset:PayAPI;
      
      private var m_payAPIListenerForReset:PayAPIListener;
      
      private var m_dateInforOfGetTotalRechargeForReset:DateInfor;
      
      public function MidautumnView()
      {
         super();
         m_calculate = new CalculateLogic();
         m_xlist = new Vector.<Number>();
         m_ylist = new Vector.<Number>();
         m_xlist.push(393);
         m_xlist.push(465);
         m_xlist.push(400);
         m_xlist.push(448);
         m_xlist.push(468);
         m_xlist.push(523);
         m_ylist.push(312);
         m_ylist.push(302);
         m_ylist.push(378);
         m_ylist.push(400);
         m_ylist.push(356);
         m_ylist.push(357);
         m_vectNum = new Vector.<DiceItem>();
         m_payAPIListenerForReset = new PayAPIListener();
         m_payAPIListenerForReset.getRechargedMoneyErrorFun = getRechargedMoneyErrorForReset;
         m_payAPIListenerForReset.getRechargedMoneySuccessFun = getRechargedMoneySuccessForReset;
         m_payAPIForReset = GamingUI.getInstance().getAPI4399().payAPI;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         clearTimeout(m_timeId);
         m_timeId = 0;
         ClearUtil.clearObject(m_calculate);
         m_calculate = null;
         ClearUtil.clearObject(m_xlist);
         m_xlist = null;
         ClearUtil.clearObject(m_ylist);
         m_ylist = null;
         ClearUtil.clearObject(m_midautumnResult);
         m_midautumnResult = null;
         ClearUtil.clearObject(m_ifbuyView);
         m_ifbuyView = null;
         ClearUtil.clearObject(m_btnClose);
         m_btnClose = null;
         ClearUtil.clearObject(m_btnStart);
         m_btnStart = null;
         ClearUtil.clearObject(m_btnRule);
         m_btnRule = null;
         ClearUtil.clearObject(m_numPanel);
         m_numPanel = null;
         ClearUtil.clearObject(m_dateInforOfGetTotalRechargeForReset);
         m_dateInforOfGetTotalRechargeForReset = null;
         m_handLogic.clear();
         ClearUtil.clearObject(m_handLogic);
         m_handLogic = null;
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].clear();
            m_vectNum[_loc1_] = null;
            _loc1_++;
         }
         ClearUtil.clearObject(m_vectNum);
         m_vectNum = null;
         if(m_payAPIForReset)
         {
            m_payAPIForReset.removePayAPIListener(m_payAPIListenerForReset);
         }
         ClearUtil.clearObject(m_payAPIListenerForReset);
         m_payAPIListenerForReset = null;
         m_payAPIForReset = null;
         m_show.removeEventListener("clickButton",clickButton,true);
         m_show.removeEventListener("warningBox",sureOrCancel,true);
         super.clear();
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_handLogic)
         {
            m_handLogic.render(param1);
         }
         m_i = 0;
         while(m_i < m_vectNum.length)
         {
            m_vectNum[m_i].render(param1);
            m_i++;
         }
         if(m_midautumnResult)
         {
            m_midautumnResult.render(param1);
         }
         if(m_bRewardShow == false && getIsEndShow() && m_bStartRun)
         {
            m_bRewardShow = true;
            if(m_timeId > 0)
            {
               clearTimeout(m_timeId);
               m_timeId = 0;
            }
            m_timeId = setTimeout(mainResult,2000);
         }
      }
      
      private function mainResult() : void
      {
         clearTimeout(m_timeId);
         m_timeId = 0;
         showResult();
      }
      
      private function getIsEndShow() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:Boolean = true;
         _loc2_ = 0;
         while(_loc2_ < m_vectNum.length)
         {
            if(m_vectNum[_loc2_].getEndShow() == false)
            {
               _loc1_ = false;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      private function sendInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorHandler);
         _loc1_.addEventListener("securityError",onErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strOpenUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         m_useNum = int(_loc2_.BoBingTimes);
         MidautumnData.getInstance().setFreeNum(int(_loc2_.FreeTime));
         MidautumnData.getInstance().setNumZY(int(_loc2_.ZyCount));
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         getTotalRechargeForSomeTimeForReset();
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求数据错误!");
      }
      
      protected function onErrorGetNumHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorGetNumHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorGetNumHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求数据错误!");
         m_btnStart.unLock();
      }
      
      public function sendGetResultNum() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         m_bGetSixNum = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderNumResultHandler);
         _loc1_.addEventListener("ioError",onErrorGetNumHandler);
         _loc1_.addEventListener("securityError",onErrorGetNumHandler);
         var _loc3_:URLRequest = new URLRequest(this.m_getNumUrl);
         _loc3_.method = "POST";
         _loc3_.contentType = "application/json";
         var _loc4_:Object = {};
         _loc4_.MD5 = "2B01530154A2C991";
         _loc4_.UID = GameData.getInstance().getLoginReturnData().getUid();
         if(MidautumnData.getInstance().getFreeNum() > 0)
         {
            MidautumnData.getInstance().setIsAdd(false);
         }
         else
         {
            MidautumnData.getInstance().setIsAdd(true);
         }
         var _loc2_:Boolean = false;
         if(MidautumnData.getInstance().getIsAdd() == false || MidautumnData.getInstance().getIsBuyAdd() == false)
         {
            _loc2_ = false;
         }
         else
         {
            _loc2_ = true;
         }
         _loc4_.IsAdd = _loc2_;
         _loc3_.data = MyJSON.encode(_loc4_);
         _loc1_.load(_loc3_);
      }
      
      protected function onLoaderNumResultHandler(param1:Event) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",onLoaderNumResultHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorGetNumHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorGetNumHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         var _loc4_:Array = String(_loc2_.Result).split(",");
         _loc3_ = 0;
         while(_loc3_ < m_vectNum.length)
         {
            m_vectNum[_loc3_].setEndXY(m_xlist[_loc3_],m_ylist[_loc3_]);
            m_vectNum[_loc3_].setResult(int(_loc4_[_loc3_]));
            _loc3_++;
         }
         MidautumnData.getInstance().setRid(int(_loc2_.Rid));
         MidautumnData.getInstance().setNumZY(int(_loc2_.Zy));
         m_useNum = int(_loc2_.BoBingTimes);
         MidautumnData.getInstance().setIsBuyAdd(true);
         m_bGetSixNum = true;
         if(MidautumnData.getInstance().getFreeNum() > 0)
         {
            MidautumnData.getInstance().setFreeNum(MidautumnData.getInstance().getFreeNum() - 1);
         }
         refreshNum();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         m_handLogic.MouseMove(null);
      }
      
      public function getSixNumLoad() : Boolean
      {
         return m_bGetSixNum;
      }
      
      public function init(param1:MovieClip, param2:MidautumnPanel) : void
      {
         param1.addEventListener("clickButton",clickButton,true,0,true);
         param1.addEventListener("warningBox",sureOrCancel,true,0,true);
         m_show = param1;
         m_midautumnPanel = param2;
         initNum(m_show);
         initOut(m_show);
         initMain(m_show["mainbobing"]);
         initGet(m_show);
         initRule(m_show);
         initResult(m_show);
         initRank(m_show);
         initWzwResult(m_show);
         initWzwPanel(m_show);
         if(MidautumnData.getInstance().getLoadMoney() == true)
         {
            showWarningBox("金额获取中，请稍后......",1,{
               "type":"showtip",
               "okFunction":callclose
            });
            return;
         }
         sendInfo();
      }
      
      private function callclose() : void
      {
         m_midautumnPanel.CloseUI();
      }
      
      private function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChild(WarningBoxSingle.getInstance());
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function refreshTime() : void
      {
         m_txtFreeTime.text = String(MidautumnData.getInstance().getFreeNum() + int(m_rechargedMoney / MidautumnData.getInstance().getTicketToNum()) - m_useNum);
         m_txtNumZy.text = String(MidautumnData.getInstance().getNumZY());
      }
      
      private function refreshNum() : void
      {
         m_txtFreeTime.text = String(MidautumnData.getInstance().getFreeNum() + int(m_rechargedMoney / MidautumnData.getInstance().getTicketToNum()) - m_useNum);
      }
      
      private function getTotalRechargeForSomeTimeForReset() : void
      {
         m_payAPIForReset.addPayAPIListener(m_payAPIListenerForReset);
         if(m_dateInforOfGetTotalRechargeForReset == null)
         {
            m_dateInforOfGetTotalRechargeForReset = new DateInfor("2018-09-20 00:00:00","2018-10-12 00:00:00");
         }
         m_payAPIForReset.getTotalRechargeFun(m_dateInforOfGetTotalRechargeForReset);
         GamingUI.getInstance().manBan.text.text = "请求数据中， 请勿关闭游戏！";
      }
      
      private function getRechargedMoneyErrorForReset() : void
      {
         m_show.mouseChildren = true;
         m_show.mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("获取充值失败");
      }
      
      private function getRechargedMoneySuccessForReset(param1:int) : void
      {
         if(m_payAPIForReset)
         {
            m_payAPIForReset.removePayAPIListener(m_payAPIListenerForReset);
         }
         m_rechargedMoney = param1;
         if(m_bBtnClick)
         {
            startPlay();
            m_bBtnClick = false;
         }
         refreshTime();
         m_show.mouseChildren = true;
         m_show.mouseEnabled = true;
      }
      
      private function initNum(param1:MovieClip) : void
      {
         var _loc2_:DiceItem = null;
         var _loc3_:int = 0;
         m_numPanel = param1["panelnum"] as MovieClip;
         m_handLogic = new HandLogic();
         m_handLogic.init(m_numPanel,this);
         _loc3_ = 0;
         while(_loc3_ < 6)
         {
            _loc2_ = new DiceItem();
            _loc2_.init(_loc3_ + 1,m_numPanel,this);
            _loc2_.setEndXY(m_xlist[_loc3_],m_ylist[_loc3_]);
            m_vectNum.push(_loc2_);
            _loc3_++;
         }
      }
      
      private function initOut(param1:MovieClip) : void
      {
         (param1["playcan"] as MovieClip).visible = false;
      }
      
      private function initMain(param1:MovieClip) : void
      {
         m_btnClose = new ButtonLogicShell2();
         m_btnClose.setShow(param1["btnClose"]);
         m_btnClose.setTipString("关闭");
         m_btnStart = new ButtonLogicShell2();
         m_btnStart.setShow(param1["btnStart"]);
         m_btnStart.setTipString("点击博饼");
         m_btnRule = new ButtonLogicShell2();
         m_btnRule.setShow(param1["btnRule"]);
         m_btnRule.setTipString("玩法说明");
         m_txtFreeTime = param1["txtNum"] as TextField;
         m_txtNumZy = param1["txtNumzy"] as TextField;
         m_txtFreeTime.text = "0";
         m_txtNumZy.text = "0";
      }
      
      private function initGet(param1:MovieClip) : void
      {
         m_getPanel = param1["getpanel"] as MovieClip;
         m_getPanel.visible = false;
      }
      
      private function initRule(param1:MovieClip) : void
      {
         m_rulePanel = param1["panelrule"] as MovieClip;
         m_rulePanel.visible = false;
         m_rulePanel.gotoAndStop("1");
         m_rulePanel.addEventListener("click",callRule);
      }
      
      private function initResult(param1:MovieClip) : void
      {
         m_resultPanel = param1["panelresult"] as MovieClip;
         m_resultPanel.visible = false;
      }
      
      private function initRank(param1:MovieClip) : void
      {
         m_rankPanel = param1["panelrank"] as MovieClip;
         m_rankPanel.visible = false;
      }
      
      private function initWzwResult(param1:MovieClip) : void
      {
         m_wzwResult = param1["wzwResult"] as MovieClip;
         m_wzwResult.visible = false;
      }
      
      private function initWzwPanel(param1:MovieClip) : void
      {
         m_wzwMainPanel = param1["panelwzwmain"] as MovieClip;
         m_wzwMainPanel.visible = false;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(MidautumnData.getInstance().getLoadMoney() == true)
         {
            return;
         }
         switch(param1.button)
         {
            case m_btnClose:
               if(m_bStartRun == false)
               {
                  m_midautumnPanel.CloseUI();
               }
               break;
            case m_btnRule:
               if(m_bStartRun == false)
               {
                  m_rulePanel.visible = true;
               }
               break;
            case m_btnStart:
               if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
               {
                  GamingUI.getInstance().showMessageTip("背包空位不足，请先清理");
                  return;
               }
               m_bBtnClick = true;
               getTotalRechargeForSomeTimeForReset();
               break;
         }
      }
      
      private function startPlay() : void
      {
         if(MidautumnData.getInstance().getFreeNum() + int(m_rechargedMoney / MidautumnData.getInstance().getTicketToNum()) - m_useNum > 0)
         {
            m_btnStart.lock();
            sendGetResultNum();
         }
         else
         {
            showIfBuy();
         }
      }
      
      private function callRule(param1:MouseEvent) : void
      {
         m_rulePanel.visible = false;
      }
      
      public function unlockBtn() : void
      {
         m_btnStart.unLock();
      }
      
      public function hideAllNum() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].hideItem();
            _loc1_++;
         }
      }
      
      public function showAllNum() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].showItem();
            _loc1_++;
         }
      }
      
      public function startRun() : void
      {
         var _loc1_:int = 0;
         m_bStartRun = true;
         showAllNum();
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].setType(m_handLogic.getType());
            m_vectNum[_loc1_].Calculate_BeginPos();
            _loc1_++;
         }
      }
      
      public function showResult() : void
      {
         m_resultPanel.visible = true;
         if(m_midautumnResult == null)
         {
            m_midautumnResult = new MidautumnResult();
            m_midautumnResult.init(m_resultPanel,this);
         }
         m_calculate.init(m_vectNum);
         m_midautumnResult.showRewardByResult(m_calculate.getResult());
         if(m_calculate.getResult() == 1 || m_calculate.getResult() == 2 || m_calculate.getResult() == 3)
         {
         }
         refreshTime();
         GamingUI.getInstance().refresh(16384);
      }
      
      public function hideResult() : void
      {
         m_resultPanel.visible = false;
         m_bRewardShow = false;
         m_bStartRun = false;
         refreshTime();
      }
      
      public function showIfBuy() : void
      {
         m_getPanel.visible = true;
         if(m_ifbuyView == null)
         {
            m_ifbuyView = new IfBuyView();
            m_ifbuyView.init(m_getPanel,this);
         }
      }
      
      public function hideIfBuy() : void
      {
         m_getPanel.visible = false;
         if(m_ifbuyView)
         {
            m_ifbuyView.removeTip();
         }
      }
      
      public function startReward() : void
      {
         m_btnStart.lock();
         m_handLogic.MouseMove(null);
      }
   }
}

