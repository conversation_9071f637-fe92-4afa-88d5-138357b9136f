package UI.ShopWall
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class RechargeBtn extends Btn
   {
      public function RechargeBtn()
      {
         super();
         setTipString("点击打开充值网址");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickRechargeBtn"));
      }
   }
}

