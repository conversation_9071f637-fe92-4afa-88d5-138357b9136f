package UI2.Mount.MountLogic
{
   import UI2.Mount.MountData.MountSystemData.MaterialData;
   import UI2.Mount.MountData.MountVO;
   import YJFY.Utils.ClearUtil;
   
   public class RandomMaterialReturnData
   {
      public var resultMaterialDatas:Vector.<MaterialData>;
      
      public var resultMateiralDataNums:Vector.<uint>;
      
      public var newGetMountVOs:Vector.<MountVO>;
      
      public var upLevelMountVOs:Vector.<MountVO>;
      
      public function RandomMaterialReturnData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(resultMaterialDatas,false,false,false);
         resultMaterialDatas = null;
         ClearUtil.clearObject(resultMateiralDataNums);
         resultMateiralDataNums = null;
         ClearUtil.nullArr(newGetMountVOs,false,false,false);
         newGetMountVOs = null;
         ClearUtil.nullArr(upLevelMountVOs,false,false,false);
         upLevelMountVOs = null;
      }
   }
}

