package UI.Task.TaskReward
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class TaskRewardVO
   {
      public var type:String;
      
      public var description:String;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function TaskRewardVO()
      {
         super();
         init();
      }
      
      protected function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
   }
}

