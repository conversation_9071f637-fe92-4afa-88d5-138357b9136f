package UI.Button.Pet
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Pet_InforBtn extends SwitchBtn
   {
      public function Pet_InforBtn()
      {
         super();
         setTipString("宠物信息");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToPetInformation"));
      }
   }
}

