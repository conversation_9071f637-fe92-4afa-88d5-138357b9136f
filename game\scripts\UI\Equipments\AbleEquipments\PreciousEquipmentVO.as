package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import YJFY.Utils.ClearUtil;
   
   public class PreciousEquipmentVO extends AbleEquipmentVO
   {
      public var basisAttr:Vector.<String> = new Vector.<String>();
      
      public var basisAttrValue:Vector.<Number> = new Vector.<Number>();
      
      public var basisMinUp:Vector.<Number> = new Vector.<Number>();
      
      public var basisMaxUp:Vector.<Number> = new Vector.<Number>();
      
      public var basisUpValue:Vector.<Number> = new Vector.<Number>();
      
      public var basisWeight:Vector.<Number> = new Vector.<Number>();
      
      public var basisTotalWeight:Vector.<Number> = new Vector.<Number>();
      
      public var materialid:int;
      
      public var sAttrName:Vector.<String> = new Vector.<String>();
      
      public var sAttrValue:Vector.<Number> = new Vector.<Number>();
      
      public var sAvgValue:Vector.<Number> = new Vector.<Number>();
      
      public var sMaxValue:Vector.<Number> = new Vector.<Number>();
      
      public var sMinValue:Vector.<Number> = new Vector.<Number>();
      
      public var sWeight:Vector.<Number> = new Vector.<Number>();
      
      public var sTotalWeight:Vector.<Number> = new Vector.<Number>();
      
      public var addPlayerSaveAttr:Vector.<String> = new Vector.<String>();
      
      public var addPlayerSaveAttrVals:Vector.<Number> = new Vector.<Number>();
      
      public function PreciousEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(sWeight);
         sWeight = null;
         ClearUtil.clearObject(sTotalWeight);
         sTotalWeight = null;
         ClearUtil.clearObject(sMaxValue);
         sMaxValue = null;
         ClearUtil.clearObject(sMinValue);
         sMinValue = null;
         ClearUtil.clearObject(basisWeight);
         basisWeight = null;
         ClearUtil.clearObject(basisTotalWeight);
         basisTotalWeight = null;
         ClearUtil.clearObject(basisAttr);
         basisAttr = null;
         ClearUtil.clearObject(basisAttrValue);
         basisAttrValue = null;
         ClearUtil.clearObject(basisMinUp);
         basisMinUp = null;
         ClearUtil.clearObject(basisMaxUp);
         basisMaxUp = null;
         ClearUtil.clearObject(basisUpValue);
         basisUpValue = null;
         ClearUtil.clearObject(sAttrName);
         sAttrName = null;
         ClearUtil.clearObject(sAttrValue);
         sAttrValue = null;
         ClearUtil.clearObject(sAvgValue);
         sAvgValue = null;
         super.clear();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:PreciousEquipmentVO = new PreciousEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as PreciousEquipmentVO).materialid = materialid;
         _loc2_ = int(addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).addPlayerSaveAttr.push(addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).addPlayerSaveAttrVals.push(addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisAttr.push(basisAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisAttrValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisAttrValue.push(basisAttrValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisMinUp.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisMinUp.push(basisMinUp[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisUpValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisUpValue.push(basisUpValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisMaxUp.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisMaxUp.push(basisMaxUp[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisWeight.push(basisWeight[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(basisTotalWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).basisTotalWeight.push(basisTotalWeight[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sAttrName.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sAttrName.push(sAttrName[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sAttrValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sAttrValue.push(sAttrValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sAvgValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sAvgValue.push(sAvgValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sMaxValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sMaxValue.push(sMaxValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sMinValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sMinValue.push(sMinValue[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sWeight.push(sWeight[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(sTotalWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as PreciousEquipmentVO).sTotalWeight.push(sTotalWeight[_loc3_]);
            _loc3_++;
         }
      }
      
      public function getAmount() : int
      {
         return basisAttr.length;
      }
      
      public function setData(param1:PreciousEquipmentVO) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         basisAttr.length = 0;
         _loc2_ = int(param1.basisAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisAttr.push(param1.basisAttr[_loc3_]);
            _loc3_++;
         }
         basisAttrValue.length = 0;
         _loc2_ = int(param1.basisAttrValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisAttrValue.push(param1.basisAttrValue[_loc3_]);
            _loc3_++;
         }
         basisMinUp.length = 0;
         _loc2_ = int(param1.basisMinUp.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisMinUp.push(param1.basisMinUp[_loc3_]);
            _loc3_++;
         }
         basisMaxUp.length = 0;
         _loc2_ = int(param1.basisMaxUp.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisMaxUp.push(param1.basisMaxUp[_loc3_]);
            _loc3_++;
         }
         basisUpValue.length = 0;
         _loc2_ = int(param1.basisUpValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisUpValue.push(param1.basisUpValue[_loc3_]);
            _loc3_++;
         }
         basisWeight.length = 0;
         _loc2_ = int(param1.basisWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisWeight.push(param1.basisWeight[_loc3_]);
            _loc3_++;
         }
         basisTotalWeight.length = 0;
         _loc2_ = int(param1.basisTotalWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            basisTotalWeight.push(param1.basisTotalWeight[_loc3_]);
            _loc3_++;
         }
         sAttrName.length = 0;
         _loc2_ = int(param1.sAttrName.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sAttrName.push(param1.sAttrName[_loc3_]);
            _loc3_++;
         }
         sAttrValue.length = 0;
         _loc2_ = int(param1.sAttrValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sAttrValue.push(param1.sAttrValue[_loc3_]);
            _loc3_++;
         }
         sAvgValue.length = 0;
         _loc2_ = int(param1.sAvgValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sAvgValue.push(param1.sAvgValue[_loc3_]);
            _loc3_++;
         }
         sMaxValue.length = 0;
         _loc2_ = int(param1.sMaxValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sMaxValue.push(param1.sMaxValue[_loc3_]);
            _loc3_++;
         }
         sMinValue.length = 0;
         _loc2_ = int(param1.sMinValue.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sMinValue.push(param1.sMinValue[_loc3_]);
            _loc3_++;
         }
         sWeight.length = 0;
         _loc2_ = int(param1.sWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sWeight.push(param1.sWeight[_loc3_]);
            _loc3_++;
         }
         sTotalWeight.length = 0;
         _loc2_ = int(param1.sTotalWeight.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            sTotalWeight.push(param1.sTotalWeight[_loc3_]);
            _loc3_++;
         }
         addPlayerSaveAttr.length = 0;
         _loc2_ = int(param1.addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            addPlayerSaveAttr.push(param1.addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         addPlayerSaveAttrVals.length = 0;
         _loc2_ = int(param1.addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            addPlayerSaveAttrVals.push(param1.addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
      }
   }
}

