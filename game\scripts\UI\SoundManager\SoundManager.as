package UI.SoundManager
{
   import UI.MyFunction2;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   import flash.system.Capabilities;
   
   public class SoundManager
   {
      protected var _soundList:Object;
      
      protected var _allMuted:Boolean = false;
      
      protected var _canPlaySound:Boolean = Capabilities.hasAudio;
      
      public function SoundManager()
      {
         super();
         _soundList = {};
      }
      
      public function clear() : void
      {
         if(_soundList)
         {
            for(var _loc1_ in _soundList)
            {
               if(_soundList[_loc1_])
               {
                  _soundList[_loc1_].clear();
               }
               _soundList[_loc1_] = null;
            }
         }
         _soundList = null;
      }
      
      public function addSound(param1:String, param2:Sound) : void
      {
         var _loc3_:SoundObject = new SoundObject(param1,param2);
         _soundList[param1] = _loc3_;
      }
      
      public function play(param1:String, param2:Number = 0, param3:int = 0, param4:SoundTransform = null) : SoundChannel
      {
         var _loc5_:SoundChannel = null;
         if(_canPlaySound && _allMuted == false)
         {
            if(_soundList[param1])
            {
               _loc5_ = _soundList[param1].play(param2,param3,param4);
               if(_loc5_ == null)
               {
                  return null;
               }
               if(_allMuted)
               {
                  _soundList[param1].turnMuteOn();
               }
               return _loc5_;
            }
            return addSoundAndPlay(param1,param2,param3,param4);
         }
         return null;
      }
      
      protected function addSoundAndPlay(param1:String, param2:Number = 0, param3:int = 0, param4:SoundTransform = null) : SoundChannel
      {
         var _loc6_:Sound = null;
         var _loc5_:* = null;
         var _loc7_:Class = MyFunction2.returnClassByClassName(param1);
         if(_loc7_)
         {
            _loc6_ = new _loc7_() as Sound;
            addSound(param1,_loc6_);
            return play(param1,param2,param3,param4);
         }
         throw new Error("GameSoundEngine don\'t have" + param1 + " sound");
      }
      
      public function stop(param1:String = null) : void
      {
         if(_canPlaySound)
         {
            if(param1)
            {
               if(!_soundList[param1])
               {
                  throw new Error("Sound " + param1 + " was not found, ignoring stop command.");
               }
               _soundList[param1].stop();
            }
            else
            {
               for(var _loc2_ in _soundList)
               {
                  _soundList[_loc2_].stop();
               }
            }
         }
      }
      
      public function setSoundTransForm(param1:SoundTransform, param2:String = null) : void
      {
         if(_canPlaySound)
         {
            if(param2)
            {
               if(!_soundList[param2])
               {
                  throw new Error("Sound" + param2 + "was not found, ignoring stop command.");
               }
               _soundList[param2].soundTransform = param1;
            }
            else
            {
               for(var _loc3_ in _soundList)
               {
                  _soundList[_loc3_].soundTransform = param1;
               }
            }
         }
      }
      
      public function setVolume(param1:Number, param2:String = null) : void
      {
         if(_canPlaySound)
         {
            if(param2)
            {
               if(!_soundList[param2])
               {
                  throw new Error("Sound " + param2 + " was not found, ignoring stop command.");
               }
               _soundList[param2].volume = Math.max(0,Math.min(1,param1));
            }
            else
            {
               for(var _loc3_ in _soundList)
               {
                  _soundList[_loc3_].volume = Math.max(0,Math.min(1,param1));
               }
            }
         }
      }
      
      public function getVolume(param1:String = null) : Number
      {
         if(_canPlaySound)
         {
            if(param1)
            {
               if(_soundList[param1])
               {
                  return _soundList[param1].volume;
               }
               throw new Error("Sound " + param1 + " was not found, ignoring stop command.");
            }
         }
         return 0;
      }
      
      public function mute(param1:String = null) : void
      {
         if(_canPlaySound)
         {
            if(param1)
            {
               if(!_soundList[param1])
               {
                  throw new Error("Sound " + param1 + " was not found, ignoring stop command.");
               }
               _soundList[param1].mute();
               if(!_soundList[param1].isMuted)
               {
                  _allMuted = false;
               }
            }
            else
            {
               _allMuted = !_allMuted;
               if(_allMuted)
               {
                  for each(var _loc3_ in _soundList)
                  {
                     _loc3_.turnMuteOn();
                  }
               }
               else
               {
                  for each(var _loc2_ in _soundList)
                  {
                     _loc2_.turnMuteOff();
                  }
               }
            }
         }
      }
      
      public function turnAllSoundOn() : void
      {
         if(_canPlaySound)
         {
            if(_allMuted)
            {
               mute();
            }
            else
            {
               for each(var _loc1_ in _soundList)
               {
                  if(_loc1_.isMuted)
                  {
                     _loc1_.turnMuteOn();
                  }
               }
            }
         }
      }
      
      public function turnAllSoundOff() : void
      {
         if(_canPlaySound)
         {
            if(!_allMuted)
            {
               mute();
            }
         }
      }
      
      public function pause(param1:String = null) : void
      {
         if(_canPlaySound)
         {
            if(param1)
            {
               if(!_soundList[param1])
               {
                  throw new Error("Sound " + param1 + " was not found, ignoring stop command.");
               }
               _soundList[param1].pause();
            }
            else
            {
               for(var _loc2_ in _soundList)
               {
                  _soundList[_loc2_].pause();
               }
            }
         }
      }
      
      public function isPlaying(param1:String) : Boolean
      {
         if(_canPlaySound)
         {
            if(_soundList[param1])
            {
               return _soundList[param1].playing;
            }
            trace("Sound " + param1 + " does not exist.");
         }
         return false;
      }
      
      public function isPaused(param1:String) : Boolean
      {
         if(_canPlaySound)
         {
            if(_soundList[param1])
            {
               return _soundList[param1].isPaused;
            }
            throw new Error("Sound " + param1 + " does not exist.");
         }
         return false;
      }
      
      public function isMuted(param1:String = null) : Boolean
      {
         if(_canPlaySound)
         {
            if(param1)
            {
               if(_soundList[param1])
               {
                  return _soundList[param1].isMuted;
               }
               throw new Error("Sound " + param1 + " does not exist.");
            }
            return _allMuted;
         }
         return true;
      }
      
      public function get soundList() : Object
      {
         return _soundList;
      }
   }
}

