package UI2.broadcast
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class BroadcastFunction extends MovieClip
   {
      private static var _instance:BroadcastFunction;
      
      public var m_maxTime:int = 10;
      
      private var m_getTime:int = 10;
      
      private var m_nCdTime:int = 0;
      
      private var m_nCdSec:int = 0;
      
      private var m_isStart:Boolean = false;
      
      private var m_saveTime:Number;
      
      private var m_nowTime:Number;
      
      public var m_time:String;
      
      public var m_saveyear:int;
      
      public var m_savemonth:int;
      
      public var m_saveday:int;
      
      public var m_savehour:int;
      
      public var m_savemin:int;
      
      public var m_savescend:int;
      
      public var zanting:Boolean = false;
      
      public function BroadcastFunction()
      {
         super();
         addEventListener("enterFrame",renderRefresh);
      }
      
      public static function getInstance() : BroadcastFunction
      {
         if(_instance == null)
         {
            _instance = new BroadcastFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         removeEventListener("enterFrame",renderRefresh);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
      }
      
      public function renderRefresh(param1:Event) : void
      {
         if(m_isStart && zanting == false)
         {
            updatetime();
         }
      }
      
      private function updatetime() : void
      {
         this.m_nCdTime++;
         if(this.m_nCdTime >= 24)
         {
            this.m_nCdTime = 0;
            this.m_nCdSec++;
            if(m_nCdSec >= m_getTime * 60)
            {
               m_nCdSec = 0;
               m_getTime = m_maxTime;
               startGetData();
            }
         }
      }
      
      public function setTime(param1:String) : void
      {
         var nowDate:Date;
         var time:String = param1;
         var saveDate:Date = TimeUtil.getTimeUtil().stringToDate(time);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            checkNewDay(time,param1);
            nowDate = TimeUtil.getTimeUtil().stringToDate(param1);
            if(nowDate.time - saveDate.time >= m_maxTime * 60 * 1000)
            {
               m_time = param1;
               m_getTime = m_maxTime;
               startGetData();
               m_isStart = true;
            }
            else
            {
               m_getTime = (m_maxTime * 60 * 1000 - (nowDate.time - saveDate.time)) / 1000 / 60;
               m_isStart = true;
            }
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      private function checkNewDay(param1:String, param2:String) : void
      {
         var _loc4_:Date = TimeUtil.getTimeUtil().stringToDate(param1);
         var _loc3_:Date = TimeUtil.getTimeUtil().stringToDate(param2);
         if(_loc4_.fullYear != _loc3_.fullYear || _loc4_.month != _loc3_.month || _loc4_.day != _loc3_.day)
         {
            BroadDataManager.getInstance().upNum = 0;
         }
      }
      
      public function setFirst() : void
      {
         var nowDate:Date;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_time = param1;
            m_getTime = m_maxTime;
            startGetData();
            m_isStart = true;
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      public function startGetData() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_time = param1;
            GetDataFunction.getInstance().getRankListData(0,GetDataFunction.getInstance().rankidlist[0],1,30);
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
   }
}

