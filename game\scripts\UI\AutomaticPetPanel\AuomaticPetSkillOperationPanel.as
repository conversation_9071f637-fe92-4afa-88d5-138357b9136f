package UI.AutomaticPetPanel
{
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Other.EquipmentAndNumShow2;
   import UI.Other.NeedEquipmentData;
   import UI.Other.NeedEquipmentsData;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVOFactory;
   import YJFY.AutomaticPet.AutomaticSkillVO.IAutomaticPetPassiveSkillVO;
   import YJFY.Loader.IProgressShow;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AuomaticPetSkillOperationPanel extends MySprite
   {
      public const const_upgradeSkill:String = "upgradeSkill";
      
      public const const_getNewPassiveSkill:String = "getNewPassiveSkill";
      
      public const const_resetSkill:String = "resetSkill";
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_currentState:String;
      
      private var m_ableDragMC:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_oldSkillCell:AutomaticPetSkillCellLogic;
      
      private var m_newSkillCell:AutomaticPetSkillCellLogic;
      
      private var m_upgradeNeedEqAndNumShows:Vector.<EquipmentAndNumShow2>;
      
      private var m_needMoneyText:TextField;
      
      private var m_successRateText:TextField;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var m_nextLevelAutomaticPetSkillVO:AutomaticPetSkillVO;
      
      private var m_automaticPetSkillOprationData:AutomaticPetSkillOprationData;
      
      private var m_needEquipments:Vector.<Equipment>;
      
      private var m_skillIds:Vector.<String>;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetSkillVO:AutomaticPetSkillVO;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_inforShowPanel:InforShowPanel;
      
      private var m_automaticPetDataXML:XML;
      
      public function AuomaticPetSkillOperationPanel()
      {
         super();
         m_ableDragMC = new AbleDragSpriteLogicShell();
         m_quitBtn = new ButtonLogicShell2();
         m_showMC = new MovieClipPlayLogicShell();
         m_upgradeNeedEqAndNumShows = new Vector.<EquipmentAndNumShow2>();
         m_sureBtn = new ButtonLogicShell2();
         m_cancelBtn = new ButtonLogicShell2();
         m_automaticPetSkillOprationData = new AutomaticPetSkillOprationData();
         m_needEquipments = new Vector.<Equipment>();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_ableDragMC);
         m_ableDragMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_oldSkillCell);
         m_oldSkillCell = null;
         ClearUtil.clearObject(m_newSkillCell);
         m_newSkillCell = null;
         ClearUtil.clearObject(m_upgradeNeedEqAndNumShows);
         m_upgradeNeedEqAndNumShows = null;
         m_needMoneyText = null;
         m_successRateText = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         ClearUtil.clearObject(m_nextLevelAutomaticPetSkillVO);
         m_nextLevelAutomaticPetSkillVO = null;
         ClearUtil.clearObject(m_automaticPetSkillOprationData);
         m_automaticPetSkillOprationData = null;
         ClearUtil.clearObject(m_needEquipments);
         m_needEquipments = null;
         m_currentState = null;
         ClearUtil.clearObject(m_skillIds);
         m_skillIds = null;
         ClearUtil.clearObject(m_buyMaterialBtn);
         m_buyMaterialBtn = null;
         m_automaticPetVO = null;
         m_automaticPetSkillVO = null;
         m_versionControl = null;
         m_loadUI = null;
         m_inforShowPanel = null;
         m_automaticPetDataXML = null;
         super.clear();
      }
      
      public function init(param1:IVersionControl, param2:IProgressShow, param3:InforShowPanel, param4:XML) : void
      {
         m_versionControl = param1;
         m_loadUI = param2;
         m_inforShowPanel = param3;
         m_automaticPetDataXML = param4;
         m_show = MyFunction2.returnShowByClassName("AutomaitcPetSkillOperationPanel") as MovieClip;
         addChild(m_show);
         initShow();
      }
      
      public function setUpgradeData(param1:AutomaticPetSkillVO, param2:AutomaticPetVO) : void
      {
         m_automaticPetSkillVO = param1;
         m_automaticPetVO = param2;
         m_currentState = "upgradeSkill";
         initUpgradeSkillShow();
      }
      
      public function setGetNewSkillData(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         m_currentState = "getNewPassiveSkill";
         initGetNewSkillShow();
      }
      
      public function setResetSkillsData(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         m_currentState = "resetSkill";
         initResetSkillsShow();
      }
      
      private function initResetSkillsShow() : void
      {
         initResetSkillsFrame();
         ClearUtil.clearObject(m_nextLevelAutomaticPetSkillVO);
         m_nextLevelAutomaticPetSkillVO = null;
         if(m_automaticPetVO == null)
         {
            return;
         }
         getResetSkillsData();
         showOperationData();
      }
      
      private function initGetNewSkillShow() : void
      {
         initGetNewSkillFrame();
         ClearUtil.clearObject(m_nextLevelAutomaticPetSkillVO);
         m_nextLevelAutomaticPetSkillVO = null;
         if(m_automaticPetVO == null)
         {
            return;
         }
         getGetNewSkillData();
         showOperationData();
      }
      
      private function initUpgradeSkillShow() : void
      {
         initUpgradeSkillFrame();
         m_oldSkillCell.setData(null,null);
         m_newSkillCell.setData(null,null);
         ClearUtil.clearObject(m_nextLevelAutomaticPetSkillVO);
         m_nextLevelAutomaticPetSkillVO = null;
         if(m_automaticPetSkillVO == null || m_automaticPetVO == null)
         {
            return;
         }
         getUpgradeSkillData(m_automaticPetSkillVO);
         m_nextLevelAutomaticPetSkillVO = m_automaticPetSkillVO.clone();
         m_nextLevelAutomaticPetSkillVO.changeLevel(m_nextLevelAutomaticPetSkillVO.getLevel() + 1);
         m_oldSkillCell.setData(m_automaticPetVO,m_automaticPetSkillVO);
         m_newSkillCell.setData(m_automaticPetVO,m_nextLevelAutomaticPetSkillVO);
         showOperationData();
      }
      
      private function showOperationData() : void
      {
         var _loc4_:int = 0;
         var _loc2_:Equipment = null;
         var _loc3_:NeedEquipmentData = null;
         if(m_show == null)
         {
            return;
         }
         ClearUtil.clearObject(m_needEquipments);
         m_needEquipments.length = 0;
         ClearUtil.clearObject(m_buyMaterialBtn);
         m_buyMaterialBtn = null;
         m_automaticPetSkillOprationData.getNeedEquipmentsData().refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         var _loc1_:int = int(m_upgradeNeedEqAndNumShows.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            if(m_automaticPetSkillOprationData.getNeedEquipmentsData().getNeedEquipmentDataNum() < _loc4_ + 1)
            {
               break;
            }
            _loc3_ = m_automaticPetSkillOprationData.getNeedEquipmentsData().getNeedEquipmentDataByIndex(_loc4_);
            _loc2_ = MyFunction2.sheatheEquipmentShell(_loc3_.getEquipmentVO());
            _loc2_.addEventListener("rollOver",onOver,false,0,true);
            _loc2_.addEventListener("rollOut",onOut,false,0,true);
            m_needEquipments.push(_loc2_);
            m_upgradeNeedEqAndNumShows[_loc4_].getEqContainer().addChild(_loc2_);
            m_upgradeNeedEqAndNumShows[_loc4_].getCurrentNumText().text = _loc3_.getCurrentNum().toString();
            m_upgradeNeedEqAndNumShows[_loc4_].getSlashText().text = "/";
            m_upgradeNeedEqAndNumShows[_loc4_].getNumText().text = _loc3_.getNum().toString();
            if(_loc3_.getCurrentNum() < _loc3_.getNum())
            {
               m_upgradeNeedEqAndNumShows[_loc4_].changeCurrentNumTextColor(16711680);
               if(_loc3_.getEquipmentVO().ticketPrice)
               {
                  m_buyMaterialBtn = new BuyMaterialGuideBtn(_loc3_.getEquipmentVO().id,showOperationData,1);
                  m_buyMaterialBtn.x = m_upgradeNeedEqAndNumShows[_loc4_].getShow().x - m_upgradeNeedEqAndNumShows[_loc4_].getShow().width / 2 + 5;
                  m_buyMaterialBtn.y = m_upgradeNeedEqAndNumShows[_loc4_].getShow().y + m_upgradeNeedEqAndNumShows[_loc4_].getShow().height / 2;
                  m_show.addChild(m_buyMaterialBtn);
               }
            }
            else
            {
               m_upgradeNeedEqAndNumShows[_loc4_].changeCurrentNumTextColor(52224);
            }
            _loc4_++;
         }
         if(m_automaticPetSkillOprationData.getSuccessRate() < 1)
         {
            MyFunction2.changeTextFieldColor(16711680,m_successRateText);
         }
         else
         {
            MyFunction2.changeTextFieldColor(65280,m_successRateText);
         }
         m_successRateText.text = int(m_automaticPetSkillOprationData.getSuccessRate() * 100) + "%";
         if(m_automaticPetSkillOprationData.getIsEnoughForMoney(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null))
         {
            MyFunction2.changeTextFieldColor(65280,m_needMoneyText);
         }
         else
         {
            MyFunction2.changeTextFieldColor(16711680,m_needMoneyText);
         }
         m_needMoneyText.text = m_automaticPetSkillOprationData.getNeedMoney().toString();
      }
      
      private function getUpgradeSkillData(param1:AutomaticPetSkillVO) : void
      {
         var _loc3_:XML = getUpgradeSkillXML(m_automaticPetSkillVO);
         if(_loc3_ == null)
         {
            m_inforShowPanel.getAutomaticPetPanel().showWarningBox("没有技能升级数据，尝试升级技能失败",0);
            m_inforShowPanel.closeAutomaticSkillOperationPanel();
         }
         var _loc2_:NeedEquipmentsData = new NeedEquipmentsData();
         _loc2_.initByXML(_loc3_,GamingUI.getInstance().getNewestTimeStrFromSever());
         _loc2_.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         m_automaticPetSkillOprationData.setData(_loc2_,_loc3_.@successRate,_loc3_.@money);
      }
      
      private function getGetNewSkillData() : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:XML = getGetNewSkillXML();
         if(_loc4_ == null)
         {
            m_inforShowPanel.getAutomaticPetPanel().showWarningBox("没有技能升级数据，尝试升级技能失败",0);
            m_inforShowPanel.closeAutomaticSkillOperationPanel();
         }
         var _loc1_:NeedEquipmentsData = new NeedEquipmentsData();
         _loc1_.initByXML(_loc4_,GamingUI.getInstance().getNewestTimeStrFromSever());
         _loc1_.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         m_automaticPetSkillOprationData.setData(_loc1_,_loc4_.@successRate,_loc4_.@money);
         ClearUtil.clearObject(m_skillIds);
         m_skillIds = MyFunction.getInstance().excreteStringToString(_loc4_.@skillIds);
         var _loc3_:int = int(m_automaticPetVO.getPassiveSkillVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_ = int(m_skillIds.indexOf(m_automaticPetVO.getPassiveSkillVOByIndex(_loc5_).getId()));
            while(_loc2_ != -1)
            {
               m_skillIds.splice(_loc2_,1);
               _loc2_ = int(m_skillIds.indexOf(m_automaticPetVO.getPassiveSkillVOByIndex(_loc5_).getId()));
            }
            _loc5_++;
         }
      }
      
      private function getResetSkillsData() : void
      {
         var _loc2_:XML = getResetSkillsXML();
         if(_loc2_ == null)
         {
            m_inforShowPanel.getAutomaticPetPanel().showWarningBox("没有技能升级数据，尝试升级技能失败",0);
            m_inforShowPanel.closeAutomaticSkillOperationPanel();
         }
         var _loc1_:NeedEquipmentsData = new NeedEquipmentsData();
         _loc1_.initByXML(_loc2_,GamingUI.getInstance().getNewestTimeStrFromSever());
         _loc1_.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         m_automaticPetSkillOprationData.setData(_loc1_,_loc2_.@successRate,_loc2_.@money);
         ClearUtil.clearObject(m_skillIds);
         m_skillIds = MyFunction.getInstance().excreteStringToString(_loc2_.@skillIds);
      }
      
      private function getUpgradeSkillXML(param1:AutomaticPetSkillVO) : XML
      {
         var _loc2_:XML = m_automaticPetDataXML.upgradeSkills[0].skill.(@id == param1.getId())[0];
         if(_loc2_ == null)
         {
            _loc2_ = m_automaticPetDataXML.upgradeSkills[0].skill.(@id == "default")[0];
         }
         return _loc2_.levelData.(@oldLevel == param1.getLevel())[0];
      }
      
      private function getGetNewSkillXML() : XML
      {
         return m_automaticPetDataXML.getNewSkill[0];
      }
      
      private function getResetSkillsXML() : XML
      {
         return m_automaticPetDataXML.resetSkill[0];
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:EquipmentAndNumShow2 = null;
         m_showMC.setShow(m_show);
         m_ableDragMC.setShow(m_show);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_needMoneyText = m_show["moneyText"];
         m_successRateText = m_show["successRateText"];
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         var _loc3_:int = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = m_show.getChildAt(_loc5_);
            if(_loc1_.name.substr(0,13) == "materialShow_")
            {
               _loc4_++;
            }
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = new EquipmentAndNumShow2();
            _loc2_.setShow(m_show["materialShow_" + (_loc5_ + 1)]);
            m_upgradeNeedEqAndNumShows.push(_loc2_);
            _loc5_++;
         }
      }
      
      private function initUpgradeSkillFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("upgradeSkill");
         m_currentState = "upgradeSkill";
         m_oldSkillCell = new AutomaticPetSkillCellLogic();
         m_oldSkillCell.setLoadUI(m_loadUI);
         m_oldSkillCell.setVersionControl(m_versionControl);
         m_oldSkillCell.setShow(m_show["oldSkillCell"],null);
         m_newSkillCell = new AutomaticPetSkillCellLogic();
         m_newSkillCell.setLoadUI(m_loadUI);
         m_newSkillCell.setVersionControl(m_versionControl);
         m_newSkillCell.setShow(m_show["newSkillCell"],null);
      }
      
      private function initGetNewSkillFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("getNewPassiveSkill");
         m_currentState = "getNewPassiveSkill";
      }
      
      private function initResetSkillsFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("resetSkill");
         m_currentState = "resetSkill";
      }
      
      private function frameClear() : void
      {
         ClearUtil.clearObject(m_oldSkillCell);
         m_oldSkillCell = null;
         ClearUtil.clearObject(m_newSkillCell);
         m_newSkillCell = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quitBtn:
            case m_cancelBtn:
               m_inforShowPanel.closeAutomaticSkillOperationPanel();
               break;
            case m_sureBtn:
               startOprationSkill();
         }
      }
      
      private function startOprationSkill() : void
      {
         if(m_automaticPetSkillOprationData.getIsEnoughForNeedEquipments() == false)
         {
            m_inforShowPanel.getAutomaticPetPanel().showWarningBox("材料不足",0);
            return;
         }
         if(m_automaticPetSkillOprationData.getIsEnoughForMoney(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null) == false)
         {
            m_inforShowPanel.getAutomaticPetPanel().showWarningBox("元宝不足",0);
            return;
         }
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var _loc5_:AutomaticPetSkillVOFactory = null;
            var _loc8_:AutomaticPetSkillVO = null;
            var _loc6_:String = null;
            var _loc1_:int = 0;
            var _loc4_:* = 0;
            var _loc7_:int = 0;
            m_automaticPetSkillOprationData.minusNeedEquipmentsAndMoneyFromPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
            var _loc3_:Number = Math.random();
            if(_loc3_ < m_automaticPetSkillOprationData.getSuccessRate())
            {
               switch(m_currentState)
               {
                  case "upgradeSkill":
                     m_automaticPetSkillVO.changeLevel(m_automaticPetSkillVO.getLevel() + 1);
                     m_automaticPetVO.changeData();
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("升级成功",0);
                     break;
                  case "getNewPassiveSkill":
                     _loc1_ = Math.random() * m_skillIds.length;
                     _loc6_ = m_skillIds[_loc1_];
                     _loc5_ = new AutomaticPetSkillVOFactory();
                     _loc8_ = _loc5_.createNewAutomaticPetSkillVO(_loc6_,1);
                     ClearUtil.clearObject(_loc5_);
                     _loc5_ = null;
                     m_automaticPetVO.addPassiveSkillVO(_loc8_ as IAutomaticPetPassiveSkillVO);
                     m_automaticPetVO.changeData();
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("领悟技能成功",0);
                     break;
                  case "resetSkill":
                     _loc4_ = m_automaticPetVO.getPassiveSkillVONum();
                     m_automaticPetVO.clearPassiveSkillVOs();
                     _loc7_ = 0;
                     while(_loc7_ < _loc4_)
                     {
                        _loc1_ = Math.random() * m_skillIds.length;
                        _loc6_ = m_skillIds[_loc1_];
                        m_skillIds.splice(_loc1_,1);
                        _loc5_ = new AutomaticPetSkillVOFactory();
                        _loc8_ = _loc5_.createNewAutomaticPetSkillVO(_loc6_,1);
                        ClearUtil.clearObject(_loc5_);
                        _loc5_ = null;
                        m_automaticPetVO.addPassiveSkillVO(_loc8_ as IAutomaticPetPassiveSkillVO);
                        _loc7_++;
                     }
                     m_automaticPetVO.changeData();
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("洗练成功",0);
                     break;
                  case "resetSkill":
                     break;
                  default:
                     throw new Error();
               }
            }
            else
            {
               switch(m_currentState)
               {
                  case "upgradeSkill":
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("升级失败",0);
                     break;
                  case "getNewPassiveSkill":
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("领悟技能失败",0);
                     break;
                  case "resetSkill":
                     m_inforShowPanel.getAutomaticPetPanel().showWarningBox("洗练技能失败",0);
                     break;
                  default:
                     throw new Error();
               }
            }
            m_inforShowPanel.setData(m_automaticPetVO);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            m_inforShowPanel.closeAutomaticSkillOperationPanel();
         },m_inforShowPanel.getAutomaticPetPanel().showWarningBox,true);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":0
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

