package UI.RecaptureGold.Parent
{
   import UI.UIInterface.OldInterface.ISprite;
   import flash.display.DisplayObjectContainer;
   
   public interface IThief extends ISprite
   {
      function playAnimationAfterCatch(param1:XML, param2:DisplayObjectContainer, param3:ICatchTarget) : void;
      
      function playAnimationAfterExplodeDie(param1:XML, param2:DisplayObjectContainer, param3:ICatchTarget) : void;
      
      function clear() : void;
   }
}

