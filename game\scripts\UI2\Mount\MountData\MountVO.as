package UI2.Mount.MountData
{
   import UI.DataManagerParent;
   import UI.Players.PlayerVO;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountSkillVO.IMountPassiveSkillVO;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVO;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVOFactory;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   
   public class MountVO extends DataManagerParent
   {
      private var m_mountVOListeners:Vector.<IMountVOListener>;
      
      private var m_id:String;
      
      private var m_level1:uint;
      
      private var m_level2:uint;
      
      private var m_currPowerChipNum:uint;
      
      private var m_currStrengthenNum:uint;
      
      private var m_maxLevel1:uint;
      
      private var m_maxLevel2:uint;
      
      private var m_name:String;
      
      private var m_logicPartMountXMLPath:String;
      
      private var m_showSwfPath:String;
      
      private var m_showClassName:String;
      
      private var m_baseAttack:uint;
      
      private var m_proAttack1:Number;
      
      private var m_proAttack2:Number;
      
      private var m_baseDefence:uint;
      
      private var m_proDefence1:Number;
      
      private var m_proDefence2:Number;
      
      private var m_baseHp:uint;
      
      private var m_proHp1:Number;
      
      private var m_proHp2:Number;
      
      private var m_needPowerChipNum:uint;
      
      private var m_needStrengthenNum:uint;
      
      private var m_passiveSkillVOs:Vector.<IMountPassiveSkillVO>;
      
      private var m_attackAddToPlayer:uint;
      
      private var m_defenceAddToPlayer:uint;
      
      private var m_hpAddToPlayer:uint;
      
      private var m_isRide:Boolean;
      
      private var m_playerVO:PlayerVO;
      
      public function MountVO()
      {
         super();
         m_mountVOListeners = new Vector.<IMountVOListener>();
         m_passiveSkillVOs = new Vector.<IMountPassiveSkillVO>();
      }
      
      override public function clear() : void
      {
         ClearUtil.nullArr(m_mountVOListeners,false,false,false);
         m_mountVOListeners = null;
         m_id = null;
         m_name = null;
         m_logicPartMountXMLPath = null;
         m_showSwfPath = null;
         m_showClassName = null;
         ClearUtil.clearObject(m_passiveSkillVOs);
         m_passiveSkillVOs = null;
         m_playerVO = null;
         super.clear();
      }
      
      public function addMountVOListener(param1:IMountVOListener) : void
      {
         ListenerUtil.addListener(m_mountVOListeners,param1);
      }
      
      public function removeMountVOListener(param1:IMountVOListener) : void
      {
         ListenerUtil.removeListener(m_mountVOListeners,param1);
      }
      
      public function initFromSaveXML(param1:XML, param2:XML) : void
      {
         this.id = String(param1.@id);
         this.level1 = uint(param1.@l1);
         this.level2 = uint(param1.@l2);
         initFromXML(this.id,this.level1,this.level2,param2);
         this.currPowerChipNum = uint(param1.@pN);
         this.currStrengthenNum = uint(param1.@sN);
         this.isRide = Boolean(int(param1.@isR));
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = null;
         if(Boolean(this.level1) || this.currPowerChipNum)
         {
            _loc1_ = <mount />;
            _loc1_.@id = this.id;
            _loc1_.@l1 = this.level1;
            _loc1_.@l2 = this.level2;
            _loc1_.@pN = this.currPowerChipNum;
            _loc1_.@sN = this.currStrengthenNum;
            if(isRide)
            {
               _loc1_.@isR = int(this.isRide);
            }
            return _loc1_;
         }
         return null;
      }
      
      public function initFromXML(param1:String, param2:uint, param3:uint, param4:XML) : void
      {
         var _loc10_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:MountSkillVO = null;
         this.id = param1;
         this.level1 = param2;
         this.level2 = param3;
         var _loc8_:XML = param4.mount.(@id == param1)[0];
         this.name = String(_loc8_.@name);
         this.logicPartMountXMLPath = String(_loc8_.@logicPartMountXMLPath);
         this.showSwfPath = String(_loc8_.@showSwfPath);
         this.showClassName = String(_loc8_.@showClassName);
         this.maxLevel1 = uint(_loc8_.@maxLevel1);
         this.maxLevel2 = uint(_loc8_.@maxLevel2);
         this.baseAttack = uint(_loc8_.@baseAttack);
         this.proAttack1 = Number(_loc8_.@proAttack1);
         this.proAttack2 = Number(_loc8_.@proAttack2);
         this.baseDefence = uint(_loc8_.@baseDefence);
         this.proDefence1 = Number(_loc8_.@proDefence1);
         this.proDefence2 = Number(_loc8_.@proDefence2);
         this.baseHp = uint(_loc8_.@baseHp);
         this.proHp1 = Number(_loc8_.@proHp1);
         this.proHp2 = Number(_loc8_.@proHp2);
         initLevel1Data(param2,_loc8_);
         initLevel2Data(param3,_loc8_);
         updateAttackAddToPlayer();
         updateDefenceAddToPlayer();
         updateHpAddToPlayer();
         var _loc9_:XMLList = _loc8_.initSkillsData[0].passiveSkill;
         _loc6_ = int(!!_loc9_ ? _loc9_.length() : 0);
         var _loc5_:MountSkillVOFactory = new MountSkillVOFactory();
         _loc10_ = 0;
         while(_loc10_ < _loc6_)
         {
            _loc7_ = _loc5_.createNewMountSkillVO(_loc9_[_loc10_].@id,_loc9_[_loc10_].@level);
            if(_loc7_ is IMountPassiveSkillVO == false)
            {
               throw new Error("被动技能配置中存在非 被动技能配置");
            }
            m_passiveSkillVOs.push(_loc7_ as IMountPassiveSkillVO);
            (_loc7_ as IMountPassiveSkillVO).setMountVO(this);
            _loc10_++;
         }
         ClearUtil.clearObject(_loc5_);
         _loc5_ = null;
         this.currPowerChipNum = 0;
         this.currStrengthenNum = 0;
      }
      
      public function copy(param1:MountVO) : void
      {
         this.id = param1.id;
         this.level1 = param1.level1;
         this.level2 = param1.level2;
         initFromXML(this.id,this.level1,this.level2,XMLSingle.getInstance().mountsXML);
      }
      
      public function setLevel1(param1:uint) : void
      {
         this.level1 = param1;
         var _loc2_:XML = XMLSingle.getInstance().mountsXML.mount.(@id == id)[0];
         initLevel1Data(param1,_loc2_);
         updateAttackAddToPlayer();
         updateDefenceAddToPlayer();
         updateHpAddToPlayer();
         dispatchChangeData();
      }
      
      public function setLevel2(param1:uint) : void
      {
         this.level2 = param1;
         var _loc2_:XML = XMLSingle.getInstance().mountsXML.mount.(@id == id)[0];
         initLevel2Data(param1,_loc2_);
         updateAttackAddToPlayer();
         updateDefenceAddToPlayer();
         updateHpAddToPlayer();
      }
      
      public function clone() : MountVO
      {
         var _loc1_:MountVO = new MountVO();
         _loc1_.copy(this);
         return _loc1_;
      }
      
      public function setPlayerVO(param1:PlayerVO) : void
      {
         m_playerVO = param1;
         if(m_playerVO == null)
         {
            isRide = false;
         }
         dispatchChangeData();
      }
      
      public function addPowerChipNum(param1:uint) : uint
      {
         if(this.currPowerChipNum > this.needPowerChipNum)
         {
            throw new Error("power chip 计算出错了");
         }
         var _loc2_:int = param1 - needPowerChipNum + currPowerChipNum;
         if(_loc2_ >= 0)
         {
            currPowerChipNum = needPowerChipNum;
            if(this.level1 < this.maxLevel1)
            {
               setLevel1(this.level1 + 1);
               currPowerChipNum = 0;
               _loc2_ = int(addPowerChipNum(_loc2_));
            }
         }
         else
         {
            currPowerChipNum += param1;
         }
         return _loc2_;
      }
      
      public function addStrengthenNum(param1:uint) : uint
      {
         if(this.currStrengthenNum > this.needStrengthenNum)
         {
            throw new Error("strengthen num 计算出错了");
         }
         var _loc2_:int = param1 - needStrengthenNum + currStrengthenNum;
         if(_loc2_ >= 0)
         {
            currStrengthenNum = needStrengthenNum;
            if(this.level2 < this.maxLevel2)
            {
               setLevel2(this.level2 + 1);
               currStrengthenNum = 0;
               _loc2_ = int(addStrengthenNum(_loc2_));
            }
         }
         else
         {
            currStrengthenNum += param1;
         }
         return _loc2_;
      }
      
      public function getPlayerVO() : PlayerVO
      {
         return m_playerVO;
      }
      
      public function getIsRide() : Boolean
      {
         return isRide;
      }
      
      public function setIsRide(param1:Boolean) : void
      {
         isRide = param1;
      }
      
      public function getId() : String
      {
         return id;
      }
      
      public function getName() : String
      {
         return name;
      }
      
      public function getLevel1() : uint
      {
         return level1;
      }
      
      public function getLevel2() : uint
      {
         return level2;
      }
      
      public function getMaxLevel1() : uint
      {
         return maxLevel1;
      }
      
      public function getMaxLevel2() : uint
      {
         return maxLevel2;
      }
      
      public function getCurrentPowerChipNum() : uint
      {
         return currPowerChipNum;
      }
      
      public function getCurrentStrengthenNum() : uint
      {
         return currStrengthenNum;
      }
      
      public function getNeedPowerChipNum() : uint
      {
         return needPowerChipNum;
      }
      
      public function getNeedStrengthenNum() : uint
      {
         return needStrengthenNum;
      }
      
      public function getAttackAddToPlayer() : uint
      {
         return attackAddToPlayer;
      }
      
      public function getDefenceAddToPlayer() : uint
      {
         return defenceAddToPlayer;
      }
      
      public function getHpAddToPlayer() : uint
      {
         return hpAddToPlayer;
      }
      
      public function getLogicPartMountXMLPath() : String
      {
         return logicPartMountXMLPath;
      }
      
      public function getShowSwfPath() : String
      {
         return showSwfPath;
      }
      
      public function getShowClassName() : String
      {
         return showClassName;
      }
      
      public function getPassiveSkillVONum() : uint
      {
         return m_passiveSkillVOs.length;
      }
      
      public function getPassiveSkillVOByIndex(param1:int) : IMountPassiveSkillVO
      {
         return m_passiveSkillVOs[param1];
      }
      
      private function updateAttackAddToPlayer() : void
      {
         if(this.level1 == 0)
         {
            this.attackAddToPlayer = 0;
            return;
         }
         this.attackAddToPlayer = this.baseAttack + this.level2 * (this.proAttack1 + this.level1 * this.proAttack2);
      }
      
      private function updateDefenceAddToPlayer() : void
      {
         if(this.level1 == 0)
         {
            this.defenceAddToPlayer = 0;
            return;
         }
         this.defenceAddToPlayer = this.baseDefence + this.level2 * (this.proDefence1 + this.level1 * this.proDefence2);
      }
      
      private function updateHpAddToPlayer() : void
      {
         if(this.level1 == 0)
         {
            this.hpAddToPlayer = 0;
            return;
         }
         this.hpAddToPlayer = this.baseHp + this.level2 * (this.proHp1 + this.level1 * this.proHp2);
      }
      
      private function initLevel1Data(param1:uint, param2:XML) : void
      {
         var _loc6_:int = 0;
         var _loc5_:XML = param2.level1Datas[0].level1Data.(@level == param1)[0];
         var _loc3_:XMLList = _loc5_.data;
         var _loc4_:int = int(_loc3_.length());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            this[String(_loc3_[_loc6_].@att)] = String(_loc3_[_loc6_].@value);
            _loc6_++;
         }
      }
      
      private function initLevel2Data(param1:uint, param2:XML) : void
      {
         var _loc6_:int = 0;
         var _loc5_:XML = param2.level2Datas[0].level2Data.(@level == param1)[0];
         var _loc3_:XMLList = _loc5_.data;
         var _loc4_:int = int(_loc3_.length());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            this[String(_loc3_[_loc6_].@att)] = String(_loc3_[_loc6_].@value);
            _loc6_++;
         }
      }
      
      private function dispatchChangeData() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:Vector.<IMountVOListener> = !!m_mountVOListeners ? m_mountVOListeners.slice(0) : null;
         _loc2_ = !!_loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].changeData(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         if(_loc1_)
         {
            _loc1_.length = 0;
         }
         _loc1_ = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.id = m_id;
         _antiwear.level1 = m_level1;
         _antiwear.level2 = m_level2;
         _antiwear.currPowerChipNum = m_currPowerChipNum;
         _antiwear.currStrengthenNum = m_currStrengthenNum;
         _antiwear.maxLevel1 = m_maxLevel1;
         _antiwear.maxLevel2 = m_maxLevel2;
         _antiwear.baseAttack = m_baseAttack;
         _antiwear.proAttack1 = m_proAttack1;
         _antiwear.proAttack2 = m_proAttack2;
         _antiwear.baseDefence = m_baseDefence;
         _antiwear.proDefence1 = m_proDefence1;
         _antiwear.proDefence2 = m_proDefence2;
         _antiwear.baseHp = m_baseHp;
         _antiwear.proHp1 = m_proHp1;
         _antiwear.proHp2 = m_proHp2;
         _antiwear.needPowerChipNum = m_needPowerChipNum;
         _antiwear.needStrengthenNum = m_needStrengthenNum;
         _antiwear.attackAddToPlayer = m_attackAddToPlayer;
         _antiwear.defenceAddToPlayer = m_defenceAddToPlayer;
         _antiwear.hpAddToPlayer = m_hpAddToPlayer;
      }
      
      private function get id() : String
      {
         return _antiwear.id;
      }
      
      private function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      private function get level1() : uint
      {
         return _antiwear.level1;
      }
      
      private function set level1(param1:uint) : void
      {
         _antiwear.level1 = param1;
      }
      
      private function get level2() : uint
      {
         return _antiwear.level2;
      }
      
      private function set level2(param1:uint) : void
      {
         _antiwear.level2 = param1;
      }
      
      private function get currPowerChipNum() : uint
      {
         return _antiwear.currPowerChipNum;
      }
      
      private function set currPowerChipNum(param1:uint) : void
      {
         _antiwear.currPowerChipNum = param1;
      }
      
      private function get currStrengthenNum() : uint
      {
         return _antiwear.currStrengthenNum;
      }
      
      private function set currStrengthenNum(param1:uint) : void
      {
         _antiwear.currStrengthenNum = param1;
      }
      
      private function get maxLevel1() : uint
      {
         return _antiwear.maxLevel1;
      }
      
      private function set maxLevel1(param1:uint) : void
      {
         _antiwear.maxLevel1 = param1;
      }
      
      private function get maxLevel2() : uint
      {
         return _antiwear.maxLevel2;
      }
      
      private function set maxLevel2(param1:uint) : void
      {
         _antiwear.maxLevel2 = param1;
      }
      
      private function get name() : String
      {
         return m_name;
      }
      
      private function set name(param1:String) : void
      {
         m_name = param1;
      }
      
      private function get logicPartMountXMLPath() : String
      {
         return m_logicPartMountXMLPath;
      }
      
      private function set logicPartMountXMLPath(param1:String) : void
      {
         m_logicPartMountXMLPath = param1;
      }
      
      private function get showSwfPath() : String
      {
         return m_showSwfPath;
      }
      
      private function set showSwfPath(param1:String) : void
      {
         m_showSwfPath = param1;
      }
      
      private function get showClassName() : String
      {
         return m_showClassName;
      }
      
      private function set showClassName(param1:String) : void
      {
         m_showClassName = param1;
      }
      
      private function get baseAttack() : uint
      {
         return _antiwear.baseAttack;
      }
      
      private function set baseAttack(param1:uint) : void
      {
         _antiwear.baseAttack = param1;
      }
      
      private function get proAttack1() : Number
      {
         return _antiwear.proAttack1;
      }
      
      private function set proAttack1(param1:Number) : void
      {
         _antiwear.proAttack1 = param1;
      }
      
      private function get proAttack2() : Number
      {
         return _antiwear.proAttack2;
      }
      
      private function set proAttack2(param1:Number) : void
      {
         _antiwear.proAttack2 = param1;
      }
      
      private function get baseDefence() : uint
      {
         return _antiwear.baseDefence;
      }
      
      private function set baseDefence(param1:uint) : void
      {
         _antiwear.baseDefence = param1;
      }
      
      private function get proDefence1() : Number
      {
         return _antiwear.proDefence1;
      }
      
      private function set proDefence1(param1:Number) : void
      {
         _antiwear.proDefence1 = param1;
      }
      
      private function get proDefence2() : Number
      {
         return _antiwear.proDefence2;
      }
      
      private function set proDefence2(param1:Number) : void
      {
         _antiwear.proDefence2 = param1;
      }
      
      private function get baseHp() : uint
      {
         return _antiwear.baseHp;
      }
      
      private function set baseHp(param1:uint) : void
      {
         _antiwear.baseHp = param1;
      }
      
      private function get proHp1() : Number
      {
         return _antiwear.proHp1;
      }
      
      private function set proHp1(param1:Number) : void
      {
         _antiwear.proHp1 = param1;
      }
      
      private function get proHp2() : Number
      {
         return _antiwear.proHp2;
      }
      
      private function set proHp2(param1:Number) : void
      {
         _antiwear.proHp2 = param1;
      }
      
      private function get needPowerChipNum() : uint
      {
         return _antiwear.needPowerChipNum;
      }
      
      private function set needPowerChipNum(param1:uint) : void
      {
         _antiwear.needPowerChipNum = param1;
      }
      
      private function get needStrengthenNum() : uint
      {
         return _antiwear.needStrengthenNum;
      }
      
      private function set needStrengthenNum(param1:uint) : void
      {
         _antiwear.needStrengthenNum = param1;
      }
      
      private function get attackAddToPlayer() : uint
      {
         return _antiwear.attackAddToPlayer;
      }
      
      private function set attackAddToPlayer(param1:uint) : void
      {
         _antiwear.attackAddToPlayer = param1;
      }
      
      private function get defenceAddToPlayer() : uint
      {
         return _antiwear.defenceAddToPlayer;
      }
      
      private function set defenceAddToPlayer(param1:uint) : void
      {
         _antiwear.defenceAddToPlayer = param1;
      }
      
      private function get hpAddToPlayer() : uint
      {
         return _antiwear.hpAddToPlayer;
      }
      
      private function set hpAddToPlayer(param1:uint) : void
      {
         _antiwear.hpAddToPlayer = param1;
      }
      
      private function get isRide() : Boolean
      {
         return m_isRide;
      }
      
      private function set isRide(param1:Boolean) : void
      {
         m_isRide = param1;
      }
   }
}

