package UI.WorldBoss.ShakeView
{
   import UI.WorldBoss.View;
   
   public class ShakeView
   {
      protected var _isShake:<PERSON><PERSON><PERSON>;
      
      protected var _isMyShake:<PERSON><PERSON><PERSON>;
      
      protected var _view:View;
      
      public function ShakeView()
      {
         super();
      }
      
      public function shakeView(param1:View) : void
      {
         if(_isMyShake)
         {
            return;
         }
         _view = param1;
         _isShake = _view.getIsShake();
      }
      
      public function clear() : void
      {
         recover();
         _view = null;
      }
      
      protected function recover() : void
      {
         if(_isMyShake)
         {
            _view.setIsShake(false);
         }
         _isMyShake = false;
      }
      
      public function getIsShake() : Bo<PERSON>an
      {
         return _isShake;
      }
      
      public function getIsMyShake() : Bo<PERSON><PERSON>
      {
         return _isMyShake;
      }
   }
}

