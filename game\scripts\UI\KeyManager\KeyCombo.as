package UI.KeyManager
{
   import UI.MyMovieClip;
   import flash.events.Event;
   
   public class KeyCombo extends MyMovieClip
   {
      public static var COMBO_PRESSED:String = "combo_pressed";
      
      public static var COMBO_RELEASED:String = "combo_released";
      
      public static var COMBO_REPEAT:String = "combo_repeat";
      
      private var _keys:Array;
      
      private var _comboActivated:Boolean;
      
      private var _comboPressFun:Function;
      
      private var _comboPressFunParams:Array;
      
      private var _comboRepeatFun:Function;
      
      private var _comboRepeatFunParams:Array;
      
      private var _comboReleasedFun:Function;
      
      private var _comboReleasedFunParams:Array;
      
      public function KeyCombo()
      {
         super();
         _comboActivated = false;
         _keys = [];
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         if(_keys)
         {
            _loc1_ = int(_keys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _keys[_loc2_].clear();
               _keys[_loc2_] = null;
               _loc2_++;
            }
         }
         _keys = null;
         _comboPressFun = null;
         _comboPressFunParams = null;
         _comboRepeatFun = null;
         _comboRepeatFunParams = null;
         _comboReleasedFun = null;
         _comboReleasedFunParams = null;
      }
      
      public function addKey(param1:Key) : void
      {
         _keys.push(param1);
      }
      
      public function addComboKeyResponseFun(param1:Function, param2:Array, param3:Function, param4:Array, param5:Function, param6:Array) : void
      {
         _comboPressFun = param1;
         _comboPressFunParams = param2;
         _comboRepeatFun = param3;
         _comboRepeatFunParams = param4;
         _comboReleasedFun = param5;
         _comboReleasedFunParams = param6;
      }
      
      public function getComboActivated() : Boolean
      {
         return _comboActivated;
      }
      
      private function analyzeState() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Key = null;
         var _loc1_:Boolean = true;
         _loc3_ = 0;
         while(_loc3_ < _keys.length)
         {
            _loc2_ = _keys[_loc3_];
            if(!_loc2_.getIsDown())
            {
               _loc1_ = false;
               break;
            }
            _loc3_++;
         }
         if(_loc1_ && !_comboActivated)
         {
            _comboActivated = true;
            trace("激活键组：",_keys.toString());
            if(Boolean(_comboPressFun))
            {
               _comboPressFun.apply(null,_comboPressFunParams);
            }
            dispatchEvent(new Event(COMBO_PRESSED));
         }
         else if(_loc1_ && _comboActivated)
         {
            if(Boolean(_comboRepeatFun))
            {
               _comboRepeatFun.apply(null,_comboRepeatFunParams);
            }
            dispatchEvent(new Event(COMBO_REPEAT));
         }
         else if(!_loc1_ && _comboActivated)
         {
            _comboActivated = false;
            trace("释放键组：",_keys.toString());
            if(Boolean(_comboReleasedFun))
            {
               _comboReleasedFun.apply(null,_comboReleasedFunParams);
            }
            dispatchEvent(new Event(COMBO_RELEASED));
         }
      }
      
      public function keyDown(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:Key = null;
         var _loc4_:Boolean = false;
         _loc3_ = 0;
         while(_loc3_ < _keys.length)
         {
            _loc2_ = _keys[_loc3_];
            if(_loc2_.getKeyId() == param1)
            {
               _loc2_.setIsDown(true);
               _loc4_ = true;
               break;
            }
            _loc3_++;
         }
         if(_loc4_)
         {
            analyzeState();
         }
      }
      
      public function keyUp(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:Key = null;
         var _loc4_:Boolean = false;
         _loc3_ = 0;
         while(_loc3_ < _keys.length)
         {
            _loc2_ = _keys[_loc3_];
            if(_loc2_.getKeyId() == param1)
            {
               _loc2_.setIsDown(false);
               _loc4_ = true;
               break;
            }
            _loc3_++;
         }
         if(_loc4_)
         {
            analyzeState();
         }
      }
   }
}

