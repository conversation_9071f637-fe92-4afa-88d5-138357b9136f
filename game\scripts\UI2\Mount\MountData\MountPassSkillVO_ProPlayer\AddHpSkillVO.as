package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddHpSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addHp:uint;
      
      public function AddHpSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("blood_mountAdd1",m_targetPlayer.playerVO.get2("blood_mountAdd1") - m_addValue);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addHp;
         m_targetPlayer.playerVO.set2("blood_mountAdd1",m_targetPlayer.playerVO.get2("blood_mountAdd1") + m_addValue);
      }
      
      public function getAddHp() : uint
      {
         return addHp;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addHp = uint(param1.data.(@att == "addHp")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addHp = m_addHp;
      }
      
      private function get addHp() : uint
      {
         return _antiwear.addHp;
      }
      
      private function set addHp(param1:uint) : void
      {
         _antiwear.addHp = param1;
      }
   }
}

