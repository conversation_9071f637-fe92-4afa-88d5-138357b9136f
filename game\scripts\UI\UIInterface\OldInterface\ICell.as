package UI.UIInterface.OldInterface
{
   public interface ICell extends ISprite
   {
      function get isFocus() : Boolean;
      
      function get isHaveChild() : Boolean;
      
      function set isHaveChild(param1:Boolean) : void;
      
      function changeBorderColor(param1:uint) : void;
      
      function changeBackgroundColor(param1:uint) : void;
      
      function get cellBackground() : ICellBackground;
      
      function showBorder() : void;
      
      function hideBorder() : void;
      
      function clear() : void;
   }
}

