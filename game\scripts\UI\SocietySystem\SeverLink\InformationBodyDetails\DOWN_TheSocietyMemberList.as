package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.GamingUI;
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class DOWN_TheSocietyMemberList extends InformationBodyDetail
   {
      protected const m_const_validInterval:int = 60000;
      
      protected var m_memberTotalNum:int;
      
      protected var m_memberNum:int;
      
      protected var m_memberDatas:Vector.<MemberDataInMemberList>;
      
      protected var m_dataDeadTime:Number;
      
      protected var m_pageIndex:int;
      
      protected var m_num:int;
      
      public function DOWN_TheSocietyMemberList()
      {
         super();
         m_informationBodyId = 3009;
         m_dataDeadTime = 0;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_memberDatas);
         m_memberDatas = null;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:MemberDataInMemberList = null;
         super.initFromByteArray(param1);
         m_memberTotalNum = param1.readInt();
         m_memberNum = param1.readInt();
         trace("memberTotalNum:",m_memberTotalNum);
         trace("memberNum:",m_memberNum);
         if(m_memberNum)
         {
            _loc2_ = m_memberNum;
            m_memberDatas = new Vector.<MemberDataInMemberList>();
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc3_ = new MemberDataInMemberList();
               _loc3_.initFromByteArray(param1);
               m_memberDatas.push(_loc3_);
               _loc4_++;
            }
         }
         m_dataDeadTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + 60000;
      }
      
      public function initOther(param1:int, param2:int) : void
      {
         m_pageIndex = param1;
         m_num = param2;
      }
      
      public function initFromUPShowTheSocietyMemberList(param1:UP_ShowTheSocietyMemberList) : void
      {
         m_pageIndex = param1.getPageIndex();
         m_num = param1.getNum();
      }
      
      public function getMemberNum() : int
      {
         return !!m_memberDatas ? m_memberDatas.length : 0;
      }
      
      public function getMemberTotalNum() : int
      {
         return m_memberTotalNum;
      }
      
      public function getPageIndex() : int
      {
         return m_pageIndex;
      }
      
      public function getNum() : int
      {
         return m_num;
      }
      
      public function getMemberByIndex(param1:int) : MemberDataInMemberList
      {
         if(m_memberDatas == null || param1 < 0 || param1 > m_memberDatas.length - 1)
         {
            throw new Error("index:" + param1 + "出错了！");
         }
         return m_memberDatas[param1];
      }
      
      public function getDataDeadTime() : Number
      {
         return m_dataDeadTime;
      }
      
      private function sortByPersonTotalConValue(param1:MemberDataInMemberList, param2:MemberDataInMemberList) : int
      {
         if(param1.getPersonalTotalConValue() < param2.getPersonalTotalConValue())
         {
            return 1;
         }
         if(param1.getPersonalTotalConValue() > param2.getPersonalTotalConValue())
         {
            return -1;
         }
         return param2.getLevel_member() - param1.getLevel_member();
      }
   }
}

