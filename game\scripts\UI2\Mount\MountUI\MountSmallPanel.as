package UI2.Mount.MountUI
{
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.PlayerVO;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class MountSmallPanel extends MySprite
   {
      private const m_const_card_num:uint = 1;
      
      private var m_show:MovieClip;
      
      private var m_mountInforCardShow:MountInforCardShow;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_totalAddDefenceText:TextField;
      
      private var m_totalAddAttackText:TextField;
      
      private var m_totalAddHpText:TextField;
      
      private var m_mountInforCards:Vector.<MountInforCardShow>;
      
      private var m_mountVOs:Vector.<MountVO>;
      
      private var m_mountsVO:MountsVO;
      
      private var m_playerVO:PlayerVO;
      
      public function MountSmallPanel()
      {
         super();
         m_mountInforCardShow = new MountInforCardShow();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_mountInforCards = new Vector.<MountInforCardShow>();
         m_mountInforCards.push(m_mountInforCardShow);
         m_mountVOs = new Vector.<MountVO>();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_mountInforCardShow);
         m_mountInforCardShow = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         m_totalAddAttackText = null;
         m_totalAddDefenceText = null;
         m_totalAddHpText = null;
         ClearUtil.clearObject(m_mountInforCards);
         m_mountInforCards = null;
         ClearUtil.nullArr(m_mountVOs,false,false,false);
         m_mountVOs = null;
         m_mountsVO = null;
         m_playerVO = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("MountSmallPanel") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      public function setPlayerVO(param1:PlayerVO) : void
      {
         m_mountsVO = param1.player.getMountsVO();
         m_playerVO = param1;
         initShow2();
      }
      
      private function initShow() : void
      {
         m_mountInforCardShow.setShow(m_show["mountCard"]);
         m_mountInforCardShow.setVersionControl(Part1.getInstance().getVersionControl());
         m_mountInforCardShow.setProgressShow(Part1.getInstance().getLoadUI());
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_totalAddAttackText = m_show["totalAddAttackText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_totalAddAttackText);
         m_totalAddDefenceText = m_show["totalAddDefenceText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_totalAddDefenceText);
         m_totalAddHpText = m_show["totalAddHpText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_totalAddHpText);
      }
      
      private function initShow2() : void
      {
         var _loc5_:int = 0;
         var _loc3_:MountVO = null;
         var _loc1_:* = null;
         var _loc4_:* = null;
         if(m_mountsVO == null || m_playerVO == null)
         {
            return;
         }
         ClearUtil.nullArr(m_mountVOs,false,false,false);
         m_mountVOs.length = 0;
         var _loc2_:int = int(m_mountsVO.getMountVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = m_mountsVO.getMountVOByIndex(_loc5_);
            if(_loc3_.getPlayerVO() == null)
            {
               m_mountVOs.push(_loc3_);
            }
            else if(_loc3_.getPlayerVO() == m_playerVO)
            {
               _loc1_ = _loc3_;
            }
            else
            {
               _loc4_ = _loc3_;
            }
            _loc5_++;
         }
         if(_loc4_)
         {
            m_mountVOs.unshift(_loc4_);
         }
         if(_loc1_)
         {
            m_mountVOs.unshift(_loc1_);
         }
         setPageBtn(1);
         arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_mountInforCards.length);
         m_totalAddAttackText.text = m_mountsVO.getTotalAddAttackOfMountVOs().toString();
         m_totalAddDefenceText.text = m_mountsVO.getTotalAddDefenceOfMountVOs().toString();
         m_totalAddHpText.text = m_mountsVO.getTotalAddHpOfMountVOs().toString();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_pageBtnGroup === _loc2_)
         {
            arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_mountInforCards.length);
         }
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_mountsVO == null || m_mountsVO.getMountVONum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_mountsVO.getMountVONum() % m_mountInforCards.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_mountsVO.getMountVONum() / m_mountInforCards.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_mountsVO.getMountVONum() / m_mountInforCards.length) + 1);
         }
      }
      
      private function arrangeActiveTaskLineShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc4_:MountVO = null;
         var _loc5_:int = param1 + m_mountInforCards.length;
         var _loc2_:int = !!m_mountVOs ? m_mountVOs.length : 0;
         var _loc3_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc2_)
         {
            _loc4_ = m_mountVOs[_loc6_];
            m_mountInforCards[_loc3_].setMountVO(_loc4_);
            m_mountInforCards[_loc3_].getShow().visible = true;
            _loc3_++;
            _loc6_++;
         }
         while(_loc3_ < m_mountInforCards.length)
         {
            m_mountInforCards[_loc3_].getShow().visible = false;
            _loc3_++;
         }
      }
   }
}

