package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_GetLogicServerReturn extends InformationBodyDetail
   {
      private const m_host_length:int = 16;
      
      protected var m_host:String;
      
      protected var m_port:int;
      
      public function DOWN_GetLogicServerReturn()
      {
         super();
         m_informationBodyId = 3045;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         trace("获取逻辑服务器端口返回 byteArr。toString：",param1.toString());
         super.initFromByteArray(param1);
         m_host = param1.readUTFBytes(16);
         m_port = param1.readInt();
         trace("获取到逻辑服务器的host：" + m_host + "port：" + m_port);
      }
      
      public function getHost() : String
      {
         return m_host;
      }
      
      public function getPort() : int
      {
         return m_port;
      }
   }
}

