package UI.EquipEquipmentFuns
{
   public class EquipEqListener implements IEquipEqListener
   {
      public var getWarningTextFontSizeFun:Function;
      
      public var showWarningFun:Function;
      
      public var actionAfterUseFun:Function;
      
      public var actionAfterUnableUseFun:Function;
      
      public function EquipEqListener()
      {
         super();
      }
      
      public function clear() : void
      {
         getWarningTextFontSizeFun = null;
         showWarningFun = null;
         actionAfterUnableUseFun = null;
         actionAfterUseFun = null;
      }
      
      public function getWarningTextFontSize() : int
      {
         var _loc1_:int = 0;
         if(Bo<PERSON>an(getWarningTextFontSizeFun))
         {
            _loc1_ = getWarningTextFontSizeFun();
         }
         return _loc1_;
      }
      
      public function showWarning(param1:String, param2:int) : void
      {
         if(Boolean(showWarningFun))
         {
            showWarningFun(param1,param2);
         }
      }
      
      public function actionAfterUse() : void
      {
         if(<PERSON><PERSON>an(actionAfterUseFun))
         {
            actionAfterUseFun();
         }
         clear();
      }
      
      public function actionAfterUnableUse() : void
      {
         if(<PERSON><PERSON><PERSON>(actionAfterUnableUseFun))
         {
            actionAfterUnableUseFun();
         }
         clear();
      }
   }
}

