package UI.MainLineTask
{
   import GM_UI.GMData;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MyHaveLockSwitchBtnLogicShell;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MainLineTask.TaskDescription.TaskDescription1;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.Utils.LoadXML.LoadXMLListener1;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MainLineTaskPanel extends MySprite
   {
      private const TASK_LINE_NUM:int = 4;
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_taskLineSwitchBtns:SwitchBtnGroupLogicShell;
      
      private var m_taskLines:Vector.<TaskLine>;
      
      private var m_taskDetailShow:ScrollSpriteLogicShell;
      
      private var m_dataLayer:DataLayer_TaskPanel;
      
      private var m_rewardShow:RewardShow_TaskPanel;
      
      private var m_choiceGetRewardPlayerPanel:ChoiceGetRewardPlayerPanel;
      
      private var m_mainLineTaskDescriptionXML:XML;
      
      private var m_mainLineTaskData:MainLineTaskData;
      
      private var m_currentSelectTaskList:PhaseTaskList;
      
      private var m_currentTaskVO:MainLineTaskVO;
      
      private var m_wantLoadSources:Array = ["mainLineTaskPanel"];
      
      private var m_gamingUI:GamingUI;
      
      public function MainLineTaskPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("clickLockBtn",clickLockButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      public function init(param1:MainLineTaskData) : void
      {
         var loadFinishLitener:LoadFinishListener1;
         var mainLineTaskData:MainLineTaskData = param1;
         m_mainLineTaskData = mainLineTaskData;
         m_mainLineTaskData.gotoNextTaskList();
         if(m_gamingUI)
         {
            loadFinishLitener = new LoadFinishListener1(function():void
            {
               init2();
            },null);
            m_gamingUI.loadQueue.load(m_wantLoadSources,loadFinishLitener,"allShade");
         }
         else
         {
            init2();
         }
      }
      
      public function setMainLineTaskDescriptionXML_test(param1:XML) : void
      {
         m_mainLineTaskDescriptionXML = param1;
      }
      
      private function init2() : void
      {
         var _loc6_:int = 0;
         var _loc5_:TaskLine = null;
         var _loc3_:MyHaveLockSwitchBtnLogicShell = null;
         trace("start to load openHole.xml");
         if(m_show == null)
         {
            m_show = MyFunction2.returnShowByClassName("MainLineTaskPanel") as MovieClip;
         }
         addChild(m_show);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击退出");
         m_taskDetailShow = new ScrollSpriteLogicShell();
         m_taskDetailShow.setShow(m_show["taskDetailShow"]);
         m_taskDetailShow.closeMouseWheel();
         m_dataLayer = new DataLayer_TaskPanel();
         m_dataLayer.setShow(m_taskDetailShow.getDataLayer() as MovieClip);
         m_rewardShow = new RewardShow_TaskPanel();
         m_rewardShow.setShow(m_show["taskRewardShow"]);
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_taskLineSwitchBtns = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         m_taskLines = new Vector.<TaskLine>();
         var _loc4_:int = 4;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = new TaskLine();
            _loc5_.setShow(m_show["taskLine_" + (_loc6_ + 1)]);
            m_taskLines.push(_loc5_);
            m_taskLineSwitchBtns.addSwitchBtn(_loc5_);
            _loc6_++;
         }
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(MyHaveLockSwitchBtnLogicShell);
         _loc4_ = int(m_mainLineTaskData.phaseTaskLists.length);
         var _loc1_:int = int(m_mainLineTaskData.phaseTaskLists.indexOf(m_mainLineTaskData.currentPhaseTaskList));
         if(_loc1_ == -1)
         {
            throw new Error();
         }
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_ = new MyHaveLockSwitchBtnLogicShell();
            _loc3_.setShow(m_show["taskBtn_" + (_loc6_ + 1)]);
            _loc3_.data = m_mainLineTaskData.phaseTaskLists[_loc6_];
            if(_loc6_ > _loc1_)
            {
               _loc3_.lock();
            }
            m_switchBtnGroup.addSwitchBtn(_loc3_);
            _loc6_++;
         }
         m_switchBtnGroup.getSwitchBtnByIndex(_loc1_).turnActiveAndDispatchEvent();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("clickLockBtn",clickLockButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         if(m_gamingUI)
         {
            m_gamingUI.loadQueue.unLoad(m_wantLoadSources);
         }
         super.clear();
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_taskLineSwitchBtns);
         m_taskLineSwitchBtns;
         ClearUtil.nullArr(m_taskLines);
         m_taskLines = null;
         ClearUtil.clearObject(m_taskDetailShow);
         m_taskDetailShow = null;
         ClearUtil.clearObject(m_dataLayer);
         m_dataLayer = null;
         ClearUtil.clearObject(m_rewardShow);
         m_rewardShow = null;
         m_mainLineTaskDescriptionXML = null;
         m_mainLineTaskData = null;
         m_currentSelectTaskList = null;
         m_currentTaskVO = null;
         ClearUtil.nullArr(m_wantLoadSources);
         m_wantLoadSources = null;
         m_gamingUI = null;
         ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
         m_choiceGetRewardPlayerPanel = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function refreshPanel() : void
      {
         rewardShow(m_currentTaskVO);
         var _loc1_:MySwitchBtnLogicShell = m_taskLineSwitchBtns.currentActivateBtn() as MySwitchBtnLogicShell;
         if(_loc1_ == null)
         {
            throw new Error();
         }
         (_loc1_ as TaskLine).setTaskVO(null);
      }
      
      private function getTaskReward(param1:Player) : void
      {
         var currentBtn:MySwitchBtnLogicShell;
         var player:Player = param1;
         var getTaskRewardListener:GetRewardListener = new GetRewardListener();
         getTaskRewardListener.notChoiceEquipmentVOsFun = function():void
         {
            showWarningBox("请选择要获得的奖励！",0);
         };
         getTaskRewardListener.packageNotHaveSpaceFun = function():void
         {
            showWarningBox("背包空间不足！",0);
         };
         getTaskRewardListener.rewardHaveBeGotFun = function():void
         {
            showWarningBox("奖励已被领取！",0);
         };
         getTaskRewardListener.successGetRewardFun = function():void
         {
            var _loc1_:int = 0;
            var _loc2_:MySwitchBtnLogicShell = null;
            showWarningBox("奖励领取成功！",0);
            runTaskDetectorsInTaskList();
            if(m_mainLineTaskData.gotoNextTaskList())
            {
               _loc1_ = int(m_mainLineTaskData.phaseTaskLists.indexOf(m_mainLineTaskData.currentPhaseTaskList));
               _loc2_ = m_switchBtnGroup.getSwitchBtnByIndex(_loc1_) as MySwitchBtnLogicShell;
               if(_loc2_ as MyHaveLockSwitchBtnLogicShell)
               {
                  (_loc2_ as MyHaveLockSwitchBtnLogicShell).unLock();
               }
            }
         };
         m_currentTaskVO.getTaskReward(player,getTaskRewardListener);
         rewardShow(m_currentTaskVO);
         currentBtn = m_taskLineSwitchBtns.currentActivateBtn() as MySwitchBtnLogicShell;
         if(currentBtn == null)
         {
            throw new Error();
         }
         (currentBtn as TaskLine).setTaskVO(null);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:SwitchBtnLogicShell = null;
         loop2:
         switch(param1.button)
         {
            case m_pageBtnGroup:
               arrangeTaskLines((m_pageBtnGroup.pageNum - 1) * 4,m_currentSelectTaskList.taskVOs);
               break;
            case m_quitBtn:
               if(m_gamingUI)
               {
                  m_gamingUI.closeMainLineTaskPanel();
               }
               break;
            case m_rewardShow.getTaskRewardBtn:
               if(GamingUI.getInstance().player2)
               {
                  if(m_choiceGetRewardPlayerPanel == null)
                  {
                     m_choiceGetRewardPlayerPanel = new ChoiceGetRewardPlayerPanel();
                  }
                  m_choiceGetRewardPlayerPanel.x = (this.width - m_choiceGetRewardPlayerPanel.width) / 2;
                  m_choiceGetRewardPlayerPanel.y = (this.height - m_choiceGetRewardPlayerPanel.height) / 2;
                  addChild(m_choiceGetRewardPlayerPanel);
                  break;
               }
               getTaskReward(GamingUI.getInstance().player1);
               break;
            case m_taskDetailShow:
            case m_rewardShow.rewardScrollShow_H:
            case m_rewardShow.rewardScrollShow_L:
               switch(param1.button)
               {
                  case m_taskDetailShow:
                     m_taskDetailShow.openMouseWheel();
                     m_rewardShow.rewardScrollShow_H.closeMouseWheel();
                     m_rewardShow.rewardScrollShow_L.closeMouseWheel();
                     break loop2;
                  case m_rewardShow.rewardScrollShow_H:
                  case m_rewardShow.rewardScrollShow_L:
               }
               m_taskDetailShow.closeMouseWheel();
               m_rewardShow.rewardScrollShow_L.openMouseWheel();
               m_rewardShow.rewardScrollShow_H.closeMouseWheel();
         }
         if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn1)
         {
            getTaskReward(GamingUI.getInstance().player1);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
         else if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn2)
         {
            getTaskReward(GamingUI.getInstance().player2);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
         _loc3_ = !!m_switchBtnGroup ? m_switchBtnGroup.getSwitchBtnNum() : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = m_switchBtnGroup.getSwitchBtnByIndex(_loc4_) as SwitchBtnLogicShell;
            if(_loc2_ == param1.button)
            {
               clickSwitchBtn(_loc2_);
               return;
            }
            _loc4_++;
         }
         _loc3_ = !!m_taskLineSwitchBtns ? m_taskLineSwitchBtns.getSwitchBtnNum() : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = m_taskLineSwitchBtns.getSwitchBtnByIndex(_loc4_) as SwitchBtnLogicShell;
            if(_loc2_ == param1.button)
            {
               clickTaskLine(_loc2_ as TaskLine);
               return;
            }
            _loc4_++;
         }
      }
      
      private function clickLockButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:MySwitchBtnLogicShell = null;
         _loc3_ = m_switchBtnGroup.getSwitchBtnNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = m_switchBtnGroup.getSwitchBtnByIndex(_loc4_) as MySwitchBtnLogicShell;
            if(_loc2_ == param1.button)
            {
               showWarningBox(PhaseTaskList(_loc2_.data).description_NotReachLevel,0);
               return;
            }
            _loc4_++;
         }
      }
      
      private function clickSwitchBtn(param1:SwitchBtnLogicShell) : void
      {
         m_currentSelectTaskList = PhaseTaskList(param1.data);
         runTaskDetectorsInTaskList();
         setPageBtn(1,m_currentSelectTaskList.taskVOs);
         arrangeTaskLines((m_pageBtnGroup.pageNum - 1) * 4,m_currentSelectTaskList.taskVOs);
      }
      
      private function runTaskDetectorsInTaskList() : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:MainLineTaskVO = null;
         var _loc1_:int = int(m_currentSelectTaskList.taskVOs.length);
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc4_ = m_currentSelectTaskList.taskVOs[_loc5_];
            if((Boolean(_loc4_)) && Boolean(_loc4_.taskDetectors) && _loc4_.taskDetectors.length)
            {
               _loc2_ = int(_loc4_.taskDetectors.length);
               _loc3_ = 0;
               while(_loc3_ < _loc2_)
               {
                  _loc4_.taskDetectors[_loc3_].detect();
                  _loc3_++;
               }
            }
            _loc5_++;
         }
      }
      
      private function clickTaskLine(param1:TaskLine) : void
      {
         var taskVO:MainLineTaskVO;
         var taskLine:TaskLine = param1;
         ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
         m_choiceGetRewardPlayerPanel = null;
         taskVO = taskLine.getTaskVO();
         m_currentTaskVO = taskVO;
         if(m_mainLineTaskDescriptionXML == null)
         {
            MyFunction2.loadXMLFunction("mainLineTask/mainLineTaskDescription",function(param1:XML):void
            {
               m_mainLineTaskDescriptionXML = param1;
               taskVO.initTaskDescription(m_mainLineTaskDescriptionXML);
               showDescription(taskVO);
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         }
         else
         {
            taskVO.initTaskDescription(m_mainLineTaskDescriptionXML);
            showDescription(taskVO);
         }
      }
      
      private function showDescription(param1:MainLineTaskVO) : void
      {
         var taskDescription1:TaskDescription1;
         var loadXMLListener:LoadXMLListener1;
         var taskVO:MainLineTaskVO = param1;
         if(taskVO.description is TaskDescription1 && GMData.getInstance().isGMApplication == false)
         {
            taskDescription1 = taskVO.description as TaskDescription1;
            if(taskDescription1.dropOutEquipmentVOs_boss == null || taskDescription1.dropOutEquipmentVOs_enemy == null)
            {
               loadXMLListener = new LoadXMLListener1();
               loadXMLListener.loadSuccessFun = function(param1:XML):void
               {
                  m_dataLayer.setTaskVO(taskVO);
                  m_taskDetailShow.refresh();
                  rewardShow(taskVO);
               };
               taskDescription1.loadXML.addLoadXMLListener(loadXMLListener);
            }
            else
            {
               m_dataLayer.setTaskVO(taskVO);
               m_taskDetailShow.refresh();
               rewardShow(taskVO);
            }
         }
         else
         {
            m_dataLayer.setTaskVO(taskVO);
            m_taskDetailShow.refresh();
            rewardShow(taskVO);
         }
      }
      
      private function rewardShow(param1:MainLineTaskVO) : void
      {
         m_rewardShow.showReward(param1);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChild(WarningBoxSingle.getInstance());
      }
      
      private function setPageBtn(param1:int, param2:Vector.<MainLineTaskVO>) : void
      {
         if(param2 == null || param2.length == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(param2.length % 4 == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,param2.length / 4);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(param2.length / 4) + 1);
         }
      }
      
      private function arrangeTaskLines(param1:int, param2:Vector.<MainLineTaskVO>) : void
      {
         var _loc7_:* = 0;
         var _loc6_:MainLineTaskVO = null;
         var _loc5_:int = param1 + 4;
         var _loc3_:int = !!param2 ? param2.length : 0;
         var _loc4_:int = 0;
         _loc7_ = param1;
         while(_loc7_ < _loc5_ && _loc7_ < _loc3_)
         {
            _loc6_ = param2[_loc7_];
            m_taskLines[_loc4_].getShow().visible = true;
            m_taskLines[_loc4_].setTaskVO(_loc6_);
            _loc4_++;
            _loc7_++;
         }
         while(_loc4_ < 4)
         {
            m_taskLines[_loc4_].getShow().visible = false;
            _loc4_++;
         }
         m_taskLineSwitchBtns.addEnd();
      }
      
      public function getCurrentTaskVO() : MainLineTaskVO
      {
         return m_currentTaskVO;
      }
   }
}

class GetRewardListener implements IGetTaskRewardListener
{
   public var successGetRewardFun:Function;
   
   public var taskNotFinishFun:Function;
   
   public var rewardHaveBeGotFun:Function;
   
   public var packageNotHaveSpaceFun:Function;
   
   public var notChoiceEquipmentVOsFun:Function;
   
   public function GetRewardListener()
   {
      super();
   }
   
   public function clear() : void
   {
      successGetRewardFun = null;
      taskNotFinishFun = null;
      rewardHaveBeGotFun = null;
      packageNotHaveSpaceFun = null;
      notChoiceEquipmentVOsFun = null;
   }
   
   public function successGetReward() : void
   {
      if(successGetRewardFun)
      {
         successGetRewardFun();
      }
      clear();
   }
   
   public function taskNotFinish() : void
   {
      if(taskNotFinishFun)
      {
         taskNotFinishFun();
      }
      clear();
   }
   
   public function rewardHaveBeGot() : void
   {
      if(rewardHaveBeGotFun)
      {
         rewardHaveBeGotFun();
      }
      clear();
   }
   
   public function packageNotHaveSpace() : void
   {
      if(packageNotHaveSpaceFun)
      {
         packageNotHaveSpaceFun();
      }
      clear();
   }
   
   public function notChoiceEquipmentVOs() : void
   {
      if(notChoiceEquipmentVOsFun)
      {
         notChoiceEquipmentVOsFun();
      }
      clear();
   }
}
