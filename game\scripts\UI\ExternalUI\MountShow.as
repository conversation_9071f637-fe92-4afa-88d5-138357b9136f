package UI.ExternalUI
{
   import UI2.Mount.MountData.MountVO;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MountShow
   {
      private var m_show:MovieClip;
      
      private var m_headShow:MovieClipPlayLogicShell;
      
      private var m_mountVO:MountVO;
      
      public function MountShow()
      {
         super();
         m_headShow = new MovieClipPlayLogicShell();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_headShow);
         m_headShow = null;
         m_show = null;
         m_mountVO = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function setMountVO(param1:MountVO) : void
      {
         m_mountVO = param1;
         if(m_mountVO)
         {
            m_headShow.gotoAndStop(m_mountVO.getId());
         }
      }
      
      private function initShow() : void
      {
         m_headShow.setShow(m_show["headShow"]);
      }
   }
}

