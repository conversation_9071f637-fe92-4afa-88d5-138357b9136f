package UI.InitPlayerData
{
   public class InitCompleteListener2 implements IInitCompleteListener
   {
      public var initCompleteFun:Function;
      
      public function InitCompleteListener2()
      {
         super();
      }
      
      public function clear() : void
      {
         initCompleteFun = null;
      }
      
      public function initComplete() : void
      {
         if(<PERSON><PERSON>an(initCompleteFun))
         {
            initCompleteFun();
         }
      }
   }
}

