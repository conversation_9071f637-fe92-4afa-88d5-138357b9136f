package UI2.firstPay
{
   import UI.DataManagerParent;
   
   public class FirstPayData extends DataManagerParent
   {
      private static var _instance:FirstPayData;
      
      private var _isGetOnce:Boolean = false;
      
      private var _isGetAwardToday:Boolean = false;
      
      private var _everydayTimeStr:String = "";
      
      private var _isUseLongwang:Boolean = false;
      
      private var _isUseDuowen:Boolean = false;
      
      private var _isUseDabai:Boolean = false;
      
      public function FirstPayData()
      {
         super();
      }
      
      public static function getInstance() : FirstPayData
      {
         if(!_instance)
         {
            _instance = new FirstPayData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         _instance = null;
      }
      
      public function initFromSaveXML(param1:XML, param2:String) : void
      {
         this.isGetOnce = false;
         if(param1.hasOwnProperty("firstPay") == false)
         {
            return;
         }
         var _loc3_:XML = param1.firstPay[0];
         if(_loc3_)
         {
            this.isGetOnce = Boolean(int(_loc3_.@isGetOnce));
            this.isGetAwardToday = Boolean(int(_loc3_.@isGetAwardToday));
            this.isUseLongwang = Boolean(int(_loc3_.@isUseLongwang));
            this.isUseDuowen = Boolean(int(_loc3_.@isUseDuowen));
            this.isUseDabai = Boolean(int(_loc3_.@isUseDabai));
            this.everydayTimeStr = String(_loc3_.@everydayTimeStr);
         }
         if(param2.split(" ")[0] != this.everydayTimeStr.split(" ")[0])
         {
            this.isGetAwardToday = false;
            this.everydayTimeStr = param2;
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <firstPay />;
         _loc1_.@isGetOnce = int(isGetOnce);
         _loc1_.@isGetAwardToday = int(isGetAwardToday);
         _loc1_.@isUseLongwang = int(isUseLongwang);
         _loc1_.@isUseDuowen = int(isUseDuowen);
         _loc1_.@isUseDabai = int(isUseDabai);
         _loc1_.@everydayTimeStr = everydayTimeStr;
         return _loc1_;
      }
      
      override protected function init() : void
      {
         super.init();
         isGetOnce = false;
         everydayTimeStr = "";
         isGetAwardToday = false;
         isUseLongwang = false;
         isUseDuowen = false;
         isUseDabai = false;
      }
      
      public function get isGetOnce() : Boolean
      {
         return _antiwear._isGetOnce;
      }
      
      public function set isGetOnce(param1:Boolean) : void
      {
         _antiwear._isGetOnce = param1;
      }
      
      public function get everydayTimeStr() : String
      {
         return _antiwear._everydayTimeStr;
      }
      
      public function set everydayTimeStr(param1:String) : void
      {
         _antiwear._everydayTimeStr = param1;
      }
      
      public function get isGetAwardToday() : Boolean
      {
         return _antiwear._isGetAwardToday;
      }
      
      public function set isGetAwardToday(param1:Boolean) : void
      {
         _antiwear._isGetAwardToday = param1;
      }
      
      public function get isUseLongwang() : Boolean
      {
         return _antiwear._isUseLongwang;
      }
      
      public function set isUseLongwang(param1:Boolean) : void
      {
         _antiwear._isUseLongwang = param1;
      }
      
      public function get isUseDuowen() : Boolean
      {
         return _antiwear._isUseDuowen;
      }
      
      public function set isUseDuowen(param1:Boolean) : void
      {
         _antiwear._isUseDuowen = param1;
      }
      
      public function get isUseDabai() : Boolean
      {
         return _antiwear._isUseDabai;
      }
      
      public function set isUseDabai(param1:Boolean) : void
      {
         _antiwear._isUseDabai = param1;
      }
   }
}

