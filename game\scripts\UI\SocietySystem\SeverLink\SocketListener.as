package UI.SocietySystem.SeverLink
{
   public class SocketListener implements ISocketListener
   {
      public var getSocketDataFun:Function;
      
      public var closeFun:Function;
      
      public var connectCompleteFun:Function;
      
      public var ioErrorFun:Function;
      
      public var securityErrorFun:Function;
      
      public function SocketListener()
      {
         super();
      }
      
      public function clear() : void
      {
         getSocketDataFun = null;
         closeFun = null;
         connectCompleteFun = null;
         ioErrorFun = null;
         securityErrorFun = null;
      }
      
      public function getSocketData(param1:InformationBody) : void
      {
         if(<PERSON><PERSON>an(getSocketDataFun))
         {
            getSocketDataFun(param1);
         }
      }
      
      public function close() : void
      {
         if(Bo<PERSON>an(closeFun))
         {
            closeFun();
         }
      }
      
      public function connectComplete() : void
      {
         if(Boolean(connectCompleteFun))
         {
            connectCompleteFun();
         }
      }
      
      public function ioError(param1:String) : void
      {
         if(<PERSON><PERSON><PERSON>(ioErrorFun))
         {
            ioErrorFun(param1);
         }
      }
      
      public function securityError(param1:String) : void
      {
         if(<PERSON><PERSON>an(securityErrorFun))
         {
            securityErrorFun(param1);
         }
      }
   }
}

