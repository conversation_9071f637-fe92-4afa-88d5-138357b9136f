package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class MemberDataInMemberList
   {
      protected var m_uid_member:Number;
      
      protected var m_idx_member:int;
      
      protected var m_nameLength_member:int;
      
      protected var m_name_member:String;
      
      protected var m_level_member:int;
      
      protected var m_isOnLine_member:int;
      
      protected var m_personalReConValue:int;
      
      protected var m_personalTotalConValue:int;
      
      protected var m_rankByPersonalTotalConValue:int;
      
      public function MemberDataInMemberList()
      {
         super();
      }
      
      public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid_member = _loc2_.uid;
         m_idx_member = _loc2_.idx;
         m_nameLength_member = param1.readInt();
         if(m_nameLength_member)
         {
            m_name_member = param1.readUTFBytes(m_nameLength_member);
         }
         m_level_member = param1.readInt();
         m_isOnLine_member = param1.readInt();
         m_personalTotalConValue = param1.readInt();
         m_personalReConValue = param1.readInt();
         m_rankByPersonalTotalConValue = param1.readInt();
         trace("m_uid_member:",m_uid_member);
         trace("m_idx_member:",m_idx_member);
         trace("m_level_member:",m_level_member);
         trace("m_personalReConValue:",m_personalReConValue);
         trace("m_personalTotalConValue:",m_personalTotalConValue);
         trace("m_rankByPersonalTotalConValue:",m_rankByPersonalTotalConValue);
      }
      
      public function getUid_member() : Number
      {
         return m_uid_member;
      }
      
      public function getIdx_member() : int
      {
         return m_idx_member;
      }
      
      public function getNameLength_member() : int
      {
         return m_nameLength_member;
      }
      
      public function getName_member() : String
      {
         return m_name_member;
      }
      
      public function getLevel_member() : int
      {
         return m_level_member;
      }
      
      public function getIsOnLineMember() : int
      {
         return m_isOnLine_member;
      }
      
      public function getPersonalReConValue() : int
      {
         return m_personalReConValue;
      }
      
      public function getPersonalTotalConValue() : int
      {
         return m_personalTotalConValue;
      }
      
      public function getRankByPersonalTotalConValue() : int
      {
         return m_rankByPersonalTotalConValue;
      }
   }
}

