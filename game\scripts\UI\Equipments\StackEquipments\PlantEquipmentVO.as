package UI.Equipments.StackEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class PlantEquipmentVO extends StackEquipmentVO
   {
      public function PlantEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:PlantEquipmentVO = new PlantEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
      }
   }
}

