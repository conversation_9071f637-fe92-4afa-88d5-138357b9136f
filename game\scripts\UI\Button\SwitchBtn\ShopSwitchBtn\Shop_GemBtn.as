package UI.Button.SwitchBtn.ShopSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Shop_GemBtn extends SwitchBtn
   {
      public var btnName:String;
      
      public function Shop_GemBtn()
      {
         super();
         setTipString("宝石");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopSwitchBtn"));
      }
   }
}

