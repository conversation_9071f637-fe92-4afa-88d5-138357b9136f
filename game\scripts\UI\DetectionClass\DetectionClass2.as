package UI.DetectionClass
{
   import UI.CheatData.CheatData;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import YJFY.Part1;
   
   public class DetectionClass2
   {
      private static var _instance:DetectionClass2 = null;
      
      public function DetectionClass2()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗！？");
      }
      
      public static function getInstance() : DetectionClass2
      {
         if(!_instance)
         {
            _instance = new DetectionClass2();
         }
         return _instance;
      }
      
      public function detectionEquipmentVOsQuoteIsSame(param1:Vector.<EquipmentVO>) : void
      {
         // 装备引用检测已禁用 - Equipment reference detection disabled
         return;
      }
   }
}

