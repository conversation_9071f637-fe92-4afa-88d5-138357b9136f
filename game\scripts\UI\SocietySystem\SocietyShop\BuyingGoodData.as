package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   
   public class BuyingGoodData extends DataManagerParent
   {
      private var m_goodData:GoodData;
      
      private var m_shopLevel:int;
      
      private var m_buyNum:int;
      
      private var m_extra:Object;
      
      public function BuyingGoodData(param1:GoodData, param2:int, param3:int, param4:Object = null)
      {
         super();
         m_goodData = param1;
         this.shopLevel = param3;
         this.buyNum = param2;
         m_extra = param4;
      }
      
      override public function clear() : void
      {
         super.clear();
         m_goodData = null;
         m_extra = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.buyNum = m_buyNum;
         _antiwear.shopLevel = m_shopLevel;
      }
      
      public function getGoodData() : GoodData
      {
         return m_goodData;
      }
      
      public function getBuyNum() : int
      {
         return buyNum;
      }
      
      public function getShopLevel() : int
      {
         return shopLevel;
      }
      
      public function getExtra() : Object
      {
         return m_extra;
      }
      
      private function get buyNum() : int
      {
         return _antiwear.buyNum;
      }
      
      private function set buyNum(param1:int) : void
      {
         _antiwear.buyNum = param1;
      }
      
      private function get shopLevel() : int
      {
         return _antiwear.shopLevel;
      }
      
      private function set shopLevel(param1:int) : void
      {
         _antiwear.shopLevel = param1;
      }
   }
}

