package UI.SocietySystem.SocialChatPanel
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.RichTextArea.IconData.IconData;
   import YJFY.RichTextArea.IconData.IconData_Gif;
   import YJFY.RichTextArea.IconData.IconData_Jpg;
   import YJFY.RichTextArea.IconData.IconData_MovieClip;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Rectangle;
   import flash.net.URLRequest;
   import org.bytearray.gif.player.GIFPlayer;
   
   public class FaceIconsPanel extends MySprite
   {
      private const m_const_movieClipFaceWidth:Number = 60;
      
      private const m_const_movieClipFaceHeigth:Number = 60;
      
      private var m_faceIconContainers:Vector.<ButtonLogicShell2>;
      
      private var m_imageLoaders:Vector.<Loader>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_onePageFaceIconNum:int;
      
      private var m_faceIconSprites:Vector.<Sprite>;
      
      private var m_show:MovieClip;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_iconDatas:Vector.<IconData>;
      
      private var m_societyChatPanel:SocietyChatPanel;
      
      public function FaceIconsPanel()
      {
         super();
         m_faceIconContainers = new Vector.<ButtonLogicShell2>();
         m_faceIconSprites = new Vector.<Sprite>();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("addedToStage",addToStage,false);
         super.clear();
         ClearUtil.clearObject(m_faceIconContainers);
         m_faceIconContainers = null;
         ClearUtil.clearObject(m_faceIconSprites);
         m_faceIconSprites = null;
         ClearUtil.clearObject(m_imageLoaders);
         m_imageLoaders = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         if(m_show && m_show.parent)
         {
            m_show.parent.removeChild(m_show);
         }
         m_show = null;
         m_myLoader = null;
         m_iconDatas = null;
         m_societyChatPanel = null;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setIconDatas(param1:Vector.<IconData>) : void
      {
         m_iconDatas = param1;
      }
      
      public function setSocietyChatPanel(param1:SocietyChatPanel) : void
      {
         m_societyChatPanel = param1;
      }
      
      public function initShow(param1:String, param2:String) : void
      {
         if(m_show == null)
         {
            m_myLoader.getClass(param1,param2,getPanelShowSuccess,getFailFun);
            m_myLoader.load();
         }
         else
         {
            initShow2();
         }
      }
      
      private function initShow2() : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:ButtonLogicShell2 = null;
         var _loc4_:DisplayObject = null;
         _loc3_ = m_show.numChildren;
         m_onePageFaceIconNum = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = m_show.getChildAt(_loc5_);
            if(_loc4_.name.substr(0,13) == "faceContainer")
            {
               m_onePageFaceIconNum++;
            }
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < m_onePageFaceIconNum)
         {
            _loc1_ = new ButtonLogicShell2();
            _loc1_.setShow(m_show["faceContainer" + (_loc5_ + 1)]);
            m_faceIconContainers.push(_loc1_);
            _loc5_++;
         }
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         setPageBtn(1,m_iconDatas.length);
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_onePageFaceIconNum);
      }
      
      private function setPageBtn(param1:int, param2:int) : void
      {
         if(param2 == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(param2 % m_onePageFaceIconNum == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,param2 / m_onePageFaceIconNum);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(param2 / m_onePageFaceIconNum) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc7_:* = 0;
         var _loc5_:int = 0;
         var _loc2_:ButtonLogicShell2 = null;
         var _loc3_:MySprite = null;
         var _loc6_:int = param1 + m_onePageFaceIconNum;
         var _loc4_:int = int(m_iconDatas.length);
         ClearUtil.clearObject(m_faceIconSprites);
         _loc7_ = param1;
         while(_loc7_ < _loc6_ && _loc7_ < _loc4_)
         {
            _loc2_ = m_faceIconContainers[_loc5_];
            _loc2_.getShow().visible = true;
            _loc2_.setTipString(m_iconDatas[_loc7_].getIconName());
            _loc2_.extraData = m_iconDatas[_loc7_];
            _loc3_ = new MySprite();
            m_faceIconSprites.push(_loc3_);
            (_loc2_.getShow() as DisplayObjectContainer).addChild(_loc3_);
            if(m_iconDatas[_loc7_] is IconData_MovieClip)
            {
               addMovieClip(m_iconDatas[_loc7_] as IconData_MovieClip,_loc3_,refreshShow);
            }
            else if(m_iconDatas[_loc7_] is IconData_Jpg)
            {
               addJpg(m_iconDatas[_loc7_] as IconData_Jpg,_loc3_,refreshShow);
            }
            else
            {
               if(!(m_iconDatas[_loc7_] is IconData_Gif))
               {
                  throw new Error("图标类型出错。");
               }
               addGif(m_iconDatas[_loc7_] as IconData_Gif,_loc3_,refreshShow);
            }
            _loc5_++;
            _loc7_++;
         }
         while(_loc5_ < m_onePageFaceIconNum)
         {
            m_faceIconContainers[_loc5_].getShow().visible = false;
            m_faceIconContainers[_loc5_].extraData = null;
            _loc5_++;
         }
      }
      
      private function refreshShow() : void
      {
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         stage.addEventListener("click",onClick,true,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         stage.removeEventListener("click",onClick,true);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function getPanelShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         if(stage)
         {
            m_show.x = (stage.stageWidth - m_show.width) / 2;
            m_show.y = (stage.stageHeight - m_show.height) / 2;
         }
         initShow2();
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("加载失败");
      }
      
      private function onClick(param1:Event) : void
      {
         if(!MyFunction2.judgeParentsIsTheObject(param1.target as DisplayObject,this,GamingUI.getInstance()))
         {
            m_societyChatPanel.closeFaceIconsPanel();
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc4_:* = param1.button;
         if(m_pageBtnGroup !== _loc4_)
         {
            var _loc2_:int = !!m_faceIconContainers ? m_faceIconContainers.length : 0;
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(param1.button == m_faceIconContainers[_loc3_])
               {
                  m_societyChatPanel.insertIconData(m_faceIconContainers[_loc3_].extraData as IconData);
                  m_societyChatPanel.closeFaceIconsPanel();
                  return;
               }
               _loc3_++;
            }
            return;
         }
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_onePageFaceIconNum);
      }
      
      private function addMovieClip(param1:IconData_MovieClip, param2:MySprite, param3:Function) : void
      {
         var iconData_movieClip:IconData_MovieClip = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         if(m_myLoader == null)
         {
            trace("loader is null, can not load movieclip");
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         }
         else
         {
            m_myLoader.getClass(iconData_movieClip.getSwfPath(),iconData_movieClip.getClassName(),function(param1:YJFYLoaderData):void
            {
               var _loc2_:Class = param1.resultClass;
               var _loc3_:MovieClip = new _loc2_();
               existIconShowContainer.addChild(_loc3_);
               if(Boolean(completeFun))
               {
                  completeFun();
               }
            },function(param1:YJFYLoaderData):void
            {
               drawErrGraphics(existIconShowContainer);
               if(Boolean(completeFun))
               {
                  completeFun();
               }
            });
            m_myLoader.load();
         }
      }
      
      private function addJpg(param1:IconData_Jpg, param2:MySprite, param3:Function) : void
      {
         var onComplete:Function;
         var onError:Function;
         var iconData_jpg:IconData_Jpg = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         var imgLoader:Loader = new Loader();
         existIconShowContainer.addChild(imgLoader);
         m_imageLoaders.push(imgLoader);
         imgLoader.load(new URLRequest(iconData_jpg.getUrl()));
         imgLoader.contentLoaderInfo.addEventListener("complete",onComplete);
         imgLoader.contentLoaderInfo.addEventListener("ioError",onError);
         onComplete = function(param1:Event):void
         {
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         onError = function(param1:Event):void
         {
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
      }
      
      private function addGif(param1:IconData_Gif, param2:MySprite, param3:Function) : void
      {
         var onComplete:Function;
         var onError:Function;
         var iconData_gif:IconData_Gif = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         var gifPlayer:GIFPlayer = new GIFPlayer();
         existIconShowContainer.addChild(gifPlayer);
         onComplete = function(param1:Object):void
         {
            var _loc2_:Rectangle = param1.rect;
            gifPlayer.play();
            trace("gifPlayer totalFrames:",gifPlayer.totalFrames);
            drawRectGraphics(existIconShowContainer,_loc2_.width,_loc2_.height,false,0);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         onError = function(param1:Event):void
         {
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         gifPlayer.addEventListener("complete",onComplete);
         gifPlayer.addEventListener("ioError",onError);
         gifPlayer.load(new URLRequest(iconData_gif.getUrl()));
      }
      
      private function drawRectGraphics(param1:Sprite, param2:int = 10, param3:int = 10, param4:Boolean = false, param5:int = 1) : void
      {
         if(param4)
         {
            param1.graphics.clear();
         }
         param1.graphics.beginFill(0,param5);
         param1.graphics.drawRect(0,0,param2,param3);
         param1.graphics.endFill();
      }
      
      private function drawErrGraphics(param1:Sprite) : void
      {
         param1.graphics.clear();
         param1.graphics.lineStyle(1,16711680);
         param1.graphics.beginFill(16777215);
         param1.graphics.drawRect(0,0,10,10);
         param1.graphics.moveTo(0,0);
         param1.graphics.lineTo(10,10);
         param1.graphics.moveTo(0,10);
         param1.graphics.lineTo(10,0);
         param1.graphics.endFill();
      }
   }
}

