package UI2.OnePlayerToTwo
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class OnePlayerToTwoExplain extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_resetPlayerTypePanel:OnePlayerToTwoPanel;
      
      public function OnePlayerToTwoExplain()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_resetPlayerTypePanel = null;
         super.clear();
      }
      
      public function init(param1:OnePlayerToTwoPanel) : void
      {
         m_resetPlayerTypePanel = param1;
         m_show = MyFunction2.returnShowByClassName("NewRoleExplanation") as MovieClip;
         addChild(m_show);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击关闭");
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_resetPlayerTypePanel.closeExplainPanel();
         }
      }
   }
}

