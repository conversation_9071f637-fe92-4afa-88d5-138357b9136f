package UI.ExchangeEquipment
{
   import UI.Buff.Buff.BuffDrive;
   import UI.Button.QuitBtn;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.PocketEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.ExportUI;
   import UI.GamingUI;
   import UI.InitPlayerData.InitCompleteListener2;
   import UI.InitPlayerData.InitPlayersData;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.SmallPackage.SmallPackage;
   import UI.TextTrace.traceText;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.API_4399.SaveAPI.SaveAPIListener2;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.EntityShowContainer;
   import YJFY.GameData;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   
   public class ExchangeWorld extends MySprite
   {
      private var m_const_saveMaxLimitTime:uint = 4000;
      
      public var quitBtn:QuitBtn;
      
      public var container1:Sprite;
      
      public var container2:Sprite;
      
      public var container3:Sprite;
      
      public var container4:Sprite;
      
      public var container5:Sprite;
      
      public var container6:Sprite;
      
      private var _currentIdx:uint;
      
      private var _clockTarget:Boolean;
      
      private var _package:SmallPackage;
      
      private var _containers:Vector.<Sprite>;
      
      private var _nextSendTime:Number = 0;
      
      private var _exechangeEquipmentXML:XML;
      
      private var _firstSaveLasttime:int;
      
      private var _saveApiListener:SaveAPIListener2;
      
      private var m_initPlayersDatas:Vector.<InitPlayersData> = new Vector.<InitPlayersData>();
      
      private var m_initcompleteListener:InitCompleteListener2;
      
      private var m_currentInitPlayersData:InitPlayersData;
      
      public function ExchangeWorld()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this,true,false);
         if(parent)
         {
            parent.removeChild(this);
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         var _loc1_:int = !!_containers ? _containers.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            ClearUtil.clearDisplayObjectInContainer(_containers[_loc2_],false,false);
            TweenLite.killTweensOf(_containers[_loc2_]);
            _containers[_loc2_] = null;
            _loc2_++;
         }
         container1 = null;
         container2 = null;
         container3 = null;
         container4 = null;
         container5 = null;
         container6 = null;
         _containers = null;
         _package = null;
         _exechangeEquipmentXML = null;
         ClearUtil.clearObject(m_initPlayersDatas);
         m_initPlayersDatas = null;
         ClearUtil.clearObject(m_initcompleteListener);
         m_initcompleteListener = null;
         m_currentInitPlayersData = null;
         ExEPlayerData.getInstance().exist();
      }
      
      private function addToStage(param1:Event) : void
      {
         var _loc3_:int = 0;
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         var _loc2_:int = int(_containers.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _containers[_loc3_].addEventListener("click",onClick,false,0,true);
            _containers[_loc3_].addEventListener("mouseOver",onOver,false,0,true);
            _containers[_loc3_].addEventListener("mouseOut",onOut,false,0,true);
            _loc3_++;
         }
         addEventListener("clickSendBtn",sendEquipment,true,0,true);
         addEventListener("warningBox",clickOk,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         var _loc3_:int = 0;
         removeEventListener("removedFromStage",removeFromStage,false);
         var _loc2_:int = int(_containers.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _containers[_loc3_].removeEventListener("click",onClick,false);
            _containers[_loc3_].removeEventListener("mouseOver",onOver,false);
            _containers[_loc3_].removeEventListener("mouseOut",onOut,false);
            _loc3_++;
         }
         removeEventListener("clickSendBtn",sendEquipment,true);
         removeEventListener("warningBox",clickOk,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc4_:Sprite = param1.currentTarget as Sprite;
         var _loc2_:Array = _loc4_.filters;
         var _loc3_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(_loc2_[_loc5_] is GlowFilter)
            {
               _loc2_.splice(_loc5_,1);
               _loc5_--;
               _loc3_--;
            }
            _loc5_++;
         }
         _loc2_.push(new GlowFilter(16777215,1,42,42,1,1));
         _loc4_.filters = _loc2_;
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc4_:Sprite = param1.currentTarget as Sprite;
         var _loc2_:Array = _loc4_.filters;
         var _loc3_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(_loc2_[_loc5_] is GlowFilter)
            {
               _loc2_.splice(_loc5_,1);
               _loc5_--;
               _loc3_--;
            }
            _loc5_++;
         }
         _loc4_.filters = _loc2_;
      }
      
      public function sendEquipment(param1:UIBtnEvent = null, param2:IEquipmentCell = null) : void
      {
         var exePlayerData:ExEPlayerData;
         var cdTime:Number;
         var equipmentCell:IEquipmentCell;
         var position:int;
         var equipmentVO1:EquipmentVO;
         var saveinfo:SaveTaskInfo;
         var oldPlayerEquipmentVOs:Vector.<EquipmentVO>;
         var isSuccess:Boolean;
         var buyExeNumBox:BuyExeNumBox;
         var world:ExchangeWorld;
         var saveinfo1:SaveTaskInfo;
         var fail:*;
         var success:*;
         var saveinfo2:SaveTaskInfo;
         var e:UIBtnEvent = param1;
         var cell:IEquipmentCell = param2;
         var targetPlayer:Player = ExEPlayerData.getInstance().myPlayerss[_currentIdx][0];
         if(targetPlayer == null)
         {
            exePlayerData = ExEPlayerData.getInstance();
            showWarningBox("出错了, 请关闭传送界面，重新打开",0);
            return;
         }
         if(e)
         {
            equipmentCell = e.target.parent.parent;
         }
         else
         {
            equipmentCell = cell;
         }
         if(_exechangeEquipmentXML == null)
         {
            showWarningBox("配置加载失败！请关掉界面，重新进入！",0);
            return;
         }
         if(_nextSendTime > new Date().getTime())
         {
            showWarningBox("休息下，" + Math.ceil((_nextSendTime - new Date().getTime()) / 1000) + "秒后才能传送 !",0);
            return;
         }
         clockTarget = true;
         clock2(true);
         position = int(SmallPackage.getInstance().currentSmallPackage.area.equipmentCells.indexOf(equipmentCell));
         equipmentVO1 = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(position);
         if(equipmentVO1 == null)
         {
            clockTarget = false;
            clock2(false);
            return;
         }
         if(equipmentVO1.getIsBinding() || equipmentVO1 is PocketEquipmentVO)
         {
            SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(equipmentVO1,position);
            if(equipmentVO1.getIsBinding())
            {
               showWarningBox("该物品已绑定， 不能传送！, 请重新选择！",0);
            }
            else
            {
               showWarningBox("该物品不能传送！请重新选择！",0);
            }
            cdTime = Number(_exechangeEquipmentXML.CDTime[0].@time) * 1000;
            _nextSendTime = new Date().getTime() + cdTime;
            clockTarget = false;
            clock2(false);
            saveinfo = new SaveTaskInfo();
            saveinfo.type = "4399";
            saveinfo.isHaveData = false;
            SaveTaskList.getInstance().addData(saveinfo);
            MyFunction2.saveGame();
            return;
         }
         targetPlayer = ExEPlayerData.getInstance().myPlayerss[_currentIdx][0];
         oldPlayerEquipmentVOs = MyFunction.getInstance().cloneEquipmentVOArray(targetPlayer.playerVO.packageEquipmentVOs);
         isSuccess = MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(targetPlayer.playerVO.packageEquipmentVOs,equipmentVO1.clone());
         if(isSuccess)
         {
            fail = function():void
            {
               var saveinfo:SaveTaskInfo;
               GamingUI.getInstance().showMessageTip("存档失败");
               SmallPackage.getInstance().addEquipmentVOs(equipmentVO1);
               targetPlayer.playerVO.packageEquipmentVOs = oldPlayerEquipmentVOs;
               showWarningBox("传送失败!, 请重试！",0);
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399_2";
               saveinfo.isHaveData = false;
               saveinfo.successFunc = function():void
               {
                  clockTarget = false;
                  clock2(false);
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               };
               saveinfo.failFunc = function():void
               {
                  showWarningBox("存档失败！请重新保存游戏！",1);
                  clockTarget = false;
                  clock2(false);
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               };
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame();
            };
            success = function():void
            {
               var saveinfo:SaveTaskInfo;
               if(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - _firstSaveLasttime > m_const_saveMaxLimitTime)
               {
                  SmallPackage.getInstance().addEquipmentVOs(equipmentVO1);
                  targetPlayer.playerVO.packageEquipmentVOs = oldPlayerEquipmentVOs;
                  showWarningBox("网速过低, 不能传送，传送失败！",1);
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399_3";
                  saveinfo.isHaveData = false;
                  saveinfo.successFunc = function():void
                  {
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  saveinfo.failFunc = function():void
                  {
                     showWarningBox("存档失败！请重新保存游戏！",1);
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame();
                  return;
               }
               changeAndSaveFile(function():void
               {
                  var saveinfo:SaveTaskInfo;
                  var cdTime:Number = Number(_exechangeEquipmentXML.CDTime[0].@time) * 1000;
                  ExEPlayerData.getInstance().haveExeNum = 0;
                  _nextSendTime = new Date().getTime() + cdTime;
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399_4";
                  saveinfo.isHaveData = false;
                  saveinfo.successFunc = function():void
                  {
                     showWarningBox("传送成功！",0);
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  saveinfo.failFunc = function():void
                  {
                     showWarningBox("存档失败！请重新保存游戏！",1);
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame();
                  addSendLightEffect(ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][0].getShow());
               },function():void
               {
                  var saveinfo:SaveTaskInfo;
                  SmallPackage.getInstance().addEquipmentVOs(equipmentVO1);
                  targetPlayer.playerVO.packageEquipmentVOs = oldPlayerEquipmentVOs;
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399_5";
                  saveinfo.isHaveData = false;
                  saveinfo.successFunc = function():void
                  {
                     showWarningBox("传送失败!, 请重试！",0);
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  saveinfo.failFunc = function():void
                  {
                     showWarningBox("存档失败！请重新保存游戏！",1);
                     clockTarget = false;
                     clock2(false);
                     GamingUI.getInstance().mouseChildren = true;
                     GamingUI.getInstance().mouseEnabled = true;
                  };
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame();
               });
            };
            if(ExEPlayerData.getInstance().haveExeNum <= 0 && !GamingUI.getInstance().player1.vipVO.isFreeExchangeEquipment)
            {
               clock2(false);
               buyExeNumBox = new BuyExeNumBox(_exechangeEquipmentXML,equipmentCell,this);
               buyExeNumBox.x = (stage.stageWidth - buyExeNumBox.width) / 2;
               buyExeNumBox.y = (stage.stageHeight - buyExeNumBox.height) / 2;
               addChild(buyExeNumBox);
               SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(equipmentVO1,position);
               targetPlayer.playerVO.packageEquipmentVOs = oldPlayerEquipmentVOs;
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
               return;
            }
            SmallPackage.getInstance().deleEquipmentVOs(equipmentVO1);
            world = this;
            _firstSaveLasttime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
            saveinfo1 = new SaveTaskInfo();
            saveinfo1.type = "4399_1";
            saveinfo1.isHaveData = false;
            saveinfo1.successFunc = success;
            saveinfo1.failFunc = fail;
            SaveTaskList.getInstance().addData(saveinfo1);
            MyFunction2.saveGame();
         }
         else
         {
            SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(equipmentVO1,position);
            showWarningBox("目标背包已满！， 不能传送！",0);
            if(!GamingUI.getInstance().player1.vipVO.isFreeExchangeEquipment && ExEPlayerData.getInstance().haveExeNum)
            {
               saveinfo2 = new SaveTaskInfo();
               saveinfo2.type = "4399_6";
               saveinfo2.isHaveData = false;
               saveinfo2.successFunc = function():void
               {
                  showWarningBox("传送成功！",0);
                  clockTarget = false;
                  clock2(false);
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               };
               saveinfo2.failFunc = function():void
               {
                  showWarningBox("存档失败！请重新保存游戏！",1);
                  clockTarget = false;
                  clock2(false);
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               };
               SaveTaskList.getInstance().addData(saveinfo2);
               MyFunction2.saveGame();
            }
            else
            {
               clockTarget = false;
               clock2(false);
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
         }
      }
      
      private function changeAndSaveFile(param1:Function, param2:Function) : void
      {
         var exePlayerData:ExEPlayerData;
         var saveinfo:SaveTaskInfo;
         var successFun:Function = param1;
         var failFun:Function = param2;
         var export:ExportUI = new ExportUI();
         var xmls:Array = export.createPlayerPartXML(ExEPlayerData.getInstance().myPlayerss[_currentIdx][0],ExEPlayerData.getInstance().myPlayerss[_currentIdx][1]);
         trace("old save  file:" + ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx].toXMLString());
         trace("xmls[0].name():",xmls[0].name());
         ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx]["Data"][0][xmls[0].name()][0] = xmls[0];
         if(xmls[1])
         {
            ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx]["Data"][0][xmls[1].name()][0] = xmls[1];
         }
         trace("new save  file:" + ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx].toXMLString());
         if(ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx]["Data"][0].hasOwnProperty("PublicStorage") == false)
         {
            exePlayerData = ExEPlayerData.getInstance();
            trace("出错了");
            throw new Error();
         }
         saveinfo = new SaveTaskInfo();
         saveinfo.type = "changeAndSaveFile";
         saveinfo.isHaveData = true;
         saveinfo.data = ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx];
         saveinfo.saveIndex = _currentIdx;
         saveinfo.successFunc = function():void
         {
            GamingUI.getInstance().showMessageTip("存档成功");
            if(Boolean(successFun))
            {
               successFun();
            }
         };
         saveinfo.failFunc = function():void
         {
            GamingUI.getInstance().showMessageTip("存档失败");
            if(Boolean(failFun))
            {
               failFun();
            }
         };
         saveinfo.title = ExEPlayerData.getInstance().myPlayerSaveFileDatas[_currentIdx].title;
         SaveTaskList.getInstance().addData(saveinfo);
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         var _loc3_:int = 0;
         if(_clockTarget)
         {
            showWarningBox("操作正在进行！",0);
            return;
         }
         if(int(DisplayObjectContainer(param1.currentTarget).numChildren) <= 0)
         {
            return;
         }
         _currentIdx = uint(String(param1.currentTarget.name).slice(9,10));
         requestFile();
         var _loc2_:int = int(_containers.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_containers[_loc3_] == param1.currentTarget)
            {
               MyFunction.getInstance().changeSaturation2(_containers[_loc3_],0,0,0);
            }
            else
            {
               MyFunction.getInstance().changeSaturation2(_containers[_loc3_],-70,0,-70);
            }
            _loc3_++;
         }
         if(_currentIdx != GameData.getInstance().getSaveFileData().index && !ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx])
         {
            if(getChildByName(SmallPackage.getInstance().name))
            {
               removeChild(SmallPackage.getInstance());
            }
            return;
         }
         if(!getChildByName(SmallPackage.getInstance().name))
         {
            addChild(SmallPackage.getInstance());
         }
         traceText("........................",_currentIdx);
      }
      
      private function init() : void
      {
         var i:int;
         var length:int;
         m_initcompleteListener = new InitCompleteListener2();
         m_initcompleteListener.initCompleteFun = initComplete;
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _containers = new Vector.<Sprite>();
         _containers.push(container1);
         _containers.push(container2);
         _containers.push(container3);
         _containers.push(container4);
         _containers.push(container5);
         _containers.push(container6);
         container2.scaleX = -1;
         container3.scaleX = -1;
         container6.scaleX = -1;
         length = int(_containers.length);
         i = 0;
         while(i < length)
         {
            _containers[i].buttonMode = true;
            if(_containers[i].name != "container" + GameData.getInstance().getSaveFileData().index)
            {
               addLightShow(_containers[i]);
            }
            i++;
         }
         length = int(ExEPlayerData.getInstance().myPlayerss.length);
         i = 0;
         while(i < length)
         {
            if(ExEPlayerData.getInstance().myPlayerss[i])
            {
               _currentIdx = i;
               if(hasOwnProperty("container" + _currentIdx))
               {
                  ClearUtil.clearDisplayObjectInContainer(this["container" + _currentIdx],false,false);
               }
               initShow();
            }
            i++;
         }
         PackageEquipmentCell.packageBtnSate = 320;
         SmallPackage.getInstance().refreshSmallPackage("doublePackage",null);
         SmallPackage.getInstance().x = 386;
         SmallPackage.getInstance().y = 217;
         _saveApiListener = new SaveAPIListener2();
         _saveApiListener.getDataExcepFun = getDataFail;
         _saveApiListener.getSaveFileSuccessFun = getSaveFileSuccess;
         _saveApiListener.netGetErrorFun = getDataFail;
         MyFunction2.loadXMLFunction("exchangeEquipment",function(param1:XML):void
         {
            _exechangeEquipmentXML = param1;
         },loadXMLFailFun,true);
      }
      
      private function loadXMLFailFun() : void
      {
         quitBtn.dispatchEvent(new UIBtnEvent("clickQuitBtn"));
      }
      
      private function requestFile() : void
      {
         if(_currentIdx != GameData.getInstance().getSaveFileData().index && !ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx])
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "加载中...";
            GamingUI.getInstance().getAPI4399().saveAPI.addSaveAPIListener(_saveApiListener);
            GamingUI.getInstance().getAPI4399().saveAPI.getData(false,_currentIdx);
         }
      }
      
      private function getDataFail(param1:SaveFileData = null) : void
      {
         GamingUI.getInstance().getAPI4399().saveAPI.removeSaveAPIListener(_saveApiListener);
         requestFileFail();
      }
      
      private function getSaveFileSuccess(param1:SaveFileData) : void
      {
         GamingUI.getInstance().getAPI4399().saveAPI.removeSaveAPIListener(_saveApiListener);
         ExEPlayerData.getInstance().myPlayerSaveFileDatas[_currentIdx] = param1;
         if(param1)
         {
            getFile(param1.saveXML);
         }
         else
         {
            getFile(null);
         }
      }
      
      private function getFile(param1:XML) : void
      {
         if(param1 == null)
         {
            showWarningBox("该存档为空！",0);
            if(hasOwnProperty("container" + _currentIdx))
            {
               ClearUtil.clearDisplayObjectInContainer(this["container" + _currentIdx],false,false);
            }
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
            return;
         }
         ExEPlayerData.getInstance().myPlayerXMLs[_currentIdx] = param1;
         try
         {
            m_currentInitPlayersData = new InitPlayersData();
            m_initPlayersDatas.push(m_currentInitPlayersData);
            m_currentInitPlayersData.addInitCompleteListener(m_initcompleteListener);
            m_currentInitPlayersData.initPlayerData(param1,_currentIdx,3839,false);
         }
         catch(error:Error)
         {
            showWarningBox("第" + _currentIdx + "个档初始化失败！",0);
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
      
      private function initComplete() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx])
         {
            _loc1_ = int(ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx].length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_])
               {
                  _loc2_ = int(ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_].length);
                  _loc3_ = 0;
                  while(_loc3_ < _loc2_)
                  {
                     if(ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_][_loc3_])
                     {
                        ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_][_loc3_].clear();
                     }
                     ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_][_loc3_] = null;
                     _loc3_++;
                  }
               }
               ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx][_loc4_] = null;
               _loc4_++;
            }
            ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx] = null;
         }
         if(ExEPlayerData.getInstance().myPlayerss[_currentIdx])
         {
            _loc1_ = int(ExEPlayerData.getInstance().myPlayerss[_currentIdx].length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(ExEPlayerData.getInstance().myPlayerss[_currentIdx][_loc4_])
               {
                  ExEPlayerData.getInstance().myPlayerss[_currentIdx][_loc4_].clear();
               }
               ExEPlayerData.getInstance().myPlayerss[_currentIdx][_loc4_] = null;
               _loc4_++;
            }
            ExEPlayerData.getInstance().myPlayerss[_currentIdx] = null;
         }
         ExEPlayerData.getInstance().myPlayerNicknameDatas[_currentIdx] = null;
         ExEPlayerData.getInstance().myPlayerss[_currentIdx] = new Vector.<Player>();
         ExEPlayerData.getInstance().myPlayerss[_currentIdx].push(m_currentInitPlayersData.player1);
         ExEPlayerData.getInstance().myPlayerss[_currentIdx].push(m_currentInitPlayersData.player2);
         ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx] = new Vector.<Vector.<BuffDrive>>();
         ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx].push(m_currentInitPlayersData.player1BuffDrives);
         ExEPlayerData.getInstance().myPlayerBuffss[_currentIdx].push(m_currentInitPlayersData.player2BuffDrives);
         ExEPlayerData.getInstance().myPlayerNicknameDatas[_currentIdx] = m_currentInitPlayersData.nickNameData;
         m_currentInitPlayersData.player1 = null;
         m_currentInitPlayersData.player2 = null;
         m_currentInitPlayersData.player1BuffDrives = null;
         m_currentInitPlayersData.player2BuffDrives = null;
         m_currentInitPlayersData.nickNameData = null;
         m_currentInitPlayersData.setSaveXML(null);
         initShow();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
      
      private function requestFileFail() : void
      {
         showWarningBox("第" + _currentIdx + "个档初始化失败！",0);
      }
      
      private function initShow() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(ExEPlayerData.getInstance().myPlayerss[_currentIdx])
         {
            _loc1_ = int(ExEPlayerData.getInstance().myPlayerss[_currentIdx].length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(ExEPlayerData.getInstance().myPlayerss[_currentIdx][_loc2_])
               {
                  if(ExEPlayerData.getInstance().myPlayerShowss[_currentIdx] == null)
                  {
                     ExEPlayerData.getInstance().myPlayerShowss[_currentIdx] = new Vector.<EntityShowContainer>(2);
                  }
                  if(ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] == null)
                  {
                     ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] = new EntityShowContainer();
                     (ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] as EntityShowContainer).init();
                     (ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] as EntityShowContainer).refreshPlayerShow(ExEPlayerData.getInstance().myPlayerss[_currentIdx][_loc2_].playerVO);
                     (ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] as EntityShowContainer).getShow().scaleY = 1.5;
                     (ExEPlayerData.getInstance().myPlayerShowss[_currentIdx][_loc2_] as EntityShowContainer).getShow().scaleX = 1.5;
                  }
               }
               _loc2_++;
            }
            if(this.hasOwnProperty("container" + _currentIdx))
            {
               ClearUtil.clearDisplayObjectInContainer(this["container" + _currentIdx],false,false);
               addEffects(this["container" + _currentIdx]);
            }
         }
      }
      
      private function addSendLightEffect(param1:DisplayObjectContainer) : void
      {
         var container:DisplayObjectContainer = param1;
         var effects1:MovieClip = MyFunction2.returnShowByClassName("SendLight") as MovieClip;
         effects1.addFrameScript(effects1.totalFrames - 1,function():void
         {
            effects1.stop();
            if(effects1.parent)
            {
               effects1.parent.removeChild(effects1);
            }
         });
         effects1.x = -80;
         effects1.y = -271;
         container.addChildAt(effects1,0);
         effects1.play();
      }
      
      private function addLightShow(param1:DisplayObjectContainer) : void
      {
         var _loc2_:MovieClip = MyFunction2.returnShowByClassName("Light") as MovieClip;
         param1.addChild(_loc2_);
      }
      
      private function addEffects(param1:DisplayObjectContainer) : void
      {
         var length:int;
         var j:int;
         var container:DisplayObjectContainer = param1;
         var effects:MovieClip = MyFunction2.returnShowByClassName("GatherEffects") as MovieClip;
         var world:ExchangeWorld = this;
         effects.addFrameScript(effects.totalFrames - 1,function():void
         {
            var index:int;
            var effects2:MovieClip;
            effects.stop();
            if(container.getChildByName(effects.name))
            {
               container.removeChild(effects);
            }
            if(_containers)
            {
               index = int(_containers.indexOf(container as Sprite));
               if(index != -1)
               {
                  index += 1;
                  length = ExEPlayerData.getInstance().myPlayerShowss[index].length;
                  j = 0;
                  while(j < length)
                  {
                     if(ExEPlayerData.getInstance().myPlayerShowss[index][j])
                     {
                        if(ExEPlayerData.getInstance().myPlayerShowss[index][1] == null)
                        {
                           (ExEPlayerData.getInstance().myPlayerShowss[index][j] as EntityShowContainer).getShow().x = -10;
                        }
                        else
                        {
                           (ExEPlayerData.getInstance().myPlayerShowss[index][j] as EntityShowContainer).getShow().x = -60 + 100 * j;
                        }
                        (ExEPlayerData.getInstance().myPlayerShowss[index][j] as EntityShowContainer).getShow().y = 0;
                        container.addChild((ExEPlayerData.getInstance().myPlayerShowss[index][j] as EntityShowContainer).getShow());
                     }
                     j++;
                  }
                  effects2 = MyFunction2.returnShowByClassName("IncreasedParticle") as MovieClip;
                  effects2.addFrameScript(effects2.totalFrames - 1,function():void
                  {
                     effects2.stop();
                     if(effects2.parent)
                     {
                        effects2.parent.removeChild(effects2);
                     }
                  });
                  effects2.x = -63;
                  effects2.y = -200;
                  container.addChild(effects2);
                  effects2.play();
               }
            }
         });
         effects.x = -102;
         effects.y = -133;
         container.addChild(effects);
         effects.play();
      }
      
      private function clickOk(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data.task.fun)
            {
               param1.data.task.fun();
            }
            for(var _loc2_ in param1.data.task)
            {
               param1.data.task[_loc2_] = null;
            }
            param1.data.task = null;
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         if(stage == null)
         {
            return;
         }
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            var _loc4_:* = param3.type;
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function set clockTarget(param1:Boolean) : void
      {
         _clockTarget = param1;
      }
      
      private function clock2(param1:Boolean) : void
      {
         if(param1)
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "正在处理中, 请勿刷新！";
         }
         else
         {
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().manBan.text.text = "";
         }
      }
   }
}

