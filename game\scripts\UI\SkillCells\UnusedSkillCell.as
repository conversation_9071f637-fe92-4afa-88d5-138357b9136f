package UI.SkillCells
{
   import UI.CellBorderSingle;
   import UI.UIInterface.OldInterface.ISkillCellBackground;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class UnusedSkillCell extends SkillCell
   {
      public var skillbackground:ISkillCellBackground;
      
      public function UnusedSkillCell()
      {
         super();
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(skillbackground)
         {
            skillbackground.clear();
         }
         skillbackground = null;
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
         super.rollOut(param1);
      }
      
      override public function get skillCellBackground() : ISkillCellBackground
      {
         return skillbackground;
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.visible == true)
         {
            CellBorderSingle.getInstance().x = -0.55;
            CellBorderSingle.getInstance().y = -1.8;
            CellBorderSingle.getInstance().width = 47.25;
            CellBorderSingle.getInstance().height = 46.25;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return skillbackground.getRect(param1);
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
   }
}

