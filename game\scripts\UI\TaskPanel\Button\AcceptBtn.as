package UI.TaskPanel.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction;
   import flash.events.MouseEvent;
   
   public class AcceptBtn extends Btn
   {
      public function AcceptBtn()
      {
         super();
         setTipString("点击接受任务\n已接受的日常任务将出现在背包任务栏中");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickAcceptTaskBtn"));
      }
      
      public function set isClickAble(param1:Boolean) : void
      {
         if(param1)
         {
            mouseChildren = true;
            mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(this,0);
         }
         else
         {
            mouseChildren = false;
            mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(this,-100);
         }
      }
   }
}

