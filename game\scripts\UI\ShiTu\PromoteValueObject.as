package UI.ShiTu
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloneUtil;
   
   public class PromoteValueObject
   {
      public var value:Object;
      
      public var addAttributes:Vector.<String>;
      
      public var addAttributeValueObjects:Vector.<PromoteValueObject>;
      
      public var descriptions:Vector.<String>;
      
      public var extras:Vector.<Object>;
      
      public function PromoteValueObject()
      {
         super();
      }
      
      public static function getDescriptions(param1:Vector.<String>, param2:Vector.<PromoteValueObject>, param3:Vector.<String>) : Vector.<String>
      {
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:* = undefined;
         var _loc4_:* = undefined;
         _loc6_ = !!param1 ? param1.length : 0;
         var _loc7_:* = new Vector.<String>();
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            if(param2[_loc8_].addAttributes == null || param2[_loc8_].addAttributes.length == 0)
            {
               _loc7_.push(param3[_loc8_]);
            }
            else
            {
               _loc5_ = getDescriptions(param2[_loc8_].addAttributes,param2[_loc8_].addAttributeValueObjects,param2[_loc8_].descriptions);
               _loc4_ = _loc7_.concat(_loc5_);
               ClearUtil.nullArr(_loc5_,false,false,false);
               ClearUtil.nullArr(_loc7_,false,false,false);
               _loc7_ = _loc4_;
            }
            _loc8_++;
         }
         return _loc7_;
      }
      
      public function clear() : void
      {
         value = null;
         ClearUtil.nullArr(addAttributes);
         ClearUtil.nullArr(addAttributeValueObjects);
         ClearUtil.nullArr(descriptions);
         ClearUtil.nullArr(extras);
         addAttributes = null;
         addAttributeValueObjects = null;
         descriptions = null;
         extras = null;
      }
      
      public function clone() : PromoteValueObject
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:PromoteValueObject = new PromoteValueObject();
         _loc1_.value = this.value;
         _loc2_ = !!addAttributes ? addAttributes.length : 0;
         _loc1_.addAttributes = new Vector.<String>();
         _loc1_.addAttributeValueObjects = new Vector.<PromoteValueObject>();
         _loc1_.descriptions = new Vector.<String>();
         _loc1_.extras = new Vector.<Object>();
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_.addAttributes.push(addAttributes[_loc3_]);
            _loc1_.addAttributeValueObjects.push(addAttributeValueObjects[_loc3_].clone());
            _loc1_.descriptions.push(descriptions[_loc3_]);
            _loc1_.extras.push(extras[_loc3_]);
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function getOriginalValueObject() : PromoteValueObject
      {
         var _loc3_:int = 0;
         var _loc2_:PromoteValueObject = new PromoteValueObject();
         if(value is int || value is Number)
         {
            _loc2_.value = 0;
         }
         else if(value is String && (int(value) || Number(value)))
         {
            _loc2_.value = "0";
         }
         else
         {
            _loc2_.value = value;
         }
         _loc2_.addAttributes = !!addAttributes ? addAttributes.slice() : null;
         _loc2_.addAttributeValueObjects = new Vector.<PromoteValueObject>();
         _loc2_.descriptions = !!descriptions ? descriptions.slice() : null;
         _loc2_.extras = CloneUtil.cloneArr(extras);
         var _loc1_:int = !!addAttributeValueObjects ? addAttributeValueObjects.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_.addAttributeValueObjects.push(addAttributeValueObjects[_loc3_].getOriginalValueObject());
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getOppositeValueObject() : PromoteValueObject
      {
         var _loc3_:int = 0;
         var _loc2_:PromoteValueObject = new PromoteValueObject();
         if(value is int || value is Number)
         {
            _loc2_.value = -Number(value);
         }
         else if(value is String && Number(value))
         {
            _loc2_.value = -Number(value) + "";
         }
         else
         {
            _loc2_.value = value;
         }
         _loc2_.addAttributes = !!addAttributes ? addAttributes.slice() : null;
         _loc2_.addAttributeValueObjects = new Vector.<PromoteValueObject>();
         _loc2_.descriptions = !!addAttributes ? descriptions.slice() : null;
         _loc2_.extras = CloneUtil.cloneArr(extras);
         var _loc1_:int = !!addAttributeValueObjects ? addAttributeValueObjects.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_.addAttributeValueObjects.push(addAttributeValueObjects[_loc3_].getOppositeValueObject());
            _loc3_++;
         }
         return _loc2_;
      }
   }
}

