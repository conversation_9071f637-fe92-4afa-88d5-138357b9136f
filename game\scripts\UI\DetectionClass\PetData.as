package UI.DetectionClass
{
   import YJFY.Utils.ClearUtil;
   
   public class PetData
   {
      private var m_numOfAdvancePet:int;
      
      private var m_maxValueOfProOfPetPassiveSkillObj:Object;
      
      public function PetData()
      {
         super();
         m_maxValueOfProOfPetPassiveSkillObj = {};
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_maxValueOfProOfPetPassiveSkillObj);
         m_maxValueOfProOfPetPassiveSkillObj = null;
      }
      
      public function setNumOfAdvancePet(param1:int) : void
      {
         numOfAdvancePet = param1;
      }
      
      public function getNumOfAdvancePet() : int
      {
         return numOfAdvancePet;
      }
      
      public function setMaxValueOfProOfPetPassiveSkill(param1:String, param2:int) : void
      {
         var _loc3_:int = int(m_maxValueOfProOfPetPassiveSkillObj[param1]);
         if(_loc3_ < param2)
         {
            m_maxValueOfProOfPetPassiveSkillObj[param1] = param2;
         }
      }
      
      public function getMaxValueOfProOfPetPassiveSkill(param1:String) : int
      {
         return int(m_maxValueOfProOfPetPassiveSkillObj[param1]);
      }
      
      private function get numOfAdvancePet() : int
      {
         return m_numOfAdvancePet;
      }
      
      private function set numOfAdvancePet(param1:int) : void
      {
         m_numOfAdvancePet = param1;
      }
   }
}

