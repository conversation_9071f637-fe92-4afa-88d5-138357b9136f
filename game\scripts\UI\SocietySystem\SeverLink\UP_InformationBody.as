package UI.SocietySystem.SeverLink
{
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.InformationBodySeverDetail;
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.UP_VerifySeverDetail;
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class UP_InformationBody extends InformationBody
   {
      protected var m_inormationBodySeverDetail:InformationBodySeverDetail;
      
      public function UP_InformationBody()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_inormationBodySeverDetail);
         m_inormationBodySeverDetail = null;
      }
      
      public function setSeverDetail(param1:InformationBodySeverDetail) : void
      {
         if(id_informationBody == 2000 && !(param1 is UP_VerifySeverDetail))
         {
            throw new Error("登录的severDetail必须是UP_LogInSeverDetail");
         }
         m_inormationBodySeverDetail = param1;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc3_:ByteArray = new ByteArray();
         _loc3_.endian = "littleEndian";
         var _loc1_:ByteArray = !!m_informationBodyDetail ? m_informationBodyDetail.getThisByteArray() : null;
         var _loc5_:int = !!_loc1_ ? _loc1_.length : 0;
         _loc3_.writeInt(0 + _loc5_ + 4 + 4);
         _loc3_.writeInt(id_informationBody);
         if(_loc1_)
         {
            _loc1_.position = 0;
            _loc3_.writeBytes(_loc1_);
         }
         _loc3_.position = 0;
         return _loc3_;
      }
   }
}

