package UI2.Midautumn
{
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.BuffData;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class IfBuyView
   {
      private var m_show:MovieClip;
      
      private var m_midautumn:MidautumnView;
      
      private var m_txtMoney:TextField;
      
      private var m_btnOK:ButtonLogicShell2;
      
      private var m_btnCancel:ButtonLogicShell2;
      
      private var m_mcBuyType:MovieClip;
      
      private var _buffId:int;
      
      public function IfBuyView()
      {
         super();
         m_btnOK = new ButtonLogicShell2();
         m_btnCancel = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         m_midautumn = null;
         m_txtMoney = null;
         m_mcBuyType = null;
         ClearUtil.clearObject(m_btnCancel);
         m_btnCancel = null;
         ClearUtil.clearObject(m_btnOK);
         m_btnOK = null;
         m_show.removeEventListener("clickButton",clickButton,true);
         m_show.removeEventListener("showMessageBox",showMessageBox,true);
         m_show.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_show.removeEventListener("showMessageBox",showMessageBox,false);
         m_show.removeEventListener("hideMessageBox",hideMessageBox,false);
         m_show.removeEventListener("warningBox",sureOrCancel,true);
      }
      
      public function init(param1:MovieClip, param2:MidautumnView) : void
      {
         m_show = param1;
         m_midautumn = param2;
         m_txtMoney = m_show["txtMoney"] as TextField;
         m_mcBuyType = m_show["mcBuyType"] as MovieClip;
         m_mcBuyType.gotoAndStop(1);
         m_btnOK.setShow(m_show["btnyes"]);
         m_btnOK.setTipString("确定");
         m_btnCancel.setShow(m_show["btncancel"]);
         m_btnCancel.setTipString("取消");
         m_show.addEventListener("clickButton",clickButton,true);
         m_show.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_show.addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_btnOK:
               buy2();
               break;
            case m_btnCancel:
               m_midautumn.hideIfBuy();
         }
      }
      
      private function buy2() : void
      {
         var price:uint = uint(MidautumnData.getInstance().getTicketPrice());
         var ticketId:String = String(MidautumnData.getInstance().getTicketPriceId());
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买博饼次数";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               throw new Error("购买物品id前后端不相同！");
            }
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            _buffId = 13011;
            MyFunction2.getServerTimeFunction(addBuff,null,false);
            MyFunction2.saveGame2();
            m_midautumn.startReward();
            MidautumnData.getInstance().setIsBuyAdd(false);
            m_midautumn.sendGetResultNum();
            m_midautumn.hideIfBuy();
         },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function addBuff(param1:String) : void
      {
         var _loc2_:AllTimeBuffVO = XMLSingle.getBuff(_buffId,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc2_.startDate = param1;
         _loc2_.totalTime = _loc2_.xml.@time;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc2_),BuffData.getInstance().buffDrives);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         else if(param1.data.detail == 2)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.cancelFunction))
            {
               (param1.data.task.cancelFunction as Function).apply(null,param1.data.task.cancelFunctionParams);
               ClearUtil.nullArr(param1.data.task.cancelFunctionParams,false,false);
               param1.data.task.cancelFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function removeTip() : void
      {
      }
   }
}

