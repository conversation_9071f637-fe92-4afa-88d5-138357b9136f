package UI.Button.Pet
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class TakeBackPetBtn extends Btn
   {
      public function TakeBackPetBtn()
      {
         super();
         setTipString("收回宠物");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickTakeBackPetBtn"));
      }
   }
}

