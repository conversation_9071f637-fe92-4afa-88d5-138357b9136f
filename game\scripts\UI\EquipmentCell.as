package UI
{
   import UI.Button.ChoiceBtns.EquipmentDestinationBtnListSingle;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.getTimer;
   
   public class EquipmentCell extends Cell implements IEquipmentCell
   {
      private var _child:EquipmentVO;
      
      protected var _equipment:Equipment;
      
      protected var _intervalTime:int;
      
      protected const _TIME:int = 500;
      
      public function EquipmentCell()
      {
         super();
         initCell();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function get equipmentCellBackground() : IEquipmentCellBackground
      {
         return null;
      }
      
      override public function clear() : void
      {
         if(Boolean(_equipment) && _equipment.parent)
         {
            _equipment.parent.removeChild(_equipment);
         }
         _child = null;
         super.clear();
         EquipmentDestinationBtnListSingle.getInstance().clear();
         _child = null;
      }
      
      public function addEquipmentVO(param1:EquipmentVO) : void
      {
         removeEquipmentVO();
         if(!isHaveChild && param1 != null)
         {
            _child = param1;
            _equipment = MyFunction2.sheatheEquipmentShell(param1);
            _equipment.x = 0;
            _equipment.y = 0;
            addChild(_equipment);
            isHaveChild = true;
         }
      }
      
      public function addEquipment(param1:Equipment) : void
      {
         removeEquipmentVO();
         if(!isHaveChild && param1 != null)
         {
            _child = param1.equipmentVO;
            _equipment = param1;
            _equipment.x = 0;
            _equipment.y = 0;
            addChild(_equipment);
            isHaveChild = true;
         }
      }
      
      public function removeEquipmentVO(param1:EquipmentVO = null) : void
      {
         if(isHaveChild)
         {
            if(param1 == _child || param1 == null)
            {
               if(Boolean(_equipment) && _equipment.parent)
               {
                  _equipment.parent.removeChild(_equipment);
               }
               _child = null;
               isHaveChild = false;
            }
         }
      }
      
      public function get child() : EquipmentVO
      {
         return _child;
      }
      
      public function get equipment() : Equipment
      {
         return _equipment;
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      protected function rollOver(param1:MouseEvent) : void
      {
         showBorder();
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":child}));
         }
         if(parent)
         {
            parent.setChildIndex(this,parent.numChildren - 1);
         }
      }
      
      protected function rollOut(param1:MouseEvent) : void
      {
         hideBorder();
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      protected function mouseDown(param1:MouseEvent) : void
      {
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
         _intervalTime = getTimer();
      }
      
      protected function click(param1:MouseEvent) : void
      {
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
         _intervalTime = getTimer() - _intervalTime;
      }
      
      protected function mouseUp(param1:MouseEvent) : void
      {
      }
      
      override protected function addToStage(param1:Event) : void
      {
         addEventListener("rollOver",rollOver,false,0,true);
         addEventListener("rollOut",rollOut,false,0,true);
         addEventListener("mouseDown",mouseDown,true,0,true);
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",click,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("rollOver",rollOver,false);
         removeEventListener("rollOut",rollOut,false);
         removeEventListener("mouseDown",mouseDown,true);
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",click,true);
      }
   }
}

