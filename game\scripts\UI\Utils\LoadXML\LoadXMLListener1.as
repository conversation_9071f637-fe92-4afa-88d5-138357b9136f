package UI.Utils.LoadXML
{
   public class LoadXMLListener1 implements ILoadXMLListener
   {
      public var loadSuccessFun:Function;
      
      public function LoadXMLListener1()
      {
         super();
      }
      
      public function clear() : void
      {
         loadSuccessFun = null;
      }
      
      public function loadSuccess(param1:XML) : void
      {
         loadSuccessFun(param1);
         clear();
      }
      
      public function errorHandler() : void
      {
         clear();
      }
      
      public function securityError() : void
      {
         clear();
      }
      
      public function ioError() : void
      {
         clear();
      }
   }
}

