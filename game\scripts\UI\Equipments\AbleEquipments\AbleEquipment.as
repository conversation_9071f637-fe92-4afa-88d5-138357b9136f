package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EqMagicFrame;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentFrame;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.Number.EquipmentLevelNumber.AddSign;
   import UI.Number.NumberContainer;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.filters.GradientGlowFilter;
   
   public class AbleEquipment extends Equipment
   {
      private static const path:String = "UI.Equipments.AbleEquipments.";
      
      private var _showLayer:Sprite = new Sprite();
      
      private var _levelLayer:Sprite = new Sprite();
      
      private var _frameLayer:Sprite = new Sprite();
      
      private var _holeLayer:Sprite = new Sprite();
      
      private var _useLayer:Sprite = new Sprite();
      
      public function AbleEquipment(param1:AbleEquipmentVO)
      {
         addChild(_frameLayer);
         _frameLayer.mouseChildren = false;
         _frameLayer.mouseEnabled = false;
         mouseEnabled = false;
         addChild(_showLayer);
         addChild(_levelLayer);
         addChild(_holeLayer);
         addChild(_useLayer);
         super(param1);
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(_showLayer);
         ClearUtil.clearDisplayObjectInContainer(_levelLayer);
         ClearUtil.clearDisplayObjectInContainer(_frameLayer);
         ClearUtil.clearDisplayObjectInContainer(_holeLayer);
         ClearUtil.clearDisplayObjectInContainer(_useLayer);
         _showLayer = null;
         _levelLayer = null;
         _frameLayer = null;
         _holeLayer = null;
         _useLayer = null;
      }
      
      private function setLevelNumber(param1:AbleEquipmentVO) : void
      {
         var _loc6_:PreciousEquipmentVO = null;
         var _loc4_:WeaponEquipmentVO = null;
         var _loc5_:NecklaceEquipmentVO = null;
         var _loc8_:GourdEquipmentVO = null;
         var _loc2_:ClothesEquipmentVO = null;
         var _loc3_:NumberContainer = null;
         var _loc7_:AddSign = null;
         while(_levelLayer.numChildren > 0)
         {
            _levelLayer.removeChildAt(0);
         }
         switch(param1.equipmentType)
         {
            case "precious":
               _loc6_ = param1 as PreciousEquipmentVO;
               if(_loc6_.addPlayerSaveAttr.length > 0)
               {
                  _levelLayer.addChild(new EqMagicFrame());
               }
               break;
            case "weapon":
               _loc4_ = param1 as WeaponEquipmentVO;
               if(_loc4_.addPlayerSaveAttr.length > 0)
               {
                  _levelLayer.addChild(new EqMagicFrame());
               }
               break;
            case "necklace":
               _loc5_ = param1 as NecklaceEquipmentVO;
               if(_loc5_.addPlayerSaveAttr.length > 0)
               {
                  _levelLayer.addChild(new EqMagicFrame());
               }
               break;
            case "gourd":
               _loc8_ = param1 as GourdEquipmentVO;
               if(_loc8_.addPlayerSaveAttr.length > 0)
               {
                  _levelLayer.addChild(new EqMagicFrame());
               }
               break;
            case "clothes":
               _loc2_ = param1 as ClothesEquipmentVO;
               if(_loc2_.addPlayerSaveAttr.length > 0)
               {
                  _levelLayer.addChild(new EqMagicFrame());
                  break;
               }
         }
         if(param1.level != 0)
         {
            _loc3_ = new NumberContainer();
            _loc7_ = new AddSign();
            _loc3_.initNumber(param1.level,"UI.Number.EquipmentLevelNumber.EquipmentLevelNumber",0);
            _loc7_.x = -21;
            _loc7_.y = -21;
            _levelLayer.addChild(_loc7_);
            _loc3_.x = _loc7_.x + 15;
            _loc3_.y = _loc7_.y;
            _levelLayer.addChild(_loc3_);
         }
      }
      
      private function setLevelFilterColor(param1:AbleEquipmentVO) : void
      {
         if(param1.level && _equipmentSprite)
         {
            _equipmentSprite.filters = [new GlowFilter(param1.messageBoxColor,1,8,8,3,1)];
         }
      }
      
      private function setHoleLayer(param1:AbleEquipmentVO) : void
      {
         var _loc7_:int = 0;
         var _loc5_:InsetGemEquipmentVO = null;
         var _loc3_:Sprite = null;
         var _loc2_:* = 0;
         var _loc6_:Number = NaN;
         ClearUtil.clearDisplayObjectInContainer(_holeLayer);
         var _loc4_:int = param1.getHoleNum();
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc5_ = param1.getInsetGem(_loc7_);
            _loc2_ = !!_loc5_ ? _loc5_.showColor : 0;
            _loc6_ = !!_loc5_ ? 1 : 1;
            _loc3_ = createHoleShow(_loc2_,_loc6_,Boolean(_loc5_));
            _loc3_.x = 16 + _loc7_ % 5 * -8;
            _loc3_.y = 12;
            _holeLayer.addChild(_loc3_);
            _loc7_++;
         }
      }
      
      private function createHoleShow(param1:uint, param2:Number, param3:Boolean) : Sprite
      {
         var _loc4_:Array = null;
         var _loc5_:Sprite = new Sprite();
         _loc5_.graphics.lineStyle();
         if(param3)
         {
            _loc4_ = [16777215,param1,0];
         }
         else
         {
            _loc4_ = [0,param1,0];
         }
         var _loc6_:Array = [1,1,1];
         var _loc7_:Array = [0,10,20];
         _loc5_.graphics.beginGradientFill("radial",_loc4_,_loc6_,_loc7_,null,"pad","rgb");
         _loc5_.graphics.drawCircle(3,3,3);
         _loc5_.graphics.endFill();
         if(param3)
         {
            _loc4_ = [0,16777215];
            _loc6_ = [0,1];
            _loc7_ = [0,255];
            _loc5_.filters = [new GradientGlowFilter(0,45,_loc4_,_loc6_,_loc7_,5,5,1,1,"outer")];
         }
         return _loc5_;
      }
      
      private function setFrame(param1:AbleEquipmentVO) : void
      {
         while(_frameLayer.numChildren > 0)
         {
            _frameLayer.removeChildAt(0);
         }
         if(param1.level == 9)
         {
            _frameLayer.addChild(new EquipmentFrame());
         }
      }
      
      override protected function setShow(param1:EquipmentVO) : void
      {
         var loadListener:LoadFinishListener1;
         var equipmentVO:EquipmentVO = param1;
         if(equipmentVO == null)
         {
            return;
         }
         loadListener = new LoadFinishListener1(function():void
         {
            if(_equipmentVO == null)
            {
               return;
            }
            setmg(equipmentVO.className);
            setLevelNumber(equipmentVO as AbleEquipmentVO);
            setFrame(equipmentVO as AbleEquipmentVO);
            setLevelFilterColor(equipmentVO as AbleEquipmentVO);
            setHoleLayer(equipmentVO as AbleEquipmentVO);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSources,loadListener);
      }
      
      override protected function setmg(param1:String) : void
      {
         _equipmentSprite = MyFunction2.returnShowByClassName("UI.Equipments.AbleEquipments." + param1) as Sprite;
         if(_equipmentSprite == null)
         {
            trace("没有装备显示！");
            return;
         }
         while(_showLayer.numChildren > 0)
         {
            _showLayer.removeChildAt(_showLayer.numChildren - 1);
         }
         _showLayer.addChild(_equipmentSprite);
      }
      
      override public function set equipmentVO(param1:EquipmentVO) : void
      {
         _equipmentVO = param1;
         setShow(_equipmentVO);
      }
      
      override public function get equipmentVO() : EquipmentVO
      {
         return _equipmentVO;
      }
      
      override public function imperfectClone() : Equipment
      {
         return new AbleEquipment(equipmentVO as AbleEquipmentVO);
      }
      
      override public function clone() : Equipment
      {
         return new AbleEquipment((equipmentVO as AbleEquipmentVO).clone() as AbleEquipmentVO);
      }
   }
}

