package UI.Animation
{
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   
   public class M2B
   {
      public function M2B()
      {
         super();
      }
      
      public static function transformM2B(param1:MovieClip, param2:Boolean = false, param3:Number = 0, param4:Number = 0) : Array
      {
         var _loc6_:BitmapData = null;
         var _loc8_:int = 0;
         var _loc7_:Matrix = null;
         var _loc5_:Array = [];
         _loc8_ = 1;
         while(_loc8_ < param1.totalFrames)
         {
            param1.gotoAndStop(_loc8_);
            _loc7_ = new Matrix();
            if(param2)
            {
               _loc7_.translate((param1.width + param3) / 2,(param1.height + param4) / 2);
            }
            _loc6_ = new BitmapData(param1.width + param3,param1.height + param4,true,0);
            _loc6_.draw(param1,_loc7_);
            _loc5_.push(_loc6_);
            _loc8_++;
         }
         return _loc5_;
      }
      
      public static function transformM2B2(param1:MovieClip, param2:Number = 0, param3:Number = 0) : Array
      {
         var _loc5_:BitmapData = null;
         var _loc6_:int = 0;
         var _loc4_:Array = [];
         _loc6_ = 1;
         while(_loc6_ < param1.totalFrames)
         {
            param1.gotoAndStop(_loc6_);
            _loc5_ = new BitmapData(param2,param3,true,0);
            _loc5_.draw(param1);
            _loc4_.push(_loc5_);
            _loc6_++;
         }
         return _loc4_;
      }
   }
}

