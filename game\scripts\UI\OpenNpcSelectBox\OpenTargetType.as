package UI.OpenNpcSelectBox
{
   public class OpenTargetType
   {
      public static const PK_ONE:String = "pkOne";
      
      public static const PK_TWO:String = "pkTwo";
      
      public static const HATCH:String = "hatch";
      
      public static const MIRAGE:String = "mirage";
      
      public static const ADVANCE:String = "advance";
      
      public static const PK_RANK_ONE:String = "pkRankOne";
      
      public static const PK_RANK_TWO:String = "PKRankTwo";
      
      public static const TASK:String = "task";
      
      public static const MAIN_LINE_TASK:String = "mainLineTask";
      
      public static const WORLD_BOSS:String = "worldBoss";
      
      public static const ENDLESS:String = "endless";
      
      public static const AUTO_PET_SCORE:String = "autoPetScore";
      
      public static const PKMODE2_1:String = "pkMode2_1";
      
      public static const JOIN_SOCIETY_PANEL:String = "joinSocietyPanel";
      
      public static const CREATE_SOCIETY_PANEL:String = "createSocietyPanel";
      
      public static const SOCIETY_LIST_PANEL:String = "societyListPanel";
      
      public static const MY_SOCIETY_PANEL:String = "mySocietyPanel";
      
      public static const RESET_PLAYER_1:String = "resetPlayer1";
      
      public static const RESET_PLAYER_2:String = "resetPlayer2";
      
      public static const EQUIPMENT_CREATMAGIC:String = "equipmentCreatMagic";
      
      public static const EQUIPMENT_INHERITMAGIC:String = "equipmentInheritMagic";
      
      public static const ONEPALYERTOTWO:String = "onePlayerToTwo";
      
      public function OpenTargetType()
      {
         super();
      }
   }
}

