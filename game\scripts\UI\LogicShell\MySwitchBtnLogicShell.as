package UI.LogicShell
{
   import GM_UI.GMData;
   import UI.MyFont.FangZhengKaTongJianTi;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.SoundData;
   import flash.events.MouseEvent;
   
   public class MySwitchBtnLogicShell extends SwitchBtnLogicShell
   {
      protected var m_buttonSoundData:SoundData;
      
      public function MySwitchBtnLogicShell()
      {
         super();
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         m_buttonSoundData = new SoundData("buttonSound","buttonSound","NewGameFolder/FirstEnterSource.swf","ButtonSound");
         Part1.getInstance().getSoundManager().addSound2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
      }
      
      public static function setDefaultActivateBtnFromBtns(param1:Array) : MySwitchBtnLogicShell
      {
         if(param1 == null)
         {
            throw new Error();
         }
         MySwitchBtnLogicShell(param1[0]).turnActivate();
         MySwitchBtnLogicShell(param1[0]).getShow().dispatchEvent(new ButtonEvent("clickButton",param1[0]));
         return param1[0];
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_buttonSoundData);
         m_buttonSoundData = null;
         super.clear();
      }
      
      override protected function onClick(param1:MouseEvent) : void
      {
         super.onClick(param1);
         if(GMData.getInstance().isGMApplication == false)
         {
            Part1.getInstance().getSoundManager().play2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
         }
      }
      
      override protected function createSmallToolTip() : void
      {
         super.createSmallToolTip();
         m_smallToolTip.setFont(new FangZhengKaTongJianTi());
      }
   }
}

