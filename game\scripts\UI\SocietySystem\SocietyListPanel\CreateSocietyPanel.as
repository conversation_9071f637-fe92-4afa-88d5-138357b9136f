package UI.SocietySystem.SocietyListPanel
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class CreateSocietyPanel
   {
      private var m_clear:ClearHelper;
      
      private var m_show:Sprite;
      
      private var m_ableDragShow:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_nameText:TextField;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_maxLength:int;
      
      public function CreateSocietyPanel()
      {
         super();
         m_clear = null;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         if(Boolean(m_show) && m_show.parent)
         {
            m_show.parent.removeChild(m_show);
         }
         m_show = null;
         ClearUtil.clearObject(m_ableDragShow);
         m_ableDragShow = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_nameText = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
      }
      
      public function setNameMaxLength(param1:int) : void
      {
         if(param1 <= 0)
         {
            throw new Error("名字最大长度不能小于等于0");
         }
         m_maxLength = param1;
      }
      
      public function setShow(param1:Sprite) : void
      {
         m_show = param1;
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_ableDragShow = new AbleDragSpriteLogicShell();
         m_ableDragShow.setShow(m_show);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_nameText);
         m_nameText.type = "input";
         m_nameText.maxChars = m_maxLength;
         m_nameText.multiline = false;
         m_nameText.restrict = "^ ";
         m_nameText.text = "";
         m_sureBtn = new ButtonLogicShell2();
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_sureBtn.setTipString("点击创建帮会");
         m_cancelBtn = new ButtonLogicShell2();
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         m_cancelBtn.setTipString("点击取消创建");
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function getNameText() : TextField
      {
         return m_nameText;
      }
      
      public function getSureBtn() : ButtonLogicShell2
      {
         return m_sureBtn;
      }
      
      public function getCancelBtn() : ButtonLogicShell2
      {
         return m_cancelBtn;
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
   }
}

