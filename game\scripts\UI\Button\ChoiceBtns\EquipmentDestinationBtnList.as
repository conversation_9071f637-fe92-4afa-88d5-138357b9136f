package UI.Button.ChoiceBtns
{
   import UI.MySprite;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class EquipmentDestinationBtnList extends MySprite
   {
      public static const USE_BTN:int = 1;
      
      public static const SELL_BTN:int = 2;
      
      public static const SAVE_BTN:int = 4;
      
      public static const GET_OUT_BTN:int = 8;
      
      public static const PUT_IN_BTN:int = 16;
      
      public static const TAKE_DOWN_BTN:int = 32;
      
      public static const SEND_BTN:int = 64;
      
      public static const FAST_USE_BTN:int = 128;
      
      public static const DETAIL_BTN:int = 256;
      
      private var _useBtn:UseBtn;
      
      private var _sellBtn:SellBtn;
      
      private var _saveBtn:SaveEquipmentBtn;
      
      private var _getOutBtn:GetOutBtn;
      
      private var _putInBtn:PutInBtn;
      
      private var _takeDownBtn:TakeDownBtn;
      
      private var _sendBtn:SendBtn;
      
      private var _fastUseBtn:FastUseBtn;
      
      private var _detailBtn:DetailBtn;
      
      public function EquipmentDestinationBtnList()
      {
         super();
      }
      
      private function clearBtns() : void
      {
         var _loc1_:DisplayObject = null;
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_)
            {
               removeChildAt(0);
            }
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_useBtn)
         {
            _useBtn.clear();
         }
         if(_sellBtn)
         {
            _sellBtn.clear();
         }
         if(_saveBtn)
         {
            _saveBtn.clear();
         }
         if(_getOutBtn)
         {
            _getOutBtn.clear();
         }
         if(_putInBtn)
         {
            _putInBtn.clear();
         }
         if(_takeDownBtn)
         {
            _takeDownBtn.clear();
         }
         if(_sendBtn)
         {
            _sendBtn.clear();
         }
         if(_fastUseBtn)
         {
            _fastUseBtn.clear();
         }
         if(_detailBtn)
         {
            _detailBtn.clear();
         }
         _useBtn = null;
         _sellBtn = null;
         _saveBtn = null;
         _getOutBtn = null;
         _putInBtn = null;
         _takeDownBtn = null;
         _sendBtn = null;
         _fastUseBtn = null;
         _detailBtn = null;
      }
      
      override public function clear() : void
      {
         super.clear();
         clearBtns();
      }
      
      public function switchToPackageState(param1:int) : void
      {
         initBtnPlace(param1);
      }
      
      private function initBtnPlace(param1:int) : void
      {
         var _loc4_:Sprite = null;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         clearBtns();
         if(param1 & 1)
         {
            _useBtn = new UseBtn();
            setBtnXY(_useBtn,_loc4_);
            _loc4_ = _useBtn;
         }
         if(param1 & 2)
         {
            _sellBtn = new SellBtn();
            setBtnXY(_sellBtn,_loc4_);
            _loc4_ = _sellBtn;
         }
         if(param1 & 4)
         {
            _saveBtn = new SaveEquipmentBtn();
            setBtnXY(_saveBtn,_loc4_);
            _loc4_ = _saveBtn;
         }
         if(param1 & 8)
         {
            _getOutBtn = new GetOutBtn();
            setBtnXY(_getOutBtn,_loc4_);
            _loc4_ = _getOutBtn;
         }
         if(param1 & 16)
         {
            _putInBtn = new PutInBtn();
            setBtnXY(_putInBtn,_loc4_);
            _loc4_ = _putInBtn;
         }
         if(param1 & 32)
         {
            _takeDownBtn = new TakeDownBtn();
            setBtnXY(_takeDownBtn,_loc4_);
            _loc4_ = _takeDownBtn;
         }
         if(param1 & 64)
         {
            _sendBtn = new SendBtn();
            setBtnXY(_sendBtn,_loc4_);
            _loc4_ = _sendBtn;
         }
         if(param1 & 128)
         {
            _fastUseBtn = new FastUseBtn();
            setBtnXY(_fastUseBtn,_loc4_);
            _loc4_ = _fastUseBtn;
         }
         if(param1 & 256)
         {
            _detailBtn = new DetailBtn();
            setBtnXY(_detailBtn,_loc4_);
            _loc4_ = _detailBtn;
         }
      }
      
      private function setBtnXY(param1:Sprite, param2:Sprite) : void
      {
         if(param2 == null)
         {
            param1.x = 25.7;
            param1.y = 15.3;
         }
         else
         {
            param1.x = 25.7;
            param1.y = param2.y + param2.height;
         }
         addChild(param1);
      }
   }
}

