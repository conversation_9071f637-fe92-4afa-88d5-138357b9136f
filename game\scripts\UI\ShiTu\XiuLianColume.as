package UI.ShiTu
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.HaveSpotCMSXLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Players.Player;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class XiuLianColume
   {
      private var m_showBtn:ButtonLogicShell2;
      
      private var m_iconShow:MovieClipPlayLogicShell2;
      
      private var m_nameText:TextField;
      
      private var m_levelText:TextField;
      
      private var m_descriptionText:TextField;
      
      private var m_progressBar:HaveSpotCMSXLogicShell;
      
      private var m_xiuLianContent:XiuLianContent;
      
      private var m_player:Player;
      
      private var m_show:MovieClip;
      
      public function XiuLianColume()
      {
         super();
         m_iconShow = new MovieClipPlayLogicShell2();
      }
      
      public function clear() : void
      {
         m_show = null;
         if(m_showBtn)
         {
            m_showBtn.clear();
         }
         m_showBtn = null;
         m_nameText = null;
         m_levelText = null;
         m_descriptionText = null;
         if(m_progressBar)
         {
            m_progressBar.clear();
         }
         m_progressBar = null;
         ClearUtil.clearObject(m_iconShow);
         m_iconShow = null;
         m_xiuLianContent = null;
         m_player = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getBtn() : ButtonLogicShell2
      {
         return m_showBtn;
      }
      
      public function setXiuLianContent(param1:XiuLianContent, param2:Player) : void
      {
         m_xiuLianContent = param1;
         m_player = param2;
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_xiuLianContent == null || m_player == null)
         {
            return;
         }
         m_iconShow.gotoAndStop(m_xiuLianContent.getId2());
         m_nameText.text = m_xiuLianContent.name;
         m_levelText.text = m_xiuLianContent.level + "/" + m_xiuLianContent.maxLevel;
         m_descriptionText.text = m_xiuLianContent.description;
         if(m_xiuLianContent.level < m_xiuLianContent.maxLevel)
         {
            m_progressBar.change(m_xiuLianContent.currentXiuLianValue / m_xiuLianContent.needXiuLianValue);
            m_progressBar.setDataShow("" + m_xiuLianContent.currentXiuLianValue + "/" + m_xiuLianContent.needXiuLianValue);
         }
         else
         {
            m_progressBar.change(1);
            m_progressBar.setDataShow("已满级");
         }
      }
      
      private function initShow() : void
      {
         m_showBtn = new ButtonLogicShell2();
         m_showBtn.setShow(m_show);
         m_iconShow.setShow(m_show["xiuLianIcon"]);
         m_nameText = m_show["nameText"];
         m_levelText = m_show["levelText"];
         m_descriptionText = m_show["descriptionText"];
         m_progressBar = new HaveSpotCMSXLogicShell();
         m_progressBar.setShow(m_show["progressBar"],false);
         m_nameText.mouseEnabled = false;
         m_levelText.mouseEnabled = false;
         m_descriptionText.mouseEnabled = false;
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_nameText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_levelText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_descriptionText,true);
      }
      
      public function getXiuLianContent() : XiuLianContent
      {
         return m_xiuLianContent;
      }
   }
}

