package UI.AutomaticPetPanel
{
   import UI.GamingUI;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class ChoosePetSwitchBtn extends MySwitchBtnLogicShell
   {
      private var m_petStateShowMC:MovieClipPlayLogicShell;
      
      private var m_pictruecontainerMC:MovieClipPlayLogicShell;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      public function ChoosePetSwitchBtn()
      {
         super();
         m_petStateShowMC = new MovieClipPlayLogicShell();
         m_pictruecontainerMC = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_petStateShowMC);
         m_petStateShowMC = null;
         ClearUtil.clearObject(m_pictruecontainerMC);
         m_pictruecontainerMC = null;
         m_automaticPetVO = null;
         super.clear();
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         initShow2();
      }
      
      public function getAutomaticPetVO() : AutomaticPetVO
      {
         return m_automaticPetVO;
      }
      
      private function initShow() : void
      {
         m_petStateShowMC.setShow(m_show["petStateShow"]);
         m_pictruecontainerMC.setShow(m_petStateShowMC.getShow()["petShow"]);
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO == null)
         {
            return;
         }
         if(m_automaticPetVO.getPlayerVO())
         {
            if(GamingUI.getInstance().player2)
            {
               if(m_automaticPetVO.getPlayerVO() == GamingUI.getInstance().player1.playerVO)
               {
                  m_petStateShowMC.gotoAndStop("chuZhan1p");
               }
               else
               {
                  m_petStateShowMC.gotoAndStop("chuZhan2p");
               }
            }
            else
            {
               m_petStateShowMC.gotoAndStop("chuZhan");
            }
         }
         else
         {
            m_petStateShowMC.gotoAndStop("rest");
         }
         m_pictruecontainerMC.gotoAndStop(m_automaticPetVO.getId());
      }
   }
}

