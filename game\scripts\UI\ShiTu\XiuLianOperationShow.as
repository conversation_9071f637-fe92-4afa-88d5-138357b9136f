package UI.ShiTu
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.HaveSpotCMSXLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PlayerDataForPK;
   import UI.Players.Player;
   import UI.XMLSingle;
   import UI2.TicketBuyLogic;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class XiuLianOperationShow extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_nameText:TextField;
      
      private var m_iconShow:MovieClipPlayLogicShell2;
      
      private var m_levelText:TextField;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_progressBar:HaveSpotCMSXLogicShell;
      
      private var m_needMoneyText:TextField;
      
      private var m_needPKPointText:TextField;
      
      private var m_startXiuLianBtn:ButtonLogicShell2;
      
      private var m_numText:TextField;
      
      private var m_xiuLianData:XiuLianData;
      
      private var m_ticketBuyLogic:TicketBuyLogic;
      
      private var m_closeSubUI:CloseSubUI;
      
      private var m_xiuLianContent:XiuLianContent;
      
      private var m_player:Player;
      
      public function XiuLianOperationShow()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_progressBar = new HaveSpotCMSXLogicShell();
         m_startXiuLianBtn = new ButtonLogicShell2();
         m_xiuLianData = new XiuLianData();
         m_ticketBuyLogic = new TicketBuyLogic();
         m_iconShow = new MovieClipPlayLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_nameText = null;
         m_levelText = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_progressBar);
         m_progressBar = null;
         m_needMoneyText = null;
         m_needPKPointText = null;
         ClearUtil.clearObject(m_startXiuLianBtn);
         m_startXiuLianBtn = null;
         m_numText = null;
         ClearUtil.clearObject(m_xiuLianData);
         m_xiuLianData = null;
         ClearUtil.clearObject(m_ticketBuyLogic);
         m_ticketBuyLogic = null;
         ClearUtil.clearObject(m_iconShow);
         m_iconShow = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_xiuLianContent = null;
         m_player = null;
         super.clear();
      }
      
      public function init(param1:CloseSubUI) : void
      {
         m_closeSubUI = param1;
         m_xiuLianData.initByXML(XMLSingle.getInstance().xiuLianContentXML);
         m_show = MyFunction2.returnShowByClassName("XiuLianOperation") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      public function setData(param1:XiuLianContent, param2:Player) : void
      {
         m_xiuLianContent = param1;
         m_player = param2;
         initShow2();
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(m_show["quitBtn3"]);
         m_iconShow.setShow(m_show["xiuLianIcon"]);
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_nameText);
         m_levelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_levelText);
         m_progressBar.setShow(m_show["progressBar"]);
         m_needMoneyText = m_show["needMoneyText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_needMoneyText);
         m_needPKPointText = m_show["needPKPointText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_needPKPointText);
         m_startXiuLianBtn.setShow(m_show["startXiuLianBtn"]);
         m_numText = m_show["numText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_numText);
      }
      
      private function initShow2() : void
      {
         if(m_xiuLianContent == null || m_player == null)
         {
            return;
         }
         m_player.playerVO.shiFuVO.updateData(GamingUI.getInstance().getNewestTimeStrFromSever());
         m_iconShow.gotoAndStop(m_xiuLianContent.getId2());
         m_nameText.text = m_xiuLianContent.name;
         m_levelText.text = m_xiuLianContent.level + "/" + m_xiuLianContent.maxLevel;
         if(m_xiuLianContent.level < m_xiuLianContent.maxLevel)
         {
            m_progressBar.change(m_xiuLianContent.currentXiuLianValue / m_xiuLianContent.needXiuLianValue);
            m_progressBar.setDataShow("" + m_xiuLianContent.currentXiuLianValue + "/" + m_xiuLianContent.needXiuLianValue);
         }
         else
         {
            m_progressBar.change(1);
            m_progressBar.setDataShow("已满级");
         }
         m_needMoneyText.text = m_xiuLianData.getNeedMoney().toString();
         m_needPKPointText.text = m_xiuLianData.getNeedPKPoint().toString();
         m_numText.text = getRemainNumOfXiuLian().toString();
      }
      
      private function updateShow() : void
      {
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_startXiuLianBtn:
               startXiuLian();
               break;
            case m_quitBtn:
               m_closeSubUI.close();
         }
      }
      
      private function startXiuLian() : void
      {
         if(m_xiuLianContent.level >= m_xiuLianContent.maxLevel)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"该修炼已是最大等级！",
               "flag":0
            }));
            return;
         }
         if(m_player.playerVO.money < m_xiuLianData.getNeedMoney())
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足",
               "flag":0
            }));
            return;
         }
         if(m_player.getPKVO().getPKPoint() < m_xiuLianData.getNeedPKPoint())
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"PK点不足",
               "flag":0
            }));
            return;
         }
         if(getRemainNumOfXiuLian() == 0)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"是否花费" + m_xiuLianData.getBuyXiuLianNumPointTicket() + "点券购买修炼。",
               "flag":3,
               "task":{
                  "type":"buyXiuLianOne",
                  "okFunction":buyXiuLian
               }
            }));
            return;
         }
         xiuLian();
         m_player.playerVO.shiFuVO.addOneUsedXiuLianNum();
         updateShow();
      }
      
      private function buyXiuLian() : void
      {
         if(m_ticketBuyLogic == null)
         {
            return;
         }
         m_ticketBuyLogic.buy(m_xiuLianData.getBuyXiuLianNumPointTicket(),m_xiuLianData.getBuyXiuLianNumPointTicketId(),"购买修炼",function():void
         {
            xiuLian();
            updateShow();
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },function(param1:String, param2:int):void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":param1,
               "flag":param2
            }));
         });
      }
      
      private function xiuLian() : void
      {
         m_player.playerVO.money -= m_xiuLianData.getNeedMoney();
         PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint - m_xiuLianData.getNeedPKPoint();
         MyFunction.getInstance().addXiuLianValueToXiuLianContent(m_xiuLianContent,m_xiuLianData.getGetXiuLianValue(),function():void
         {
            MyFunction.getInstance().refreshXiuLianTargetVO(m_xiuLianContent.xiuLianTargetVO);
            if(m_xiuLianContent.owner == "ShiFu")
            {
               GamingUI.getInstance().refresh(203);
            }
         },null,function():void
         {
            GamingUI.getInstance().refresh(35);
         },null);
      }
      
      private function getRemainNumOfXiuLian() : uint
      {
         return Math.max(0,m_xiuLianData.getXiuLianNumOfOneDay() - m_player.playerVO.shiFuVO.getUsedXiuLianNum());
      }
   }
}

