package UI2.medalPanel
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyControlPanel;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import com.greensock.TweenLite;
   import com.greensock.easing.Expo;
   import flash.display.Sprite;
   
   public class MedalPanel extends MySprite
   {
      private var pageItemNum:int = 2;
      
      private var items:Vector.<MedalIPanelItem>;
      
      private var show:Sprite;
      
      private var btnClose:ButtonLogicShell2;
      
      private var btnNextPage:ButtonLogicShell2;
      
      private var btnPrePage:ButtonLogicShell2;
      
      private var btnInstruction:ButtonLogicShell2;
      
      private var btnCloseInstruction:ButtonLogicShell2;
      
      private var m_dragL:AbleDragSpriteLogicShell;
      
      private var m_closeSubUI:CloseSubUI;
      
      private var m_player:Player;
      
      private var m_myControlPanel:MyControlPanel;
      
      private var currentPage:int = 1;
      
      private var medalsArr:Array;
      
      public function MedalPanel()
      {
         super();
         m_dragL = new AbleDragSpriteLogicShell();
         items = new Vector.<MedalIPanelItem>();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         super.clear();
         ClearUtil.clearObject(btnClose);
         btnClose = null;
         ClearUtil.clearObject(btnNextPage);
         btnNextPage = null;
         ClearUtil.clearObject(btnPrePage);
         btnPrePage = null;
         ClearUtil.clearObject(btnInstruction);
         btnInstruction = null;
         ClearUtil.clearObject(btnCloseInstruction);
         btnCloseInstruction = null;
         ClearUtil.clearObject(medalsArr);
         medalsArr = null;
         ClearUtil.clearObject(items);
         items = null;
         ClearUtil.clearObject(m_dragL);
         m_dragL = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_player = null;
         m_myControlPanel = null;
      }
      
      public function init(param1:CloseSubUI, param2:MyControlPanel) : void
      {
         m_closeSubUI = param1;
         m_myControlPanel = param2;
         show = MyFunction2.returnShowByClassName("MedalPanel") as Sprite;
         addChild(show);
         initShow();
         if(m_player)
         {
            initShow2();
         }
      }
      
      public function setPlayer(param1:Player) : void
      {
         m_player = param1;
         initShow2();
      }
      
      private function initShow() : void
      {
         m_dragL.setShow(show);
         btnClose = new ButtonLogicShell2();
         btnClose.setShow(show["btnClose"]);
         btnPrePage = new ButtonLogicShell2();
         btnPrePage.setShow(show["btnPre"]);
         btnNextPage = new ButtonLogicShell2();
         btnNextPage.setShow(show["btnNext"]);
         btnInstruction = new ButtonLogicShell2();
         btnInstruction.setShow(show["btnInstruction"]);
         btnCloseInstruction = new ButtonLogicShell2();
         btnCloseInstruction.setShow(show["btnCloseInstruction"]);
         var _loc1_:int = 0;
         while(show["mcItem" + _loc1_])
         {
            items.push(new MedalIPanelItem(show["mcItem" + _loc1_],this));
            _loc1_++;
         }
         pageItemNum = _loc1_;
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         show["txtMedalNum"].text = String(m_player.playerVO.medalEquipmentVOs.length);
         hideInstruction();
         _loc3_ = 0;
         while(_loc3_ < show.numChildren)
         {
            if(show.getChildAt(_loc3_).name.indexOf("txtAttribute_") != -1)
            {
               show.getChildAt(_loc3_)["text"] = "0";
            }
            _loc3_++;
         }
         var _loc1_:Object = MedalFunction.getInstance().getALlMedalAttribute(m_player.playerVO.medalEquipmentVOs);
         for(var _loc2_ in _loc1_)
         {
            show["txtAttribute_" + _loc2_].text = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc2_,_loc1_[_loc2_])[1];
         }
         processMedals();
         currentPage = 1;
         showPage();
      }
      
      private function showPage() : void
      {
         var _loc2_:int = 0;
         show["txtCurrentMedal"].text = !!m_player.playerVO.medal ? m_player.playerVO.medal.name : "无";
         show["txtPage"].text = String(currentPage) + "/" + getMaxPageNum();
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < pageItemNum)
         {
            _loc1_ = _loc2_ + (currentPage - 1) * pageItemNum;
            if(_loc1_ < medalsArr.length)
            {
               items[_loc2_].setInfo(medalsArr[_loc1_],m_player.playerVO.medal && m_player.playerVO.medal.id == medalsArr[_loc1_][0]);
            }
            else
            {
               items[_loc2_].setInfo(null,false);
            }
            _loc2_++;
         }
      }
      
      private function getMaxPageNum() : int
      {
         return int((medalsArr.length - 1) / pageItemNum + 1);
      }
      
      private function processMedals() : void
      {
         medalsArr = [];
         var _loc3_:String = String(MedalFunction.getInstance().allMedalsXml);
         loop0:
         for each(var _loc1_ in MedalFunction.getInstance().allMedalsXml.item)
         {
            for each(var _loc2_ in m_player.playerVO.medalEquipmentVOs)
            {
               if(_loc2_.id == int(_loc1_.@id))
               {
                  medalsArr.unshift([int(_loc1_.@id),_loc2_.clone()]);
                  continue loop0;
               }
            }
            medalsArr.push([int(_loc1_.@id),null]);
         }
      }
      
      public function equipMedal(param1:int) : void
      {
         if(param1 == 0)
         {
            m_player.playerVO.medal = null;
         }
         else
         {
            for each(var _loc2_ in m_player.playerVO.medalEquipmentVOs)
            {
               if(_loc2_.id == param1)
               {
                  m_player.playerVO.medal = _loc2_;
                  break;
               }
            }
         }
         m_player.changeData();
         showPage();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case btnClose:
               m_closeSubUI.close();
               break;
            case btnNextPage:
               if(currentPage < getMaxPageNum())
               {
                  currentPage++;
                  showPage();
               }
               break;
            case btnPrePage:
               if(currentPage > 1)
               {
                  currentPage--;
                  showPage();
               }
               break;
            case btnInstruction:
               showInstruction();
               break;
            case btnCloseInstruction:
               hideInstruction();
         }
      }
      
      private function showInstruction() : void
      {
         show["mcInstruction"].visible = true;
         show["btnCloseInstruction"].visible = true;
      }
      
      private function hideInstruction() : void
      {
         show["mcInstruction"].visible = false;
         show["btnCloseInstruction"].visible = false;
      }
      
      public function playAddedAnimation() : void
      {
         TweenLite.from(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn
         });
      }
      
      public function playRemovedAnimation(param1:Function, param2:Array) : void
      {
         TweenLite.to(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn,
            "onComplete":param1,
            "onCompleteParams":param2
         });
      }
      
      public function getPlayer() : Player
      {
         return m_player;
      }
   }
}

