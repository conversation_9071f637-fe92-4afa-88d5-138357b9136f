package UI2.OnePlayerToTwo
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Task.TaskFunction;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.ResetPlayerTypeLogic;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.GameData;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class OnePlayerToTwoPanel extends MySprite
   {
      private const m_const_frameLabel:String = "action^stop^";
      
      private var m_playerAllTypes:Array;
      
      private var m_show:MovieClip;
      
      private var m_resetPlayerTypeData:OnePlayerToTwoData;
      
      private var m_isReadyOfResetPlayerTypeData:Boolean;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_playersShowBtnGroup:Sprite;
      
      private var m_upPlayerShowBtn:ButtonLogicShell2;
      
      private var m_downPlayerShowBtn:ButtonLogicShell2;
      
      private var m_newP1Btn:ButtonLogicShell2;
      
      private var m_newP2Btn:ButtonLogicShell2;
      
      private var m_explainBtn:ButtonLogicShell2;
      
      private var m_playersShow:MovieClipPlayLogicShell;
      
      private var m_playerShow:MovieClipPlayLogicShell;
      
      private var m_playerOneAnimation:AnimationShowPlayLogicShell;
      
      private var m_playerOneAnimationFrameListener:AnimationPlayFrameLabelListener;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_currentPlayerTypeIndex:int;
      
      private var m_resetPlayerTypeLogic:ResetPlayerTypeLogic;
      
      private var m_explainPanel:OnePlayerToTwoExplain;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_gamingUI:GamingUI;
      
      private var m_player_wantResetType:Player;
      
      private var m_player1:Player;
      
      private var m_loadSources:Array = ["NewRolePanel"];
      
      public function OnePlayerToTwoPanel()
      {
         super();
         m_playerAllTypes = [];
         m_playerAllTypes.push("SunWuKong");
         m_playerAllTypes.push("BaiLongMa");
         m_playerAllTypes.push("ErLangShen");
         m_playerAllTypes.push("ChangE");
         m_playerAllTypes.push("Fox");
         m_playerAllTypes.push("TieShan");
         m_playerAllTypes.push("Houyi");
         m_playerAllTypes.push("ZiXia");
         m_quitBtn = new ButtonLogicShell2();
         m_upPlayerShowBtn = new ButtonLogicShell2();
         m_downPlayerShowBtn = new ButtonLogicShell2();
         m_newP1Btn = new ButtonLogicShell2();
         m_newP2Btn = new ButtonLogicShell2();
         m_explainBtn = new ButtonLogicShell2();
         m_playersShow = new MovieClipPlayLogicShell();
         m_playerShow = new MovieClipPlayLogicShell();
         m_playerOneAnimation = new AnimationShowPlayLogicShell();
         m_playerOneAnimationFrameListener = new AnimationPlayFrameLabelListener();
         m_playerOneAnimationFrameListener.reachFrameLabelFun2 = reachFrameLabel;
         m_playerOneAnimation.addFrameLabelListener(m_playerOneAnimationFrameListener);
         m_resetPlayerTypeData = new OnePlayerToTwoData();
         m_currentPlayerTypeIndex = -1;
         m_resetPlayerTypeLogic = new ResetPlayerTypeLogic();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         GamingUI.getInstance().loadQueue.unLoad(m_loadSources);
         m_loadSources = [];
         m_loadSources = null;
         ClearUtil.clearObject(m_playerAllTypes);
         m_playerAllTypes = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_resetPlayerTypeData);
         m_resetPlayerTypeData = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_playersShowBtnGroup);
         m_playersShowBtnGroup = null;
         ClearUtil.clearObject(m_upPlayerShowBtn);
         m_upPlayerShowBtn = null;
         ClearUtil.clearObject(m_downPlayerShowBtn);
         m_downPlayerShowBtn = null;
         ClearUtil.clearObject(m_newP1Btn);
         m_newP1Btn = null;
         ClearUtil.clearObject(m_newP2Btn);
         m_newP2Btn = null;
         ClearUtil.clearObject(m_explainBtn);
         m_explainBtn = null;
         ClearUtil.clearObject(m_playersShow);
         m_playersShow = null;
         ClearUtil.clearObject(m_playerShow);
         m_playerShow = null;
         ClearUtil.clearObject(m_playerOneAnimation);
         m_playerOneAnimation = null;
         ClearUtil.clearObject(m_playerOneAnimationFrameListener);
         m_playerOneAnimationFrameListener = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_resetPlayerTypeLogic);
         m_resetPlayerTypeLogic = null;
         ClearUtil.clearObject(m_explainPanel);
         m_explainPanel = null;
         m_versionControl = null;
         m_loadUI = null;
         m_gamingUI = null;
         m_player_wantResetType = null;
         m_player1 = null;
         super.clear();
      }
      
      public function init(param1:Player, param2:GamingUI, param3:IVersionControl, param4:IProgressShow) : void
      {
         m_player_wantResetType = param1;
         m_gamingUI = param2;
         m_versionControl = param3;
         m_loadUI = param4;
         m_player1 = m_gamingUI.player1;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/OnePlayerToTwoPlayer.xml",getXMLSuccess,getFail);
         m_myLoader.getClass("UISprite2/NewAPlayerPanel.swf",m_loadSources[0],getShowSuccess,getFail);
         m_myLoader.load();
         m_isReadyOfResetPlayerTypeData = false;
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_playersShowBtnGroup = m_show["playersShowBtnGroup"];
         m_upPlayerShowBtn.setShow(m_playersShowBtnGroup["upBtn"]);
         m_downPlayerShowBtn.setShow(m_playersShowBtnGroup["downBtn"]);
         m_newP1Btn.setShow(m_show["newP1Btn"]);
         m_newP2Btn.setShow(m_show["newP2Btn"]);
         m_explainBtn.setShow(m_show["explainBtn"]);
         m_explainBtn.setTipString("点击查看具体说明");
         m_playersShow.setShow(m_show["playersShow"]);
         (m_show["playersShow"] as MovieClip).mouseEnabled = false;
         (m_show["playersShow"] as MovieClip).mouseEnabled = false;
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_isReadyOfResetPlayerTypeData == false)
         {
            return;
         }
         var _loc1_:String = downPlayerType();
         changePlayerShow(_loc1_);
      }
      
      private function changePlayerShow(param1:String) : void
      {
         m_playersShow.gotoAndStop(param1);
         m_playerShow.setShow(m_playersShow.getShow()["playerShow"]);
         m_playerShow.gotoAndStop("2");
         m_playerOneAnimation.setShow(m_playerShow.getShow().getChildAt(0));
         (m_playersShow.getShow()["playerShow"] as MovieClip).mouseEnabled = false;
         (m_playersShow.getShow()["playerShow"] as MovieClip).mouseChildren = false;
         m_playerOneAnimation.gotoAndPlay("1");
      }
      
      private function reachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         if(param2 == "action^stop^")
         {
            m_playerShow.gotoAndStop("1");
            m_playerOneAnimation.setShow(m_playerShow.getShow().getChildAt(0));
            m_playerOneAnimation.gotoAndPlay("1");
         }
      }
      
      private function downPlayerType() : String
      {
         ++m_currentPlayerTypeIndex;
         if(m_currentPlayerTypeIndex >= m_playerAllTypes.length)
         {
            m_currentPlayerTypeIndex = 0;
         }
         if(isFeasibleForCurrentPlayerType() == false)
         {
            downPlayerType();
         }
         return m_playerAllTypes[m_currentPlayerTypeIndex];
      }
      
      private function upPlayerType() : String
      {
         --m_currentPlayerTypeIndex;
         if(m_currentPlayerTypeIndex < 0)
         {
            m_currentPlayerTypeIndex = m_playerAllTypes.length - 1;
         }
         if(isFeasibleForCurrentPlayerType() == false)
         {
            upPlayerType();
         }
         return m_playerAllTypes[m_currentPlayerTypeIndex];
      }
      
      private function isFeasibleForCurrentPlayerType() : Boolean
      {
         if(m_currentPlayerTypeIndex == -1)
         {
            return false;
         }
         if(m_currentPlayerTypeIndex >= m_playerAllTypes.length)
         {
            return false;
         }
         var _loc1_:String = m_playerAllTypes[m_currentPlayerTypeIndex];
         if(m_player1.playerVO.playerType == _loc1_)
         {
            return false;
         }
         return true;
      }
      
      private function isAbleResetPlayerType() : Boolean
      {
         if(m_player_wantResetType.playerVO.level < m_resetPlayerTypeData.getMinLevel())
         {
            showWarningBox("等级不足,不能新增",0);
            return false;
         }
         return true;
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      private function openExplainPanel() : void
      {
         if(m_explainPanel == null)
         {
            m_explainPanel = new OnePlayerToTwoExplain();
            m_explainPanel.init(this);
            addChild(m_explainPanel);
            m_explainPanel.x = (stage.stageWidth - m_explainPanel.width) / 2;
            m_explainPanel.y = (stage.stageHeight - m_explainPanel.height) / 2;
         }
      }
      
      public function closeExplainPanel() : void
      {
         ClearUtil.clearObject(m_explainPanel);
         m_explainPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         switch(param1.button)
         {
            case m_upPlayerShowBtn:
               changePlayerShow(upPlayerType());
               break;
            case m_downPlayerShowBtn:
               changePlayerShow(downPlayerType());
               break;
            case m_quitBtn:
               m_gamingUI.closeOnePlayerToTwo();
               break;
            case m_newP1Btn:
               newPlayer1();
               break;
            case m_newP2Btn:
               newPlayer2();
               break;
            case m_explainBtn:
               openExplainPanel();
         }
      }
      
      private function newPlayer1() : void
      {
         showWarningBox("是否花费" + m_resetPlayerTypeData.getNeedTicketPrice() + "点券新增P1，新增成功后重新读档生效",3,{
            "type":"resetPlayerType",
            "okFunction":donewP1
         });
      }
      
      private function donewP1() : void
      {
         var price:int = int(m_resetPlayerTypeData.getNeedTicketPrice());
         var ticketId:String = m_resetPlayerTypeData.getNeedTiccketId();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买新增玩家";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            GamingUI.getInstance().player2 = GamingUI.getInstance().player1;
            GamingUI.getInstance().player1 = newPlayer();
            dotask();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            _loc2_.successFunc = zhuanzhi;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function zhuanzhi() : void
      {
         if(m_gamingUI)
         {
            m_gamingUI.closeOnePlayerToTwo();
         }
         Part1.getInstance().reStartGame();
      }
      
      private function dotask() : void
      {
         var _loc2_:MTaskVO = null;
         var _loc3_:String = null;
         var _loc4_:int = TasksManager.getInstance().activityTaskVOs.length - 1;
         var _loc1_:Boolean = false;
         while(_loc4_ >= 0)
         {
            _loc2_ = TasksManager.getInstance().activityTaskVOs[_loc4_];
            if(_loc2_.id == 13026)
            {
               _loc1_ = true;
               break;
            }
            _loc4_--;
         }
         if(_loc1_)
         {
            TasksManager.getInstance().acceptedActivityTaskVOs.push(_loc2_);
            _loc3_ = MyFunction.getInstance().splitTimeString(TimeUtil.timeStr);
            (_loc2_ as AccumulatedTaskVO).deteDate = _loc3_;
            TasksManager.getInstance().activityTaskVOs.splice(_loc4_,1);
         }
         TaskFunction.getInstance().addTaskGoalVOByString("oneToTwo");
      }
      
      private function save2() : void
      {
         var saveinfo:SaveTaskInfo = new SaveTaskInfo();
         saveinfo.type = "4399";
         saveinfo.isHaveData = false;
         saveinfo.successFunc = function():void
         {
            m_gamingUI.closeOnePlayerToTwo();
            Part1.getInstance().reStartGame();
         };
         saveinfo.failFunc = function():void
         {
            showWarningBox("存档失败！请重新保存游戏！",1,{
               "type":"oneplayertotwo",
               "okFunction":save2
            });
         };
         SaveTaskList.getInstance().addData(saveinfo);
         MyFunction2.saveGame();
      }
      
      private function newPlayer2() : void
      {
         showWarningBox("是否花费" + m_resetPlayerTypeData.getNeedTicketPrice() + "点券新增P2，新增成功后重新读档生效",3,{
            "type":"resetPlayerType",
            "okFunction":donewP2
         });
      }
      
      private function donewP2() : void
      {
         var price:int = int(m_resetPlayerTypeData.getNeedTicketPrice());
         var ticketId:String = m_resetPlayerTypeData.getNeedTiccketId();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买新增玩家";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            GamingUI.getInstance().player2 = newPlayer();
            dotask();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            _loc2_.successFunc = zhuanzhi;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function newPlayer() : Player
      {
         var _loc6_:Player = null;
         var _loc11_:String = m_playerAllTypes[m_currentPlayerTypeIndex];
         var _loc4_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc1_:XML = XMLSingle.getInstance().skillXML;
         var _loc3_:XML = XMLSingle.getInstance().talentXML;
         var _loc9_:XML = XMLSingle.getInstance().privilegeXML;
         var _loc7_:XML = XMLSingle.getInstance().taskXML;
         var _loc5_:XML = XMLSingle.getInstance().dataXML;
         var _loc12_:XML = XMLSingle.getInstance().buffXML;
         var _loc10_:XML = XMLSingle.getInstance().farmXML;
         var _loc8_:XML = Part1.getInstance().m_part2._xml2;
         var _loc2_:String = TimeUtil.timeStr;
         switch(_loc11_)
         {
            case "SunWuKong":
               _loc6_ = InitUI.getInstance().initMonkey(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "BaiLongMa":
               _loc6_ = InitUI.getInstance().initDragon(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "ErLangShen":
               _loc6_ = InitUI.getInstance().initErLangShen(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "ChangE":
               _loc6_ = InitUI.getInstance().initChangE(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "Fox":
               _loc6_ = InitUI.getInstance().initFox(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "TieShan":
               _loc6_ = InitUI.getInstance().initTieShan(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "Houyi":
               _loc6_ = InitUI.getInstance().initHouYi(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            case "ZiXia":
               _loc6_ = InitUI.getInstance().initZiXia(_loc8_,_loc4_,_loc1_,_loc3_,_loc5_,_loc2_,3839,"playerTwo");
               break;
            default:
               throw new Error(_loc11_ + "不存在的人物类型！");
         }
         _loc6_.setMountsVO(GamingUI.getInstance().player1.getMountsVO());
         _loc6_.vipVO = GamingUI.getInstance().player1.vipVO;
         _loc6_.setsocietyDataVO(GamingUI.getInstance().player1.getSocietyDataVO());
         _loc6_.setPKMode2VO1(GamingUI.getInstance().player1.getPKMode2VO1());
         _loc6_.setPKVO(GamingUI.getInstance().player1.getPKVO());
         return _loc6_;
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_isReadyOfResetPlayerTypeData = true;
         initShow();
         initShow2();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_resetPlayerTypeData.initByXML(param1.resultXML);
         initShow2();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeOnePlayerToTwo();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
   }
}

