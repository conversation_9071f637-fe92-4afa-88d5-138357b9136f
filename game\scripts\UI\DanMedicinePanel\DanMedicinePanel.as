package UI.DanMedicinePanel
{
   import UI.Button.QuitBtn3;
   import UI.DanMedicinePanel.Btn.AttackDanSwitchBtn;
   import UI.DanMedicinePanel.Btn.DefenceDanSwitchBtn;
   import UI.DanMedicinePanel.Cell.DanEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.Players.Player;
   import com.greensock.TweenLite;
   import com.greensock.easing.Expo;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class DanMedicinePanel extends MySprite
   {
      public static const REALITY_TOTAL_LINE_NUM:int = 7;
      
      public static const START_LINE_NUM:int = 2;
      
      public static const EVERY_LINE_INCREASE_NUM:int = 1;
      
      public static const LOCK_LINE_NUM:int = 0;
      
      public static const ATTACK_DAN_MEDICINE:String = "attackDM";
      
      public static const DEFENCE_DAN_MEDICINE:String = "defenceDM";
      
      public var attackDanSwitchBtn:AttackDanSwitchBtn;
      
      public var defenceDanSwitchBtn:DefenceDanSwitchBtn;
      
      public var quitBtn3:QuitBtn3;
      
      private const _FIRST_CELL_X:Number = 125;
      
      private const _FIRST_CELL_Y:Number = 130;
      
      private var _danEquipmentCellGrid:Vector.<Vector.<DanEquipmentCell>>;
      
      private var _attackEquipmentVOGrid:Vector.<Vector.<EquipmentVO>>;
      
      private var _defenceEquipmentVOGrid:Vector.<Vector.<EquipmentVO>>;
      
      private var _totalAttributeNameText:TextField;
      
      private var _totalAttributeValueText:TextField;
      
      private var _currentState:String;
      
      private var _player:Player;
      
      public function DanMedicinePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc4_:DisplayObject = null;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc4_ = getChildAt(0);
            removeChildAt(0);
            if(_loc4_.hasOwnProperty("clear"))
            {
               _loc4_["clear"]();
            }
         }
         if(attackDanSwitchBtn)
         {
            attackDanSwitchBtn.clear();
         }
         attackDanSwitchBtn = null;
         if(defenceDanSwitchBtn)
         {
            defenceDanSwitchBtn.clear();
         }
         defenceDanSwitchBtn = null;
         if(quitBtn3)
         {
            quitBtn3.clear();
         }
         quitBtn3 = null;
         if(_danEquipmentCellGrid)
         {
            _loc1_ = int(_danEquipmentCellGrid.length);
            _loc5_ = 0;
            while(_loc5_ < _loc1_)
            {
               if(_danEquipmentCellGrid[_loc5_])
               {
                  _loc2_ = int(_danEquipmentCellGrid[_loc5_].length);
                  _loc3_ = 0;
                  while(_loc3_ < _loc2_)
                  {
                     if(_danEquipmentCellGrid[_loc5_][_loc3_])
                     {
                        _danEquipmentCellGrid[_loc5_][_loc3_].clear();
                     }
                     _danEquipmentCellGrid[_loc5_][_loc3_] = null;
                     _loc3_++;
                  }
                  _danEquipmentCellGrid[_loc5_] = null;
               }
               _loc5_++;
            }
            _danEquipmentCellGrid = null;
         }
         _attackEquipmentVOGrid = null;
         _defenceEquipmentVOGrid = null;
         _totalAttributeNameText = null;
         _totalAttributeValueText = null;
         _player = null;
         TweenLite.killTweensOf(this);
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      public function playAddedAnimation() : void
      {
         TweenLite.from(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn
         });
      }
      
      public function playRemovedAnimation(param1:Function, param2:Array) : void
      {
         TweenLite.to(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn,
            "onComplete":param1,
            "onCompleteParams":param2
         });
      }
      
      public function showWantAddCellBorder() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc1_ = int(_danEquipmentCellGrid.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = int(_danEquipmentCellGrid[_loc4_].length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(!_danEquipmentCellGrid[_loc4_][_loc3_].isHaveChild)
               {
                  _danEquipmentCellGrid[_loc4_][_loc3_].showBorder();
                  return;
               }
               _loc3_++;
            }
            _loc4_++;
         }
      }
      
      public function hideBorder() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc1_ = int(_danEquipmentCellGrid.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = int(_danEquipmentCellGrid[_loc4_].length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               _danEquipmentCellGrid[_loc4_][_loc3_].hideBorder();
               _loc3_++;
            }
            _loc4_++;
         }
      }
      
      public function refreshPanel(param1:Vector.<Vector.<EquipmentVO>>, param2:Vector.<Vector.<EquipmentVO>>) : void
      {
         _attackEquipmentVOGrid = param1;
         _defenceEquipmentVOGrid = param2;
         var _loc3_:String = _currentState;
         if("attackDM" !== _loc3_)
         {
            arrangeEquipmentVOGrideToUsedEquipmentCellGride(_defenceEquipmentVOGrid);
            _totalAttributeNameText.text = "防御力：";
         }
         else
         {
            arrangeEquipmentVOGrideToUsedEquipmentCellGride(_attackEquipmentVOGrid);
            _totalAttributeNameText.text = "攻击力：";
         }
      }
      
      private function init() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         _totalAttributeNameText = new TextField();
         _totalAttributeValueText = new TextField();
         _totalAttributeNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16777215);
         _totalAttributeValueText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16776960);
         _totalAttributeNameText.selectable = false;
         _totalAttributeValueText.selectable = false;
         _totalAttributeNameText.x = 300;
         _totalAttributeNameText.y = 120;
         _totalAttributeNameText.width = 80;
         _totalAttributeNameText.height = 30;
         _totalAttributeValueText.x = _totalAttributeNameText.x + _totalAttributeNameText.width + 5;
         _totalAttributeValueText.y = _totalAttributeNameText.y;
         addChild(_totalAttributeNameText);
         addChild(_totalAttributeValueText);
         attackDanSwitchBtn.init(false);
         defenceDanSwitchBtn.init(true);
         _currentState = "attackDM";
         drawCellGrid();
      }
      
      private function arrangeEquipmentVOGrideToUsedEquipmentCellGride(param1:Vector.<Vector.<EquipmentVO>>) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = int(param1[_loc5_].length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               _danEquipmentCellGrid[_loc5_][_loc4_].removeEquipmentVO();
               _danEquipmentCellGrid[_loc5_][_loc4_].addEquipmentVO(param1[_loc5_][_loc4_]);
               _loc4_++;
            }
            _loc5_++;
         }
         _totalAttributeValueText.text = DanMedicineFunction.getInstance().calculationTotalValue(param1).toString();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchToAttackDan",switchPanel,true,0,true);
         addEventListener("switchToDefenceDan",switchPanel,true,0,true);
         addEventListener("mouseDown",mouseDownBox,false,0,true);
         addEventListener("mouseUp",mouseUpBox,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchToAttackDan",switchPanel,true);
         removeEventListener("switchToDefenceDan",switchPanel,true);
         removeEventListener("mouseDown",mouseDownBox,false);
         removeEventListener("mouseUp",mouseUpBox,false);
      }
      
      private function switchPanel(param1:UIBtnEvent) : void
      {
         var _loc2_:* = param1.type;
         if("switchToAttackDan" !== _loc2_)
         {
            attackDanSwitchBtn.gotoTwoFrame();
            _currentState = "defenceDM";
            _totalAttributeNameText.text = "防御力：";
            arrangeEquipmentVOGrideToUsedEquipmentCellGride(_defenceEquipmentVOGrid);
         }
         else
         {
            defenceDanSwitchBtn.gotoTwoFrame();
            _currentState = "attackDM";
            _totalAttributeNameText.text = "攻击力：";
            arrangeEquipmentVOGrideToUsedEquipmentCellGride(_attackEquipmentVOGrid);
         }
      }
      
      private function drawCellGrid() : void
      {
         var _loc4_:int = 0;
         var _loc3_:LockCell_DanMedicinePanel = null;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         _danEquipmentCellGrid = new Vector.<Vector.<DanEquipmentCell>>(7);
         _loc6_ = 0;
         while(_loc6_ < 7)
         {
            _loc4_ = 2 + _loc6_ * 1;
            if(_loc6_ < 7)
            {
               _danEquipmentCellGrid[_loc6_] = new Vector.<DanEquipmentCell>(_loc4_);
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _danEquipmentCellGrid[_loc6_][_loc5_] = new DanEquipmentCell();
                  _danEquipmentCellGrid[_loc6_][_loc5_].x = 125 + _loc5_ * 50;
                  _danEquipmentCellGrid[_loc6_][_loc5_].y = 130 + _loc6_ * 50;
                  addChild(_danEquipmentCellGrid[_loc6_][_loc5_]);
                  _loc5_++;
               }
            }
            else
            {
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _loc3_ = new LockCell_DanMedicinePanel();
                  _loc3_.x = 125 + _loc5_ * 50;
                  _loc3_.y = 130 + _loc6_ * 50;
                  addChild(_loc3_);
                  _loc5_++;
               }
            }
            _loc6_++;
         }
      }
      
      private function mouseDownBox(param1:MouseEvent) : void
      {
         if(parent)
         {
            if(parent is MyControlPanel)
            {
               parent.setChildIndex(this,parent.getChildIndex((parent as MyControlPanel).topLayer) - 1);
            }
            else
            {
               parent.setChildIndex(this,parent.numChildren - 1);
            }
         }
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      private function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      private function test(param1:Event) : void
      {
         var _loc2_:Point = null;
         if(stage)
         {
            _loc2_ = localToGlobal(new Point(mouseX,mouseY));
            if(_loc2_.x > stage.stageWidth)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x -= 10;
            }
            if(_loc2_.x < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x += 10;
            }
            if(_loc2_.y > stage.stageHeight)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y -= 10;
            }
            if(_loc2_.y < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y += 10;
            }
         }
      }
   }
}

