package UI2.Midautumn
{
   import Json.MyJSON;
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   
   public class MidautumnResult
   {
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/bobing/alter";
      
      private var m_rewardEquipments:Vector.<Equipment>;
      
      private var m_eqCells:Vector.<MovieClip>;
      
      private var m_allshow:MovieClip;
      
      private var m_show:MovieClip;
      
      private var m_title:MovieClip;
      
      private var m_txtInfo:MovieClip;
      
      private var m_btnOK:ButtonLogicShell2;
      
      private var m_checkList:Vector.<MovieClip>;
      
      private var m_index:int = -1;
      
      private var m_midautumn:MidautumnView;
      
      private var m_result:int;
      
      private var m_nX:Number;
      
      private var m_nY:Number;
      
      private var m_down:Boolean = false;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var m_itemlist:Vector.<EquipmentVO>;
      
      public function MidautumnResult()
      {
         super();
         m_rewardEquipments = new Vector.<Equipment>();
         m_eqCells = new Vector.<MovieClip>();
         m_checkList = new Vector.<MovieClip>();
         m_btnOK = new ButtonLogicShell2();
         m_itemlist = new Vector.<EquipmentVO>();
         equipmentVOsData = new EquipmentVOsData();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            m_eqCells[_loc1_].removeEventListener("click",callback);
            m_eqCells[_loc1_] = null;
            _loc1_++;
         }
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(m_btnOK);
         m_btnOK = null;
         m_midautumn = null;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         ClearUtil.clearObject(m_checkList);
         m_checkList = null;
         m_allshow.removeEventListener("showMessageBox",showMessageBox,true);
         m_allshow.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_allshow.removeEventListener("showMessageBox",showMessageBox,false);
         m_allshow.removeEventListener("hideMessageBox",hideMessageBox,false);
         m_show.removeEventListener("clickButton",clickButton,true);
         m_allshow.removeEventListener("mouseDown",calldown,true);
         m_allshow.removeEventListener("mouseUp",callup,true);
      }
      
      public function init(param1:MovieClip, param2:MidautumnView) : void
      {
         var _loc3_:int = 0;
         m_allshow = param1;
         m_show = param1["movepanel"] as MovieClip;
         m_title = m_show["resultTitle"] as MovieClip;
         m_txtInfo = m_show["txtInfo"] as MovieClip;
         m_btnOK.setShow(m_show["btnOK"] as MovieClip);
         m_btnOK.setTipString("确定");
         m_midautumn = param2;
         m_eqCells.length = 0;
         _loc3_ = 0;
         while(_loc3_ < 8)
         {
            m_eqCells.push(m_show["eqcell_" + (_loc3_ + 1)] as MovieClip);
            m_checkList.push(m_eqCells[_loc3_]["checkbg"] as MovieClip);
            m_checkList[_loc3_].visible = false;
            m_eqCells[_loc3_].addEventListener("click",callback);
            _loc3_++;
         }
         m_nX = m_show.x;
         m_nY = m_show.y;
         m_allshow.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_allshow.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_allshow.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_allshow.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_show.addEventListener("clickButton",clickButton,true);
         m_allshow.addEventListener("mouseDown",calldown);
         m_allshow.addEventListener("mouseUp",callup);
      }
      
      private function calldown(param1:MouseEvent) : void
      {
         m_down = true;
         m_nX = m_allshow.mouseX - m_show.x;
         m_nY = m_allshow.mouseY - m_show.y;
      }
      
      private function callup(param1:MouseEvent) : void
      {
         m_down = false;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_down)
         {
            m_show.x = m_allshow.mouseX - m_nX;
            m_show.y = m_allshow.mouseY - m_nY;
         }
      }
      
      private function sendInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorHandler);
         _loc1_.addEventListener("securityError",onErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strOpenUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc4_:Object = {};
         _loc4_.MD5 = "2B01530154A2C991";
         _loc4_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc4_.Rid = MidautumnData.getInstance().getRid();
         var _loc3_:Array = [];
         _loc3_.push(MidautumnData.getInstance().getItemByType(m_result,m_index - 1).id);
         _loc4_.Items = Object(_loc3_);
         _loc2_.data = MyJSON.encode(_loc4_);
         _loc1_.load(_loc2_);
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         if(int(_loc2_.Result) != 1)
         {
            GamingUI.getInstance().showMessageTip("服务器反馈错误码");
         }
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求数据错误!");
      }
      
      private function hideAll() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_checkList.length)
         {
            m_checkList[_loc1_].visible = false;
            _loc1_++;
         }
      }
      
      private function callback(param1:MouseEvent) : void
      {
         m_index = int(String(param1.currentTarget.name).split("_")[1]);
         if(m_index > MidautumnData.getInstance().getListByType(m_result).length)
         {
            m_index = -1;
            return;
         }
         hideAll();
         if(m_checkList[m_index - 1] && MidautumnData.getInstance().getItemByType(m_result,m_index - 1))
         {
            m_checkList[m_index - 1].visible = true;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:SaveTaskInfo = null;
         var _loc3_:* = param1.button;
         if(m_btnOK === _loc3_)
         {
            if(m_index > 0)
            {
               loadInBag(m_index - 1);
               _loc2_ = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
               m_midautumn.hideResult();
               sendInfo();
               m_midautumn.unlockBtn();
            }
            else
            {
               GamingUI.getInstance().showMessageTip("请选择其中一个奖励!");
            }
         }
      }
      
      private function loadInBag(param1:int) : void
      {
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         m_itemlist = new Vector.<EquipmentVO>();
         m_itemlist.push(m_rewardEquipments[param1].equipmentVO.clone());
         equipmentVOsData.setEquipmentVOs(m_itemlist);
         if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player1))
         {
            GamingUI.getInstance().showMessageTip("领取成功");
         }
         else
         {
            GamingUI.getInstance().showMessageTip("背包已满");
         }
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_allshow.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showRewardByResult(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Equipment = null;
         hideAll();
         m_index = 0;
         m_result = param1;
         if(param1 == 9)
         {
            m_txtInfo.gotoAndStop("1");
            m_title.gotoAndStop("1");
         }
         else
         {
            m_txtInfo.gotoAndStop("2");
            m_title.gotoAndStop(String(param1 + 1));
         }
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments.length = 0;
         var _loc2_:int = Math.min(m_eqCells.length,MidautumnData.getInstance().getListByType(param1).length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(MidautumnData.getInstance().getItemByType(param1,_loc4_).clone());
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_rewardEquipments.push(_loc3_);
            m_eqCells[_loc4_].addChild(_loc3_);
            _loc4_++;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_allshow.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_allshow.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

