package UI.newTask.EveyDayTask
{
   import UI.Task.TaskVO.MTaskVO;
   import UI.newTask.NewTaskPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class NewEveryDayPanel
   {
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_neweverymainlist:NewEveryMainList;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_neweverydetail:NewEveryDetail;
      
      private var m_neweverybtnshell:NewEveryBtnShell;
      
      public function NewEveryDayPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_neweverymainlist);
         m_neweverymainlist = null;
         ClearUtil.clearObject(m_neweverydetail);
         m_neweverydetail = null;
         ClearUtil.clearObject(m_neweverybtnshell);
         m_neweverybtnshell = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip) : void
      {
         m_newtaskpanel = param1;
         m_show = param2;
         m_mc = m_show["tasklistmc"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_neweverymainlist = new NewEveryMainList();
         m_neweverymainlist.init(m_newtaskpanel,m_show,this);
         m_neweverydetail = new NewEveryDetail();
         m_neweverydetail.init(m_newtaskpanel,m_show,this);
         m_neweverybtnshell = new NewEveryBtnShell();
         m_neweverybtnshell.init(m_newtaskpanel,m_show,this);
      }
      
      public function show(param1:String) : void
      {
         m_neweverydetail.show();
         m_neweverybtnshell.show();
         m_neweverymainlist.show(param1);
      }
      
      public function hide() : void
      {
         m_neweverymainlist.hide();
         m_neweverydetail.hide();
         m_neweverybtnshell.hide();
      }
      
      public function refreshlist() : void
      {
         m_neweverymainlist.refreshlist();
      }
      
      public function refreshScript(param1:MTaskVO) : void
      {
         m_neweverydetail.refreshScript(param1);
         m_neweverybtnshell.refreshScript(param1);
      }
   }
}

