package UI.WorldBoss
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class BossDetailPanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_abeDragLS:AbleDragSpriteLogicShell;
      
      private var m_backgroud:AnimationShowPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_bossId:String;
      
      public function BossDetailPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearDisplayObjectInContainer(m_show);
         m_show = null;
         super.clear();
         ClearUtil.clearObject(m_backgroud);
         m_backgroud = null;
         ClearUtil.clearObject(m_abeDragLS);
         m_abeDragLS = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_myLoader = null;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setBossId(param1:String) : void
      {
         m_bossId = param1;
      }
      
      public function init() : void
      {
         m_myLoader.getClass("BossDetail.swf","BossDetailPanel",getShowSuccessFun,getFailFun);
         m_myLoader.load();
      }
      
      private function getShowSuccessFun(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         if(stage)
         {
            m_show.x = (stage.stageWidth - m_show.width) / 2;
            m_show.y = (stage.stageHeight - m_show.height) / 2;
         }
         m_backgroud = new AnimationShowPlayLogicShell();
         m_backgroud.setShow(m_show["background"] as DisplayObject);
         m_abeDragLS = new AbleDragSpriteLogicShell();
         m_abeDragLS.setShow(m_show);
         m_backgroud.gotoAndStop(m_bossId);
         m_quitBtn = new ButtonLogicShell();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            clear();
         }
      }
   }
}

