package UI2.Consumer
{
   import UI.Equipments.Equipment;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class ConsumerItem
   {
      private var m_view:ConsumerView;
      
      private var m_show:MovieClip;
      
      private var m_nowPage:int = 1;
      
      private var m_totalPage:int = 0;
      
      private var m_typeId:String;
      
      private var m_infoList:Vector.<MovieClip>;
      
      private var m_nAvgPage:int = 3;
      
      private var m_btnGetlist:Vector.<ButtonLogicShell2>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData1:EquipmentVOsData;
      
      private var equipments1:Vector.<Equipment>;
      
      private var equipmentVOsData2:EquipmentVOsData;
      
      private var equipments2:Vector.<Equipment>;
      
      private var equipmentVOsData3:EquipmentVOsData;
      
      private var equipments3:Vector.<Equipment>;
      
      private var equipmentVOsData4:EquipmentVOsData;
      
      public function ConsumerItem()
      {
         super();
         m_infoList = new Vector.<MovieClip>();
         m_btnGetlist = new Vector.<ButtonLogicShell2>();
         equipmentVOsData1 = new EquipmentVOsData();
         equipments1 = new Vector.<Equipment>();
         equipmentVOsData2 = new EquipmentVOsData();
         equipments2 = new Vector.<Equipment>();
         equipmentVOsData3 = new EquipmentVOsData();
         equipments3 = new Vector.<Equipment>();
         equipmentVOsData4 = new EquipmentVOsData();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(equipmentVOsData1);
         equipmentVOsData1 = null;
         ClearUtil.clearObject(equipments1);
         equipments1 = null;
         ClearUtil.clearObject(equipmentVOsData2);
         equipmentVOsData2 = null;
         ClearUtil.clearObject(equipments2);
         equipments2 = null;
         ClearUtil.clearObject(equipmentVOsData3);
         equipmentVOsData3 = null;
         ClearUtil.clearObject(equipments3);
         equipments3 = null;
         ClearUtil.clearObject(equipmentVOsData4);
         equipmentVOsData4 = null;
         ClearUtil.clearObject(m_infoList);
         m_infoList = null;
         ClearUtil.clearObject(m_btnGetlist);
         m_btnGetlist = null;
      }
      
      public function setParams(param1:ConsumerView, param2:MovieClip) : void
      {
         m_view = param1;
         m_show = param2;
         init();
      }
      
      private function init() : void
      {
         var _loc1_:ButtonLogicShell2 = null;
         m_infoList.push(m_show["infoitem_1"] as MovieClip);
         m_infoList.push(m_show["infoitem_2"] as MovieClip);
         m_infoList.push(m_show["infoitem_3"] as MovieClip);
         _loc1_ = new ButtonLogicShell2();
         _loc1_.setShow(m_infoList[0]["btnCanGet"]);
         m_btnGetlist.push(_loc1_);
         _loc1_ = new ButtonLogicShell2();
         _loc1_.setShow(m_infoList[1]["btnCanGet"]);
         m_btnGetlist.push(_loc1_);
         _loc1_ = new ButtonLogicShell2();
         _loc1_.setShow(m_infoList[2]["btnCanGet"]);
         m_btnGetlist.push(_loc1_);
      }
      
      public function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc3_:PayInfoItem = null;
         var _loc2_:SaveTaskInfo = null;
         _loc4_ = 0;
         while(_loc4_ < m_btnGetlist.length)
         {
            if(param1.button == m_btnGetlist[_loc4_] && m_btnGetlist[_loc4_].isLock == false)
            {
               _loc3_ = m_btnGetlist[_loc4_].extraData as PayInfoItem;
               equipmentVOsData4.setEquipmentVOs(XMLSingle.getEquipmentVOs(_loc3_.rewardXML,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
               if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData4,GamingUI.getInstance().player1))
               {
                  m_view.showWarningBox("获取礼包成功",0);
                  (m_btnGetlist[_loc4_].getShow() as MovieClip).gotoAndStop("geted");
                  m_btnGetlist[_loc4_].isLock = true;
                  ConsumerData.getInstance().addData(_loc3_);
                  _loc2_ = new SaveTaskInfo();
                  _loc2_.type = "4399";
                  _loc2_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc2_);
                  MyFunction2.saveGame();
                  m_view.refresh();
               }
               else
               {
                  m_view.showWarningBox("背包已满, 不能获取礼包",0);
               }
            }
            _loc4_++;
         }
      }
      
      public function nextPage() : void
      {
         if(m_nowPage + 1 > m_totalPage)
         {
            return;
         }
         if(m_typeId == "1001")
         {
            refreshPage(1,m_nowPage + 1);
         }
         else if(m_typeId == "1002")
         {
            refreshPage(2,m_nowPage + 1);
         }
      }
      
      public function prePage() : void
      {
         if(m_nowPage - 1 < 1)
         {
            return;
         }
         if(m_typeId == "1001")
         {
            refreshPage(1,m_nowPage - 1);
         }
         else if(m_typeId == "1002")
         {
            refreshPage(2,m_nowPage - 1);
         }
      }
      
      public function refresh() : void
      {
         if(m_typeId == "1001")
         {
            refreshPage(1,m_nowPage);
         }
         else if(m_typeId == "1002")
         {
            refreshPage(2,m_nowPage);
         }
      }
      
      public function refreshPage(param1:int, param2:int) : void
      {
         var _loc5_:String = null;
         var _loc4_:Number = NaN;
         var _loc3_:PayInfoItem = null;
         var _loc6_:int = 0;
         if(param1 == 1)
         {
            _loc5_ = "1001";
            _loc4_ = ConsumerData.getInstance().getDayPaied();
         }
         else
         {
            if(param1 != 2)
            {
               return;
            }
            _loc5_ = "1002";
            _loc4_ = ConsumerData.getInstance().getWeekPaied();
         }
         if(ConsumerData.getInstance().getLenById(_loc5_) % m_nAvgPage == 0)
         {
            m_totalPage = ConsumerData.getInstance().getLenById(_loc5_) / m_nAvgPage;
         }
         else
         {
            m_totalPage = ConsumerData.getInstance().getLenById(_loc5_) / m_nAvgPage + 1;
         }
         if(param2 > m_totalPage || param2 < 1)
         {
            return;
         }
         m_nowPage = param2;
         m_typeId = _loc5_;
         _loc6_ = (m_nowPage - 1) * m_nAvgPage;
         while(_loc6_ < m_nAvgPage)
         {
            _loc3_ = ConsumerData.getInstance().getDataById(m_typeId,_loc6_);
            if(_loc3_)
            {
               (m_infoList[_loc6_]["txtpay"] as TextField).text = String(_loc3_.pay);
               m_btnGetlist[_loc6_].extraData = _loc3_;
               if(_loc3_.pay <= _loc4_ && ConsumerData.getInstance().getBGetById(_loc3_.id) == false)
               {
                  (m_infoList[_loc6_]["txtpay_1"] as TextField).textColor = 65280;
                  m_btnGetlist[_loc6_].unLock();
               }
               else if(_loc3_.pay > _loc4_ && ConsumerData.getInstance().getBGetById(_loc3_.id) == false)
               {
                  (m_infoList[_loc6_]["txtpay_1"] as TextField).textColor = 16711680;
                  m_btnGetlist[_loc6_].lock();
               }
               else
               {
                  (m_infoList[_loc6_]["txtpay_1"] as TextField).textColor = 65280;
                  (m_btnGetlist[_loc6_].getShow() as MovieClip).gotoAndStop("geted");
                  m_btnGetlist[_loc6_].isLock = true;
               }
               if(_loc6_ == 0)
               {
                  showEq1(_loc3_);
               }
               else if(_loc6_ == 1)
               {
                  showEq2(_loc3_);
               }
               else if(_loc6_ == 2)
               {
                  showEq3(_loc3_);
               }
               (m_infoList[_loc6_]["txtpay_3"] as TextField).text = String(_loc3_.pay);
               if(_loc4_ <= 0)
               {
                  (m_infoList[_loc6_]["txtpay_1"] as TextField).text = "0";
               }
               else
               {
                  (m_infoList[_loc6_]["txtpay_1"] as TextField).text = String(_loc4_);
               }
            }
            else
            {
               m_infoList[_loc6_].visible = false;
            }
            _loc6_++;
         }
         (m_show["txtpage"] as TextField).text = String(m_nowPage) + "/" + String(m_totalPage);
         (m_show["txtpage"] as TextField).mouseEnabled = false;
      }
      
      private function showEq1(param1:PayInfoItem) : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         equipmentVOsData1.setEquipmentVOs(XMLSingle.getEquipmentVOs(param1.rewardXML,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         ClearUtil.clearObject(equipments1);
         equipments1 = null;
         equipments1 = new Vector.<Equipment>();
         _loc3_ = 0;
         while(_loc3_ < equipmentVOsData1.getEquipmentVONum())
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData1.getEquipmentVOByIndex(_loc3_).clone());
            _loc2_.x = 27.5;
            _loc2_.y = 27.5;
            m_infoList[0]["cell_" + (_loc3_ + 1)].addChild(_loc2_);
            equipments1.push(_loc2_);
            _loc2_.addEventListener("rollOver",m_view.onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",m_view.onOut2,false,0,true);
            _loc3_++;
         }
      }
      
      private function showEq2(param1:PayInfoItem) : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         equipmentVOsData2.setEquipmentVOs(XMLSingle.getEquipmentVOs(param1.rewardXML,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         ClearUtil.clearObject(equipments2);
         equipments2 = null;
         equipments2 = new Vector.<Equipment>();
         _loc3_ = 0;
         while(_loc3_ < equipmentVOsData2.getEquipmentVONum())
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData2.getEquipmentVOByIndex(_loc3_).clone());
            _loc2_.x = 27.5;
            _loc2_.y = 27.5;
            m_infoList[1]["cell_" + (_loc3_ + 1)].addChild(_loc2_);
            equipments2.push(_loc2_);
            _loc2_.addEventListener("rollOver",m_view.onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",m_view.onOut2,false,0,true);
            _loc3_++;
         }
      }
      
      private function showEq3(param1:PayInfoItem) : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         equipmentVOsData3.setEquipmentVOs(XMLSingle.getEquipmentVOs(param1.rewardXML,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         ClearUtil.clearObject(equipments3);
         equipments3 = null;
         equipments3 = new Vector.<Equipment>();
         _loc3_ = 0;
         while(_loc3_ < equipmentVOsData3.getEquipmentVONum())
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData3.getEquipmentVOByIndex(_loc3_).clone());
            _loc2_.x = 27.5;
            _loc2_.y = 27.5;
            m_infoList[2]["cell_" + (_loc3_ + 1)].addChild(_loc2_);
            equipments3.push(_loc2_);
            _loc2_.addEventListener("rollOver",m_view.onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",m_view.onOut2,false,0,true);
            _loc3_++;
         }
      }
   }
}

