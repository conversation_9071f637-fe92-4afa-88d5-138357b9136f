package UI.PKUI
{
   import UI.Buff.Buff.BuffDrive;
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.Players.Player;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class PlayerDataForPK extends DataManagerParent
   {
      public static const PK_RANKLIST_MAX_PLAYER_NUM:int = 80;
      
      public static var _instance:PlayerDataForPK = null;
      
      public var serveTimeforInitPlayer:String;
      
      private var _userData:Object;
      
      private var _rankListInfo:Vector.<Object>;
      
      private var _userNicknameData:Object;
      
      public var goldPlayers:Vector.<Player>;
      
      public var goldPlayersBuffs:Vector.<Vector.<BuffDrive>>;
      
      public var goldNicknameData:Object;
      
      public var myPlayers:Vector.<Player>;
      
      public var myPlayersBuffs:Vector.<Vector.<BuffDrive>>;
      
      public var myNicknameData:Object;
      
      public var playersInRankList:Vector.<Player>;
      
      public var playersInRankListBuffs:Vector.<Vector.<BuffDrive>>;
      
      public var playersInRankListNicknameData:Object;
      
      public var playerName:String;
      
      private var _allMatch:int;
      
      private var _winMatch:int;
      
      private var _failMatch:int;
      
      private var _pkPoint:int;
      
      private var _extraAddPkPointRate:int = 0;
      
      public var isSignUpForDouble:Boolean = false;
      
      public var isSignUpForSingle:Boolean = false;
      
      private var _allMatchForTwoPlayer:int;
      
      private var _winMatchForTwoPlayer:int;
      
      private var _failMatchForTwoPlayer:int;
      
      private var _matchDateForTwoPlayer:String;
      
      public var monthResetTime:String = "";
      
      public var isReseted:int;
      
      public function PlayerDataForPK()
      {
         if(!_instance)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看到实例已经存在了么？！");
      }
      
      public static function getInstance() : PlayerDataForPK
      {
         if(!_instance)
         {
            _instance = new PlayerDataForPK();
         }
         return _instance;
      }
      
      public function get userData() : Object
      {
         if(!_userData)
         {
            _userData = {};
         }
         return _userData;
      }
      
      public function get rankListInfo() : Vector.<Object>
      {
         if(!_rankListInfo)
         {
            _rankListInfo = new Vector.<Object>(80);
         }
         return _rankListInfo;
      }
      
      public function get userNicknameData() : Object
      {
         if(!_userNicknameData)
         {
            _userNicknameData = {};
         }
         return _userNicknameData;
      }
      
      public function clearRankListTsData() : void
      {
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:* = undefined;
         var _loc5_:Object = null;
         var _loc2_:String = null;
         if(_userData)
         {
            for(_loc2_ in _userData)
            {
               _loc3_ = _userData[_loc2_];
               if(_loc3_)
               {
                  _loc4_ = int(_loc3_.length);
                  _loc8_ = 0;
                  while(_loc8_ < _loc4_)
                  {
                     _loc5_ = _loc3_[_loc8_];
                     for(var _loc1_ in _loc3_[_loc8_])
                     {
                        _loc3_[_loc8_][_loc1_] = null;
                     }
                     _loc5_ = null;
                     _loc3_[_loc8_] = null;
                     _loc8_++;
                  }
                  _loc3_ = null;
                  _userData[_loc2_] = null;
               }
            }
            _userData = null;
         }
         if(_rankListInfo)
         {
            _loc4_ = int(_rankListInfo.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               _loc5_ = _rankListInfo[_loc8_];
               if(_loc5_)
               {
                  for(_loc2_ in _loc5_)
                  {
                     _loc5_[_loc2_] = null;
                  }
                  _loc5_ = null;
                  _rankListInfo[_loc8_] = null;
               }
               _loc8_++;
            }
            _rankListInfo = null;
         }
         if(playersInRankList)
         {
            _loc4_ = int(playersInRankList.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(playersInRankList[_loc8_])
               {
                  playersInRankList[_loc8_].clear();
               }
               playersInRankList[_loc8_] = null;
               _loc8_++;
            }
         }
         playersInRankList = null;
         if(playersInRankListBuffs)
         {
            _loc4_ = int(playersInRankListBuffs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(playersInRankListBuffs[_loc8_])
               {
                  _loc6_ = int(playersInRankListBuffs[_loc8_].length);
                  _loc7_ = 0;
                  while(_loc7_ < _loc6_)
                  {
                     if(playersInRankListBuffs[_loc8_][_loc7_])
                     {
                        playersInRankListBuffs[_loc8_][_loc7_].clear();
                     }
                     playersInRankListBuffs[_loc8_][_loc7_] = null;
                     _loc7_++;
                  }
                  playersInRankListBuffs[_loc8_] = null;
               }
               _loc8_++;
            }
         }
         playersInRankListBuffs = null;
         playersInRankListNicknameData = null;
      }
      
      override public function clear() : void
      {
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:* = undefined;
         var _loc5_:Object = null;
         var _loc2_:String = null;
         super.clear();
         if(goldPlayers)
         {
            _loc4_ = int(goldPlayers.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(goldPlayers[_loc8_])
               {
                  goldPlayers[_loc8_].clear();
               }
               goldPlayers[_loc8_] = null;
               _loc8_++;
            }
         }
         goldPlayers = null;
         if(myPlayers)
         {
            _loc4_ = int(myPlayers.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(myPlayers[_loc8_])
               {
                  myPlayers[_loc8_].clear();
               }
               myPlayers[_loc8_] = null;
               _loc8_++;
            }
         }
         myPlayers = null;
         if(playersInRankList)
         {
            _loc4_ = int(playersInRankList.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(playersInRankList[_loc8_])
               {
                  playersInRankList[_loc8_].clear();
               }
               playersInRankList[_loc8_] = null;
               _loc8_++;
            }
         }
         playersInRankList = null;
         if(myPlayersBuffs)
         {
            _loc4_ = int(myPlayersBuffs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(myPlayersBuffs[_loc8_])
               {
                  _loc6_ = int(myPlayersBuffs[_loc8_].length);
                  _loc7_ = 0;
                  while(_loc7_ < _loc6_)
                  {
                     if(myPlayersBuffs[_loc8_][_loc7_])
                     {
                        myPlayersBuffs[_loc8_][_loc7_].clear();
                     }
                     myPlayersBuffs[_loc8_][_loc7_] = null;
                     _loc7_++;
                  }
               }
               myPlayersBuffs[_loc8_] = null;
               _loc8_++;
            }
         }
         myPlayersBuffs = null;
         myNicknameData = null;
         if(goldPlayersBuffs)
         {
            _loc4_ = int(goldPlayersBuffs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(goldPlayersBuffs[_loc8_])
               {
                  _loc6_ = int(goldPlayersBuffs[_loc8_].length);
                  _loc7_ = 0;
                  while(_loc7_ < _loc6_)
                  {
                     if(goldPlayersBuffs[_loc8_][_loc7_])
                     {
                        goldPlayersBuffs[_loc8_][_loc7_].clear();
                     }
                     goldPlayersBuffs[_loc8_][_loc7_] = null;
                     _loc7_++;
                  }
               }
               goldPlayersBuffs[_loc8_] = null;
               _loc8_++;
            }
         }
         goldPlayersBuffs = null;
         goldNicknameData = null;
         if(playersInRankListBuffs)
         {
            _loc4_ = int(playersInRankListBuffs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(playersInRankListBuffs[_loc8_])
               {
                  _loc6_ = int(playersInRankListBuffs[_loc8_].length);
                  _loc7_ = 0;
                  while(_loc7_ < _loc6_)
                  {
                     if(playersInRankListBuffs[_loc8_][_loc7_])
                     {
                        playersInRankListBuffs[_loc8_][_loc7_].clear();
                     }
                     playersInRankListBuffs[_loc8_][_loc7_] = null;
                     _loc7_++;
                  }
               }
               playersInRankListBuffs[_loc8_] = null;
               _loc8_++;
            }
         }
         playersInRankListBuffs = null;
         playersInRankListNicknameData = null;
         if(_userData)
         {
            for(_loc2_ in _userData)
            {
               _loc3_ = _userData[_loc2_];
               if(_loc3_)
               {
                  _loc4_ = int(_loc3_.length);
                  _loc8_ = 0;
                  while(_loc8_ < _loc4_)
                  {
                     _loc5_ = _loc3_[_loc8_];
                     for(var _loc1_ in _loc3_[_loc8_])
                     {
                        _loc3_[_loc8_][_loc1_] = null;
                     }
                     _loc5_ = null;
                     _loc3_[_loc8_] = null;
                     _loc8_++;
                  }
                  _loc3_ = null;
                  _userData[_loc2_] = null;
               }
            }
            _userData = null;
         }
         ClearUtil.nullObject(_userNicknameData);
         if(_rankListInfo)
         {
            _loc4_ = int(_rankListInfo.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               _loc5_ = _rankListInfo[_loc8_];
               if(_loc5_)
               {
                  for(_loc2_ in _loc5_)
                  {
                     _loc5_[_loc2_] = null;
                  }
                  _loc5_ = null;
                  _rankListInfo[_loc8_] = null;
               }
               _loc8_++;
            }
            _rankListInfo = null;
         }
         _instance = null;
      }
      
      public function get allMatch() : int
      {
         return _antiwear.allMatch;
      }
      
      public function set allMatch(param1:int) : void
      {
         _antiwear.allMatch = param1;
      }
      
      public function get winMatch() : int
      {
         return Math.max(1,_antiwear.winMatch);
      }
      
      public function set winMatch(param1:int) : void
      {
         _antiwear.winMatch = param1;
      }
      
      public function get winMonthMatch() : int
      {
         return Math.max(0,_antiwear.winMonthMatch);
      }
      
      public function set winMonthMatch(param1:int) : void
      {
         _antiwear.winMonthMatch = param1;
      }
      
      public function get failMatch() : int
      {
         return _antiwear.failMatch;
      }
      
      public function set failMatch(param1:int) : void
      {
         _antiwear.failMatch = param1;
      }
      
      public function get mingrenMatchForTwoPlayer() : int
      {
         return _antiwear.mingrenMatchForTwoPlayer;
      }
      
      public function set mingrenMatchForTwoPlayer(param1:int) : void
      {
         _antiwear.mingrenMatchForTwoPlayer = param1;
      }
      
      public function get allMatchForTwoPlayer() : int
      {
         return _antiwear.allMatchForTwoPlayer;
      }
      
      public function set allMatchForTwoPlayer(param1:int) : void
      {
         _antiwear.allMatchForTwoPlayer = param1;
      }
      
      public function get winMatchForTwoPlayer() : int
      {
         return Math.max(1,_antiwear.winMatchForTwoPlayer);
      }
      
      public function set winMatchForTwoPlayer(param1:int) : void
      {
         _antiwear.winMatchForTwoPlayer = param1;
      }
      
      public function get failMatchForTwoPlayer() : int
      {
         return _antiwear.failMatchForTwoPlayer;
      }
      
      public function set failMatchForTwoPlayer(param1:int) : void
      {
         _antiwear.failMatchForTwoPlayer = param1;
      }
      
      public function get matchDateForTwoPlayer() : String
      {
         return _antiwear.matchDateForTwoPlayer;
      }
      
      public function set matchDateForTwoPlayer(param1:String) : void
      {
         _antiwear.matchDateForTwoPlayer = param1;
      }
      
      public function get pkPoint() : int
      {
         return _antiwear.pkPoint;
      }
      
      public function set pkPoint(param1:int) : void
      {
         if(param1 <= XMLSingle.getInstance().maxPKPoint + XMLSingle.getInstance().maxDxValue_PKPoint)
         {
            if(param1 > XMLSingle.getInstance().maxPKPoint)
            {
               _antiwear.pkPoint = XMLSingle.getInstance().maxPKPoint;
            }
            else
            {
               _antiwear.pkPoint = Math.max(0,param1);
            }
         }
         GamingUI.getInstance().player1.getPKVO().setPKPoint(pkPoint);
      }
      
      public function get extraAddPkPointRate() : int
      {
         var _loc1_:TimeUtil = new TimeUtil();
         if(_loc1_.getWeek() < 6 && _loc1_.isInTimeOfOneDay(TimeUtil.timeStr,"18:00","20:00"))
         {
            return 100;
         }
         return 0;
      }
      
      public function set extraAddPkPointRate(param1:int) : void
      {
         _antiwear.extraAddPkPointRate = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.allMatch = _allMatch;
         _antiwear.winMatch = _winMatch;
         _antiwear.winMonthMatch = 0;
         _antiwear.failMatch = _failMatch;
         _antiwear.pkPoint = _pkPoint;
         _antiwear.extraAddPkPointRate = _extraAddPkPointRate;
         _antiwear.allMatchForTwoPlayer = _allMatchForTwoPlayer = 1;
         _antiwear.mingrenMatchForTwoPlayer = 0;
         _antiwear.winMatchForTwoPlayer = _winMatchForTwoPlayer = 1;
         _antiwear.failMatchForTwoPlayer = _failMatchForTwoPlayer = 0;
         _antiwear.matchDateForTwoPlayer = _matchDateForTwoPlayer = "";
         isSignUpForDouble = false;
         isSignUpForDouble = false;
      }
   }
}

