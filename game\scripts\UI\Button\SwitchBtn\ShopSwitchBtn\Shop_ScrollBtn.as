package UI.Button.SwitchBtn.ShopSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Shop_ScrollBtn extends SwitchBtn
   {
      public function Shop_ScrollBtn()
      {
         super();
         setTipString("磨具");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopSwitchBtn"));
      }
   }
}

