package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class WeaponEquipmentVO extends AbleEquipmentVO
   {
      private var _attack:int;
      
      private var _minAttack:int;
      
      private var _maxAttack:int;
      
      public var addPlayerSaveAttr:Vector.<String> = new Vector.<String>();
      
      public var addPlayerSaveAttrVals:Vector.<Number> = new Vector.<Number>();
      
      public function WeaponEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.attack = _attack;
         _antiwear.maxAttack = _maxAttack;
         _antiwear.minAttack = _minAttack;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:WeaponEquipmentVO = new WeaponEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as WeaponEquipmentVO).attack = this.attack;
         (param1 as WeaponEquipmentVO).maxAttack = this.maxAttack;
         (param1 as WeaponEquipmentVO).minAttack = this.minAttack;
         _loc2_ = int(addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as WeaponEquipmentVO).addPlayerSaveAttr.push(addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as WeaponEquipmentVO).addPlayerSaveAttrVals.push(addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
      }
      
      public function get attack() : int
      {
         return _antiwear.attack;
      }
      
      public function set attack(param1:int) : void
      {
         _antiwear.attack = param1;
      }
      
      public function get maxAttack() : int
      {
         return _antiwear.maxAttack;
      }
      
      public function set maxAttack(param1:int) : void
      {
         _antiwear.maxAttack = param1;
      }
      
      public function get minAttack() : int
      {
         return _antiwear.minAttack;
      }
      
      public function set minAttack(param1:int) : void
      {
         _antiwear.minAttack = param1;
      }
   }
}

