package UI.EquipmentCells
{
   import UI.Button.ChoiceBtns.EquipmentDestinationBtnListSingle;
   import UI.CellBorderSingle;
   import UI.EquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class UsedEquipmentCell extends EquipmentCell
   {
      public var equipbackground:UsedEquipmentCellBackground;
      
      private var _shadingLayer:Bitmap = new Bitmap();
      
      private var _cellType:String;
      
      private var _owner:String;
      
      private var _player:Player;
      
      private var _isShowBtn:Boolean;
      
      public function UsedEquipmentCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
         if(_shadingLayer)
         {
            if(_shadingLayer.bitmapData)
            {
               _shadingLayer.bitmapData.dispose();
            }
            _shadingLayer.bitmapData = null;
         }
         _shadingLayer = null;
         _owner = null;
         _player = null;
      }
      
      public function get cellType() : String
      {
         return _cellType;
      }
      
      public function set cellType(param1:String) : void
      {
         _cellType = param1;
      }
      
      public function get owner() : String
      {
         return _owner;
      }
      
      public function set owner(param1:String) : void
      {
         _owner = param1;
      }
      
      public function get player() : Player
      {
         return _player;
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      public function set isShowBtn(param1:Boolean) : void
      {
         _isShowBtn = param1;
      }
      
      public function get isShowBtn() : Boolean
      {
         return _isShowBtn;
      }
      
      public function setShading(param1:BitmapData) : void
      {
         _shadingLayer.bitmapData = param1;
      }
      
      override public function addEquipmentVO(param1:EquipmentVO) : void
      {
         super.addEquipmentVO(param1);
         if(isHaveChild)
         {
            _shadingLayer.visible = false;
         }
         else
         {
            _shadingLayer.visible = true;
         }
      }
      
      override public function removeEquipmentVO(param1:EquipmentVO = null) : void
      {
         super.removeEquipmentVO(param1);
         if(isHaveChild)
         {
            _shadingLayer.visible = false;
         }
         else
         {
            _shadingLayer.visible = true;
         }
      }
      
      override protected function initCell() : void
      {
         super.initCell();
         _shadingLayer.x = -width / 2;
         _shadingLayer.y = -height / 2;
         addChild(_shadingLayer);
         _isShowBtn = true;
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
         if(getChildByName(EquipmentDestinationBtnListSingle.getInstance().name))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
         super.rollOut(param1);
      }
      
      override protected function click(param1:MouseEvent) : void
      {
         super.click(param1);
         if(_intervalTime < 500 && _isShowBtn)
         {
            if(isHaveChild)
            {
               if(!getChildByName(EquipmentDestinationBtnListSingle.getInstance().name))
               {
                  EquipmentDestinationBtnListSingle.getInstance().x = mouseX;
                  EquipmentDestinationBtnListSingle.getInstance().y = mouseY;
                  EquipmentDestinationBtnListSingle.getInstance().switchToPackageState(MyFunction2.brushBtn(288,child));
                  addChild(EquipmentDestinationBtnListSingle.getInstance());
               }
            }
         }
      }
      
      override public function get equipmentCellBackground() : IEquipmentCellBackground
      {
         return equipbackground;
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.visible == true)
         {
            CellBorderSingle.getInstance().x = -0.3;
            CellBorderSingle.getInstance().y = -1.6;
            CellBorderSingle.getInstance().width = 52;
            CellBorderSingle.getInstance().height = 54;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return equipbackground.getRect(param1);
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickTakeDownBtn",hideList,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickTakeDownBtn",hideList,true);
      }
      
      protected function hideList(param1:UIBtnEvent) : void
      {
         if(getChildByName(EquipmentDestinationBtnListSingle.getInstance().name))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
      }
   }
}

