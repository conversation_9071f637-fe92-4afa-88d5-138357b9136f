package UI2.SmallAssistant
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI2.SmallAssistant.ActiveTask.ActiveTaskRewardData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ActiveRewardShow
   {
      private var m_equipmentCells:Vector.<Sprite>;
      
      private var m_btnStateShow:MovieClipPlayLogicShell;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_show:MovieClip;
      
      private var m_activeTaskRewardData:ActiveTaskRewardData;
      
      public function ActiveRewardShow()
      {
         super();
         m_equipmentCells = new Vector.<Sprite>();
         m_btnStateShow = new MovieClipPlayLogicShell();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_equipmentCells);
         m_equipmentCells = null;
         ClearUtil.clearObject(m_btnStateShow);
         m_btnStateShow = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         m_show = null;
         m_activeTaskRewardData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setEquipmentVOs(param1:ActiveTaskRewardData) : void
      {
         m_activeTaskRewardData = param1;
         initShow2();
      }
      
      public function tranToNotAbleGet() : void
      {
         btnStateShowInitNotAbleGetFrame();
      }
      
      public function tranToAbleGet() : void
      {
         btnStateShowInitAbleGetFrame();
      }
      
      public function tranToGotReward() : void
      {
         btnStateShowInitGotRewardFrame();
      }
      
      public function getActiveTaskRewardData() : ActiveTaskRewardData
      {
         return m_activeTaskRewardData;
      }
      
      public function getGetRewardBtn() : ButtonLogicShell2
      {
         return m_getRewardBtn;
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:DisplayObject = null;
         _loc2_ = m_show.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ = m_show.getChildAt(_loc4_);
            if(_loc1_.name.substr(0,6) == "eqCell")
            {
               _loc3_++;
            }
            _loc4_++;
         }
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            m_equipmentCells.push(m_show["eqCell_" + (_loc4_ + 1)]);
            _loc4_++;
         }
         m_btnStateShow.setShow(m_show["btnStateShow"]);
         btnStateShowInitNotAbleGetFrame();
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:Equipment = null;
         if(m_show == null || m_activeTaskRewardData == null)
         {
            return;
         }
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         _loc1_ = int(m_activeTaskRewardData.getEquipmentVONum());
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(m_activeTaskRewardData.getEquipmentVOByIndex(_loc3_));
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            m_equipments.push(_loc2_);
            if(_loc3_ < m_equipmentCells.length)
            {
               m_equipmentCells[_loc3_].addChild(_loc2_);
            }
            _loc3_++;
         }
      }
      
      private function btnStateShowFrameClear() : void
      {
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
      }
      
      private function btnStateShowInitNotAbleGetFrame() : void
      {
         btnStateShowFrameClear();
         m_btnStateShow.gotoAndStop("notAbleGet");
      }
      
      private function btnStateShowInitAbleGetFrame() : void
      {
         btnStateShowFrameClear();
         m_btnStateShow.gotoAndStop("ableGet");
         m_getRewardBtn = new ButtonLogicShell2();
         m_getRewardBtn.setShow(m_btnStateShow.getShow()["getRewardBtn"]);
      }
      
      private function btnStateShowInitGotRewardFrame() : void
      {
         btnStateShowFrameClear();
         m_btnStateShow.gotoAndStop("gotReward");
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

