package UI.WorldBoss
{
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AttackAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.CriticalAndDodgeData;
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Boss.BossBloodData;
   import UI.WorldBoss.FlashView.FlashViewFactory;
   import UI.WorldBoss.ShakeView.ShakeViewFactory;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class PetEntity extends OwnSideEntity
   {
      private var m_petVO:PetEquipmentVO;
      
      public function PetEntity()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_petVO = null;
      }
      
      public function setPetVO(param1:PetEquipmentVO) : void
      {
         var _loc4_:XML = null;
         m_petVO = param1;
         m_isReadyFight = true;
         try
         {
            _loc4_ = m_worldBossXML.petShow[0].pet.(@petClassName == m_petVO.className)[0];
         }
         catch(error:Error)
         {
            if(_loc4_ == null)
            {
               _loc4_ = m_worldBossXML.petShow[0].pet[0];
            }
         }
         if(_loc4_ == null)
         {
            _loc4_ = m_worldBossXML.petShow[0].pet[0];
         }
         m_myLoader.getClass(_loc4_.@swf,_loc4_.@className,getShowSuccess,getShowFail);
         m_myLoader.load();
         _loc4_ = m_worldBossXML.petViewData[0].shakeView[0];
         var _loc2_:ShakeViewFactory = new ShakeViewFactory();
         m_shakeView_attack = _loc2_.createShakeViewByXML(_loc4_);
         ClearUtil.clearObject(_loc2_);
         _loc4_ = m_worldBossXML.petViewData[0].flashView[0];
         var _loc3_:FlashViewFactory = new FlashViewFactory();
         m_flashView_attack = _loc3_.createFlashViewByXML(_loc4_);
         ClearUtil.clearObject(_loc3_);
      }
      
      override public function attack(param1:Vector.<IEntity>) : void
      {
         var _loc5_:int = 0;
         var _loc10_:int = 0;
         var _loc12_:AbleAttackedBoss = null;
         var _loc7_:int = 0;
         var _loc3_:BossBloodData = null;
         var _loc11_:CriticalAndDodgeData = null;
         _loc10_ = int(param1.length);
         var _loc8_:Vector.<int> = new Vector.<int>();
         var _loc14_:Vector.<int> = new Vector.<int>();
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc9_:Vector.<Number> = new Vector.<Number>();
         var _loc13_:Vector.<int> = new Vector.<int>();
         var _loc6_:Vector.<CriticalAndDodgeData> = new Vector.<CriticalAndDodgeData>();
         _loc5_ = 0;
         while(_loc5_ < _loc10_)
         {
            _loc12_ = param1[_loc5_] as AbleAttackedBoss;
            _loc11_ = new CriticalAndDodgeData();
            _loc8_.push(_loc12_.getLevel());
            _loc7_ = caculateHurt(_loc12_,_loc11_);
            _loc12_.addLostHurt(_loc7_);
            _loc3_ = new BossBloodData();
            _loc12_.getBloodData(_loc3_);
            _loc2_.push(_loc3_.beAttackedBloodVolume);
            _loc9_.push(_loc3_.bloodPercent);
            _loc13_.push(_loc3_.bloodShowIndex);
            _loc14_.push(_loc7_);
            _loc6_.push(_loc11_);
            _loc5_++;
         }
         var _loc4_:AttackAnimationData = new AttackAnimationData();
         _loc4_.roundNum = m_world.getGetNextStepActionEnities().getRoundNum();
         _loc4_.attackEntity = this;
         _loc4_.attackEntityLevel = m_petVO.petLevel;
         _loc4_.beAttackedEntities = param1.slice(0);
         _loc4_.beAttackedEntityLevels = _loc8_;
         _loc4_.hurts = _loc14_;
         _loc4_.currentBloods = _loc2_;
         _loc4_.bloodPercents = _loc9_;
         _loc4_.bloodShowIndexs = _loc13_;
         _loc4_.criticalAndDodgeDatas = _loc6_;
         m_animationQueueData.addAnimationData(_loc4_);
      }
      
      private function caculateHurt(param1:Boss, param2:CriticalAndDodgeData) : int
      {
         var _loc4_:int = 0;
         var _loc3_:Number = ProgramStartData.getInstance().getZero();
         var _loc6_:Number = ProgramStartData.getInstance().getZero();
         var _loc5_:Number = Math.random();
         if(_loc5_ < _loc6_)
         {
            param2.isBeDodge = true;
            return ProgramStartData.getInstance().getZero();
         }
         _loc5_ = Math.random();
         if(_loc5_ < _loc3_)
         {
            param2.isCritical = true;
            _loc4_ = Math.round(Math.max(ProgramStartData.getInstance().getOne(),(m_petVO.activeSkillVO as PetActiveSkillVO).hurt * (ProgramStartData.getInstance().getOne() + m_proAttackPercent) * (ProgramStartData.getInstance().getOne() + ProgramStartData.getInstance().getNormalCriticalMulti()) - param1.getDefence()));
         }
         else
         {
            _loc4_ = Math.max(ProgramStartData.getInstance().getOne(),(m_petVO.activeSkillVO as PetActiveSkillVO).hurt * (ProgramStartData.getInstance().getOne() + m_proAttackPercent) - param1.getDefence());
         }
         trace("宠物提升攻击力百分比为：",m_proAttackPercent);
         return _loc4_;
      }
      
      public function getPetVO() : PetEquipmentVO
      {
         return m_petVO;
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_mc = new AnimationShowPlayLogicShell();
         m_mc.addNextStopListener(this);
         m_mc.setShow(new _loc2_());
         addChild(m_mc.getShow() as MovieClip);
         setLevelStr(m_petVO.petLevel);
         m_isReadyPlay = true;
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
      }
   }
}

