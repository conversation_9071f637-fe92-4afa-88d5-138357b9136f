package UI.HatchPanel
{
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EggEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MainGameCall;
   import UI.MyControlPanel;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Other.QuickenChargeBox;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.Privilege.PrivilegeVO;
   import UI2.broadcast.SubmitFunction;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.EntityShowContainer;
   import YJFY.EntityShowContainerListener;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class HatchPanel extends MySprite
   {
      private const m_const_normalHatch:String = "normal";
      
      private const m_const_superHatch:String = "super";
      
      private const m_const_hatchPetFrameLabel:String = "down";
      
      private var m_show:MovieClip;
      
      private var m_timeShow:Sprite;
      
      private var m_hourShow:MultiPlaceNumLogicShell;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_speedUpEnBtn:ButtonLogicShell;
      
      private var m_normalEnergyEffectShow:AnimationShowPlayLogicShell;
      
      private var m_speedUpEnergyEffectShow:AnimationShowPlayLogicShell;
      
      private var m_superHatchEffectShow:AnimationShowPlayLogicShell;
      
      private var m_normalHatchEffectShow:AnimationShowPlayLogicShell;
      
      private var m_stopListener:StopListener;
      
      private var m_frameLabelListener:AnimationPlayFrameLabelListener;
      
      private var m_eggShowMC:MovieClipPlayLogicShell2;
      
      private var m_hatchMachine:MovieClipPlayLogicShell;
      
      private var m_normalHatchBtn:ButtonLogicShell;
      
      private var m_superHatchBtn:ButtonLogicShell;
      
      private var m_superHatchTipShow:MovieClip;
      
      private var m_superHatchTipSureBtn:ButtonLogicShell;
      
      private var m_superHatchTipCancelBtn:ButtonLogicShell;
      
      private var m_petShowContainer:EntityShowContainer;
      
      private var m_petShowContainerListener:EntityShowContainerListener;
      
      private var _m_remainTimeForCompleteEnergy:uint;
      
      private var m_isStartHatch:Boolean;
      
      private var m_hatchType:String;
      
      private var m_player1VO:PlayerVO;
      
      private var m_player2VO:PlayerVO;
      
      private var m_curentPlayerVO:PlayerVO;
      
      private var m_quickenChargeBox:QuickenChargeBox;
      
      private var m_eggVO:EggEquipmentVO;
      
      private var m_petVOForHatchTarget:PetEquipmentVO;
      
      private var m_myControlPanel:MyControlPanel;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function HatchPanel()
      {
         super();
         m_stopListener = new StopListener();
         m_stopListener.stop2Fun = animationStop;
         m_frameLabelListener = new AnimationPlayFrameLabelListener();
         m_frameLabelListener.reachFrameLabelFun2 = reachFrameLabel;
         m_petShowContainer = new EntityShowContainer();
         m_petShowContainer.init();
         m_petShowContainerListener = new EntityShowContainerListener();
         m_petShowContainerListener.loadCompleteFun = loadPetShowComplete;
         m_petShowContainer.addEntityShowContainerListener(m_petShowContainerListener);
         Part1.getInstance().stage.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         if(m_eggVO)
         {
            if(m_curentPlayerVO)
            {
               retrieveEggByPlayer(m_curentPlayerVO);
            }
            m_eggVO = null;
         }
         Part1.getInstance().stage.removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_timeShow);
         m_timeShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.clearObject(m_speedUpEnBtn);
         m_speedUpEnBtn = null;
         ClearUtil.clearObject(m_normalEnergyEffectShow);
         m_normalEnergyEffectShow = null;
         ClearUtil.clearObject(m_speedUpEnergyEffectShow);
         m_speedUpEnergyEffectShow = null;
         ClearUtil.clearObject(m_superHatchEffectShow);
         m_superHatchEffectShow = null;
         ClearUtil.clearObject(m_normalHatchEffectShow);
         m_normalHatchEffectShow = null;
         ClearUtil.clearObject(m_stopListener);
         m_stopListener = null;
         ClearUtil.clearObject(m_frameLabelListener);
         m_frameLabelListener = null;
         ClearUtil.clearObject(m_eggShowMC);
         m_eggShowMC = null;
         ClearUtil.clearObject(m_hatchMachine);
         m_hatchMachine = null;
         ClearUtil.clearObject(m_normalHatchBtn);
         m_normalHatchBtn = null;
         ClearUtil.clearObject(m_superHatchBtn);
         m_superHatchBtn = null;
         ClearUtil.clearObject(m_superHatchTipShow);
         m_superHatchTipShow = null;
         ClearUtil.clearObject(m_superHatchTipCancelBtn);
         m_superHatchTipCancelBtn = null;
         ClearUtil.clearObject(m_superHatchTipSureBtn);
         m_superHatchTipSureBtn = null;
         ClearUtil.clearObject(m_petShowContainer);
         m_petShowContainer = null;
         ClearUtil.clearObject(m_petShowContainerListener);
         m_petShowContainerListener = null;
         m_hatchType = null;
         m_player1VO = null;
         m_player2VO = null;
         m_curentPlayerVO = null;
         m_quickenChargeBox = null;
         m_eggVO = null;
         m_petVOForHatchTarget = null;
         m_myControlPanel = null;
         super.clear();
      }
      
      public function init(param1:MyControlPanel) : void
      {
         var _loc4_:* = undefined;
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.m_remainTimeForCompleteEnergy = _m_remainTimeForCompleteEnergy;
         m_myControlPanel = param1;
         if(m_show)
         {
            throw new Error();
         }
         m_show = MyFunction2.returnShowByClassName("HathPetUI") as MovieClip;
         addChild(m_show);
         m_player1VO = GamingUI.getInstance().player1.playerVO;
         if(GamingUI.getInstance().player2)
         {
            m_player2VO = GamingUI.getInstance().player2.playerVO;
         }
         m_curentPlayerVO = param1.currentPlayer.playerVO;
         initShow();
         calulateRemainTimeForCompleteEnegy();
         var _loc3_:int = 0;
         var _loc2_:Boolean = false;
         if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.vipVO && GamingUI.getInstance().player1.vipVO.privilegeVOs)
         {
            _loc4_ = GamingUI.getInstance().player1.vipVO.privilegeVOs;
            _loc3_ = 0;
            while(_loc3_ < _loc4_.length)
            {
               if(_loc4_[_loc3_].id == 184)
               {
                  _loc2_ = true;
               }
               _loc3_++;
            }
         }
         else
         {
            _loc2_ = false;
         }
         if(_loc2_)
         {
            m_remainTimeForCompleteEnergy = 0;
         }
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      public function swapPlayerVO(param1:Player) : void
      {
         if(m_curentPlayerVO)
         {
            retrieveEggByPlayer(m_curentPlayerVO);
         }
         m_curentPlayerVO = param1.playerVO;
         calulateRemainTimeForCompleteEnegy();
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      public function getIsAblePutInEgg() : Boolean
      {
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            return true;
         }
         return false;
      }
      
      public function putInEgg(param1:EggEquipmentVO) : void
      {
         if(m_remainTimeForCompleteEnergy > 0)
         {
            return;
         }
         if(param1.equipmentType != "egg")
         {
            throw new Error();
         }
         if(m_eggVO)
         {
            retrieveEggByPlayer(m_curentPlayerVO);
         }
         m_eggVO = param1;
         refreshEggShow();
      }
      
      private function calulateRemainTimeForCompleteEnegy() : void
      {
         m_remainTimeForCompleteEnergy = Math.max(0,m_curentPlayerVO.hatchVO.getRemainTimeForCompleteEnergy() - (GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - m_curentPlayerVO.hatchVO.getOnLineTimeForCalRemainTime()));
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         if(m_curentPlayerVO == null)
         {
            return;
         }
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            return;
         }
         var _loc4_:int;
         m_remainTimeForCompleteEnergy = _loc4_ = Math.max(0,m_remainTimeForCompleteEnergy - param1.getAddTimeOneFrame());
         if(_loc4_ <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            _loc3_ = Math.ceil(_loc4_ / 1000);
            _loc5_ = _loc3_ / 3600;
            _loc3_ -= _loc5_ * 3600;
            _loc2_ = _loc3_ / 60;
            _loc3_ -= _loc2_ * 60;
            m_hourShow.showNum(_loc5_);
            m_minuteShow.showNum(_loc2_);
            m_secondShow.showNum(_loc3_);
         }
      }
      
      private function normalHatch() : void
      {
         if(m_eggVO == null)
         {
            return;
         }
         m_hatchType = "normal";
         hatch();
      }
      
      private function superHatch() : void
      {
         if(m_eggVO == null)
         {
            return;
         }
         m_hatchType = "super";
         hatch();
      }
      
      private function hatch() : void
      {
         hideHatchBtn();
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            ClearUtil.clearObject(m_petVOForHatchTarget);
            m_petVOForHatchTarget = getTargetPet();
            m_petShowContainer.refreshPetShow(m_petVOForHatchTarget);
         },function():void
         {
            endHatch();
            GamingUI.getInstance().showMessageTip("网络连接失败");
         },true);
      }
      
      private function endHatch() : void
      {
         showHatchBtn();
         m_isStartHatch = false;
         m_hatchType = null;
         m_myControlPanel.mouseChildren = true;
         m_myControlPanel.mouseEnabled = true;
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
         GamingUI.getInstance().refresh(2);
      }
      
      private function loadPetShowComplete(param1:EntityShowContainer) : void
      {
         var _loc2_:EquipmentVO = MainGameCall.getInstance().addHatchPet(m_petVOForHatchTarget,"super");
         SubmitFunction.getInstance().setData1(1,String(_loc2_.id),_loc2_.name);
         if(m_hatchType == "normal")
         {
            m_curentPlayerVO.hatchVO.setDataForStartHatch(GamingUI.getInstance().getNewestTimeStrFromSever(),"n");
         }
         else
         {
            m_curentPlayerVO.hatchVO.setDataForStartHatch(GamingUI.getInstance().getNewestTimeStrFromSever(),"s");
         }
         calulateRemainTimeForCompleteEnegy();
         m_myControlPanel.mouseChildren = false;
         m_myControlPanel.mouseEnabled = false;
         MainGameCall.getInstance().addMainLineTaskGoalGameEventStr("HatchPet_" + m_hatchType);
         var _loc3_:SaveTaskInfo = new SaveTaskInfo();
         _loc3_.type = "4399";
         _loc3_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc3_);
         MyFunction2.saveGame();
         m_isStartHatch = true;
         if(m_hatchType == "normal")
         {
            playNormalHatchEffectShow();
         }
         else
         {
            playSuperHatchEffectShow();
         }
         m_eggVO = null;
         GamingUI.getInstance().internalPanel.showWarningBox("孵化成功",0);
      }
      
      private function getTargetPet() : PetEquipmentVO
      {
         var _loc3_:Vector.<EquipmentVO> = m_eggVO.getTargetPetVOs();
         var _loc1_:int = Math.random() * _loc3_.length;
         var _loc2_:PetEquipmentVO = _loc3_[_loc1_] as PetEquipmentVO;
         _loc3_[_loc1_] = null;
         ClearUtil.nullArr(_loc3_);
         _loc3_ = null;
         return _loc2_;
      }
      
      private function retrieveEggByPlayer(param1:PlayerVO) : void
      {
         if(m_eggVO)
         {
            MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param1.packageEquipmentVOs,m_eggVO,1);
            m_eggVO = null;
            refreshEggShow();
         }
      }
      
      private function hideHatchBtn() : void
      {
         m_normalHatchBtn.getShow().visible = false;
         m_superHatchBtn.getShow().visible = false;
      }
      
      private function showHatchBtn() : void
      {
         m_normalHatchBtn.getShow().visible = true;
         m_superHatchBtn.getShow().visible = true;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:Boolean = false;
         var _loc4_:* = undefined;
         if(param1.button == null)
         {
            return;
         }
         switch(param1.button)
         {
            case m_superHatchBtn:
               if(m_eggVO == null)
               {
                  return;
               }
               if(m_superHatchTipShow)
               {
                  return;
               }
               _loc3_ = 0;
               _loc2_ = true;
               if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.vipVO && GamingUI.getInstance().player1.vipVO.privilegeVOs)
               {
                  _loc4_ = GamingUI.getInstance().player1.vipVO.privilegeVOs;
                  _loc3_ = 0;
                  while(_loc3_ < _loc4_.length)
                  {
                     if(_loc4_[_loc3_].id == 184)
                     {
                        _loc2_ = false;
                     }
                     _loc3_++;
                  }
               }
               else
               {
                  _loc2_ = true;
               }
               if(_loc2_)
               {
                  m_superHatchTipShow = MyFunction2.returnShowByClassName("SuperHatchTip") as MovieClip;
                  m_myControlPanel.addChild(m_superHatchTipShow);
                  m_superHatchTipSureBtn = new ButtonLogicShell();
                  m_superHatchTipSureBtn.setShow(m_superHatchTipShow["yesBtn"]);
                  m_superHatchTipCancelBtn = new ButtonLogicShell();
                  m_superHatchTipCancelBtn.setShow(m_superHatchTipShow["noBtn"]);
                  break;
               }
               superHatch();
               break;
            case m_superHatchTipSureBtn:
            case m_superHatchTipCancelBtn:
               if(param1.button == m_superHatchTipSureBtn)
               {
                  superHatch();
               }
               clearSuperHatchTipShow();
               break;
            case m_normalHatchBtn:
               normalHatch();
               clearSuperHatchTipShow();
               break;
            case m_speedUpEnBtn:
               m_quickenChargeBox = GamingUI.getInstance().showQuickenChargeBox(Math.ceil(m_remainTimeForCompleteEnergy / 3600000),speedUp);
         }
      }
      
      private function clearSuperHatchTipShow() : void
      {
         if(m_superHatchTipShow != null && m_superHatchTipShow.parent)
         {
            m_superHatchTipShow.parent.removeChild(m_superHatchTipShow);
         }
         ClearUtil.clearObject(m_superHatchTipShow);
         m_superHatchTipShow = null;
         ClearUtil.clearObject(m_superHatchTipSureBtn);
         m_superHatchTipSureBtn = null;
         ClearUtil.clearObject(m_superHatchTipCancelBtn);
         m_superHatchTipCancelBtn = null;
      }
      
      private function speedUp(param1:uint) : void
      {
         var speedUpHour:uint = param1;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_curentPlayerVO.hatchVO.buyHatchTime(speedUpHour * 3600000,param1);
            calulateRemainTimeForCompleteEnegy();
            playSpeedUpEnergyEffectShow();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },function():void
         {
            m_curentPlayerVO.hatchVO.buyHatchTime(speedUpHour * 3600000,GamingUI.getInstance().getNewestTimeStrFromSever());
            calulateRemainTimeForCompleteEnegy();
            playSpeedUpEnergyEffectShow();
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },true);
         m_quickenChargeBox = null;
      }
      
      private function playNormalHatchEffectShow() : void
      {
         m_show.addChild(m_normalHatchEffectShow.getDisplayShow());
         m_normalHatchEffectShow.gotoAndPlay("1");
      }
      
      private function endPlayNormalHatchEffectShow() : void
      {
         if(m_normalHatchEffectShow.getDisplayShow().parent)
         {
            m_normalHatchEffectShow.getDisplayShow().parent.removeChild(m_normalHatchEffectShow.getDisplayShow());
         }
      }
      
      private function playSuperHatchEffectShow() : void
      {
         m_show.addChild(m_superHatchEffectShow.getDisplayShow());
         m_superHatchEffectShow.gotoAndPlay("1");
      }
      
      private function endPlaySuperHatchEffectShow() : void
      {
         if(m_superHatchEffectShow.getDisplayShow().parent)
         {
            m_superHatchEffectShow.getDisplayShow().parent.removeChild(m_superHatchEffectShow.getDisplayShow());
         }
      }
      
      private function playSpeedUpEnergyEffectShow() : void
      {
         m_show.addChild(m_speedUpEnergyEffectShow.getShow() as DisplayObject);
         if(m_normalEnergyEffectShow.getDisplayShow().parent)
         {
            m_normalEnergyEffectShow.getDisplayShow().parent.removeChild(m_normalEnergyEffectShow.getDisplayShow());
         }
         m_speedUpEnergyEffectShow.gotoAndPlay("1");
         m_timeShow.visible = false;
      }
      
      private function endPlaySpeedUpEnergyEffectShow() : void
      {
         m_timeShow.visible = true;
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      private function refreshEggShow() : void
      {
         if(m_eggVO == null)
         {
            m_eggShowMC.gotoAndStop("nothing");
            if(m_eggShowMC.getShow().parent)
            {
               m_eggShowMC.getShow().parent.removeChild(m_eggShowMC.getShow());
            }
         }
         else
         {
            m_eggShowMC.gotoAndStop(m_eggVO.className);
            m_show.addChild(m_eggShowMC.getShow());
         }
      }
      
      private function initShow() : void
      {
         m_timeShow = m_show["timeShow"];
         m_hourShow = new MultiPlaceNumLogicShell();
         m_hourShow.setShow(m_timeShow["hour"]);
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_timeShow["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_timeShow["second"]);
         m_hourShow.setIsShowZero(true);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
         m_speedUpEnBtn = new ButtonLogicShell();
         m_speedUpEnBtn.setShow(m_show["superEngergyBtn"]);
         m_normalEnergyEffectShow = new AnimationShowPlayLogicShell();
         m_normalEnergyEffectShow.setShow(m_show["normalEnergyEffect"]);
         m_speedUpEnergyEffectShow = new AnimationShowPlayLogicShell();
         m_speedUpEnergyEffectShow.setShow(m_show["speedUpEnergyEffectShow"]);
         m_speedUpEnergyEffectShow.addNextStopListener(m_stopListener);
         m_normalHatchEffectShow = new AnimationShowPlayLogicShell();
         m_normalHatchEffectShow.setShow(m_show["normalHatchEffectShow"]);
         m_normalHatchEffectShow.addNextStopListener(m_stopListener);
         m_normalHatchEffectShow.addFrameLabelListener(m_frameLabelListener);
         m_superHatchEffectShow = new AnimationShowPlayLogicShell();
         m_superHatchEffectShow.setShow(m_show["superHatchEffectShow"]);
         m_superHatchEffectShow.addNextStopListener(m_stopListener);
         m_superHatchEffectShow.addFrameLabelListener(m_frameLabelListener);
         m_eggShowMC = new MovieClipPlayLogicShell2();
         m_eggShowMC.setShow(m_show["eggShow"]);
         m_eggShowMC.gotoAndStop("nothing");
         m_hatchMachine = new MovieClipPlayLogicShell();
         m_hatchMachine.setShow(m_show["hatchMachine"]);
         initHatchMachineCloseFrameShow();
      }
      
      private function initNotHatchState() : void
      {
         m_show.addChild(m_timeShow);
         m_show.addChild(m_speedUpEnBtn.getShow());
         m_show.addChild(m_normalEnergyEffectShow.getShow() as DisplayObject);
         if(m_speedUpEnergyEffectShow.getDisplayShow().parent)
         {
            m_speedUpEnergyEffectShow.getDisplayShow().parent.removeChild(m_speedUpEnergyEffectShow.getDisplayShow());
         }
         if(m_normalHatchEffectShow.getDisplayShow().parent)
         {
            m_normalHatchEffectShow.getDisplayShow().parent.removeChild(m_normalHatchEffectShow.getDisplayShow());
         }
         if(m_superHatchEffectShow.getDisplayShow().parent)
         {
            m_superHatchEffectShow.getDisplayShow().parent.removeChild(m_superHatchEffectShow.getDisplayShow());
         }
         if(m_eggShowMC.getShow().parent)
         {
            m_eggShowMC.getShow().parent.removeChild(m_eggShowMC.getShow());
         }
         initHatchMachineCloseFrameShow();
      }
      
      private function initAbleHatchState() : void
      {
         if(m_timeShow.parent)
         {
            m_timeShow.parent.removeChild(m_timeShow);
         }
         if(m_speedUpEnBtn.getShow().parent)
         {
            m_speedUpEnBtn.getShow().parent.removeChild(m_speedUpEnBtn.getShow());
         }
         if(m_normalEnergyEffectShow.getDisplayShow().parent)
         {
            m_normalEnergyEffectShow.getDisplayShow().parent.removeChild(m_normalEnergyEffectShow.getDisplayShow());
         }
         if(m_speedUpEnergyEffectShow.getDisplayShow().parent)
         {
            m_speedUpEnergyEffectShow.getDisplayShow().parent.removeChild(m_speedUpEnergyEffectShow.getDisplayShow());
         }
         if(m_normalHatchEffectShow.getDisplayShow().parent)
         {
            m_normalHatchEffectShow.getDisplayShow().parent.removeChild(m_normalHatchEffectShow.getDisplayShow());
         }
         if(m_superHatchEffectShow.getDisplayShow().parent)
         {
            m_superHatchEffectShow.getDisplayShow().parent.removeChild(m_superHatchEffectShow.getDisplayShow());
         }
         if(m_eggShowMC.getShow().parent)
         {
            m_eggShowMC.getShow().parent.removeChild(m_eggShowMC.getShow());
         }
         initHatchMachineOpenFrameShow();
      }
      
      private function clearHatchMachineFrameShow() : void
      {
         ClearUtil.clearObject(m_normalHatchBtn);
         m_normalHatchBtn = null;
         ClearUtil.clearObject(m_superHatchBtn);
         m_superHatchBtn = null;
      }
      
      private function initHatchMachineOpenFrameShow() : void
      {
         clearHatchMachineFrameShow();
         m_hatchMachine.gotoAndPlay("open");
         m_normalHatchBtn = new ButtonLogicShell();
         m_normalHatchBtn.setShow(m_hatchMachine.getShow()["hatchTwoBtn"]["NormalHatchBtn"]);
         m_superHatchBtn = new ButtonLogicShell();
         m_superHatchBtn.setShow(m_hatchMachine.getShow()["hatchTwoBtn"]["SuperHatchBtn"]);
      }
      
      private function initHatchMachineCloseFrameShow() : void
      {
         clearHatchMachineFrameShow();
         m_hatchMachine.gotoAndPlay("close");
      }
      
      private function animationStop(param1:AnimationShowPlayLogicShell) : void
      {
         switch(param1)
         {
            case m_speedUpEnergyEffectShow:
               endPlaySpeedUpEnergyEffectShow();
               break;
            case m_normalHatchEffectShow:
               endPlayNormalHatchEffectShow();
               break;
            case m_superHatchEffectShow:
               endPlaySuperHatchEffectShow();
         }
      }
      
      private function reachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         var _loc3_:* = param2;
         if("down" === _loc3_)
         {
            if(m_isStartHatch == false)
            {
               return;
            }
            if(m_eggShowMC.getShow().parent)
            {
               m_eggShowMC.getShow().parent.removeChild(m_eggShowMC.getShow());
            }
            addPetShow();
         }
      }
      
      private function addPetShow() : void
      {
         m_petShowContainer.getShow().x = 284;
         m_petShowContainer.getShow().y = 384;
         Part1.getInstance().stage.addChild(m_petShowContainer.getShow());
         TweenLite.to(m_petShowContainer.getShow(),1,{
            "x":m_petShowContainer.getShow().x + 560,
            "onComplete":endFly,
            "ease":Back.easeIn
         });
      }
      
      private function endFly() : void
      {
         if(m_petShowContainer.getShow().parent)
         {
            m_petShowContainer.getShow().parent.removeChild(m_petShowContainer.getShow());
         }
         endHatch();
      }
      
      public function get m_remainTimeForCompleteEnergy() : uint
      {
         return _antiwear.m_remainTimeForCompleteEnergy;
      }
      
      public function set m_remainTimeForCompleteEnergy(param1:uint) : void
      {
         _antiwear.m_remainTimeForCompleteEnergy = param1;
      }
   }
}

