package UI.Button
{
   import flash.events.MouseEvent;
   
   public class QuitBtn4 extends QuitBtn
   {
      public var isLoaded:Boolean = false;
      
      public function QuitBtn4()
      {
         super();
         isLoaded = false;
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         if(isLoaded)
         {
            super.clickBtn(null);
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         isLoaded = false;
      }
   }
}

