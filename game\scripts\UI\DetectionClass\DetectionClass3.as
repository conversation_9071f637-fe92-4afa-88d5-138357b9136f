package UI.DetectionClass
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.InitUI;
   import UI.Players.Player;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import UI2.ProgramStartData.PetSkillProMaxValueData;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.GameData;
   import YJFY.Utils.ClearUtil;
   
   public class DetectionClass3
   {
      private var m_allPetSerial:Array = ["tyrannosaurs","xiaoPiKaQiu","shiTouRen","huoRen","jiQiRen","bingLong","liHe","jian","lu"];
      
      private var m_insetGemMaxLevel:int = 8;
      
      private var m_detectGemStartLevel:int = 8;
      
      private var m_lastPlayer1:Player;
      
      private var m_lastPlayer2:Player;
      
      private var m_lastPublicEquipmentVOs:Vector.<EquipmentVO>;
      
      private var m_playerDatas:PlayerDatas;
      
      private var m_playerData:InitPlayersData;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      public function DetectionClass3()
      {
         super();
         m_getPlayerDataListener = new GetPlayerDataListener();
         m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData2;
         m_playerDatas = new PlayerDatas();
         m_playerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_playerDatas.init();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_allPetSerial);
         m_allPetSerial = null;
         ClearUtil.clearObject(m_lastPlayer1);
         m_lastPlayer1 = null;
         ClearUtil.clearObject(m_lastPlayer2);
         m_lastPlayer2 = null;
         ClearUtil.clearObject(m_lastPublicEquipmentVOs);
         m_lastPublicEquipmentVOs = null;
         ClearUtil.clearObject(m_playerDatas);
         m_playerDatas = null;
         ClearUtil.clearObject(m_playerData);
         m_playerData = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
      }
      
      public function init(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(Boolean(m_lastPlayer1) || Boolean(m_lastPlayer2) || Boolean(m_lastPublicEquipmentVOs))
         {
            throw new Error("DetectionClass3 don\'t reInit.");
         }
         m_playerDatas.getPlayerData2(GameData.getInstance().getLoginReturnData().getUid(),GameData.getInstance().getSaveFileData().index,param1,1791,false);
         m_lastPublicEquipmentVOs = InitUI.getInstance().initPublicStorage(param1.Data[0],XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
      }
      
      public function detectionByOriginalData(param1:Player, param2:Player, param3:CheatErrorData) : void
      {
         if(m_lastPlayer1 == null)
         {
            return;
         }
         var _loc5_:Number = CurrentTicketPointManager.getInstance().getTotalTicketPoint();
         var _loc4_:EquipmentData = new EquipmentData();
         var _loc6_:EquipmentData = new EquipmentData();
         checkPlayer(m_lastPlayer1,param1,_loc5_,_loc4_,_loc6_,param3);
         if(param2)
         {
            checkPlayer(m_lastPlayer2,param2,_loc5_,_loc4_,_loc6_,param3);
         }
         checkEquipments(m_lastPublicEquipmentVOs,GamingUI.getInstance().publicStorageEquipmentVOs,_loc5_,_loc4_,_loc6_);
         detectionPetData(_loc4_,_loc6_,_loc5_,param3);
         detectionInsetNum(_loc4_,_loc6_,_loc5_,param3);
         if(_loc6_.getGiftBag1Num() > 1 || _loc5_ <= 0 && _loc6_.getGiftBag1Num() > 0)
         {
            param3.resetData(true,"1005","首冲礼包数量出错，玩家现有首冲礼包数量：" + _loc6_.getGiftBag1Num() + "他的充值点券：" + _loc5_);
            return;
         }
      }
      
      private function detectionInsetNum(param1:EquipmentData, param2:EquipmentData, param3:Number, param4:CheatErrorData) : void
      {
         var _loc7_:int = 0;
         var _loc6_:* = 0;
         var _loc5_:* = 0;
         _loc7_ = m_detectGemStartLevel;
         while(_loc7_ < m_insetGemMaxLevel)
         {
            _loc5_ = uint(param2.getInsetGemNumByLevel(_loc7_.toString()));
            _loc6_ = uint(param1.getInsetGemNumByLevel(_loc7_.toString()));
            if(_loc5_ - _loc6_ > 1 && param3 < 100)
            {
               param4.resetData(true,"1004","玩家充值金额小于100, 等级为 " + _loc7_ + "的镶嵌宝石数量出错" + "lastNum:" + _loc6_ + "nowNum:" + _loc5_);
               return;
            }
            _loc7_++;
         }
      }
      
      private function detectionPetData(param1:EquipmentData, param2:EquipmentData, param3:Number, param4:CheatErrorData) : void
      {
         var _loc9_:int = 0;
         var _loc13_:int = 0;
         var _loc14_:PetData = null;
         var _loc10_:PetData = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc5_:PetSkillProMaxValueData = null;
         _loc13_ = int(m_allPetSerial.length);
         _loc8_ = int(ProgramStartData.getInstance().getPetPassiveSkillProMaxValueDataNum());
         var _loc11_:* = 0;
         var _loc12_:* = 0;
         var _loc15_:* = 0;
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            _loc14_ = param1.getPetDataByPetSerial(m_allPetSerial[_loc9_]);
            _loc10_ = param2.getPetDataByPetSerial(m_allPetSerial[_loc9_]);
            _loc6_ = (!!_loc10_ ? _loc10_.getNumOfAdvancePet() : 0) - (!!_loc14_ ? _loc14_.getNumOfAdvancePet() : 0);
            if(_loc6_ > 1)
            {
               param4.resetData(true,"1007","超进化宠物数量出错， 宠物类别：" + m_allPetSerial[_loc9_] + "该系列超进化宠物数量本次为：" + (!!_loc10_ ? _loc10_.getNumOfAdvancePet() : 0) + "上次为：" + (!!_loc14_ ? _loc14_.getNumOfAdvancePet() : 0) + "差超过了" + 1);
               return;
            }
            if(_loc10_)
            {
               _loc7_ = 0;
               while(_loc7_ < _loc8_)
               {
                  _loc5_ = ProgramStartData.getInstance().getPetPassiveSkillProMaxValueDataByIndex(_loc7_);
                  _loc11_ = 0;
                  _loc12_ = 0;
                  _loc15_ = _loc5_.getPetSkillProMaxValue();
                  if(_loc14_)
                  {
                     _loc11_ = uint(_loc14_.getMaxValueOfProOfPetPassiveSkill(_loc5_.getPetSkillName()));
                  }
                  _loc12_ = uint(_loc10_.getMaxValueOfProOfPetPassiveSkill(_loc5_.getPetSkillName()));
                  if(_loc12_ - _loc11_ > _loc15_)
                  {
                     param4.resetData(true,"1006","宠物幻化出错, 宠物类型：" + m_allPetSerial[_loc9_] + "技能类名：" + _loc5_.getPetSkillName() + "的幻化值本次为：" + _loc12_ + "上次为：" + _loc11_ + "超过了一次提升最大值：" + _loc15_);
                     return;
                  }
                  _loc7_++;
               }
            }
            _loc9_++;
         }
      }
      
      private function checkPlayer(param1:Player, param2:Player, param3:Number, param4:EquipmentData, param5:EquipmentData, param6:CheatErrorData) : void
      {
         var _loc7_:int = param2.playerVO.level;
         var _loc8_:int = param1.playerVO.level;
         if(_loc7_ > 45)
         {
            if(_loc8_ < 45)
            {
               param6.resetData(true,"1002","人物等级出错， lastLevel:" + _loc8_ + " nowLevel:" + _loc7_);
               return;
            }
            if(_loc7_ - _loc8_ > 1)
            {
               param6.resetData(true,"1002","人物等级出错， lastLevel:" + _loc8_ + " nowLevel:" + _loc7_);
               return;
            }
         }
         checkEquipments(param1.playerVO.packageEquipmentVOs,param2.playerVO.packageEquipmentVOs,param3,param4,param5);
         checkEquipments(param1.playerVO.storageEquipmentVOs,param2.playerVO.storageEquipmentVOs,param3,param4,param5);
         checkEquipments(param1.playerVO.inforEquipmentVOs,param2.playerVO.inforEquipmentVOs,param3,param4,param5);
         checkPetEquipment(!!param1.playerVO.pet ? param1.playerVO.pet.petEquipmentVO : null,!!param2.playerVO.pet ? param2.playerVO.pet.petEquipmentVO : null,param3,param4,param5);
      }
      
      private function checkPetEquipment(param1:PetEquipmentVO, param2:PetEquipmentVO, param3:uint, param4:EquipmentData, param5:EquipmentData) : void
      {
         if(param2)
         {
            recordPetData(param5,param2);
         }
         if(param1)
         {
            recordPetData(param4,param1);
         }
      }
      
      private function checkEquipments(param1:Vector.<EquipmentVO>, param2:Vector.<EquipmentVO>, param3:Number, param4:EquipmentData, param5:EquipmentData) : void
      {
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         _loc6_ = int(param2.length);
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            if(param2[_loc7_] != null)
            {
               if(param2[_loc7_].equipmentType == "pet")
               {
                  recordPetData(param5,param2[_loc7_] as PetEquipmentVO);
               }
               else if(param2[_loc7_].equipmentType == "insetGem")
               {
                  param5.setInsetGemNumbyLevel(param2[_loc7_].level.toString(),param5.getInsetGemNumByLevel(param2[_loc7_].level.toString()) + 1);
               }
               else if(param2[_loc7_].id == 11100001)
               {
                  param5.setGiftBag1Num(param5.getGiftBag1Num() + 1);
               }
               checkInsetGemNumInEquipment(param2[_loc7_],param5);
            }
            _loc7_++;
         }
         _loc6_ = int(param1.length);
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            if(param1[_loc7_] != null)
            {
               if(param1[_loc7_].equipmentType == "pet")
               {
                  recordPetData(param4,param1[_loc7_] as PetEquipmentVO);
               }
               else if(param1[_loc7_].equipmentType == "insetGem")
               {
                  param4.setInsetGemNumbyLevel(param1[_loc7_].level.toString(),param4.getInsetGemNumByLevel(param1[_loc7_].level.toString()) + 1);
               }
               else if(param1[_loc7_].id == 11100001)
               {
                  param4.setGiftBag1Num(param4.getGiftBag1Num() + 1);
               }
               checkInsetGemNumInEquipment(param1[_loc7_],param4);
            }
            _loc7_++;
         }
      }
      
      private function checkInsetGemNumInEquipment(param1:EquipmentVO, param2:EquipmentData) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = !!(param1 as AbleEquipmentVO) ? (param1 as AbleEquipmentVO).getHoleNum() : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if((param1 as AbleEquipmentVO).getInsetGem(_loc5_))
            {
               param2.setInsetGemNumbyLevel((param1 as AbleEquipmentVO).getInsetGem(_loc5_).level.toString(),param2.getInsetGemNumByLevel((param1 as AbleEquipmentVO).getInsetGem(_loc5_).level.toString()) + 1);
            }
            _loc5_++;
         }
      }
      
      private function recordPetData(param1:EquipmentData, param2:PetEquipmentVO) : void
      {
         var _loc5_:PetData = null;
         var _loc10_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:String = null;
         var _loc4_:Vector.<SkillVO> = param2.passiveSkillVOs;
         _loc5_ = param1.getPetDataByPetSerial(param2.petSeries);
         if(_loc5_ == null)
         {
            _loc5_ = new PetData();
            _loc5_.setNumOfAdvancePet(0);
            param1.setPetDataByPetSerial(param2.petSeries,_loc5_);
         }
         if(param2.level > 3)
         {
            _loc5_.setNumOfAdvancePet(_loc5_.getNumOfAdvancePet() + 1);
         }
         _loc6_ = !!_loc4_ ? _loc4_.length : 0;
         _loc10_ = 0;
         while(_loc10_ < _loc6_)
         {
            _loc9_ = _loc4_[_loc10_].className;
            _loc8_ = (_loc4_[_loc10_] as PetPassiveSkillVO).promoteValue;
            if(_loc5_.getMaxValueOfProOfPetPassiveSkill(_loc9_) < _loc8_)
            {
               _loc5_.setMaxValueOfProOfPetPassiveSkill(_loc9_,_loc8_);
            }
            _loc10_++;
         }
      }
      
      private function getPlayerData2(param1:InitPlayersData, param2:PlayerDatas) : void
      {
         if(param2 != m_playerDatas)
         {
            throw new Error("出错了");
         }
         m_playerData = param1;
         m_lastPlayer1 = m_playerData.player1;
         m_lastPlayer2 = m_playerData.player2;
      }
   }
}

