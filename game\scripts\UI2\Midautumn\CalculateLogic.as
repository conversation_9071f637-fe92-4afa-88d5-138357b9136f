package UI2.Midautumn
{
   public class CalculateLogic
   {
      private var m_vect:Vector.<DiceItem>;
      
      public function CalculateLogic()
      {
         super();
      }
      
      public function init(param1:Vector.<DiceItem>) : void
      {
         m_vect = param1;
      }
      
      public function getResult() : int
      {
         var _loc1_:int = 9;
         if(checkzycjh())
         {
            _loc1_ = 1;
         }
         else if(checkzy())
         {
            _loc1_ = 2;
         }
         else if(checkzy2() || checkzy3())
         {
            _loc1_ = 3;
         }
         else if(checkbangyan())
         {
            _loc1_ = 4;
         }
         else if(checksanhong())
         {
            _loc1_ = 5;
         }
         else if(checksijin() != 4)
         {
            _loc1_ = 6;
         }
         else if(checkerju())
         {
            _loc1_ = 7;
         }
         else if(checkyixiu())
         {
            _loc1_ = 8;
         }
         return _loc1_;
      }
      
      private function checkzycjh() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_vect.length)
         {
            if(m_vect[_loc3_].getResult() == 4)
            {
               _loc2_++;
            }
            _loc3_++;
         }
         if(_loc2_ != 4)
         {
            return false;
         }
         _loc3_ = 0;
         while(_loc3_ < m_vect.length)
         {
            if(m_vect[_loc3_].getResult() == 1)
            {
               _loc1_++;
            }
            _loc3_++;
         }
         if(_loc1_ != 2)
         {
            return false;
         }
         return true;
      }
      
      private function checkzy() : Boolean
      {
         var _loc2_:int = 0;
         if(m_vect.length <= 0)
         {
            return false;
         }
         var _loc1_:int = m_vect[0].getResult();
         _loc2_ = 1;
         while(_loc2_ < m_vect.length)
         {
            if(_loc1_ != m_vect[_loc2_].getResult())
            {
               return false;
            }
            _loc2_++;
         }
         return true;
      }
      
      private function checkzy2() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_vect.length)
         {
            if(m_vect[_loc2_].getResult() == 4)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ >= 4)
         {
            return true;
         }
         return false;
      }
      
      private function checkzy3() : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_vect.length)
         {
            _loc1_ = 0;
            _loc2_ = 0;
            while(_loc2_ < m_vect.length)
            {
               if(m_vect[_loc3_].getResult() == m_vect[_loc2_].getResult())
               {
                  _loc1_++;
               }
               _loc2_++;
            }
            if(_loc1_ >= 5)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      private function checkbangyan() : Boolean
      {
         var _loc7_:int = 0;
         var _loc1_:Boolean = false;
         var _loc3_:Boolean = false;
         var _loc2_:Boolean = false;
         var _loc5_:Boolean = false;
         var _loc4_:Boolean = false;
         var _loc6_:Boolean = false;
         _loc7_ = 0;
         while(_loc7_ < m_vect.length)
         {
            if(m_vect[_loc7_].getResult() == 1)
            {
               _loc1_ = true;
            }
            else if(m_vect[_loc7_].getResult() == 2)
            {
               _loc3_ = true;
            }
            else if(m_vect[_loc7_].getResult() == 3)
            {
               _loc2_ = true;
            }
            else if(m_vect[_loc7_].getResult() == 4)
            {
               _loc5_ = true;
            }
            else if(m_vect[_loc7_].getResult() == 5)
            {
               _loc4_ = true;
            }
            else if(m_vect[_loc7_].getResult() == 6)
            {
               _loc6_ = true;
            }
            _loc7_++;
         }
         if(_loc1_ && _loc3_ && _loc2_ && _loc5_ && _loc4_ && _loc6_)
         {
            return true;
         }
         return false;
      }
      
      private function checksanhong() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_vect.length)
         {
            if(m_vect[_loc2_].getResult() == 4)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ == 3)
         {
            return true;
         }
         return false;
      }
      
      private function checksijin() : int
      {
         var _loc4_:int = 0;
         var _loc3_:int = -1;
         var _loc2_:int = 0;
         var _loc6_:int = 0;
         _loc6_ = 0;
         while(_loc6_ < 6)
         {
            _loc2_ = 0;
            _loc4_ = 0;
            while(_loc4_ < m_vect.length)
            {
               if(m_vect[_loc4_].getResult() == _loc6_ + 1)
               {
                  _loc2_++;
               }
               _loc4_++;
            }
            if(_loc2_ == 4)
            {
               _loc3_ = _loc6_ + 1;
               break;
            }
            _loc6_++;
         }
         if(_loc3_ == -1)
         {
            return 4;
         }
         var _loc1_:Array = [];
         _loc6_ = 0;
         while(_loc6_ < m_vect.length)
         {
            if(_loc3_ != m_vect[_loc6_].getResult())
            {
               _loc1_.push(m_vect[_loc6_].getResult());
            }
            _loc6_++;
         }
         if(_loc1_.length < 2)
         {
            _loc1_.push(_loc3_);
         }
         _loc2_ = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc1_.length)
         {
            if(int(_loc1_[_loc6_]) == 4)
            {
               _loc2_++;
            }
            _loc6_++;
         }
         if(_loc2_ == 0)
         {
            return 1;
         }
         if(_loc2_ == 1)
         {
            return 3;
         }
         return 2;
      }
      
      private function checkerju() : Boolean
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_vect.length)
         {
            if(m_vect[_loc2_].getResult() == 4)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ == 2)
         {
            return true;
         }
         return false;
      }
      
      private function checkyixiu() : Boolean
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_vect.length)
         {
            if(m_vect[_loc2_].getResult() == 4)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ == 1)
         {
            return true;
         }
         return false;
      }
   }
}

