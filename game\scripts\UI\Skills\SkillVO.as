package UI.Skills
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class SkillVO
   {
      public var className:String;
      
      public var name:String;
      
      public var description:String;
      
      public var type:String;
      
      public var owner:String;
      
      private var _id:String;
      
      private var _level:int;
      
      private var _upgradePrice:int;
      
      private var _maxLevel:int;
      
      private var _serialNumber:int;
      
      private var _initLevel:int;
      
      private var _isActive:Boolean = true;
      
      public var isUntileUse:Boolean = false;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function SkillVO()
      {
         super();
         init();
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.level = _level;
         _antiwear.upgradePrice = _upgradePrice;
         _antiwear.maxLevel = _maxLevel;
         _antiwear.serialNumber = _serialNumber;
         _antiwear.initLevel = _initLevel;
         _antiwear.isActive = _isActive;
      }
      
      public function upgrade() : void
      {
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function clone() : SkillVO
      {
         var _loc1_:SkillVO = new SkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      protected function cloneAttribue(param1:SkillVO) : void
      {
         param1.id = this.id;
         param1.name = this.name;
         param1.description = this.description;
         param1.type = this.type;
         param1.className = this.className;
         param1.owner = this.owner;
         param1.level = this.level;
         param1.upgradePrice = this.upgradePrice;
         param1.maxLevel = this.maxLevel;
         param1.serialNumber = this.serialNumber;
         param1.initLevel = this.initLevel;
         param1.isActive = this.isActive;
      }
      
      public function get id() : String
      {
         return _antiwear.id;
      }
      
      public function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      public function get level() : int
      {
         return _antiwear.level;
      }
      
      public function set level(param1:int) : void
      {
         _antiwear.level = param1;
      }
      
      public function get upgradePrice() : int
      {
         return _antiwear.upgradePrice;
      }
      
      public function set upgradePrice(param1:int) : void
      {
         _antiwear.upgradePrice = param1;
      }
      
      public function get maxLevel() : int
      {
         return _antiwear.maxLevel;
      }
      
      public function set maxLevel(param1:int) : void
      {
         _antiwear.maxLevel = param1;
      }
      
      public function get serialNumber() : int
      {
         return _antiwear.serialNumber;
      }
      
      public function set serialNumber(param1:int) : void
      {
         _antiwear.serialNumber = param1;
      }
      
      public function get initLevel() : int
      {
         return _antiwear.initLevel;
      }
      
      public function set initLevel(param1:int) : void
      {
         _antiwear.initLevel = param1;
      }
      
      public function get isActive() : Boolean
      {
         return _antiwear.isActive;
      }
      
      public function set isActive(param1:Boolean) : void
      {
         _antiwear.isActive = param1;
      }
   }
}

