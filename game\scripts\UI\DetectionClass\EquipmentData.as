package UI.DetectionClass
{
   import UI2.ProgramStartData.PetSkillProMaxValueData;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.Utils.ClearUtil;
   
   public class EquipmentData
   {
      private var m_insetGemNumsObjByLevel:Object;
      
      private var m_giftBag1Num:int;
      
      private var m_petDatasObj:Object;
      
      public function EquipmentData()
      {
         super();
         m_insetGemNumsObjByLevel = {};
         m_petDatasObj = {};
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_insetGemNumsObjByLevel);
         m_insetGemNumsObjByLevel = null;
         ClearUtil.clearObject(m_petDatasObj);
         m_petDatasObj = null;
      }
      
      public function setInsetGemNumbyLevel(param1:String, param2:int) : void
      {
         m_insetGemNumsObjByLevel[param1] = param2;
      }
      
      public function getInsetGemNumByLevel(param1:String) : int
      {
         return int(m_insetGemNumsObjByLevel[param1]);
      }
      
      public function getGiftBag1Num() : int
      {
         return m_giftBag1Num;
      }
      
      public function setGiftBag1Num(param1:int) : void
      {
         m_giftBag1Num = param1;
      }
      
      public function setPetDataByPetSerial(param1:String, param2:PetData) : void
      {
         m_petDatasObj[param1] = param2;
      }
      
      public function getPetDataByPetSerial(param1:String) : PetData
      {
         return !!m_petDatasObj[param1] ? m_petDatasObj[param1] : null;
      }
      
      public function addEquipmentData(param1:EquipmentData) : void
      {
         var _loc5_:String = null;
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:PetSkillProMaxValueData = null;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:PetData = null;
         var _loc8_:PetData = null;
         setGiftBag1Num(getGiftBag1Num() + param1.getGiftBag1Num());
         for(_loc5_ in m_insetGemNumsObjByLevel)
         {
            setInsetGemNumbyLevel(_loc5_,getInsetGemNumByLevel(_loc5_) + param1.getInsetGemNumByLevel(_loc5_));
         }
         for(_loc5_ in m_petDatasObj)
         {
            _loc3_ = m_petDatasObj[_loc5_];
            _loc8_ = param1.getPetDataByPetSerial(_loc5_);
            _loc3_.setNumOfAdvancePet(_loc3_.getNumOfAdvancePet() + (!!_loc8_ ? _loc8_.getNumOfAdvancePet() : 0));
            _loc7_ = int(ProgramStartData.getInstance().getPetPassiveSkillProMaxValueDataNum());
            _loc9_ = 0;
            while(_loc9_ < _loc7_)
            {
               _loc2_ = ProgramStartData.getInstance().getPetPassiveSkillProMaxValueDataByIndex(_loc9_);
               _loc4_ = 0;
               _loc6_ = 0;
               _loc4_ = _loc3_.getMaxValueOfProOfPetPassiveSkill(_loc2_.getPetSkillName());
               _loc6_ = _loc8_.getMaxValueOfProOfPetPassiveSkill(_loc2_.getPetSkillName());
               if(_loc6_ > _loc4_)
               {
                  _loc3_.setMaxValueOfProOfPetPassiveSkill(_loc2_.getPetSkillName(),_loc6_);
               }
               _loc9_++;
            }
         }
      }
   }
}

