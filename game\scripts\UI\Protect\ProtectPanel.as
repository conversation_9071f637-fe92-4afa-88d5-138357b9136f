package UI.Protect
{
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.BuffData;
   import UI.Button.QuitBtn;
   import UI.EquipmentCellBackground;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Privilege.Privilege;
   import UI.Privilege.PrivilegeVO;
   import UI.Protect.Btn.GetProtectBtn;
   import UI.Protect.Btn.GetProtectGiftBtn;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ProtectPanel extends MySprite
   {
      public var timeText:TextField;
      
      public var getProtectBtn:GetProtectBtn;
      
      public var getProtectGiftBtn:GetProtectGiftBtn;
      
      public var quitBtn:QuitBtn;
      
      private var _remaineTimeForGetGiftText:TextField;
      
      public function ProtectPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         timeText = null;
         if(getProtectBtn)
         {
            getProtectBtn.clear();
         }
         getProtectBtn = null;
         if(getProtectGiftBtn)
         {
            getProtectGiftBtn.clear();
         }
         getProtectGiftBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _remaineTimeForGetGiftText = null;
      }
      
      public function startProtect() : void
      {
         getProtectGiftBtn.mouseChildren = true;
         getProtectGiftBtn.mouseEnabled = true;
         MyFunction.getInstance().changeSaturation(getProtectGiftBtn,0);
         timeText.visible = true;
      }
      
      public function endProtect() : void
      {
         getProtectGiftBtn.mouseChildren = false;
         getProtectGiftBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(getProtectGiftBtn,-100);
         timeText.visible = false;
      }
      
      public function setTimeText(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:int = param1 / 86400;
         _loc3_ = param1 - _loc6_ * 86400;
         var _loc5_:int = _loc3_ / 3600;
         _loc3_ -= _loc5_ * 3600;
         var _loc2_:int = _loc3_ / 60;
         if(!_loc6_ && !_loc5_ && !_loc2_)
         {
            _loc4_ = _loc3_ - _loc2_ * 60;
            timeText.text = "剩余保佑时间：" + _loc4_ + "秒";
         }
         else
         {
            timeText.text = "剩余保佑时间：" + (!!_loc6_ ? _loc6_ + "天" : "") + (!!_loc5_ ? _loc5_ + "小时" : "") + (!!_loc2_ ? _loc2_ + "分钟" : "");
         }
      }
      
      private function init() : void
      {
         var privilegeVOs:Vector.<PrivilegeVO>;
         var font:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         var proctedXml:XML = XMLSingle.getInstance().buffXML;
         var giftBag:Vector.<EquipmentVO> = XMLSingle.getEquipmentVOs(proctedXml.item[0].giftBag[0],XMLSingle.getInstance().equipmentXML,true);
         arrangeGift(giftBag);
         privilegeVOs = XMLSingle.getPrivilegeVOs(String(proctedXml.item[0].@privileges),XMLSingle.getInstance().privilegeXML);
         arrangePrivilege(privilegeVOs);
         getProtectGiftBtn.mouseChildren = false;
         getProtectGiftBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(getProtectGiftBtn,-100);
         timeText.visible = false;
         timeText.defaultTextFormat = new TextFormat(font.fontName,20,16766337);
         timeText.filters = [new GlowFilter(0,1,2,2,10,3)];
         timeText.selectable = false;
         timeText.embedFonts = true;
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc2_:BuffVO = null;
            var _loc3_:int = 0;
            var _loc5_:int = 0;
            var _loc4_:int = int(BuffData.getInstance().buffDrives.length);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc2_ = BuffData.getInstance().buffDrives[_loc5_].buffVO;
               if(_loc2_.className == "Buff_ProtectPrivileges")
               {
                  _loc3_ = new TimeUtil().timeInterval(ProtectData.getInstance().getGiftDate,param1);
                  if(_loc3_ > 168)
                  {
                     if(getProtectGiftBtn)
                     {
                        getProtectGiftBtn.mouseChildren = true;
                        getProtectGiftBtn.mouseEnabled = true;
                        MyFunction.getInstance().changeSaturation(getProtectGiftBtn,0);
                     }
                  }
                  else if(getProtectGiftBtn)
                  {
                     if(!_remaineTimeForGetGiftText)
                     {
                        _remaineTimeForGetGiftText = new TextField();
                     }
                     _remaineTimeForGetGiftText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,15,16777215);
                     _remaineTimeForGetGiftText.selectable = false;
                     _remaineTimeForGetGiftText.x = 600;
                     _remaineTimeForGetGiftText.y = 515;
                     _remaineTimeForGetGiftText.text = "下次领取：" + Math.ceil((168 - _loc3_) / 24) + "天后！";
                     _remaineTimeForGetGiftText.width = _remaineTimeForGetGiftText.textWidth + 5;
                     _remaineTimeForGetGiftText.height = _remaineTimeForGetGiftText.textHeight + 3;
                     addChild(_remaineTimeForGetGiftText);
                  }
                  if(timeText)
                  {
                     timeText.visible = true;
                  }
               }
               _loc5_++;
            }
         },showWarningBox);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("clickGetProtectGiftBtn",getGift,true,0,true);
         addEventListener("clickGetProtectBtn",getProtect,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("clickGetProtectGiftBtn",getGift,true);
         removeEventListener("clickGetProtectBtn",getProtect,true);
      }
      
      private function getProtect(param1:UIBtnEvent) : void
      {
         var _loc4_:BuyProtectBox = null;
         var _loc5_:int = 0;
         var _loc3_:int = numChildren;
         var _loc2_:Boolean = false;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(getChildAt(_loc5_) is BuyProtectBox)
            {
               _loc2_ = true;
            }
            _loc5_++;
         }
         if(!_loc2_)
         {
            _loc4_ = new BuyProtectBox();
            _loc4_.x = (stage.stageWidth - _loc4_.width) / 2;
            _loc4_.y = (stage.stageHeight - _loc4_.height) / 2;
            addChild(_loc4_);
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function arrangeGift(param1:Vector.<EquipmentVO>) : void
      {
         var _loc4_:Equipment = null;
         var _loc2_:EquipmentCellBackground = null;
         var _loc5_:int = 0;
         var _loc3_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_ = new EquipmentCellBackground();
            _loc2_.x = 445 + 50 * _loc5_;
            _loc2_.y = 360;
            addChild(_loc2_);
            _loc4_ = MyFunction2.sheatheEquipmentShell(param1[_loc5_]);
            _loc4_.x = _loc2_.x;
            _loc4_.y = _loc2_.y;
            _loc4_.addEventListener("rollOver",equipmentInfor,false,0,true);
            _loc4_.addEventListener("rollOut",equipmentInfor,false,0,true);
            addChild(_loc4_ as DisplayObject);
            _loc5_++;
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function arrangePrivilege(param1:Vector.<PrivilegeVO>) : void
      {
         var _loc2_:Privilege = null;
         var _loc4_:int = 0;
         var _loc3_:int = int(param1.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = new Privilege(param1[_loc4_]);
            _loc2_.x = 418 + (_loc2_.width + 5) * (_loc4_ % width);
            _loc2_.y = 155 + (_loc2_.height + 5) * (int(_loc4_ / width));
            addChild(_loc2_);
            _loc4_++;
         }
      }
      
      private function getGift(param1:UIBtnEvent) : void
      {
         var _loc2_:* = undefined;
         getProtectGiftBtn.mouseChildren = false;
         getProtectGiftBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(getProtectGiftBtn,-100);
         var _loc3_:XML = XMLSingle.getInstance().buffXML;
         _loc2_ = XMLSingle.getEquipmentVOs(_loc3_.item[0].giftBag[0],XMLSingle.getInstance().equipmentXML,true);
         dispatchEvent(new UIPassiveEvent("addGifts",{
            "successFunction":getedGift,
            "successFunParams":[],
            "failFunction":showWarningBox,
            "failFunParams":["背包空间不足，放不下礼包！",0],
            "giftBags":_loc2_
         }));
      }
      
      private function getedGift() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            ProtectData.getInstance().getGiftDate = param1;
            if(getProtectGiftBtn)
            {
               getProtectGiftBtn.mouseChildren = false;
               getProtectGiftBtn.mouseEnabled = false;
               MyFunction.getInstance().changeSaturation(getProtectGiftBtn,-100);
               showWarningBox("礼包领取成功！");
            }
         },showWarningBox);
      }
      
      public function showWarningBox(param1:String, param2:int = 0) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

