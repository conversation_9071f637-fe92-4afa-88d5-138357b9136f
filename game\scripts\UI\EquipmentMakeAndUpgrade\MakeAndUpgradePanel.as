package UI.EquipmentMakeAndUpgrade
{
   import UI.DetectionClass.DetectionClass;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyControlPanel;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class MakeAndUpgradePanel extends MySprite
   {
      public var makePanel:EquipmentMakePanel;
      
      public var upgradePanel:EquipmentUpgradePanel;
      
      public var makeBtn:MovieClip;
      
      public var upgradeBtn:MovieClip;
      
      public var openHoleBtn:MovieClip;
      
      public var gemInsetBtn:MovieClip;
      
      public var gemRemoveBtn:MovieClip;
      
      public var hitTestArea:Sprite;
      
      private var _openHolePanel:OpenHolePanel;
      
      private var _gemInsetPanel:GemInsetPanel;
      
      private var _gemRemovePanel:GemRemovePanel;
      
      private var _makeBtn:MySwitchBtnLogicShell;
      
      private var _upgradeBtn:MySwitchBtnLogicShell;
      
      private var _openHoleBtn:MySwitchBtnLogicShell;
      
      private var _gemInsetBtn:MySwitchBtnLogicShell;
      
      private var _gemRemoveBtn:MySwitchBtnLogicShell;
      
      private var _switchBtns:Array;
      
      private const EQ_MAKE:String = "装备打造";
      
      private const EQ_UPGRADE:String = "装备升级";
      
      private var _equipmentVO:EquipmentVO;
      
      private var _player:Player;
      
      private var _Parent:MyControlPanel;
      
      private var _currentEquipmentProcessPanel:IEquipmentProcessPanel;
      
      public function MakeAndUpgradePanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function init(param1:MyControlPanel) : void
      {
         _makeBtn = new MySwitchBtnLogicShell();
         _makeBtn.setShow(makeBtn);
         _makeBtn.setTipString("打造装备界面");
         _upgradeBtn = new MySwitchBtnLogicShell();
         _upgradeBtn.setShow(upgradeBtn);
         _upgradeBtn.setTipString("升级装备界面");
         _openHoleBtn = new MySwitchBtnLogicShell();
         _openHoleBtn.setShow(openHoleBtn);
         _openHoleBtn.setTipString("装备开孔界面");
         _gemInsetBtn = new MySwitchBtnLogicShell();
         _gemInsetBtn.setShow(gemInsetBtn);
         _gemInsetBtn.setTipString("镶嵌宝石界面");
         _gemRemoveBtn = new MySwitchBtnLogicShell();
         _gemRemoveBtn.setShow(gemRemoveBtn);
         _gemRemoveBtn.setTipString("宝石摘除界面");
         _switchBtns = [];
         _switchBtns.push(_makeBtn);
         _switchBtns.push(_upgradeBtn);
         _switchBtns.push(_openHoleBtn);
         _switchBtns.push(_gemInsetBtn);
         _switchBtns.push(_gemRemoveBtn);
         _makeBtn.switchBtns = _switchBtns;
         _upgradeBtn.switchBtns = _switchBtns;
         _openHoleBtn.switchBtns = _switchBtns;
         _gemInsetBtn.switchBtns = _switchBtns;
         _gemRemoveBtn.switchBtns = _switchBtns;
         MySwitchBtnLogicShell.setDefaultActivateBtnFromBtns(_switchBtns);
         makePanel.init();
         upgradePanel.init();
         upgradePanel.visible = false;
         _currentEquipmentProcessPanel = makePanel;
         _Parent = param1;
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         _equipmentVO = null;
         return _currentEquipmentProcessPanel.putOutEquipmentVO(param1);
      }
      
      public function putInEquipmentVO(param1:EquipmentVO) : Vector.<EquipmentVO>
      {
         if(param1)
         {
            DetectionClass.getInstance().detectionEquipmentVO(param1);
         }
         var _loc2_:Vector.<EquipmentVO> = putOutEquipmentVO(param1);
         _equipmentVO = param1;
         _currentEquipmentProcessPanel.putInEquipmentVO(param1,_player);
         return _loc2_;
      }
      
      public function isCanPutEquipmentVO(param1:EquipmentVO) : IsCanPutInfo
      {
         var _loc2_:IsCanPutInfo = new IsCanPutInfo();
         switch(_currentEquipmentProcessPanel)
         {
            case makePanel:
               if(param1 is ScrollEquipmentVO)
               {
                  _loc2_.isCanPut = true;
                  break;
               }
               _loc2_.isCanPut = false;
               _loc2_.message = "只有模具才能用来进行装备打造！";
               break;
            case upgradePanel:
               if(!(param1 is AbleEquipmentVO))
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "该物品无法强化！";
                  break;
               }
               if((param1 as AbleEquipmentVO).level >= (param1 as AbleEquipmentVO).maxLevel)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "此物品已经是最大级！";
                  break;
               }
               _loc2_.isCanPut = true;
               break;
            case _openHolePanel:
               if(!(param1 is AbleEquipmentVO) || (param1 as AbleEquipmentVO).maxHoleNum == 0)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "该装备无法打孔！";
                  break;
               }
               if((param1 as AbleEquipmentVO).getHoleNum() >= (param1 as AbleEquipmentVO).maxHoleNum)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "该装备的孔数已达最大数量， 不能再开孔了！";
                  break;
               }
               _loc2_.isCanPut = true;
               break;
            case _gemInsetPanel:
               _loc2_ = _gemInsetPanel.isCanPutEquipmentVO(param1);
               break;
            case _gemRemovePanel:
               _loc2_ = _gemRemovePanel.isCanPutEquipmentVO(param1);
         }
         return _loc2_;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(makePanel)
         {
            makePanel.clear();
         }
         makePanel = null;
         if(upgradePanel)
         {
            upgradePanel.clear();
         }
         upgradePanel = null;
         if(_openHolePanel)
         {
            _openHolePanel.clear();
         }
         _openHolePanel = null;
         if(_gemInsetPanel)
         {
            _gemInsetPanel.clear();
         }
         _gemInsetPanel = null;
         if(_gemRemovePanel)
         {
            _gemRemovePanel.clear();
         }
         _gemRemovePanel = null;
         if(_makeBtn)
         {
            _makeBtn.clear();
         }
         _makeBtn = null;
         if(_upgradeBtn)
         {
            _upgradeBtn.clear();
         }
         _upgradeBtn = null;
         if(_openHoleBtn)
         {
            _openHoleBtn.clear();
         }
         _openHoleBtn = null;
         if(_gemInsetBtn)
         {
            _gemInsetBtn.clear();
         }
         _gemInsetBtn = null;
         if(_gemRemoveBtn)
         {
            _gemRemoveBtn.clear();
         }
         _gemRemoveBtn = null;
         makeBtn = null;
         upgradeBtn = null;
         openHoleBtn = null;
         gemInsetBtn = null;
         gemRemoveBtn = null;
         hitTestArea = null;
         _equipmentVO = null;
         _player = null;
         _currentEquipmentProcessPanel = null;
         ClearUtil.nullArr(_switchBtns);
         _switchBtns = null;
      }
      
      public function get player() : Player
      {
         return _player;
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",switchPanel,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",switchPanel,true);
      }
      
      protected function switchPanel(param1:ButtonEvent) : void
      {
         var _loc2_:* = undefined;
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         loop0:
         switch(param1.button)
         {
            case _makeBtn:
            case _upgradeBtn:
            case _openHoleBtn:
            case _gemInsetBtn:
            case _gemRemoveBtn:
               _Parent._havegoods = false;
               _loc2_ = _currentEquipmentProcessPanel.putOutEquipmentVO();
               if(_loc2_)
               {
                  ClearUtil.nullArr(_loc2_,false,false,false);
               }
               switch(param1.button)
               {
                  case _makeBtn:
                     _currentEquipmentProcessPanel = makePanel;
                     makePanel.visible = true;
                     upgradePanel.visible = false;
                     clearOpenHolePanel();
                     clearGemInsetPanel();
                     clearGemRemovePanel();
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     break loop0;
                  case _upgradeBtn:
                     _currentEquipmentProcessPanel = upgradePanel;
                     makePanel.visible = false;
                     upgradePanel.visible = true;
                     clearOpenHolePanel();
                     clearGemInsetPanel();
                     clearGemRemovePanel();
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     break loop0;
                  case _openHoleBtn:
                     makePanel.visible = false;
                     upgradePanel.visible = false;
                     clearGemInsetPanel();
                     clearGemRemovePanel();
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     if(_openHolePanel == null)
                     {
                        _openHolePanel = new OpenHolePanel(_Parent);
                     }
                     _openHolePanel.x = 40.35;
                     _openHolePanel.y = 109.05;
                     addChild(_openHolePanel);
                     _currentEquipmentProcessPanel = _openHolePanel;
                     break loop0;
                  case _gemInsetBtn:
                     makePanel.visible = false;
                     upgradePanel.visible = false;
                     clearOpenHolePanel();
                     clearGemRemovePanel();
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     if(_gemInsetPanel == null)
                     {
                        _gemInsetPanel = new GemInsetPanel(_Parent);
                     }
                     _gemInsetPanel.x = 40.35;
                     _gemInsetPanel.y = 109.05;
                     addChild(_gemInsetPanel);
                     _currentEquipmentProcessPanel = _gemInsetPanel;
                     break loop0;
                  case _gemRemoveBtn:
                     makePanel.visible = false;
                     upgradePanel.visible = false;
                     clearGemInsetPanel();
                     clearOpenHolePanel();
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     if(_gemRemovePanel == null)
                     {
                        _gemRemovePanel = new GemRemovePanel(_Parent);
                     }
                     _gemRemovePanel.x = 40.35;
                     _gemRemovePanel.y = 109.05;
                     addChild(_gemRemovePanel);
                     _currentEquipmentProcessPanel = _gemRemovePanel;
               }
         }
      }
      
      private function clearOpenHolePanel() : void
      {
         if(_openHolePanel)
         {
            if(_openHolePanel.parent)
            {
               _openHolePanel.parent.removeChild(_openHolePanel);
            }
            _openHolePanel.clear();
            _openHolePanel = null;
         }
      }
      
      private function clearGemInsetPanel() : void
      {
         if(_gemInsetPanel)
         {
            if(_gemInsetPanel.parent)
            {
               _gemInsetPanel.parent.removeChild(_gemInsetPanel);
            }
            _gemInsetPanel.clear();
            _gemInsetPanel = null;
         }
      }
      
      private function clearGemRemovePanel() : void
      {
         if(_gemRemovePanel)
         {
            if(_gemRemovePanel.parent)
            {
               _gemRemovePanel.parent.removeChild(_gemRemovePanel);
            }
            _gemRemovePanel.clear();
            _gemRemovePanel = null;
         }
      }
   }
}

