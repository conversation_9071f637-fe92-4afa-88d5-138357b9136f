package UI.SocietySystem.SocialChatPanel
{
   import UI.GamingUI;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   import UI.UIConstant.UIConstantData;
   
   public class NewGotChatInfor
   {
      private const m_const_systemChat:String = "systemChat";
      
      private var m_chatHeadStr:String;
      
      private var m_chatHeadFontColor:uint;
      
      private var m_chatHeadFontSize:uint;
      
      private var m_chatStr:String;
      
      private var m_chatFontColor:uint;
      
      private var m_chatFontSize:uint;
      
      public function NewGotChatInfor()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function init(param1:DOWN_GetChatInfor) : void
      {
         m_chatStr = "";
         m_chatHeadStr = "";
         var _loc3_:Boolean = GamingUI.getInstance().player1.getSocietyDataVO().getUid_leader() == param1.getUid() && GamingUI.getInstance().player1.getSocietyDataVO().getIdx_leader() == param1.getIdx();
         m_chatHeadStr += "[" + (!!param1.getName() ? param1.getName() : param1.getUid()) + (_loc3_ ? "(帮主)" : "") + "]： ";
         m_chatStr += param1.getChatStr() + "\r";
         var _loc2_:String = getChatTypeInfor(param1);
         switch(_loc2_)
         {
            case "systemChat":
               m_chatHeadFontColor = 16711680;
               m_chatHeadFontSize = 20;
               break;
            case "SunWuKong":
               m_chatHeadFontColor = 16756542;
               m_chatHeadFontSize = 18;
               break;
            case "BaiLongMa":
               m_chatHeadFontColor = 50943;
               m_chatHeadFontSize = 18;
               break;
            case "ErLangShen":
               m_chatHeadFontColor = 13500490;
               m_chatHeadFontSize = 18;
               break;
            case "ChangE":
               m_chatHeadFontColor = 16742299;
               m_chatHeadFontSize = 18;
               break;
            case UIConstantData:
               m_chatFontColor = 16717269;
               m_chatHeadFontSize = 18;
               break;
            default:
               m_chatHeadFontColor = 0;
               m_chatHeadFontSize = 16;
         }
         var _loc4_:* = _loc2_;
         if("systemChat" !== _loc4_)
         {
            m_chatFontColor = 16777215;
            m_chatFontSize = 16;
         }
         else
         {
            m_chatFontColor = 16711680;
            m_chatFontSize = 20;
         }
      }
      
      public function getChatHeadStr() : String
      {
         return m_chatHeadStr;
      }
      
      public function getChatHeadFontColor() : uint
      {
         return m_chatHeadFontColor;
      }
      
      public function getChatHeadFontSize() : uint
      {
         return m_chatHeadFontSize;
      }
      
      public function getChatStr() : String
      {
         return m_chatStr;
      }
      
      public function getChatFontColor() : uint
      {
         return m_chatFontColor;
      }
      
      public function getChatFontSize() : uint
      {
         return m_chatFontSize;
      }
      
      private function getChatTypeInfor(param1:DOWN_GetChatInfor) : String
      {
         var _loc2_:String = null;
         switch(param1.getExtraData() + 1)
         {
            case 0:
               _loc2_ = "systemChat";
               break;
            case 2:
               _loc2_ = "SunWuKong";
               break;
            case 3:
               _loc2_ = "BaiLongMa";
               break;
            case 4:
               _loc2_ = "ErLangShen";
               break;
            case 5:
               _loc2_ = "ChangE";
               break;
            case 6:
               _loc2_ = "Fox";
               break;
            case 7:
               _loc2_ = "TieShan";
               break;
            default:
               _loc2_ = "";
         }
         return _loc2_;
      }
      
      public function getUpChatTypeDataByPlayerType() : int
      {
         switch(GamingUI.getInstance().player1.playerVO.playerType)
         {
            case "SunWuKong":
               return 1;
            case "BaiLongMa":
               return 2;
            case "ErLangShen":
               return 3;
            case "ChangE":
               return 4;
            case "Fox":
               return 5;
            case "TieShan":
               return 6;
            default:
               return 0;
         }
      }
   }
}

