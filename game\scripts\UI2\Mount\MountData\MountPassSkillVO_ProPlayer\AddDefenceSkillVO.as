package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddDefenceSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addDefence:uint;
      
      public function AddDefenceSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("defence_mountAdd1",m_targetPlayer.playerVO.get2("defence_mountAdd1") - m_addValue);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addDefence;
         m_targetPlayer.playerVO.set2("defence_mountAdd1",m_targetPlayer.playerVO.get2("defence_mountAdd1") + m_addValue);
      }
      
      public function getAddDefence() : uint
      {
         return addDefence;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addDefence = uint(param1.data.(@att == "addDefence")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addDefence = m_addDefence;
      }
      
      private function get addDefence() : uint
      {
         return _antiwear.addDefence;
      }
      
      private function set addDefence(param1:uint) : void
      {
         _antiwear.addDefence = param1;
      }
   }
}

