package UI.Shop
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.SureAndCancelBtn;
   import UI.EquipmentCells.ShopEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.tehui.TeHuiData;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class BuyPopUpBox extends MySprite
   {
      public static const SHOP_MONEY_PRICE:String = "shopMoneyPrice";
      
      public static const SHOP_PK_POINT_PRICE:String = "shopPkPointPrice";
      
      public static const SHOP_WALL_PRICE:String = "shopWallPrice";
      
      protected var _show:MovieClip;
      
      protected var _numBtnGroup:NumberBtnGroupLogicShell;
      
      protected var _sureBtn:SureAndCancelBtn;
      
      protected var _cancelBtn:SureAndCancelBtn;
      
      protected var _nameText:TextField;
      
      protected var _priceText:TextField;
      
      protected var _descriptionText:TextField;
      
      protected var _mengBan:MovieClip;
      
      private var equipVO:EquipmentVO;
      
      private var m_WarningBox:TehuiView;
      
      protected var tehuipanel:MovieClip;
      
      protected var priceTxt:TextField;
      
      protected var bagName:TextField;
      
      protected var remainNum:TextField;
      
      protected var buyBtn:ButtonLogicShell2;
      
      protected var item:XML;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentData:EquipmentVOsData;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      private var _wanLoadSource:Array;
      
      protected var equipmentCell:IEquipmentCell;
      
      protected var giftCell:IEquipmentCell;
      
      private var _potion:String;
      
      private var _listener:Listener;
      
      public function BuyPopUpBox(param1:EquipmentVO, param2:String)
      {
         var loadFinishLitener:LoadFinishListener1;
         var equipmentVO:EquipmentVO = param1;
         var potion:String = param2;
         _wanLoadSource = ["buyPopUpBox"];
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         m_WarningBox = new TehuiView();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_equipmentVOs = new Vector.<EquipmentVO>();
         equipmentData = new EquipmentVOsData();
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.minNum = 1;
         _numBtnGroup.maxNum = 100;
         loadFinishLitener = new LoadFinishListener1(function():void
         {
            _show = MyFunction2.returnShowByClassName("BuyPopUpBox") as MovieClip;
            addChildAt(_show,0);
            _numBtnGroup.setShow(_show["numBtnGroup"]);
            _nameText = _show["nameText"];
            _priceText = _show["priceText"];
            _descriptionText = _show["descriptionText"];
            _mengBan = _show["mengBan"];
            tehuipanel = _show["tehuipanel"] as MovieClip;
            tehuipanel.visible = false;
            priceTxt = tehuipanel["priceone"] as TextField;
            bagName = tehuipanel["bagName"] as TextField;
            remainNum = tehuipanel["count"] as TextField;
            buyBtn = new ButtonLogicShell2();
            buyBtn.setShow(tehuipanel["buyBtn"]);
            buyBtn.setTipString("点击购买");
            var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_nameText);
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_priceText);
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_descriptionText);
            MyFunction2.changeTextFieldFont(_loc1_.fontName,priceTxt);
            MyFunction2.changeTextFieldFont(_loc1_.fontName,bagName);
            MyFunction2.changeTextFieldFont(_loc1_.fontName,remainNum);
            _mengBan.buttonMode = true;
            _mengBan.addEventListener("mouseDown",mouseDownBox,false,0,true);
            _mengBan.addEventListener("mouseUp",mouseUpBox,false,0,true);
            _sureBtn = new SureAndCancelBtn();
            _sureBtn.x = 48;
            _sureBtn.y = 180;
            addChild(_sureBtn);
            _cancelBtn = new SureAndCancelBtn();
            _cancelBtn.x = 160;
            _cancelBtn.y = 180;
            addChild(_cancelBtn);
            _numBtnGroup.isOpenInput = true;
            initBox(equipmentVO,potion);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSource,loadFinishLitener);
      }
      
      public function initBox(param1:EquipmentVO, param2:String) : void
      {
         var equipmentVO:EquipmentVO = param1;
         var potion:String = param2;
         if(equipmentCell)
         {
            equipmentCell.removeEquipmentVO();
         }
         else
         {
            equipmentCell = new ShopEquipmentCell();
            equipmentCell.x = 15 + equipmentCell.width / 2;
            equipmentCell.y = 15 + equipmentCell.height / 2;
            addChild(equipmentCell as DisplayObject);
         }
         ClearUtil.clearObject(m_WarningBox);
         m_WarningBox = null;
         m_WarningBox = new TehuiView();
         equipmentCell.addEquipmentVO(equipmentVO);
         _nameText.text = equipmentVO.name;
         _descriptionText.text = equipmentVO.description;
         _potion = potion;
         if(potion == "shopMoneyPrice")
         {
            _priceText.text = equipmentVO.price + "  元宝";
         }
         else if(potion == "shopPkPointPrice")
         {
            _priceText.text = equipmentVO.pkPrice + "  PK点";
         }
         else
         {
            _priceText.text = equipmentVO.ticketPrice + "  点券";
         }
         _numBtnGroup.num = 1;
         _sureBtn.showText("确定");
         _cancelBtn.showText("取消");
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            TeHuiData.getInstance().checkNewWeek(param1);
            initTeHui(equipmentVO);
         },null,true);
      }
      
      private function initTeHui(param1:EquipmentVO) : void
      {
         var _loc4_:int = 0;
         ClearUtil.clearObject(giftCell);
         giftCell = null;
         var _loc2_:Boolean = false;
         var _loc3_:XMLList = XMLSingle.getInstance().tehui.item;
         _loc4_ = 0;
         while(_loc4_ < _loc3_.length())
         {
            if(int(_loc3_[_loc4_].@goodsid) == param1.id)
            {
               _loc2_ = true;
               item = _loc3_[_loc4_];
            }
            _loc4_++;
         }
         if(_loc2_)
         {
            tehuipanel.visible = true;
            equipVO = XMLSingle.getEquipment(int(item.@giftid),XMLSingle.getInstance().equipmentXML);
            bagName.text = equipVO.name;
            priceTxt.text = String(equipVO.ticketPrice);
            priceTxt.text = String(equipVO.ticketPrice) + "  点券";
            if(giftCell)
            {
               giftCell.removeEquipmentVO();
            }
            else
            {
               giftCell = new ShopEquipmentCell();
               giftCell.x = 353 + giftCell.width / 2;
               giftCell.y = 40 + giftCell.height / 2;
               addChild(giftCell as DisplayObject);
            }
            ClearUtil.clearObject(m_equipmentVOs);
            m_equipmentVOs = null;
            m_equipmentVOs = new Vector.<EquipmentVO>();
            m_equipmentVOs.length = 0;
            if(11000004 == int(item.@goodsid))
            {
               if(GamingUI.getInstance().player1.playerVO.level < 75)
               {
                  giftCell.addEquipmentVO(XMLSingle.getEquipmentVOByID(int(item.@giftid),XMLSingle.getInstance().equipmentXML));
                  m_equipmentVOs.push(XMLSingle.getEquipmentVOByID(int(item.@giftid),XMLSingle.getInstance().equipmentXML));
                  remainNum.text = String(int(item.@totalnum) - TeHuiData.getInstance().getBuyNum(int(item.@giftid)));
                  equipmentData.setEquipmentVOs(m_equipmentVOs);
               }
               else
               {
                  giftCell.addEquipmentVO(XMLSingle.getEquipmentVOByID(int(item.@giftid2),XMLSingle.getInstance().equipmentXML));
                  m_equipmentVOs.push(XMLSingle.getEquipmentVOByID(int(item.@giftid2),XMLSingle.getInstance().equipmentXML));
                  remainNum.text = String(int(item.@totalnum) - TeHuiData.getInstance().getBuyNum(int(item.@giftid2)));
                  equipmentData.setEquipmentVOs(m_equipmentVOs);
               }
            }
            else
            {
               giftCell.addEquipmentVO(XMLSingle.getEquipmentVOByID(int(item.@giftid),XMLSingle.getInstance().equipmentXML));
               m_equipmentVOs.push(XMLSingle.getEquipmentVOByID(int(item.@giftid),XMLSingle.getInstance().equipmentXML));
               remainNum.text = String(int(item.@totalnum) - TeHuiData.getInstance().getBuyNum(int(item.@giftid)));
               equipmentData.setEquipmentVOs(m_equipmentVOs);
            }
            if(int(remainNum.text) <= 0)
            {
               tehuipanel.visible = false;
               ClearUtil.clearObject(giftCell);
               giftCell = null;
            }
            return;
         }
         tehuipanel.visible = false;
      }
      
      public function setNumBtnGroupMaxNum(param1:int, param2:Function, param3:Array) : void
      {
         _listener = new Listener();
         _listener.outMaxNumFun = param2;
         _listener.outMaxNumFunParams = param3;
         _numBtnGroup.maxNum = param1;
         _numBtnGroup.addListener(_listener);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         ClearUtil.clearObject(m_WarningBox);
         m_WarningBox = null;
         removeEventListener("addedToStage",addToStage,false);
         removeEventListener("clickButton",clickButton,true);
         GamingUI.getInstance().loadQueue.unLoad(_wanLoadSource);
         ClearUtil.clearObject(buyBtn);
         buyBtn = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         ClearUtil.clearObject(equipmentData);
         equipmentData = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         if(_mengBan)
         {
            _mengBan.removeEventListener("mouseDown",mouseDownBox,false);
            _mengBan.removeEventListener("mouseUp",mouseUpBox,false);
         }
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_sureBtn)
         {
            _sureBtn.clear();
         }
         _sureBtn = null;
         if(_cancelBtn)
         {
            _cancelBtn.clear();
         }
         _cancelBtn = null;
         _nameText = null;
         _priceText = null;
         _descriptionText = null;
         _mengBan = null;
         if(equipmentCell)
         {
            equipmentCell.clear();
         }
         equipmentCell = null;
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         ClearUtil.nullArr(_wanLoadSource);
         _wanLoadSource = null;
         if(_listener)
         {
            _listener.clear();
         }
         _listener = null;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickSureAndCancelBtn",clickBtn,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("clickSureAndCancelBtn",clickBtn,true);
         removeEventListener("removedFromStage",removeFromStage,false);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var equipmentVOs:Vector.<EquipmentVO>;
         var e:ButtonEvent = param1;
         var _loc3_:* = e.button;
         if(buyBtn === _loc3_)
         {
            equipmentVOs = new Vector.<EquipmentVO>();
            if(TeHuiData.getInstance().getBuyNum(m_equipmentVOs[0].id) >= int(item.@totalnum))
            {
               GamingUI.getInstance().showMessageTip("本周限购次数已经用完");
            }
            else
            {
               equipmentVOs.push(equipVO.clone());
               MyFunction2.falseAddEquipmentVOs(equipmentVOs,GamingUI.getInstance().player1,function():void
               {
                  GamingUI.getInstance().showMessageTip("背包已满");
               },function():void
               {
                  showWarningBox("是否花费" + priceTxt.text + "购买" + equipVO.name,3,{
                     "type":"buyTeHuiLiBao",
                     "okFunction":buy2
                  });
               },null,null,0);
            }
         }
      }
      
      protected function okFunc() : void
      {
         hideWarningBox();
         buy2();
      }
      
      private function buy2() : void
      {
         var price:uint = uint(int(equipVO.ticketPrice));
         var ticketId:String = equipVO.ticketId;
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买特惠礼包";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_getEquipmentVOsLogic.getEquipmentVOs(equipmentData,GamingUI.getInstance().player1);
            TeHuiData.getInstance().addData(m_equipmentVOs[0].id);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            quitBuy();
         },moneyNo,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function quitBuy() : void
      {
         parent.removeChild(this);
      }
      
      private function moneyNo(param1:String, param2:int, param3:Object = null) : void
      {
         if(param1 == "点券不足， 无法购买！")
         {
            quitBuy();
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         m_WarningBox.initBox(param1,param2);
         m_WarningBox.okFun = okFunc;
         m_WarningBox.noFun = hideWarningBox;
         addChild(m_WarningBox);
         m_WarningBox.x = 50;
         m_WarningBox.y = 50;
      }
      
      public function hideWarningBox() : void
      {
         ClearUtil.clearObject(m_WarningBox);
         m_WarningBox = null;
         m_WarningBox = new TehuiView();
      }
      
      protected function clickBtn(param1:UIBtnEvent) : void
      {
         var _loc2_:String = null;
         if(param1.target == _sureBtn)
         {
            switch(_potion)
            {
               case "shopMoneyPrice":
                  _loc2_ = "moneyBuy";
                  break;
               case "shopPkPointPrice":
                  if(ShopData.getInstance().currentBuyNumInPKShop >= XMLSingle.getInstance().maxBuyNumInPKShop - 1)
                  {
                     (parent as ShopOne).mouseChildren = false;
                     (parent as ShopOne).mouseEnabled = false;
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"已经改买了" + XMLSingle.getInstance().maxBuyNumInPKShop + "次物品，不能再购买了！",
                        "flag":0
                     }));
                  }
                  _loc2_ = "pkPointBuy";
                  break;
               case "shopWallPrice":
                  _loc2_ = "poincketTicket";
                  break;
               default:
                  throw new Error();
            }
            dispatchEvent(new UIDataEvent("buyEquipment",{
               "equipment":equipmentCell.child,
               "num":_numBtnGroup.num,
               "buyMode":_loc2_
            }));
         }
         else
         {
            dispatchEvent(new UIDataEvent("cancelBuy"));
         }
      }
      
      protected function mouseDownBox(param1:MouseEvent) : void
      {
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      protected function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      protected function test(param1:Event) : void
      {
         var _loc2_:Point = localToGlobal(new Point(mouseX,mouseY));
         if(_loc2_.x > stage.stageWidth)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            x -= 10;
         }
         if(_loc2_.x < 0)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            x += 10;
         }
         if(_loc2_.y > stage.stageHeight)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            y -= 10;
         }
         if(_loc2_.y < 0)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            y += 10;
         }
      }
   }
}

import YJFY.ShowLogicShell.NumBtnGroupLogicShell.INumBtnGroupListener;
import YJFY.Utils.ClearUtil;

class Listener implements INumBtnGroupListener
{
   public var outMaxNumFun:Function;
   
   public var outMaxNumFunParams:Array;
   
   public function Listener()
   {
      super();
   }
   
   public function clear() : void
   {
      outMaxNumFun = null;
      ClearUtil.nullArr(outMaxNumFunParams,false,false,false);
      outMaxNumFunParams = null;
   }
   
   public function outMaxNumWarning(param1:int, param2:int) : void
   {
      if(outMaxNumFun)
      {
         outMaxNumFun.apply(null,outMaxNumFunParams);
      }
   }
   
   public function outMinNumWarning(param1:int, param2:int) : void
   {
   }
}
