package UI.NicknameSystem.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class FreeChangeNicknameBtn extends Btn
   {
      public static const CLICK_FREE_CHANGE_NICKNAME_BTN:String = "clickFreeChangeNicknameBtn";
      
      public function FreeChangeNicknameBtn()
      {
         super();
         setTipString("点击设置免费昵称");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickFreeChangeNicknameBtn"));
      }
   }
}

