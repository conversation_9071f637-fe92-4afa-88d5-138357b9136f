package UI
{
   import UI.UIInterface.OldInterface.ICell;
   import UI.UIInterface.OldInterface.ICellBackground;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class Cell extends MySprite implements ICell
   {
      private var _isHaveChild:Boolean;
      
      private var _isFocus:Boolean;
      
      private var _id:int;
      
      protected var _layer:Sprite;
      
      public function Cell()
      {
         super();
         initCell();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_layer)
         {
            while(_layer.numChildren > 0)
            {
               _layer.removeChildAt(0);
            }
         }
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _layer = null;
      }
      
      public function get isFocus() : Boolean
      {
         return _isFocus;
      }
      
      public function set isFocus(param1:Boolean) : void
      {
         _isFocus = param1;
      }
      
      public function get isHaveChild() : Boolean
      {
         return _isHaveChild;
      }
      
      public function set isHaveChild(param1:Boolean) : void
      {
         _isHaveChild = param1;
      }
      
      public function set id(param1:int) : void
      {
         _id = param1;
      }
      
      public function get id() : int
      {
         return _id;
      }
      
      public function get cellBackground() : ICellBackground
      {
         return null;
      }
      
      public function changeBorderColor(param1:uint) : void
      {
      }
      
      public function changeBackgroundColor(param1:uint) : void
      {
      }
      
      public function showBorder() : void
      {
      }
      
      public function hideBorder() : void
      {
      }
      
      protected function initCell() : void
      {
         _isHaveChild = false;
         _layer = new Sprite();
         addChild(_layer);
         buttonMode = true;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
      }
   }
}

