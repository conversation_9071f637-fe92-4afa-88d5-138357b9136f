package UI.InitPlayerData
{
   import UI.GamingUI;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SaveData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class PlayerDatas
   {
      private var m_playerData:Object;
      
      private var m_getPlayerDataListeners:Vector.<IGetPlayerDataListener>;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_isRead:Boolean;
      
      private var m_uid:String;
      
      private var m_idx:int;
      
      private var m_initMode:int;
      
      private var m_isInitNickname:Boolean;
      
      public function PlayerDatas()
      {
         super();
         m_isRead = true;
         m_playerData = {};
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.getSaveDataSuccessFun = getSaveDataSuccess;
         m_rankListAPIListener.getSaveDataErrorFun = getSaveDataError;
         m_initMode = 2777;
      }
      
      public function clear() : void
      {
         ClearUtil.nullObject(m_playerData);
         m_playerData = null;
         ClearUtil.nullArr(m_getPlayerDataListeners,false,false,false);
         m_getPlayerDataListeners = null;
         if(Boolean(Part1.getInstance().getApi4399()) && Part1.getInstance().getApi4399().rankListAPI)
         {
            Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         }
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
      }
      
      public function init() : void
      {
      }
      
      public function addGetPlayerDataListener(param1:IGetPlayerDataListener) : void
      {
         if(m_getPlayerDataListeners == null)
         {
            m_getPlayerDataListeners = new Vector.<IGetPlayerDataListener>();
         }
         var _loc2_:int = int(m_getPlayerDataListeners.indexOf(param1));
         if(_loc2_ != -1)
         {
            return;
         }
         m_getPlayerDataListeners.push(param1);
      }
      
      public function removeGetPlayerDataListener(param1:IGetPlayerDataListener) : void
      {
         if(m_getPlayerDataListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_getPlayerDataListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_getPlayerDataListeners.splice(_loc2_,1);
            _loc2_ = int(m_getPlayerDataListeners.indexOf(param1));
         }
      }
      
      public function getPlayerData(param1:String, param2:int, param3:int = 0, param4:Boolean = true) : void
      {
         if(param3)
         {
            m_initMode = param3;
         }
         m_isInitNickname = param4;
         if(m_isRead == false)
         {
            GamingUI.getInstance().showMessageTip("系统工作中，稍后再试");
            return;
         }
         m_isRead = false;
         var _loc5_:InitPlayersData = getOnePlayerData(param1,param2);
         if(_loc5_ == null)
         {
            m_uid = param1;
            m_idx = param2;
            trace("调用排行榜方法getUserdata获取存档, 玩家uid:" + m_uid + "玩家idx:" + param2);
            Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
            Part1.getInstance().getApi4399().rankListAPI.getSaveData(param1.toString(),param2);
         }
         else
         {
            getPlayerDataSuccess(_loc5_);
            m_isRead = true;
         }
      }
      
      private function getSaveDataSuccess(param1:SaveData) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         getSaveXMLSuccess(param1.getSaveData());
      }
      
      private function getSaveDataError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("获取存档失败");
         trace("获取存档失败, errorMessage:",param1);
      }
      
      public function getPlayerData2(param1:String, param2:int, param3:XML, param4:int, param5:Boolean) : void
      {
         if(param4)
         {
            m_initMode = param4;
         }
         m_isInitNickname = param5;
         if(m_isRead == false)
         {
            GamingUI.getInstance().showMessageTip("系统工作中，稍后再试");
            return;
         }
         m_isRead = false;
         getSaveXMLSuccess(param3);
      }
      
      private function getPlayerDataSuccess(param1:InitPlayersData) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<IGetPlayerDataListener> = !!m_getPlayerDataListeners ? m_getPlayerDataListeners.slice(0) : null;
         var _loc2_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_[_loc4_])
            {
               _loc3_[_loc4_].getPlayerData(param1);
               _loc3_[_loc4_].getPlayerData2(param1,this);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc3_,false,false,false);
      }
      
      private function initPlayerData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         if(m_playerData[param1] == null)
         {
            m_playerData[param1] = [];
         }
         var _loc5_:InitPlayersData = new InitPlayersData();
         m_playerData[param1][param2] = _loc5_;
         _loc5_.uid = param1;
         _loc5_.idx = param2;
         param4.addTarget = _loc5_;
         _loc5_.addInitCompleteListener(param4);
         _loc5_.initPlayerData(param3,param2,m_initMode,m_isInitNickname);
      }
      
      private function getOnePlayerData(param1:String, param2:int) : InitPlayersData
      {
         if(m_playerData[param1] == null)
         {
            return null;
         }
         return m_playerData[param1][param2];
      }
      
      public function clearOnePlayerData(param1:InitPlayersData) : void
      {
         if(m_playerData[param1.uid] == null)
         {
            return;
         }
         if(m_playerData[param1.uid][param1.idx] == null)
         {
            return;
         }
         m_playerData[param1.uid][param1.idx] = null;
         ClearUtil.clearObject(param1);
         param1 = null;
      }
      
      private function getSaveXMLSuccess(param1:XML) : void
      {
         if(param1 == null)
         {
            trace("从排行榜获得的存档为空");
         }
         if(m_playerData == null)
         {
            return;
         }
         var _loc2_:InitCompleteListener = new InitCompleteListener();
         _loc2_.initCompleteFun = initComplete;
         initPlayerData(m_uid,m_idx,param1,_loc2_);
      }
      
      private function failFun() : void
      {
         GamingUI.getInstance().showMessageTip("读取存档失败");
         m_isRead = true;
      }
      
      private function initComplete() : void
      {
         var _loc1_:InitPlayersData = getOnePlayerData(m_uid,m_idx);
         _loc1_.player1.playerVO.playerUid = m_uid;
         if(_loc1_.player2)
         {
            _loc1_.player2.playerVO.playerUid = m_uid;
         }
         getPlayerDataSuccess(_loc1_);
         m_isRead = true;
      }
   }
}

