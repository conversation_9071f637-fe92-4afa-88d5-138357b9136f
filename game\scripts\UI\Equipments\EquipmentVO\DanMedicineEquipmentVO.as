package UI.Equipments.EquipmentVO
{
   public class DanMedicineEquipmentVO extends EquipmentVO
   {
      public static const ATTACK:String = "attack";
      
      public static const DEFENCE:String = "defence";
      
      public var danMedicineType:String;
      
      public var maxValue:int;
      
      public var reduceValue:int;
      
      public var enablePutLine:int;
      
      public function DanMedicineEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:DanMedicineEquipmentVO = new DanMedicineEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as DanMedicineEquipmentVO).maxValue = this.maxValue;
         (param1 as DanMedicineEquipmentVO).reduceValue = this.reduceValue;
         (param1 as DanMedicineEquipmentVO).danMedicineType = this.danMedicineType;
         (param1 as DanMedicineEquipmentVO).enablePutLine = this.enablePutLine;
      }
   }
}

