package UI.Equipments
{
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class SceneEquipment extends MySprite
   {
      protected const m_path:String = "UI.Equipments.SceneEquipments.";
      
      protected var m_equipment:Equipment;
      
      private var listenerList:Array = [];
      
      public var show:Sprite;
      
      public function SceneEquipment()
      {
         super();
      }
      
      public function getEquipment() : Equipment
      {
         return m_equipment;
      }
      
      public function setEquipment(param1:Equipment) : void
      {
         m_equipment = param1.clone();
         var _loc2_:String = m_equipment.equipmentVO.className;
         _loc2_ = "UI.Equipments.SceneEquipments." + _loc2_ + "_S";
         show = MyFunction2.returnShowByClassName(_loc2_) as Sprite;
         if(show)
         {
            addChild(show);
         }
      }
      
      override public function clear() : void
      {
         destory();
         super.clear();
         ClearUtil.clearObject(m_equipment);
         m_equipment = null;
         ClearUtil.nullArr(listenerList);
         listenerList = null;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         if(listenerList == null)
         {
            listenerList = [];
         }
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = !!listenerList ? listenerList.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(listenerList[_loc5_] != null)
            {
               if(listenerList[_loc5_].type == param1 && listenerList[_loc5_].listener == param2)
               {
                  listenerList.splice(_loc5_,1);
                  _loc5_--;
                  super.removeEventListener(param1,param2,param3);
               }
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
      }
   }
}

