package UI2.GetEquipmentVOsLogic
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MyFunction2;
   import UI.Players.Player;
   import YJFY.Utils.ClearUtil;
   
   public class GetEquipmentVOsLogic
   {
      public function GetEquipmentVOsLogic()
      {
         super();
      }
      
      public function getIsAbleAddEquipmentVOs(param1:IEquipmentVOsData, param2:Player) : Boolean
      {
         var isSuccess:Boolean;
         var equipmentVOsData:IEquipmentVOsData = param1;
         var player:Player = param2;
         var equipmentVOs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var length:int = int(equipmentVOsData.getEquipmentVONum());
         var i:int = 0;
         while(i < length)
         {
            equipmentVOs.push(equipmentVOsData.getEquipmentVOByIndex(i).clone());
            ++i;
         }
         MyFunction2.falseAddEquipmentVOs(equipmentVOs,player,function():void
         {
            isSuccess = false;
         },function():void
         {
            isSuccess = true;
         },null,null,0);
         ClearUtil.clearObject(equipmentVOs);
         equipmentVOs = null;
         return isSuccess;
      }
      
      public function getEquipmentVOs(param1:IEquipmentVOsData, param2:Player) : Boolean
      {
         var isSuccess:Boolean;
         var equipmentVOsData:IEquipmentVOsData = param1;
         var player:Player = param2;
         var equipmentVOs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var length:int = int(equipmentVOsData.getEquipmentVONum());
         var i:int = 0;
         while(i < length)
         {
            equipmentVOs.push(equipmentVOsData.getEquipmentVOByIndex(i).clone());
            ++i;
         }
         MyFunction2.addEquipmentVOs(equipmentVOs,player,function():void
         {
            isSuccess = false;
         },function():void
         {
            isSuccess = true;
         },null,null,0);
         ClearUtil.clearObject(equipmentVOs);
         equipmentVOs = null;
         return isSuccess;
      }
   }
}

