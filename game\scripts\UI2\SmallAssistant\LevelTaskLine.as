package UI2.SmallAssistant
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI2.SmallAssistant.LevelTask.LevelTaskRewardData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class LevelTaskLine
   {
      private var m_inforPicShow:MovieClipPlayLogicShell;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_getRewardBtnStateShow:MovieClipPlayLogicShell;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_show:MovieClip;
      
      private var m_levelTaskRewardData:LevelTaskRewardData;
      
      public function LevelTaskLine()
      {
         super();
         m_inforPicShow = new MovieClipPlayLogicShell();
         m_eqCells = new Vector.<Sprite>();
         m_getRewardBtnStateShow = new MovieClipPlayLogicShell();
         m_equipments = new Vector.<Equipment>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_inforPicShow);
         m_inforPicShow = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         ClearUtil.clearObject(m_getRewardBtnStateShow);
         m_getRewardBtnStateShow = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_show = null;
         m_levelTaskRewardData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function setLevelTaskRewardData(param1:LevelTaskRewardData) : void
      {
         m_levelTaskRewardData = param1;
         initShow2();
      }
      
      public function tranToAbleGet() : void
      {
         getRewardBtnStateShowInitAbleFrame();
      }
      
      public function tranToUnableGet() : void
      {
         getRewardBtnstateShowInitUnableFrame();
      }
      
      public function getLevelTaskRewardData() : LevelTaskRewardData
      {
         return m_levelTaskRewardData;
      }
      
      public function getGetRewardBtn() : ButtonLogicShell2
      {
         return m_getRewardBtn;
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:DisplayObject = null;
         m_inforPicShow.setShow(m_show["inforPicShow"]);
         _loc2_ = m_show.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ = m_show.getChildAt(_loc4_);
            if(_loc1_.name.substr(0,7) == "eqCell_")
            {
               _loc3_++;
            }
            _loc4_++;
         }
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc4_ + 1)]);
            _loc4_++;
         }
         m_getRewardBtnStateShow.setShow(m_show["getRewardBtnStateShow"]);
         getRewardBtnstateShowInitUnableFrame();
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:Equipment = null;
         if(m_show == null || m_levelTaskRewardData == null)
         {
            return;
         }
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         _loc1_ = int(m_levelTaskRewardData.getEquipmentVONum());
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(m_levelTaskRewardData.getEquipmentVOByIndex(_loc3_));
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            m_equipments.push(_loc2_);
            if(_loc3_ < m_eqCells.length)
            {
               m_eqCells[_loc3_].addChild(_loc2_);
            }
            _loc3_++;
         }
         m_inforPicShow.gotoAndStop("level_" + m_levelTaskRewardData.getNeedMinLevel() + "_task");
      }
      
      private function getRewardBtnStateShowFrameClear() : void
      {
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
      }
      
      private function getRewardBtnstateShowInitUnableFrame() : void
      {
         getRewardBtnStateShowFrameClear();
         m_getRewardBtnStateShow.gotoAndStop("unable");
      }
      
      private function getRewardBtnStateShowInitAbleFrame() : void
      {
         getRewardBtnStateShowFrameClear();
         m_getRewardBtnStateShow.gotoAndStop("able");
         m_getRewardBtn = new ButtonLogicShell2();
         m_getRewardBtn.setShow(m_getRewardBtnStateShow.getShow()["getRewardBtn"]);
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

