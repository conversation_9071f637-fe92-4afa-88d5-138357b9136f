package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyListOfApplyReturn;
   
   public class TEST_DOWN_TheSocietyListOfApplyReturn extends DOWN_TheSocietyListOfApplyReturn
   {
      public function TEST_DOWN_TheSocietyListOfApplyReturn()
      {
         super();
         m_informationBodyId = 3037;
      }
      
      public function initData(param1:int, param2:Vector.<int>) : void
      {
         m_societyNum = param1;
         m_societyIds = m_societyIds;
      }
   }
}

