package UI.EquipmentMakeAndUpgrade
{
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MessageBox.MessageBoxFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipMagicCreatResult extends MySprite
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["OpenEquipMagic"];
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_myMagicPanel:EquipmentMagicCreat;
      
      private var _newContainer:Sprite;
      
      private var _newEquipment:Equipment;
      
      private var _sayTexts:Vector.<TextField> = new Vector.<TextField>();
      
      private var m_magicAddDecStr:Vector.<String> = new Vector.<String>();
      
      public function EquipMagicCreatResult()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_myMagicPanel = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         if(_sayTexts)
         {
            _loc2_ = int(_sayTexts.length);
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _sayTexts[_loc4_] = null;
               _loc4_++;
            }
         }
         if(m_magicAddDecStr)
         {
            _loc3_ = int(m_magicAddDecStr.length);
            _loc1_ = 0;
            while(_loc1_ < _loc3_)
            {
               m_magicAddDecStr[_loc1_] = null;
               _loc1_++;
            }
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_myMagicPanel.closeUseContractSuccessInforPanel();
         }
      }
      
      public function init(param1:EquipmentMagicCreat, param2:Boolean) : void
      {
         var myEquipmentMagicCreat:EquipmentMagicCreat = param1;
         var bSuccess:Boolean = param2;
         m_myMagicPanel = myEquipmentMagicCreat;
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("ShowCreateResultPanel") as MovieClip;
            }
            addChild(_show);
            _showMC = new MovieClipPlayLogicShell();
            _showMC.setShow(_show);
            if(bSuccess)
            {
               _showMC.gotoAndStop("successResult");
            }
            else
            {
               _showMC.gotoAndStop("failResult");
            }
            initShow();
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(_show["quitBtn"]);
         _newContainer = _show["newContainer"];
         _sayTexts.push(_show["attr1"]);
         _sayTexts.push(_show["attr2"]);
         _sayTexts.push(_show["attr3"]);
         _sayTexts.push(_show["attr4"]);
         _sayTexts.push(_show["attr5"]);
         this.x = 280.35;
         this.y = -52;
      }
      
      public function AddShowReultEquipment(param1:Equipment, param2:Boolean) : void
      {
         var _loc11_:int = 0;
         var _loc7_:int = 0;
         var _loc12_:Array = null;
         var _loc3_:EquipmentVO = null;
         var _loc4_:* = undefined;
         var _loc6_:* = undefined;
         var _loc9_:WeaponEquipmentVO = null;
         var _loc10_:NecklaceEquipmentVO = null;
         var _loc13_:GourdEquipmentVO = null;
         var _loc8_:ClothesEquipmentVO = null;
         var _loc5_:PreciousEquipmentVO = null;
         if(param1)
         {
            _newEquipment = param1.clone();
            _newEquipment.addEventListener("rollOver",onOver,false,0,true);
            _newEquipment.addEventListener("rollOut",onOut,false,0,true);
            _newEquipment.equipmentVO = param1.equipmentVO;
            (_newEquipment as DisplayObject).scaleX = (param1 as DisplayObject).scaleY = 1.1;
            _newContainer.addChild(_newEquipment as DisplayObject);
            if(param2 == true)
            {
               _loc3_ = param1.equipmentVO;
               switch(_loc3_.equipmentType)
               {
                  case "weapon":
                     _loc9_ = _loc3_ as WeaponEquipmentVO;
                     _loc4_ = _loc9_.addPlayerSaveAttr;
                     _loc6_ = _loc9_.addPlayerSaveAttrVals;
                     _loc11_ = int(_loc4_.length);
                     _loc7_ = 0;
                     while(_loc7_ < _loc11_ && _loc7_ < 5)
                     {
                        _loc12_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc4_[_loc7_],_loc6_[_loc7_]);
                        m_magicAddDecStr[_loc7_] = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc12_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc12_[1],16);
                        _sayTexts[_loc7_].text = _loc12_[0] + "：" + _loc12_[1];
                        _loc12_[0] = null;
                        _loc12_[1] = null;
                        _loc12_ = null;
                        _loc7_++;
                     }
                     break;
                  case "necklace":
                     _loc10_ = _loc3_ as NecklaceEquipmentVO;
                     _loc4_ = _loc10_.addPlayerSaveAttr;
                     _loc6_ = _loc10_.addPlayerSaveAttrVals;
                     _loc11_ = int(_loc4_.length);
                     _loc7_ = 0;
                     while(_loc7_ < _loc11_ && _loc7_ < 5)
                     {
                        _loc12_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc4_[_loc7_],_loc6_[_loc7_]);
                        m_magicAddDecStr[_loc7_] = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc12_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc12_[1],16);
                        _sayTexts[_loc7_].text = _loc12_[0] + "：" + _loc12_[1];
                        _loc12_[0] = null;
                        _loc12_[1] = null;
                        _loc12_ = null;
                        _loc7_++;
                     }
                     break;
                  case "gourd":
                     _loc13_ = _loc3_ as GourdEquipmentVO;
                     _loc4_ = _loc13_.addPlayerSaveAttr;
                     _loc6_ = _loc13_.addPlayerSaveAttrVals;
                     _loc11_ = int(_loc4_.length);
                     _loc7_ = 0;
                     while(_loc7_ < _loc11_ && _loc7_ < 5)
                     {
                        _loc12_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc4_[_loc7_],_loc6_[_loc7_]);
                        m_magicAddDecStr[_loc7_] = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc12_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc12_[1],16);
                        _sayTexts[_loc7_].text = _loc12_[0] + "：" + _loc12_[1];
                        _loc12_[0] = null;
                        _loc12_[1] = null;
                        _loc12_ = null;
                        _loc7_++;
                     }
                     break;
                  case "clothes":
                     _loc8_ = _loc3_ as ClothesEquipmentVO;
                     _loc4_ = _loc8_.addPlayerSaveAttr;
                     _loc6_ = _loc8_.addPlayerSaveAttrVals;
                     _loc11_ = int(_loc4_.length);
                     _loc7_ = 0;
                     while(_loc7_ < _loc11_ && _loc7_ < 5)
                     {
                        _loc12_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc4_[_loc7_],_loc6_[_loc7_]);
                        m_magicAddDecStr[_loc7_] = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc12_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc12_[1],16);
                        _sayTexts[_loc7_].text = _loc12_[0] + "：" + _loc12_[1];
                        _loc12_[0] = null;
                        _loc12_[1] = null;
                        _loc12_ = null;
                        _loc7_++;
                     }
                     break;
                  case "precious":
                     _loc5_ = _loc3_ as PreciousEquipmentVO;
                     _loc4_ = _loc5_.addPlayerSaveAttr;
                     _loc6_ = _loc5_.addPlayerSaveAttrVals;
                     _loc11_ = int(_loc4_.length);
                     _loc7_ = 0;
                     while(_loc7_ < _loc11_ && _loc7_ < 5)
                     {
                        _loc12_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc4_[_loc7_],_loc6_[_loc7_]);
                        m_magicAddDecStr[_loc7_] = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc12_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc12_[1],16);
                        _sayTexts[_loc7_].text = _loc12_[0] + "：" + _loc12_[1];
                        _loc12_[0] = null;
                        _loc12_[1] = null;
                        _loc12_ = null;
                        _loc7_++;
                     }
               }
            }
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

