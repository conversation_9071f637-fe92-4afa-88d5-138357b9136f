package UI2.APIDataAnalyze
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import YJFY.GameData;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.events.Event;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   
   public class APIDataAnalyze
   {
      private static var m_instance:APIDataAnalyze;
      
      private var m_apiDataAnalyzeTime:String;
      
      private var m_opentime:String;
      
      private var _lastGameTime:Number;
      
      public var remainTime:int;
      
      private var remainTimeDatas:Array = [10,30,60,90,180,999999];
      
      private var payMoneyDatas:Array = [0,10,50,100,300,500,999999];
      
      public var payMoney:int = 0;
      
      private var m_playerLevelDatas:Array = [{
         "id":"1",
         "minLevel":1,
         "maxLevel":40
      },{
         "id":"2",
         "minLevel":41,
         "maxLevel":50
      },{
         "id":"3",
         "minLevel":51,
         "maxLevel":60
      },{
         "id":"4",
         "minLevel":61,
         "maxLevel":70
      },{
         "id":"5",
         "minLevel":71,
         "maxLevel":80
      },{
         "id":"6",
         "minLevel":81,
         "maxLevel":85
      },{
         "id":"7",
         "minLevel":86,
         "maxLevel":90
      }];
      
      private var m_playerTypeDatas:Array = [{
         "id":"1",
         "type":"SunWuKong"
      },{
         "id":"2",
         "type":"BaiLongMa"
      },{
         "id":"3",
         "type":"ErLangShen"
      },{
         "id":"4",
         "type":"ChangE"
      },{
         "id":"5",
         "type":"Fox"
      },{
         "id":"6",
         "type":"TieShan"
      },{
         "id":"7",
         "type":"Houyi"
      },{
         "id":"8",
         "type":"ZiXia"
      }];
      
      private var m_levelMode1LevelDatas:Array = [{
         "id":"1",
         "minLevel":0,
         "maxLevel":13
      },{
         "id":"2",
         "minLevel":14,
         "maxLevel":16
      },{
         "id":"3",
         "minLevel":17,
         "maxLevel":20
      },{
         "id":"4",
         "minLevel":21,
         "maxLevel":30
      }];
      
      private var m_attackDatas:Array = [{
         "id":"1",
         "minAttack":0,
         "maxAttack":300
      },{
         "id":"2",
         "minAttack":301,
         "maxAttack":1000
      },{
         "id":"3",
         "minAttack":1001,
         "maxAttack":3000
      },{
         "id":"4",
         "minAttack":3001,
         "maxAttack":5000
      },{
         "id":"5",
         "minAttack":5001,
         "maxAttack":10000
      },{
         "id":"6",
         "minAttack":10001,
         "maxAttack":15000
      },{
         "id":"7",
         "minAttack":15001,
         "maxAttack":20000
      },{
         "id":"8",
         "minAttack":20001,
         "maxAttack":30000
      }];
      
      private var m_mainLineTaskPhaseDatas:Array = [{
         "id":"1",
         "phase":"phase_1"
      },{
         "id":"2",
         "phase":"phase_2"
      },{
         "id":"3",
         "phase":"phase_3"
      },{
         "id":"4",
         "phase":"phase_4"
      },{
         "id":"5",
         "phase":"phase_5"
      }];
      
      private var m_playerNumDatas:Array = [{"id":"1"},{"id":"2"}];
      
      public function APIDataAnalyze()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            return;
         }
         throw new Error("实例已经存在");
      }
      
      public static function getInstance() : APIDataAnalyze
      {
         if(m_instance == null)
         {
            m_instance = new APIDataAnalyze();
         }
         return m_instance;
      }
      
      public function clear() : void
      {
         m_apiDataAnalyzeTime = null;
         ClearUtil.clearObject(m_playerLevelDatas);
         m_playerLevelDatas = null;
         ClearUtil.clearObject(m_playerTypeDatas);
         m_playerTypeDatas = null;
         ClearUtil.clearObject(m_levelMode1LevelDatas);
         m_levelMode1LevelDatas = null;
         ClearUtil.clearObject(m_attackDatas);
         m_attackDatas = null;
         ClearUtil.clearObject(m_mainLineTaskPhaseDatas);
         m_mainLineTaskPhaseDatas = null;
         ClearUtil.clearObject(m_playerNumDatas);
         m_playerNumDatas = null;
         m_instance = null;
      }
      
      public function initBySaveXML(param1:XML) : void
      {
         if(param1.hasOwnProperty("Ada"))
         {
            m_apiDataAnalyzeTime = MyFunction2.resetErrorTime(String(param1.Ada[0].@t));
            m_opentime = GamingUI.getInstance().getNewestTimeStrFromSever();
            remainTime = int(param1.Ada[0].@rt);
            payMoney = int(param1.Ada[0].@pm);
            trace("读档,在线时间:",remainTime);
            trace("读档,付费:",payMoney);
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <Ada />
			;
         if(m_apiDataAnalyzeTime)
         {
            if(new TimeUtil().newDateIsNewDay(m_apiDataAnalyzeTime,GamingUI.getInstance().getNewestTimeStrFromSever()))
            {
               _loc1_.@t = m_apiDataAnalyzeTime;
               _loc1_.@rt = 0;
               _loc1_.@pm = 0;
            }
            else
            {
               if(m_apiDataAnalyzeTime)
               {
                  _loc1_.@t = m_apiDataAnalyzeTime;
               }
               if(remainTime)
               {
                  _loc1_.@rt = remainTime;
               }
               if(payMoney)
               {
                  _loc1_.@pm = payMoney;
               }
            }
         }
         trace("存档,在线时间:",remainTime);
         trace("存档,付费:",payMoney);
         return _loc1_;
      }
      
      public function sendData() : void
      {
         var _loc11_:String = null;
         var _loc13_:String = null;
         var _loc6_:String = null;
         var _loc1_:String = null;
         var _loc4_:String = null;
         var _loc5_:String = null;
         var _loc8_:String = null;
         var _loc2_:String = null;
         var _loc7_:String = null;
         var _loc9_:int = 0;
         var _loc12_:int = 0;
         var _loc3_:int = 0;
         var _loc10_:String = null;
         if(new TimeUtil().newDateIsNewDay(m_apiDataAnalyzeTime,GamingUI.getInstance().getNewestTimeStrFromSever()))
         {
            m_apiDataAnalyzeTime = GamingUI.getInstance().getNewestTimeStrFromSever();
            _loc11_ = "1";
            _loc13_ = "1";
            _loc6_ = "1";
            _loc1_ = "1";
            _loc4_ = "1";
            _loc5_ = "1";
            _loc8_ = "1";
            _loc2_ = "1";
            _loc7_ = "1";
            _loc12_ = int(m_playerLevelDatas.length);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               if(GamingUI.getInstance().player1.playerVO.level >= m_playerLevelDatas[_loc9_].minLevel && GamingUI.getInstance().player1.playerVO.level <= m_playerLevelDatas[_loc9_].maxLevel)
               {
                  _loc11_ = m_playerLevelDatas[_loc9_].id;
                  break;
               }
               _loc9_++;
            }
            _loc12_ = int(m_playerTypeDatas.length);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               if(GamingUI.getInstance().player1.playerVO.playerType == m_playerTypeDatas[_loc9_].type)
               {
                  _loc13_ = m_playerTypeDatas[_loc9_].id;
                  break;
               }
               _loc9_++;
            }
            _loc12_ = int(m_levelMode1LevelDatas.length);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               if(LevelModeSaveData.getInstance().getMapLevel() >= m_levelMode1LevelDatas[_loc9_].minLevel && LevelModeSaveData.getInstance().getMapLevel() <= m_levelMode1LevelDatas[_loc9_].maxLevel)
               {
                  _loc6_ = m_levelMode1LevelDatas[_loc9_].id;
                  break;
               }
               _loc9_++;
            }
            _loc12_ = int(m_attackDatas.length);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               if(GamingUI.getInstance().player1.playerVO.attack >= m_attackDatas[_loc9_].minAttack && GamingUI.getInstance().player1.playerVO.attack <= m_attackDatas[_loc9_].maxAttack)
               {
                  _loc1_ = m_attackDatas[_loc9_].id;
                  break;
               }
               _loc9_++;
            }
            _loc12_ = int(m_mainLineTaskPhaseDatas.length);
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               if(NewMainTaskData.getInstance().currentPhase == m_mainLineTaskPhaseDatas[_loc9_].phase)
               {
                  _loc4_ = m_mainLineTaskPhaseDatas[_loc9_].id;
                  break;
               }
               _loc9_++;
            }
            if(Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO))
            {
               _loc5_ = m_playerNumDatas[1].id;
            }
            _loc8_ = String(GamingUI.getInstance().player1.vipVO.vipLevel + 1);
            _loc9_ = 1;
            while(_loc9_ <= 5)
            {
               if(GamingUI.getInstance().getSvActivitySaveData().isGotThisGiftBagOfJiLeiHaoLi("g" + _loc9_))
               {
                  _loc2_ = String(_loc9_ + 1);
               }
               _loc9_++;
            }
            _loc9_ = 1;
            while(_loc9_ < payMoneyDatas.length)
            {
               if(payMoney <= payMoneyDatas[_loc9_])
               {
                  _loc7_ = String(_loc9_);
                  break;
               }
               _loc9_++;
            }
            _loc9_ = 0;
            while(_loc9_ < remainTimeDatas.length)
            {
               if(Math.abs(remainTime) <= remainTimeDatas[_loc9_] * 1000 * 60)
               {
                  break;
               }
               _loc9_++;
            }
            _loc3_ = _loc9_ + 1;
            _loc10_ = "a=" + _loc11_ + "&b=" + _loc8_ + "&c=" + _loc3_ + "&d=" + _loc7_ + "&e=" + _loc13_ + "&f=" + _loc1_ + "&g=" + _loc5_ + "&h=" + _loc4_;
            trace("信息:",_loc10_);
            sendRequest(_loc10_);
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(isNaN(_lastGameTime))
         {
            _lastGameTime = param1.getOnLineTimeForThisInit();
         }
         var _loc2_:Number = param1.getOnLineTimeForThisInit() - _lastGameTime;
         remainTime += _loc2_;
         _lastGameTime = param1.getOnLineTimeForThisInit();
      }
      
      private function sendRequest(param1:String) : void
      {
         var _loc4_:String = "http://stat.api.4399.com/archive_statistics/log.js?game_id=100021366&uid=" + GameData.getInstance().getLoginReturnData().getUid() + "&index=" + GameData.getInstance().getSaveFileData().index + "&" + param1;
         var _loc3_:URLRequest = new URLRequest(_loc4_);
         _loc3_.method = "POST";
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.dataFormat = "text";
         _loc2_.addEventListener("complete",DoUIEvent);
         _loc2_.addEventListener("ioError",DoUIEvent);
         _loc2_.addEventListener("securityError",DoUIEvent);
         _loc2_.load(_loc3_);
      }
      
      private function DoUIEvent(param1:Event) : void
      {
         trace("apiDataAnalyze e:",param1);
      }
   }
}

