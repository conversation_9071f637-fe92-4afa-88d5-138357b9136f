package UI.EquipmentMakeAndUpgrade
{
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.EnterFrameTime;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.GhostTreasureShow;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Other.QuickenChargeBox;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipmentMagicCreat extends MySprite
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      public var sayText:TextField;
      
      public var sayText2:TextField;
      
      public var sayText3:TextField;
      
      public var requiredMoneyText:TextField;
      
      public var successRateText:TextField;
      
      private const GHOST_TREASURE_X:Number = 65;
      
      private const GHOST_TREASURE_Y:Number = 166;
      
      private var _ghostTreasure:GhostTreasureShow;
      
      private var _player:Player;
      
      private var _numBtnGroup:NumberBtnGroupLogicShell;
      
      private var _startOpenHoleBtn:ButtonLogicShell2;
      
      private var _getMagicTextBtn:ButtonLogicShell2;
      
      private var _equipMagicXML:XML;
      
      private var _wantLoadSources:Array = ["OpenEquipMagic"];
      
      private var _oldContainer:Sprite;
      
      private var _materialContainer:Sprite;
      
      private var _luckStoneContainer:Sprite;
      
      private var _successRateText:TextField;
      
      private var _moneyText:TextField;
      
      public var hitTestArea:Sprite;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _oldEquipment:Equipment;
      
      private var _newEquipment:Equipment;
      
      private var _baseSuccessRate:Number;
      
      private var _successRate:Number;
      
      private var _maxSuccessRate:Number;
      
      private var _needMoney:int;
      
      private var _needMaterial:Equipment;
      
      private var _needMaterialNum:int;
      
      private var _needMagicMaterial:Equipment;
      
      private var _needMagicMaterialNum:int;
      
      private var _needMagicNumText:TextField;
      
      private var _magicMeterialContainer:Sprite;
      
      private var _haveMagicNumText:TextField;
      
      private var _buyMagicMaterialBtn:BuyMaterialGuideBtn;
      
      private var _needAMaterial:Equipment;
      
      private var _needAMaterialNum:int;
      
      private var _needANumText:TextField;
      
      private var _aMeterialContainer:Sprite;
      
      private var _haveANumText:TextField;
      
      private var _buyAMaterialBtn:BuyMaterialGuideBtn;
      
      private var _luckStoneShow:LuckStoneShow;
      
      private var _needNumText:TextField;
      
      private var _buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var _buyLuckStoneGuideBtn:BuyMaterialGuideBtn;
      
      private var _haveNumText:TextField;
      
      private var _buyGoldPocketBtn:BuyGoldPocketGuideBtn;
      
      private var _currentOpenHoleXML:XML;
      
      private var m_timeShow:Sprite;
      
      private var m_lockShow:Sprite;
      
      private var m_hourShow:MultiPlaceNumLogicShell;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_remainTimeForCompleteEnergy:uint;
      
      private var m_speedUpEnBtn:ButtonLogicShell;
      
      private var m_quickenChargeBox:QuickenChargeBox;
      
      private var m_curentPlayerVO:PlayerVO;
      
      private var m_resultShowPanel:EquipMagicCreatResult;
      
      private var m_textShowPanel:ShowEqMagicText;
      
      public function EquipmentMagicCreat()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function calulateRemainTimeForCompleteEnegy() : void
      {
         m_remainTimeForCompleteEnergy = Math.max(0,m_curentPlayerVO.eqMagicVO.getRemainTimeForCompleteEnergy() - (GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - m_curentPlayerVO.eqMagicVO.getOnLineTimeForCalRemainTime()));
      }
      
      public function swapPlayerVO(param1:Player) : void
      {
         m_curentPlayerVO = param1.playerVO;
         calulateRemainTimeForCompleteEnegy();
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      public function getIsAblePutInEq() : Boolean
      {
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            return true;
         }
         return false;
      }
      
      public function refreshtimeshow() : void
      {
         if(m_timeShow)
         {
            if(m_timeShow.parent)
            {
               m_timeShow.parent.removeChild(m_timeShow);
            }
            ClearUtil.clearObject(m_timeShow);
            m_timeShow = null;
         }
         if(m_minuteShow)
         {
            ClearUtil.clearObject(m_minuteShow);
         }
         m_minuteShow = null;
         if(m_secondShow)
         {
            ClearUtil.clearObject(m_secondShow);
         }
         m_secondShow = null;
         if(m_hourShow)
         {
            ClearUtil.clearObject(m_hourShow);
         }
         m_hourShow = null;
         if(m_speedUpEnBtn)
         {
            if(m_speedUpEnBtn.getShow().parent)
            {
               m_speedUpEnBtn.getShow().parent.removeChild(m_speedUpEnBtn.getShow());
            }
            ClearUtil.clearObject(m_speedUpEnBtn);
            m_speedUpEnBtn = null;
         }
         if(m_lockShow)
         {
            if(m_lockShow.parent)
            {
               m_lockShow.parent.removeChild(m_lockShow);
            }
            ClearUtil.clearObject(m_lockShow);
            m_lockShow = null;
         }
         m_timeShow = _show["timeShow"];
         m_hourShow = new MultiPlaceNumLogicShell();
         m_hourShow.setShow(m_timeShow["hour"]);
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_timeShow["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_timeShow["second"]);
         m_speedUpEnBtn = new ButtonLogicShell();
         m_speedUpEnBtn.setShow(_show["superEngergyBtn"]);
         m_lockShow = _show["lock"];
         m_hourShow.setIsShowZero(true);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
         calulateRemainTimeForCompleteEnegy();
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      public function init(param1:MyControlPanel) : void
      {
         var controlPanel:MyControlPanel = param1;
         m_curentPlayerVO = controlPanel.currentPlayer.playerVO;
         _luckStoneShow = new LuckStoneShow();
         _ghostTreasure = new GhostTreasureShow();
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            MyFunction2.loadXMLFunction("equipMagic",function(param1:XML):void
            {
               _equipMagicXML = param1;
               if(_show == null)
               {
                  _show = MyFunction2.returnShowByClassName("EquipMagicPanel") as MovieClip;
               }
               this.x = 40.35;
               this.y = 118;
               addChild(_show);
               _showMC = new MovieClipPlayLogicShell();
               _showMC.setShow(_show);
               _getMagicTextBtn = new ButtonLogicShell2();
               _getMagicTextBtn.setShow(_show["showTextBtn"]);
               _getMagicTextBtn.setTipString("点击查看附魔说明");
               initNotPutInFrame();
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function initNotPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("notPutIn");
         hitTestArea = _show["hitTestArea"];
         if(hitTestArea)
         {
            hitTestArea.x = 40;
            hitTestArea.y = 60;
         }
         this.x = 40.35;
         this.y = 118;
         refreshtimeshow();
      }
      
      private function clearNotPutInFrame() : void
      {
      }
      
      private function clearPutInFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _magicMeterialContainer = null;
         _aMeterialContainer = null;
         _luckStoneContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
      }
      
      private function clearSuccessFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _magicMeterialContainer = null;
         _aMeterialContainer = null;
         _luckStoneContainer = null;
      }
      
      private function clearFailFrame() : void
      {
         ClearUtil.clearDisplayObjectInContainer(_oldContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_materialContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_magicMeterialContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_aMeterialContainer,false,false);
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _magicMeterialContainer = null;
         _aMeterialContainer = null;
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         ClearUtil.clearDisplayObjectInContainer(_oldContainer);
         _oldContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_materialContainer);
         _materialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_aMeterialContainer);
         _aMeterialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer);
         _luckStoneContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
         if(_getMagicTextBtn)
         {
            _getMagicTextBtn.clear();
         }
         _getMagicTextBtn = null;
         _ghostTreasure = null;
         sayText = null;
         sayText2 = null;
         sayText3 = null;
         requiredMoneyText = null;
         successRateText = null;
         _player = null;
         _equipMagicXML = null;
         hitTestArea = null;
         _equipmentVO = null;
         if(_oldEquipment)
         {
            _oldEquipment.clear();
         }
         _oldEquipment = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         if(_needMaterial)
         {
            _needMaterial.clear();
         }
         _needMaterial = null;
         if(_needMagicMaterial)
         {
            _needMagicMaterial.clear();
         }
         _needMagicMaterial = null;
         ClearUtil.clearDisplayObjectInContainer(_magicMeterialContainer);
         _magicMeterialContainer = null;
         _luckStoneShow = null;
         _needNumText = null;
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyMagicMaterialBtn)
         {
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
         _haveNumText = null;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
         _currentOpenHoleXML = null;
         ClearUtil.clearObject(m_timeShow);
         m_timeShow = null;
         ClearUtil.clearObject(m_lockShow);
         m_lockShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_speedUpEnBtn);
         m_speedUpEnBtn = null;
         m_quickenChargeBox = null;
         m_curentPlayerVO = null;
         ClearUtil.clearObject(m_resultShowPanel);
         m_resultShowPanel = null;
         ClearUtil.clearObject(m_textShowPanel);
         m_textShowPanel = null;
      }
      
      private function openTextShowPanel() : void
      {
         if(m_textShowPanel == null)
         {
            m_textShowPanel = new ShowEqMagicText();
            m_textShowPanel.init(this);
         }
         addChild(m_textShowPanel);
      }
      
      public function closeTextShowPanel() : void
      {
         ClearUtil.clearObject(m_textShowPanel);
         m_textShowPanel = null;
      }
      
      private function openUseContractSuccessInforPanel() : void
      {
         if(m_resultShowPanel == null)
         {
            m_resultShowPanel = new EquipMagicCreatResult();
            m_resultShowPanel.init(this,true);
         }
         if(_newEquipment)
         {
            m_resultShowPanel.AddShowReultEquipment(_newEquipment,true);
         }
         addChild(m_resultShowPanel);
      }
      
      private function openFailInforPanel() : void
      {
         if(m_resultShowPanel == null)
         {
            m_resultShowPanel = new EquipMagicCreatResult();
            m_resultShowPanel.init(this,false);
         }
         if(_oldEquipment)
         {
            m_resultShowPanel.AddShowReultEquipment(_oldEquipment,false);
         }
         addChild(m_resultShowPanel);
      }
      
      public function closeUseContractSuccessInforPanel() : void
      {
         ClearUtil.clearObject(m_resultShowPanel);
         m_resultShowPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _numBtnGroup:
               changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
               break;
            case _startOpenHoleBtn:
               start();
               break;
            case _getMagicTextBtn:
               openTextShowPanel();
               break;
            case m_speedUpEnBtn:
               m_quickenChargeBox = GamingUI.getInstance().showQuickenChargeBox(Math.ceil(m_remainTimeForCompleteEnergy / 3600000),speedUp);
         }
      }
      
      private function endPlaySpeedUpEnergyEffectShow() : void
      {
         m_lockShow.visible = true;
         m_timeShow.visible = true;
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            initNotHatchState();
         }
      }
      
      private function speedUp(param1:uint) : void
      {
         var speedUpHour:uint = param1;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_curentPlayerVO.eqMagicVO.buyHatchTime(speedUpHour * 3600000,param1);
            calulateRemainTimeForCompleteEnegy();
            endPlaySpeedUpEnergyEffectShow();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },function():void
         {
            m_curentPlayerVO.eqMagicVO.buyHatchTime(speedUpHour * 3600000,GamingUI.getInstance().getNewestTimeStrFromSever());
            calulateRemainTimeForCompleteEnegy();
            endPlaySpeedUpEnergyEffectShow();
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },true);
         m_quickenChargeBox = null;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         if(m_curentPlayerVO == null)
         {
            return;
         }
         if(m_remainTimeForCompleteEnergy <= 0)
         {
            return;
         }
         var _loc4_:int;
         m_remainTimeForCompleteEnergy = _loc4_ = Math.max(0,m_remainTimeForCompleteEnergy - param1.getAddTimeOneFrame());
         if(_loc4_ <= 0)
         {
            initAbleHatchState();
         }
         else
         {
            _loc3_ = Math.ceil(_loc4_ / 1000);
            _loc5_ = _loc3_ / 3600;
            _loc3_ -= _loc5_ * 3600;
            _loc2_ = _loc3_ / 60;
            _loc3_ -= _loc2_ * 60;
            m_hourShow.showNum(_loc5_);
            m_minuteShow.showNum(_loc2_);
            m_secondShow.showNum(_loc3_);
         }
      }
      
      private function initAbleHatchState() : void
      {
         if(m_lockShow.parent)
         {
            m_lockShow.parent.removeChild(m_lockShow);
         }
         if(m_timeShow.parent)
         {
            m_timeShow.parent.removeChild(m_timeShow);
         }
         if(m_speedUpEnBtn.getShow().parent)
         {
            m_speedUpEnBtn.getShow().parent.removeChild(m_speedUpEnBtn.getShow());
         }
      }
      
      private function initNotHatchState() : void
      {
         _show.addChild(m_lockShow);
         _show.addChild(m_timeShow);
         _show.addChild(m_speedUpEnBtn.getShow());
      }
      
      private function ClearAttrFail(param1:EquipmentVO) : void
      {
         var _loc5_:int = 0;
         var _loc3_:WeaponEquipmentVO = null;
         var _loc4_:NecklaceEquipmentVO = null;
         var _loc6_:GourdEquipmentVO = null;
         var _loc2_:ClothesEquipmentVO = null;
         switch(param1.equipmentType)
         {
            case "weapon":
               _loc3_ = param1 as WeaponEquipmentVO;
               _loc5_ = int(_loc3_.addPlayerSaveAttr.length);
               _loc3_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc3_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "necklace":
               _loc4_ = param1 as NecklaceEquipmentVO;
               _loc5_ = int(_loc4_.addPlayerSaveAttr.length);
               _loc4_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc4_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "gourd":
               _loc6_ = param1 as GourdEquipmentVO;
               _loc5_ = int(_loc6_.addPlayerSaveAttr.length);
               _loc6_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc6_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "clothes":
               _loc2_ = param1 as ClothesEquipmentVO;
               _loc5_ = int(_loc2_.addPlayerSaveAttr.length);
               _loc2_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc2_.addPlayerSaveAttrVals.splice(0,_loc5_);
         }
      }
      
      private function start() : void
      {
         if(_oldEquipment == null)
         {
            return;
         }
         if(_needMaterialNum > getHaveMaterialNum(_needMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return;
         }
         if(_needMagicMaterialNum > getHaveMaterialNum(_needMagicMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return;
         }
         if(_needAMaterialNum > getHaveMaterialNum(_needAMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return;
         }
         if(_needMoney > _player.playerVO.money)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足！",
               "flag":0
            }));
            return;
         }
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var saveinfo:SaveTaskInfo;
            var r:Number = Math.random();
            _player.playerVO.money -= _needMoney;
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMaterialNum,_needMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMagicMaterialNum,_needMagicMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needAMaterialNum,_needAMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_numBtnGroup.num,10500000);
            clearBuyBtn();
            clearBuyLuckStoneBtn();
            clearBuyGoldPocketBtn();
            if(r <= _successRate)
            {
               _oldContainer.addChild(_newEquipment as DisplayObject);
               m_curentPlayerVO.eqMagicVO.setDataForStartHatch(GamingUI.getInstance().getNewestTimeStrFromSever());
               calulateRemainTimeForCompleteEnegy();
               parent.parent.mouseChildren = false;
               parent.parent.mouseEnabled = false;
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_newEquipment.equipmentVO,_oldEquipment.equipmentVO);
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               initSuccessFrame();
               equipmentsAnimation(function():void
               {
                  openUseContractSuccessInforPanel();
                  clearNewEquipment();
                  clearOldEquipment();
                  clearNeedMaterial();
                  initNotPutInFrame();
                  dispatchEvent(new UIPassiveEvent("refreshAtt",34));
                  parent.parent.mouseChildren = true;
                  parent.parent.mouseEnabled = true;
               });
            }
            else
            {
               ClearAttrFail(_oldEquipment.equipmentVO);
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_oldEquipment.equipmentVO,_oldEquipment.equipmentVO);
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               initFailFrame();
               equipmentsAnimation(function():void
               {
                  openFailInforPanel();
                  initNotPutInFrame();
                  clearOldEquipment();
                  clearNewEquipment();
                  clearNeedMaterial();
                  clearBuyBtn();
                  dispatchEvent(new UIPassiveEvent("refreshAtt",34));
               });
            }
         },function():void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"网络连接失败！",
               "flag":0
            }));
         },true);
      }
      
      private function initFailFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("fail");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _magicMeterialContainer = _show["magicMaterialContainer"];
         _aMeterialContainer = _show["anMaterialContainer"];
      }
      
      private function equipmentsAnimation(param1:Function) : void
      {
         if(_oldEquipment)
         {
            TweenLite.to(_oldEquipment,2,{
               "alpha":0,
               "x":0,
               "y":0,
               "ease":Back.easeIn,
               "onComplete":param1
            });
         }
         if(_needMaterial)
         {
            TweenLite.to(_needMaterial,2,{
               "alpha":0,
               "x":-81,
               "y":86.2,
               "ease":Back.easeIn
            });
         }
         if(_needMagicMaterial)
         {
            TweenLite.to(_needMagicMaterial,2,{
               "alpha":0,
               "x":81,
               "y":86.2,
               "ease":Back.easeIn
            });
         }
         if(_needAMaterial)
         {
            TweenLite.to(_needAMaterial,2,{
               "alpha":0,
               "x":1,
               "y":-86.2,
               "ease":Back.easeIn
            });
         }
      }
      
      private function initSuccessFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("success");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _magicMeterialContainer = _show["magicMaterialContainer"];
         _aMeterialContainer = _show["anMaterialContainer"];
      }
      
      private function switchToNothing() : void
      {
      }
      
      public function get player() : Player
      {
         return _player;
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      public function refreshPanel() : void
      {
      }
      
      public function isCanPutEquipmentVO(param1:EquipmentVO) : IsCanPutInfo
      {
         var _loc2_:IsCanPutInfo = new IsCanPutInfo();
         if(getIsAblePutInEq() == false)
         {
            _loc2_.isCanPut = false;
            _loc2_.message = "冷却时间未到！";
            return _loc2_;
         }
         if(param1.equipmentType == "clothes" || param1.equipmentType == "necklace" || param1.equipmentType == "weapon" || param1.equipmentType == "gourd" || param1.equipmentType == "precious")
         {
            _loc2_.isCanPut = true;
         }
         else
         {
            _loc2_.isCanPut = false;
            _loc2_.message = "只有武器、护甲、项链、葫芦才能用来附魔！";
         }
         return _loc2_;
      }
      
      public function putInEquipmentVO(param1:EquipmentVO) : Vector.<EquipmentVO>
      {
         var _loc2_:Vector.<EquipmentVO> = putOutEquipmentVO(param1);
         _equipmentVO = param1;
         putInEquipmentVO1(param1);
         return _loc2_;
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         _equipmentVO = null;
         initNotPutInFrame();
         var _loc3_:EquipmentVO = !!_oldEquipment ? _oldEquipment.equipmentVO : null;
         clearOldEquipment();
         clearNewEquipment();
         clearBuyBtn();
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      private function addMagicAttribute(param1:EquipmentVO) : void
      {
         var _loc12_:XMLList = null;
         var _loc19_:* = undefined;
         var _loc24_:* = undefined;
         var _loc25_:int = 0;
         var _loc11_:* = undefined;
         var _loc18_:Number = NaN;
         var _loc31_:Number = NaN;
         var _loc29_:WeaponEquipmentVO = null;
         var _loc9_:* = undefined;
         var _loc8_:* = undefined;
         var _loc16_:int = 0;
         var _loc23_:PreciousEquipmentVO = null;
         var _loc6_:* = undefined;
         var _loc27_:* = undefined;
         var _loc20_:int = 0;
         var _loc13_:NecklaceEquipmentVO = null;
         var _loc32_:* = undefined;
         var _loc4_:* = undefined;
         var _loc22_:int = 0;
         var _loc17_:GourdEquipmentVO = null;
         var _loc14_:* = undefined;
         var _loc5_:* = undefined;
         var _loc21_:int = 0;
         var _loc10_:ClothesEquipmentVO = null;
         var _loc26_:* = undefined;
         var _loc2_:* = undefined;
         var _loc30_:int = 0;
         if(_equipMagicXML == null)
         {
            return;
         }
         if(param1.equipmentType != "clothes" && param1.equipmentType != "necklace" && param1.equipmentType != "weapon" && param1.equipmentType != "gourd" && param1.equipmentType != "precious")
         {
            return;
         }
         switch(param1.equipmentType)
         {
            case "precious":
               _loc12_ = _equipMagicXML.preciousAddAttr;
               break;
            case "weapon":
               _loc12_ = _equipMagicXML.weaponAddAttr;
               break;
            case "necklace":
               _loc12_ = _equipMagicXML.necklaceAddAttr;
               break;
            case "gourd":
               _loc12_ = _equipMagicXML.gourdAddAttr;
               break;
            case "clothes":
               _loc12_ = _equipMagicXML.clothesAddAttr;
         }
         var _loc28_:XMLList = _loc12_.value;
         var _loc15_:int = int(_loc28_.length());
         _loc19_ = new Vector.<String>();
         _loc24_ = new Vector.<Number>();
         _loc25_ = 0;
         while(_loc25_ < _loc15_)
         {
            _loc19_.push(String(_loc28_[_loc25_].@addPlayerAttribute));
            _loc11_ = MyFunction.getInstance().excreteStringToNumber(String(_loc28_[_loc25_].@addPlayerAttributeValue));
            _loc18_ = Math.abs(_loc11_[0] - _loc11_[1]);
            _loc31_ = Math.floor((Math.random() * _loc18_ + _loc11_[1]) * 100);
            _loc24_.push(_loc31_ / 100);
            _loc25_++;
         }
         var _loc3_:int = Math.random() * 100 + 1;
         var _loc7_:int = 1;
         if(_loc3_ > 0 && _loc3_ <= 20)
         {
            _loc7_ = 1;
         }
         else if(_loc3_ > 20 && _loc3_ <= 50)
         {
            _loc7_ = 2;
         }
         else if(_loc3_ > 50 && _loc3_ <= 85)
         {
            _loc7_ = 3;
         }
         else if(_loc3_ > 85 && _loc3_ <= 95)
         {
            _loc7_ = 4;
         }
         else if(_loc3_ > 95 && _loc3_ <= 100)
         {
            _loc7_ = 5;
         }
         switch(param1.equipmentType)
         {
            case "weapon":
               _loc29_ = param1 as WeaponEquipmentVO;
               _loc9_ = new Vector.<String>();
               _loc8_ = new Vector.<Number>();
               _loc25_ = 0;
               while(_loc25_ < _loc7_)
               {
                  _loc15_ = int(_loc19_.length);
                  _loc16_ = Math.floor(Math.random() * _loc15_);
                  _loc9_.push(_loc19_[_loc16_]);
                  _loc8_.push(_loc24_[_loc16_]);
                  _loc19_.splice(_loc16_,1);
                  _loc24_.splice(_loc16_,1);
                  _loc25_++;
               }
               _loc29_.addPlayerSaveAttr = _loc9_;
               _loc29_.addPlayerSaveAttrVals = _loc8_;
               break;
            case "precious":
               _loc23_ = param1 as PreciousEquipmentVO;
               _loc6_ = new Vector.<String>();
               _loc27_ = new Vector.<Number>();
               _loc25_ = 0;
               while(_loc25_ < _loc7_)
               {
                  _loc15_ = int(_loc19_.length);
                  _loc20_ = Math.floor(Math.random() * _loc15_);
                  _loc6_.push(_loc19_[_loc20_]);
                  _loc27_.push(_loc24_[_loc20_]);
                  _loc19_.splice(_loc20_,1);
                  _loc24_.splice(_loc20_,1);
                  _loc25_++;
               }
               _loc23_.addPlayerSaveAttr = _loc6_;
               _loc23_.addPlayerSaveAttrVals = _loc27_;
               break;
            case "necklace":
               _loc13_ = param1 as NecklaceEquipmentVO;
               _loc32_ = new Vector.<String>();
               _loc4_ = new Vector.<Number>();
               _loc25_ = 0;
               while(_loc25_ < _loc7_)
               {
                  _loc15_ = int(_loc19_.length);
                  _loc22_ = Math.floor(Math.random() * _loc15_);
                  _loc32_.push(_loc19_[_loc22_]);
                  _loc4_.push(_loc24_[_loc22_]);
                  _loc19_.splice(_loc22_,1);
                  _loc24_.splice(_loc22_,1);
                  _loc25_++;
               }
               _loc13_.addPlayerSaveAttr = _loc32_;
               _loc13_.addPlayerSaveAttrVals = _loc4_;
               break;
            case "gourd":
               _loc17_ = param1 as GourdEquipmentVO;
               _loc14_ = new Vector.<String>();
               _loc5_ = new Vector.<Number>();
               _loc25_ = 0;
               while(_loc25_ < _loc7_)
               {
                  _loc15_ = int(_loc19_.length);
                  _loc21_ = Math.floor(Math.random() * _loc15_);
                  _loc14_.push(_loc19_[_loc21_]);
                  _loc5_.push(_loc24_[_loc21_]);
                  _loc19_.splice(_loc21_,1);
                  _loc24_.splice(_loc21_,1);
                  _loc25_++;
               }
               _loc17_.addPlayerSaveAttr = _loc14_;
               _loc17_.addPlayerSaveAttrVals = _loc5_;
               break;
            case "clothes":
               _loc10_ = param1 as ClothesEquipmentVO;
               _loc26_ = new Vector.<String>();
               _loc2_ = new Vector.<Number>();
               _loc25_ = 0;
               while(_loc25_ < _loc7_)
               {
                  _loc15_ = int(_loc19_.length);
                  _loc30_ = Math.floor(Math.random() * _loc15_);
                  _loc26_.push(_loc19_[_loc30_]);
                  _loc2_.push(_loc24_[_loc30_]);
                  _loc19_.splice(_loc30_,1);
                  _loc24_.splice(_loc30_,1);
                  _loc25_++;
               }
               _loc10_.addPlayerSaveAttr = _loc26_;
               _loc10_.addPlayerSaveAttrVals = _loc2_;
         }
         GamingUI.getInstance().refresh(515);
      }
      
      public function putInEquipmentVO1(param1:EquipmentVO) : void
      {
         if(param1.equipmentType != "clothes" && param1.equipmentType != "necklace" && param1.equipmentType != "weapon" && param1.equipmentType != "gourd" && param1.equipmentType != "precious")
         {
            return;
         }
         clearOldEquipment();
         _oldEquipment = MyFunction2.sheatheEquipmentShell(param1);
         _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
         _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
         var _loc3_:XML = getCurrentEquipMagicXML(_oldEquipment.equipmentVO);
         switch(param1.equipmentType)
         {
            case "weapon":
               _currentOpenHoleXML = _loc3_["weapon"][0];
               break;
            case "necklace":
               _currentOpenHoleXML = _loc3_["necklace"][0];
               break;
            case "gourd":
               _currentOpenHoleXML = _loc3_["gourd"][0];
               break;
            case "clothes":
               _currentOpenHoleXML = _loc3_["clothes"][0];
               break;
            case "precious":
               _currentOpenHoleXML = _loc3_["precious"][0];
               break;
            default:
               return;
         }
         _baseSuccessRate = Number(_currentOpenHoleXML.@successRate);
         _maxSuccessRate = Number(_currentOpenHoleXML.@maxSuccessRate);
         _maxSuccessRate = Math.min(1,_maxSuccessRate);
         _successRate = _baseSuccessRate;
         _needMoney = int(_currentOpenHoleXML.@needMoney);
         clearNeedMaterial();
         _needMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[0].@id,XMLSingle.getInstance().equipmentXML));
         _needMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needMaterialNum = int(_currentOpenHoleXML.material[0].@num);
         clearNeedMagicMaterial();
         _needMagicMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[1].@id,XMLSingle.getInstance().equipmentXML));
         _needMagicMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needMagicMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needMagicMaterialNum = int(_currentOpenHoleXML.material[1].@num);
         clearNeedAMaterial();
         _needAMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[2].@id,XMLSingle.getInstance().equipmentXML));
         _needAMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needAMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needAMaterialNum = int(_currentOpenHoleXML.material[2].@num);
         clearNewEquipment();
         _newEquipment = _oldEquipment.clone();
         _newEquipment.addEventListener("rollOver",onOver,false,0,true);
         _newEquipment.addEventListener("rollOut",onOut,false,0,true);
         _newEquipment.equipmentVO = _newEquipment.equipmentVO;
         (_newEquipment as DisplayObject).scaleX = (_newEquipment as DisplayObject).scaleY = 1.5;
         addMagicAttribute(_newEquipment.equipmentVO);
         initPutInFrame();
         initShow();
         var _loc2_:uint = Math.ceil((_maxSuccessRate - _successRate) / 0.1);
         changeSuccessRate2(_loc2_);
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGroup.num = param1;
         changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
      }
      
      private function changeSuccessRate(param1:Number) : void
      {
         _successRate = Math.min(Math.max(0,param1),_maxSuccessRate);
         _successRateText.text = int(_successRate * 100) + "%";
      }
      
      private function initShow() : void
      {
         (_oldEquipment as DisplayObject).scaleX = (_oldEquipment as DisplayObject).scaleY = 1.3;
         (_needMaterial as DisplayObject).scaleX = (_needMaterial as DisplayObject).scaleY = 1.3;
         (_needMagicMaterial as DisplayObject).scaleX = (_needMagicMaterial as DisplayObject).scaleY = 1.3;
         (_needAMaterial as DisplayObject).scaleX = (_needAMaterial as DisplayObject).scaleY = 1.3;
         _oldContainer.addChild(_oldEquipment as DisplayObject);
         _materialContainer.addChild(_needMaterial as DisplayObject);
         _luckStoneContainer.addChild(_luckStoneShow);
         _magicMeterialContainer.addChild(_needMagicMaterial as DisplayObject);
         _aMeterialContainer.addChild(_needAMaterial as DisplayObject);
         _needNumText.text = _needMaterialNum.toString();
         _needMagicNumText.text = _needMagicMaterialNum.toString();
         _needANumText.text = _needAMaterialNum.toString();
         _successRateText.text = int(_successRate * 100) + "%";
         clearBuyLuckStoneBtn();
         _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
         _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
         _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
         addChild(_buyLuckStoneGuideBtn);
         refreshMaterialNumShow();
         refreshMagicMaterialNumShow();
         refreshAMaterialNumShow();
         refreshMoneyShow();
      }
      
      private function refreshMoneyShow() : void
      {
         if(_moneyText == null)
         {
            return;
         }
         clearBuyGoldPocketBtn();
         var _loc1_:TextFormat = _moneyText.defaultTextFormat;
         if(_player.playerVO.money >= _needMoney)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyGoldPocketBtn = new BuyGoldPocketGuideBtn(11100000,refreshMoneyShow,1);
         }
         _moneyText.defaultTextFormat = _loc1_;
         _moneyText.text = _needMoney.toString();
         _moneyText.width = _moneyText.textWidth + 5;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.x = _moneyText.x + _moneyText.width + 2;
            _buyGoldPocketBtn.y = _moneyText.y;
            addChild(_buyGoldPocketBtn);
            _buyGoldPocketBtn.IsBtnLaterOn(true);
         }
      }
      
      private function clearBuyGoldPocketBtn() : void
      {
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
      
      private function clearBuyMaterialBtn() : void
      {
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
      }
      
      private function clearBuyMagicMaterialBtn() : void
      {
         if(_buyMagicMaterialBtn)
         {
            if(_buyMagicMaterialBtn.parent)
            {
               _buyMagicMaterialBtn.parent.removeChild(_buyMagicMaterialBtn);
            }
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
      }
      
      private function refreshMaterialNumShow() : void
      {
         if(_needMaterial == null || _haveNumText == null)
         {
            return;
         }
         clearBuyMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMaterial,_player);
         var _loc1_:TextFormat = _haveNumText.defaultTextFormat;
         if(_loc2_ >= _needMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMaterial.equipmentVO.ticketPrice)
            {
               _buyMaterialBtn = new BuyMaterialGuideBtn(_needMaterial.equipmentVO.id,refreshMaterialNumShow,1);
               _buyMaterialBtn.x = _materialContainer.x - _buyMaterialBtn.width / 2;
               _buyMaterialBtn.y = _materialContainer.y + 35;
               addChild(_buyMaterialBtn);
            }
         }
         _haveNumText.defaultTextFormat = _loc1_;
         _haveNumText.text = _loc2_.toString();
      }
      
      private function refreshMagicMaterialNumShow() : void
      {
         if(_needMagicMaterial == null || _haveMagicNumText == null)
         {
            return;
         }
         clearBuyMagicMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMagicMaterial,_player);
         var _loc1_:TextFormat = _haveMagicNumText.defaultTextFormat;
         if(_loc2_ >= _needMagicMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMagicMaterial.equipmentVO.ticketPrice)
            {
               _buyMagicMaterialBtn = new BuyMaterialGuideBtn(_needMagicMaterial.equipmentVO.id,refreshMagicMaterialNumShow,1);
               _buyMagicMaterialBtn.x = _magicMeterialContainer.x - _buyMagicMaterialBtn.width / 2;
               _buyMagicMaterialBtn.y = _magicMeterialContainer.y + 35;
               addChild(_buyMagicMaterialBtn);
            }
         }
         _haveMagicNumText.defaultTextFormat = _loc1_;
         _haveMagicNumText.text = _loc2_.toString();
      }
      
      private function clearBuyAMaterialBtn() : void
      {
         if(_buyAMaterialBtn)
         {
            if(_buyAMaterialBtn.parent)
            {
               _buyAMaterialBtn.parent.removeChild(_buyAMaterialBtn);
            }
            _buyAMaterialBtn.clear();
         }
         _buyAMaterialBtn = null;
      }
      
      private function refreshAMaterialNumShow() : void
      {
         if(_needAMaterial == null || _haveANumText == null)
         {
            return;
         }
         clearBuyAMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needAMaterial,_player);
         var _loc1_:TextFormat = _haveANumText.defaultTextFormat;
         if(_loc2_ >= _needAMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needAMaterial.equipmentVO.ticketPrice)
            {
               _buyAMaterialBtn = new BuyMaterialGuideBtn(_needAMaterial.equipmentVO.id,refreshAMaterialNumShow,1);
               _buyAMaterialBtn.x = _aMeterialContainer.x - _buyAMaterialBtn.width / 2;
               _buyAMaterialBtn.y = _aMeterialContainer.y + 35;
               addChild(_buyAMaterialBtn);
            }
         }
         _haveANumText.defaultTextFormat = _loc1_;
         _haveANumText.text = _loc2_.toString();
      }
      
      private function setNumBtnGroupMaxNum() : void
      {
         if(_numBtnGroup)
         {
            _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         }
      }
      
      private function getHaveMaterialNum(param1:*, param2:Player) : int
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is Equipment)
         {
            _loc3_ = (param1 as Equipment).equipmentVO.id;
         }
         else
         {
            _loc3_ = param1;
         }
         var _loc5_:Vector.<EquipmentVO> = param2.playerVO.packageEquipmentVOs;
         var _loc4_:int = int(_loc5_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(_loc5_[_loc7_])
            {
               if(_loc5_[_loc7_].id == _loc3_)
               {
                  if(_loc5_[_loc7_] is StackEquipmentVO)
                  {
                     _loc6_ += (_loc5_[_loc7_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc6_++;
                  }
               }
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function clearBuyLuckStoneBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            if(_buyLuckStoneGuideBtn.parent)
            {
               _buyLuckStoneGuideBtn.parent.removeChild(_buyLuckStoneGuideBtn);
            }
            _buyLuckStoneGuideBtn.clear();
            _buyLuckStoneGuideBtn = null;
         }
      }
      
      private function initPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putIn");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _magicMeterialContainer = _show["magicMaterialContainer"];
         _aMeterialContainer = _show["anMaterialContainer"];
         _successRateText = _show["successRateText"];
         _moneyText = _show["moneyText"];
         _haveNumText = _show["haveNumText"];
         _needNumText = _show["needNumText"];
         _haveMagicNumText = _show["haveMagicNumText"];
         _needMagicNumText = _show["needMagicNumText"];
         _haveANumText = _show["haveANumText"];
         _needANumText = _show["needANumText"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_successRateText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_moneyText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveMagicNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needMagicNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveANumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needANumText,true);
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.setShow(_show["numBtnGroup"]);
         setNumBtnGroupMaxNum();
         _startOpenHoleBtn = new ButtonLogicShell2();
         _startOpenHoleBtn.setShow(_show["startOpenHoleBtn"]);
         _startOpenHoleBtn.setTipString("点击开始附魔");
      }
      
      private function getCurrentEquipMagicXML(param1:EquipmentVO) : XML
      {
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:XML = null;
         var _loc2_:* = null;
         var _loc5_:XMLList = _equipMagicXML.equipment;
         _loc3_ = int(_loc5_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc3_)
         {
            _loc4_ = _loc5_[_loc8_];
            _loc6_ = int(_loc4_.@num);
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               if(String(_loc4_["id" + (_loc7_ + 1)]) == param1.id.toString())
               {
                  _loc2_ = _loc4_;
                  break;
               }
               _loc7_++;
            }
            _loc8_++;
         }
         if(_loc2_ == null)
         {
            _loc2_ = _equipMagicXML.defau[0];
         }
         return _loc2_;
      }
      
      private function clearOldEquipment() : void
      {
         if(_oldEquipment)
         {
            if((_oldEquipment as DisplayObject).parent)
            {
               (_oldEquipment as DisplayObject).parent.removeChild(_oldEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_oldEquipment);
            _oldEquipment.clear();
            _oldEquipment = null;
         }
      }
      
      private function clearNewEquipment() : void
      {
         if(_newEquipment)
         {
            if((_newEquipment as DisplayObject).parent)
            {
               (_newEquipment as DisplayObject).parent.removeChild(_newEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_newEquipment);
            _newEquipment.clear();
            _newEquipment = null;
         }
      }
      
      private function clearNeedMagicMaterial() : void
      {
         if(_needMagicMaterial)
         {
            if((_needMagicMaterial as DisplayObject).parent)
            {
               (_needMagicMaterial as DisplayObject).parent.removeChild(_needMagicMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMagicMaterial);
            _needMagicMaterial.clear();
            _needMagicMaterial = null;
         }
      }
      
      private function clearNeedAMaterial() : void
      {
         if(_needAMaterial)
         {
            if((_needAMaterial as DisplayObject).parent)
            {
               (_needAMaterial as DisplayObject).parent.removeChild(_needAMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needAMaterial);
            _needAMaterial.clear();
            _needAMaterial = null;
         }
      }
      
      private function clearNeedMaterial() : void
      {
         if(_needMaterial)
         {
            if((_needMaterial as DisplayObject).parent)
            {
               (_needMaterial as DisplayObject).parent.removeChild(_needMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMaterial);
            _needMaterial.clear();
            _needMaterial = null;
         }
      }
      
      private function clearBuyBtn() : void
      {
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyMagicMaterialBtn)
         {
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

