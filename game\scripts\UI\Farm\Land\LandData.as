package UI.Farm.Land
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class LandData
   {
      public static const SEED_STATE:String = "seedState";
      
      public static const SPROUT_STATE:String = "sproutState";
      
      public static const GROWTH_STATE:String = "growthState";
      
      public static const MATURITY_STATE:String = "maturityState";
      
      public static const WITHER_STATE:String = "witherState";
      
      public var description:String;
      
      public var vipAccLandRecoverTime:int;
      
      private var _remainTime:int;
      
      private var _plantState:String;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function LandData()
      {
         super();
         init();
      }
      
      private function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.remainTime = _remainTime;
         _antiwear.plantState = _plantState;
      }
      
      public function get remainTime() : int
      {
         return _antiwear.remainTime;
      }
      
      public function set remainTime(param1:int) : void
      {
         _antiwear.remainTime = param1;
      }
      
      public function get plantState() : String
      {
         return _antiwear.plantState;
      }
      
      public function set plantState(param1:String) : void
      {
         _antiwear.plantState = param1;
      }
   }
}

