package UI.EquipmentMakeAndUpgrade
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class HuYouVipShow extends MySprite
   {
      private var _wanLoadSource:Array;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _show:MovieClip;
      
      public function HuYouVipShow(param1:Boolean)
      {
         var loadFinishListener:LoadFinishListener1;
         var isHaveBless:Boolean = param1;
         _wanLoadSource = ["OpenEquipMagic"];
         super();
         loadFinishListener = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("HuYouVipShow") as MovieClip;
            }
            _showMC = new MovieClipPlayLogicShell();
            _showMC.setShow(_show);
            addChild(_show);
            if(isHaveBless == true)
            {
               _showMC.gotoAndStop("haveVip");
            }
            else
            {
               _showMC.gotoAndStop("notHaveVip");
            }
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSource,loadFinishListener);
      }
      
      public function IsHaveBless(param1:Boolean) : void
      {
         if(_showMC == null)
         {
            return;
         }
         if(param1 == true)
         {
            _showMC.gotoAndStop("haveVip");
         }
         else
         {
            _showMC.gotoAndStop("notHaveVip");
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wanLoadSource);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
      }
   }
}

