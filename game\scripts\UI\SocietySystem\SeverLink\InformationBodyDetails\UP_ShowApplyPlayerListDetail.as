package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class UP_ShowApplyPlayerListDetail extends InformationBodyDetail
   {
      private var m_societyId:int;
      
      public function UP_ShowApplyPlayerListDetail()
      {
         super();
         m_informationBodyId = 3019;
      }
      
      public function initData(param1:int) : void
      {
         m_societyId = param1;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_societyId);
         return _loc1_;
      }
   }
}

