package UI.Animation.AnimationObjectByOneBitmap
{
   import flash.display.BitmapData;
   
   public class AnimationObjectDataByOneBitmap implements IAnimationObjectDataByOneBitmap
   {
      public static const POSITIVE_DIRECTION:int = 1;
      
      public static const NEGATIVE_DIRECTION:int = -1;
      
      private var _sourceBitmapData:BitmapData;
      
      private var _delay:Number;
      
      private var _moveDistance:Number;
      
      private var _direction:int;
      
      private var _width:Number;
      
      private var _height:Number;
      
      public function AnimationObjectDataByOneBitmap()
      {
         super();
      }
      
      public function get sourceBitmapData() : BitmapData
      {
         return _sourceBitmapData;
      }
      
      public function set sourceBitmapData(param1:BitmapData) : void
      {
         _sourceBitmapData = param1;
      }
      
      public function get delay() : Number
      {
         return _delay;
      }
      
      public function set delay(param1:Number) : void
      {
         _delay = param1;
      }
      
      public function get moveDistance() : Number
      {
         return _moveDistance;
      }
      
      public function set moveDistance(param1:Number) : void
      {
         _moveDistance = param1;
      }
      
      public function get direction() : int
      {
         return _direction;
      }
      
      public function set direction(param1:int) : void
      {
         _direction = param1;
      }
      
      public function get width() : Number
      {
         return _width;
      }
      
      public function set width(param1:Number) : void
      {
         _width = param1;
      }
      
      public function get height() : Number
      {
         return _height;
      }
      
      public function set height(param1:Number) : void
      {
         _height = param1;
      }
   }
}

