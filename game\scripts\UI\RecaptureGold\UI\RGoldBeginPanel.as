package UI.RecaptureGold.UI
{
   import UI.Button.QuitBtn;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RecaptureGold.RecaptureGoldMain;
   import UI.RecaptureGold.UI.Btn.BuyNumBtn;
   import UI.RecaptureGold.UI.Btn.InstructionBtn;
   import UI.RecaptureGold.UI.Btn.MoneyShopRewardBtn;
   import UI.RecaptureGold.UI.Btn.RGoldRankListBtn;
   import UI.RecaptureGold.UI.Btn.StartGameBtn;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class RGoldBeginPanel extends MySprite
   {
      public var startGameBtn:StartGameBtn;
      
      public var gotoMoneyShopBtn:MoneyShopRewardBtn;
      
      public var gotoRGoldRankListBtn:RGoldRankListBtn;
      
      public var buyNumBtn:BuyNumBtn;
      
      public var numText:TextField;
      
      public var instructionBtn:InstructionBtn;
      
      public var quitBtn:QuitBtn;
      
      private var _recaptureGoldXML:XML;
      
      public function RGoldBeginPanel(param1:XML)
      {
         super();
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _recaptureGoldXML = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(startGameBtn)
         {
            startGameBtn.clear();
         }
         startGameBtn = null;
         if(gotoMoneyShopBtn)
         {
            gotoMoneyShopBtn.clear();
         }
         gotoMoneyShopBtn = null;
         if(gotoRGoldRankListBtn)
         {
            gotoRGoldRankListBtn.clear();
         }
         gotoRGoldRankListBtn = null;
         if(buyNumBtn)
         {
            buyNumBtn.clear();
         }
         buyNumBtn = null;
         numText = null;
         if(instructionBtn)
         {
            instructionBtn.clear();
         }
         instructionBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _recaptureGoldXML = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("buyRGNum",showBuyPanel,true,0,true);
         addEventListener("showInstruction",showIntruction,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("buyRGNum",showBuyPanel,true);
         removeEventListener("showInstruction",showIntruction,true);
      }
      
      private function showIntruction(param1:Event) : void
      {
         var _loc4_:Boolean = false;
         var _loc5_:int = 0;
         var _loc2_:OIPanel = null;
         var _loc3_:int = numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(getChildAt(_loc5_) is OIPanel)
            {
               _loc4_ = true;
            }
            _loc5_++;
         }
         if(!_loc4_)
         {
            _loc2_ = new OIPanel();
            _loc2_.x = 50;
            _loc2_.y = 200;
            addChild(_loc2_);
         }
      }
      
      private function showBuyPanel(param1:Event) : void
      {
         var _loc2_:BuyRGNumBox = new BuyRGNumBox(_recaptureGoldXML,parent as RecaptureGoldMain,calculateRemianNum);
         _loc2_.x = (stage.stageWidth - _loc2_.width) / 2;
         _loc2_.y = (stage.stageHeight - _loc2_.height) / 2;
         addChild(_loc2_);
      }
      
      private function init() : void
      {
         startGameBtn.mouseChildren = false;
         startGameBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(startGameBtn,-100);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc2_:String = MyFunction.getInstance().splitTimeString(param1);
            if(RecaptureGoldData.getInstance().rGDate != _loc2_)
            {
               RecaptureGoldData.getInstance().buyNum = 0;
               RecaptureGoldData.getInstance().rGNum = 0;
               RecaptureGoldData.getInstance().rGDate = _loc2_;
            }
            calculateRemianNum();
         },null,true);
      }
      
      private function calculateRemianNum() : void
      {
         if(!_recaptureGoldXML)
         {
            return;
         }
         var _loc2_:int = int(_recaptureGoldXML.OtherData[0].@maxNum) + Math.abs(RecaptureGoldData.getInstance().buyNum);
         var _loc1_:int = _loc2_ - RecaptureGoldData.getInstance().rGNum;
         numText.text = _loc1_.toString();
         if(_loc1_ > 0)
         {
            startGameBtn.mouseChildren = true;
            startGameBtn.mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(startGameBtn,0);
         }
         else
         {
            startGameBtn.mouseChildren = false;
            startGameBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(startGameBtn,-100);
         }
      }
   }
}

