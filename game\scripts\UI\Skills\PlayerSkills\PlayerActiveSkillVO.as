package UI.Skills.PlayerSkills
{
   import UI.Skills.SkillVO;
   import UI.UIInterface.IActiveSkillVO;
   import YJFY.XydzjsData.IAttackSkillVOForAttackDataCalculate;
   
   public class PlayerActiveSkillVO extends SkillVO implements IActiveSkillVO, IAttackSkillVOForAttackDataCalculate
   {
      private var _originalCoolDown:int;
      
      private var _originalNextCoolDown:int;
      
      private var _coolDown:int;
      
      private var _nextCoolDown:int;
      
      private var _manaCost:int;
      
      private var _nextManaCost:int;
      
      private var _hurt:int;
      
      private var _nextHurt:int;
      
      private var _lengthOfTime:int;
      
      private var _nextLengthOfTime:int;
      
      private var _additionalAttack:int;
      
      private var _nextAdditionalAttack:int;
      
      private var _isCD:Boolean;
      
      private var _currentCDTime:int;
      
      public function PlayerActiveSkillVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.originalCoolDown = _originalCoolDown;
         _antiwear.originalNextCoolDown = _originalNextCoolDown;
         _antiwear.coolDown = _coolDown;
         _antiwear.nextCoolDown = _nextCoolDown;
         _antiwear.manaCost = _manaCost;
         _antiwear.nextManaCost = _nextManaCost;
         _antiwear.hurt = _hurt;
         _antiwear.nextHurt = _nextHurt;
         _antiwear.lengthOfTime = _lengthOfTime;
         _antiwear.nextLengthOfTime = _nextLengthOfTime;
         _antiwear.additionalAttack = _additionalAttack;
         _antiwear.nextAdditionalAttack = _nextAdditionalAttack;
         _antiwear.isCD = _isCD;
         _antiwear.currentCDTime = _currentCDTime;
      }
      
      override public function clone() : SkillVO
      {
         var _loc1_:PlayerActiveSkillVO = new PlayerActiveSkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribue(param1:SkillVO) : void
      {
         super.cloneAttribue(param1);
         (param1 as PlayerActiveSkillVO).originalCoolDown = this.originalCoolDown;
         (param1 as PlayerActiveSkillVO).originalNextCoolDown = this.originalNextCoolDown;
         (param1 as PlayerActiveSkillVO).coolDown = this.coolDown;
         (param1 as PlayerActiveSkillVO).nextCoolDown = this.nextCoolDown;
         (param1 as PlayerActiveSkillVO).manaCost = this.manaCost;
         (param1 as PlayerActiveSkillVO).nextManaCost = this.nextManaCost;
         (param1 as PlayerActiveSkillVO).hurt = this.hurt;
         (param1 as PlayerActiveSkillVO).nextHurt = this.nextHurt;
         (param1 as PlayerActiveSkillVO).lengthOfTime = this.lengthOfTime;
         (param1 as PlayerActiveSkillVO).nextLengthOfTime = this.nextLengthOfTime;
         (param1 as PlayerActiveSkillVO).additionalAttack = this.additionalAttack;
         (param1 as PlayerActiveSkillVO).nextAdditionalAttack = this.nextAdditionalAttack;
         (param1 as PlayerActiveSkillVO).currentCDTime = this.currentCDTime;
      }
      
      public function startCD() : void
      {
         currentCDTime = coolDown;
      }
      
      public function get originalCoolDown() : int
      {
         return _antiwear.originalCoolDown;
      }
      
      public function set originalCoolDown(param1:int) : void
      {
         _antiwear.originalCoolDown = param1;
      }
      
      public function get originalNextCoolDown() : int
      {
         return _antiwear.originalNextCoolDown;
      }
      
      public function set originalNextCoolDown(param1:int) : void
      {
         _antiwear.originalNextCoolDown = param1;
      }
      
      public function get coolDown() : int
      {
         return _antiwear.coolDown;
      }
      
      public function set nextCoolDown(param1:int) : void
      {
         _antiwear.nextCoolDown = param1;
      }
      
      public function get nextCoolDown() : int
      {
         return _antiwear.nextCoolDown;
      }
      
      public function set coolDown(param1:int) : void
      {
         _antiwear.coolDown = param1;
      }
      
      public function get manaCost() : int
      {
         return _antiwear.manaCost;
      }
      
      public function set manaCost(param1:int) : void
      {
         _antiwear.manaCost = param1;
      }
      
      public function get nextManaCost() : int
      {
         return _antiwear.nextManaCost;
      }
      
      public function set nextManaCost(param1:int) : void
      {
         _antiwear.nextManaCost = param1;
      }
      
      public function get hurt() : int
      {
         return _antiwear.hurt;
      }
      
      public function set hurt(param1:int) : void
      {
         _antiwear.hurt = param1;
      }
      
      public function get nextHurt() : int
      {
         return _antiwear.nextHurt;
      }
      
      public function set nextHurt(param1:int) : void
      {
         _antiwear.nextHurt = param1;
      }
      
      public function get lengthOfTime() : int
      {
         return _antiwear.lengthOfTime;
      }
      
      public function set lengthOfTime(param1:int) : void
      {
         _antiwear.lengthOfTime = param1;
      }
      
      public function get nextLengthOfTime() : int
      {
         return _antiwear.nextLengthOfTime;
      }
      
      public function set nextLengthOfTime(param1:int) : void
      {
         _antiwear.nextLengthOfTime = param1;
      }
      
      public function get additionalAttack() : int
      {
         return _antiwear.additionalAttack;
      }
      
      public function set additionalAttack(param1:int) : void
      {
         _antiwear.additionalAttack = param1;
      }
      
      public function get nextAdditionalAttack() : int
      {
         return _antiwear.nextAdditionalAttack;
      }
      
      public function set nextAdditionalAttack(param1:int) : void
      {
         _antiwear.nextAdditionalAttack = param1;
      }
      
      public function get isCD() : Boolean
      {
         return _antiwear.currentCDTime;
      }
      
      public function get currentCDTime() : int
      {
         return _antiwear.currentCDTime;
      }
      
      public function set currentCDTime(param1:int) : void
      {
         if(param1 < 0)
         {
            param1 = 0;
         }
         _antiwear.currentCDTime = param1;
      }
      
      public function getSkillHurtMulti() : Number
      {
         return additionalAttack / 100 - 1;
      }
      
      public function getSkillHurtAdd() : uint
      {
         return hurt;
      }
   }
}

