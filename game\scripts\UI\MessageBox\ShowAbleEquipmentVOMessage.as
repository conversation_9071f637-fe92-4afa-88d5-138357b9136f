package UI.MessageBox
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.ShiTu.PromoteValueObject;
   
   public class ShowAbleEquipmentVOMessage
   {
      public function ShowAbleEquipmentVOMessage()
      {
         super();
      }
      
      public function addMessage_InsetGem(param1:AbleEquipmentVO) : String
      {
         var _loc10_:int = 0;
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:InsetGemEquipmentVO = null;
         var _loc9_:* = undefined;
         var _loc4_:String = null;
         var _loc6_:String = "";
         var _loc3_:String = "";
         var _loc5_:int = param1.getHoleNum();
         _loc10_ = 0;
         while(_loc10_ < _loc5_)
         {
            _loc2_ = param1.getInsetGem(_loc10_);
            if(_loc2_)
            {
               _loc3_ = "";
               _loc3_ += MessageBoxFunction.getInstance().toHTMLText(_loc2_.name + "：",15);
               _loc9_ = PromoteValueObject.getDescriptions(_loc2_.attributes,_loc2_.attributeValues,_loc2_.descriptions);
               _loc7_ = !!_loc9_ ? _loc9_.length : 0;
               _loc4_ = "";
               _loc8_ = 0;
               while(_loc8_ < _loc7_)
               {
                  _loc4_ += MessageBoxFunction.getInstance().toHTMLText(_loc9_[_loc8_],16);
                  _loc8_++;
               }
               _loc3_ += _loc4_;
               if(_loc10_ < _loc5_ - 1 && param1.getInsetGem(_loc10_ + 1))
               {
                  _loc3_ += MessageBoxFunction.getInstance().toHTMLText("<br>");
               }
               _loc3_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc3_,int(_loc2_.showColor).toString(16));
               _loc6_ += _loc3_;
            }
            else if(_loc10_ < _loc5_ - 1 && param1.getInsetGem(_loc10_ + 1))
            {
               _loc6_ += MessageBoxFunction.getInstance().toHTMLText("<br>");
            }
            _loc10_++;
         }
         return _loc6_;
      }
   }
}

