package UI2.Midautumn
{
   import Json.MyJSON;
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.text.TextField;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class WzwView extends MySprite
   {
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/wzw/query";
      
      private var m_strRank:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/wzw/more";
      
      private var m_strGetNum:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/wzw/rank";
      
      private var m_show:MovieClip;
      
      private var m_midautumnPanel:MidautumnPanel;
      
      private var m_mainbobing:MovieClip;
      
      private var m_getPanel:MovieClip;
      
      private var m_rulePanel:MovieClip;
      
      private var m_resultPanel:MovieClip;
      
      private var m_numPanel:MovieClip;
      
      private var m_rankPanel:MovieClip;
      
      private var m_wzwResult:MovieClip;
      
      private var m_wzwMainPanel:MovieClip;
      
      private var m_canInPanel:MovieClip;
      
      private var m_buyview:WzwBuyView;
      
      private var m_resultView:WzwResult;
      
      private var m_rankview:RankView;
      
      private var m_handLogic:HandLogic;
      
      private var m_vectNum:Vector.<DiceItem>;
      
      private var m_btnCanIn:ButtonLogicShell2;
      
      private var m_btnClose:ButtonLogicShell2;
      
      private var m_btnStart:ButtonLogicShell2;
      
      private var m_btnRule:ButtonLogicShell2;
      
      private var m_btnRank:ButtonLogicShell2;
      
      private var m_topThree:Vector.<MovieClip>;
      
      private var m_topName:Vector.<TextField>;
      
      private var m_bestPoint:MovieClip;
      
      private var m_txtRemain:TextField;
      
      private var m_txtCurRank:TextField;
      
      private var m_bOpen:Boolean = true;
      
      private var m_bStartRun:Boolean = false;
      
      private var m_bBtnClick:Boolean = false;
      
      private var m_bRewardShow:Boolean = false;
      
      private var m_timeId:int;
      
      private var m_i:int = 0;
      
      private var m_xlist:Vector.<Number>;
      
      private var m_ylist:Vector.<Number>;
      
      public function WzwView()
      {
         super();
         m_vectNum = new Vector.<DiceItem>();
         m_topThree = new Vector.<MovieClip>();
         m_topName = new Vector.<TextField>();
         m_xlist = new Vector.<Number>();
         m_ylist = new Vector.<Number>();
         m_xlist.push(393);
         m_xlist.push(465);
         m_xlist.push(400);
         m_xlist.push(448);
         m_xlist.push(468);
         m_xlist.push(523);
         m_ylist.push(312);
         m_ylist.push(302);
         m_ylist.push(378);
         m_ylist.push(400);
         m_ylist.push(356);
         m_ylist.push(357);
      }
      
      override public function clear() : void
      {
         m_rulePanel.removeEventListener("click",callRule);
         m_show.removeEventListener("clickButton",clickButton);
         clearTimeout(m_timeId);
         m_timeId = 0;
         ClearUtil.clearObject(m_rankview);
         m_rankview = null;
         ClearUtil.clearObject(m_xlist);
         m_xlist = null;
         ClearUtil.clearObject(m_ylist);
         m_ylist = null;
         ClearUtil.clearObject(m_vectNum);
         m_vectNum = null;
         ClearUtil.clearObject(m_btnClose);
         m_btnClose = null;
         ClearUtil.clearObject(m_btnCanIn);
         m_btnCanIn = null;
         ClearUtil.clearObject(m_btnStart);
         m_btnStart = null;
         ClearUtil.clearObject(m_btnRule);
         m_btnRule = null;
         ClearUtil.clearObject(m_btnRank);
         m_btnRank = null;
         ClearUtil.clearObject(m_topThree);
         m_topThree = null;
         ClearUtil.clearObject(m_topName);
         m_topName = null;
         super.clear();
      }
      
      public function init(param1:MovieClip, param2:MidautumnPanel) : void
      {
         m_bOpen = true;
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_midautumnPanel = param2;
         initNum(m_show);
         initOut(m_show);
         initMain(m_show);
         initGet(m_show);
         initRule(m_show);
         initResult(m_show);
         initRank(m_show);
         initWzwResult(m_show);
         initWzwPanel(m_show);
         sendInfo();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_handLogic)
         {
            m_handLogic.render(param1);
         }
         m_i = 0;
         while(m_i < m_vectNum.length)
         {
            m_vectNum[m_i].render(param1);
            m_i++;
         }
         if(m_bRewardShow == false && getIsEndShow() && m_bStartRun)
         {
            m_bRewardShow = true;
            if(m_timeId > 0)
            {
               clearTimeout(m_timeId);
               m_timeId = 0;
            }
            m_timeId = setTimeout(mainResult,2000);
         }
         if(m_resultView)
         {
            m_resultView.render(param1);
         }
      }
      
      private function mainResult() : void
      {
         clearTimeout(m_timeId);
         m_timeId = 0;
         showResult();
      }
      
      private function showResult() : void
      {
         m_wzwResult.visible = true;
         if(m_resultView == null)
         {
            m_resultView = new WzwResult();
            m_resultView.init(m_wzwResult,this);
         }
         m_resultView.refreshInfo();
         GamingUI.getInstance().refresh(16384);
      }
      
      public function hideResult() : void
      {
         m_wzwResult.visible = false;
         m_bRewardShow = false;
         m_bStartRun = false;
         m_btnStart.unLock();
         sendInfo();
      }
      
      private function getIsEndShow() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:Boolean = true;
         _loc2_ = 0;
         while(_loc2_ < m_vectNum.length)
         {
            if(m_vectNum[_loc2_].getEndShow() == false)
            {
               _loc1_ = false;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      private function initOut(param1:MovieClip) : void
      {
         (param1["playcan"] as MovieClip).visible = false;
         m_canInPanel = param1["playcan"] as MovieClip;
      }
      
      private function showOut() : void
      {
         m_canInPanel.visible = true;
         if(m_btnCanIn == null)
         {
            m_btnCanIn = new ButtonLogicShell2();
            m_btnCanIn.setShow(m_canInPanel["btnyes"]);
            m_btnCanIn.setTipString("点击退出");
         }
      }
      
      private function initNum(param1:MovieClip) : void
      {
         var _loc2_:DiceItem = null;
         var _loc3_:int = 0;
         m_numPanel = param1["panelnum"] as MovieClip;
         m_handLogic = new HandLogic();
         m_handLogic.init(m_numPanel,null,this);
         _loc3_ = 0;
         while(_loc3_ < 6)
         {
            _loc2_ = new DiceItem();
            _loc2_.init(_loc3_ + 1,m_numPanel,null,this);
            _loc2_.setEndXY(m_xlist[_loc3_],m_ylist[_loc3_]);
            m_vectNum.push(_loc2_);
            _loc3_++;
         }
      }
      
      private function initMain(param1:MovieClip) : void
      {
         m_mainbobing = param1["mainbobing"] as MovieClip;
         m_mainbobing.visible = false;
      }
      
      private function initGet(param1:MovieClip) : void
      {
         m_getPanel = param1["getpanel"] as MovieClip;
         m_getPanel.visible = false;
      }
      
      private function initRule(param1:MovieClip) : void
      {
         m_rulePanel = param1["panelrule"] as MovieClip;
         m_rulePanel.visible = false;
         m_rulePanel.gotoAndStop("2");
         m_rulePanel.addEventListener("click",callRule);
      }
      
      private function initResult(param1:MovieClip) : void
      {
         m_resultPanel = param1["panelresult"] as MovieClip;
         m_resultPanel.visible = false;
      }
      
      private function initRank(param1:MovieClip) : void
      {
         m_rankPanel = param1["panelrank"] as MovieClip;
         m_rankPanel.visible = false;
      }
      
      public function hideRank() : void
      {
         m_rankPanel.visible = false;
      }
      
      private function initWzwResult(param1:MovieClip) : void
      {
         m_wzwResult = param1["wzwResult"] as MovieClip;
         m_wzwResult.visible = false;
      }
      
      private function initWzwPanel(param1:MovieClip) : void
      {
         var _loc2_:int = 0;
         m_wzwMainPanel = param1["panelwzwmain"] as MovieClip;
         m_wzwMainPanel.visible = true;
         m_btnClose = new ButtonLogicShell2();
         m_btnClose.setShow(m_wzwMainPanel["btnClose"]);
         m_btnClose.setTipString("关闭");
         m_btnStart = new ButtonLogicShell2();
         m_btnStart.setShow(m_wzwMainPanel["btnStart"]);
         m_btnStart.setTipString("点击博饼");
         m_btnRule = new ButtonLogicShell2();
         m_btnRule.setShow(m_wzwMainPanel["btnRule"]);
         m_btnRule.setTipString("查看规则");
         m_btnRank = new ButtonLogicShell2();
         m_btnRank.setShow(m_wzwMainPanel["btnLookRank"]);
         m_btnRank.setTipString("查看排名");
         m_txtRemain = m_wzwMainPanel["txtNum"] as TextField;
         m_txtCurRank = m_wzwMainPanel["txtCurRank"] as TextField;
         _loc2_ = 1;
         while(_loc2_ <= 3)
         {
            m_topThree.push(m_wzwMainPanel["mcNum_" + _loc2_] as MovieClip);
            m_topName.push(m_wzwMainPanel["txtname_" + _loc2_] as TextField);
            _loc2_++;
         }
         m_bestPoint = m_wzwMainPanel["mcnummax"] as MovieClip;
         initPoint();
         initBestPoint();
      }
      
      private function initPoint() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_topThree.length)
         {
            _loc1_ = 1;
            while(_loc1_ <= 6)
            {
               (m_topThree[_loc2_]["num_" + _loc1_] as MovieClip).gotoAndStop("2");
               _loc1_++;
            }
            _loc2_++;
         }
      }
      
      private function initBestPoint() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 1;
         while(_loc1_ <= 6)
         {
            (m_bestPoint["num_" + _loc1_] as MovieClip).gotoAndStop("3");
            _loc1_++;
         }
      }
      
      private function callRule(param1:MouseEvent) : void
      {
         m_rulePanel.visible = false;
      }
      
      public function hideAllNum() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].hideItem();
            _loc1_++;
         }
      }
      
      public function showAllNum() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].showItem();
            _loc1_++;
         }
      }
      
      public function startRun() : void
      {
         var _loc1_:int = 0;
         m_bStartRun = true;
         showAllNum();
         _loc1_ = 0;
         while(_loc1_ < m_vectNum.length)
         {
            m_vectNum[_loc1_].setType(m_handLogic.getType());
            m_vectNum[_loc1_].Calculate_BeginPos();
            _loc1_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var e:ButtonEvent = param1;
         switch(e.button)
         {
            case m_btnCanIn:
               m_canInPanel.visible = false;
               m_btnStart.lock();
               break;
            case m_btnClose:
               if(m_bStartRun == false)
               {
                  m_midautumnPanel.CloseUI();
               }
               break;
            case m_btnRule:
               if(m_bStartRun == false)
               {
                  m_rulePanel.visible = true;
               }
               break;
            case m_btnStart:
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  if(TimeUtil.getTimeUtil().getInTime(MidautumnData.getInstance().getStartTime2(),MidautumnData.getInstance().getEndTime2(),param1))
                  {
                     checkTime(param1);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("超出活动时间");
                  }
               },function():void
               {
                  GamingUI.getInstance().showMessageTip("服务器时间加载失败");
                  Part1.getInstance().hideGameWaitShow();
               },true);
               break;
            case m_btnRank:
               if(m_rankview == null)
               {
                  m_rankview = new RankView();
                  m_rankview.init(m_rankPanel,this);
               }
               m_rankview.sendRank();
               m_rankPanel.visible = true;
         }
      }
      
      public function checkTime(param1:String) : void
      {
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
         {
            GamingUI.getInstance().showMessageTip("背包空位不足，请先清理");
            return;
         }
         if(WzwData.getInstance().getFreeNum() > 0)
         {
            m_bBtnClick = true;
            sendPoint();
         }
         else
         {
            m_getPanel.visible = true;
            if(m_buyview == null)
            {
               m_buyview = new WzwBuyView();
               m_buyview.init(m_getPanel,this);
            }
            m_buyview.refreshInfo();
         }
      }
      
      public function hideIfBuy() : void
      {
         m_getPanel.visible = false;
      }
      
      public function startReward() : void
      {
         m_btnStart.lock();
         m_handLogic.MouseMove(null);
      }
      
      private function sendInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "请求数据中， 请勿关闭游戏！";
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorHandler);
         _loc1_.addEventListener("securityError",onErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strOpenUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         WzwData.getInstance().initData(_loc2_);
         if(canIn())
         {
            refreshBest();
            refreshRemain();
            refreshTopThree();
         }
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求数据错误!");
      }
      
      public function sendPoint() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderPointCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorPointHandler);
         _loc1_.addEventListener("securityError",onErrorPointHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strGetNum);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc3_.Name = GameData.getInstance().getLoginReturnData().getNickname();
         _loc3_.IsAdd = true;
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderPointCompleteHandler(param1:Event) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",onLoaderPointCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorPointHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorPointHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         WzwData.getInstance().initPoint(_loc2_);
         refreshRemain();
         if(canIn())
         {
            _loc3_ = 0;
            while(_loc3_ < m_vectNum.length)
            {
               m_vectNum[_loc3_].setResult(WzwData.getInstance().getPointByIndex(_loc3_));
               _loc3_++;
            }
            startReward();
         }
      }
      
      protected function onErrorPointHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderPointCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorPointHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorPointHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求点数数据错误!");
      }
      
      private function canIn() : Boolean
      {
         return true;
      }
      
      private function hideMy() : void
      {
         m_txtRemain.text = "0";
         m_bestPoint.visible = false;
         m_txtCurRank.text = "无";
      }
      
      public function sendRank() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderRankCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorRankHandler);
         _loc1_.addEventListener("securityError",onErrorRankHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strRank);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.Start = 0;
         _loc3_.Limit = 3;
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderRankCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRankCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRankHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRankHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         WzwData.getInstance().initTopThree(_loc2_);
         refreshTopThree();
      }
      
      protected function onErrorRankHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRankCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRankHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRankHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("请求博饼排行榜数据错误!");
      }
      
      private function hideTopThree() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 3)
         {
            m_topThree[_loc1_].visible = false;
            m_topName[_loc1_].visible = false;
            _loc1_++;
         }
      }
      
      public function refreshTopThree() : void
      {
         var _loc1_:RankItem = null;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         hideTopThree();
         _loc3_ = 0;
         while(_loc3_ < 3)
         {
            m_topName[_loc3_].visible = true;
            _loc1_ = WzwData.getInstance().getTopByIndex(_loc3_);
            if(_loc1_)
            {
               m_topThree[_loc3_].visible = true;
               (m_topName[_loc3_] as TextField).text = _loc1_.name;
               _loc2_ = 0;
               while(_loc2_ < 6)
               {
                  (m_topThree[_loc3_]["num_" + (_loc2_ + 1)] as MovieClip).gotoAndStop(String(_loc1_.numlist[_loc2_]));
                  _loc2_++;
               }
            }
            else
            {
               (m_topName[_loc3_] as TextField).text = "无";
            }
            _loc3_++;
         }
      }
      
      public function refreshBest() : void
      {
         var _loc1_:int = 0;
         if(WzwData.getInstance().getIsHaveBest() == false)
         {
            m_bestPoint.visible = false;
            m_txtCurRank.text = "无";
            return;
         }
         m_bestPoint.visible = true;
         _loc1_ = 0;
         while(_loc1_ < 6)
         {
            if(WzwData.getInstance().getNumByIndex(_loc1_))
            {
               (m_bestPoint["num_" + (_loc1_ + 1)] as MovieClip).gotoAndStop(String(WzwData.getInstance().getNumByIndex(_loc1_)));
            }
            _loc1_++;
         }
         m_txtCurRank.text = String(WzwData.getInstance().getCurrRank());
      }
      
      public function refreshRemain() : void
      {
         m_txtRemain.text = String(WzwData.getInstance().getFreeNum());
      }
   }
}

