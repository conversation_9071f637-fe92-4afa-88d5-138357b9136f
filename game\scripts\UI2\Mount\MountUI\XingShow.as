package UI2.Mount.MountUI
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class XingShow
   {
      private var m_xingTotalNum:uint;
      
      private var m_xingShineNum:uint;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_xingShows:Vector.<MovieClipPlayLogicShell>;
      
      private var m_show:MovieClip;
      
      public function XingShow()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         m_xingShows = new Vector.<MovieClipPlayLogicShell>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_xingShows);
         m_xingShows = null;
         m_show = null;
      }
      
      public function init(param1:MovieClip, param2:uint) : void
      {
         m_show = param1;
         m_showMC.setShow(m_show);
         m_xingTotalNum = param2;
         initShow();
         initShow2();
      }
      
      public function setXingTotalNum(param1:uint) : void
      {
         m_xingTotalNum = param1;
         initShow();
      }
      
      public function setXingShineNum(param1:uint) : void
      {
         var _loc2_:int = 0;
         m_xingShineNum = param1;
         _loc2_ = 0;
         while(_loc2_ < m_xingShineNum)
         {
            if(_loc2_ < m_xingShows.length)
            {
               m_xingShows[_loc2_].gotoAndStop("2");
            }
            _loc2_++;
         }
         while(_loc2_ < m_xingShows.length)
         {
            m_xingShows[_loc2_].gotoAndStop("1");
            _loc2_++;
         }
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:int = 0;
         var _loc3_:MovieClipPlayLogicShell = null;
         m_showMC.gotoAndStop(m_xingTotalNum.toString());
         ClearUtil.clearObject(m_xingShows);
         m_xingShows.length = 0;
         _loc4_ = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc1_ = m_show.getChildAt(_loc5_);
            if(_loc1_.name.substr(0,5) == "xing_")
            {
               _loc2_++;
            }
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = new MovieClipPlayLogicShell();
            _loc3_.setShow(m_show["xing_" + (_loc5_ + 1)]);
            m_xingShows.push(_loc3_);
            _loc5_++;
         }
      }
      
      private function initShow2() : void
      {
         setXingShineNum(0);
      }
   }
}

