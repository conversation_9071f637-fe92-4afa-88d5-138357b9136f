package UI.PackageAndStorage
{
   import UI.Button.AKeySoldBtn;
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.TiDyBtn;
   import UI.DragEquipmentEngine;
   import UI.EquipmentArea;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.EquipmentCells.StorageEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.LockCell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.TidyEquipments.TidyEquipmentEvent;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class StorageArea extends MySprite
   {
      public static const PACKAGE_TYPE:int = 0;
      
      public static const STORAGE_TYPE:int = 1;
      
      public static const PUBLIC_STORAGE_TYPE:int = 2;
      
      public static const SMALL_PACKAGE_TYPE:int = 3;
      
      private static const _WARNING_BOX_X:Number = 69;
      
      private static const _WARNING_BOX_Y:Number = 100;
      
      private static const _CELL_START_X:Number = -3;
      
      private static const _CELL_START_Y:Number = -10;
      
      protected var _area:EquipmentArea;
      
      protected var _equipmentVOs:Vector.<EquipmentVO>;
      
      protected var _useEquipmentVOs:Vector.<EquipmentVO>;
      
      private var m_list:Vector.<int>;
      
      protected var _lockCells:Vector.<LockCell>;
      
      protected var _widthSize:int;
      
      protected var _heightSize:int;
      
      protected var _dragEquipmentEngine:DragEquipmentEngine;
      
      protected var _moneyShow:MoneyShow;
      
      protected var _PKPointShow:PKPointShow;
      
      protected var _moneyText:TextField;
      
      protected var _pkPointText:TextField;
      
      protected var _storageType:int;
      
      protected var _money:int;
      
      protected var _PKPoint:int;
      
      protected var _cellAllNum:int;
      
      public var pageBtnGroup:PageBtnGroup;
      
      private var m_getValue:Boolean = false;
      
      private var _currentShowIndex:int;
      
      public function StorageArea(param1:int)
      {
         super();
         m_getValue = false;
         m_list = new Vector.<int>();
         init(param1);
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.deleteEventListener(_area.equipmentCells);
         }
         ClearUtil.clearObject(m_list);
         m_list = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            if(_loc2_ is Bitmap)
            {
               if((_loc2_ as Bitmap).bitmapData)
               {
                  (_loc2_ as Bitmap).bitmapData.dispose();
               }
               (_loc2_ as Bitmap).bitmapData = null;
            }
            removeChildAt(0);
         }
         if(_area)
         {
            _area.clear();
         }
         _area = null;
         var _loc3_:int = 0;
         _equipmentVOs = null;
         _useEquipmentVOs = null;
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.clear();
         }
         _dragEquipmentEngine = null;
         if(_lockCells)
         {
            _loc1_ = int(_lockCells.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _lockCells[_loc3_] = null;
               _loc3_++;
            }
         }
         _lockCells = null;
         if(pageBtnGroup)
         {
            pageBtnGroup.clear();
         }
         pageBtnGroup = null;
         if(_moneyText)
         {
            _moneyText.removeEventListener("rollOver",rollOver,false);
            _moneyText.removeEventListener("rollOut",rollOut,false);
         }
         _moneyText = null;
         _pkPointText = null;
      }
      
      public function refreshEquipments(param1:Vector.<EquipmentVO> = null) : void
      {
         if(param1)
         {
            _equipmentVOs = param1;
         }
         m_getValue = true;
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.equipmentVOs = _equipmentVOs;
         }
         var _loc2_:int = int(_equipmentVOs.length / (_widthSize * _heightSize)) + 1;
         if(pageBtnGroup.pageNum <= _loc2_)
         {
            setAreaPage(pageBtnGroup.pageNum);
         }
         else
         {
            setAreaPage(1);
         }
         arrangeEquipment((pageBtnGroup.pageNum - 1) * (_widthSize * _heightSize));
      }
      
      public function addEquipmentVOs(param1:EquipmentVO = null, param2:int = 0) : Boolean
      {
         if(MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_equipmentVOs,param1,param2))
         {
            setAreaPage(pageBtnGroup.pageNum);
            arrangeEquipment((pageBtnGroup.pageNum - 1) * (_widthSize * _heightSize));
            return true;
         }
         return false;
      }
      
      public function removeEquipmentVO(param1:int) : EquipmentVO
      {
         var _loc3_:int = param1 + _widthSize * _heightSize * (pageBtnGroup.pageNum - 1);
         if(_equipmentVOs[_loc3_] == null)
         {
            return null;
         }
         var _loc2_:EquipmentVO = _equipmentVOs[_loc3_];
         _equipmentVOs[_loc3_] = null;
         return _loc2_;
      }
      
      public function getEquipmentVO(param1:int) : EquipmentVO
      {
         var _loc3_:int = param1 + _widthSize * _heightSize * (pageBtnGroup.pageNum - 1);
         if(_equipmentVOs[_loc3_] == null)
         {
            return null;
         }
         return _equipmentVOs[_loc3_];
      }
      
      public function addEquipmentVO(param1:EquipmentVO, param2:int) : void
      {
         var _loc3_:int = param2 + _widthSize * _heightSize * (pageBtnGroup.pageNum - 1);
         if(_equipmentVOs[_loc3_] != null)
         {
            return;
         }
         _equipmentVOs[_loc3_] = param1;
      }
      
      public function removeEquipmentVOFromCell(param1:int) : void
      {
         if(!_area.equipmentCells[param1].isHaveChild)
         {
            return;
         }
         _area.equipmentCells[param1].removeEquipmentVO();
      }
      
      public function addEquipmentVOToCell(param1:EquipmentVO, param2:int) : void
      {
         if(_area.equipmentCells[param2].isHaveChild)
         {
            return;
         }
         _area.equipmentCells[param2].addEquipmentVO(param1);
         setAreaPage(pageBtnGroup.pageNum);
         arrangeEquipment((pageBtnGroup.pageNum - 1) * (_widthSize * _heightSize));
      }
      
      public function addDragEquipment(param1:Sprite) : void
      {
         _dragEquipmentEngine = new DragEquipmentEngine();
         _dragEquipmentEngine.init(_area.equipmentCells,param1,this);
         _dragEquipmentEngine.equipmentVOs = _equipmentVOs;
      }
      
      public function addDragTarget(param1:*) : void
      {
         _dragEquipmentEngine.addMoveTarget(param1);
      }
      
      public function removeDragTarget(param1:*) : void
      {
         _dragEquipmentEngine.removeMoveTarget(param1);
      }
      
      public function isLoaded() : Boolean
      {
         return _area.isLoaded();
      }
      
      public function isAllChildLoad() : Boolean
      {
         if(m_getValue == false)
         {
            return false;
         }
         var _loc2_:int = Math.min(_area.equipmentCells.length,30);
         var _loc3_:int = 0;
         var _loc1_:Boolean = true;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if((_area.equipmentCells[_loc3_] == null || (_area.equipmentCells[_loc3_] as PackageEquipmentCell).isHaveChild == false) && _equipmentVOs[_loc3_] != null)
            {
               _loc1_ = false;
               break;
            }
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function arrangeEquipment(param1:int) : void
      {
         var _loc5_:int = 0;
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.deleteEventListener(_area.equipmentCells);
         }
         for each(var _loc2_ in _area.equipmentCells)
         {
            _loc2_.removeEquipmentVO();
         }
         arrangArea(Math.min(Math.max(_equipmentVOs.length - param1,0),_widthSize * _heightSize));
         var _loc3_:int = int(_area.equipmentCells.length);
         var _loc4_:int = _equipmentVOs.length - param1;
         _loc5_ = 0;
         while(_loc5_ < _loc3_ && _loc5_ < _loc4_)
         {
            _area.equipmentCells[_loc5_].addEquipmentVO(_equipmentVOs[param1 + _loc5_]);
            _loc5_++;
         }
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.addCellEventListener(_area.equipmentCells);
         }
      }
      
      public function refreshCellEventListener() : void
      {
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.deleteEventListener(_area.equipmentCells);
         }
         if(_dragEquipmentEngine)
         {
            _dragEquipmentEngine.addCellEventListener(_area.equipmentCells);
         }
      }
      
      public function get area() : EquipmentArea
      {
         return _area;
      }
      
      public function get equipmentVOs() : Vector.<EquipmentVO>
      {
         return _equipmentVOs;
      }
      
      public function setMoneyText(param1:int) : void
      {
         _money = param1;
         if(_moneyText)
         {
            _moneyText.text = MyFunction2.transformMoneyConcise1(param1);
         }
      }
      
      public function setPKPointText(param1:int) : void
      {
         _PKPoint = param1;
         if(_pkPointText)
         {
            _pkPointText.text = param1.toString();
         }
      }
      
      protected function init(param1:int) : void
      {
         var _loc4_:TiDyBtn = null;
         var _loc10_:PackageBackgroundBitmapData = null;
         var _loc11_:Bitmap = null;
         var _loc2_:AKeySoldBtn = null;
         var _loc8_:StorageBackgroundBitmapData = null;
         var _loc7_:Bitmap = null;
         var _loc3_:PublicStorageBackgroupBitmapData = null;
         var _loc9_:Bitmap = null;
         var _loc5_:DoubleStorageNameBitmapdata = null;
         var _loc6_:Bitmap = null;
         _storageType = param1;
         _equipmentVOs = new Vector.<EquipmentVO>();
         _lockCells = new Vector.<LockCell>();
         pageBtnGroup = new PageBtnGroup();
         switch(param1)
         {
            case 0:
               _loc10_ = new PackageBackgroundBitmapData(444,427);
               _loc11_ = new Bitmap(_loc10_);
               _loc11_.x = -25;
               _loc11_.y = -40;
               addChild(_loc11_);
               _moneyShow = new MoneyShow();
               _moneyShow.x = 275;
               _moneyShow.y = 318;
               addChild(_moneyShow);
               _PKPointShow = new PKPointShow();
               _PKPointShow.x = 140;
               _PKPointShow.y = 310;
               addChild(_PKPointShow);
               _moneyText = _moneyShow.getMoneyText();
               _moneyText.selectable = false;
               MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_moneyText);
               _pkPointText = _PKPointShow.getPKText();
               _pkPointText.selectable = false;
               MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_pkPointText);
               _loc2_ = new AKeySoldBtn();
               _loc2_.x = 0;
               _loc2_.y = 325;
               addChild(_loc2_);
               pageBtnGroup.x = 210;
               pageBtnGroup.y = 275;
               addChild(pageBtnGroup);
               _loc4_ = new TiDyBtn();
               _loc4_.x = 0;
               _loc4_.y = 272;
               addChild(_loc4_);
               _cellAllNum = XMLSingle.getInstance().getPackageCellNum();
               initAreaSize(6,5,PackageEquipmentCell);
               setAreaPage(1);
               break;
            case 1:
               _loc8_ = new StorageBackgroundBitmapData(452,333);
               _loc7_ = new Bitmap(_loc8_);
               _loc7_.x = -25;
               _loc7_.y = -32;
               addChild(_loc7_);
               pageBtnGroup.x = 210;
               pageBtnGroup.y = 334;
               addChild(pageBtnGroup);
               _loc4_ = new TiDyBtn();
               _loc4_.x = 0;
               _loc4_.y = 330;
               addChild(_loc4_);
               _cellAllNum = XMLSingle.getInstance().getStorageCellNum();
               initAreaSize(6,6,StorageEquipmentCell);
               setAreaPage(1);
               break;
            case 2:
               _loc3_ = new PublicStorageBackgroupBitmapData(452,333);
               _loc9_ = new Bitmap(_loc3_);
               _loc9_.x = -25;
               _loc9_.y = -32;
               addChild(_loc9_);
               _loc5_ = new DoubleStorageNameBitmapdata();
               _loc6_ = new Bitmap(_loc5_);
               _loc6_.x = 680;
               _loc6_.y = -165;
               addChild(_loc6_);
               pageBtnGroup.x = 210;
               pageBtnGroup.y = 234;
               addChild(pageBtnGroup);
               _loc4_ = new TiDyBtn();
               _loc4_.x = 0;
               _loc4_.y = 228;
               addChild(_loc4_);
               _cellAllNum = XMLSingle.getInstance().getPublicStorageCellNum();
               initAreaSize(6,4,StorageEquipmentCell);
               setAreaPage(1);
               break;
            case 3:
               pageBtnGroup.x = 10;
               pageBtnGroup.y = 170;
               addChild(pageBtnGroup);
               initAreaSize(3,3,PackageEquipmentCell);
               setAreaPage(1);
               break;
            default:
               throw new Error("没有该装备区类型");
         }
      }
      
      protected function initAreaSize(param1:int, param2:int, param3:Class) : void
      {
         _widthSize = param1;
         _heightSize = param2;
         _area = new EquipmentArea(param1,param2,param3);
         _area.x = -3;
         _area.y = -10;
         addChild(_area);
      }
      
      protected function arrangArea(param1:int) : void
      {
         var _loc4_:LockCell = null;
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         _area.initArea(param1);
         if(_storageType != 3)
         {
            _loc5_ = int(_lockCells.length);
            _loc7_ = 0;
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               removeChild(_lockCells[_loc7_]);
               _lockCells[_loc7_] = null;
               _loc7_++;
            }
            _loc2_ = getChildIndex(_area);
            _loc3_ = Math.min(_widthSize * _heightSize,_cellAllNum - _widthSize * _heightSize * (pageBtnGroup.pageNum - 1));
            _lockCells = new Vector.<LockCell>();
            _loc5_ = _loc3_ - param1;
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc4_ = new LockCell();
               _lockCells.push(_loc4_);
               _loc7_++;
            }
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc6_ = _loc7_ + param1;
               _lockCells[_loc7_].x = _area.x + _loc6_ % _widthSize * _lockCells[_loc7_].width + _lockCells[_loc7_].width / 2;
               _lockCells[_loc7_].y = _area.y + int(_loc6_ / _widthSize) * _lockCells[_loc7_].height + _lockCells[_loc7_].height / 2;
               addChildAt(_lockCells[_loc7_],_loc2_);
               _loc7_++;
            }
         }
         _currentShowIndex = 0;
      }
      
      public function render() : void
      {
      }
      
      protected function setAreaPage(param1:int) : void
      {
         var _loc2_:int = 0;
         switch(_storageType)
         {
            case 0:
            case 1:
            case 2:
               if(_cellAllNum == 0)
               {
                  _loc2_ = 1;
               }
               else if(_cellAllNum % (_widthSize * _heightSize) == 0)
               {
                  _loc2_ = _cellAllNum / (_widthSize * _heightSize);
               }
               else
               {
                  _loc2_ = int(_cellAllNum / (_widthSize * _heightSize)) + 1;
               }
               pageBtnGroup.initPageNumber(param1,_loc2_);
               break;
            case 3:
               if(!_equipmentVOs.length)
               {
                  pageBtnGroup.initPageNumber(1,1);
                  return;
               }
               if(_equipmentVOs.length % (_widthSize * _heightSize) == 0)
               {
                  pageBtnGroup.initPageNumber(param1,_equipmentVOs.length / (_widthSize * _heightSize));
                  break;
               }
               pageBtnGroup.initPageNumber(param1,int(_equipmentVOs.length / (_widthSize * _heightSize)) + 1);
               break;
            default:
               throw new Error("提供的参数不符合要求！");
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("clickAKeySoldBtn",clickAKeySold,true,0,true);
         addEventListener("clickTidyBtn",clickTidyBtn,true,0,true);
         addEventListener("clickLockCell",clickLockCell,true,0,true);
         if(_moneyText)
         {
            _moneyText.addEventListener("rollOver",rollOver,false,0,true);
            _moneyText.addEventListener("rollOut",rollOut,false,0,true);
         }
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("clickAKeySoldBtn",clickAKeySold,true);
         removeEventListener("clickTidyBtn",clickTidyBtn,true);
         removeEventListener("clickLockCell",clickLockCell,true);
         removeEventListener("clickButton",clickButton,true);
      }
      
      protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if((!!_moneyShow ? _moneyShow.getShowBtn() : null) !== _loc2_)
         {
            if((!!_PKPointShow ? _PKPointShow.getShowBtn() : null) === _loc2_)
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"现有PK点：<font size=\'25\' color=\'#fcca00\'>" + _PKPoint + "</font>",
                  "flag":0
               }));
            }
         }
         else
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"现有元宝：<font size=\'25\' color=\'#fcca00\'>" + _money + "</font>",
               "flag":0
            }));
         }
      }
      
      private function rollOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":{"target":"moneyText"}}));
      }
      
      private function rollOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      protected function clickLockCell(param1:UIBtnEvent) : void
      {
         var _loc3_:int = 0;
         var _loc6_:int = _widthSize * _heightSize;
         if(_lockCells.length < _loc6_)
         {
            _loc3_ = _lockCells.indexOf(param1.target as LockCell) + 1;
         }
         else
         {
            _loc3_ = (pageBtnGroup.pageNum - 1) * _loc6_ - _equipmentVOs.length + (_lockCells.indexOf(param1.target as LockCell) + 1);
         }
         var _loc5_:String = XMLSingle.getInstance().returnLockCellTicketId(_storageType);
         var _loc4_:int = XMLSingle.getInstance().returnLockCellTicket(_storageType);
         var _loc7_:int = _loc4_ * _loc3_;
         var _loc2_:Object = {};
         _loc2_["propId"] = _loc5_;
         _loc2_["count"] = _loc3_;
         _loc2_["price"] = _loc4_;
         _loc2_["idx"] = GameData.getInstance().getSaveFileData().index;
         _loc2_["tag"] = "购买装备格";
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":"是否花费 <font size=\'25\' color=\'#ff0000\'>" + _loc7_ + "</font>点券开启所选择的<font size=\'25\' color=\'#ff0000\'>" + _loc3_ + "</font>个装备格！",
            "flag":3,
            "task":{
               "type":"buyCellTask",
               "buyDataObj":_loc2_,
               "buyNum":_loc3_,
               "equipments":_equipmentVOs
            }
         }));
      }
      
      protected function pageUp(param1:UIBtnEvent) : void
      {
         setAreaPage(pageBtnGroup.pageNum);
         arrangeEquipment((pageBtnGroup.pageNum - 1) * _widthSize * _heightSize);
      }
      
      protected function pageDown(param1:UIBtnEvent) : void
      {
         setAreaPage(pageBtnGroup.pageNum);
         arrangeEquipment((pageBtnGroup.pageNum - 1) * _widthSize * _heightSize);
      }
      
      protected function clickAKeySold(param1:UIBtnEvent) : void
      {
      }
      
      protected function clickTidyBtn(param1:UIBtnEvent) : void
      {
         dispatchEvent(new TidyEquipmentEvent("tidyEquipments"));
      }
      
      protected function cutOffTail() : void
      {
         var _loc1_:int = 0;
         _loc1_ = _equipmentVOs.length - 1;
         while(_loc1_ >= 0)
         {
            if(_equipmentVOs[_loc1_] != null)
            {
               break;
            }
            _equipmentVOs.splice(_loc1_,1);
            _loc1_--;
         }
      }
   }
}

