package UI.SocietySystem
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   
   public class SocietySystemListener implements ISocietySystemListener
   {
      public var logInSuccessFun:Function;
      
      public var getTheSocietyDataSuccessFun:Function;
      
      public var loginCompleteFun:Function;
      
      public var downGetChatInforFun:Function;
      
      public function SocietySystemListener()
      {
         super();
      }
      
      public function clear() : void
      {
         logInSuccessFun = null;
         getTheSocietyDataSuccessFun = null;
         loginCompleteFun = null;
         downGetChatInforFun = null;
      }
      
      public function logInSuccess() : void
      {
         if(Boolean(logInSuccessFun))
         {
            logInSuccessFun();
         }
      }
      
      public function getTheSocietyDataSuccess() : void
      {
         if(Boolean(getTheSocietyDataSuccessFun))
         {
            getTheSocietyDataSuccessFun();
         }
      }
      
      public function loginComplete() : void
      {
         if(Boolean(loginCompleteFun))
         {
            loginCompleteFun();
         }
      }
      
      public function downGetChatInfor(param1:DOWN_GetChatInfor) : void
      {
         if(Boolean(downGetChatInforFun))
         {
            downGetChatInforFun(param1);
         }
      }
   }
}

