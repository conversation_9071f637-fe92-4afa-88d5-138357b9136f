package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddRegHpSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addRegHp:Number;
      
      public function AddRegHpSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("regHp_mountAdd1",m_targetPlayer.playerVO.get2("regHp_mountAdd1") - m_addValue);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addRegHp;
         m_targetPlayer.playerVO.set2("regHp_mountAdd1",m_targetPlayer.playerVO.get2("regHp_mountAdd1") + m_addValue);
      }
      
      public function getAddRegHp() : Number
      {
         return addRegHp;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addRegHp = Number(param1.data.(@att == "addRegHp")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addRegHp = m_addRegHp;
      }
      
      private function get addRegHp() : Number
      {
         return _antiwear.addRegHp;
      }
      
      private function set addRegHp(param1:Number) : void
      {
         _antiwear.addRegHp = param1;
      }
   }
}

