package UI.WorldBoss.AnimationQueueData.AnimationData
{
   import YJFY.Utils.ClearUtil;
   
   public class AnimationData
   {
      private var _playEndListeners:Vector.<IPlayEndListener>;
      
      public function AnimationData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_playEndListeners,false,false,false);
         _playEndListeners = null;
      }
      
      public function addPlayEndListener(param1:IPlayEndListener) : void
      {
         if(_playEndListeners == null)
         {
            _playEndListeners = new Vector.<IPlayEndListener>();
         }
         _playEndListeners.push(param1);
      }
      
      public function removePlayEndListener(param1:IPlayEndListener) : void
      {
         if(_playEndListeners == null)
         {
            return;
         }
         var _loc2_:int = int(_playEndListeners.indexOf(param1));
         while(_loc2_ == -1)
         {
            _playEndListeners.splice(_loc2_,1);
            _loc2_ = int(_playEndListeners.indexOf(param1));
         }
      }
      
      public function playAnimation() : void
      {
      }
      
      protected function playEnd() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IPlayEndListener> = _playEndListeners;
         var _loc2_:int = !!_loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].playEnd();
            }
            _loc3_++;
         }
      }
   }
}

