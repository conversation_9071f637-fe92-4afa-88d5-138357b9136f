package UI2.Mount.MountLogic
{
   import UI2.Mount.MountData.MountVO;
   import UI2.broadcast.SubmitFunction;
   
   public class AddStrengthenNumLogic
   {
      public function AddStrengthenNumLogic()
      {
         super();
      }
      
      public function addStrengthenNum(param1:MountVO, param2:Number, param3:uint, param4:uint, param5:AddStrengthenNumReturnData) : void
      {
         var _loc7_:Number = Math.random();
         if(_loc7_ < param2)
         {
            param3 = param4;
         }
         var _loc6_:uint = param1.getLevel2();
         param1.addStrengthenNum(param3);
         param5.addNum = param3;
         param5.isUpgrade = param1.getLevel2() > _loc6_;
         if(param5.isUpgrade)
         {
            SubmitFunction.getInstance().setData3(7,param1.getId(),param1.getName(),param1.getLevel2());
         }
      }
      
      public function addStrengthenByNum(param1:MountVO, param2:Number, param3:uint, param4:uint, param5:AddStrengthenNumReturnData, param6:Number, param7:Boolean) : void
      {
         var _loc11_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:* = param3;
         var _loc8_:uint = param1.getLevel2();
         _loc11_ = 0;
         while(_loc11_ < param6)
         {
            _loc9_ = Math.random();
            if(_loc9_ < param2)
            {
               param3 = param4;
            }
            param1.addStrengthenNum(param3);
            param5.addNum += param3;
            if(param3 == param4)
            {
               param7 = true;
               param3 = _loc10_;
            }
            _loc11_++;
         }
         param5.isUpgrade = param1.getLevel2() > _loc8_;
         if(param5.isUpgrade)
         {
            SubmitFunction.getInstance().setData3(7,param1.getId(),param1.getName(),param1.getLevel2());
         }
      }
   }
}

