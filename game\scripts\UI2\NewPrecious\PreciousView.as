package UI2.NewPrecious
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.LoadUI2;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.HaveLockSwitchBtnLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.Font;
   import flash.text.TextField;
   
   public class PreciousView extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_takein:ButtonLogicShell2;
      
      private var m_pagedown:ButtonLogicShell2;
      
      private var m_pageup:ButtonLogicShell2;
      
      private var m_itemlist:Vector.<MovieClip>;
      
      private var m_namelist:Vector.<MovieClip>;
      
      private var m_fuBenDataShows:Vector.<PreciousDataShow>;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_rewardEquipments:Vector.<Equipment>;
      
      private var m_fuBenbtnDataShowSwitchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_data:PreDataInfo;
      
      private var m_currData:InfoData;
      
      private var m_txtinfo:TextField;
      
      private var m_xmlpath:String = null;
      
      private var m_index:int = 0;
      
      private var m_num:int = 3;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_gamingUI:GamingUI;
      
      private var m_myLoader:YJFYLoader;
      
      private var font:Font;
      
      private var m_serverTime:String;
      
      public function PreciousView()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_itemlist = new Vector.<MovieClip>();
         m_pagedown = new ButtonLogicShell2();
         m_pageup = new ButtonLogicShell2();
         m_takein = new ButtonLogicShell2();
         m_namelist = new Vector.<MovieClip>();
         m_eqCells = new Vector.<Sprite>();
         font = new FangZhengKaTongJianTi();
         m_rewardEquipments = new Vector.<Equipment>();
         m_fuBenbtnDataShowSwitchBtnGroup = new SwitchBtnGroupLogicShell(HaveLockSwitchBtnLogicShell);
         m_fuBenDataShows = new Vector.<PreciousDataShow>();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(font);
         font = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_takein);
         m_takein = null;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         ClearUtil.clearObject(m_namelist);
         m_namelist = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_fuBenbtnDataShowSwitchBtnGroup);
         m_fuBenbtnDataShowSwitchBtnGroup = null;
         _loc1_ = 0;
         while(_loc1_ < m_fuBenDataShows.length)
         {
            ClearUtil.clearObject(m_fuBenDataShows[_loc1_]);
            m_fuBenDataShows[_loc1_] = null;
            _loc1_++;
         }
         ClearUtil.clearObject(m_fuBenDataShows);
         m_fuBenDataShows = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(m_pagedown);
         m_pagedown = null;
         ClearUtil.clearObject(m_pageup);
         m_pageup = null;
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         super.clear();
      }
      
      public function init(param1:IVersionControl, param2:IProgressShow, param3:GamingUI) : void
      {
         var versionControl:IVersionControl = param1;
         var loadUI:IProgressShow = param2;
         var gamingUI:GamingUI = param3;
         m_versionControl = versionControl;
         m_loadUI = loadUI;
         m_gamingUI = gamingUI;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_serverTime = param1;
            init2();
         },fail,true);
      }
      
      private function init2() : void
      {
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         (m_loadUI as LoadUI2).tranToTransparentcy();
         m_myLoader.getXML("NewGameFolder/LevelMode3/lingge.xml",getConfigSuccess,getFail);
         m_myLoader.getClass("precious.swf","LingFu",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getConfigSuccess(param1:YJFYLoaderData) : void
      {
         PreDataInfo.getInstance().initXML(param1.resultXML);
      }
      
      private function fail(param1:String) : void
      {
         if(param1)
         {
            m_gamingUI.showMessageTip(param1);
         }
         m_gamingUI.closePreciousView();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         fail("资源加载失败");
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         m_show = new param1.resultClass();
         addChild(m_show);
         initShow();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:XML = param1.resultXML;
      }
      
      private function initShow() : void
      {
         var _loc2_:PreciousDataShow = null;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         m_quitBtn.setShow(m_show["closebtn"]);
         m_quitBtn.setTipString("点击关闭");
         m_pagedown.setShow(m_show["nextpage"]);
         m_pagedown.setTipString("下一页");
         m_pageup.setShow(m_show["prepage"]);
         m_pageup.setTipString("上一页");
         m_takein.setShow(m_show["takein"]);
         m_takein.setTipString("点击进入挑战");
         m_txtinfo = m_show["txtinfo"] as TextField;
         MyFunction2.changeTextFieldFont(font.fontName,m_txtinfo);
         m_txtinfo.text = "";
         _loc3_ = 0;
         while(_loc3_ < 3)
         {
            m_itemlist.push(m_show["item_" + (_loc3_ + 1)]);
            _loc2_ = new PreciousDataShow();
            _loc2_.setShow(m_itemlist[_loc3_]["fuBenSwitchBtn"]);
            m_fuBenDataShows.push(_loc2_);
            m_fuBenbtnDataShowSwitchBtnGroup.addSwitchBtn(_loc2_);
            _loc3_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 9)
         {
            m_eqCells.push(m_show["equip_" + (_loc1_ + 1)]);
            _loc1_++;
         }
         PreDataInfo.getInstance().refreshdata(m_serverTime);
         refresh();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(m_fuBenDataShows.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_fuBenDataShows[_loc3_])
            {
               refresh();
               showPrecious();
            }
            _loc3_++;
         }
         switch(param1.button)
         {
            case m_quitBtn:
               m_gamingUI.closePreciousView();
               break;
            case m_pagedown:
               nextpage();
               break;
            case m_pageup:
               prepage();
               break;
            case m_takein:
               fight();
         }
      }
      
      private function fight() : void
      {
         if(m_currData == null)
         {
            GamingUI.getInstance().showMessageTip("请选择关卡!");
            return;
         }
         if(GamingUI.getInstance().player1.playerVO.level < m_currData.needLevel)
         {
            GamingUI.getInstance().showMessageTip(String(m_currData.needLevel) + "级才可挑战");
            return;
         }
         if(GamingUI.getInstance().player2 != null && GamingUI.getInstance().player2.playerVO.level < m_currData.needLevel)
         {
            GamingUI.getInstance().showMessageTip(String(m_currData.needLevel) + "级才可挑战");
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            PreDataInfo.getInstance().refreshdata(param1);
            if(PreDataInfo.getInstance().getNumByIndex(m_currData.id) <= 0)
            {
               GamingUI.getInstance().showMessageTip("挑战次数不足");
               return;
            }
            if(m_xmlpath && PreDataInfo.getInstance().getNumByIndex(m_currData.id) > 0)
            {
               PreDataInfo.getInstance().setCurrId(m_currData.id);
               PreDataInfo.getInstance().setNumByIndex(m_currData.id);
               GamingUI.getInstance().closePreciousView();
               Part1.getInstance().closeCityMap();
               if(m_xmlpath == "NewGameFolder/LevelMode3/Level31.xml")
               {
                  Part1.getInstance().openLevelNew(m_xmlpath);
               }
               else if(m_xmlpath == "NewGameFolder/LevelMode3/Level32.xml")
               {
                  Part1.getInstance().openAnyeLevel(m_xmlpath);
               }
               else if(m_xmlpath == "NewGameFolder/LevelMode3/Level33.xml")
               {
                  Part1.getInstance().openEndDancerLevel(m_xmlpath);
               }
            }
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      private function showPrecious() : void
      {
         var _loc5_:int = 0;
         var _loc3_:Equipment = null;
         var _loc2_:PreciousDataShow = m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as PreciousDataShow;
         m_currData = _loc2_.getShowData();
         m_txtinfo.text = _loc2_.getShowData().description;
         m_xmlpath = _loc2_.getShowData().levelXMLPath;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments.length = 0;
         var _loc4_:int = 0;
         if(_loc2_.getShowData().equiplist)
         {
            _loc4_ = int(_loc2_.getShowData().equiplist.length);
         }
         else
         {
            _loc4_ = 0;
         }
         var _loc1_:int = Math.min(m_eqCells.length,_loc4_);
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(_loc2_.getShowData().getItemByIndex(_loc5_).clone());
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_rewardEquipments.push(_loc3_);
            m_eqCells[_loc5_].addChild(_loc3_);
            _loc5_++;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function nextpage() : void
      {
         if(PreDataInfo.getInstance().getAmount() > m_index + 3)
         {
            m_index += 3;
            refresh();
         }
      }
      
      private function prepage() : void
      {
         if(m_index - 3 >= 0)
         {
            m_index -= 3;
            refresh();
         }
      }
      
      private function refresh() : void
      {
         var _loc2_:InfoData = null;
         var _loc3_:int = 0;
         var _loc1_:int = m_index;
         _loc3_ = 0;
         while(_loc3_ < 3)
         {
            _loc2_ = PreDataInfo.getInstance().getDataByIndex(_loc1_);
            if(_loc2_)
            {
               if(GamingUI.getInstance().player1.playerVO.level >= _loc2_.needLevel)
               {
                  m_fuBenDataShows[_loc3_].setData(_loc2_);
                  m_fuBenDataShows[_loc3_].unLock();
               }
               else
               {
                  m_fuBenDataShows[_loc3_].setData(_loc2_);
                  m_fuBenDataShows[_loc3_].showNeedLv(_loc2_.needLevel);
               }
            }
            else
            {
               m_fuBenDataShows[_loc3_].showNoOpen();
               m_fuBenDataShows[_loc3_].lock();
            }
            _loc1_++;
            _loc3_++;
         }
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

