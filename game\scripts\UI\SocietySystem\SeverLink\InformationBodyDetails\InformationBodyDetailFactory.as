package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class InformationBodyDetailFactory
   {
      private var m_datasOfIdAndClass:Object;
      
      public function InformationBodyDetailFactory()
      {
         super();
         m_datasOfIdAndClass = {};
         m_datasOfIdAndClass["3045"] = DOWN_GetLogicServerReturn;
         m_datasOfIdAndClass["3028"] = DOWN_AddConValueReturn;
         m_datasOfIdAndClass["3023"] = DOWN_CreateSocietySuccess;
         m_datasOfIdAndClass["3030"] = DOWN_DecConValueReturn;
         m_datasOfIdAndClass["3026"] = DOWN_DissolveTheSociety_Broadcast;
         m_datasOfIdAndClass["3025"] = DOWN_DissolveSocietyReturn;
         m_datasOfIdAndClass["3043"] = DOWN_GetChatInfor;
         m_datasOfIdAndClass["3039"] = DOWN_CancelDissolveSocietyReturn;
         m_datasOfIdAndClass["3014"] = DOWN_HavePlayerApplyForJoin;
         m_datasOfIdAndClass["3016"] = DOWN_IsSuccessJoin;
         m_datasOfIdAndClass["3011"] = DOWN_LeaveTheSocietySuccess;
         m_datasOfIdAndClass["3001"] = DOWN_LogInSuccess;
         m_datasOfIdAndClass["3108"] = DOWN_LogOut;
         m_datasOfIdAndClass["3020"] = DOWN_PlayerListOfApply;
         m_datasOfIdAndClass["3007"] = DOWN_PlayerTheSocietyData;
         m_datasOfIdAndClass["3037"] = DOWN_TheSocietyListOfApplyReturn;
         m_datasOfIdAndClass["3005"] = DOWN_TheSocietyList;
         m_datasOfIdAndClass["3013"] = DOWN_ApplyJoinReturn;
         m_datasOfIdAndClass["3018"] = DOWN_CancelApplyJoinReturn;
         m_datasOfIdAndClass["3032"] = DOWN_ChangeAnnouncementReturn;
         m_datasOfIdAndClass["3021"] = DOWN_HaveNewMember;
         m_datasOfIdAndClass["3034"] = DOWN_RemoveMemberReturnToLeader;
         m_datasOfIdAndClass["3035"] = DOWN_RemoveMemberReturnToMember;
         m_datasOfIdAndClass["3009"] = DOWN_TheSocietyMemberList;
         m_datasOfIdAndClass["3041"] = DOWN_UpDatePlayerDataReturn;
         m_datasOfIdAndClass["2001"] = DOWN_VerifyReturn;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_datasOfIdAndClass);
         m_datasOfIdAndClass = null;
      }
      
      public function createById(param1:int) : InformationBodyDetail
      {
         var _loc3_:* = null;
         var _loc2_:Class = m_datasOfIdAndClass["" + param1];
         trace("create informationBodyDetail    id:" + param1 + "cla:" + _loc2_ + "\n");
         return new _loc2_();
      }
      
      public function createByIdAndByteArray(param1:int, param2:ByteArray) : InformationBodyDetail
      {
         var _loc3_:InformationBodyDetail = null;
         _loc3_ = createById(param1);
         _loc3_.initFromByteArray(param2);
         return _loc3_;
      }
   }
}

