package UI.WorldBoss.Boss.CreateOwnImageBoss
{
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.Boss.Boss;
   
   public class CreateOwnImageAniData extends AnimationData
   {
      public var creator:CreateOwnImageBoss;
      
      public var createImages:Vector.<Boss>;
      
      public function CreateOwnImageAniData()
      {
         super();
      }
      
      override public function playAnimation() : void
      {
         super.playAnimation();
         creator.createOwnImageAnimation(createImages,playEnd);
      }
   }
}

