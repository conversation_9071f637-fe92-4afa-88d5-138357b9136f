package UI
{
   import GM_UI.GMData;
   import UI.ActivityPanel.ActivityPanel;
   import UI.AutomaticPetPanel.AutomaticPetPanel;
   import UI.AutomaticPetPanel.AutomaticPetScoreRankList;
   import UI.Button.QuitBtn;
   import UI.CheatData.CheatData;
   import UI.CollectTimePanel.CollectTimePanel;
   import UI.DetectionClass.DetectionClass;
   import UI.DetectionClass.DetectionClass3;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.PotionEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.ExchangeEquipment.ExchangeWorld;
   import UI.ExchangeGiftBag.ExchangeGiftBagPanel;
   import UI.ExternalUI.ExternalPanel;
   import UI.Farm.Farm;
   import UI.HonourHallPanel.HonourHallPanel;
   import UI.MainLineTask.MainLineTaskData;
   import UI.MainLineTask.MainLineTaskPanel;
   import UI.MiragePanel.MiragePanel;
   import UI.NicknameSystem.NicknameData;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import UI.OnLineGiftBag.OnLineGiftBagPanel;
   import UI.OpenNpcSelectBox.OpenNpcListener1;
   import UI.OpenNpcSelectBox.OpenNpcSelectBox;
   import UI.Other.QuickenChargeBox;
   import UI.Other.RechargePanel;
   import UI.PKUI.PKPanel;
   import UI.PKUI.PKRankListPanel.OnePlayerAllPKRankListPanel;
   import UI.PKUI.PKRankListPanel.TwoPlayerWeekPkRankListPanel;
   import UI.PKUI.PlayerDataForPK;
   import UI.PKUI.RankListPanel;
   import UI.PackageAndStorage.StorageNameBitmapdata;
   import UI.PetAdvancePanel.PetAdvancePanel;
   import UI.Players.Player;
   import UI.Players.VipVO;
   import UI.Protect.ProtectData;
   import UI.Protect.ProtectPanel;
   import UI.RecaptureGold.RankList.RecaptureGoldRankList;
   import UI.RecaptureGold.RecaptureGoldMain;
   import UI.RecaptureGold.RewardPanel.RewardPanel;
   import UI.RefineFactory.RefineFactory;
   import UI.ShiTu.ShiTuPanel;
   import UI.Shop.ShopNameBitmapdata;
   import UI.Shop.ShopOne;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.ShopWall.ShopWall;
   import UI.SignPanel.SignPanel;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.SocietySystem.ExSocietyPanel.ExSocietyPanel;
   import UI.SocietySystem.MySocietyPanel.MySocietyPanel;
   import UI.SocietySystem.SocietyListPanel.SocietyListPanel;
   import UI.SocietySystem.SocietySystem;
   import UI.SocietySystem.SocietySystemListener;
   import UI.Task.TaskFunction;
   import UI.TaskPanel.ExTaskPanel;
   import UI.TextTrace.TextTrace;
   import UI.UIInterface.IActiveSkillVO;
   import UI.Utils.GetServerTime.GetServerTime;
   import UI.Utils.LoadQueue.LoadQueue;
   import UI.Utils.LoadXML.SharedXMLs;
   import UI.VIPPanel.VIPPanel;
   import UI.WorldBoss.Utils.GetWorldBossData;
   import UI.WorldBoss.Utils.WorldBossFun;
   import UI.WorldBoss.Utils.WorldBossNeedSomeData;
   import UI.WorldBoss.WorldBossPanel;
   import UI.WorldBoss.WorldBossRankListPanel;
   import UI.WorldBoss.WorldBossSaveData;
   import UI.XiangMoLevelPanel.XiangMoLevelPanel;
   import UI.newShop.NewShop;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import UI.newTask.NewTaskPanel;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.BBSPanel;
   import UI2.Consumer.ConsumerView;
   import UI2.HLPanel;
   import UI2.LiuyiPosterPanle;
   import UI2.Midautumn.MidautumnData;
   import UI2.Midautumn.MidautumnPanel;
   import UI2.MobileGiftsPanel;
   import UI2.Mount.MountUI.MountsPanel;
   import UI2.NewPrecious.PreciousView;
   import UI2.NewRank.NewAllRank;
   import UI2.NewRank.RankDataInfo;
   import UI2.NewRank.RankWorld;
   import UI2.OnePlayerToTwo.OnePlayerToTwoPanel;
   import UI2.ResetPlayerType.ResetPlayerTypePanel;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SVActivity.Show.SVActivityPanel;
   import UI2.SmallAssistant.SmallAssistant;
   import UI2.SmallAssistant.SmallAssistantSaveData;
   import UI2.WuYiPosterPanel;
   import UI2.ZhouNianActivity.ZhounianActivityPanel;
   import UI2.broadcast.GetDataFunction;
   import UI2.buchang.BuchangPanel;
   import UI2.codekey.CodekeyGiftBagPanel;
   import UI2.codekey.CodekeyZhuoquGiftBagPanel;
   import UI2.doubleegg.DoubleEggPanel;
   import UI2.firstPay.FirstPayPanel;
   import UI2.jhsbPanel.JhsbView;
   import UI2.newSign.NewSignPanel;
   import UI2.realname.RealNamePanel;
   import UI2.weekpay.WeekPayPanel;
   import UI2.wuyi.WuyiPanel;
   import YJFY.API_4399.API_4399;
   import YJFY.API_4399.PayAPI.GetPaiedManager;
   import YJFY.API_4399.PayAPI.GetRechargeManager;
   import YJFY.API_4399.PayAPI.RequireInfo;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.AutomaticPet.SubmitAutomaticPetScore;
   import YJFY.BossMode.BossUIAndData.BossChooceUI;
   import YJFY.BossMode.PetBossUIAndData.PetBossChooceUI;
   import YJFY.EndlessMode.EndLessMapUI;
   import YJFY.EndlessMode.EndlessManage;
   import YJFY.EndlessMode.EndlessRankPanel;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.LoadPart;
   import YJFY.PKMode2.PKMode2Data;
   import YJFY.PKMode2.PKModeRankList1;
   import YJFY.Part1;
   import YJFY.ToolTip.MessageTip;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VerifyUserState.VerifyUserState;
   import flash.display.MovieClip;
   import flash.display.Stage;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.system.System;
   import unit4399.events.PayEvent;
   import unit4399.events.ShopEvent;
   
   public class GamingUI extends MySprite
   {
      private static var _instance:GamingUI = null;
      
      public var manBan:DoPayMask;
      
      private var _internalPanel:MyControlPanel;
      
      private var _externalPanel:ExternalPanel;
      
      private var _VIPPanel:VIPPanel;
      
      private var _exTaskPanel:ExTaskPanel;
      
      private var _player1:Player;
      
      private var _player2:Player;
      
      private var _publicStorageEquipments:Vector.<EquipmentVO>;
      
      private var _shop:ShopOne;
      
      private var _shopWall:ShopWall;
      
      private var _miragePanel:MiragePanel;
      
      private var _rankListPanel:RankListPanel;
      
      private var _rankworld:RankWorld;
      
      private var _farm:Farm;
      
      private var _refineFactory:RefineFactory;
      
      private var _protectPanel:ProtectPanel;
      
      private var _exchangeGiftBagPanel:ExchangeGiftBagPanel;
      
      private var _codekeyGiftBagPanel:CodekeyGiftBagPanel;
      
      private var _bbsPanel:BBSPanel;
      
      private var _hlPanel:HLPanel;
      
      private var _midautumn:MidautumnPanel;
      
      private var _doubleEggPanel:DoubleEggPanel;
      
      private var _zhuoquGiftPanel:CodekeyZhuoquGiftBagPanel;
      
      private var _realNamePanel:RealNamePanel;
      
      private var _jhsbPanel:JhsbView;
      
      private var _wuyiPoster:WuYiPosterPanel;
      
      private var _liuyiPoster:LiuyiPosterPanle;
      
      private var _firstPayPanel:FirstPayPanel;
      
      private var _weekPayPanel:WeekPayPanel;
      
      private var _BuchangPanel:BuchangPanel;
      
      private var _WuyiPanel:WuyiPanel;
      
      private var _consumerPanel:ConsumerView;
      
      private var _newShop:NewShop;
      
      private var _newSign:NewSignPanel;
      
      private var _mobileGift:MobileGiftsPanel;
      
      private var _signPanel:SignPanel;
      
      private var _honourHallPanel:HonourHallPanel;
      
      private var _recaptureGoldGame:RecaptureGoldMain;
      
      private var _recaptureGoldRewardPanel:RewardPanel;
      
      private var _exchangeWorld:ExchangeWorld;
      
      private var _shiTuPanel:ShiTuPanel;
      
      private var _petAdvancePanel:PetAdvancePanel;
      
      private var _onLineGiftBagPanel:OnLineGiftBagPanel;
      
      private var _mainLineTaskData:MainLineTaskData;
      
      private var _mainLineTaskPanel:MainLineTaskPanel;
      
      private var _loadQueue:LoadQueue;
      
      private var _rechargePanel:RechargePanel;
      
      private var _worldBossPanel:WorldBossPanel;
      
      private var _activityPanel:ActivityPanel;
      
      private var _collectTimePanel:CollectTimePanel;
      
      private var _getServerTime:GetServerTime;
      
      private var _sharedXMLs:SharedXMLs;
      
      private var _changeFrameRatePanel:ChangeFrameRatePanel;
      
      private var _openNpcSelectBox:OpenNpcSelectBox;
      
      private var _xiangMoLevelPanel:XiangMoLevelPanel;
      
      private var _preciousPanel:PreciousView;
      
      private var _newtaskPanel:NewTaskPanel;
      
      private var _automaticPetPanel:AutomaticPetPanel;
      
      private var _svActivityPanel:SVActivityPanel;
      
      private var _rankWorldPanel:RankWorld;
      
      private var _resetPlayerTypePanel:ResetPlayerTypePanel;
      
      private var _onePlayertoTwoPanel:OnePlayerToTwoPanel;
      
      private var _EndlessLevelPanel:EndLessMapUI;
      
      private var _EndlessRankPanel:EndlessRankPanel;
      
      private var _NewAllRank:NewAllRank;
      
      private var _mountsPanel:MountsPanel;
      
      private var _zhounianPanel:ZhounianActivityPanel;
      
      private var _societySystem:SocietySystem;
      
      private var _societySystemListener:SocietySystemListener;
      
      private var _societyListPanel:SocietyListPanel;
      
      private var _mySocietyPanel:MySocietyPanel;
      
      private var _exSocietyPanel:ExSocietyPanel;
      
      private var _bossChooseUI:BossChooceUI;
      
      private var _petBossChooseUI:PetBossChooceUI;
      
      private var _automaticPetsData:AutomaticPetsData;
      
      private var _smallAssistantSaveData:SmallAssistantSaveData;
      
      private var _smallAssistant:SmallAssistant;
      
      private var _svActivitySaveData:SVActivitySaveData;
      
      private var _messageTipShow:MovieClip;
      
      private var _messageTip:MessageTip;
      
      private var _submitAutomaticPetScore:SubmitAutomaticPetScore;
      
      private var _verifyUserState:VerifyUserState;
      
      private var _detectionClass3:DetectionClass3;
      
      private var _refreshTaskflag:int;
      
      private var _linBtn:LinShiBtn;
      
      private var _textTrace:TextTrace;
      
      private var _isCanUseShopWall:Boolean;
      
      private var _intervalID:uint = 0;
      
      private var _newestTimeStrFromServer:String;
      
      public function GamingUI()
      {
         super();
         if(!_instance)
         {
            if(GMData.getInstance().isGMApplication == false)
            {
               _linBtn = new LinShiBtn();
               _linBtn.addEventListener("click",openExTaskPanel,false,0,true);
               _linBtn.x = 736;
               _linBtn.y = 538;
               if(getVersionControl().getLineMode().getLineMode() == "offLine")
               {
                  addChild(_linBtn);
               }
            }
            _loadQueue = new LoadQueue();
            _getServerTime = new GetServerTime();
            _sharedXMLs = new SharedXMLs();
            _instance = this;
            addEventListener("addedToStage",addToStage,false,0,true);
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在么？！");
      }
      
      public static function getInstance() : GamingUI
      {
         if(_instance == null)
         {
            _instance = new GamingUI();
         }
         return _instance;
      }
      
      public function init() : void
      {
         _verifyUserState = new VerifyUserState();
         _verifyUserState.setSaveAPI(getAPI4399().saveAPI);
         _verifyUserState.setUserData(new UserData());
         _verifyUserState.setVersionControl(getVersionControl());
         _submitAutomaticPetScore = new SubmitAutomaticPetScore();
         _submitAutomaticPetScore.init(getAPI4399().rankListAPI);
      }
      
      private function openExTaskPanel(param1:MouseEvent) : void
      {
         GamingUI.getInstance().openShopWall();
      }
      
      public function get textTrace() : TextTrace
      {
         return _textTrace;
      }
      
      public function get player1() : Player
      {
         return _player1;
      }
      
      public function set player1(param1:Player) : void
      {
         _player1 = param1;
      }
      
      public function get player2() : Player
      {
         return _player2;
      }
      
      public function set player2(param1:Player) : void
      {
         _player2 = param1;
      }
      
      public function initUI(param1:Player = null, param2:Player = null, param3:Vector.<EquipmentVO> = null, param4:AutomaticPetsData = null, param5:SmallAssistantSaveData = null, param6:SVActivitySaveData = null, param7:String = null, param8:DetectionClass3 = null) : void
      {
         _detectionClass3 = param8;
         _automaticPetsData = param4;
         _smallAssistantSaveData = param5;
         _svActivitySaveData = param6;
         _externalPanel = new ExternalPanel();
         addChild(_externalPanel);
         _player1 = param1;
         _player2 = param2;
         _publicStorageEquipments = param3;
         _externalPanel.initPanel(param1,param2,param7);
         addChild(PKPanel.getInstance());
         _externalPanel.middlePanel.shopBtn.isLock = true;
         var _loc9_:PayMoneyVar = PayMoneyVar.getInstance();
         var _loc11_:RequireInfo = new RequireInfo();
         _loc11_.typeid = "openrecharge";
         _loc11_.dateinfo = null;
         _loc11_.failFun = null;
         _loc11_.successFun = null;
         AnalogServiceHoldFunction.getInstance().getTotalRechargedFun2(_loc11_,null,null,null,null);
         AnalogServiceHoldFunction.getInstance().getBalance();
         if(!param1)
         {
            throw new Error("第一角色不能为空！");
         }
         _smallAssistant = new SmallAssistant();
         _smallAssistant.init(_externalPanel.middlePanel,_smallAssistantSaveData,XMLSingle.getInstance().smallAssistantXML,this);
         refresh(639168);
         manBan = new DoPayMask();
         if(_isCanUseShopWall)
         {
            _externalPanel.middlePanel.shopBtn.isLock = false;
         }
      }
      
      public function resetPlayerType() : void
      {
         _externalPanel.resetPlayerType();
         refresh(639168);
      }
      
      public function get internalPanel() : MyControlPanel
      {
         return _internalPanel;
      }
      
      public function get publicStorageEquipmentVOs() : Vector.<EquipmentVO>
      {
         return _publicStorageEquipments;
      }
      
      public function set publicStorageEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _publicStorageEquipments = param1;
      }
      
      public function get externalPanel() : ExternalPanel
      {
         return _externalPanel;
      }
      
      public function get exTaskPanel() : ExTaskPanel
      {
         return _exTaskPanel;
      }
      
      public function get protectPanel() : ProtectPanel
      {
         return _protectPanel;
      }
      
      public function refresh(param1:int) : void
      {
         _refreshTaskflag |= param1;
      }
      
      public function filterRefresh(param1:int) : void
      {
         _refreshTaskflag = MyFunction2.filterBit(_refreshTaskflag,param1);
      }
      
      private function callRefresh() : void
      {
         if(_refreshTaskflag == 0)
         {
            return;
         }
         refreshPlayer(_refreshTaskflag);
         if(_internalPanel)
         {
            _internalPanel.refresh(_refreshTaskflag);
         }
         if(_externalPanel)
         {
            if(_externalPanel.buffPanel)
            {
               _externalPanel.buffPanel.refreshBuff(_refreshTaskflag);
            }
            if(_externalPanel.player1Panel)
            {
               _externalPanel.player1Panel.refresh(_refreshTaskflag);
            }
            if(_externalPanel.player2Panel)
            {
               _externalPanel.player2Panel.refresh(_refreshTaskflag);
            }
         }
         if(_refreshTaskflag & 8192)
         {
            if(_internalPanel)
            {
               _internalPanel.initPublicStorage(_publicStorageEquipments);
            }
         }
         _refreshTaskflag = 0;
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         removeEventListener("refreshAtt",refreshUI,true);
         removeEventListener("deleItem",deleItem,false);
         removeEventListener("clickQuitBtn",quit,true);
         removeEventListener("enterIntoPackage",enterToPackage,true);
         removeEventListener("enterIntoPet",enterToPet,true);
         removeEventListener("enterIntoProtect",enterToProtect,true);
         removeEventListener("enterIntoShop",enterToShowWall,true);
         removeEventListener("enterIntoExchangePanel",enterToExchangePanel,true);
         removeEventListener("enterIntoCodekeyPanel",enterToCodekeyPanel,true);
         removeEventListener("enterIntoBBSPanel",enterToBBSPanel,true);
         removeEventListener("enterinfohuanlezp",enterToHuanlePanel,true);
         removeEventListener("enterinfomidautumn",enterToMidautumnPanel,true);
         removeEventListener("enterinfodoubleegg",enterToDoubleEggPanel,true);
         removeEventListener("enterinfozhuoqugift",enterToZhuoquGiftPanel,true);
         removeEventListener("enterinforealname",enterToRealNamePanel,true);
         removeEventListener("enterinfojhsbp",enterToJhsbPanel,true);
         removeEventListener("enterIntoZhouNianPanel",zhouNianQing,true);
         removeEventListener("enterIntoSignPanel",enterToSignPanel,true);
         removeEventListener(" enterIntoFarm",enterToFarm,true);
         removeEventListener("enterIntoRecaptureGold",enterToRecaptureGold,true);
         removeEventListener("addGifts",addGifts,true);
         removeEventListener("getMinHunTangNum",getMinHunTangNum,false);
         removeEventListener("cutOneMinHunTang",cutOneMinHunTang,false);
         removeEventListener("clickRefineFactoryBtn",openRefineFactory,true);
         Part1.getInstance().stage.removeEventListener("logsuccess",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("usePayApi",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("incMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("decMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("getMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("payMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("paiedMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("rechargedMoney",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("payError",onPayEventHandler,false);
         Part1.getInstance().stage.removeEventListener("StoreStateEvent",getStoreStateHandler,false);
         Part1.getInstance().stage.removeEventListener("shopErrorNd",onShopEventHandler);
         Part1.getInstance().stage.removeEventListener("shopBuyNd",onShopEventHandler);
         Part1.getInstance().stage.removeEventListener("shopGetList",onShopEventHandler);
         if(_internalPanel)
         {
            _internalPanel.clear();
         }
         if(_externalPanel)
         {
            _externalPanel.clear();
         }
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         manBan = null;
         _internalPanel = null;
         _externalPanel = null;
         if(_VIPPanel)
         {
            _VIPPanel.clear();
         }
         _VIPPanel = null;
         if(_exTaskPanel)
         {
            _exTaskPanel.clear();
         }
         _exTaskPanel = null;
         if(_player1)
         {
            _player1.clear();
         }
         _player1 = null;
         if(_player2)
         {
            _player2.clear();
         }
         _player2 = null;
         if(_publicStorageEquipments)
         {
            _loc1_ = int(_publicStorageEquipments.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_publicStorageEquipments[_loc4_])
               {
                  (_publicStorageEquipments[_loc4_] as EquipmentVO).clear();
                  _publicStorageEquipments[_loc4_] = null;
               }
               _loc4_++;
            }
         }
         _publicStorageEquipments = null;
         if(_shop)
         {
            _shop.clear();
         }
         _shop = null;
         if(_shopWall)
         {
            _shopWall.clear();
         }
         _shopWall = null;
         if(_miragePanel)
         {
            _miragePanel.clear();
         }
         _miragePanel = null;
         if(_rankListPanel)
         {
            _rankListPanel.clear();
         }
         _rankListPanel = null;
         if(_farm)
         {
            _farm.clear();
         }
         _farm = null;
         if(_refineFactory)
         {
            _refineFactory.clear();
         }
         _refineFactory = null;
         if(_protectPanel)
         {
            _protectPanel.clear();
         }
         _protectPanel = null;
         if(_exchangeGiftBagPanel)
         {
            _exchangeGiftBagPanel.clear();
         }
         _exchangeGiftBagPanel = null;
         if(_codekeyGiftBagPanel)
         {
            _codekeyGiftBagPanel.clear();
         }
         _codekeyGiftBagPanel = null;
         if(_bbsPanel)
         {
            _bbsPanel.clear();
         }
         _bbsPanel = null;
         if(_hlPanel)
         {
            _hlPanel.CloseUI();
         }
         _hlPanel = null;
         if(_midautumn)
         {
            _midautumn.CloseUI();
         }
         _midautumn = null;
         if(_doubleEggPanel)
         {
            _doubleEggPanel.CloseUI();
         }
         _doubleEggPanel = null;
         if(_zhuoquGiftPanel)
         {
            _zhuoquGiftPanel.clear();
         }
         _zhuoquGiftPanel = null;
         if(_jhsbPanel)
         {
            _jhsbPanel.clear();
         }
         _jhsbPanel = null;
         if(_wuyiPoster)
         {
            _wuyiPoster.clear();
         }
         _wuyiPoster = null;
         if(_firstPayPanel)
         {
            _firstPayPanel.clear();
         }
         _firstPayPanel = null;
         if(_weekPayPanel)
         {
            _weekPayPanel.clear();
         }
         _weekPayPanel = null;
         if(_BuchangPanel)
         {
            _BuchangPanel.clear();
         }
         _BuchangPanel = null;
         if(_WuyiPanel)
         {
            _WuyiPanel.clear();
         }
         _WuyiPanel = null;
         if(_consumerPanel)
         {
            _consumerPanel.clear();
         }
         _consumerPanel = null;
         if(_newShop)
         {
            _newShop.clear();
         }
         _newShop = null;
         if(_newShop)
         {
            _newSign.clear();
         }
         _newSign = null;
         if(_mobileGift)
         {
            _mobileGift.clear();
         }
         _mobileGift = null;
         if(_signPanel)
         {
            _signPanel.clear();
         }
         _signPanel = null;
         if(_honourHallPanel)
         {
            _honourHallPanel.clear();
         }
         _honourHallPanel = null;
         if(_recaptureGoldGame)
         {
            _recaptureGoldGame.clear();
         }
         _recaptureGoldGame = null;
         if(_recaptureGoldRewardPanel)
         {
            _recaptureGoldRewardPanel.clear();
         }
         _recaptureGoldRewardPanel = null;
         if(_exchangeWorld)
         {
            _exchangeWorld.clear();
         }
         _exchangeWorld = null;
         if(_shiTuPanel)
         {
            _shiTuPanel.clear();
         }
         _shiTuPanel = null;
         if(_petAdvancePanel)
         {
            _petAdvancePanel.clear();
         }
         _petAdvancePanel = null;
         if(_onLineGiftBagPanel)
         {
            ClearUtil.clearObject(_onLineGiftBagPanel);
         }
         _onLineGiftBagPanel = null;
         OnLineGiftBagData.getInstance().clear();
         if(_mainLineTaskData)
         {
            _mainLineTaskData.clear();
         }
         _mainLineTaskData = null;
         if(_EndlessRankPanel)
         {
            _EndlessRankPanel.clear();
         }
         _EndlessRankPanel = null;
         if(_mainLineTaskPanel)
         {
            ClearUtil.clearObject(_mainLineTaskPanel);
         }
         _mainLineTaskPanel = null;
         if(_loadQueue)
         {
            ClearUtil.clearObject(_loadQueue);
         }
         _loadQueue = null;
         if(_getServerTime)
         {
            ClearUtil.clearObject(_getServerTime);
         }
         _getServerTime = null;
         if(_sharedXMLs)
         {
            ClearUtil.clearObject(_sharedXMLs);
         }
         _sharedXMLs = null;
         if(_worldBossPanel)
         {
            ClearUtil.clearObject(_worldBossPanel);
         }
         _worldBossPanel = null;
         if(_changeFrameRatePanel)
         {
            ClearUtil.clearObject(_changeFrameRatePanel);
         }
         _changeFrameRatePanel = null;
         if(_activityPanel)
         {
            ClearUtil.clearObject(_activityPanel);
         }
         _activityPanel = null;
         if(_collectTimePanel)
         {
            ClearUtil.clearObject(_collectTimePanel);
         }
         _collectTimePanel = null;
         if(_verifyUserState)
         {
            ClearUtil.clearObject(_verifyUserState);
         }
         _verifyUserState = null;
         if(_societySystem)
         {
            ClearUtil.clearObject(_societySystem);
         }
         _societySystem = null;
         if(_societySystemListener)
         {
            ClearUtil.clearObject(_societySystemListener);
         }
         _societySystemListener = null;
         if(_societyListPanel)
         {
            ClearUtil.clearObject(_societyListPanel);
         }
         _societyListPanel = null;
         if(_mySocietyPanel)
         {
            ClearUtil.clearObject(_mySocietyPanel);
         }
         _mySocietyPanel = null;
         if(_exSocietyPanel)
         {
            ClearUtil.clearObject(_exSocietyPanel);
         }
         _exSocietyPanel = null;
         if(_openNpcSelectBox)
         {
            ClearUtil.clearObject(_openNpcSelectBox);
         }
         _openNpcSelectBox = null;
         if(_bossChooseUI)
         {
            ClearUtil.clearObject(_bossChooseUI);
         }
         _bossChooseUI = null;
         if(_petBossChooseUI)
         {
            ClearUtil.clearObject(_petBossChooseUI);
         }
         _petBossChooseUI = null;
         if(_messageTipShow)
         {
            ClearUtil.clearObject(_messageTipShow);
         }
         _messageTipShow = null;
         if(_messageTip)
         {
            ClearUtil.clearObject(_messageTip);
         }
         _messageTip = null;
         if(_xiangMoLevelPanel)
         {
            ClearUtil.clearObject(_xiangMoLevelPanel);
         }
         _xiangMoLevelPanel = null;
         if(_preciousPanel)
         {
            ClearUtil.clearObject(_preciousPanel);
         }
         _preciousPanel = null;
         if(_newtaskPanel)
         {
            ClearUtil.clearObject(_newtaskPanel);
         }
         _newtaskPanel = null;
         if(_EndlessLevelPanel)
         {
            ClearUtil.clearObject(_EndlessLevelPanel);
         }
         _EndlessLevelPanel = null;
         if(_automaticPetPanel)
         {
            ClearUtil.clearObject(_automaticPetPanel);
         }
         _automaticPetPanel = null;
         if(_svActivityPanel)
         {
            ClearUtil.clearObject(_svActivityPanel);
         }
         _svActivityPanel = null;
         if(_resetPlayerTypePanel)
         {
            ClearUtil.clearObject(_resetPlayerTypePanel);
         }
         _resetPlayerTypePanel = null;
         if(_onePlayertoTwoPanel)
         {
            ClearUtil.clearObject(_onePlayertoTwoPanel);
         }
         _onePlayertoTwoPanel = null;
         if(_mountsPanel)
         {
            ClearUtil.clearObject(_mountsPanel);
         }
         _mountsPanel = null;
         if(_submitAutomaticPetScore)
         {
            ClearUtil.clearObject(_submitAutomaticPetScore);
         }
         _submitAutomaticPetScore = null;
         if(_automaticPetsData)
         {
            ClearUtil.clearObject(_automaticPetsData);
         }
         _automaticPetsData = null;
         if(_smallAssistantSaveData)
         {
            ClearUtil.clearObject(_smallAssistantSaveData);
         }
         _smallAssistantSaveData = null;
         if(_svActivitySaveData)
         {
            ClearUtil.clearObject(_svActivitySaveData);
         }
         _svActivitySaveData = null;
         if(_smallAssistant)
         {
            ClearUtil.clearObject(_smallAssistant);
         }
         _smallAssistant = null;
         if(_detectionClass3)
         {
            ClearUtil.clearObject(_detectionClass3);
         }
         _detectionClass3 = null;
         _instance = null;
      }
      
      public function get getServerTime() : GetServerTime
      {
         return _getServerTime;
      }
      
      public function set mainLineTaskData(param1:MainLineTaskData) : void
      {
         _mainLineTaskData = param1;
      }
      
      override public function set mouseChildren(param1:Boolean) : void
      {
         super.mouseChildren = param1;
         var _loc2_:Stage = Part1.getInstance().stage;
         if(!param1 && Boolean(manBan))
         {
            if(!_loc2_.getChildByName(manBan.name))
            {
               _loc2_.addChildAt(manBan,_loc2_.numChildren);
            }
            manBan.visible = true;
         }
         else if(manBan)
         {
            if(_loc2_.getChildByName(manBan.name))
            {
               _loc2_.removeChild(manBan);
            }
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         if(_changeFrameRatePanel)
         {
            _changeFrameRatePanel.setGamingUI(this);
         }
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("refreshAtt",refreshUI,true,0,true);
         addEventListener("deleItem",deleItem,false,0,true);
         addEventListener("clickQuitBtn",quit,true,0,true);
         addEventListener("enterIntoPackage",enterToPackage,true,0,true);
         addEventListener("enterIntoPet",enterToPet,true,0,true);
         addEventListener("enterIntoProtect",enterToProtect,true,0,true);
         addEventListener("enterIntoShop",enterToShowWall,true,0,true);
         addEventListener("enterIntoExchangePanel",enterToExchangePanel,true,0,true);
         addEventListener("enterIntoCodekeyPanel",enterToCodekeyPanel,true,0,true);
         addEventListener("enterIntoBBSPanel",enterToBBSPanel,true,0,true);
         addEventListener("enterinfohuanlezp",enterToHuanlePanel,true,0,true);
         addEventListener("enterinfomidautumn",enterToMidautumnPanel,true,0,true);
         addEventListener("enterinfodoubleegg",enterToDoubleEggPanel,true,0,true);
         addEventListener("enterinfozhuoqugift",enterToZhuoquGiftPanel,true,0,true);
         addEventListener("enterinforealname",enterToRealNamePanel,true,0,true);
         addEventListener("enterinfojhsbp",enterToJhsbPanel,true,0,true);
         addEventListener("enterIntoZhouNianPanel",zhouNianQing,true,0,true);
         addEventListener("enterIntoSignPanel",enterToSignPanel,true,0,true);
         addEventListener(" enterIntoFarm",enterToFarm,true,0,true);
         addEventListener("enterIntoRecaptureGold",enterToRecaptureGold,true,0,true);
         addEventListener("addGifts",addGifts,true,0,true);
         addEventListener("getMinHunTangNum",getMinHunTangNum,false,0,true);
         addEventListener("cutOneMinHunTang",cutOneMinHunTang,false,0,true);
         addEventListener("clickRefineFactoryBtn",openRefineFactory,true,0,true);
         Part1.getInstance().stage.addEventListener("logsuccess",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("usePayApi",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("incMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("decMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("getMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("payMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("paiedMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("rechargedMoney",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("payError",onPayEventHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("StoreStateEvent",getStoreStateHandler,false,0,true);
         Part1.getInstance().stage.addEventListener("shopErrorNd",onShopEventHandler);
         Part1.getInstance().stage.addEventListener("shopBuyNd",onShopEventHandler);
         Part1.getInstance().stage.addEventListener("shopGetList",onShopEventHandler);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(_player1)
         {
            _player1.render(param1);
         }
         if(_player2)
         {
            _player2.render(param1);
         }
         if(_externalPanel)
         {
            _externalPanel.render(param1);
         }
         OnLineGiftBagData.getInstance().render(param1);
         APIDataAnalyze.getInstance().render(param1);
         if(_onLineGiftBagPanel)
         {
            _onLineGiftBagPanel.render();
         }
         if(_internalPanel)
         {
            _internalPanel.render(param1);
         }
         if(_honourHallPanel)
         {
            _honourHallPanel.render(param1);
         }
         if(_worldBossPanel)
         {
            _worldBossPanel.render(param1);
         }
         if(_EndlessLevelPanel)
         {
            _EndlessLevelPanel.render(param1);
         }
         if(_societySystem)
         {
            _societySystem.render(param1);
         }
         if(_mySocietyPanel)
         {
            _mySocietyPanel.render(param1);
         }
         if(_exSocietyPanel)
         {
            _exSocietyPanel.render(param1);
         }
         if(_collectTimePanel)
         {
            _collectTimePanel.render(param1);
         }
         if(_automaticPetsData)
         {
            _automaticPetsData.render(param1);
         }
         if(_automaticPetPanel)
         {
            _automaticPetPanel.render(param1);
         }
         if(_smallAssistant)
         {
            _smallAssistant.render(param1);
         }
         if(_svActivityPanel)
         {
            _svActivityPanel.render(param1);
         }
         callRefresh();
         if(getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            addChild(_linBtn);
         }
         if(_hlPanel)
         {
            _hlPanel.render(param1);
         }
         if(_midautumn)
         {
            _midautumn.render(param1);
         }
         if(_jhsbPanel)
         {
            _jhsbPanel.render(param1);
         }
      }
      
      private function refreshUI(param1:UIPassiveEvent) : void
      {
         if(param1.data == null)
         {
            throw new Error("错误");
         }
         refresh(param1.data);
      }
      
      private function refreshPlayer(param1:int) : void
      {
         if(param1 & 512)
         {
            MyFunction.getInstance().refreshPlayer(_player1,1);
            MyFunction.getInstance().refreshPlayer(_player2,1);
         }
         if(param1 & 524288 && !EndlessManage.getInstance().IsEndlessMode)
         {
            if(_externalPanel)
            {
               _externalPanel.refreshCountFastUseEquip();
            }
         }
      }
      
      public function addExp(param1:uint, param2:Player) : void
      {
         addExperience(param2,param1 * ((!!param2.playerVO.extraAddExperienceRate ? 1 + param2.playerVO.extraAddExperienceRate / 100 : 1) * (!!param2.vipVO.addExperienceValue ? 1 + param2.vipVO.addExperienceValue / 100 : 1) * (!!ProtectData.getInstance().addExperienceValue ? 1 + ProtectData.getInstance().addExperienceValue / 100 : 1)));
         refresh(128);
      }
      
      public function addMoney(param1:uint, param2:Player) : void
      {
         param2.playerVO.money += param1 * (1 + param2.playerVO.extraAddMoneyRate / 100);
      }
      
      public function addHp(param1:uint, param2:Player) : void
      {
         param2.playerVO.bloodPercent = (param2.playerVO.bloodPercent * param2.playerVO.bloodVolume + param1) / param2.playerVO.bloodVolume;
         if(param2.playerVO.bloodPercent == 0)
         {
            if(_externalPanel.player1Panel.player == param2)
            {
               _externalPanel.player1Panel.isActive = false;
            }
            if(_externalPanel.player2Panel)
            {
               if(_externalPanel.player2Panel.player == param2)
               {
                  _externalPanel.player2Panel.isActive = false;
               }
            }
         }
         else
         {
            if(_externalPanel.player1Panel.player == param2)
            {
               _externalPanel.player1Panel.isActive = true;
            }
            if(_externalPanel.player2Panel)
            {
               if(_externalPanel.player2Panel.player == param2)
               {
                  _externalPanel.player2Panel.isActive = true;
               }
            }
         }
         refresh(128);
      }
      
      public function setAddDown(param1:Boolean) : void
      {
      }
      
      public function decHp(param1:uint, param2:Player) : void
      {
         param2.playerVO.bloodPercent = (param2.playerVO.bloodPercent * param2.playerVO.bloodVolume - param1) / param2.playerVO.bloodVolume;
         if(param2.playerVO.bloodPercent == 0)
         {
            if(_externalPanel.player1Panel.player == param2)
            {
               _externalPanel.player1Panel.isActive = false;
            }
            if(_externalPanel.player2Panel)
            {
               if(_externalPanel.player2Panel.player == param2)
               {
                  _externalPanel.player2Panel.isActive = false;
               }
            }
         }
         else
         {
            if(_externalPanel.player1Panel.player == param2)
            {
               _externalPanel.player1Panel.isActive = true;
            }
            if(_externalPanel.player2Panel)
            {
               if(_externalPanel.player2Panel.player == param2)
               {
                  _externalPanel.player2Panel.isActive = true;
               }
            }
         }
         refresh(128);
      }
      
      public function addMp(param1:uint, param2:Player) : void
      {
         param2.playerVO.magicPercent = (param2.playerVO.magicPercent * param2.playerVO.maxMagic + param1) / param2.playerVO.maxMagic;
         refresh(128);
      }
      
      public function decMp(param1:uint, param2:Player) : void
      {
         param2.playerVO.magicPercent = (param2.playerVO.magicPercent * param2.playerVO.maxMagic - param1) / param2.playerVO.maxMagic;
         refresh(128);
      }
      
      public function stopGame() : void
      {
         if(_player1)
         {
            _player1.stopGame();
         }
         if(_player2)
         {
            _player2.stopGame();
         }
         if(_shiTuPanel)
         {
            _shiTuPanel.stopGame();
         }
         if(_recaptureGoldGame)
         {
            _recaptureGoldGame.stopGame();
         }
         _externalPanel.stopGame();
      }
      
      public function continueGame() : void
      {
         if(_player1)
         {
            _player1.continueGame();
         }
         if(_player2)
         {
            _player2.continueGame();
         }
         if(_shiTuPanel)
         {
            _shiTuPanel.continueGame();
         }
         if(_externalPanel)
         {
            _externalPanel.continueGame();
         }
      }
      
      public function isAbleRunPlayerSkill(param1:SkillVO, param2:Player) : Boolean
      {
         if(param1.level <= 0)
         {
            return false;
         }
         if((param1 as PlayerActiveSkillVO).isCD)
         {
            return false;
         }
         if((param1 as PlayerActiveSkillVO).manaCost > param2.playerVO.getCurrentMagic())
         {
            return false;
         }
         return true;
      }
      
      public function runPlayerSkill(param1:SkillVO, param2:Player) : void
      {
         if((param1 as PlayerActiveSkillVO).isCD == true)
         {
            throw new Error("技能正在cd中，不能释放");
         }
         param2.playerVO.decMagic((param1 as PlayerActiveSkillVO).manaCost);
         (param1 as IActiveSkillVO).startCD();
         refresh(193);
      }
      
      public function isAbleRunPetSkill(param1:SkillVO, param2:Player) : Boolean
      {
         if(param1.level <= 0)
         {
            return false;
         }
         if((param1 as PetActiveSkillVO).isCD)
         {
            return false;
         }
         if((param1 as PetActiveSkillVO).manaCost > param2.playerVO.getCurrentMagic())
         {
            return false;
         }
         if(param2.playerVO.pet.petEquipmentVO.essentialPercent <= 0)
         {
            showMessageTip("宠物精气不足");
            return false;
         }
         return true;
      }
      
      public function runPetSkill(param1:SkillVO, param2:Player) : void
      {
         (param1 as IActiveSkillVO).startCD();
         param2.playerVO.pet.petEquipmentVO.essentialPercent = (param2.playerVO.pet.petEquipmentVO.essentialPercent * param2.playerVO.pet.petEquipmentVO.essentialVolume - (param1 as PetActiveSkillVO).essenceCost) / param2.playerVO.pet.petEquipmentVO.essentialVolume;
         param2.playerVO.decMagic((param1 as PetActiveSkillVO).manaCost);
         MyFunction.getInstance().refreshPet(param2.playerVO.pet.petEquipmentVO);
         refresh(201);
         if(param2.playerVO.pet.petEquipmentVO.essentialPercent == 0)
         {
            refresh(512);
         }
      }
      
      public function addEqFromDropEq(param1:String, param2:Player) : Boolean
      {
         var _loc3_:int = 0;
         var _loc6_:PetActiveSkillVO = null;
         var _loc5_:XML = XMLSingle.getInstance().equipmentXML;
         _loc3_ = XMLSingle.getIDByClassName(MyFunction.getInstance().unweaveClassNameString(param1),_loc5_);
         var _loc4_:EquipmentVO;
         switch((_loc4_ = XMLSingle.getEquipment(_loc3_,_loc5_)).equipmentType)
         {
            case "pet":
               (_loc4_ as PetEquipmentVO).petLevel = 1;
               _loc6_ = (_loc4_ as PetEquipmentVO).activeSkillVO as PetActiveSkillVO;
               _loc6_.originalHurt = (_loc4_ as PetEquipmentVO).petLevel * _loc6_.hurCoefficient + _loc6_.additionHurt;
               _loc6_.originalPkHurt = (_loc4_ as PetEquipmentVO).petLevel * _loc6_.pkHurtCoefficient + _loc6_.additionPkHurt;
               XMLSingle.getInstance().setPetData(_loc3_,(_loc4_ as PetEquipmentVO).petLevel,_loc4_ as PetEquipmentVO);
               (_loc4_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(0);
               if(PetEquipmentVO(_loc4_).initPassiveSkillNum)
               {
                  (_loc4_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomFixedNumPassiveSkillVO(_loc4_ as PetEquipmentVO,(_loc4_ as PetEquipmentVO).initPassiveSkillNum);
               }
               else
               {
                  (_loc4_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomOnePassiveSkillVOInCreatePet(_loc4_ as PetEquipmentVO);
               }
               (_loc4_ as PetEquipmentVO).essentialPercent = 1;
               MyFunction.getInstance().refreshPet(_loc4_ as PetEquipmentVO);
               (_loc4_ as PetEquipmentVO).experiencePercent = 0;
               break;
            case "material":
            case "potion":
            case "pocket":
            case "buffEquipment":
            case "grass":
            case "insetGem":
               (_loc4_ as StackEquipmentVO).num = 1;
         }
         if(MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param2.playerVO.packageEquipmentVOs,_loc4_,2))
         {
            return true;
         }
         showMessageTip("您的背包已满.");
         return false;
      }
      
      private function addGifts(param1:UIPassiveEvent) : void
      {
         MyFunction2.addEquipmentVOs(param1.data.giftBags,GamingUI.getInstance().player1,param1.data.failFunction,param1.data.successFunction,param1.data.failFunParams,param1.data.successFunParams);
      }
      
      public function showQuickenChargeBox(param1:int, param2:Function) : QuickenChargeBox
      {
         var _loc3_:QuickenChargeBox = new QuickenChargeBox();
         _loc3_.x = stage.stageWidth / 2;
         _loc3_.y = stage.stageHeight / 2;
         _loc3_.timeFun = param2;
         _loc3_.setNum(param1);
         _loc3_.activeFun = toActive;
         toNoActive();
         addChild(_loc3_);
         return _loc3_;
      }
      
      private function toActive() : void
      {
         _internalPanel.mouseChildren = true;
         _internalPanel.mouseEnabled = true;
      }
      
      private function toNoActive() : void
      {
         _internalPanel.mouseChildren = false;
         _internalPanel.mouseEnabled = false;
      }
      
      public function openMiragePanel() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_miragePanel)
            {
               _miragePanel = new MiragePanel();
            }
            if(!getChildByName(_miragePanel.name))
            {
               addChildAt(_miragePanel,0);
            }
            removeChild(_externalPanel);
            _miragePanel.openMiragePanel();
            Part1.getInstance().hideTipForPet();
         },"mirage");
      }
      
      public function closeMirage() : void
      {
         Part1.getInstance().showTipForPet();
         _miragePanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
         _miragePanel.closeMiragePanel();
         if(Boolean(_miragePanel) && getChildByName(_miragePanel.name))
         {
            removeChild(_miragePanel);
         }
         if(_miragePanel)
         {
            _miragePanel.clear();
            _miragePanel = null;
         }
         addChild(_externalPanel);
         LoadPart.getInstance().unLoadOnePart("mirage");
         Part1.getInstance().continueGame();
      }
      
      public function openAdvancePetPanel() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_petAdvancePanel)
            {
               _petAdvancePanel = new PetAdvancePanel();
            }
            if(!getChildByName(_petAdvancePanel.name))
            {
               addChild(_petAdvancePanel);
            }
            removeChild(_externalPanel);
            Part1.getInstance().hideTipForPet();
         },"petAdancePanel");
      }
      
      public function openTaskPanel() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_exTaskPanel)
            {
               _exTaskPanel = new ExTaskPanel();
            }
            if(!getChildByName(_exTaskPanel.name))
            {
               addChildAt(_exTaskPanel,0);
            }
            _exTaskPanel.refreshTaskList();
            removeChild(_externalPanel);
         },"exTaskPanel");
      }
      
      public function openOnePlayerRankListPanel() : void
      {
         var gamingUI:GamingUI;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_rankListPanel);
            _rankListPanel = new OnePlayerAllPKRankListPanel();
            if(!getChildByName(_rankListPanel.name))
            {
               addChildAt(_rankListPanel,0);
            }
            _rankListPanel.setGamingUI(gamingUI);
            _rankListPanel.init(GameData.MonthCicly ? 1576 : 1583,PlayerDataForPK.getInstance().winMonthMatch);
            removeChild(_externalPanel);
         },"rankList");
      }
      
      public function openWorldBossRankListPanel(param1:int, param2:Number) : void
      {
         var gamingUI:GamingUI;
         var rankId:int = param1;
         var currrentScore:Number = param2;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_rankListPanel);
            _rankListPanel = new WorldBossRankListPanel();
            if(!getChildByName(_rankListPanel.name))
            {
               addChildAt(_rankListPanel,0);
            }
            _rankListPanel.setGamingUI(gamingUI);
            _rankListPanel.init(rankId,currrentScore);
            removeChild(_externalPanel);
         },"rankList");
      }
      
      public function openPKMode2RankList1Panel(param1:int, param2:uint) : void
      {
         var gamingUI:GamingUI;
         var rankId:int = param1;
         var currentScore:uint = param2;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_rankListPanel);
            _rankListPanel = new PKModeRankList1();
            if(!getChildByName(_rankListPanel.name))
            {
               addChildAt(_rankListPanel,0);
            }
            _rankListPanel.setGamingUI(gamingUI);
            _rankListPanel.init(rankId,currentScore);
            removeChild(_externalPanel);
         },"rankList");
      }
      
      public function openAutomaticPetScoreRankList() : void
      {
         var gamingUI:GamingUI;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_rankListPanel);
            _rankListPanel = new AutomaticPetScoreRankList();
            if(!getChildByName(_rankListPanel.name))
            {
               addChildAt(_rankListPanel,0);
            }
            _rankListPanel.setGamingUI(gamingUI);
            _rankListPanel.init(993,_submitAutomaticPetScore.getAutomaticPetScore(_player1,_player2));
            removeChild(_externalPanel);
         },"rankList");
      }
      
      public function openTwoPlayerRankListPanel() : void
      {
         var gamingUI:GamingUI;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_rankListPanel);
            _rankListPanel = new TwoPlayerWeekPkRankListPanel();
            if(!getChildByName(_rankListPanel.name))
            {
               addChildAt(_rankListPanel,0);
            }
            _rankListPanel.setGamingUI(gamingUI);
            _rankListPanel.init(1580,PlayerDataForPK.getInstance().winMatchForTwoPlayer);
            removeChild(_externalPanel);
         },"rankList");
      }
      
      public function openRecaptureGoldRankList() : void
      {
         var gamingUI:GamingUI;
         Part1.getInstance().stopGame();
         gamingUI = this;
         MyFunction2.loadXMLFunction("recaptureGold",function(param1:XML):void
         {
            var xml:XML = param1;
            LoadPart.getInstance().loadOnePart1(function():void
            {
               ClearUtil.clearObject(_rankListPanel);
               _rankListPanel = new RecaptureGoldRankList();
               if(!getChildByName(_rankListPanel.name))
               {
                  addChildAt(_rankListPanel,0);
               }
               _rankListPanel.setGamingUI(gamingUI);
               _rankListPanel.init(xml.OtherData[0].RankList[0].@rId,0);
               if(_recaptureGoldGame)
               {
                  if(getChildByName(_recaptureGoldGame.name))
                  {
                     removeChild(_recaptureGoldGame);
                  }
                  _recaptureGoldGame.clear();
                  _recaptureGoldGame = null;
                  LoadPart.getInstance().unLoadOnePart("recaptureGold");
               }
               if(Boolean(_externalPanel) && getChildByName(_externalPanel.name))
               {
                  removeChild(_externalPanel);
               }
            },"rankList");
         },function():void
         {
            showMessageTip("出错了，请重试！");
         },true);
      }
      
      public function openRecaptureGoldRewardPanel() : void
      {
         Part1.getInstance().stopGame();
         MyFunction2.loadXMLFunction("recaptureGold",function(param1:XML):void
         {
            var xml:XML = param1;
            LoadPart.getInstance().loadOnePart1(function():void
            {
               if(!_recaptureGoldRewardPanel)
               {
                  _recaptureGoldRewardPanel = new RewardPanel(xml.OtherData[0].RankList[0].@rId);
               }
               if(!getChildByName(_recaptureGoldRewardPanel.name))
               {
                  addChildAt(_recaptureGoldRewardPanel,0);
               }
               if(_recaptureGoldGame)
               {
                  if(getChildByName(_recaptureGoldGame.name))
                  {
                     removeChild(_recaptureGoldGame);
                  }
                  _recaptureGoldGame.clear();
                  _recaptureGoldGame = null;
                  LoadPart.getInstance().unLoadOnePart("recaptureGold");
               }
               if(Boolean(_externalPanel) && getChildByName(_externalPanel.name))
               {
                  removeChild(_externalPanel);
               }
            },"recaptureGoldReward");
         },function():void
         {
            showMessageTip("出错了，请重试！");
         },true);
      }
      
      public function openWorldBoss() : void
      {
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_worldBossPanel);
         _worldBossPanel = new WorldBossPanel();
         _worldBossPanel.setGamingUI(this);
         _worldBossPanel.init(getVersionControl());
         addChild(_worldBossPanel);
      }
      
      public function openWeaponMake() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToEqMakeAndUpgradePanel();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openHatchPanel() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToHatchPanel();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openShop() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToShop(null);
            _internalPanel.shopBtn.gotoOneFrame();
            _internalPanel.packageBtn.visible = false;
            _internalPanel.skillPanelBtn.visible = false;
            _internalPanel.storageBtn.visible = false;
            _internalPanel.shopBtn.visible = false;
            _internalPanel.taskBtn.visible = false;
            _internalPanel.npcNameBitmap.bitmapData = new ShopNameBitmapdata();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openStore() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToStorage(null);
            _internalPanel.storageBtn.gotoOneFrame();
            _internalPanel.packageBtn.visible = false;
            _internalPanel.skillPanelBtn.visible = false;
            _internalPanel.storageBtn.visible = false;
            _internalPanel.shopBtn.visible = false;
            _internalPanel.taskBtn.visible = false;
            _internalPanel.npcNameBitmap.bitmapData = new StorageNameBitmapdata();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openPublicStore() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToPublicStorage();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openVipPanel() : void
      {
         Part1.getInstance().stopGame();
         Part1.getInstance().mouseChildren = false;
         Part1.getInstance().mouseEnabled = false;
         AnalogServiceHoldFunction.getInstance().getTotalRechargedFun(LoadPart.getInstance().loadOnePart1,[function():void
         {
            if(!_VIPPanel)
            {
               _VIPPanel = new VIPPanel();
            }
            if(!getChildByName(_VIPPanel.name))
            {
               addChildAt(_VIPPanel,0);
            }
            _VIPPanel.refreshVIPPanel(_player1.vipVO);
            removeChild(_externalPanel);
            Part1.getInstance().mouseChildren = true;
            Part1.getInstance().mouseEnabled = true;
         },"vipPanel"],function(param1:Stage, param2:String):void
         {
            Part1.getInstance().mouseChildren = true;
            Part1.getInstance().mouseEnabled = true;
            showMessageTip(param2);
         },[Part1.getInstance().stage,"打开VIPNPC出错了！请重试！"]);
      }
      
      public function openTask() : void
      {
         var openTaskNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("taskNpc");
         addChild(_openNpcSelectBox);
         openTaskNpcListener = new OpenNpcListener1();
         openTaskNpcListener.openNpcFun = function(param1:String):void
         {
            switch(param1)
            {
               case "task":
                  openTaskPanel();
                  break;
               case "mainLineTask":
                  openMainLineTaskPanel();
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openTaskNpcListener);
      }
      
      public function openEquipMagicCreat() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToEqMagicPanel();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openEquipMagicInhret() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToEqInheritPanel();
            removeChild(_externalPanel);
         },"internalPanel");
      }
      
      public function openEquipMagic() : void
      {
         var openTaskNpcListener:OpenNpcListener1;
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("equipMagicNpc");
         addChild(_openNpcSelectBox);
         openTaskNpcListener = new OpenNpcListener1();
         openTaskNpcListener.openNpcFun = function(param1:String):void
         {
            switch(param1)
            {
               case "equipmentCreatMagic":
                  openEquipMagicCreat();
                  break;
               case "equipmentInheritMagic":
                  openEquipMagicInhret();
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openTaskNpcListener);
      }
      
      public function openNo1Rank() : void
      {
         RankDataInfo.getInstance().setInType(1);
         _NewAllRank = new NewAllRank();
         addChild(_NewAllRank);
      }
      
      private function openEndlessRank() : void
      {
         _EndlessRankPanel = new EndlessRankPanel();
         _EndlessRankPanel.setRoleMode(!!player2 ? 2 : 1);
         _EndlessRankPanel.loadUI(getVersionControl(),Part1.getInstance().getLoadUI(),this);
         addChild(_EndlessRankPanel);
      }
      
      public function openRankList() : void
      {
         var openRankListNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("RankNpc");
         addChild(_openNpcSelectBox);
         openRankListNpcListener = new OpenNpcListener1();
         openRankListNpcListener.openNpcFun = function(param1:String):void
         {
            GetDataFunction.getInstance().bIsRank = true;
            switch(param1)
            {
               case "pkRankOne":
                  openOnePlayerRankListPanel();
                  break;
               case "PKRankTwo":
                  openTwoPlayerRankListPanel();
                  break;
               case "worldBoss":
                  openWorldBossRankList();
                  break;
               case "endless":
                  openEndlessRank();
                  break;
               case "autoPetScore":
                  openAutomaticPetScoreRankList();
                  break;
               case "pkMode2_1":
                  openNo1Rank();
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openRankListNpcListener);
      }
      
      public function openPet() : void
      {
         var openPetNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("petNpc");
         addChild(_openNpcSelectBox);
         openPetNpcListener = new OpenNpcListener1();
         openPetNpcListener.openNpcFun = function(param1:String):void
         {
            switch(param1)
            {
               case "hatch":
                  openHatchPanel();
                  break;
               case "mirage":
                  openMiragePanel();
                  break;
               case "advance":
                  openAdvancePetPanel();
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openPetNpcListener);
      }
      
      public function openPK() : void
      {
         var openPKNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("PKNpc");
         addChild(_openNpcSelectBox);
         openPKNpcListener = new OpenNpcListener1();
         openPKNpcListener.openNpcFun = function(param1:String):void
         {
            switch(param1)
            {
               case "pkOne":
                  Part1.getInstance().openPK("onePK");
                  break;
               case "pkTwo":
                  Part1.getInstance().openPK("twoPK");
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openPKNpcListener);
      }
      
      public function openSocietySystem() : void
      {
         Part1.getInstance().stopGame();
         if(_societySystemListener == null)
         {
            _societySystemListener = new SocietySystemListener();
         }
         if(_societySystem == null)
         {
            _societySystem = new SocietySystem();
            _societySystem.setGamingUI(this);
            _societySystem.setVersionControl(getVersionControl());
            _societySystem.init();
            _societySystem.addSocietySystemListener(_societySystemListener);
         }
         else if(_societySystem.getIsLogIn() == false)
         {
            _societySystem.tryReConnectAndLogIn();
         }
         else
         {
            if(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId())
            {
               _societySystem.getTheSocietyData();
            }
            openSocietyNpcBox();
         }
         _societySystemListener.loginCompleteFun = openSocietyNpcBox;
      }
      
      public function openExchangeEquipment() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_exchangeWorld)
            {
               _exchangeWorld = new ExchangeWorld();
            }
            if(!getChildByName(_exchangeWorld.name))
            {
               addChild(_exchangeWorld);
            }
            removeChild(_externalPanel);
         },"exchangeEquipment");
      }
      
      public function openFarm() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_farm)
            {
               _farm = new Farm();
            }
            if(!getChildByName(_farm.name))
            {
               addChild(_farm);
            }
            removeChild(_externalPanel);
         },"farm");
      }
      
      public function openShopWall() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_shopWall)
            {
               _shopWall = new ShopWall(_player1,_player2);
            }
            if(!getChildByName(_shopWall.name))
            {
               addChild(_shopWall);
            }
            _shopWall.currentTicketPointText.text = CurrentTicketPointManager.getInstance().getCurrentTicketPoint().toString();
            _shopWall.addGoods();
            if(_externalPanel.parent)
            {
               removeChild(_externalPanel);
            }
         },"shopWall");
      }
      
      public function openSignPanel() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_signPanel)
            {
               _signPanel = new SignPanel();
               if(!getChildByName(_signPanel.name))
               {
                  addChild(_signPanel);
               }
               _signPanel.init();
            }
            removeChild(_externalPanel);
         },"sign");
      }
      
      private function openRefineFactory(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         if(Boolean(_farm) && getChildByName(_farm.name))
         {
            removeChild(_farm);
         }
         if(_farm)
         {
            _farm.clear();
            _farm = null;
         }
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_refineFactory)
            {
               _refineFactory = new RefineFactory();
            }
            if(!getChildByName(_refineFactory.name))
            {
               addChild(_refineFactory);
            }
         },"farm");
      }
      
      public function openHonourPanel(param1:String = null) : void
      {
         var gamingUI:GamingUI;
         var houonurHallName:String = param1;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_honourHallPanel);
            _honourHallPanel = new HonourHallPanel();
            _honourHallPanel.setGotoHallName(houonurHallName);
            _honourHallPanel.setGamingUI(gamingUI);
            _honourHallPanel.init();
            if(!getChildByName(_honourHallPanel.name))
            {
               addChild(_honourHallPanel);
            }
         },"honourHall");
      }
      
      public function openShiTu() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(_shiTuPanel == null)
            {
               _shiTuPanel = new ShiTuPanel();
               _shiTuPanel.init();
            }
            if(!getChildByName(_shiTuPanel.name))
            {
               addChild(_shiTuPanel);
            }
            removeChild(_externalPanel);
         },"shiTu");
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         refresh(524288);
         if(e.target.parent == _internalPanel)
         {
            _internalPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            _internalPanel.takeOutEquipment();
            if(Boolean(_internalPanel) && getChildByName(_internalPanel.name))
            {
               removeChild(_internalPanel);
            }
            if(_internalPanel)
            {
               _internalPanel.clear();
               _internalPanel = null;
            }
            addChild(_externalPanel);
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _VIPPanel)
         {
            _VIPPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_VIPPanel) && getChildByName(_VIPPanel.name))
            {
               removeChild(_VIPPanel);
            }
            if(_VIPPanel)
            {
               _VIPPanel.clear();
               _VIPPanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("vipPanel");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _shopWall)
         {
            _shopWall.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_shopWall) && getChildByName(_shopWall.name))
            {
               removeChild(_shopWall);
            }
            if(_shopWall)
            {
               _shopWall.clear();
               _shopWall = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("shopWall");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _exTaskPanel)
         {
            _exTaskPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_exTaskPanel) && getChildByName(_exTaskPanel.name))
            {
               removeChild(_exTaskPanel);
            }
            if(_exTaskPanel)
            {
               _exTaskPanel.clear();
               _exTaskPanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("exTaskPanel");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _miragePanel)
         {
            _miragePanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            _miragePanel.closeMiragePanel();
            if(Boolean(_miragePanel) && getChildByName(_miragePanel.name))
            {
               removeChild(_miragePanel);
            }
            if(_miragePanel)
            {
               _miragePanel.clear();
               _miragePanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("mirage");
            Part1.getInstance().continueGame();
            Part1.getInstance().showTipForPet();
         }
         else if(e.target.parent.parent == _farm && e.target is QuitBtn)
         {
            _farm.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_farm) && getChildByName(_farm.name))
            {
               removeChild(_farm);
            }
            if(_farm)
            {
               _farm.clear();
               _farm = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("farm");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _refineFactory)
         {
            if(Boolean(_refineFactory) && getChildByName(_refineFactory.name))
            {
               removeChild(_refineFactory);
            }
            if(_refineFactory)
            {
               _refineFactory.clear();
               _refineFactory = null;
            }
            LoadPart.getInstance().loadOnePart1(function():void
            {
               if(!_farm)
               {
                  _farm = new Farm();
               }
               if(!getChildByName(_farm.name))
               {
                  addChild(_farm);
               }
            },"farm");
         }
         else if(e.target.parent == _protectPanel && e.target is QuitBtn)
         {
            _protectPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_protectPanel) && getChildByName(_protectPanel.name))
            {
               removeChild(_protectPanel);
            }
            if(_protectPanel)
            {
               _protectPanel.clear();
               _protectPanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("protect");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _exchangeGiftBagPanel)
         {
            if(Boolean(_exchangeGiftBagPanel) && getChildByName(_exchangeGiftBagPanel.name))
            {
               removeChild(_exchangeGiftBagPanel);
            }
            if(_exchangeGiftBagPanel)
            {
               _exchangeGiftBagPanel.clear();
               _exchangeGiftBagPanel = null;
            }
            LoadPart.getInstance().unLoadOnePart("exchangeGiftBagPanel");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _signPanel)
         {
            _signPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
            if(Boolean(_signPanel) && getChildByName(_signPanel.name))
            {
               removeChild(_signPanel);
            }
            if(_signPanel)
            {
               _signPanel.clear();
               _signPanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("sign");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent.parent == _recaptureGoldGame)
         {
            if(_recaptureGoldGame)
            {
               if(getChildByName(_recaptureGoldGame.name))
               {
                  removeChild(_recaptureGoldGame);
               }
               _recaptureGoldGame.clear();
               _recaptureGoldGame = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("recaptureGold");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _recaptureGoldRewardPanel)
         {
            if(_recaptureGoldRewardPanel)
            {
               if(getChildByName(_recaptureGoldRewardPanel.name))
               {
                  removeChild(_recaptureGoldRewardPanel);
               }
               _recaptureGoldRewardPanel.clear();
               _recaptureGoldRewardPanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("recaptureGoldReward");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _exchangeWorld)
         {
            if(_exchangeWorld)
            {
               if(getChildByName(_exchangeWorld.name))
               {
                  removeChild(_exchangeWorld);
               }
               _exchangeWorld.clear();
               _exchangeWorld = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("exchangeEquipment");
            Part1.getInstance().continueGame();
         }
         else if(e.target.parent == _shiTuPanel)
         {
            closeShiTuPanel();
         }
         else if(e.target.parent == _petAdvancePanel)
         {
            if(_petAdvancePanel)
            {
               if(getChildByName(_petAdvancePanel.name))
               {
                  removeChild(_petAdvancePanel);
               }
               _petAdvancePanel.clear();
               _petAdvancePanel = null;
            }
            addChild(_externalPanel);
            LoadPart.getInstance().unLoadOnePart("petAdancePanel");
            Part1.getInstance().continueGame();
            Part1.getInstance().showTipForPet();
         }
      }
      
      public function closeShiTuPanel() : void
      {
         if(_shiTuPanel)
         {
            if(_shiTuPanel.parent)
            {
               _shiTuPanel.parent.removeChild(_shiTuPanel);
            }
            _shiTuPanel.clear();
            _shiTuPanel = null;
         }
         if(_externalPanel.parent == null)
         {
            addChild(_externalPanel);
         }
         LoadPart.getInstance().unLoadOnePart("shiTu");
         Part1.getInstance().continueGame();
      }
      
      private function enterToPackage(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            refresh(1024);
            DetectionClass.getInstance().detectionAllPlayer1(_player1,player2,publicStorageEquipmentVOs);
            removeChild(_externalPanel);
         },"internalPanel");
         Part1.getInstance().stopGame();
      }
      
      private function enterToPet(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_internalPanel)
            {
               _internalPanel = new MyControlPanel();
            }
            _internalPanel.initControlPanel(_player1,_player2);
            _internalPanel.isVIPState = _player1.vipVO.vipLevel;
            _internalPanel.initPublicStorage(_publicStorageEquipments);
            addChildAt(_internalPanel,0);
            _internalPanel.switchToPet();
            removeChild(_externalPanel);
         },"internalPanel");
         Part1.getInstance().stopGame();
      }
      
      public function enterToProtect(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_protectPanel)
            {
               _protectPanel = new ProtectPanel();
            }
            if(!getChildByName(_protectPanel.name))
            {
               addChild(_protectPanel);
            }
            removeChild(_externalPanel);
         },"protect");
         Part1.getInstance().stopGame();
      }
      
      private function enterToShowWall(param1:UIBtnEvent) : void
      {
         enterToNewShop();
      }
      
      private function enterToExchangePanel(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_exchangeGiftBagPanel)
            {
               _exchangeGiftBagPanel = new ExchangeGiftBagPanel();
            }
            if(!getChildByName(_exchangeGiftBagPanel.name))
            {
               addChild(_exchangeGiftBagPanel);
            }
         },"exchangeGiftBagPanel");
      }
      
      private function enterToCodekeyPanel(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_codekeyGiftBagPanel)
            {
               _codekeyGiftBagPanel = new CodekeyGiftBagPanel();
            }
            if(!getChildByName(_codekeyGiftBagPanel.name))
            {
               addChild(_codekeyGiftBagPanel);
            }
         },"CodekeyGiftBagPanel");
      }
      
      private function enterToBBSPanel(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_bbsPanel)
            {
               _bbsPanel = new BBSPanel();
            }
            if(!getChildByName(_bbsPanel.name))
            {
               addChild(_bbsPanel);
            }
         },"bbsPanel");
      }
      
      public function enterToHuanlePanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_hlPanel)
            {
               _hlPanel = new HLPanel(_svActivitySaveData);
            }
            if(!getChildByName(_hlPanel.name))
            {
               addChild(_hlPanel);
            }
         },"HuanlePanel");
      }
      
      public function clearHLPanel() : void
      {
         if(_hlPanel)
         {
            _hlPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("HuanlePanel");
         Part1.getInstance().continueGame();
      }
      
      public function enterToMidautumnPanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_midautumn)
            {
               _midautumn = new MidautumnPanel();
            }
            if(!getChildByName(_midautumn.name))
            {
               addChild(_midautumn);
            }
         },"MidautumnPanel");
      }
      
      public function clearMidautumnPanel() : void
      {
         if(_midautumn)
         {
            _midautumn = null;
         }
         LoadPart.getInstance().unLoadOnePart("MidautumnPanel");
         Part1.getInstance().continueGame();
      }
      
      public function enterToJhsbPanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_jhsbPanel)
            {
               _jhsbPanel = new JhsbView();
               _jhsbPanel.init(_svActivitySaveData,Part1.getInstance().getVersionControl());
            }
            if(!getChildByName(_jhsbPanel.name))
            {
               addChild(_jhsbPanel);
            }
         },"JhsbView");
      }
      
      public function clearJhsbPanel() : void
      {
         if(_jhsbPanel)
         {
            _jhsbPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("JhsbView");
         Part1.getInstance().continueGame();
      }
      
      public function clearBBSPanel() : void
      {
         if(_bbsPanel)
         {
            _bbsPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("bbsPanel");
         Part1.getInstance().continueGame();
      }
      
      public function clearCodekeyPanel() : void
      {
         if(_codekeyGiftBagPanel)
         {
            _codekeyGiftBagPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("CodekeyGiftBagPanel");
         Part1.getInstance().continueGame();
      }
      
      public function enterToWuyiPoster() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_wuyiPoster)
            {
               _wuyiPoster = new WuYiPosterPanel();
            }
            if(!getChildByName(_wuyiPoster.name))
            {
               addChild(_wuyiPoster);
            }
         },"wuyiPoster");
      }
      
      public function enterToLiuyiPoster(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_wuyiPoster)
            {
               _liuyiPoster = new LiuyiPosterPanle();
            }
            if(!getChildByName(_liuyiPoster.name))
            {
               addChild(_liuyiPoster);
            }
         },"childrenday");
      }
      
      public function zhouNianQing(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().setTipXY(250,10);
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_zhounianPanel)
            {
               _zhounianPanel = new ZhounianActivityPanel();
            }
            if(!getChildByName(_zhounianPanel.name))
            {
               addChild(_zhounianPanel);
            }
         },"wuyiPoster");
      }
      
      public function clearZhouNianQing() : void
      {
         Part1.getInstance().setTipXY(250,150);
         LoadPart.getInstance().unLoadOnePart("wuyiPoster");
         if(_zhounianPanel)
         {
            _zhounianPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function clearWuyiPoster() : void
      {
         LoadPart.getInstance().unLoadOnePart("wuyiPoster");
         if(_wuyiPoster)
         {
            _wuyiPoster = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function clearLiuyiPoster() : void
      {
         LoadPart.getInstance().unLoadOnePart("childrenday");
         if(_liuyiPoster)
         {
            _liuyiPoster = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToFirstPay() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_firstPayPanel)
            {
               _firstPayPanel = new FirstPayPanel();
            }
            if(!getChildByName(_firstPayPanel.name))
            {
               addChild(_firstPayPanel);
            }
         },"firstPayPanel");
      }
      
      public function clearFirstPay() : void
      {
         LoadPart.getInstance().unLoadOnePart("firstPayPanel");
         if(_firstPayPanel)
         {
            _firstPayPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToWeekPay() : void
      {
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_weekPayPanel)
            {
               _weekPayPanel = new WeekPayPanel();
            }
            if(!getChildByName(_weekPayPanel.name))
            {
               addChild(_weekPayPanel);
            }
         },"weekPayPanel");
      }
      
      public function clearWeekPay() : void
      {
         LoadPart.getInstance().unLoadOnePart("weekPayPanel");
         if(_weekPayPanel)
         {
            _weekPayPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToBuchang() : void
      {
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_BuchangPanel)
            {
               _BuchangPanel = new BuchangPanel();
            }
            if(!getChildByName(_BuchangPanel.name))
            {
               addChild(_BuchangPanel);
            }
         },"BuchangPanel");
      }
      
      public function clearBuchang() : void
      {
         LoadPart.getInstance().unLoadOnePart("BuchangPanel");
         if(_BuchangPanel)
         {
            _BuchangPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToWuyiView() : void
      {
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_WuyiPanel)
            {
               _WuyiPanel = new WuyiPanel();
            }
            if(!getChildByName(_WuyiPanel.name))
            {
               addChild(_WuyiPanel);
            }
         },"WuyiPanel");
      }
      
      public function clearWuyiView() : void
      {
         LoadPart.getInstance().unLoadOnePart("WuyiPanel");
         if(_WuyiPanel)
         {
            _WuyiPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function getConsumerView() : ConsumerView
      {
         return _consumerPanel;
      }
      
      public function enterToConsumer() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_consumerPanel)
            {
               _consumerPanel = new ConsumerView();
            }
            if(!getChildByName(_consumerPanel.name))
            {
               addChild(_consumerPanel);
            }
         },"ConsumerView");
      }
      
      public function clearConsumer() : void
      {
         LoadPart.getInstance().unLoadOnePart("ConsumerView");
         if(_consumerPanel)
         {
            _consumerPanel = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToNewShop(param1:int = 0, param2:int = 0) : void
      {
         var _page1:int = param1;
         var _page2:int = param2;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_newShop)
            {
               _newShop = new NewShop(_page1,_page2);
            }
            if(!getChildByName(_newShop.name))
            {
               addChild(_newShop);
            }
         },"newShop");
      }
      
      public function clearNewShop() : void
      {
         LoadPart.getInstance().unLoadOnePart("newShop");
         if(_newShop)
         {
            _newShop = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToNewSign() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_newSign)
            {
               _newSign = new NewSignPanel();
            }
            if(!getChildByName(_newSign.name))
            {
               addChild(_newSign);
            }
         },"newSign");
      }
      
      public function clearNewSign() : void
      {
         LoadPart.getInstance().unLoadOnePart("newSign");
         if(_newSign)
         {
            _newSign = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToMobileGift() : void
      {
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_mobileGift)
            {
               _mobileGift = new MobileGiftsPanel();
            }
            if(!getChildByName(_mobileGift.name))
            {
               addChild(_mobileGift);
            }
         },"mobileGifts");
      }
      
      public function clearMobileGift() : void
      {
         LoadPart.getInstance().unLoadOnePart("mobileGifts");
         if(_mobileGift)
         {
            _mobileGift = null;
         }
         Part1.getInstance().continueGame();
      }
      
      public function enterToDoubleEggPanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_doubleEggPanel)
            {
               _doubleEggPanel = new DoubleEggPanel();
            }
            if(!getChildByName(_doubleEggPanel.name))
            {
               addChild(_doubleEggPanel);
            }
         },"DoubleEggPanel");
      }
      
      public function clearDoubleEggPanel() : void
      {
         if(_doubleEggPanel)
         {
            _doubleEggPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("DoubleEgg");
         Part1.getInstance().continueGame();
      }
      
      public function enterToZhuoquGiftPanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_zhuoquGiftPanel)
            {
               _zhuoquGiftPanel = new CodekeyZhuoquGiftBagPanel();
            }
            if(!getChildByName(_zhuoquGiftPanel.name))
            {
               addChild(_zhuoquGiftPanel);
            }
         },"CodekeyZhuoquGiftBagPanel");
      }
      
      public function enterToRealNamePanel(param1:UIBtnEvent = null) : void
      {
         var e:UIBtnEvent = param1;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_realNamePanel)
            {
               _realNamePanel = new RealNamePanel();
            }
            if(!getChildByName(_realNamePanel.name))
            {
               addChild(_realNamePanel);
            }
         },"RealNamePanel");
      }
      
      public function clearZhuoquGiftPanel() : void
      {
         if(_zhuoquGiftPanel)
         {
            _zhuoquGiftPanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("codekeyzhuoqu");
         Part1.getInstance().continueGame();
      }
      
      public function clearRealNamePanel() : void
      {
         if(_realNamePanel)
         {
            _realNamePanel = null;
         }
         LoadPart.getInstance().unLoadOnePart("realname");
         Part1.getInstance().continueGame();
      }
      
      private function enterToSignPanel(param1:UIBtnEvent) : void
      {
         enterToNewSign();
      }
      
      private function enterToFarm(param1:UIBtnEvent) : void
      {
         openFarm();
      }
      
      private function enterToRecaptureGold(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         Part1.getInstance().stopGame();
         LoadPart.getInstance().loadOnePart1(function():void
         {
            if(!_recaptureGoldGame)
            {
               _recaptureGoldGame = new RecaptureGoldMain();
            }
            if(!getChildByName(_recaptureGoldGame.name))
            {
               addChild(_recaptureGoldGame);
            }
            removeChild(_externalPanel);
         },"recaptureGold");
      }
      
      private function deleItem(param1:UIPassiveEvent) : void
      {
         _internalPanel.removeEquipmentFromPackageCell(param1.data);
      }
      
      private function getMinHunTangNum(param1:UIPassiveEvent) : void
      {
         var _loc2_:* = null;
         var _loc3_:int = 0;
         for each(_loc2_ in _player1.playerVO.packageEquipmentVOs)
         {
            if(_loc2_)
            {
               if(_loc2_.className == "Potion_MiHunTang")
               {
                  _loc3_ += (_loc2_ as PotionEquipmentVO).num;
               }
            }
         }
         if(_player2)
         {
            for each(_loc2_ in _player2.playerVO.packageEquipmentVOs)
            {
               if(_loc2_)
               {
                  if(_loc2_.className == "Potion_MiHunTang")
                  {
                     _loc3_ += (_loc2_ as PotionEquipmentVO).num;
                  }
               }
            }
         }
         param1.data(_loc3_);
      }
      
      private function cutOneMinHunTang(param1:UIPassiveEvent) : void
      {
         if(MyFunction.getInstance().minusEquipmentVOs(_player1.playerVO.packageEquipmentVOs,1,11000001) == 0)
         {
            refresh(2);
            param1.data("pass");
            return;
         }
         if(Boolean(_player2) && MyFunction.getInstance().minusEquipmentVOs(_player2.playerVO.packageEquipmentVOs,1,11000001) == 0)
         {
            refresh(2);
            param1.data("pass");
            return;
         }
         param1.data("noPass");
      }
      
      private function chooseSkill(param1:String, param2:Player) : SkillVO
      {
         if(param2)
         {
            if(param2.playerVO)
            {
               for each(var _loc3_ in param2.playerVO.skillVOs)
               {
                  if(_loc3_)
                  {
                     if(_loc3_.name == param1)
                     {
                        return _loc3_;
                     }
                  }
               }
               if(param2.playerVO.pet)
               {
                  if(param2.playerVO.pet.petEquipmentVO)
                  {
                     if(param2.playerVO.pet.petEquipmentVO.activeSkillVO.name == param1)
                     {
                        return param2.playerVO.pet.petEquipmentVO.activeSkillVO;
                     }
                  }
               }
            }
         }
         return null;
      }
      
      private function addExperience(param1:Player, param2:int) : void
      {
         MyFunction.getInstance().addPlayerExperience(param1,param2);
         MyFunction.getInstance().addPetExperience(param1,param2);
      }
      
      private function onPayEventHandler(param1:PayEvent) : void
      {
         var _loc3_:VipVO = null;
         var _loc2_:Object = null;
         switch(param1.type)
         {
            case "logsuccess":
               break;
            case "usePayApi":
               if(_externalPanel && _externalPanel.middlePanel)
               {
                  _externalPanel.middlePanel.shopBtn.isLock = false;
               }
               _isCanUseShopWall = true;
               break;
            case "incMoney":
               if(param1.data !== null && !(param1.data is Boolean))
               {
                  if(_shopWall)
                  {
                     _shopWall.currentTicketPointText.text = param1.data.balance.toString();
                  }
                  CurrentTicketPointManager.getInstance().setCurrentTicketPoint(param1.data.balance.toString());
                  GameEvent.dispatchEventWith("ticketChange",param1.data.balance.toString());
               }
               break;
            case "decMoney":
               if(param1.data !== null && !(param1.data is Boolean))
               {
                  AnalogServiceHoldFunction.getInstance().decMoney(param1.data.balance);
                  if(_shopWall)
                  {
                     _shopWall.currentTicketPointText.text = param1.data.balance.toString();
                  }
                  CurrentTicketPointManager.getInstance().setCurrentTicketPoint(param1.data.balance);
                  GameEvent.dispatchEventWith("ticketChange",param1.data.balance.toString());
                  break;
               }
               AnalogServiceHoldFunction.getInstance().decMoneyError();
               break;
            case "getMoney":
               if(param1.data !== null && !(param1.data is Boolean))
               {
                  if(_shopWall)
                  {
                     _shopWall.currentTicketPointText.text = param1.data.balance.toString();
                  }
                  CurrentTicketPointManager.getInstance().setCurrentTicketPoint(param1.data.balance);
                  GameEvent.dispatchEventWith("ticketChange",param1.data.balance.toString());
               }
               break;
            case "payMoney":
               if(_shopWall)
               {
                  _shopWall.showWarningBox("充值失败！",0);
               }
               break;
            case "paiedMoney":
               if(param1.data !== null && !(param1.data is Boolean))
               {
                  GetPaiedManager.getInstance().requirSuccess(Number(param1.data.balance));
                  break;
               }
               GetPaiedManager.getInstance().requirFail();
               break;
            case "rechargedMoney":
               if(param1.data !== null && !(param1.data is Boolean))
               {
                  GetRechargeManager.getInstance().requirSuccess(Number(param1.data.balance));
                  MidautumnData.getInstance().setLoadMoney(false);
                  if(AnalogServiceHoldFunction.getInstance().getIsGettingTotalRecharged() == false)
                  {
                     return;
                  }
                  CurrentTicketPointManager.getInstance().setTotalTicketPoint(param1.data.balance);
                  _loc3_ = _player1.vipVO;
                  if(!isNaN(CurrentTicketPointManager.getInstance().getTotalTicketPoint()))
                  {
                     if(!GMData.getInstance().isGMApplication && Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine" && GamingUI.getInstance().player1)
                     {
                        if(!isNaN(CurrentTicketPointManager.getInstance().getTotalTicketPoint()) && CurrentTicketPointManager.getInstance().getTotalTicketPoint() < APIDataAnalyze.getInstance().payMoney)
                        {
                           CheatData.getInstance().addCheatDataStr("金额异常");
                           CheatData.getInstance().addCheatDataStr("金额异常");
                           CheatData.getInstance().addCheatDataStr("金额异常");
                           CheatData.getInstance().addCheatDataStr("金额异常");
                           CheatData.getInstance().addCheatDataStr("金额异常");
                        }
                     }
                  }
                  _player1.vipVO = XMLSingle.getVipVO(Number(param1.data.balance),XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().privilegeXML,TimeUtil.timeStr);
                  if(_player2)
                  {
                     _player2.vipVO = _player1.vipVO;
                  }
                  _player1.vipVO.oldDateGetGift = _loc3_.oldDateGetGift;
                  _loc2_ = NicknameData.getInstance().myDataInNicknameRankList;
                  if(_player1.vipVO.vipLevel)
                  {
                     _loc2_.nicknameType = "redNickname";
                  }
                  else
                  {
                     _loc2_.nicknameType = "whiteNickname";
                  }
                  if(_VIPPanel)
                  {
                     _VIPPanel.refreshVIPPanel(_player1.vipVO);
                  }
                  if(_internalPanel)
                  {
                     _internalPanel.isVIPState = _player1.vipVO.vipLevel;
                  }
                  AnalogServiceHoldFunction.getInstance().getRechargedSuceessFun();
                  break;
               }
               GetRechargeManager.getInstance().requirFail();
               AnalogServiceHoldFunction.getInstance().getRechargedErrorFun();
               break;
            case "payError":
               trace("/*","0|请重试!若还不行,请重新登录!!","1|程序有问题，请联系技术人员100584399!!","2|请检查,目前传进来的值等于0!!","3|游戏不存在或者没有支付接口!!","4|余额不足!!","5|出错了,请重新登录!","6|日期或者时间的格式出错了!!","*/");
               MidautumnData.getInstance().setLoadMoney(false);
               mouseChildren = true;
               mouseEnabled = true;
               if(param1.data != null)
               {
                  if(param1.data.hasOwnProperty("info"))
                  {
                     if(param1.data.info is String)
                     {
                        if(_shopWall)
                        {
                           _shopWall.showWarningBox(param1.data.info,0);
                        }
                     }
                  }
                  break;
               }
         }
      }
      
      private function getStoreStateHandler(param1:DataEvent) : void
      {
         switch(param1.data)
         {
            case "0":
               if(Boolean(GamingUI.getInstance().manBan) && GamingUI.getInstance().manBan.text)
               {
                  GamingUI.getInstance().manBan.text.text = "游戏多开了！！！";
               }
               break;
            case "1":
               AnalogServiceHoldFunction.getInstance().dealStoreState();
               break;
            default:
               GamingUI.getInstance().manBan.text.text = "抱歉！出错了！！";
         }
      }
      
      private function onShopEventHandler(param1:ShopEvent) : void
      {
         switch(param1.type)
         {
            case "shopErrorNd":
               errorFun(param1.data);
               break;
            case "shopBuyNd":
               buySuccFun(param1.data);
               break;
            case "shopGetList":
               getSuccFun(param1.data as Array);
         }
      }
      
      private function errorFun(param1:Object) : void
      {
         trace("eId:" + param1.eId + "  message:" + param1.msg + "\n");
         AnalogServiceHoldFunction.getInstance().buyErrorByData(param1);
      }
      
      private function getSuccFun(param1:Array) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Object = null;
         if(param1 == null)
         {
            trace("获取物品列表时，返回空值了\n");
            return;
         }
         if(param1.length == 0)
         {
            trace("无商品列表\n");
            return;
         }
         var _loc2_:int = !!param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = param1[_loc4_];
            trace("propNum:" + _loc4_ + "  propId:" + _loc3_.propId + "  price:" + _loc3_.price + "   propType:" + _loc3_.propType + "\n");
            _loc4_++;
         }
      }
      
      private function buySuccFun(param1:Object) : void
      {
         var _loc2_:int = 0;
         var _loc3_:XMLList = null;
         var _loc4_:int = 0;
         try
         {
            _loc2_ = 0;
            trace("propId:",param1.propId);
            _loc3_ = XMLSingle.getInstance().equipmentXML.item;
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length())
            {
               if(int(_loc3_[_loc4_].@ticketId) == param1.propId)
               {
                  _loc2_ = int(_loc3_[_loc4_].@ticketPrice);
               }
               _loc4_++;
            }
            APIDataAnalyze.getInstance().payMoney = APIDataAnalyze.getInstance().payMoney + param1.count * _loc2_;
         }
         catch(e:Error)
         {
         }
         AnalogServiceHoldFunction.getInstance().buySuccessByData(param1,param1.balance);
         if(_shopWall)
         {
            _shopWall.currentTicketPointText.text = param1.balance.toString();
         }
         CurrentTicketPointManager.getInstance().setCurrentTicketPoint(param1.balance);
         GameEvent.dispatchEventWith("ticketChange",param1.balance.toString());
         trace("propId:" + param1.propId + "  count:" + param1.count + "   balance:" + param1.balance + "   tag:" + param1.tag + "\n");
      }
      
      public function getVersionControl() : VersionControl
      {
         return Part1.getInstance().getVersionControl();
      }
      
      public function getAPI4399() : API_4399
      {
         return Part1.getInstance().getApi4399();
      }
      
      public function getVerifyUserState() : VerifyUserState
      {
         return _verifyUserState;
      }
      
      public function get mainLineTaskData() : MainLineTaskData
      {
         return _mainLineTaskData;
      }
      
      public function get loadQueue() : LoadQueue
      {
         return _loadQueue;
      }
      
      public function openMainLineTaskPanel() : void
      {
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_mainLineTaskPanel);
         _mainLineTaskPanel = new MainLineTaskPanel();
         _mainLineTaskPanel.setGamingUI(this);
         _mainLineTaskPanel.init(_mainLineTaskData);
         addChild(_mainLineTaskPanel);
         removeChild(_externalPanel);
      }
      
      public function openOnLineGiftBagPanel() : void
      {
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_onLineGiftBagPanel);
         _onLineGiftBagPanel = new OnLineGiftBagPanel();
         _onLineGiftBagPanel.gamingUI = this;
         _onLineGiftBagPanel.init(OnLineGiftBagData.getInstance());
         addChild(_onLineGiftBagPanel);
      }
      
      public function openOnLineGiftBagPanel2(param1:MovieClip) : void
      {
         ClearUtil.clearObject(_onLineGiftBagPanel);
         _onLineGiftBagPanel = new OnLineGiftBagPanel();
         _onLineGiftBagPanel.gamingUI = this;
         _onLineGiftBagPanel.init2(OnLineGiftBagData.getInstance(),param1);
      }
      
      public function closeOnLineGiftBagPanel2() : void
      {
         if(_onLineGiftBagPanel)
         {
            _onLineGiftBagPanel.clear();
         }
         _onLineGiftBagPanel = null;
      }
      
      public function closeOnLineGiftBagPanel() : void
      {
         Part1.getInstance().continueGame();
         if(_onLineGiftBagPanel)
         {
            _onLineGiftBagPanel.clear();
         }
         _onLineGiftBagPanel = null;
      }
      
      public function openActivityPanel() : void
      {
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_activityPanel);
         _activityPanel = new ActivityPanel();
         _activityPanel.setGamingUI(this);
         _activityPanel.init();
         addChild(_activityPanel);
      }
      
      public function closeActivityPanel() : void
      {
         Part1.getInstance().continueGame();
         if(_activityPanel)
         {
            _activityPanel.clear();
         }
         _activityPanel = null;
      }
      
      public function closeMainLineTaskPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_mainLineTaskPanel);
         _mainLineTaskPanel = null;
         addChild(_externalPanel);
      }
      
      public function openRechargePanel() : void
      {
         ClearUtil.clearObject(_rechargePanel);
         _rechargePanel = new RechargePanel();
         Part1.getInstance().stage.addChild(_rechargePanel);
      }
      
      public function closeRechargePanel() : void
      {
         if(_rechargePanel)
         {
            _rechargePanel.clear();
         }
         _rechargePanel = null;
      }
      
      public function taskEvent(param1:String) : void
      {
         TaskFunction.getInstance().addNewTaskGoalVOByString(param1);
         NewMainTaskData.getInstance().addTaskGoalByGameEventStr(param1);
         _smallAssistantSaveData.getActiveTasksSaveData().addTaskGoalByGameEventStr(param1);
      }
      
      public function addMainLineTaskGoalGameEventStr(param1:String) : void
      {
         NewMainTaskData.getInstance().addTaskGoalByGameEventStr(param1);
         _smallAssistantSaveData.getActiveTasksSaveData().addTaskGoalByGameEventStr(param1);
      }
      
      public function getSharedXMLs() : SharedXMLs
      {
         return _sharedXMLs;
      }
      
      public function closeInternalPanel() : void
      {
         if(_internalPanel)
         {
            _internalPanel.clear();
            _internalPanel = null;
         }
         addChild(_externalPanel);
         Part1.getInstance().continueGame();
      }
      
      public function closeRankList() : void
      {
         GetDataFunction.getInstance().bIsRank = false;
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_rankListPanel);
         _rankListPanel = null;
         addChild(_externalPanel);
         LoadPart.getInstance().unLoadOnePart("rankList");
      }
      
      public function openWorldBossRankList() : void
      {
         Part1.getInstance().stopGame();
         MyFunction2.loadXMLAndGetServerTimeFunction("worldBoss/worldBoss",function(param1:XML, param2:String):void
         {
            var _loc4_:WorldBossNeedSomeData = new WorldBossNeedSomeData();
            new GetWorldBossData().getData(param2,param1,true,_loc4_);
            WorldBossSaveData.getInstance().isResetData(param2,_loc4_.cycleLength,_loc4_.effectiveTime);
            var _loc5_:int = _loc4_.rankId;
            _loc4_.clear();
            var _loc3_:Number = new WorldBossFun().getTotalBossHurt();
            openWorldBossRankListPanel(_loc5_,_loc3_);
         },function():void
         {
            showMessageTip("网络连接失败");
         },true);
      }
      
      public function openPKMode2RankList1() : void
      {
         Part1.getInstance().stopGame();
         MyFunction2.loadXMLAndGetServerTimeFunction("pkMode2",function(param1:XML, param2:String):void
         {
            var _loc6_:PKMode2Data = new PKMode2Data();
            _loc6_.initByXML(param1.pk.(@pkType == "onePK")[0]);
            player1.getPKMode2VO1().resetPKResultDataByTime(param2,_loc6_.getResetTimeLong(),_loc6_.getRefrenceTime());
            var _loc5_:int = _loc6_.getCurrentRankId(param2,true);
            ClearUtil.clearObject(_loc6_);
            _loc6_ = null;
            System.disposeXML(param1);
            param1 = null;
            var _loc4_:uint = player1.getPKMode2VO1().getWinPKResultNum();
            openPKMode2RankList1Panel(_loc5_,_loc4_);
         },function():void
         {
            showMessageTip("网络连接失败");
         },true);
      }
      
      public function closeWorldBossPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_worldBossPanel);
         _worldBossPanel = null;
         addChild(_externalPanel);
         Part1.getInstance().setTipXY(250,150);
      }
      
      public function closeHonourHallPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_honourHallPanel);
         _honourHallPanel = null;
         if(!_EndlessLevelPanel)
         {
            addChild(_externalPanel);
         }
         LoadPart.getInstance().unLoadOnePart("honourHall");
      }
      
      public function initListSaveXML() : void
      {
         var _loc1_:ExportUI = new ExportUI();
         _loc1_.Export(_player1,_player2,_publicStorageEquipments,_automaticPetsData,_smallAssistantSaveData,_svActivitySaveData,_player1.vipVO);
      }
      
      public function addAutomaticSave(param1:XML) : void
      {
         var _loc2_:uint = _submitAutomaticPetScore.submitAutomaticPetScore(player1,player2);
         if(_loc2_)
         {
            param1.Data[0].AutoPets[0].@s = _loc2_;
         }
      }
      
      public function createNewTitle() : String
      {
         var _loc1_:String = getPlayerName(_player1.playerVO.playerType);
         _loc1_ += "-等级:" + _player1.playerVO.level;
         if(_player2)
         {
            _loc1_ += "\n";
            _loc1_ += getPlayerName(_player2.playerVO.playerType);
            _loc1_ += "-等级:" + _player2.playerVO.level;
         }
         return _loc1_;
      }
      
      private function getPlayerName(param1:String) : String
      {
         switch(param1)
         {
            case "SunWuKong":
               return "孙悟空";
            case "BaiLongMa":
               return "白龙马";
            case "ErLangShen":
               return "二郎神";
            case "ChangE":
               return "嫦娥";
            case "Fox":
               return "灵狐";
            case "TieShan":
               return "铁扇公主";
            case "Houyi":
               return "后羿";
            case "ZiXia":
               return "紫霞仙子";
            default:
               throw new Error("获取人物名称出错");
         }
      }
      
      public function getEnterFrameTime() : EnterFrameTime
      {
         return Part1.getInstance().getEnterFrameTime();
      }
      
      public function updateNewestTimeStrFromSever(param1:String) : void
      {
         _newestTimeStrFromServer = param1;
      }
      
      public function getNewestTimeStrFromSever() : String
      {
         return _newestTimeStrFromServer;
      }
      
      public function getAutomaticPetsData() : AutomaticPetsData
      {
         return _automaticPetsData;
      }
      
      public function setAutomaticPetsData(param1:AutomaticPetsData) : void
      {
         _automaticPetsData = param1;
      }
      
      public function getSmallAssistantSaveData() : SmallAssistantSaveData
      {
         return _smallAssistantSaveData;
      }
      
      public function setSmallAssistantSaveData(param1:SmallAssistantSaveData) : void
      {
         _smallAssistantSaveData = param1;
      }
      
      public function getSvActivitySaveData() : SVActivitySaveData
      {
         return _svActivitySaveData;
      }
      
      public function setSvActivitySaveData(param1:SVActivitySaveData) : void
      {
         _svActivitySaveData = param1;
      }
      
      public function openSocietyNpcBox() : void
      {
         var openSocietyNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_openNpcSelectBox);
         if(player1.getSocietyDataVO().getSocietyId())
         {
            _openNpcSelectBox = new OpenNpcSelectBox("societySystemNpc2");
            openExSocietyPanel();
         }
         else
         {
            _openNpcSelectBox = new OpenNpcSelectBox("societySystemNpc1");
         }
         addChild(_openNpcSelectBox);
         openSocietyNpcListener = new OpenNpcListener1();
         _openNpcSelectBox.addOpenNpcListener(openSocietyNpcListener);
         openSocietyNpcListener.openNpcFun = function(param1:String):void
         {
            if(_societySystemListener)
            {
               _societySystemListener.getTheSocietyDataSuccessFun = null;
            }
            switch(param1)
            {
               case "createSocietyPanel":
                  openSocietyListPanel("createSociety");
                  break;
               case "joinSocietyPanel":
                  openSocietyListPanel("applyJoinSociety");
                  break;
               case "societyListPanel":
                  openSocietyListPanel("lookUpSocietyList");
                  break;
               case "mySocietyPanel":
                  openMySocietyPanel();
            }
         };
      }
      
      public function closeSocietySystem() : void
      {
         Part1.getInstance().continueGame();
         unLockGamingUI();
         if(Boolean(_openNpcSelectBox) && (_openNpcSelectBox.getOption() == "societySystemNpc1" || _openNpcSelectBox.getOption() == "societySystemNpc2"))
         {
            ClearUtil.clearObject(_openNpcSelectBox);
            _openNpcSelectBox = null;
         }
         ClearUtil.clearObject(_societyListPanel);
         _societyListPanel = null;
         ClearUtil.clearObject(_mySocietyPanel);
         _mySocietyPanel = null;
         ClearUtil.clearObject(_exSocietyPanel);
         _exSocietyPanel = null;
         ClearUtil.clearObject(_societySystemListener);
         _societySystemListener = null;
         ClearUtil.clearObject(_societySystem);
         _societySystem = null;
         addChildAt(_externalPanel,0);
         Part1.getInstance().continueGame();
      }
      
      public function openSocietyListPanel(param1:String) : void
      {
         var gamingUI:GamingUI;
         var type:String = param1;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_societyListPanel);
            _societyListPanel = new SocietyListPanel();
            addChild(_societyListPanel);
            _societyListPanel.setGamingUI(gamingUI);
            _societyListPanel.setSocietySystem(_societySystem);
            _societyListPanel.setSocietySystemXML(_societySystem.getSocietySystemXML());
            _societyListPanel.setSocketManager(_societySystem.getSocketManager());
            _societyListPanel.setVersionControl(getVersionControl());
            _societyListPanel.init(type);
         },"rankList");
      }
      
      public function openMySocietyPanel() : void
      {
         var gamingUI:GamingUI;
         Part1.getInstance().stopGame();
         gamingUI = this;
         LoadPart.getInstance().loadOnePart1(function():void
         {
            ClearUtil.clearObject(_mySocietyPanel);
            _mySocietyPanel = new MySocietyPanel();
            addChild(_mySocietyPanel);
            _mySocietyPanel.setGamingUI(gamingUI);
            _mySocietyPanel.setSocietySystem(_societySystem);
            _mySocietyPanel.setSocietySystemXML(_societySystem.getSocietySystemXML());
            _mySocietyPanel.setSocketManager(_societySystem.getSocketManager());
            _mySocietyPanel.setVersionControl(getVersionControl());
            _mySocietyPanel.init();
         },"rankList");
      }
      
      public function openExSocietyPanel() : void
      {
         Part1.getInstance().stopGame();
         if(_exSocietyPanel == null)
         {
            ClearUtil.clearObject(_exSocietyPanel);
            _exSocietyPanel = new ExSocietyPanel();
            _externalPanel.addChild(_exSocietyPanel);
            _exSocietyPanel.setGamingUI(this);
            _exSocietyPanel.setSocietySystem(_societySystem);
            _exSocietyPanel.setSocietySystemXML(_societySystem.getSocietySystemXML());
            _exSocietyPanel.setSocketManager(_societySystem.getSocketManager());
            _exSocietyPanel.setVersionControl(getVersionControl());
            _exSocietyPanel.init();
         }
      }
      
      public function closeSocietyListPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_societyListPanel);
         _societyListPanel = null;
         addChildAt(_externalPanel,0);
      }
      
      public function closeMySocietyPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_mySocietyPanel);
         _mySocietyPanel = null;
         addChildAt(_externalPanel,0);
         if(_exSocietyPanel)
         {
            _exSocietyPanel.refreshRemainTimeOfUnableSendChat();
            _exSocietyPanel.hideHaveNewChatInforTip();
         }
      }
      
      public function closeExSocietyPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_exSocietyPanel);
         _exSocietyPanel = null;
         addChildAt(_externalPanel,0);
      }
      
      public function getSocietySystem() : SocietySystem
      {
         return _societySystem;
      }
      
      public function openCollectTimePanel() : void
      {
         Part1.getInstance().stopGame();
         ClearUtil.clearObject(_collectTimePanel);
         _collectTimePanel = new CollectTimePanel();
         addChild(_collectTimePanel);
         _collectTimePanel.setGamingUI(this);
         _collectTimePanel.setVersionControl(getVersionControl());
         _collectTimePanel.init();
      }
      
      public function closeCollectTimePanel() : void
      {
         Part1.getInstance().continueGame();
         if(_collectTimePanel)
         {
            _collectTimePanel.clear();
         }
         _collectTimePanel = null;
      }
      
      public function openBossChooceUI() : void
      {
         Part1.getInstance().stopGame();
         if(_bossChooseUI == null)
         {
            _bossChooseUI = new BossChooceUI();
            _bossChooseUI.init(getVersionControl(),Part1.getInstance().getLoadUI());
            addChild(_bossChooseUI);
         }
      }
      
      public function closeBossChooseUI() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_bossChooseUI);
         _bossChooseUI = null;
      }
      
      public function openXiangMoLevelPanel() : void
      {
         Part1.getInstance().stopGame();
         if(_xiangMoLevelPanel == null)
         {
            _xiangMoLevelPanel = new XiangMoLevelPanel();
            _xiangMoLevelPanel.init(getVersionControl(),Part1.getInstance().getLoadUI(),this);
            addChild(_xiangMoLevelPanel);
         }
      }
      
      public function closeXiangMoLevelPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_xiangMoLevelPanel);
         _xiangMoLevelPanel = null;
      }
      
      public function openPreciousView() : void
      {
         Part1.getInstance().stopGame();
         if(_preciousPanel == null)
         {
            _preciousPanel = new PreciousView();
            _preciousPanel.init(getVersionControl(),Part1.getInstance().getLoadUI(),this);
            addChild(_preciousPanel);
         }
      }
      
      public function closePreciousView() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_preciousPanel);
         _preciousPanel = null;
      }
      
      public function openNewTask(param1:int, param2:String) : void
      {
         Part1.getInstance().stopGame();
         if(_newtaskPanel == null)
         {
            _newtaskPanel = new NewTaskPanel(param1,param2);
            addChild(_newtaskPanel);
         }
      }
      
      public function closeNewTask() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_newtaskPanel);
         _newtaskPanel = null;
      }
      
      public function openEndlessPanel() : void
      {
         if(TimeUtil.timeStr == "")
         {
            GamingUI.getInstance().showMessageTip("时间获取失败，请返回主菜单重新进入");
            return;
         }
         Part1.getInstance().stopGame();
         if(_EndlessLevelPanel == null)
         {
            _EndlessLevelPanel = new EndLessMapUI();
            _EndlessLevelPanel.init(getVersionControl(),Part1.getInstance().getLoadUI(),this);
            addChild(_EndlessLevelPanel);
         }
      }
      
      public function closeEndlessPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_EndlessLevelPanel);
         _EndlessLevelPanel = null;
      }
      
      private function closeEndlessRankPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_EndlessRankPanel);
         _EndlessRankPanel = null;
      }
      
      public function closeNo1Rank() : void
      {
         GetDataFunction.getInstance().bIsRank = false;
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_NewAllRank);
         _NewAllRank = null;
      }
      
      public function openAutomaticPetPanel() : void
      {
         Part1.getInstance().stopGame();
         if(_automaticPetPanel == null)
         {
            _automaticPetPanel = new AutomaticPetPanel();
            _automaticPetPanel.init(_automaticPetsData,getVersionControl(),Part1.getInstance().getLoadUI(),this);
            addChild(_automaticPetPanel);
         }
      }
      
      public function closeAutomaticPetPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_automaticPetPanel);
         _automaticPetPanel = null;
      }
      
      public function openMountPanel() : void
      {
         Part1.getInstance().stopGame();
         if(_mountsPanel == null)
         {
            _mountsPanel = new MountsPanel();
            _mountsPanel.init(_player1.getMountsVO(),this,getVersionControl(),Part1.getInstance().getLoadUI());
            addChild(_mountsPanel);
         }
      }
      
      public function closeMountPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_mountsPanel);
         _mountsPanel = null;
      }
      
      public function getMountsPanel() : MountsPanel
      {
         return _mountsPanel;
      }
      
      public function openSvActivityPanel() : void
      {
         Part1.getInstance().stopGame();
         if(_svActivityPanel == null)
         {
            _svActivityPanel = new SVActivityPanel();
            _svActivityPanel.init(_svActivitySaveData,Part1.getInstance().getLoadUI(),getVersionControl(),this);
            addChild(_svActivityPanel);
         }
      }
      
      public function closeSvActivityPanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_svActivityPanel);
         _svActivityPanel = null;
      }
      
      public function openOnePlayerToTwo() : void
      {
         if(_onePlayertoTwoPanel == null)
         {
            _onePlayertoTwoPanel = new OnePlayerToTwoPanel();
            _onePlayertoTwoPanel.init(player1,this,getVersionControl(),Part1.getInstance().getLoadUI());
            addChild(_onePlayertoTwoPanel);
         }
      }
      
      public function closeOnePlayerToTwo() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_onePlayertoTwoPanel);
         _onePlayertoTwoPanel = null;
      }
      
      public function openResetPlayerTypePanel() : void
      {
         var openOneToTwoNpcListener:OpenNpcListener1;
         var openResetNpcListener:OpenNpcListener1;
         Part1.getInstance().stopGame();
         if(player2 == null)
         {
            ClearUtil.clearObject(_openNpcSelectBox);
            _openNpcSelectBox = new OpenNpcSelectBox("onePlayerToTwo");
            addChild(_openNpcSelectBox);
            openOneToTwoNpcListener = new OpenNpcListener1();
            openOneToTwoNpcListener.openNpcFun = function(param1:String):void
            {
               switch(param1)
               {
                  case "resetPlayer1":
                     openResetPlayerTypePanel2(player1);
                     break;
                  case "onePlayerToTwo":
                     openOnePlayerToTwo();
               }
            };
            _openNpcSelectBox.addOpenNpcListener(openOneToTwoNpcListener);
            return;
         }
         ClearUtil.clearObject(_openNpcSelectBox);
         _openNpcSelectBox = new OpenNpcSelectBox("resetNpc");
         addChild(_openNpcSelectBox);
         openResetNpcListener = new OpenNpcListener1();
         openResetNpcListener.openNpcFun = function(param1:String):void
         {
            switch(param1)
            {
               case "resetPlayer1":
                  openResetPlayerTypePanel2(player1);
                  break;
               case "resetPlayer2":
                  openResetPlayerTypePanel2(player2);
            }
         };
         _openNpcSelectBox.addOpenNpcListener(openResetNpcListener);
      }
      
      private function openResetPlayerTypePanel2(param1:Player) : void
      {
         if(_resetPlayerTypePanel == null)
         {
            _resetPlayerTypePanel = new ResetPlayerTypePanel();
            _resetPlayerTypePanel.init(param1,this,getVersionControl(),Part1.getInstance().getLoadUI());
            addChild(_resetPlayerTypePanel);
         }
      }
      
      public function closeResetPlayerTypePanel() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_resetPlayerTypePanel);
         _resetPlayerTypePanel = null;
      }
      
      public function openPetBossChooseUI() : void
      {
         Part1.getInstance().stopGame();
         if(_petBossChooseUI == null)
         {
            _petBossChooseUI = new PetBossChooceUI();
            _petBossChooseUI.init(getVersionControl(),Part1.getInstance().getLoadUI());
            addChild(_petBossChooseUI);
         }
      }
      
      public function closePetBossChooseUI() : void
      {
         Part1.getInstance().continueGame();
         ClearUtil.clearObject(_petBossChooseUI);
         _petBossChooseUI = null;
      }
      
      public function lockGamingUI(param1:String) : void
      {
         mouseChildren = false;
         mouseEnabled = false;
         if(Boolean(manBan) && Boolean(manBan.text))
         {
            manBan.text.text = param1;
         }
      }
      
      public function unLockGamingUI() : void
      {
         mouseChildren = true;
         mouseEnabled = true;
      }
      
      public function showMessageTip(param1:String) : void
      {
         if(_messageTipShow == null)
         {
            _messageTipShow = MyFunction2.returnShowByClassName("MessageTip") as MovieClip;
         }
         if(_messageTip == null)
         {
            _messageTip = new MessageTip();
            _messageTip.setShow(_messageTipShow,_messageTipShow["show"],_messageTipShow["show"]["text"],showMessageTipEnd);
         }
         Part1.getInstance().stage.addChild(_messageTipShow);
         _messageTip.setText(param1);
         _messageTipShow.x = (Part1.getInstance().stage.stageWidth - _messageTipShow.width) / 2;
         _messageTipShow.y = Part1.getInstance().stage.stageHeight - _messageTipShow.height;
         _messageTip.play();
      }
      
      private function showMessageTipEnd() : void
      {
         if(_messageTipShow.parent)
         {
            _messageTipShow.parent.removeChild(_messageTipShow);
         }
         ClearUtil.clearObject(_messageTip);
         _messageTip = null;
      }
      
      public function closeAllUIPanel() : void
      {
         closeActivityPanel();
         closeAutomaticPetPanel();
         closeBossChooseUI();
         closeCollectTimePanel();
         closeExSocietyPanel();
         closeHonourHallPanel();
         closeInternalPanel();
         closeMainLineTaskPanel();
         closeMySocietyPanel();
         closeOnLineGiftBagPanel();
         closePetBossChooseUI();
         closeRankList();
         closeRechargePanel();
         closeShiTuPanel();
         closeSocietyListPanel();
         closeSocietySystem();
         closeWorldBossPanel();
         closeXiangMoLevelPanel();
         closeEndlessPanel();
         closeEndlessRankPanel();
         closeNo1Rank();
         _smallAssistant.closeSmallAssistantPanel();
      }
   }
}

