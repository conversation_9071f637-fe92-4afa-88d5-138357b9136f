package UI.newGuide.GuideEveryDay
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.Task.TaskVO.MTaskVO;
   import UI.newGuide.NewGuidePanel;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GuideEveryBtnShell
   {
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_guideeverypanel:GuideEveryPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_DoneBtn:ButtonLogicShell2;
      
      private var m_GetBtn:ButtonLogicShell2;
      
      private var m_GoBtn:ButtonLogicShell2;
      
      private var m_data:MTaskVO;
      
      public function GuideEveryBtnShell()
      {
         super();
      }
      
      public function init(param1:NewGuidePanel, param2:GuideEveryPanel, param3:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_guideeverypanel = param2;
         m_show = param3;
         m_mc = m_show["listpanel"];
         initParams();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_DoneBtn);
         m_DoneBtn = null;
         ClearUtil.clearObject(m_GetBtn);
         m_GetBtn = null;
         ClearUtil.clearObject(m_GoBtn);
         m_GoBtn = null;
      }
      
      private function initParams() : void
      {
         m_DoneBtn = new ButtonLogicShell2();
         m_GetBtn = new ButtonLogicShell2();
         m_GoBtn = new ButtonLogicShell2();
         m_DoneBtn.setShow(m_mc["lookinfo"]);
         m_GetBtn.setShow(m_mc["getbtn"]);
         m_GoBtn.setShow(m_mc["gobtn"]);
         m_DoneBtn.getShow().visible = false;
         m_GetBtn.getShow().visible = false;
         m_GoBtn.getShow().visible = false;
      }
      
      public function show() : void
      {
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
         if(m_DoneBtn == null)
         {
            m_DoneBtn = new ButtonLogicShell2();
            m_DoneBtn.setShow(m_mc["donebtn"]);
            m_DoneBtn.getShow().visible = false;
         }
         if(m_GetBtn == null)
         {
            m_GetBtn = new ButtonLogicShell2();
            m_GetBtn.setShow(m_mc["getbtn"]);
            m_GetBtn.getShow().visible = false;
         }
         if(m_GoBtn == null)
         {
            m_GoBtn = new ButtonLogicShell2();
            m_GoBtn.setShow(m_mc["gobtn"]);
            m_GoBtn.getShow().visible = false;
         }
      }
      
      public function hide() : void
      {
         m_mc.removeEventListener("clickButton",clickButton,true);
         m_data = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_GetBtn:
               m_newguidepanel.refreshState();
               GamingUI.getInstance().openNewTask(m_newguidepanel.currentType,String(m_data.id));
               break;
            case m_GoBtn:
               if(m_data.gotoInfo)
               {
                  if(m_data.gotoInfo.type == "2" || m_data.gotoInfo.type == "3" || m_data.gotoInfo.type == "4" || m_data.gotoInfo.type == "5" || m_data.gotoInfo.type == "6" || m_data.gotoInfo.type == "7" || m_data.gotoInfo.type == "8" || m_data.gotoInfo.type == "9" || m_data.gotoInfo.type == "10")
                  {
                     if("NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(LevelModeSaveData.getInstance().getMapLevel() >= 16)
                        {
                           Part1.getInstance().closeCityMap();
                           Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("关卡未开启");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(GamingUI.getInstance().player1.playerVO.level >= 30)
                        {
                           Part1.getInstance().closeCityMap();
                           Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap4.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(GamingUI.getInstance().player1.playerVO.level >= 60)
                        {
                           if(LevelModeSaveData.getInstance().getGodLevel() >= int(m_data.gotoInfo.gotobtnname.substr(12,m_data.gotoInfo.gotobtnname.length)))
                           {
                              Part1.getInstance().closeCityMap();
                              Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                           }
                           else
                           {
                              GamingUI.getInstance().showMessageTip("关卡未开启");
                           }
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("60级之后才能挑战");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" == m_data.gotoInfo.xmlpath)
                     {
                        Part1.getInstance().closeCityMap();
                        Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                     }
                  }
                  else if(m_data.gotoInfo.type == "1")
                  {
                     if(GamingUI.getInstance().player1.playerVO.level >= 30)
                     {
                        GamingUI.getInstance().openPreciousView();
                     }
                     else
                     {
                        GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                     }
                  }
                  m_newguidepanel.refreshState();
               }
               break;
            case m_DoneBtn:
               m_newguidepanel.refreshState();
               GamingUI.getInstance().openNewTask(m_newguidepanel.currentType,String(m_data.id));
         }
      }
      
      public function refreshScript(param1:MTaskVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         m_data = param1;
         allhide();
         if(m_data)
         {
            _loc3_ = 0;
            _loc2_ = 0;
            _loc4_ = 0;
            _loc4_ = 0;
            while(_loc4_ < m_data.currentTaskGoalVO_nums.length)
            {
               _loc3_ += m_data.currentTaskGoalVO_nums[_loc4_];
               _loc4_++;
            }
            _loc4_ = 0;
            while(_loc4_ < m_data.taskGoalVO_nums.length)
            {
               _loc2_ += m_data.taskGoalVO_nums[_loc4_];
               _loc4_++;
            }
            if(_loc3_ >= _loc2_)
            {
               showGet();
            }
            else
            {
               showGO();
            }
         }
      }
      
      private function allhide() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showDone() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showGet() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = true;
            m_GetBtn.unLock();
         }
      }
      
      private function showGO() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = true;
            m_GoBtn.unLock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
   }
}

