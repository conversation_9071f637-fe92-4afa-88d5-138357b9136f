package UI.WorldBoss.Boss.CreateOwnImageBoss
{
   import UI.WorldBoss.Boss.AbleAttackedBoss.AttackUpgradeBoss;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao;
   import UI.WorldBoss.DaZhao.NotAttackDaZhao.NotAttackDaZhao;
   import YJFY.ShowLogicShell.PlayEndListener;
   
   public class CreateOwnImageBoss extends AttackUpgradeBoss
   {
      public function CreateOwnImageBoss()
      {
         super();
      }
      
      override protected function runNotAttackDaZhao(param1:NotAttackDaZhao) : void
      {
         var _loc2_:CreateOwnImageAniData = null;
         if(param1 is CreateOwnImageDaZhao)
         {
            _loc2_ = new CreateOwnImageAniData();
            _loc2_.createImages = (param1 as CreateOwnImageDaZhao).createImageBosses(this,m_myLoader,m_worldBoss,m_view,m_fightStage,m_soundManager,m_xml.imageBoss[0]);
            _loc2_.creator = this;
            m_animationQueueData.addAnimationData(_loc2_);
            return;
         }
         throw new Error("daZhao error in runNotAttackDaZhao");
      }
      
      public function createOwnImageAnimation(param1:Vector.<Boss>, param2:Function) : void
      {
         var _loc5_:PlayEndListener = new PlayEndListener();
         _loc5_.mc = m_mc;
         _loc5_.playEndFun = param2;
         m_mc.addNextStopListener(_loc5_);
         var _loc4_:ShowOwnImagesListener = new ShowOwnImagesListener();
         _loc4_.mc = m_mc;
         _loc4_.createBosses = param1;
         m_mc.addFrameLabelListener(_loc4_);
         var _loc3_:String = getLevelStr();
         m_mc.gotoAndPlay("createOwnImages");
         setLevelStr(_loc3_);
      }
   }
}

