package UI.ExternalUI
{
   public class MiddlePanelListener implements IMiddlePanelListener
   {
      public var tranToFightStateFun:Function;
      
      public var tranToNormalStateFun:Function;
      
      public function MiddlePanelListener()
      {
         super();
      }
      
      public function clear() : void
      {
         tranToFightStateFun = null;
         tranToNormalStateFun = null;
      }
      
      public function tranToFightState(param1:MiddlePanel) : void
      {
         if(Boolean(tranToFightStateFun))
         {
            tranToFightStateFun(param1);
         }
      }
      
      public function tranToNormalState(param1:MiddlePanel) : void
      {
         if(<PERSON><PERSON>an(tranToNormalStateFun))
         {
            tranToNormalStateFun(param1);
         }
      }
   }
}

