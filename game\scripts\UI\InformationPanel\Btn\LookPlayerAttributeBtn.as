package UI.InformationPanel.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class LookPlayerAttributeBtn extends Btn
   {
      public function LookPlayerAttributeBtn()
      {
         super();
         setTipString("查看人物详细属性");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickLookPlayerAttributeBtn"));
      }
   }
}

