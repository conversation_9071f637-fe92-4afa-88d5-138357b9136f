package UI.DanMedicinePanel.Btn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class DefenceDanSwitchBtn extends SwitchBtn
   {
      public function DefenceDanSwitchBtn()
      {
         super();
         setTipString("防御丹");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToDefenceDan"));
      }
   }
}

