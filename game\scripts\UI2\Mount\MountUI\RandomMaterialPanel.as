package UI2.Mount.MountUI
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI2.Mount.MountData.MountSystemData.MountSystemData;
   import UI2.Mount.MountData.MountsVO;
   import UI2.Mount.MountLogic.RandomMaterialLogic;
   import UI2.Mount.MountLogic.RandomMaterialReturnData;
   import UI2.TicketBuyLogic;
   import UI2.broadcast.SubmitFunction;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class RandomMaterialPanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_r1Btn:ButtonLogicShell2;
      
      private var m_r2Btn:ButtonLogicShell2;
      
      private var m_boxShow:AnimationShowPlayLogicShell;
      
      private var m_boxStopListener:StopListener;
      
      private var m_currentPointNumText:TextField;
      
      private var m_randomMaterialLogic:RandomMaterialLogic;
      
      private var m_ticketBuyLogic:TicketBuyLogic;
      
      private var m_mateiralShows:Vector.<MovieClipPlayLogicShell>;
      
      private var m_returnBtn:ButtonLogicShell2;
      
      private var m_isPlayAnimation:Boolean;
      
      private var m_randomMaterialReturnDataForShowRanResult:RandomMaterialReturnData;
      
      private var m_closeSubUI:CloseSubUI;
      
      private var m_mountsVO:MountsVO;
      
      private var m_mountSystemData:MountSystemData;
      
      private var m_mountsPanel:MountsPanel;
      
      private var m_resultShowFun:Function;
      
      public function RandomMaterialPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_showMC = new MovieClipPlayLogicShell();
         m_randomMaterialLogic = new RandomMaterialLogic();
         m_ticketBuyLogic = new TicketBuyLogic();
         m_mateiralShows = new Vector.<MovieClipPlayLogicShell>(10);
         m_boxStopListener = new StopListener();
         m_boxStopListener.stop2Fun = boxAnimationEnd;
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_r1Btn);
         m_r1Btn = null;
         ClearUtil.clearObject(m_r2Btn);
         m_r2Btn = null;
         ClearUtil.clearObject(m_boxShow);
         m_boxShow = null;
         ClearUtil.clearObject(m_boxStopListener);
         m_boxStopListener = null;
         m_currentPointNumText = null;
         ClearUtil.clearObject(m_randomMaterialLogic);
         m_randomMaterialLogic = null;
         ClearUtil.clearObject(m_ticketBuyLogic);
         m_ticketBuyLogic = null;
         ClearUtil.clearObject(m_mateiralShows);
         m_mateiralShows = null;
         ClearUtil.clearObject(m_returnBtn);
         m_returnBtn = null;
         ClearUtil.clearObject(m_randomMaterialReturnDataForShowRanResult);
         m_randomMaterialReturnDataForShowRanResult = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_mountsVO = null;
         m_mountSystemData = null;
         m_mountsPanel = null;
         m_resultShowFun = null;
         super.clear();
      }
      
      public function init(param1:MountSystemData, param2:MountsVO, param3:MountsPanel, param4:CloseSubUI) : void
      {
         ClearUtil.clearObject(m_closeSubUI);
         m_mountSystemData = param1;
         m_mountsVO = param2;
         m_mountsPanel = param3;
         m_closeSubUI = param4;
         m_show = MyFunction2.returnShowByClassName("CollectMaterialPanel") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         showInitOneFrame();
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_mountsVO == null || m_mountSystemData == null)
         {
            return;
         }
         showInitOneFrame2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var e:ButtonEvent = param1;
         switch(e.button)
         {
            case m_r1Btn:
               if(m_isPlayAnimation)
               {
                  return;
               }
               if(m_mountsVO.getGetMaterialPointNum() < m_mountSystemData.getR1Num())
               {
                  m_mountsPanel.showWarningBox("抽取点数不足, 是否花费" + m_mountSystemData.getR1Ticket() + "点券召唤1次。",3,{
                     "type":"randomMountMaterial_1",
                     "okFunction":useTicket_random1
                  });
                  return;
               }
               MyFunction2.getUserStateIsRightFunction(function():void
               {
                  m_mountsVO.decGetMaterialPointNum(m_mountSystemData.getR1Num());
                  randomOneMaterial();
               },m_mountsPanel.showWarningBox,true);
               break;
            case m_r2Btn:
               if(m_isPlayAnimation)
               {
                  return;
               }
               m_mountsPanel.showWarningBox("是否花费" + m_mountSystemData.getR10Ticket() + "点券召唤10次。",3,{
                  "type":"randomMountMaterial_10",
                  "okFunction":useTicket_random10
               });
               break;
            case m_returnBtn:
               showInitOneFrame();
               showInitOneFrame2();
               break;
            case m_quitBtn:
               m_closeSubUI.close();
         }
      }
      
      private function useTicket_random1() : void
      {
         if(m_mountsPanel == null)
         {
            return;
         }
         if(m_ticketBuyLogic == null)
         {
            return;
         }
         m_ticketBuyLogic.buy(m_mountSystemData.getR1Ticket(),m_mountSystemData.getR1TicketId(),"随机1次坐骑材料",function():void
         {
            randomOneMaterial();
         },m_mountsPanel.showWarningBox);
      }
      
      private function useTicket_random10() : void
      {
         if(m_mountsPanel == null)
         {
            return;
         }
         if(m_ticketBuyLogic == null)
         {
            return;
         }
         m_ticketBuyLogic.buy(m_mountSystemData.getR10Ticket(),m_mountSystemData.getR10TicketId(),"随机10次坐骑材料",function():void
         {
            randomMultiMaterial();
         },m_mountsPanel.showWarningBox);
      }
      
      private function randomOneMaterial() : void
      {
         ClearUtil.clearObject(m_randomMaterialReturnDataForShowRanResult);
         m_randomMaterialReturnDataForShowRanResult = new RandomMaterialReturnData();
         m_randomMaterialLogic.randomOneMaterial(m_mountsVO,m_mountSystemData,m_randomMaterialReturnDataForShowRanResult);
         playBoxAnimation();
         m_resultShowFun = randomOneMaterial2;
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
      }
      
      private function randomOneMaterial2() : void
      {
         showInitResultFrame("result1");
         showInitResultFrame2();
         m_mountsPanel.updateShow();
         showMountMessage();
      }
      
      private function randomMultiMaterial() : void
      {
         ClearUtil.clearObject(m_randomMaterialReturnDataForShowRanResult);
         m_randomMaterialReturnDataForShowRanResult = new RandomMaterialReturnData();
         m_randomMaterialLogic.randomMultiMaterial(m_mountsVO,m_mountSystemData,m_randomMaterialReturnDataForShowRanResult);
         playBoxAnimation();
         m_resultShowFun = randomMultiMaterial2;
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
      }
      
      private function randomMultiMaterial2() : void
      {
         showInitResultFrame("result2");
         showInitResultFrame2();
         m_mountsPanel.updateShow();
         showMountMessage();
      }
      
      private function showMountMessage() : void
      {
         if(Boolean(m_randomMaterialReturnDataForShowRanResult) == false)
         {
            return;
         }
         if(m_randomMaterialReturnDataForShowRanResult.newGetMountVOs != null)
         {
            if(m_randomMaterialReturnDataForShowRanResult.newGetMountVOs.length)
            {
               m_mountsPanel.showWarningBox("恭喜获得坐骑：<font size=\'25\' color=\'#fcca00\'>" + m_randomMaterialReturnDataForShowRanResult.newGetMountVOs[0].getName() + "</font>.",0);
               SubmitFunction.getInstance().setData1(6,m_randomMaterialReturnDataForShowRanResult.newGetMountVOs[0].getId(),m_randomMaterialReturnDataForShowRanResult.newGetMountVOs[0].getName());
            }
         }
         else if(m_randomMaterialReturnDataForShowRanResult.upLevelMountVOs != null)
         {
            if(m_randomMaterialReturnDataForShowRanResult.upLevelMountVOs.length)
            {
               m_mountsPanel.showWarningBox("恭喜您的<font size=\'25\' color=\'#fcca00\'>" + m_randomMaterialReturnDataForShowRanResult.upLevelMountVOs[0].getName() + "</font>升级到<font size=\'25\' color=\'#fcca00\'>" + m_randomMaterialReturnDataForShowRanResult.upLevelMountVOs[0].getLevel1() + "</font>星.",0);
            }
         }
      }
      
      private function showClearFrame() : void
      {
         ClearUtil.clearObject(m_r1Btn);
         m_r1Btn = null;
         ClearUtil.clearObject(m_r2Btn);
         m_r2Btn = null;
         ClearUtil.clearObject(m_boxShow);
         m_boxShow = null;
         m_currentPointNumText = null;
         ClearUtil.nullArr(m_mateiralShows);
         ClearUtil.clearObject(m_returnBtn);
         m_returnBtn = null;
         if(m_show["perSHowTxt"])
         {
            (m_show["perSHowTxt"] as TextField).removeEventListener("link",textHandler);
         }
      }
      
      private function showInitOneFrame() : void
      {
         showClearFrame();
         m_showMC.gotoAndStop("1");
         m_r1Btn = new ButtonLogicShell2();
         m_r1Btn.setShow(m_show["r1Btn"]);
         m_r2Btn = new ButtonLogicShell2();
         m_r2Btn.setShow(m_show["r2Btn"]);
         m_boxShow = new AnimationShowPlayLogicShell();
         m_boxShow.setShow(m_show["BoxShow"]);
         m_boxShow.addNextStopListener(m_boxStopListener);
         m_currentPointNumText = m_show["currentPointNumText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_currentPointNumText);
         (m_show["perSHowTxt"] as TextField).htmlText = "<font><a href=\'event:percent\'><u>概率公示</u></a> </font>";
         (m_show["perSHowTxt"] as TextField).addEventListener("link",textHandler);
      }
      
      private function textHandler(param1:TextEvent) : void
      {
         var _loc2_:* = param1.text;
         if("percent" === _loc2_)
         {
            navigateToURL(new URLRequest("http://my.4399.com/forums/thread-58433590"),"_blank");
         }
      }
      
      private function showInitOneFrame2() : void
      {
         m_currentPointNumText.text = m_mountsVO.getGetMaterialPointNum().toString();
      }
      
      private function showInitResultFrame(param1:String) : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:DisplayObject = null;
         var _loc3_:MovieClipPlayLogicShell = null;
         showClearFrame();
         m_showMC.gotoAndStop(param1);
         _loc4_ = m_show.numChildren;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc2_ = m_show.getChildAt(_loc6_);
            if(_loc2_.name.substr(0,13) == "materialCell_")
            {
               _loc5_++;
            }
            _loc6_++;
         }
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc3_ = new MovieClipPlayLogicShell();
            _loc3_.setShow(m_show["materialCell_" + (_loc6_ + 1)]);
            m_mateiralShows[_loc6_] = _loc3_;
            _loc6_++;
         }
         m_returnBtn = new ButtonLogicShell2();
         m_returnBtn.setShow(m_show["returnBtn"]);
      }
      
      private function showInitResultFrame2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = int(m_randomMaterialReturnDataForShowRanResult.resultMaterialDatas.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_mateiralShows[_loc2_])
            {
               m_mateiralShows[_loc2_].gotoAndStop(m_randomMaterialReturnDataForShowRanResult.resultMaterialDatas[_loc2_].getId());
            }
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_mateiralShows[_loc2_].getShow()["numText"]);
            m_mateiralShows[_loc2_].getShow()["numText"].text = m_randomMaterialReturnDataForShowRanResult.resultMaterialDatas[_loc2_].getNum() * m_randomMaterialReturnDataForShowRanResult.resultMateiralDataNums[_loc2_];
            _loc2_++;
         }
      }
      
      private function boxAnimationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         endBoxAnimation();
      }
      
      private function playBoxAnimation() : void
      {
         m_boxShow.gotoAndPlay("1");
         m_isPlayAnimation = true;
      }
      
      private function endBoxAnimation() : void
      {
         m_isPlayAnimation = false;
         if(Boolean(m_resultShowFun))
         {
            m_resultShowFun();
         }
      }
   }
}

