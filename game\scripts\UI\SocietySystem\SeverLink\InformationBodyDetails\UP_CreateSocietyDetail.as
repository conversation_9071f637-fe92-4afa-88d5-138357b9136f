package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_CreateSocietyDetail extends InformationBodyDetail
   {
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_societyNameLen:int;
      
      private var m_societyName:String;
      
      public function UP_CreateSocietyDetail()
      {
         super();
         m_informationBodyId = 3022;
      }
      
      public function initData(param1:Number, param2:int, param3:String) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_societyName = param3;
         m_societyNameLen = new InformationBodyDetailUtil().getStringLength(m_societyName);
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_societyNameLen);
         if(m_societyNameLen)
         {
            _loc1_.writeUTFBytes(m_societyName);
         }
         return _loc1_;
      }
      
      public function getSocietyName() : String
      {
         return m_societyName;
      }
   }
}

