package UI2.Mount.MountData
{
   import GM_UI.GMData;
   import UI.DataManagerParent;
   import UI.Players.Player;
   import UI.XMLSingle;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class MountsVO extends DataManagerParent
   {
      private var m_getMaterialPointNum:uint;
      
      private var m_currentStrengthenPointNum:uint;
      
      private var m_mountVOs:Vector.<MountVO>;
      
      public function MountsVO()
      {
         super();
         m_mountVOs = new Vector.<MountVO>();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_mountVOs);
         m_mountVOs = null;
         super.clear();
      }
      
      public function initFromSaveXML(param1:XML, param2:String, param3:Player, param4:Player) : void
      {
         var _loc13_:XMLList = null;
         var _loc9_:int = 0;
         var _loc12_:int = 0;
         var _loc14_:MountVO = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:String = null;
         var _loc6_:Boolean = false;
         ClearUtil.clearObject(m_mountVOs);
         m_mountVOs.length = 0;
         var _loc5_:Vector.<String> = new Vector.<String>();
         var _loc11_:XML = XMLSingle.getInstance().mountsXML;
         if(param1.hasOwnProperty("Mounts"))
         {
            this.currentStrengthenPointNum = uint(param1.Mounts[0].@sPN);
            this.getMaterialPointNum = uint(param1.Mounts[0].@gPN);
            _loc13_ = param1.Mounts[0].mount;
            _loc12_ = int(_loc13_.length());
            if(GMData.getInstance().isGMApplication == false)
            {
               if(_loc12_ > _loc11_.mount.length())
               {
                  if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
                  {
                     throw new Error("坐骑数量错误");
                  }
                  throw new Error();
               }
            }
            _loc9_ = 0;
            while(_loc9_ < _loc12_)
            {
               _loc14_ = new MountVO();
               _loc14_.initFromSaveXML(_loc13_[_loc9_],_loc11_);
               m_mountVOs.push(_loc14_);
               if(String(_loc13_[_loc9_].@pId) == "playerOne")
               {
                  param3.playerVO.mountVO = _loc14_;
               }
               else if(String(_loc13_[_loc9_].@pId) == "playerTwo")
               {
                  param4.playerVO.mountVO = _loc14_;
               }
               _loc5_.push(_loc14_.getId());
               _loc9_++;
            }
         }
         _loc13_ = _loc11_.mount;
         _loc12_ = int(_loc13_.length());
         _loc8_ = int(_loc5_.length);
         _loc9_ = 0;
         while(_loc9_ < _loc12_)
         {
            _loc10_ = String(_loc13_[_loc9_].@id);
            _loc6_ = false;
            _loc7_ = 0;
            while(_loc7_ < _loc8_)
            {
               if(_loc5_[_loc7_] == _loc10_)
               {
                  _loc6_ = true;
                  break;
               }
               _loc7_++;
            }
            if(!_loc6_)
            {
               _loc14_ = new MountVO();
               _loc14_.initFromXML(_loc10_,0,0,_loc11_);
               m_mountVOs.push(_loc14_);
            }
            _loc9_++;
         }
         _loc11_ = null;
      }
      
      public function exportSaveXML() : XML
      {
         var _loc4_:int = 0;
         var _loc1_:XML = null;
         var _loc3_:XML = <Mounts />;
         if(currentStrengthenPointNum)
         {
            _loc3_.@sPN = currentStrengthenPointNum;
         }
         if(getMaterialPointNum)
         {
            _loc3_.@gPN = getMaterialPointNum;
         }
         var _loc2_:int = int(m_mountVOs.length);
         if(_loc2_ > XMLSingle.getInstance().mountsXML.mount.length())
         {
            if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               throw new Error("坐骑数量错误");
            }
            throw new Error();
         }
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ = m_mountVOs[_loc4_].exportSaveXML();
            if(_loc1_)
            {
               if(m_mountVOs[_loc4_].getPlayerVO())
               {
                  _loc1_.@pId = m_mountVOs[_loc4_].getPlayerVO().playerID;
               }
               _loc3_.appendChild(_loc1_);
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function getMountVONum() : uint
      {
         return m_mountVOs.length;
      }
      
      public function getMounts() : Vector.<MountVO>
      {
         return m_mountVOs;
      }
      
      public function getMountVOByIndex(param1:int) : MountVO
      {
         return m_mountVOs[param1];
      }
      
      public function getMountVOById(param1:String) : MountVO
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(m_mountVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_mountVOs[_loc3_].getId() == param1)
            {
               return m_mountVOs[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function addStrengthenPointNum(param1:uint) : void
      {
         this.currentStrengthenPointNum += param1;
      }
      
      public function addGetMaterialPointNum(param1:uint) : void
      {
         this.getMaterialPointNum += param1;
      }
      
      public function decStrengthenPointNum(param1:uint) : void
      {
         if(param1 > this.currentStrengthenPointNum)
         {
            throw new Error("currentStrengthenPointNum less than decValue");
         }
         this.currentStrengthenPointNum -= param1;
      }
      
      public function decGetMaterialPointNum(param1:uint) : void
      {
         getMaterialPointNum = Math.max(0,getMaterialPointNum - param1);
      }
      
      public function getCurrentStrengthenPointNum() : uint
      {
         return currentStrengthenPointNum;
      }
      
      public function getGetMaterialPointNum() : uint
      {
         return getMaterialPointNum;
      }
      
      public function getTotalAddAttackOfMountVOs() : uint
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:* = 0;
         _loc2_ = int(m_mountVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ += m_mountVOs[_loc3_].getAttackAddToPlayer();
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function getTotalAddDefenceOfMountVOs() : uint
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:* = 0;
         _loc2_ = int(m_mountVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ += m_mountVOs[_loc3_].getDefenceAddToPlayer();
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function getTotalAddHpOfMountVOs() : uint
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:* = 0;
         _loc2_ = int(m_mountVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ += m_mountVOs[_loc3_].getHpAddToPlayer();
            _loc3_++;
         }
         return _loc1_;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currentStrengthenPointNum = m_currentStrengthenPointNum;
         _antiwear.getMaterialPointNum = m_getMaterialPointNum;
      }
      
      private function get currentStrengthenPointNum() : uint
      {
         return _antiwear.currentStrengthenPointNum;
      }
      
      private function set currentStrengthenPointNum(param1:uint) : void
      {
         _antiwear.currentStrengthenPointNum = param1;
      }
      
      private function get getMaterialPointNum() : uint
      {
         return _antiwear.getMaterialPointNum;
      }
      
      private function set getMaterialPointNum(param1:uint) : void
      {
         _antiwear.getMaterialPointNum = param1;
      }
   }
}

