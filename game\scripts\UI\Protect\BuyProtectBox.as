package UI.Protect
{
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.BuffData;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction2;
   import UI.PointTicketBuyBox;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class BuyProtectBox extends PointTicketBuyBox
   {
      private var _buySelectObjects:Array;
      
      private var _currentSlectObject:Object;
      
      private var _buffXML:XML;
      
      public function BuyProtectBox()
      {
         super();
      }
      
      override public function clear() : void
      {
         var _loc3_:DisplayObject = null;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         while(numChildren > 0)
         {
            _loc3_ = getChildAt(0);
            removeChildAt(0);
            if(_loc3_.hasOwnProperty("clear"))
            {
               _loc3_["clear"]();
            }
         }
         _loc1_ = !!_buySelectObjects ? _buySelectObjects.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _buySelectObjects[_loc2_] = null;
            _loc2_++;
         }
         _buySelectObjects = null;
         _currentSlectObject = null;
         _buffXML = null;
      }
      
      override protected function init() : void
      {
         var _loc1_:int = 0;
         var _loc2_:Object = null;
         super.init();
         _sureBtn.showText("购买");
         _sureBtn.y = 67;
         _cancelBtn.y = 67;
         _rechargeBtn.y = _sureBtn.y - 5;
         _pointTicketText.y = _rechargeBtn.y + 10;
         var _loc4_:int = 0;
         _buffXML = XMLSingle.getInstance().buffXML;
         var _loc3_:XMLList = _buffXML.item.(@className == "Buff_ProtectPrivileges")[0].price;
         _loc1_ = int(_loc3_.length());
         _buySelectObjects = [];
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = {};
            _loc2_.time = int(_loc3_[_loc4_].@time);
            _loc2_.pointTicket = int(_loc3_[_loc4_].@pointTicket);
            _loc2_.ticketId = String(_loc3_[_loc4_].@ticketId);
            _buySelectObjects.push(_loc2_);
            _loc4_++;
         }
         arrange();
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("switchBuyProtectSelectSlot",switchSlot,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("switchBuyProtectSelectSlot",switchSlot,true);
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var i:int;
         var length:int;
         var protecteBuffVO:BuffVO;
         var myParent:ProtectPanel;
         var currentSelectObject:Object;
         var buffXML:XML;
         var e:UIBtnEvent = param1;
         if(e.target == _sureBtn)
         {
            i = 0;
            myParent = parent as ProtectPanel;
            currentSelectObject = _currentSlectObject;
            buffXML = _buffXML;
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               var timeStr:String = param1;
               var dataObj:Object = {};
               dataObj["propId"] = currentSelectObject.ticketId;
               dataObj["count"] = 1;
               dataObj["price"] = currentSelectObject.pointTicket;
               dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
               dataObj["tag"] = "购买保佑";
               AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
               {
                  if(param1["propId"] != dataObj["propId"])
                  {
                     throw new Error("购买物品id前后端不相同！");
                  }
                  var _loc3_:AllTimeBuffVO = XMLSingle.getBuff(10000,buffXML) as AllTimeBuffVO;
                  _loc3_.startDate = timeStr;
                  _loc3_.totalTime = currentSelectObject.time;
                  _loc3_.remainTime = _loc3_.totalTime * 3600;
                  BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc3_),BuffData.getInstance().buffDrives);
                  var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                  _loc2_.type = "4399";
                  _loc2_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc2_);
                  MyFunction2.saveGame2();
               },myParent.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
            },myParent.showWarningBox);
         }
         super.clickBtn(e);
      }
      
      private function switchSlot(param1:UIBtnEvent) : void
      {
         var _loc2_:DisplayObject = null;
         _currentSlectObject = param1.data;
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < numChildren)
         {
            _loc2_ = getChildAt(_loc3_);
            if(_loc2_ is BuyProtectSelectSlot && _loc2_ != param1.target)
            {
               (_loc2_ as BuyProtectSelectSlot).gotoTwoFrame();
            }
            _loc3_++;
         }
      }
      
      private function arrange() : void
      {
         var _loc2_:BuyProtectSelectSlot = null;
         var _loc3_:int = 0;
         var _loc1_:int = int(_buySelectObjects.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = new BuyProtectSelectSlot();
            _loc2_.value = _buySelectObjects[_loc3_];
            _loc2_.x = 15 + 125 * _loc3_;
            _loc2_.y = 12;
            addChild(_loc2_);
            if(!_loc3_)
            {
               _loc2_.init(false);
               _currentSlectObject = _buySelectObjects[_loc3_];
            }
            else
            {
               _loc2_.init(true);
            }
            _loc3_++;
         }
      }
   }
}

