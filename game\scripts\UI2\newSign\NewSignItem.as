package UI2.newSign
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class NewSignItem
   {
      private const NORMAL:String = "normal";
      
      private const SIGNED:String = "signed";
      
      private const UNSELECT:String = "unSelect";
      
      private const SELECTING:String = "selecting";
      
      private const TODAY_NORMAL:String = "today_normal";
      
      private const TODAY_SIGNED:String = "today_signed";
      
      private var show:MovieClip;
      
      private var index:int = 0;
      
      private var day:int = 0;
      
      public function NewSignItem(param1:MovieClip)
      {
         super();
         show = param1;
         (show["txt"] as TextField).mouseEnabled = false;
      }
      
      public function clear() : void
      {
         unButtonMode();
         show = null;
      }
      
      public function setDay(param1:int) : void
      {
         day = param1;
         show["txt"].text = String(day);
      }
      
      public function getDay() : int
      {
         return day;
      }
      
      public function turnUnActivate() : void
      {
         show.visible = false;
         setDay(0);
         unButtonMode();
      }
      
      public function turnSigned() : void
      {
         show.visible = true;
         show.gotoAndStop("signed");
         unButtonMode();
      }
      
      public function turnNormal() : void
      {
         show.visible = true;
         show.gotoAndStop("normal");
         unButtonMode();
      }
      
      public function turnTodayNormal() : void
      {
         show.visible = true;
         show.gotoAndStop("today_normal");
         unButtonMode();
      }
      
      public function turnTodaySigned() : void
      {
         show.visible = true;
         show.gotoAndStop("today_signed");
         unButtonMode();
      }
      
      public function turnUnselect() : void
      {
         show.visible = true;
         show.gotoAndStop("unSelect");
         buttonMode();
      }
      
      public function turnSelecting() : void
      {
         show.visible = true;
         show.gotoAndStop("selecting");
         buttonMode();
      }
      
      public function buttonMode() : void
      {
         show.buttonMode = true;
         if(!show.hasEventListener("click"))
         {
            show.addEventListener("click",onClickHandler);
         }
      }
      
      public function unButtonMode() : void
      {
         show.buttonMode = false;
         if(show.hasEventListener("click"))
         {
            show.removeEventListener("click",onClickHandler);
         }
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         if(show.currentFrameLabel == "unSelect")
         {
            turnSelecting();
         }
         else if(show.currentFrameLabel == "selecting")
         {
            turnUnselect();
         }
      }
      
      public function getIsSigned() : Boolean
      {
         return show.currentFrameLabel == "signed";
      }
      
      public function getIsSelecting() : Boolean
      {
         return show.currentFrameLabel == "selecting";
      }
   }
}

