package UI.UICheckBox
{
   import UI.Event.UIPassiveEvent;
   import UI.MyMovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class UICheckBox extends MyMovieClip
   {
      private var _isCheck:Boolean = true;
      
      public function UICheckBox()
      {
         super();
         stop();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("addedToStage",addToStage,false);
         super.clear();
      }
      
      public function set isCheck(param1:Boolean) : void
      {
         _isCheck = param1;
         gotoAndStop(_isCheck ? 1 : 2);
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",onClick,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",onClick,false);
      }
      
      protected function onClick(param1:MouseEvent) : void
      {
         _isCheck = !_isCheck;
         gotoAndStop(_isCheck ? 1 : 2);
         dispatchEvent(new UIPassiveEvent("changeCheckBoxState",{"isCheck":_isCheck}));
      }
      
      protected function init() : void
      {
      }
   }
}

