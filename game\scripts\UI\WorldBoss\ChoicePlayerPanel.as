package UI.WorldBoss
{
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class ChoicePlayerPanel extends MySprite
   {
      private var m_show:Sprite;
      
      private var m_ableSpriteSL:AbleDragSpriteLogicShell;
      
      public var btn1:ButtonLogicShell;
      
      public var btn2:ButtonLogicShell;
      
      private var m_headShow1:MovieClipPlayLogicShell;
      
      private var m_headShow2:MovieClipPlayLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      public function ChoicePlayerPanel()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(btn1);
         btn1 = null;
         ClearUtil.clearObject(btn2);
         btn2 = null;
         ClearUtil.clearObject(m_headShow1);
         m_headShow1 = null;
         ClearUtil.clearObject(m_headShow2);
         m_headShow2 = null;
         m_myLoader = null;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function init() : void
      {
         m_myLoader.getClass("WorldBoss.swf","ChoicePlayerPanel",getShowSuccess,getFailFun);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         if(stage)
         {
            x = (stage.stageWidth - width) / 2;
            y = (stage.stageHeight - height) / 2;
         }
         m_ableSpriteSL = new AbleDragSpriteLogicShell();
         m_ableSpriteSL.setShow(m_show);
         btn1 = new ButtonLogicShell();
         btn1.setShow(m_show["btn1"]);
         btn2 = new ButtonLogicShell();
         btn2.setShow(m_show["btn2"]);
         m_headShow1 = new MovieClipPlayLogicShell();
         m_headShow1.setShow(m_show["headShow1"]);
         m_headShow2 = new MovieClipPlayLogicShell();
         m_headShow2.setShow(m_show["headShow2"]);
         m_headShow1.gotoAndStop(GamingUI.getInstance().player1.playerVO.playerType);
         if(GamingUI.getInstance().player2)
         {
            m_headShow2.gotoAndStop(GamingUI.getInstance().player2.playerVO.playerType);
         }
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

