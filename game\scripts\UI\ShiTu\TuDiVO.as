package UI.ShiTu
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MainLineTask.TaskDetectors.IPlayerVOForTaskDetector_Player;
   import UI.Players.PlayerVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import flash.utils.Dictionary;
   
   public class TuDiVO extends DataManagerParent implements IXiuLianTargetVO, IPlayerVOForTaskDetector_Player
   {
      public static const XIAO_KONG:String = "xiaoKong";
      
      public static const XIAO_BA:String = "xiaoBa";
      
      public var type:String;
      
      public var skillVOs:Vector.<TuDiSkillVO>;
      
      private var _skillVOsById2:Dictionary;
      
      private var _bloodPercent:Number;
      
      private var _energyPercent:Number;
      
      private var _currentXiuLianValue:int;
      
      private var _baseLevel:int;
      
      private var _xiuLianLevel:int;
      
      private var _baseBlood:int;
      
      private var _xiuLianBlood:int;
      
      private var _otherBlood:int;
      
      private var _baseEnergy:int;
      
      private var _xiuLianEnergy:int;
      
      private var _otherEnergy:int;
      
      private var _baseEnergyRecover:Number = 0;
      
      private var _xiuLianEnergyRecover:Number = 0;
      
      private var _baseAttack:int;
      
      private var _xiuLianAttack:int;
      
      private var _otherAttack:int;
      
      private var _baseDefence:int;
      
      private var _xiuLianDefence:int;
      
      private var _otherDefence:int;
      
      private var _baseCriticalRate:int;
      
      private var _xiuLianCriticalRate:int;
      
      private var _otherCriticalRate:int;
      
      private var _baseXiuLianValue:int;
      
      private var _otherXiuLianValue:int;
      
      private var _baseXiuLianNum:int;
      
      private var _buyXiuLianNum:int;
      
      private var _currentUsedXianLianNum:int;
      
      private var _xiuLianDate:String;
      
      private var _xiuLianContents:Vector.<XiuLianContent>;
      
      private var _xiuLianContentsByContent:Dictionary;
      
      public var playerVO:PlayerVO;
      
      public function TuDiVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(_xiuLianContents);
         ClearUtil.nullObject(_xiuLianContentsByContent);
         _xiuLianContents = null;
         _xiuLianContentsByContent = null;
         playerVO = null;
         ClearUtil.nullArr(skillVOs);
         skillVOs = null;
         ClearUtil.nullObject(_skillVOsById2);
         _skillVOsById2 = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.baseLevel = _baseLevel;
         _antiwear.xiuLianLevel = _xiuLianLevel;
         _antiwear.baseBlood = _baseBlood;
         _antiwear.xiuLianBlood = _xiuLianBlood;
         _antiwear.otherBlood = _otherBlood;
         _antiwear.baseEnergy = _baseEnergy;
         _antiwear.xiuLianEnergy = _xiuLianEnergy;
         _antiwear.otherEnergy = _otherEnergy;
         _antiwear.baseEnergyRecover = _baseEnergyRecover;
         _antiwear.xiuLianEnergyRecover = _xiuLianEnergyRecover;
         _antiwear.baseAttack = _baseAttack;
         _antiwear.xiuLianAttack = _xiuLianAttack;
         _antiwear.otherAttack = _otherAttack;
         _antiwear.baseDefence = _baseDefence;
         _antiwear.xiuLianDefence = _xiuLianDefence;
         _antiwear.otherDefence = _otherDefence;
         _antiwear.baseCriticalRate = _baseCriticalRate;
         _antiwear.xiuLianCriticalRate = _xiuLianCriticalRate;
         _antiwear.otherCriticalRate = _otherCriticalRate;
         _antiwear.baseXiuLianValue = _baseXiuLianValue;
         _antiwear.otherXiuLianValue = _otherXiuLianValue;
         _antiwear.baseXiuLianNum = _baseXiuLianNum;
         _antiwear.currentUsedXianLianNum = _currentUsedXianLianNum;
         _antiwear.xiuLianDate = _xiuLianDate;
         _antiwear.bloodPercent = _bloodPercent;
         _antiwear.energyPercent = _energyPercent;
         _antiwear.currentXiuLianValue = _currentXiuLianValue;
      }
      
      public function get bloodPercent() : Number
      {
         return _antiwear.bloodPercent;
      }
      
      public function set bloodPercent(param1:Number) : void
      {
         if(param1 > 1)
         {
            param1 = 1;
         }
         else if(param1 < 0)
         {
            param1 = 0;
         }
         _antiwear.bloodPercent = param1;
      }
      
      public function get energyPercent() : Number
      {
         return _antiwear.energyPercent;
      }
      
      public function set energyPercent(param1:Number) : void
      {
         if(param1 > 1)
         {
            param1 = 1;
         }
         else if(param1 < 0)
         {
            param1 = 0;
         }
         _antiwear.energyPercent = param1;
      }
      
      public function get currentXiuLianValue() : int
      {
         return _antiwear.currentXiuLianValue;
      }
      
      public function set currentXiuLianValue(param1:int) : void
      {
         try
         {
            if(param1 > xiuLianValue)
            {
               param1 = xiuLianValue;
            }
         }
         catch(error:Error)
         {
         }
         if(param1 < 0)
         {
            param1 = 0;
         }
         _antiwear.currentXiuLianValue = param1;
      }
      
      public function get baseLevel() : int
      {
         return _antiwear.baseLevel;
      }
      
      public function set baseLevel(param1:int) : void
      {
         _antiwear.baseLevel = param1;
      }
      
      public function get xiuLianLevel() : int
      {
         return _antiwear.xiuLianLevel;
      }
      
      public function set xiuLianLevel(param1:int) : void
      {
         _antiwear.xiuLianLevel = param1;
         InitUI.getInstance().setTuDiVOBase(this,XMLSingle.getInstance()[type + "XML"],XMLSingle.getInstance().tuDiSkillXML);
      }
      
      public function get level() : int
      {
         return baseLevel + xiuLianLevel;
      }
      
      public function get baseBlood() : int
      {
         return _antiwear.baseBlood;
      }
      
      public function set baseBlood(param1:int) : void
      {
         _antiwear.baseBlood = param1;
      }
      
      public function get xiuLianBlood() : int
      {
         return _antiwear.xiuLianBlood;
      }
      
      public function set xiuLianBlood(param1:int) : void
      {
         _antiwear.xiuLianBlood = param1;
      }
      
      public function get otherBlood() : int
      {
         return _antiwear.otherBlood;
      }
      
      public function set otherBlood(param1:int) : void
      {
         _antiwear.otherBlood = param1;
      }
      
      public function get blood() : int
      {
         return baseBlood + xiuLianBlood + otherBlood;
      }
      
      public function get baseEnergy() : int
      {
         return _antiwear.baseEnergy;
      }
      
      public function set baseEnergy(param1:int) : void
      {
         _antiwear.baseEnergy = param1;
      }
      
      public function get xiuLianEnergy() : int
      {
         return _antiwear.xiuLianEnergy;
      }
      
      public function set xiuLianEnergy(param1:int) : void
      {
         _antiwear.xiuLianEnergy = param1;
      }
      
      public function get otherEnergy() : int
      {
         return _otherEnergy;
      }
      
      public function set otherEnergy(param1:int) : void
      {
         _otherEnergy = param1;
      }
      
      public function get energy() : int
      {
         return baseEnergy + xiuLianEnergy + otherEnergy;
      }
      
      public function get baseEnergyRecover() : Number
      {
         return _antiwear.baseEnergyRecover;
      }
      
      public function set baseEnergyRecover(param1:Number) : void
      {
         _antiwear.baseEnergyRecover = param1;
      }
      
      public function get xiuLianEnergyRecover() : Number
      {
         return _antiwear.xiuLianEnergyRecover;
      }
      
      public function set xiuLianEnergyRecover(param1:Number) : void
      {
         _antiwear.xiuLianEnergyRecover = param1;
      }
      
      public function get energyRecover() : Number
      {
         return baseEnergyRecover + xiuLianEnergyRecover;
      }
      
      public function get baseAttack() : int
      {
         return _antiwear.baseAttack;
      }
      
      public function set baseAttack(param1:int) : void
      {
         _antiwear.baseAttack = param1;
      }
      
      public function get xiuLianAttack() : int
      {
         return _antiwear.xiuLianAttack;
      }
      
      public function set xiuLianAttack(param1:int) : void
      {
         _antiwear.xiuLianAttack = param1;
      }
      
      public function get otherAttack() : int
      {
         return _antiwear.otherAttack;
      }
      
      public function set otherAttack(param1:int) : void
      {
         _antiwear.otherAttack = param1;
      }
      
      public function get attack() : int
      {
         return baseAttack + xiuLianAttack + otherAttack;
      }
      
      public function get baseDefence() : int
      {
         return _antiwear.baseDefence;
      }
      
      public function set baseDefence(param1:int) : void
      {
         _antiwear.baseDefence = param1;
      }
      
      public function get xiuLianDefence() : int
      {
         return _antiwear.xiuLianDefence;
      }
      
      public function set xiuLianDefence(param1:int) : void
      {
         _antiwear.xiuLianDefence = param1;
      }
      
      public function get otherDefence() : int
      {
         return _antiwear.otherDefence;
      }
      
      public function set otherDefence(param1:int) : void
      {
         _antiwear.otherDefence = param1;
      }
      
      public function get defence() : int
      {
         return baseDefence + xiuLianDefence + otherDefence;
      }
      
      public function get baseCriticalRate() : int
      {
         return _antiwear.baseCriticalRate;
      }
      
      public function set baseCriticalRate(param1:int) : void
      {
         _antiwear.baseCriticalRate = param1;
      }
      
      public function get xiuLianCriticalRate() : int
      {
         return _antiwear.xiuLianCriticalRate;
      }
      
      public function set xiuLianCriticalRate(param1:int) : void
      {
         _antiwear.xiuLianCriticalRate = param1;
      }
      
      public function get otherCriticalRate() : int
      {
         return _antiwear.otherCriticalRate;
      }
      
      public function set otherCriticalRate(param1:int) : void
      {
         _antiwear.otherCriticalRate = param1;
      }
      
      public function get criticalRate() : int
      {
         return baseCriticalRate + xiuLianCriticalRate + otherCriticalRate;
      }
      
      public function xiuLianSkill(param1:PromoteValueObject) : void
      {
         var _loc7_:int = 0;
         var _loc2_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:String = null;
         var _loc5_:int = 0;
         var _loc4_:TuDiSkillVO = null;
         _loc2_ = !!skillVOs ? skillVOs.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc2_)
         {
            if(param1.value == skillVOs[_loc7_].id2)
            {
               _loc4_ = skillVOs[_loc7_];
               break;
            }
            _loc7_++;
         }
         if(_loc4_)
         {
            _loc6_ = int(param1.addAttributes.length);
            _loc5_ = 0;
            while(_loc5_ < _loc6_)
            {
               _loc3_ = param1.addAttributes[_loc5_];
               param1 = param1.addAttributeValueObjects[_loc5_];
               if(_loc4_[_loc3_] is Function)
               {
                  _loc4_[_loc3_](param1);
               }
               else
               {
                  _loc4_[_loc3_] = param1.value;
               }
               _loc5_++;
            }
         }
      }
      
      public function get baseXiuLianValue() : int
      {
         return _antiwear.baseXiuLianValue;
      }
      
      public function set baseXiuLianValue(param1:int) : void
      {
         _antiwear.baseXiuLianValue = param1;
      }
      
      public function get otherXiuLianValue() : int
      {
         return _antiwear.otherXiuLianValue;
      }
      
      public function set otherXiuLianValue(param1:int) : void
      {
         _antiwear.otherXiuLianValue = param1;
      }
      
      public function get xiuLianValue() : int
      {
         return baseXiuLianValue + otherXiuLianValue + (!!GamingUI.getInstance().player1 ? GamingUI.getInstance().player1.vipVO.addXiuLianUpperLimit : 0);
      }
      
      public function get baseXiuLianNum() : int
      {
         return _antiwear.baseXiuLianNum;
      }
      
      public function set baseXiuLianNum(param1:int) : void
      {
         _antiwear.baseXiuLianNum = param1;
      }
      
      public function get buyXiuLianNum() : int
      {
         return _antiwear.buyXiuLianNum;
      }
      
      public function set buyXiuLianNum(param1:int) : void
      {
         _antiwear.buyXiuLianNum = param1;
      }
      
      public function get xiuLianNum() : int
      {
         return baseXiuLianNum + buyXiuLianNum;
      }
      
      public function get currentUsedXianLianNum() : int
      {
         return _antiwear.currentUsedXianLianNum;
      }
      
      public function set currentUsedXianLianNum(param1:int) : void
      {
         _antiwear.currentUsedXianLianNum = param1;
      }
      
      public function get xiuLianDate() : String
      {
         return _antiwear.xiuLianDate;
      }
      
      public function set xiuLianDate(param1:String) : void
      {
         _antiwear.xiuLianDate = param1;
      }
      
      public function get xiuLianContents() : Vector.<XiuLianContent>
      {
         return _xiuLianContents;
      }
      
      public function getXiuLianContentByContent(param1:String) : XiuLianContent
      {
         if(_xiuLianContentsByContent == null)
         {
            return null;
         }
         return _xiuLianContentsByContent[param1];
      }
      
      public function addXiuLianContent(param1:XiuLianContent) : void
      {
         if(param1 == null)
         {
            throw new Error();
         }
         if(Boolean(_xiuLianContentsByContent) && _xiuLianContentsByContent[param1.content])
         {
            throw new Error();
         }
         if(_xiuLianContents == null)
         {
            _xiuLianContents = new Vector.<XiuLianContent>();
         }
         if(_xiuLianContentsByContent == null)
         {
            _xiuLianContentsByContent = new Dictionary();
         }
         _xiuLianContents.push(param1);
         _xiuLianContentsByContent[param1.content] = param1;
         param1.xiuLianTargetVO = this;
      }
      
      public function getTuDiSkillVOById2(param1:String) : TuDiSkillVO
      {
         if(_skillVOsById2 == null)
         {
            return null;
         }
         return _skillVOsById2[param1];
      }
      
      public function addTuDiSkillVO(param1:TuDiSkillVO) : void
      {
         if(param1 == null)
         {
            throw new Error();
         }
         if(Boolean(_skillVOsById2) && _skillVOsById2[param1.id2])
         {
            throw new Error();
         }
         if(skillVOs == null)
         {
            skillVOs = new Vector.<TuDiSkillVO>();
         }
         if(_skillVOsById2 == null)
         {
            _skillVOsById2 = new Dictionary();
         }
         skillVOs.push(param1);
         _skillVOsById2[param1.id2] = param1;
         param1.tuDiVO = this;
      }
   }
}

