package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.GamingUI;
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class DOWN_TheSocietyList extends InformationBodyDetail
   {
      protected const m_const_validInterval:int = 60000;
      
      protected var m_societyTotalNum:int;
      
      protected var m_sotictyNum:int;
      
      protected var m_societyList:Vector.<SocietyDataInList>;
      
      protected var m_dataDeadTime:Number;
      
      protected var m_pageIndex:int;
      
      protected var m_num:int;
      
      public function DOWN_TheSocietyList()
      {
         super();
         m_informationBodyId = 3005;
         m_dataDeadTime = 0;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_societyList);
         m_societyList = null;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:SocietyDataInList = null;
         super.initFromByteArray(param1);
         m_societyTotalNum = param1.readInt();
         m_sotictyNum = param1.readInt();
         trace("societyTotalNum:",m_societyTotalNum);
         trace("societyNum:",m_sotictyNum);
         if(m_sotictyNum)
         {
            _loc3_ = m_sotictyNum;
            m_societyList = new Vector.<SocietyDataInList>();
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               _loc2_ = new SocietyDataInList();
               _loc2_.initFromByteArray(param1);
               m_societyList.push(_loc2_);
               _loc4_++;
            }
         }
         m_dataDeadTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + 60000;
      }
      
      public function initOther(param1:int, param2:int) : void
      {
         m_pageIndex = param1;
         m_num = param2;
      }
      
      public function getSocietyNum() : int
      {
         return !!m_societyList ? m_societyList.length : 0;
      }
      
      public function getSocietyTotalNum() : int
      {
         return m_societyTotalNum;
      }
      
      public function getSocietyDataByIndex(param1:int) : SocietyDataInList
      {
         if(m_societyList == null || param1 < 0 || param1 > m_societyList.length - 1)
         {
            throw new Error("index : " + param1 + "出错！");
         }
         return m_societyList[param1];
      }
      
      public function getDataDeadTime() : Number
      {
         return m_dataDeadTime;
      }
      
      public function getPageIndex() : int
      {
         return m_pageIndex;
      }
      
      public function getNum() : int
      {
         return m_num;
      }
   }
}

