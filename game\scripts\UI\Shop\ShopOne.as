package UI.Shop
{
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_AllBtn;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_EquipmentBtn;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_GemBtn;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_MaterialBtn;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_OtherBtn;
   import UI.Button.SwitchBtn.ShopSwitchBtn.Shop_ScrollBtn;
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.MySprite;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class ShopOne extends MySprite
   {
      public static const MONEY_SHOP:String = "moneyShop";
      
      public static const PK_POINT_SHOP:String = "pk点商店";
      
      private var _equipmentVOsArr:Vector.<Vector.<EquipmentVO>>;
      
      private var _sign:int = 0;
      
      private var _popUpBox:BuyPopUpBox;
      
      private const X:Number = 20;
      
      private const Y:Number = 55;
      
      private var _baseNum:int = 5;
      
      private var _pageBtn:PageBtnGroup;
      
      private var _potion:String;
      
      private var _btnArr:Vector.<SwitchBtn>;
      
      public function ShopOne(param1:String, param2:int)
      {
         super();
         _baseNum = param2;
         initShop(param1);
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function get equipmentsArr() : Vector.<Vector.<EquipmentVO>>
      {
         return _equipmentVOsArr;
      }
      
      public function addGoods(param1:Vector.<Vector.<EquipmentVO>>) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1)
         {
            _sign = 0;
            _loc3_ = 0;
            _loc2_ = int(param1.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               _equipmentVOsArr[_loc3_] = param1[_loc3_];
               _loc3_++;
            }
            setShopPage(1);
            arrangeGoods((_pageBtn.pageNum - 1) * 4);
         }
      }
      
      override public function clear() : void
      {
         var _loc4_:DisplayObject = null;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc4_ = getChildAt(0);
            if(_loc4_.hasOwnProperty("clear"))
            {
               _loc4_["clear"]();
            }
            removeChildAt(0);
         }
         if(_popUpBox)
         {
            _popUpBox.clear();
         }
         _popUpBox = null;
         if(_pageBtn)
         {
            _pageBtn.clear();
         }
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         if(_equipmentVOsArr)
         {
            _loc1_ = int(_equipmentVOsArr.length);
            _loc5_ = 0;
            while(_loc5_ < _loc1_)
            {
               if(_equipmentVOsArr[_loc5_])
               {
                  _loc2_ = int(_equipmentVOsArr[_loc5_].length);
                  _loc3_ = 0;
                  while(_loc3_ < _loc2_)
                  {
                     if(_equipmentVOsArr[_loc5_][_loc3_])
                     {
                        _equipmentVOsArr[_loc5_][_loc3_].clear();
                     }
                     _equipmentVOsArr[_loc5_][_loc3_] = null;
                     _loc3_++;
                  }
               }
               _equipmentVOsArr[_loc5_] = null;
               _loc5_++;
            }
            _equipmentVOsArr = null;
         }
         _pageBtn = null;
         if(_btnArr)
         {
            _loc1_ = int(_btnArr.length);
            _loc5_ = 0;
            while(_loc5_ < _loc1_)
            {
               _btnArr[_loc5_].clear();
               _btnArr[_loc5_] = null;
               _loc5_++;
            }
            _btnArr = null;
         }
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(_popUpBox)
         {
            if(getChildByName(_popUpBox.name))
            {
               removeChild(_popUpBox);
            }
            _popUpBox = null;
         }
      }
      
      protected function initShop(param1:String) : void
      {
         _potion = param1;
         _btnArr = new Vector.<SwitchBtn>();
         _equipmentVOsArr = new Vector.<Vector.<EquipmentVO>>();
         switch(param1)
         {
            case "moneyShop":
               _btnArr[0] = new Shop_AllBtn();
               _btnArr[0].x = 12.85;
               _btnArr[0].y = 13;
               addChild(_btnArr[0]);
               _btnArr[1] = new Shop_EquipmentBtn();
               _btnArr[1].x = 105.85;
               _btnArr[1].y = 13;
               addChild(_btnArr[1]);
               _btnArr[2] = new Shop_GemBtn();
               _btnArr[2].x = 201.85;
               _btnArr[2].y = 13;
               addChild(_btnArr[2]);
               _btnArr[3] = new Shop_OtherBtn();
               _btnArr[3].x = 295.85;
               _btnArr[3].y = 13;
               addChild(_btnArr[3]);
               break;
            case "pk点商店":
               _btnArr[0] = new Shop_ScrollBtn();
               _btnArr[0].x = 12.85;
               _btnArr[0].y = 13;
               addChild(_btnArr[0]);
               _btnArr[1] = new Shop_MaterialBtn();
               _btnArr[1].x = 125.85;
               _btnArr[1].y = 13;
               addChild(_btnArr[1]);
               _btnArr[2] = new Shop_OtherBtn();
               _btnArr[2].x = 241.85;
               _btnArr[2].y = 13;
               addChild(_btnArr[2]);
               break;
            default:
               throw new Error("类型出错!");
         }
         var _loc3_:int = 0;
         var _loc2_:int = int(_btnArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(!_loc3_)
            {
               _btnArr[_loc3_].init(false);
            }
            else
            {
               _btnArr[_loc3_].init(true);
            }
            _loc3_++;
         }
         _pageBtn = new PageBtnGroup();
         _pageBtn.x = 210;
         _pageBtn.y = 318;
         addChild(_pageBtn);
         setShopPage(1);
      }
      
      protected function setShopPage(param1:int) : void
      {
         var _loc2_:int = 0;
         if(!_equipmentVOsArr.length)
         {
            return;
         }
         if(!Boolean(_equipmentVOsArr[_sign]) || !_equipmentVOsArr[_sign].length)
         {
            _pageBtn.initPageNumber(1,1);
            return;
         }
         _loc2_ = int(_equipmentVOsArr[_sign].length);
         if(_loc2_ % 4 == 0)
         {
            _pageBtn.initPageNumber(param1,_equipmentVOsArr[_sign].length / 4);
         }
         else
         {
            _pageBtn.initPageNumber(param1,int(_equipmentVOsArr[_sign].length / 4) + 1);
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("showBox",showBox,true,0,true);
         addEventListener("cancelBuy",hideBox,true,0,true);
         addEventListener("clickShopSwitchBtn",switchShop,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("showBox",showBox,true);
         removeEventListener("cancelBuy",hideBox,true);
         removeEventListener("clickShopSwitchBtn",switchShop,true);
      }
      
      protected function pageUp(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function pageDown(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function switchShop(param1:UIBtnEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(_btnArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_btnArr[_loc3_] == param1.target)
            {
               _sign = _loc3_;
            }
            else
            {
               _btnArr[_loc3_].gotoTwoFrame();
            }
            _loc3_++;
         }
         setShopPage(1);
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function switchToALL(param1:UIBtnEvent) : void
      {
         _sign = 0;
         setShopPage(1);
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function switchToEquipment(param1:UIBtnEvent) : void
      {
         _sign = 1;
         setShopPage(1);
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function switchToOther(param1:UIBtnEvent) : void
      {
         _sign = 2;
         setShopPage(1);
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      protected function arrangeGoods(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc5_:DisplayObject = null;
         var _loc6_:* = 0;
         var _loc2_:EquipmentSlot = null;
         var _loc3_:int = param1 + 4;
         while(numChildren > _baseNum)
         {
            _loc5_ = getChildAt(_baseNum);
            removeChildAt(_baseNum);
            if(_loc5_.hasOwnProperty("clear"))
            {
               _loc5_["clear"]();
            }
         }
         _loc4_ = int(_equipmentVOsArr[_sign].length);
         _loc6_ = param1;
         while(_loc6_ < _loc3_ && _loc6_ < _loc4_)
         {
            if(_equipmentVOsArr[_sign][_loc6_] != null)
            {
               _loc2_ = new EquipmentSlot(_equipmentVOsArr[_sign][_loc6_],_potion);
               _loc2_.x = 20;
               _loc2_.y = 63.5 * (_loc6_ - param1) + 55;
               addChild(_loc2_);
            }
            _loc6_++;
         }
      }
      
      protected function showBox(param1:UIDataEvent) : void
      {
         var _loc3_:String = null;
         switch(_potion)
         {
            case "moneyShop":
               _loc3_ = "shopMoneyPrice";
               break;
            case "pk点商店":
               _loc3_ = "shopPkPointPrice";
               break;
            default:
               throw new Error();
         }
         var _loc2_:EquipmentVO = param1.data.equipmentVO;
         if(getChildByName("popUpBox"))
         {
            _popUpBox.initBox(_loc2_,_loc3_);
         }
         else
         {
            _popUpBox = new BuyPopUpBox(_loc2_,_loc3_);
            _popUpBox.x = 50;
            _popUpBox.y = 56;
            _popUpBox.name = "popUpBox";
            addChild(_popUpBox);
         }
      }
      
      override public function set visible(param1:Boolean) : void
      {
         super.visible = param1;
         hideBox();
      }
   }
}

