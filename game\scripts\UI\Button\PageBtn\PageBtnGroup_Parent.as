package UI.Button.PageBtn
{
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PageBtnGroup_Parent extends MySprite
   {
      protected var _showSprite:*;
      
      protected var _pageNum:int;
      
      protected var _pageCount:int;
      
      public function PageBtnGroup_Parent()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function initPageNumber(param1:int = 1, param2:int = 4) : void
      {
         _pageNum = param1;
         _pageCount = param2;
         _showSprite.pageNumText.text = _pageNum.toString();
         _showSprite.pageCountText.text = _pageCount.toString();
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         _showSprite.pageBtnUp = null;
         _showSprite.pageBtnDown = null;
         _showSprite.pageNumText = null;
         _showSprite.pageCountText = null;
      }
      
      public function get pageNum() : int
      {
         return _pageNum;
      }
      
      public function get pageCount() : int
      {
         return _pageCount;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickPageBtn",clickPageBtn,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("clickPageBtn",clickPageBtn);
         removeEventListener("removedFromStage",removeFromStage);
      }
      
      protected function clickPageBtn(param1:UIBtnEvent) : void
      {
         if(param1.target == _showSprite.pageBtnUp && _pageNum > 1)
         {
            _pageNum -= 1;
            _showSprite.pageNumText.text = _pageNum.toString();
            dispatchEvent(new UIBtnEvent("pageUp"));
         }
         else if(param1.target == _showSprite.pageBtnDown && _pageNum < _pageCount)
         {
            _pageNum += 1;
            _showSprite.pageNumText.text = _pageNum.toString();
            dispatchEvent(new UIBtnEvent("pageDown"));
         }
      }
      
      protected function init() : void
      {
         addChild(_showSprite);
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         (_showSprite.pageNumText as TextField).defaultTextFormat = new TextFormat(_loc1_.fontName,15,16777215);
         (_showSprite.pageCountText as TextField).defaultTextFormat = new TextFormat(_loc1_.fontName,15,16777215);
      }
   }
}

