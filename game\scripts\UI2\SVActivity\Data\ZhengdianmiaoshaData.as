package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.IEquipmentVOsData;
   import YJFY.Utils.ClearUtil;
   
   public class ZhengdianmiaoshaData extends DataManagerParent implements IEquipmentVOsData
   {
      private var m_startTime:String;
      
      private var m_stopTime:String;
      
      private var m_proTime:int;
      
      private var m_remainTime:int;
      
      private var m_nprice:Vector.<int>;
      
      private var m_npriceid:Vector.<int>;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      private var m_strName:Vector.<String>;
      
      private var m_strOPrice:Vector.<String>;
      
      private var m_strNPrice:Vector.<String>;
      
      private var m_strNum:Vector.<String>;
      
      private var m_nIdList:Vector.<int>;
      
      private var m_nOpenTime:Vector.<int>;
      
      private var m_nMaxNum:Vector.<int>;
      
      private var m_nCurrIndex:int;
      
      public function ZhengdianmiaoshaData()
      {
         super();
         m_strName = new Vector.<String>();
         m_strOPrice = new Vector.<String>();
         m_strNPrice = new Vector.<String>();
         m_strNum = new Vector.<String>();
         m_nIdList = new Vector.<int>();
         m_nprice = new Vector.<int>();
         m_npriceid = new Vector.<int>();
         m_nOpenTime = new Vector.<int>();
         m_nMaxNum = new Vector.<int>();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         ClearUtil.clearObject(m_strName);
         m_strOPrice = null;
         ClearUtil.clearObject(m_strOPrice);
         m_strOPrice = null;
         ClearUtil.clearObject(m_strNPrice);
         m_strNPrice = null;
         ClearUtil.clearObject(m_strNum);
         m_strNum = null;
         ClearUtil.clearObject(m_nIdList);
         m_nIdList = null;
         ClearUtil.clearObject(m_nprice);
         m_nprice = null;
         ClearUtil.clearObject(m_npriceid);
         m_npriceid = null;
         ClearUtil.clearObject(m_nOpenTime);
         m_nOpenTime = null;
         ClearUtil.clearObject(m_nMaxNum);
         m_nMaxNum = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.m_remainTime = uint(param1.@remainTime);
         this.m_proTime = uint(param1.@proTime);
         this.m_startTime = String(param1.@startTime);
         this.m_stopTime = String(param1.@endTime);
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
         ClearUtil.clearObject(m_strName);
         ClearUtil.clearObject(m_strOPrice);
         ClearUtil.clearObject(m_strNPrice);
         ClearUtil.clearObject(m_strNum);
         ClearUtil.clearObject(m_nIdList);
         ClearUtil.clearObject(m_nprice);
         ClearUtil.clearObject(m_npriceid);
         ClearUtil.clearObject(m_nOpenTime);
         ClearUtil.clearObject(m_nMaxNum);
         var _loc2_:XMLList = param1.children();
         for each(var _loc3_ in _loc2_)
         {
            m_strName.push(_loc3_.@name);
            m_strOPrice.push(_loc3_.@oprice);
            m_strNPrice.push(_loc3_.@nowprice);
            m_strNum.push(_loc3_.@maxnum);
            m_nIdList.push(int(_loc3_.@id));
            m_nprice.push(int(_loc3_.@ticketPrice));
            m_npriceid.push(int(_loc3_.@ticketPriceId));
            m_nMaxNum.push(int(_loc3_.@maxnum));
            this.addTime(int(_loc3_.@opentme));
         }
      }
      
      private function addTime(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(m_nOpenTime.length);
         var _loc2_:int = 1;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(this.m_nOpenTime[_loc4_] == param1)
            {
               _loc2_ = 0;
            }
            _loc4_++;
         }
         if(_loc2_ == 1)
         {
            this.m_nOpenTime.push(param1);
         }
      }
      
      public function refreshID(param1:Array) : void
      {
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.remainTime = m_remainTime;
         _antiwear.protime = m_proTime;
         _antiwear.startTime = m_startTime;
         _antiwear.endTime = m_stopTime;
      }
      
      public function getEquipmentVONum() : uint
      {
         return 1;
      }
      
      public function getEquipmentVOByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[m_nCurrIndex];
      }
      
      public function getInfoByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
      
      public function getRemainTime() : uint
      {
         return m_remainTime;
      }
      
      public function getProTime() : uint
      {
         return m_remainTime;
      }
      
      public function getStartTime() : String
      {
         return m_startTime;
      }
      
      public function getEndTime() : String
      {
         return m_stopTime;
      }
      
      public function getNameByIndex(param1:int) : String
      {
         return this.m_strName[param1];
      }
      
      public function getOPriceByIndex(param1:int) : String
      {
         return this.m_strOPrice[param1];
      }
      
      public function getNPriceByIndex(param1:int) : String
      {
         return this.m_strNPrice[param1];
      }
      
      public function getNPriceIntByIndex(param1:int) : int
      {
         return int(this.m_strNPrice[param1]);
      }
      
      public function getNumByIndex(param1:int) : String
      {
         return this.m_strNum[param1];
      }
      
      public function getIdByIndex(param1:int) : int
      {
         return this.m_nIdList[param1];
      }
      
      public function getPriceByIndex(param1:int) : int
      {
         return this.m_nprice[param1];
      }
      
      public function getPriceIdByIndex(param1:int) : int
      {
         return this.m_npriceid[param1];
      }
      
      public function getOpenTimeByIndex(param1:int) : int
      {
         return this.m_nOpenTime[param1];
      }
      
      public function getMaxNumByIndex(param1:int) : int
      {
         return this.m_nMaxNum[param1];
      }
      
      public function setCurrIndex(param1:int) : void
      {
         this.m_nCurrIndex = param1;
      }
   }
}

