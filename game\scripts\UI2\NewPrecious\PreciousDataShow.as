package UI2.NewPrecious
{
   import UI.LogicShell.MyHaveLockSwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class PreciousDataShow extends MyHaveLockSwitchBtnLogicShell
   {
      private var m_remainNumText:TextField;
      
      private var m_needLv:TextField;
      
      private var m_pictruecontainerMC:MovieClipPlayLogicShell;
      
      private var m_remainNum:uint;
      
      private var m_data:InfoData;
      
      public function PreciousDataShow()
      {
         super();
         m_pictruecontainerMC = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pictruecontainerMC);
         m_pictruecontainerMC = null;
         super.clear();
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
      }
      
      private function initShow() : void
      {
         m_pictruecontainerMC.setShow(m_show["pictureContainer"]);
         m_remainNumText = m_show["txtnum"] as TextField;
         m_remainNumText.text = "";
      }
      
      public function setData(param1:InfoData) : void
      {
         m_data = param1;
         m_pictruecontainerMC.gotoAndStop(m_data.showFrameLabel);
         showRemainNum();
      }
      
      private function showRemainNum() : void
      {
         m_remainNumText = m_show["txtnum"] as TextField;
         m_remainNumText.visible = true;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remainNumText);
         if(m_remainNumText != null && m_data != null)
         {
            m_remainNumText.text = "剩余次数:" + PreDataInfo.getInstance().getNumByIndex(m_data.id);
         }
      }
      
      public function showNeedLv(param1:int) : void
      {
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remainNumText);
         if(m_remainNumText != null)
         {
            m_remainNumText.visible = true;
            m_remainNumText.text = "所需等级:" + param1;
         }
      }
      
      public function showNoOpen() : void
      {
         if(m_remainNumText)
         {
            m_remainNumText.visible = false;
            m_pictruecontainerMC.gotoAndStop("3");
         }
      }
      
      public function getShowData() : InfoData
      {
         return m_data;
      }
      
      override public function lock() : void
      {
         super.lock();
      }
      
      override public function unLock() : void
      {
         super.unLock();
      }
   }
}

