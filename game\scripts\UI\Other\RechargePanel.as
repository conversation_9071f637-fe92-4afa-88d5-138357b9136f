package UI.Other
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   
   public class RechargePanel extends MySprite
   {
      private var _show:Sprite;
      
      private var _rechargeBtn:ButtonLogicShell2;
      
      private var _quitBtn:ButtonLogicShell;
      
      private var _lookDetailBtn:ButtonLogicShell2;
      
      private var _wantLoadSources:Array = ["rechargePanel"];
      
      public function RechargePanel()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         _show = null;
         if(_rechargeBtn)
         {
            _rechargeBtn.clear();
         }
         _rechargeBtn = null;
         if(_quitBtn)
         {
            _quitBtn.clear();
         }
         _quitBtn = null;
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
      }
      
      private function init() : void
      {
         var loadFinishListener:LoadFinishListener1;
         addEventListener("addedToStage",addToStage,false,0,true);
         loadFinishListener = new LoadFinishListener1(function():void
         {
            _show = MyFunction2.returnShowByClassName("RechargePanel") as Sprite;
            addChild(_show);
            _rechargeBtn = new ButtonLogicShell2();
            _rechargeBtn.setShow(_show["rechargeBtn"]);
            _rechargeBtn.setTipString("点击充值");
            _quitBtn = new ButtonLogicShell();
            _quitBtn.setShow(_show["quitBtnT"]);
            _quitBtn.setTipString("点击退出");
            _lookDetailBtn = new ButtonLogicShell2();
            _lookDetailBtn.setShow(_show["lookDetailBtn"]);
            _lookDetailBtn.setTipString("点击查看充值详细信息");
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _rechargeBtn:
               AnalogServiceHoldFunction.getInstance().payMoney_As3();
               break;
            case _quitBtn:
               if(parent)
               {
                  parent.removeChild(this);
               }
               clear();
               break;
            case _lookDetailBtn:
               navigateToURL(new URLRequest(String(XMLSingle.getInstance().dataXML.LookRechargeDetail[0].@url)),"_blank");
         }
      }
   }
}

