package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_ApplyJoinReturn;
   
   public class TEST_DOWN_ApplyJoinReturn extends DOWN_ApplyJoinReturn
   {
      public function TEST_DOWN_ApplyJoinReturn()
      {
         super();
         m_informationBodyId = 3013;
      }
      
      public function initData(param1:int, param2:int) : void
      {
         m_data = param1;
         m_societyId = param2;
      }
   }
}

