package deng.fzip
{
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.net.URLRequest;
   import flash.net.URLStream;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import flash.utils.IDataInput;
   import flash.utils.IDataOutput;
   
   public class FZip extends EventDispatcher
   {
      private var charEncoding:String;
      
      private var filesDict:Dictionary;
      
      private var urlStream:URLStream;
      
      private var currentFile:FZipFile;
      
      private var parseFunc:Function;
      
      private var filesList:Array;
      
      public function FZip(param1:String = "utf-8")
      {
         super();
         charEncoding = param1;
         parseFunc = parseIdle;
      }
      
      protected function parse(param1:IDataInput) : Boolean
      {
         while(parseFunc(param1))
         {
         }
         return parseFunc === parseIdle;
      }
      
      protected function defaultErrorHandler(param1:Event) : void
      {
         close();
         dispatchEvent(param1.clone());
      }
      
      public function getFileCount() : uint
      {
         return !!filesList ? filesList.length : 0;
      }
      
      public function getFileAt(param1:uint) : FZipFile
      {
         return !!filesList ? filesList[param1] as FZipFile : null;
      }
      
      public function get active() : Boolean
      {
         return parseFunc !== parseIdle;
      }
      
      public function addFileFromStringAt(param1:uint, param2:String, param3:String, param4:String = "utf-8") : FZipFile
      {
         if(filesList == null)
         {
            filesList = [];
         }
         if(filesDict == null)
         {
            filesDict = new Dictionary();
         }
         else if(filesDict[param2])
         {
            throw new Error("File already exists: " + param2 + ". Please remove first.");
         }
         var _loc5_:FZipFile = new FZipFile();
         _loc5_.filename = param2;
         _loc5_.setContentAsString(param3,param4);
         if(param1 >= filesList.length)
         {
            filesList.push(_loc5_);
         }
         else
         {
            filesList.splice(param1,0,_loc5_);
         }
         filesDict[param2] = _loc5_;
         return _loc5_;
      }
      
      private function parseSignature(param1:IDataInput) : Boolean
      {
         var _loc2_:* = 0;
         if(param1.bytesAvailable >= 4)
         {
            _loc2_ = uint(param1.readUnsignedInt());
            switch(_loc2_)
            {
               case 67324752:
                  parseFunc = parseLocalfile;
                  currentFile = new FZipFile(charEncoding);
                  break;
               case 33639248:
               case 101010256:
                  parseFunc = parseIdle;
                  break;
               default:
                  throw new Error("Unknown record signature.");
            }
            return true;
         }
         return false;
      }
      
      protected function removeEventHandlers() : void
      {
         urlStream.removeEventListener("complete",defaultHandler);
         urlStream.removeEventListener("open",defaultHandler);
         urlStream.removeEventListener("httpStatus",defaultHandler);
         urlStream.removeEventListener("ioError",defaultErrorHandler);
         urlStream.removeEventListener("securityError",defaultErrorHandler);
         urlStream.removeEventListener("progress",progressHandler);
      }
      
      public function addFileAt(param1:uint, param2:String, param3:ByteArray = null) : FZipFile
      {
         if(filesList == null)
         {
            filesList = [];
         }
         if(filesDict == null)
         {
            filesDict = new Dictionary();
         }
         else if(filesDict[param2])
         {
            throw new Error("File already exists: " + param2 + ". Please remove first.");
         }
         var _loc4_:FZipFile = new FZipFile();
         _loc4_.filename = param2;
         _loc4_.content = param3;
         if(param1 >= filesList.length)
         {
            filesList.push(_loc4_);
         }
         else
         {
            filesList.splice(param1,0,_loc4_);
         }
         filesDict[param2] = _loc4_;
         return _loc4_;
      }
      
      protected function addEventHandlers() : void
      {
         urlStream.addEventListener("complete",defaultHandler);
         urlStream.addEventListener("open",defaultHandler);
         urlStream.addEventListener("httpStatus",defaultHandler);
         urlStream.addEventListener("ioError",defaultErrorHandler);
         urlStream.addEventListener("securityError",defaultErrorHandler);
         urlStream.addEventListener("progress",progressHandler);
      }
      
      public function getFileByName(param1:String) : FZipFile
      {
         return !!filesDict[param1] ? filesDict[param1] as FZipFile : null;
      }
      
      protected function defaultHandler(param1:Event) : void
      {
         dispatchEvent(param1.clone());
      }
      
      protected function progressHandler(param1:Event) : void
      {
         var _loc2_:* = param1;
         dispatchEvent(_loc2_.clone());
         try
         {
            if(parse(urlStream))
            {
               close();
               dispatchEvent(new Event("complete"));
            }
         }
         catch(e:Error)
         {
            close();
            if(!hasEventListener("parseError"))
            {
               throw e;
            }
            dispatchEvent(new FZipErrorEvent("parseError",e.message));
         }
      }
      
      public function serialize(param1:IDataOutput, param2:Boolean = false) : void
      {
         var _loc8_:String = null;
         var _loc3_:ByteArray = null;
         var _loc4_:* = 0;
         var _loc5_:* = 0;
         var _loc6_:int = 0;
         var _loc7_:FZipFile = null;
         if(param1 != null && filesList.length > 0)
         {
            _loc8_ = param1.endian;
            _loc3_ = new ByteArray();
            _loc3_.endian = "littleEndian";
            param1.endian = "littleEndian";
            _loc4_ = 0;
            _loc5_ = 0;
            _loc6_ = 0;
            while(_loc6_ < filesList.length)
            {
               _loc7_ = filesList[_loc6_] as FZipFile;
               if(_loc7_ != null)
               {
                  _loc7_.serialize(_loc3_,param2,true,_loc4_);
                  _loc4_ += _loc7_.serialize(param1,param2);
                  _loc5_++;
               }
               _loc6_++;
            }
            if(_loc3_.length > 0)
            {
               param1.writeBytes(_loc3_);
            }
            param1.writeUnsignedInt(101010256);
            param1.writeShort(0);
            param1.writeShort(0);
            param1.writeShort(_loc5_);
            param1.writeShort(_loc5_);
            param1.writeUnsignedInt(_loc3_.length);
            param1.writeUnsignedInt(_loc4_);
            param1.writeShort(0);
            param1.endian = _loc8_;
         }
      }
      
      public function loadBytes(param1:ByteArray) : void
      {
         if(!urlStream && parseFunc == parseIdle)
         {
            filesList = [];
            filesDict = new Dictionary();
            param1.position = 0;
            param1.endian = "littleEndian";
            parseFunc = parseSignature;
            if(parse(param1))
            {
               parseFunc = parseIdle;
               dispatchEvent(new Event("complete"));
            }
            else
            {
               dispatchEvent(new FZipErrorEvent("parseError","EOF"));
            }
         }
      }
      
      public function load(param1:URLRequest) : void
      {
         if(!urlStream && parseFunc == parseIdle)
         {
            urlStream = new URLStream();
            urlStream.endian = "littleEndian";
            addEventHandlers();
            filesList = [];
            filesDict = new Dictionary();
            parseFunc = parseSignature;
            urlStream.load(param1);
         }
      }
      
      private function parseIdle(param1:IDataInput) : Boolean
      {
         return false;
      }
      
      private function parseLocalfile(param1:IDataInput) : Boolean
      {
         if(currentFile.parse(param1))
         {
            filesList.push(currentFile);
            if(currentFile.filename)
            {
               filesDict[currentFile.filename] = currentFile;
            }
            dispatchEvent(new FZipEvent("fileLoaded",currentFile));
            currentFile = null;
            if(parseFunc != parseIdle)
            {
               parseFunc = parseSignature;
               return true;
            }
         }
         return false;
      }
      
      public function close() : void
      {
         if(urlStream)
         {
            parseFunc = parseIdle;
            removeEventHandlers();
            urlStream.close();
            urlStream = null;
         }
      }
      
      public function removeFileAt(param1:uint) : FZipFile
      {
         var _loc2_:FZipFile = null;
         if(filesList != null && filesDict != null && param1 < filesList.length)
         {
            _loc2_ = filesList[param1] as FZipFile;
            if(_loc2_ != null)
            {
               filesList.splice(param1,1);
               delete filesDict[_loc2_.filename];
               return _loc2_;
            }
         }
         return null;
      }
      
      public function addFile(param1:String, param2:ByteArray = null) : FZipFile
      {
         return addFileAt(!!filesList ? filesList.length : 0,param1,param2);
      }
      
      public function addFileFromString(param1:String, param2:String, param3:String = "utf-8") : FZipFile
      {
         return addFileFromStringAt(!!filesList ? filesList.length : 0,param1,param2,param3);
      }
   }
}

