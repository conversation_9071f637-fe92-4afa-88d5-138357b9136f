package UI.InformationPanel
{
   import UI.MySprite;
   import UI.UIInterface.IBarMask;
   
   public class StarMask extends MySprite implements IBarMask
   {
      public function StarMask()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
      }
   }
}

