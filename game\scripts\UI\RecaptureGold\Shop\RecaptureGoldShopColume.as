package UI.RecaptureGold.Shop
{
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.Parent.IProp;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.utils.getQualifiedClassName;
   
   public class RecaptureGoldShopColume extends MySprite
   {
      public static const SHOW_PROP_DESCRIPTION:String = "showPropDescription";
      
      public static const BUY_PROP:String = "buyProp";
      
      private var _priceText:TextField;
      
      private var _prop:IProp;
      
      private var _price:int;
      
      private var _propDescription:String;
      
      private var _show:MovieClip;
      
      public function RecaptureGoldShopColume(param1:XML, param2:IProp)
      {
         super();
         init(param1,param2);
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         _priceText = null;
         if(_prop)
         {
            _prop.clear();
         }
         _prop = null;
         _show = null;
      }
      
      private function init(param1:XML, param2:IProp) : void
      {
         _show = MyFunction2.returnShowByClassName("UI.RecaptureGold.Shop.RecaptureGoldShopColumeShow") as MovieClip;
         addChild(_show);
         _priceText = _show["priceText"];
         _prop = param2;
         var _loc4_:String = getQualifiedClassName(_prop);
         (_show as MovieClip).gotoAndStop(_loc4_);
         var _loc3_:Vector.<int> = MyFunction.getInstance().excreteString(param1.@price);
         _price = _loc3_[0] + Math.round(Math.random() * (_loc3_[1] - _loc3_[0]));
         _priceText.text = _price + "两";
         _propDescription = String(param1.@description);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",buyProp,false,0,true);
         addEventListener("rollOver",showPropDescription,false,0,true);
         addEventListener("rollOut",showPropDescription,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",buyProp,false);
         removeEventListener("rollOver",showPropDescription,false);
         removeEventListener("rollOut",showPropDescription,false);
      }
      
      private function buyProp(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("buyProp",{
            "prop":_prop,
            "price":_price
         }));
      }
      
      private function showPropDescription(param1:MouseEvent) : void
      {
         if(param1.type == "rollOver")
         {
            _show.nextFrame();
            dispatchEvent(new RecaptureGoldEvent("showPropDescription",_propDescription));
         }
         else
         {
            _show.prevFrame();
         }
      }
   }
}

