package UI.Farm.CoordGrid
{
   public class SceneCoordGrids
   {
      public static var _instance:SceneCoordGrids;
      
      private var _sceneCoordGrids:Vector.<Vector.<SceneCoordGrid>>;
      
      public function SceneCoordGrids()
      {
         super();
         if(!_instance)
         {
            initGrid();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : SceneCoordGrids
      {
         if(!_instance)
         {
            _instance = new SceneCoordGrids();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(_sceneCoordGrids)
         {
            _loc1_ = int(_sceneCoordGrids.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_sceneCoordGrids[_loc4_])
               {
                  _loc2_ = int(_sceneCoordGrids[_loc4_].length);
                  _loc3_ = 0;
                  while(_loc3_ < _loc2_)
                  {
                     if(_sceneCoordGrids[_loc4_][_loc3_])
                     {
                        _sceneCoordGrids[_loc4_][_loc3_].clear();
                     }
                     _sceneCoordGrids[_loc4_][_loc3_] = null;
                     _loc3_++;
                  }
                  _sceneCoordGrids[_loc4_] = null;
               }
               _loc4_++;
            }
            _sceneCoordGrids = null;
         }
         _instance = null;
      }
      
      public function returnSceneCoordByHV(param1:int, param2:int) : SceneCoordGrid
      {
         return _sceneCoordGrids[param1 - -30][param2 - -30];
      }
      
      public function returnSceneCoordsLength() : int
      {
         return _sceneCoordGrids.length;
      }
      
      public function returnHVByXY(param1:Number, param2:Number) : Array
      {
         var _loc7_:Number = Math.atan(0.5);
         var _loc3_:Number = 15 / Math.sin(_loc7_);
         var _loc6_:Number = param1 - 0;
         var _loc8_:Number = param2 - 0;
         var _loc4_:int = Math.round((_loc6_ / Math.cos(_loc7_) * 0.5 - _loc8_ / Math.sin(_loc7_) * 0.5) / _loc3_);
         var _loc5_:int = Math.round((_loc6_ / Math.cos(_loc7_) * 0.5 + _loc8_ / Math.sin(_loc7_) * 0.5) / _loc3_);
         return [_loc4_,_loc5_];
      }
      
      private function initGrid() : void
      {
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc1_:SceneCoordGrid = null;
         _sceneCoordGrids = new Vector.<Vector.<SceneCoordGrid>>();
         _loc9_ = 0;
         while(_loc9_ < 61)
         {
            _sceneCoordGrids.push(new Vector.<SceneCoordGrid>());
            _loc6_ = 0;
            while(_loc6_ < 61)
            {
               _loc8_ = 0 + (_loc9_ + -30 + (_loc6_ + -30)) * 0.5 * 60;
               _loc7_ = 0 + (_loc6_ + -30 - (_loc9_ + -30)) * 0.5 * 30;
               _loc1_ = new SceneCoordGrid();
               _loc1_.x = _loc8_;
               _loc1_.y = _loc7_;
               _loc1_.hI = _loc9_ + -30;
               _loc1_.vJ = _loc6_ + -30;
               _loc1_.isHaveThing = null;
               _sceneCoordGrids[_loc9_].push(_loc1_);
               _loc6_++;
            }
            _loc9_++;
         }
      }
   }
}

