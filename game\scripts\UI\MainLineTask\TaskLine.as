package UI.MainLineTask
{
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class TaskLine extends MySwitchBtnLogicShell
   {
      private var _taskNameText:TextField;
      
      private var _taskStateShow:MovieClipPlayLogicShell;
      
      private var _progressText:TextField;
      
      private var _taskVO:MainLineTaskVO;
      
      public function TaskLine()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         _taskNameText = null;
         ClearUtil.clearObject(_taskStateShow);
         _taskStateShow = null;
         _progressText = null;
         _taskVO = null;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         _taskNameText = m_show["taskNameTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_taskNameText);
         _taskNameText.selectable = false;
         _taskNameText.mouseEnabled = false;
         _progressText = m_show["progressTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_progressText);
         _progressText.selectable = false;
         _progressText.mouseEnabled = false;
         _taskStateShow = new MovieClipPlayLogicShell();
         _taskStateShow.setShow(m_show["taskStateShow"]);
      }
      
      public function setTaskVO(param1:MainLineTaskVO) : void
      {
         if(param1)
         {
            _taskVO = param1;
         }
         m_show["taskNameTxt"].text = _taskVO.name;
         var _loc4_:* = !!_taskVO.getCurrentTaskGoalByIndex(0) ? _taskVO.getCurrentTaskGoalByIndex(0).num : 0;
         var _loc3_:int = _taskVO.getNeedTaskGoalByIndex(0).num;
         if(_taskVO.isGotReward)
         {
            _loc4_ = _loc3_;
         }
         var _loc2_:TextFormat = _progressText.defaultTextFormat;
         if(_loc4_ >= _loc3_)
         {
            _loc2_.color = 52224;
         }
         else
         {
            _loc2_.color = 16711680;
         }
         _progressText.defaultTextFormat = _loc2_;
         _progressText.text = "（" + _loc4_ + "/" + _loc3_ + "）";
         _progressText.x = _taskNameText.x + _taskNameText.textWidth + 5;
         if(_taskVO.isGotReward)
         {
            _taskStateShow.gotoAndStop("gotReward");
         }
         else if(_taskVO.isFinish)
         {
            _taskStateShow.gotoAndStop("finish");
         }
         else
         {
            _taskStateShow.gotoAndStop("unFinish");
         }
      }
      
      public function getTaskVO() : MainLineTaskVO
      {
         return _taskVO;
      }
      
      override public function turnActivate() : void
      {
         super.turnActivate();
         var _loc1_:String = _taskNameText.text;
         _taskNameText = m_show["taskNameTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_taskNameText);
         _taskNameText.text = _loc1_;
      }
      
      override protected function turnInActivate() : void
      {
         super.turnInActivate();
         var _loc1_:String = _taskNameText.text;
         _taskNameText = m_show["taskNameTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_taskNameText);
         _taskNameText.text = _loc1_;
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         super.onOut(param1);
         var _loc2_:String = _taskNameText.text;
         _taskNameText = m_show["taskNameTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_taskNameText);
         _taskNameText.text = _loc2_;
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         super.onOver(param1);
         var _loc2_:String = _taskNameText.text;
         _taskNameText = m_show["taskNameTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,_taskNameText);
         _taskNameText.text = _loc2_;
      }
   }
}

