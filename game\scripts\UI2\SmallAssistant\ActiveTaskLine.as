package UI2.SmallAssistant
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI2.SmallAssistant.ActiveTask.HaveActiveValueTaskData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ActiveTaskLine
   {
      private var m_taskNameText:TextField;
      
      private var m_taskStateText:TextField;
      
      private var m_activeValueText:TextField;
      
      private var m_btnOrStateMC:MovieClipPlayLogicShell;
      
      private var m_gotoBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_haveActiveValueTaskData:HaveActiveValueTaskData;
      
      public function ActiveTaskLine()
      {
         super();
         m_btnOrStateMC = new MovieClipPlayLogicShell();
      }
      
      public function clear() : void
      {
         m_taskNameText = null;
         m_taskStateText = null;
         m_activeValueText = null;
         ClearUtil.clearObject(m_btnOrStateMC);
         m_btnOrStateMC = null;
         ClearUtil.clearObject(m_gotoBtn);
         m_gotoBtn = null;
         m_show = null;
         m_haveActiveValueTaskData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setHaveActiveValueTaskData(param1:HaveActiveValueTaskData) : void
      {
         m_haveActiveValueTaskData = param1;
         initShow2();
      }
      
      public function getHaveActiveValueTaskData() : HaveActiveValueTaskData
      {
         return m_haveActiveValueTaskData;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getGotoBtn() : ButtonLogicShell2
      {
         return m_gotoBtn;
      }
      
      private function initShow() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_taskNameText = m_show["taskNameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_taskNameText);
         m_taskStateText = m_show["taskStateText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_taskStateText);
         m_activeValueText = m_show["activeValueText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_activeValueText);
         m_btnOrStateMC.setShow(m_show["btnOrState"]);
         initGotoBtnFrame_btnOrStateMC();
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_haveActiveValueTaskData == null)
         {
            return;
         }
         m_taskNameText.text = m_haveActiveValueTaskData.getTaskVO().name;
         var _loc3_:* = !!m_haveActiveValueTaskData.getTaskVO().getCurrentTaskGoalByIndex(0) ? m_haveActiveValueTaskData.getTaskVO().getCurrentTaskGoalByIndex(0).num : 0;
         var _loc2_:int = m_haveActiveValueTaskData.getTaskVO().getNeedTaskGoalByIndex(0).num;
         if(m_haveActiveValueTaskData.getTaskVO().isGotReward)
         {
            _loc3_ = _loc2_;
         }
         var _loc1_:TextFormat = m_taskStateText.defaultTextFormat;
         if(_loc3_ >= _loc2_)
         {
            _loc1_.color = 52224;
         }
         else
         {
            _loc1_.color = 16711680;
         }
         m_taskStateText.defaultTextFormat = _loc1_;
         m_taskStateText.text = _loc3_ + "/" + _loc2_;
         m_activeValueText.text = m_haveActiveValueTaskData.getActiveValueDataOfTask().getActiveValue().toString();
         if(m_haveActiveValueTaskData.getTaskVO().isFinish)
         {
            initCompleteFrame_btnOrStateMC();
         }
         else
         {
            initGotoBtnFrame_btnOrStateMC();
         }
      }
      
      private function initGotoBtnFrame_btnOrStateMC() : void
      {
         frameClear_btnOrStateMC();
         m_btnOrStateMC.gotoAndStop("gotoBtn");
         m_gotoBtn = new ButtonLogicShell2();
         m_gotoBtn.setShow(m_btnOrStateMC.getShow()["gotoBtn"]);
      }
      
      private function initCompleteFrame_btnOrStateMC() : void
      {
         frameClear_btnOrStateMC();
         m_btnOrStateMC.gotoAndStop("complete");
      }
      
      private function frameClear_btnOrStateMC() : void
      {
         ClearUtil.clearObject(m_gotoBtn);
         m_gotoBtn = null;
      }
   }
}

