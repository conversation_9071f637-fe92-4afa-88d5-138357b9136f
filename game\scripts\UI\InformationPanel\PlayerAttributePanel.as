package UI.InformationPanel
{
   import UI.Button.QuitBtn3;
   import UI.Event.UIPassiveEvent;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.XMLSingle;
   import com.greensock.TweenLite;
   import com.greensock.easing.Expo;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PlayerAttributePanel extends MySprite
   {
      public var bloodNameText:TextField;
      
      public var magicNameText:TextField;
      
      public var regMpNameText:TextField;
      
      public var regHpNameText:TextField;
      
      public var attackNameText:TextField;
      
      public var defenseNameText:TextField;
      
      public var hitNameText:TextField;
      
      public var dodgeNameText:TextField;
      
      public var criticalPointNameText:TextField;
      
      public var riotNameText:TextField;
      
      public var characterNameText:TextField;
      
      public var offensiveNameText:TextField;
      
      public var bloodValueText:TextField;
      
      public var magicValueText:TextField;
      
      public var attackPointText:TextField;
      
      public var defensePointText:TextField;
      
      public var criticalPointText:TextField;
      
      public var regMpText:TextField;
      
      public var regHpText:TextField;
      
      public var hitText:TextField;
      
      public var dodgeText:TextField;
      
      public var riotValueText:TextField;
      
      public var offensiveText:TextField;
      
      public var characterValueText:TextField;
      
      public var wenHao:Sprite;
      
      public var quitBtn3:QuitBtn3;
      
      private var _player:Player;
      
      public function PlayerAttributePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         TweenLite.killTweensOf(this);
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         bloodValueText = null;
         magicValueText = null;
         attackPointText = null;
         defensePointText = null;
         criticalPointText = null;
         regMpText = null;
         regHpText = null;
         hitText = null;
         dodgeText = null;
         riotValueText = null;
         offensiveText = null;
         characterValueText = null;
         bloodNameText = null;
         magicNameText = null;
         regMpNameText = null;
         regHpNameText = null;
         attackNameText = null;
         defenseNameText = null;
         hitNameText = null;
         dodgeNameText = null;
         criticalPointNameText = null;
         riotNameText = null;
         characterNameText = null;
         wenHao = null;
         if(quitBtn3)
         {
            quitBtn3.clear();
         }
         quitBtn3 = null;
         _player = null;
      }
      
      public function refreshPanel(param1:Player) : void
      {
         _player = param1;
         bloodValueText.text = param1.playerVO.bloodVolume.toString();
         magicValueText.text = param1.playerVO.maxMagic.toString();
         attackPointText.text = param1.playerVO.attack.toString();
         defensePointText.text = param1.playerVO.defence.toString();
         criticalPointText.text = param1.playerVO.criticalRate.toString();
         regMpText.text = param1.playerVO.regMp.toString();
         regHpText.text = param1.playerVO.regHp.toString();
         hitText.text = (param1.playerVO.hit * 100).toFixed(2) + "%";
         dodgeText.text = (param1.playerVO.dodge * 100).toFixed(2) + "%";
         riotValueText.text = (param1.playerVO.riotValue * 100).toFixed(2) + "%";
         characterValueText.text = param1.playerVO.renPin.toString();
         offensiveText.text = (param1.playerVO.offensiveValue / 10).toFixed(2) + "%";
      }
      
      public function playAddedAnimation() : void
      {
         TweenLite.from(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn
         });
      }
      
      public function playRemovedAnimation(param1:Function, param2:Array) : void
      {
         TweenLite.to(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn,
            "onComplete":param1,
            "onCompleteParams":param2
         });
      }
      
      private function init() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         bloodNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         magicNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         regMpNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         regHpNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         attackNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         defenseNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         hitNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         dodgeNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         criticalPointNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         riotNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         characterNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,13395456);
         bloodValueText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         magicValueText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         attackPointText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         defensePointText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         criticalPointText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         regMpText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         regHpText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         hitText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         dodgeText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         riotValueText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         offensiveText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         characterValueText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         bloodNameText.embedFonts = true;
         magicNameText.embedFonts = true;
         regMpNameText.embedFonts = true;
         regHpNameText.embedFonts = true;
         attackNameText.embedFonts = true;
         defenseNameText.embedFonts = true;
         hitNameText.embedFonts = true;
         dodgeNameText.embedFonts = true;
         criticalPointNameText.embedFonts = true;
         riotNameText.embedFonts = true;
         characterNameText.embedFonts = true;
         bloodNameText.selectable = false;
         magicNameText.selectable = false;
         regMpNameText.selectable = false;
         regHpNameText.selectable = false;
         attackNameText.selectable = false;
         defenseNameText.selectable = false;
         hitNameText.selectable = false;
         dodgeNameText.selectable = false;
         criticalPointNameText.selectable = false;
         riotNameText.selectable = false;
         characterNameText.selectable = false;
         bloodValueText.selectable = false;
         magicValueText.selectable = false;
         attackPointText.selectable = false;
         defensePointText.selectable = false;
         criticalPointText.selectable = false;
         regMpText.selectable = false;
         regHpText.selectable = false;
         hitText.selectable = false;
         dodgeText.selectable = false;
         riotValueText.selectable = false;
         offensiveText.selectable = false;
         characterValueText.selectable = false;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("mouseDown",mouseDownBox,false,0,true);
         addEventListener("mouseUp",mouseUpBox,false,0,true);
         wenHao.addEventListener("mouseOver",onOver,false,0,true);
         wenHao.addEventListener("mouseOut",onOut,false,0,true);
         bloodNameText.addEventListener("mouseOver",onOver,false,0,true);
         bloodNameText.addEventListener("mouseOut",onOut,false,0,true);
         magicNameText.addEventListener("mouseOver",onOver,false,0,true);
         magicNameText.addEventListener("mouseOut",onOut,false,0,true);
         regMpNameText.addEventListener("mouseOver",onOver,false,0,true);
         regMpNameText.addEventListener("mouseOut",onOut,false,0,true);
         regHpNameText.addEventListener("mouseOver",onOver,false,0,true);
         regHpNameText.addEventListener("mouseOut",onOut,false,0,true);
         attackNameText.addEventListener("mouseOver",onOver,false,0,true);
         attackNameText.addEventListener("mouseOut",onOut,false,0,true);
         defenseNameText.addEventListener("mouseOver",onOver,false,0,true);
         defenseNameText.addEventListener("mouseOut",onOut,false,0,true);
         hitNameText.addEventListener("mouseOver",onOver,false,0,true);
         hitNameText.addEventListener("mouseOut",onOut,false,0,true);
         dodgeNameText.addEventListener("mouseOver",onOver,false,0,true);
         dodgeNameText.addEventListener("mouseOut",onOut,false,0,true);
         criticalPointNameText.addEventListener("mouseOver",onOver,false,0,true);
         criticalPointNameText.addEventListener("mouseOut",onOut,false,0,true);
         riotNameText.addEventListener("mouseOver",onOver,false,0,true);
         riotNameText.addEventListener("mouseOut",onOut,false,0,true);
         characterNameText.addEventListener("mouseOver",onOver,false,0,true);
         characterNameText.addEventListener("mouseOut",onOut,false,0,true);
         offensiveNameText.addEventListener("mouseOver",onOver,false,0,true);
         offensiveNameText.addEventListener("mouseOut",onOut,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         wenHao.removeEventListener("mouseOver",onOver,false);
         wenHao.removeEventListener("mouseOut",onOut,false);
         removeEventListener("mouseDown",mouseDownBox,false);
         removeEventListener("mouseUp",mouseUpBox,false);
         bloodNameText.removeEventListener("mouseOver",onOver,false);
         bloodNameText.removeEventListener("mouseOut",onOut,false);
         magicNameText.removeEventListener("mouseOver",onOver,false);
         magicNameText.removeEventListener("mouseOut",onOut,false);
         regMpNameText.removeEventListener("mouseOver",onOver,false);
         regMpNameText.removeEventListener("mouseOut",onOut,false);
         regHpNameText.removeEventListener("mouseOver",onOver,false);
         regHpNameText.removeEventListener("mouseOut",onOut,false);
         attackNameText.removeEventListener("mouseOver",onOver,false);
         attackNameText.removeEventListener("mouseOut",onOut,false);
         defenseNameText.removeEventListener("mouseOver",onOver,false);
         defenseNameText.removeEventListener("mouseOut",onOut,false);
         hitNameText.removeEventListener("mouseOver",onOver,false);
         hitNameText.removeEventListener("mouseOut",onOut,false);
         dodgeNameText.removeEventListener("mouseOver",onOver,false);
         dodgeNameText.removeEventListener("mouseOut",onOut,false);
         criticalPointNameText.removeEventListener("mouseOver",onOver,false);
         criticalPointNameText.removeEventListener("mouseOut",onOut,false);
         riotNameText.removeEventListener("mouseOver",onOver,false);
         riotNameText.removeEventListener("mouseOut",onOut,false);
         characterNameText.removeEventListener("mouseOver",onOver,false);
         characterNameText.removeEventListener("mouseOut",onOut,false);
         offensiveNameText.removeEventListener("mouseOver",onOver,false);
         offensiveNameText.removeEventListener("mouseOut",onOut,false);
      }
      
      private function mouseDownBox(param1:MouseEvent) : void
      {
         if(parent)
         {
            if(parent is MyControlPanel)
            {
               parent.setChildIndex(this,parent.getChildIndex((parent as MyControlPanel).topLayer) - 1);
            }
            else
            {
               parent.setChildIndex(this,parent.numChildren - 1);
            }
         }
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      private function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      private function test(param1:Event) : void
      {
         var _loc2_:Point = null;
         if(stage)
         {
            _loc2_ = localToGlobal(new Point(mouseX,mouseY));
            if(_loc2_.x > stage.stageWidth)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x -= 10;
            }
            if(_loc2_.x < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x += 10;
            }
            if(_loc2_.y > stage.stageHeight)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y -= 10;
            }
            if(_loc2_.y < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y += 10;
            }
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         if(param1.target == wenHao)
         {
            dispatchEvent(new UIPassiveEvent("showMessageBox",{
               "equipment":{"target":"charactText"},
               "player":_player
            }));
         }
         else
         {
            dispatchEvent(new UIPassiveEvent("showMessageBox",{
               "equipment":{
                  "target":"showAttributeExplain",
                  "text":String(XMLSingle.getInstance().textXML.child("showAttributeExplain")[0][(param1.target.name as String).substring(0,param1.target.name.length - 8)])
               },
               "player":null
            }));
         }
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

