package UI.RecaptureGold
{
   import UI.DataManagerParent;
   
   public class RecaptureGoldData extends DataManagerParent
   {
      private static var _instance:RecaptureGoldData = null;
      
      private var _allGoldNum:int = 0;
      
      private var _catchItemNum:int;
      
      private var _rGNum:int;
      
      private var _rGDate:String;
      
      private var _buyNum:int;
      
      public function RecaptureGoldData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : RecaptureGoldData
      {
         if(!_instance)
         {
            _instance = new RecaptureGoldData();
         }
         return _instance;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.allGoldNum = _allGoldNum;
         _antiwear.catchItemNum = _catchItemNum;
         _antiwear.rGNum = _rGNum;
         _antiwear.rGDate = _rGDate;
         _antiwear.buyNum = _buyNum;
      }
      
      override public function clear() : void
      {
         super.clear();
         _instance = null;
      }
      
      public function get allGoldNum() : int
      {
         return _antiwear.allGoldNum;
      }
      
      public function set allGoldNum(param1:int) : void
      {
         _antiwear.allGoldNum = param1;
      }
      
      public function get catchItemNum() : int
      {
         return _antiwear.catchItemNum;
      }
      
      public function set catchItemNum(param1:int) : void
      {
         _antiwear.catchItemNum = param1;
      }
      
      public function get rGNum() : int
      {
         return _antiwear.rGNum;
      }
      
      public function set rGNum(param1:int) : void
      {
         _antiwear.rGNum = param1;
      }
      
      public function get rGDate() : String
      {
         return _antiwear.rGDate;
      }
      
      public function set rGDate(param1:String) : void
      {
         _antiwear.rGDate = param1;
      }
      
      public function get buyNum() : int
      {
         return _antiwear.buyNum;
      }
      
      public function set buyNum(param1:int) : void
      {
         _antiwear.buyNum = param1;
      }
   }
}

