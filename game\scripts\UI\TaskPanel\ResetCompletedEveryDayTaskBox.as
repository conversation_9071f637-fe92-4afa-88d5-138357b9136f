package UI.TaskPanel
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.PointTicketBuyBox;
   import UI.Task.TaskFunction;
   import UI.Task.TasksManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ResetCompletedEveryDayTaskBox extends PointTicketBuyBox
   {
      public var ticketText:TextField;
      
      public function ResetCompletedEveryDayTaskBox()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ticketText = null;
      }
      
      override protected function init() : void
      {
         ticketText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.embedFonts = true;
         ticketText.text = String(XMLSingle.getInstance().dataXML.ResetCompleteEveryDayTask.@price * (TasksManager.getInstance().resetCount + 1));
         super.init();
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var price:int;
         var e:UIBtnEvent = param1;
         var myParent:ExTaskPanel = parent as ExTaskPanel;
         if(e.target == _sureBtn)
         {
            price = XMLSingle.getInstance().dataXML.ResetCompleteEveryDayTask.@price * (TasksManager.getInstance().resetCount + 1);
            AnalogServiceHoldFunction.getInstance().buyByPointTicket(price,function(param1:Function):void
            {
               var dec:Function = param1;
               dec(price,function():void
               {
                  TaskFunction.getInstance().resetCompletedTask();
                  TasksManager.getInstance().resetCount++;
                  var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                  _loc1_.type = "4399";
                  _loc1_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc1_);
                  MyFunction2.saveGame2();
               },stage,myParent.showWarningBox);
            },stage,myParent.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         super.clickBtn(e);
      }
   }
}

