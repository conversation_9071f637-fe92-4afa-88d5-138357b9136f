package UI.Shop.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class PKShopSwitchBtn extends SwitchBtn
   {
      public function PKShopSwitchBtn()
      {
         super();
         setTipString("PK点杂货");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchShop"));
      }
   }
}

