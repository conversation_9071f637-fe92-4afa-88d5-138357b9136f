package UI.Pets.Bar
{
   import UI.ChangeBar;
   
   public class PetExperienceBar extends ChangeBar
   {
      public var redBar:PetPurpleBar;
      
      public var bloodBarMask:PetExperienceBarMask;
      
      public function PetExperienceBar()
      {
         super();
         _width = redBar.width;
         _heidth = redBar.height;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(redBar)
         {
            redBar.clear();
         }
         redBar = null;
         if(bloodBarMask)
         {
            bloodBarMask.clear();
         }
         bloodBarMask = null;
      }
      
      public function changeExpericenceBar(param1:Number, param2:int = 0) : void
      {
         changebar(redBar,bloodBarMask,param1,param2);
      }
   }
}

