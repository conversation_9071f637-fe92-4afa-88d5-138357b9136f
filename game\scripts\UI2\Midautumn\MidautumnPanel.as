package UI2.Midautumn
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class MidautumnPanel extends MovieClip
   {
      private var m_myloader:YJFYLoader;
      
      private var m_show:MovieClip;
      
      private var m_view:MidautumnView;
      
      private var m_wzwView:WzwView;
      
      private var m_num:int;
      
      public function MidautumnPanel()
      {
         super();
         Part1.getInstance().showGameWaitShow();
         this.name = "MidautumnPanel";
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myloader.getXML("UIData/SVActivity/midautumn.xml",getXMLSuccess,getFail);
         m_myloader.getClass("UISprite2/midautumn.swf","midautumnpanel",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var loaderData:YJFYLoaderData = param1;
         MidautumnData.getInstance().initXML(XML(loaderData.resultXML));
         m_num++;
         if(m_num >= 2)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               showPanel(param1);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
               Part1.getInstance().hideGameWaitShow();
            },true);
         }
      }
      
      private function showPanel(param1:String) : void
      {
         if(TimeUtil.getTimeUtil().getInTime(MidautumnData.getInstance().getStartTime1(),MidautumnData.getInstance().getEndTime1(),param1))
         {
            m_view = new MidautumnView();
            m_view.init(m_show,this);
         }
         else
         {
            m_wzwView = new WzwView();
            m_wzwView.init(m_show,this);
         }
         Part1.getInstance().hideGameWaitShow();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var loaderData:YJFYLoaderData = param1;
         var cla:Class = loaderData.resultClass;
         m_show = new cla();
         this.addChild(m_show);
         m_num++;
         if(m_num >= 2)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               showPanel(param1);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      public function CloseUI(param1:MouseEvent = null) : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         clear();
         GamingUI.getInstance().clearMidautumnPanel();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_view);
         m_view = null;
         ClearUtil.clearObject(m_wzwView);
         m_wzwView = null;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_view)
         {
            m_view.render(param1);
         }
         if(m_wzwView)
         {
            m_wzwView.render(param1);
         }
      }
   }
}

