package UI.InformationPanel
{
   import UI.ChangeBar;
   import UI.UIInterface.ISegmentedBar;
   
   public class Star extends ChangeBar implements ISegmentedBar
   {
      public var yellowStar:YellowStar;
      
      public var starMask:StarMask;
      
      private var _bM_x:Number;
      
      public function Star()
      {
         super();
         _width = yellowStar.width;
         _heidth = yellowStar.height;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(yellowStar)
         {
            yellowStar.clear();
         }
         yellowStar = null;
         if(starMask)
         {
            starMask.clear();
         }
         starMask = null;
      }
      
      public function change(param1:Number, param2:Boolean = false) : void
      {
         changebar(yellowStar,starMask,param1,0,param2);
      }
   }
}

