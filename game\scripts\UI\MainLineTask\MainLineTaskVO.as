package UI.MainLineTask
{
   import UI.MainLineTask.TaskDescription.TaskDescriptionVOFactory;
   import UI.MainLineTask.TaskDescription.TaskDescriptionVO_MainLineTask;
   import UI.MainLineTask.TaskDetectors.TaskDetector;
   import UI.MainLineTask.TaskDetectors.TaskDetectorFactory;
   import UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO;
   import UI.MainLineTask.TaskRewardVO.EquipmentRewardVO;
   import UI.MainLineTask.TaskRewardVO.ExperienceRewardVO;
   import UI.MainLineTask.TaskRewardVO.MoneyRewardVO;
   import UI.MainLineTask.TaskRewardVO.TaskRewardVO;
   import UI.MainLineTask.TaskRewardVO.TaskRewardVOFactory;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.newTask.NewMainTask.NewGotoData;
   import YJFY.Utils.ClearUtil;
   
   public class MainLineTaskVO
   {
      public var id:String;
      
      public var name:String;
      
      public var isNew:Boolean;
      
      public var taskLevel:Number;
      
      public var description:TaskDescriptionVO_MainLineTask;
      
      public var descriptionId:String;
      
      public var isGotReward:Boolean;
      
      public var txtDescripton:String;
      
      public var isWorking:Boolean = false;
      
      public var isFinish:Boolean;
      
      public var gotoInfo:NewGotoData;
      
      private var _currentTaskGoals:Vector.<TaskGoalVO_MainTask>;
      
      private var _needTaskGoals:Vector.<TaskGoalVO_MainTask>;
      
      public var taskRewardVOs:Vector.<TaskRewardVO>;
      
      public var taskDetectors:Vector.<TaskDetector>;
      
      public function MainLineTaskVO()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(description);
         description = null;
         ClearUtil.nullArr(_currentTaskGoals);
         _currentTaskGoals = null;
         ClearUtil.nullArr(_needTaskGoals);
         _needTaskGoals = null;
         ClearUtil.nullArr(taskRewardVOs);
         taskRewardVOs = null;
         ClearUtil.clearObject(gotoInfo);
         gotoInfo = null;
      }
      
      public function initByXML(param1:XML, param2:XML) : void
      {
         var _loc5_:XML = null;
         var _loc8_:TaskDetectorFactory = null;
         var _loc7_:XMLList = null;
         var _loc9_:int = 0;
         var _loc4_:int = 0;
         id = String(param1.@id);
         name = String(param1.@name);
         isNew = Boolean(int(param1.@isNew));
         taskLevel = Number(param1.@taskLevel);
         if(param1.hasOwnProperty("gototask"))
         {
            _loc5_ = param1.gototask[0];
            if(String(_loc5_.@type) == "1" || String(_loc5_.@type) == "2")
            {
               gotoInfo = new NewGotoData();
               gotoInfo.type = String(_loc5_.@type);
               if(_loc5_.hasOwnProperty("swfpath"))
               {
                  gotoInfo.swfpath = String(_loc5_.swfpath[0].@value);
               }
               if(_loc5_.hasOwnProperty("xmlpath"))
               {
                  gotoInfo.xmlpath = String(_loc5_.xmlpath[0].@value);
               }
               if(_loc5_.hasOwnProperty("classname"))
               {
                  gotoInfo.showClassName = String(_loc5_.classname[0].@value);
               }
               if(_loc5_.hasOwnProperty("gotoname"))
               {
                  gotoInfo.gotobtnname = String(_loc5_.gotoname[0].@value);
               }
            }
            else if(String(_loc5_.@type) == "3" || String(_loc5_.@type) == "4" || String(_loc5_.@type) == "5" || String(_loc5_.@type) == "6" || String(_loc5_.@type) == "7" || String(_loc5_.@type) == "8" || String(_loc5_.@type) == "9")
            {
               gotoInfo = new NewGotoData();
               gotoInfo.type = String(_loc5_.@type);
            }
         }
         descriptionId = String(param1.@descriptionId);
         var _loc6_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _needTaskGoals = _loc6_.createTaskGoalsByXML(param1.taskGoals[0],param2);
         _loc6_.clear();
         var _loc3_:TaskRewardVOFactory = new TaskRewardVOFactory();
         if(param1.hasOwnProperty("taskRewards"))
         {
            taskRewardVOs = _loc3_.createTaskRewardVOsByXML(param1.taskRewards[0]);
         }
         _loc3_.clear();
         if(param1.hasOwnProperty("taskDetector"))
         {
            _loc8_ = new TaskDetectorFactory();
            _loc7_ = param1.taskDetector;
            _loc4_ = int(!!_loc7_ ? _loc7_.length() : 0);
            taskDetectors = new Vector.<TaskDetector>();
            _loc9_ = 0;
            while(_loc9_ < _loc4_)
            {
               taskDetectors.push(_loc8_.createTaskDetectorByXML(_loc7_[_loc9_]));
               _loc9_++;
            }
            _loc8_.clear();
         }
      }
      
      public function initTaskDescription(param1:XML) : void
      {
         var _loc2_:XML = null;
         var _loc3_:TaskDescriptionVOFactory = null;
         if(description == null)
         {
            _loc2_ = param1.taskDescription.(@id == descriptionId)[0];
            _loc3_ = new TaskDescriptionVOFactory();
            description = _loc3_.createTaskDescriptionByXML(_loc2_);
            _loc3_.clear();
         }
      }
      
      public function addNewTaskGoal(param1:TaskGoalVO_MainTask) : TaskGoalVO_MainTask
      {
         var _loc6_:int = 0;
         var _loc3_:TaskGoalVO_MainTask = null;
         var _loc2_:Boolean = false;
         var _loc5_:TaskGoalVO_MainTask = null;
         var _loc4_:int = !!_needTaskGoals ? _needTaskGoals.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            if(_needTaskGoals[_loc6_].id == param1.id)
            {
               _loc3_ = _needTaskGoals[_loc6_];
               break;
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            _loc4_ = !!_currentTaskGoals ? _currentTaskGoals.length : 0;
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               if(_currentTaskGoals[_loc6_].id == param1.id)
               {
                  _loc5_ = _currentTaskGoals[_loc6_];
                  break;
               }
               _loc6_++;
            }
            if(_loc5_)
            {
               if(_loc5_.num < _loc3_.num)
               {
                  _loc5_.num = Math.max(param1.num,param1.num + _loc5_.num);
                  _loc2_ = true;
               }
            }
            else
            {
               if(_currentTaskGoals == null)
               {
                  _currentTaskGoals = new Vector.<TaskGoalVO_MainTask>();
               }
               _currentTaskGoals.push(param1);
               _loc2_ = true;
            }
         }
         if(_loc2_)
         {
            judeIsFinishTask();
            return param1;
         }
         return null;
      }
      
      public function addNewTaskGoal2(param1:TaskGoalVO_MainTask) : TaskGoalVO_MainTask
      {
         var _loc6_:int = 0;
         var _loc3_:TaskGoalVO_MainTask = null;
         var _loc2_:Boolean = false;
         var _loc5_:TaskGoalVO_MainTask = null;
         if(isGotReward)
         {
            return null;
         }
         var _loc4_:int = !!_needTaskGoals ? _needTaskGoals.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            if(_needTaskGoals[_loc6_].id == param1.id)
            {
               _loc3_ = _needTaskGoals[_loc6_];
               break;
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            _loc4_ = !!_currentTaskGoals ? _currentTaskGoals.length : 0;
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               if(_currentTaskGoals[_loc6_].id == param1.id)
               {
                  _loc5_ = _currentTaskGoals[_loc6_];
                  break;
               }
               _loc6_++;
            }
            if(_loc5_)
            {
               if(_loc5_.num < _loc3_.num)
               {
                  _loc5_.num = Math.max(param1.num,param1.num + _loc5_.num);
                  _loc2_ = true;
               }
            }
            else
            {
               if(_currentTaskGoals == null)
               {
                  _currentTaskGoals = new Vector.<TaskGoalVO_MainTask>();
               }
               _currentTaskGoals.push(param1);
               _loc2_ = true;
            }
         }
         if(_loc2_)
         {
            judeIsFinishTask();
            return param1;
         }
         return null;
      }
      
      public function reset() : void
      {
         ClearUtil.clearObject(_currentTaskGoals);
         _currentTaskGoals = null;
         _currentTaskGoals = new Vector.<TaskGoalVO_MainTask>();
         this.isFinish = false;
         this.isGotReward = false;
      }
      
      public function getCurrentTaskGoalNum() : int
      {
         return !!_currentTaskGoals ? _currentTaskGoals.length : 0;
      }
      
      public function getCurrentTaskGoalByIndex(param1:int) : TaskGoalVO_MainTask
      {
         if(_currentTaskGoals == null || param1 > _currentTaskGoals.length - 1 || param1 < 0)
         {
            return null;
         }
         return _currentTaskGoals[param1];
      }
      
      public function addTaskGoalsToCurrentTaskGoalVOs(param1:Vector.<TaskGoalVO_MainTask>) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = !!param1 ? param1.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            addNewTaskGoal(param1[_loc3_]);
            _loc3_++;
         }
      }
      
      public function addTaskGoalsToCurrentTaskGoalVOs2(param1:Vector.<TaskGoalVO_MainTask>) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = !!param1 ? param1.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            addNewTaskGoal2(param1[_loc3_]);
            _loc3_++;
         }
      }
      
      public function getNeedTaskGoalNum() : int
      {
         return !!_needTaskGoals ? _needTaskGoals.length : 0;
      }
      
      public function getNeedTaskGoalByIndex(param1:int) : TaskGoalVO_MainTask
      {
         if(_needTaskGoals == null || param1 > _needTaskGoals.length - 1 || param1 < 0)
         {
            return null;
         }
         return _needTaskGoals[param1];
      }
      
      public function judeIsFinishTask() : void
      {
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc1_:TaskGoalVO_MainTask = null;
         var _loc5_:TaskGoalVO_MainTask = null;
         var _loc3_:Boolean = false;
         this.isFinish = false;
         _loc4_ = !!_needTaskGoals ? _needTaskGoals.length : 0;
         var _loc2_:Boolean = true;
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc1_ = _needTaskGoals[_loc8_];
            if(_loc1_ != null)
            {
               _loc6_ = !!_currentTaskGoals ? _currentTaskGoals.length : 0;
               _loc3_ = false;
               _loc7_ = 0;
               while(_loc7_ < _loc6_)
               {
                  _loc5_ = _currentTaskGoals[_loc8_];
                  if(_loc5_ != null)
                  {
                     if(_loc5_.id == _loc1_.id)
                     {
                        _loc3_ = true;
                        if(_loc5_.num != _loc1_.num)
                        {
                           if(_loc2_)
                           {
                              _loc2_ = false;
                           }
                           break;
                        }
                     }
                  }
                  _loc7_++;
               }
               if(_loc3_ == false)
               {
                  if(_loc2_)
                  {
                     _loc2_ = false;
                  }
               }
            }
            _loc8_++;
         }
         if(_loc2_)
         {
            this.isFinish = true;
         }
      }
      
      public function getTaskReward(param1:Player, param2:IGetTaskRewardListener) : void
      {
         var i:int;
         var length:int;
         var taskRewardVO:TaskRewardVO;
         var packageNotSpace:Boolean;
         var notChooseEquipmentVOs:Boolean;
         var choiceEquipmentRewardVO:ChoiceEquipmentRewardVO;
         var player:Player = param1;
         var getTaskRewardListener:IGetTaskRewardListener = param2;
         if(getTaskRewardListener == null)
         {
            throw new Error();
         }
         if(isFinish == false)
         {
            getTaskRewardListener.taskNotFinish();
            return;
         }
         if(isGotReward == true)
         {
            getTaskRewardListener.rewardHaveBeGot();
            return;
         }
         length = int(taskRewardVOs.length);
         i = 0;
         while(i < length)
         {
            taskRewardVO = taskRewardVOs[i];
            if(taskRewardVO is EquipmentRewardVO)
            {
               MyFunction2.falseAddEquipmentVOs((taskRewardVO as EquipmentRewardVO).getEquipmentVOs(),player,function():void
               {
                  packageNotSpace = true;
               },function():void
               {
               },null,null);
            }
            else if(taskRewardVO is ChoiceEquipmentRewardVO)
            {
               choiceEquipmentRewardVO = taskRewardVO as ChoiceEquipmentRewardVO;
               if(choiceEquipmentRewardVO.getChooseEquipmentVOs() == null)
               {
                  notChooseEquipmentVOs = true;
                  break;
               }
               MyFunction2.falseAddEquipmentVOs(choiceEquipmentRewardVO.getChooseEquipmentVOs(),player,function():void
               {
                  packageNotSpace = true;
               },function():void
               {
               },null,null);
            }
            i++;
         }
         if(packageNotSpace)
         {
            getTaskRewardListener.packageNotHaveSpace();
            return;
         }
         if(notChooseEquipmentVOs)
         {
            getTaskRewardListener.notChoiceEquipmentVOs();
            return;
         }
         isGotReward = true;
         i = 0;
         while(i < length)
         {
            taskRewardVO = taskRewardVOs[i];
            if(taskRewardVO is EquipmentRewardVO)
            {
               MyFunction2.trueAddEquipmentVOs((taskRewardVO as EquipmentRewardVO).getEquipmentVOs(),player,function():void
               {
               },null);
            }
            else if(taskRewardVO is ChoiceEquipmentRewardVO)
            {
               MyFunction2.trueAddEquipmentVOs((taskRewardVO as ChoiceEquipmentRewardVO).getChooseEquipmentVOs(),player,function():void
               {
               },null);
            }
            else if(taskRewardVO is ExperienceRewardVO)
            {
               MyFunction.getInstance().addPlayerExperience(player,(taskRewardVO as ExperienceRewardVO).getExperience());
               MyFunction.getInstance().addPetExperience(player,(taskRewardVO as ExperienceRewardVO).getExperience());
            }
            else
            {
               if(!(taskRewardVO is MoneyRewardVO))
               {
                  throw new Error("任务奖励类型错误！");
               }
               player.playerVO.money += (taskRewardVO as MoneyRewardVO).getMoney();
            }
            i++;
         }
         getTaskRewardListener.successGetReward();
      }
   }
}

