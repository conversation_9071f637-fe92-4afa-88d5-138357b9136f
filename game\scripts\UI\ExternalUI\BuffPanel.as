package UI.ExternalUI
{
   import UI.Buff.Buff.Buff;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.BuffData;
   import UI.MySprite;
   import flash.display.Sprite;
   
   public class BuffPanel extends MySprite
   {
      private var publicBuffSprite:Sprite;
      
      private var playerOneBuffSprite:Sprite;
      
      private var playerTwoBuffSprite:Sprite;
      
      public function BuffPanel()
      {
         super();
         publicBuffSprite = new Sprite();
         publicBuffSprite.x = 10;
         publicBuffSprite.y = 240;
         addChild(publicBuffSprite);
         playerOneBuffSprite = new Sprite();
         playerOneBuffSprite.x = 10;
         playerOneBuffSprite.y = 125;
         playerTwoBuffSprite = new Sprite();
         playerTwoBuffSprite.x = 930;
         playerTwoBuffSprite.y = 125;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         publicBuffSprite = null;
         playerOneBuffSprite = null;
         playerTwoBuffSprite = null;
      }
      
      public function refreshBuff(param1:int) : void
      {
         if(param1 & 16384 && BuffData.getInstance().buffDrives)
         {
            arrangBuffs(BuffData.getInstance().buffDrives,publicBuffSprite,0,0,1);
         }
         if(param1 & 32768 && BuffData.getInstance().buffDrives_playerOne)
         {
            arrangBuffs(BuffData.getInstance().buffDrives_playerOne,playerOneBuffSprite,0,0,1);
         }
         if(param1 & 65536 && BuffData.getInstance().buffDrives_playerTwo)
         {
            arrangBuffs(BuffData.getInstance().buffDrives_playerTwo,playerTwoBuffSprite,0,0,-1);
         }
      }
      
      private function arrangBuffs(param1:Vector.<BuffDrive>, param2:Sprite, param3:Number, param4:Number, param5:Number) : void
      {
         var _loc7_:Buff = null;
         while(param2.numChildren > 0)
         {
            _loc7_ = param2.getChildAt(0) as Buff;
            param2.removeChildAt(0);
            _loc7_.clear();
         }
         var _loc8_:int = 0;
         var _loc6_:int = int(param1.length);
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            _loc7_ = new Buff(param1[_loc8_].buffVO);
            _loc7_.x = param3;
            _loc7_.y = param4 + _loc8_ * 45;
            param2.addChild(_loc7_);
            _loc8_++;
         }
      }
   }
}

