package UI2.jhsbPanel
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.JinHouSonLiData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class JhsbView extends MovieClip
   {
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_show:MovieClip;
      
      private var m_jinhousonliData:JinHouSonLiData;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:IVersionControl;
      
      private var m_time:TextField;
      
      private var m_freeTimes:TextField;
      
      private var m_openOneBtn:ButtonLogicShell2;
      
      private var m_openTenBtn:ButtonLogicShell2;
      
      private var m_clolseBtn:ButtonLogicShell2;
      
      private var _isstar:Boolean;
      
      private var currentXml:XML;
      
      private var m_activityXML:XML;
      
      private var _Equipments:Vector.<EquipmentVO>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var useFreeUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/giftbox/open";
      
      private var _isFree:Boolean = false;
      
      private var m_goodsTip:JhsbGoodTip;
      
      public function JhsbView()
      {
         super();
         m_openOneBtn = new ButtonLogicShell2();
         m_openTenBtn = new ButtonLogicShell2();
         m_clolseBtn = new ButtonLogicShell2();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         _isstar = false;
         this.name = "JhsbView";
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         removeEventListener("warningBox",sureOrCancel,true);
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_time = null;
         m_freeTimes = null;
         ClearUtil.clearObject(m_jinhousonliData);
         m_jinhousonliData = null;
         ClearUtil.clearObject(m_openOneBtn);
         m_openOneBtn = null;
         ClearUtil.clearObject(m_openTenBtn);
         m_openTenBtn = null;
         ClearUtil.clearObject(m_clolseBtn);
         m_clolseBtn = null;
         ClearUtil.clearObject(currentXml);
         currentXml = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(_Equipments);
         _Equipments = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         GamingUI.getInstance().clearJhsbPanel();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         referText();
      }
      
      public function init(param1:SVActivitySaveData, param2:IVersionControl) : void
      {
         m_svActivitySaveData = param1;
         m_versionControl = param2;
         if(m_myLoader)
         {
            throw new Error("出错了");
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getClass("UISprite2/JhsbPanel.swf","JhsbPanel",getShowSuccess,getFail);
         m_myLoader.getXML("UIData/SVActivity/jinhou.xml",getXMLSuccess,getFail);
         m_myLoader.getXML("UIData/SVActivity/svActivity.xml",getXMLSuccess2,getFail);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         this.addChild(m_show);
         this.initShow();
      }
      
      private function initShow() : void
      {
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_openOneBtn.setShow(m_show["openOneBtn"]);
         m_openOneBtn.setTipString("点击打开一个宝箱");
         m_openTenBtn.setShow(m_show["openTenBtn"]);
         m_openTenBtn.setTipString("点击打开十个宝箱");
         m_clolseBtn.setShow(m_show["closebtn"]);
         m_clolseBtn.setTipString("关闭");
         m_time = m_show["timeText"] as TextField;
         m_freeTimes = m_show["freeTimes"] as TextField;
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         currentXml = param1.resultXML;
         _isstar = true;
      }
      
      private function getXMLSuccess2(param1:YJFYLoaderData) : void
      {
         m_activityXML = param1.resultXML;
         if(m_activityXML.hasOwnProperty("jinHouSonLi"))
         {
            m_jinhousonliData = new JinHouSonLiData();
            m_jinhousonliData.initByXML(m_activityXML.jinHouSonLi[0]);
         }
      }
      
      private function processXmlForDate(param1:XML) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc4_:int = 0;
         _loc4_ = currentXml.item.length() - 1;
         while(_loc4_ >= 0)
         {
            if(String(currentXml.item[_loc4_].@startDate))
            {
               _loc3_ = new TimeUtil().timeInterval(currentXml.item[_loc4_].@startDate,TimeUtil.timeStr);
               _loc2_ = new TimeUtil().timeInterval(TimeUtil.timeStr,currentXml.item[_loc4_].@endDate);
               if(_loc3_ < 0 || _loc2_ < 0)
               {
                  delete currentXml.item[_loc4_];
               }
            }
            _loc4_--;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         trace("get xml fall");
      }
      
      private function referText() : void
      {
         var _loc3_:String = null;
         var _loc5_:String = null;
         var _loc6_:String = null;
         m_svActivitySaveData.runCheckOnlineTime();
         var _loc2_:int = m_svActivitySaveData.getHounianCountDownTime();
         var _loc7_:int = _loc2_ / 3600;
         var _loc1_:int = _loc2_ % 3600 / 60;
         var _loc4_:int = _loc2_ % 60;
         if(_loc7_ > 9)
         {
            _loc3_ = String(_loc7_);
         }
         else
         {
            _loc3_ = "0" + _loc7_;
         }
         if(_loc1_ > 9)
         {
            _loc5_ = String(_loc1_);
         }
         else
         {
            _loc5_ = "0" + _loc1_;
         }
         if(_loc4_ > 9)
         {
            _loc6_ = String(_loc4_);
         }
         else
         {
            _loc6_ = "0" + _loc4_;
         }
         m_time.text = _loc3_ + ":" + _loc5_ + ":" + _loc6_;
         m_freeTimes.text = String(m_svActivitySaveData.getHounianFreeTimes());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(_isstar == false)
         {
            return;
         }
         switch(param1.button)
         {
            case m_openOneBtn:
               checkOpenOne();
               break;
            case m_openTenBtn:
               checkOpenTen();
               break;
            case m_clolseBtn:
               clear();
         }
      }
      
      private function checkOpenOne() : void
      {
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
         {
            showWarningBox("背包空间不足,请先清理背包",0);
         }
         else if(m_svActivitySaveData.getHounianFreeTimes() > 0)
         {
            _isFree = true;
            seedToServer1();
         }
         else
         {
            _isFree = false;
            showWarningBox("是否花费" + m_jinhousonliData.getTicketPriceOne() + "点券购打开一个宝箱",3,{
               "type":"buyjinhousonli1",
               "okFunction":seedToServer1
            });
         }
      }
      
      private function seedToServer1() : void
      {
         var _loc8_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:EquipmentVO = null;
         _isstar = false;
         var _loc6_:int = int(currentXml.item.length());
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         var _loc5_:Array = [];
         _loc8_ = 0;
         while(_loc8_ < 1)
         {
            _loc1_ = Math.random() * _loc6_;
            _loc3_ = XMLSingle.getEquipmentVOByID(int(currentXml.item[_loc1_].@id),XMLSingle.getInstance().equipmentXML);
            _Equipments.push(_loc3_);
            _loc5_.push(_loc3_.id);
            _loc8_++;
         }
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",seedToServerCompleteHandler);
         _loc2_.addEventListener("ioError",onErrorseedToServer);
         _loc2_.addEventListener("securityError",onErrorseedToServer);
         var _loc4_:URLRequest = new URLRequest(this.useFreeUrl);
         _loc4_.method = "POST";
         _loc4_.contentType = "application/json";
         var _loc7_:Object = {};
         _loc7_.MD5 = "2B01530154A2C991";
         _loc7_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc7_.Items = _loc5_;
         _loc7_.Free = _isFree;
         _loc4_.data = MyJSON.encode(_loc7_);
         _loc2_.load(_loc4_);
      }
      
      private function seedToServer10() : void
      {
         var _loc8_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:EquipmentVO = null;
         _isstar = false;
         var _loc6_:int = int(currentXml.item.length());
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         var _loc5_:Array = [];
         _loc8_ = 0;
         while(_loc8_ < 11)
         {
            _loc1_ = Math.random() * _loc6_;
            _loc3_ = XMLSingle.getEquipmentVOByID(int(currentXml.item[_loc1_].@id),XMLSingle.getInstance().equipmentXML);
            _Equipments.push(_loc3_);
            _loc5_.push(_loc3_.id);
            _loc8_++;
         }
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",seedToServerCompleteHandler);
         _loc2_.addEventListener("ioError",onErrorseedToServer);
         _loc2_.addEventListener("securityError",onErrorseedToServer);
         var _loc4_:URLRequest = new URLRequest(this.useFreeUrl);
         _loc4_.method = "POST";
         _loc4_.contentType = "application/json";
         var _loc7_:Object = {};
         _loc7_.MD5 = "2B01530154A2C991";
         _loc7_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc7_.Items = _loc5_;
         _loc7_.Free = _isFree;
         _loc4_.data = MyJSON.encode(_loc7_);
         _loc2_.load(_loc4_);
      }
      
      private function seedToServerCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",seedToServerCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorseedToServer);
         param1.currentTarget.removeEventListener("securityError",onErrorseedToServer);
         var _loc2_:int = int(MyJSON.decode(param1.currentTarget.data).Result);
         if(_loc2_ == 1)
         {
            trace("抽奖成功");
         }
         else
         {
            showWarningBox("连接错误",0);
         }
         _isstar = true;
         if(_Equipments.length > 1)
         {
            if(_isFree)
            {
               pushGoodsToPackeg10();
            }
            else
            {
               buy10();
            }
         }
         else if(_isFree)
         {
            pushGoodsToPackeg1();
         }
         else
         {
            buy1();
         }
      }
      
      private function onErrorseedToServer(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",seedToServerCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorseedToServer);
         param1.currentTarget.removeEventListener("securityError",onErrorseedToServer);
         showWarningBox("连接错误",0);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         _isstar = true;
      }
      
      private function buy1() : void
      {
         var price:uint = uint(m_jinhousonliData.getTicketPriceOne());
         var ticketId:String = m_jinhousonliData.getTicketPriceIdOne();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买开启一次金猴宝箱";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            pushGoodsToPackeg1();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function checkOpenTen() : void
      {
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) >= 11)
         {
            if(m_svActivitySaveData.getHounianFreeTimes() >= 10)
            {
               _isFree = true;
               seedToServer10();
            }
            else
            {
               _isFree = false;
               showWarningBox("是否花费" + m_jinhousonliData.getTicketPriceTen() + "点券购打开十个宝箱",3,{
                  "type":"buyjinhousonli1",
                  "okFunction":seedToServer10
               });
            }
         }
         else
         {
            showWarningBox("背包空间不足11格,请先清理背包",0);
         }
      }
      
      private function buy10() : void
      {
         var price:uint = uint(m_jinhousonliData.getTicketPriceTen());
         var ticketId:String = m_jinhousonliData.getTicketPriceIdTen();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买开启十次金猴宝箱";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            pushGoodsToPackeg10();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function pushGoodsToPackeg1() : void
      {
         if(_isFree)
         {
            m_svActivitySaveData.setHounianFreeTimes(m_svActivitySaveData.getHounianFreeTimes() - 1);
         }
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            showGoodSTip(_Equipments,shareLunTan);
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },null);
      }
      
      private function pushGoodsToPackeg10() : void
      {
         if(_isFree)
         {
            m_svActivitySaveData.setHounianFreeTimes(m_svActivitySaveData.getHounianFreeTimes() - 10);
         }
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            showGoodSTip(_Equipments,shareLunTan);
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },null);
      }
      
      private function shareLunTan() : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/forums/thread-send-tagid-81260"),"_blank");
         trace("点击分享论坛");
      }
      
      public function showGoodSTip(param1:Vector.<EquipmentVO>, param2:Function, param3:int = 0) : void
      {
         m_goodsTip = new JhsbGoodTip();
         m_goodsTip.init(param1,param2,this,Part1.getInstance().getVersionControl(),param3);
         this.addChild(m_goodsTip);
      }
      
      public function hidGoodsTip() : void
      {
         if(m_goodsTip)
         {
            ClearUtil.clearObject(m_goodsTip);
            m_goodsTip = null;
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         else if(param1.data.detail == 2)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.cancelFunction))
            {
               (param1.data.task.cancelFunction as Function).apply(null,param1.data.task.cancelFunctionParams);
               ClearUtil.nullArr(param1.data.task.cancelFunctionParams,false,false);
               param1.data.task.cancelFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
   }
}

