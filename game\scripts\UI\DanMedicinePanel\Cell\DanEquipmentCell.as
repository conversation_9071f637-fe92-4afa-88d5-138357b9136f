package UI.DanMedicinePanel.Cell
{
   import UI.CellBorderSingle;
   import UI.EquipmentCell;
   import flash.display.DisplayObject;
   import flash.geom.Rectangle;
   
   public class DanEquipmentCell extends EquipmentCell
   {
      public var equipbackground:DanEquipmentCellBackground;
      
      public function DanEquipmentCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.visible == true)
         {
            CellBorderSingle.getInstance().x = -0.2;
            CellBorderSingle.getInstance().y = 0;
            CellBorderSingle.getInstance().width = 50;
            CellBorderSingle.getInstance().height = 50;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return equipbackground.getRect(param1);
      }
   }
}

