package UI.CollectTimePanel
{
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.VersionControl;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.system.System;
   import flash.text.TextField;
   
   public class CollectTimePanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_collectTimeData:CollectTimeData;
      
      private var m_collectTime:CollectTime;
      
      private var m_collectTimeReturnData:CollectTimeReturnData;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_startCollectBtn:ButtonLogicShell2;
      
      private var m_timeShow:Sprite;
      
      private var m_hourShow:MultiPlaceNumLogicShell;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_transformBtn1:ButtonLogicShell2;
      
      private var m_transformBtn2:ButtonLogicShell2;
      
      private var m_getExpTxt1:TextField;
      
      private var m_getExpTxt2:TextField;
      
      private var m_gamingUI:GamingUI;
      
      private var m_versionControl:VersionControl;
      
      private var m_currentCollectTime:uint;
      
      private var m_toTime:Number;
      
      public function CollectTimePanel()
      {
         super();
         m_collectTime = new CollectTime();
         m_collectTimeReturnData = new CollectTimeReturnData();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.clearObject(m_collectTimeData);
         m_collectTimeData = null;
         ClearUtil.clearObject(m_collectTime);
         m_collectTime = null;
         ClearUtil.clearObject(m_collectTimeReturnData);
         m_collectTimeReturnData = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_startCollectBtn);
         m_startCollectBtn = null;
         ClearUtil.clearObject(m_timeShow);
         m_timeShow = null;
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.clearObject(m_transformBtn1);
         m_transformBtn1 = null;
         ClearUtil.clearObject(m_transformBtn2);
         m_transformBtn2 = null;
         m_getExpTxt1 = null;
         m_getExpTxt2 = null;
         m_gamingUI = null;
         m_versionControl = null;
         super.clear();
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function init() : void
      {
         m_myLoader = new YJFYLoader();
         m_loadUI = Part1.getInstance().getLoadUI();
         m_loadUI.tranToTransparentcy();
         stage.addChild(m_loadUI);
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/collectTime.xml",getSuccess,getFail);
         m_myLoader.getClass("CollectTimePanel.swf","CollectTimePanel",getSuccess,getFail);
         m_myLoader.load();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_collectTimeReturnData == null || m_collectTimeReturnData.getIsStartCollect() == false || isNaN(m_toTime))
         {
            return;
         }
         var _loc3_:Number = m_currentCollectTime + (param1.getOnLineTimeForThisInit() - m_toTime);
         refreshReturnDataAndShow2(_loc3_);
         _loc3_ = Math.min(m_collectTimeData.getMaxTime() * 3600000,_loc3_);
         var _loc4_:int = Math.ceil(_loc3_ / 1000);
         var _loc5_:int = _loc4_ / 3600;
         _loc4_ -= _loc5_ * 3600;
         var _loc2_:int = _loc4_ / 60;
         _loc4_ -= _loc2_ * 60;
         m_hourShow.showNum(_loc5_);
         m_minuteShow.showNum(_loc2_);
         m_secondShow.showNum(_loc4_);
      }
      
      private function initShow() : void
      {
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         addChild(m_show);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn"]);
         initFrame1Show();
         if(CollectTimeSaveData.getInstance().getStartTime())
         {
            getServerTime(function(param1:String):void
            {
               refreshReturnDataAndShow(param1);
            });
         }
      }
      
      private function refreshReturnDataAndShow(param1:String) : void
      {
         m_collectTime.transformTimeToExp(CollectTimeSaveData.getInstance().getStartTime(),param1,m_collectTimeData,GamingUI.getInstance().player1.playerVO,m_collectTimeReturnData);
         if(m_collectTimeReturnData.getIsStartCollect())
         {
            m_currentCollectTime = m_collectTimeReturnData.getCollectTime();
            m_toTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
            initFrame2Show();
            initFrame2Show2();
         }
         else
         {
            initFrame1Show();
         }
      }
      
      private function refreshReturnDataAndShow2(param1:uint) : void
      {
         m_collectTime.transformTimeToExp2(param1 / 3600000,m_collectTimeData,GamingUI.getInstance().player1.playerVO,m_collectTimeReturnData);
         if(m_collectTimeReturnData.getIsStartCollect())
         {
            if(m_getExpTxt1)
            {
               m_getExpTxt1.text = m_collectTimeReturnData.getAbleTransformExp().toString();
            }
            if(m_getExpTxt2)
            {
               m_getExpTxt2.text = m_collectTimeReturnData.getAbleTransformExpByTicket().toString();
            }
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var e:ButtonEvent = param1;
         switch(e.button)
         {
            case m_transformBtn1:
               normalTransform();
               break;
            case m_transformBtn2:
               showWarningBox("是否花费" + m_collectTimeData.getTicket() + "点券购买。",3,{
                  "type":"buyShouJiTimeToExp",
                  "okFunction":buyTransformByTicket
               });
               break;
            case m_startCollectBtn:
               getServerTime(function(param1:String):void
               {
                  CollectTimeSaveData.getInstance().reset(param1);
                  refreshReturnDataAndShow(param1);
                  var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                  _loc2_.type = "4399";
                  _loc2_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc2_);
                  MyFunction2.saveGame2();
               });
               break;
            case m_quitBtn:
               m_gamingUI.closeCollectTimePanel();
         }
      }
      
      private function buyTransformByTicket() : void
      {
         var dataObj:Object = {};
         dataObj["propId"] = m_collectTimeData.getTicketId();
         dataObj["count"] = 1;
         dataObj["price"] = m_collectTimeData.getTicket();
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买练功房经验";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != m_collectTimeData.getTicketId())
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            ticketTransform();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            showWarningBox("购买成功",0);
         },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function ticketTransform() : void
      {
         getServerTime(function(param1:String):void
         {
            refreshReturnDataAndShow(param1);
            MyFunction.getInstance().addPlayerExperience(GamingUI.getInstance().player1,m_collectTimeReturnData.getAbleTransformExpByTicket());
            if(GamingUI.getInstance().player2)
            {
               MyFunction.getInstance().addPlayerExperience(GamingUI.getInstance().player2,m_collectTimeReturnData.getAbleTransformExpByTicket());
            }
            CollectTimeSaveData.getInstance().reset(null);
            refreshReturnDataAndShow(param1);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         });
      }
      
      private function normalTransform() : void
      {
         getServerTime(function(param1:String):void
         {
            var _loc2_:SaveTaskInfo = null;
            refreshReturnDataAndShow(param1);
            if(m_collectTimeReturnData.getCollectTime() && m_collectTimeReturnData.getIsLessMinTime() == false)
            {
               MyFunction.getInstance().addPlayerExperience(GamingUI.getInstance().player1,m_collectTimeReturnData.getAbleTransformExp());
               if(GamingUI.getInstance().player2)
               {
                  MyFunction.getInstance().addPlayerExperience(GamingUI.getInstance().player2,m_collectTimeReturnData.getAbleTransformExp());
               }
               CollectTimeSaveData.getInstance().reset(null);
               refreshReturnDataAndShow(param1);
               _loc2_ = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            }
         });
      }
      
      private function getServerTime(param1:Function) : void
      {
         var fun:Function = param1;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            fun(param1);
         },function():void
         {
            GamingUI.getInstance().showMessageTip("加载失败");
            m_gamingUI.closeCollectTimePanel();
         },true);
      }
      
      private function initFrame1Show() : void
      {
         clearFrameShow();
         m_showMC.gotoAndStop("1");
         m_startCollectBtn = new ButtonLogicShell2();
         m_startCollectBtn.setShow(m_show["startCollectBtn"]);
      }
      
      private function initFrame2Show() : void
      {
         clearFrameShow();
         m_showMC.gotoAndStop("2");
         m_timeShow = m_show["timeShow"];
         m_hourShow = new MultiPlaceNumLogicShell();
         m_hourShow.setShow(m_timeShow["hour"]);
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_timeShow["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_timeShow["second"]);
         m_hourShow.setIsShowZero(true);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
         m_transformBtn1 = new ButtonLogicShell2();
         m_transformBtn1.setShow(m_show["transformBtn1"]);
         m_transformBtn2 = new ButtonLogicShell2();
         m_transformBtn2.setShow(m_show["transformBtn2"]);
         m_getExpTxt1 = m_show["getExpTxt1"];
         m_getExpTxt2 = m_show["getExpTxt2"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_getExpTxt1,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_getExpTxt2,true);
      }
      
      private function initFrame2Show2() : void
      {
         if(m_collectTimeReturnData.getIsLessMinTime())
         {
            MyFunction.getInstance().changeSaturation(m_transformBtn1.getShow(),-100);
            MyFunction.getInstance().changeSaturation(m_transformBtn2.getShow(),-100);
            (m_transformBtn1.getShow() as Sprite).mouseEnabled = false;
            (m_transformBtn1.getShow() as Sprite).mouseChildren = false;
            (m_transformBtn2.getShow() as Sprite).mouseEnabled = false;
            (m_transformBtn2.getShow() as Sprite).mouseChildren = false;
         }
         else
         {
            MyFunction.getInstance().changeSaturation(m_transformBtn1.getShow(),0);
            MyFunction.getInstance().changeSaturation(m_transformBtn2.getShow(),0);
            (m_transformBtn1.getShow() as Sprite).mouseEnabled = true;
            (m_transformBtn1.getShow() as Sprite).mouseChildren = true;
            (m_transformBtn2.getShow() as Sprite).mouseEnabled = true;
            (m_transformBtn2.getShow() as Sprite).mouseChildren = true;
         }
      }
      
      private function clearFrameShow() : void
      {
         ClearUtil.clearObject(m_timeShow);
         m_timeShow = null;
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.clearObject(m_transformBtn1);
         ClearUtil.clearObject(m_transformBtn2);
         m_transformBtn1 = null;
         m_transformBtn2 = null;
         m_getExpTxt1 = null;
         m_getExpTxt2 = null;
         ClearUtil.clearObject(m_startCollectBtn);
         m_startCollectBtn = null;
      }
      
      private function getSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         var _loc3_:XML = null;
         if(param1.loadType == YJFYLoader.SWF_LOAD_TYPE)
         {
            _loc2_ = param1.resultClass;
            m_show = new _loc2_();
            if(m_collectTimeData)
            {
               initShow();
            }
         }
         else
         {
            _loc3_ = param1.resultXML;
            m_collectTimeData = new CollectTimeData(_loc3_.@coefficient,_loc3_.@ticket,_loc3_.@ticketId,_loc3_.@multiOfTicket,_loc3_.@minTime,_loc3_.@maxTime);
            System.disposeXML(_loc3_);
            if(m_show)
            {
               initShow();
            }
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
   }
}

