package UI.RefineFactory.LianDanFurnace
{
   import UI.MyFunction2;
   import UI.RefineFactory.RefineFactoryFunction;
   import UI.TimeEngine.TimeEngine;
   import UI.XMLSingle;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   public class LianDanFurnaceDrive extends EventDispatcher
   {
      public static const CHANGE_STATE:String = "changeState";
      
      public static const CHANGE_REMAIN_TIME:String = "changeRemainTime";
      
      private var _lianDanFurnaceVO:LianDanFurnaceVO;
      
      private var _timeEngine:TimeEngine;
      
      private var _listenerList:Array;
      
      public function LianDanFurnaceDrive(param1:LianDanFurnaceVO, param2:String, param3:XML, param4:XML)
      {
         super();
         _lianDanFurnaceVO = param1;
         init(param2,param3,param4);
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         destory();
         _lianDanFurnaceVO = null;
         if(_timeEngine)
         {
            _timeEngine.clear();
         }
         _timeEngine = null;
         if(_listenerList)
         {
            _loc1_ = int(_listenerList.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _listenerList[_loc2_] = null;
               _loc2_++;
            }
         }
         _listenerList = null;
      }
      
      public function get lianDanFurnaceVO() : LianDanFurnaceVO
      {
         return _lianDanFurnaceVO;
      }
      
      public function set lianDanFurnaceVO(param1:LianDanFurnaceVO) : void
      {
         _lianDanFurnaceVO = param1;
      }
      
      public function changeLianDanFurnaceVOState(param1:int) : void
      {
         if(!_lianDanFurnaceVO)
         {
            return;
         }
         _timeEngine.stop();
         _lianDanFurnaceVO.state = param1;
         startTimeEngine();
         dispatchEvent(new Event("changeState"));
      }
      
      public function init(param1:String, param2:XML, param3:XML) : void
      {
         _timeEngine = new TimeEngine();
         _listenerList = [];
         _timeEngine.addProcessFun("lianDanFurnaceProgressFun",{"fun":progressFun});
         initLianDanFurnaceData(_lianDanFurnaceVO,param1,param2,param3);
         startTimeEngine();
      }
      
      private function initLianDanFurnaceData(param1:LianDanFurnaceVO, param2:String, param3:XML, param4:XML) : void
      {
         var _loc5_:Array = null;
         if(lianDanFurnaceVO)
         {
            _loc5_ = RefineFactoryFunction.setRefineStateByTimeStr(lianDanFurnaceVO,param2,param3,param4);
            lianDanFurnaceVO.remainTime = _loc5_[0];
            if(_loc5_[1])
            {
               changeLianDanFurnaceVOState(_loc5_[1]);
            }
         }
      }
      
      private function startTimeEngine() : void
      {
         if(_lianDanFurnaceVO.state == 1)
         {
            _timeEngine.start();
         }
      }
      
      private function progressFun() : void
      {
         var lianDanFurnaceVO:LianDanFurnaceVO;
         _lianDanFurnaceVO.remainTime--;
         if(_lianDanFurnaceVO.remainTime <= 0)
         {
            _lianDanFurnaceVO.remainTime = 0;
            if(_lianDanFurnaceVO.state == 1)
            {
               lianDanFurnaceVO = _lianDanFurnaceVO;
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  initLianDanFurnaceData(lianDanFurnaceVO,param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML);
               },null,false);
            }
         }
         else
         {
            dispatchEvent(new Event("changeRemainTime"));
         }
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(_listenerList.length);
         _loc5_ = 0;
         while(_loc5_ < _listenerList.length)
         {
            if(_listenerList[_loc5_].type == param1 && _listenerList[_loc5_].listener == param2)
            {
               _listenerList.splice(_loc5_,1);
               super.removeEventListener(param1,param2,param3);
               _loc5_--;
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
   }
}

