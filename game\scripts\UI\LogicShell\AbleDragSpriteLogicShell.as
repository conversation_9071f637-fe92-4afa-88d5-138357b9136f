package UI.LogicShell
{
   import UI.MyControlPanel;
   import YJFY.ShowLogicShell.AbleDragSpriteLogicShell;
   import flash.events.MouseEvent;
   
   public class AbleDragSpriteLogicShell extends YJFY.ShowLogicShell.AbleDragSpriteLogicShell
   {
      public function AbleDragSpriteLogicShell()
      {
         super();
      }
      
      override protected function mouseDownBox(param1:MouseEvent) : void
      {
         if(m_show.parent)
         {
            if(m_show.parent is MyControlPanel)
            {
               m_show.parent.setChildIndex(m_show,m_show.parent.getChildIndex((m_show.parent as MyControlPanel).topLayer) - 1);
            }
            else
            {
               m_show.parent.setChildIndex(m_show,m_show.parent.numChildren - 1);
            }
         }
         m_show.startDrag();
         m_show.addEventListener("enterFrame",test,false,0,true);
      }
   }
}

