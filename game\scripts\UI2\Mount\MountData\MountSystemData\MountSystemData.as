package UI2.Mount.MountData.MountSystemData
{
   import UI.DataManagerParent;
   import YJFY.Lottery.LotteryData;
   import YJFY.Utils.ClearUtil;
   
   public class MountSystemData extends DataManagerParent
   {
      private var m_normalStrengthenNum:uint;
      
      private var m_superStrengthenTicket:uint;
      
      private var m_superStrengthenTicketId:String;
      
      private var m_superStrengthenTicketFor10:uint;
      
      private var m_superStrengthenTicketFor10Id:String;
      
      private var m_strengthenPro:Number;
      
      private var m_normalAddStren:uint;
      
      private var m_normalAddStren2:uint;
      
      private var m_superAddStren:uint;
      
      private var m_superAddStren2:uint;
      
      private var m_r1Num:uint;
      
      private var m_r1Ticket:uint;
      
      private var m_r1TicketId:String;
      
      private var m_r10Ticket:uint;
      
      private var m_r10TicketId:String;
      
      private var m_materialDatas:Vector.<MaterialData>;
      
      private var m_materialDatasObj:Object;
      
      private var m_randomDatas:Vector.<LotteryData>;
      
      public function MountSystemData()
      {
         super();
         m_materialDatas = new Vector.<MaterialData>();
         m_materialDatasObj = {};
         m_randomDatas = new Vector.<LotteryData>();
      }
      
      override public function clear() : void
      {
         m_superStrengthenTicketId = null;
         m_superStrengthenTicketFor10Id = null;
         m_r1TicketId = null;
         m_r10TicketId = null;
         ClearUtil.clearObject(m_materialDatas);
         m_materialDatas = null;
         ClearUtil.clearObject(m_materialDatasObj);
         m_materialDatasObj = null;
         ClearUtil.clearObject(m_randomDatas);
         m_randomDatas = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:MaterialData = null;
         var _loc5_:LotteryData = null;
         this.normalStrengthenNum = uint(param1.strengthenData[0].@normalNum);
         this.superStrengthenTicket = uint(param1.strengthenData[0].@superTicket);
         this.superStrengthenTicketId = String(param1.strengthenData[0].@superTicketId);
         this.superStrengthenTicketFor10 = uint(param1.strengthenData[0].@superTicketFor10);
         this.superStrengthenTicketFor10Id = String(param1.strengthenData[0].@superTicketFor10Id);
         this.strengthenPro = Number(param1.strengthenData[0].@pro);
         this.normalAddStren = uint(param1.strengthenData[0].@normalAddStren);
         this.normalAddStren2 = uint(param1.strengthenData[0].@normalAddStren2);
         this.superAddStren = uint(param1.strengthenData[0].@superAddStren);
         this.superAddStren2 = uint(param1.strengthenData[0].@superAddStren2);
         this.r1Num = uint(param1.randomMaterialData[0].@r1Num);
         this.r1Ticket = uint(param1.randomMaterialData[0].@r1Ticket);
         this.r1TicketId = String(param1.randomMaterialData[0].@r1TicketId);
         this.r10Ticket = uint(param1.randomMaterialData[0].@r10Ticket);
         this.r10TicketId = String(param1.randomMaterialData[0].@r10TicketId);
         ClearUtil.clearObject(m_materialDatasObj);
         ClearUtil.clearObject(m_materialDatas);
         m_materialDatas.length = 0;
         var _loc2_:XML = param1.materials[0];
         var _loc6_:XMLList = _loc2_.material;
         _loc4_ = int(_loc6_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc3_ = new MaterialData();
            _loc3_.initByXML(_loc6_[_loc8_]);
            m_materialDatas.push(_loc3_);
            m_materialDatasObj[_loc3_.getId()] = _loc3_;
            _loc8_++;
         }
         ClearUtil.clearObject(m_randomDatas);
         m_randomDatas.length = 0;
         var _loc7_:XMLList = param1.randomMaterialData[0].randomData;
         _loc4_ = int(_loc7_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc5_ = new LotteryData();
            _loc5_.initByXML(_loc7_[_loc8_]);
            m_randomDatas.push(_loc5_);
            _loc8_++;
         }
      }
      
      public function getNormalStrengthenNum() : uint
      {
         return normalStrengthenNum;
      }
      
      public function getSuperStrengthenTicket() : uint
      {
         return superStrengthenTicket;
      }
      
      public function getSuperStrengthenTicketId() : String
      {
         return superStrengthenTicketId;
      }
      
      public function getSuperStrengthenTicketFor10() : uint
      {
         return superStrengthenTicketFor10;
      }
      
      public function getSuperStrengthenTicketFor10Id() : String
      {
         return superStrengthenTicketFor10Id;
      }
      
      public function getStrengthenPro() : Number
      {
         return strengthenPro;
      }
      
      public function getNormalAddStren() : uint
      {
         return normalAddStren;
      }
      
      public function getNormalAddStren2() : uint
      {
         return normalAddStren2;
      }
      
      public function getSuperAddStren() : uint
      {
         return superAddStren;
      }
      
      public function getSuperAddStren2() : uint
      {
         return superAddStren2;
      }
      
      public function getR1Num() : uint
      {
         return r1Num;
      }
      
      public function getR1Ticket() : uint
      {
         return r1Ticket;
      }
      
      public function getR1TicketId() : String
      {
         return r1TicketId;
      }
      
      public function getR10Ticket() : uint
      {
         return r10Ticket;
      }
      
      public function getR10TicketId() : String
      {
         return r10TicketId;
      }
      
      public function getRandomDataNum() : uint
      {
         return m_randomDatas.length;
      }
      
      public function getRandomDataByIndex(param1:int) : LotteryData
      {
         return m_randomDatas[param1];
      }
      
      public function getMaterialDataById(param1:String) : MaterialData
      {
         return m_materialDatasObj[param1];
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.normalStrengthenNum = m_normalStrengthenNum;
         _antiwear.superStrengthenTicket = m_superStrengthenTicket;
         _antiwear.superStrengthenTicketId = m_superStrengthenTicketId;
         _antiwear.superStrengthenTicketFor10 = m_superStrengthenTicketFor10;
         _antiwear.superStrengthenTicketFor10Id = m_superStrengthenTicketFor10Id;
         _antiwear.strengthenPro = m_strengthenPro;
         _antiwear.normalAddStren = m_normalAddStren;
         _antiwear.normalAddStren2 = m_normalAddStren2;
         _antiwear.superAddStren = m_superAddStren;
         _antiwear.superAddStren2 = m_superAddStren2;
         _antiwear.r1Num = m_r1Num;
         _antiwear.r1Ticket = m_r1Ticket;
         _antiwear.r1TicketId = m_r1TicketId;
         _antiwear.r10Ticket = m_r10Ticket;
         _antiwear.r10TicketId = m_r10TicketId;
      }
      
      private function get normalStrengthenNum() : uint
      {
         return _antiwear.normalStrengthenNum;
      }
      
      private function set normalStrengthenNum(param1:uint) : void
      {
         _antiwear.normalStrengthenNum = param1;
      }
      
      private function get superStrengthenTicket() : uint
      {
         return _antiwear.superStrengthenTicket;
      }
      
      private function set superStrengthenTicket(param1:uint) : void
      {
         _antiwear.superStrengthenTicket = param1;
      }
      
      private function get superStrengthenTicketId() : String
      {
         return _antiwear.superStrengthenTicketId;
      }
      
      private function set superStrengthenTicketId(param1:String) : void
      {
         _antiwear.superStrengthenTicketId = param1;
      }
      
      private function get superStrengthenTicketFor10() : uint
      {
         return _antiwear.superStrengthenTicketFor10;
      }
      
      private function set superStrengthenTicketFor10(param1:uint) : void
      {
         _antiwear.superStrengthenTicketFor10 = param1;
      }
      
      private function get superStrengthenTicketFor10Id() : String
      {
         return _antiwear.superStrengthenTicketFor10Id;
      }
      
      private function set superStrengthenTicketFor10Id(param1:String) : void
      {
         _antiwear.superStrengthenTicketFor10Id = param1;
      }
      
      private function get strengthenPro() : Number
      {
         return _antiwear.strengthenPro;
      }
      
      private function set strengthenPro(param1:Number) : void
      {
         _antiwear.strengthenPro = param1;
      }
      
      private function get normalAddStren() : uint
      {
         return _antiwear.normalAddStren;
      }
      
      private function set normalAddStren(param1:uint) : void
      {
         _antiwear.normalAddStren = param1;
      }
      
      private function get normalAddStren2() : uint
      {
         return _antiwear.normalAddStren2;
      }
      
      private function set normalAddStren2(param1:uint) : void
      {
         _antiwear.normalAddStren2 = param1;
      }
      
      private function get superAddStren() : uint
      {
         return _antiwear.superAddStren;
      }
      
      private function set superAddStren(param1:uint) : void
      {
         _antiwear.superAddStren = param1;
      }
      
      private function get superAddStren2() : uint
      {
         return _antiwear.superAddStren2;
      }
      
      private function set superAddStren2(param1:uint) : void
      {
         _antiwear.superAddStren2 = param1;
      }
      
      private function get r1Num() : uint
      {
         return _antiwear.r1Num;
      }
      
      private function set r1Num(param1:uint) : void
      {
         _antiwear.r1Num = param1;
      }
      
      private function get r1Ticket() : uint
      {
         return _antiwear.r1Ticket;
      }
      
      private function set r1Ticket(param1:uint) : void
      {
         _antiwear.r1Ticket = param1;
      }
      
      private function get r1TicketId() : String
      {
         return _antiwear.r1TicketId;
      }
      
      private function set r1TicketId(param1:String) : void
      {
         _antiwear.r1TicketId = param1;
      }
      
      private function get r10Ticket() : uint
      {
         return _antiwear.r10Ticket;
      }
      
      private function set r10Ticket(param1:uint) : void
      {
         _antiwear.r10Ticket = param1;
      }
      
      private function get r10TicketId() : String
      {
         return _antiwear.r10TicketId;
      }
      
      private function set r10TicketId(param1:String) : void
      {
         _antiwear.r10TicketId = param1;
      }
   }
}

