package UI2.SmallAssistant.LevelTask
{
   import UI.MyFunction;
   import YJFY.Utils.ClearUtil;
   
   public class LevelTasksSaveData
   {
      private var m_gotRewardDatas:Vector.<LevelTaskRewardSaveData>;
      
      public function LevelTasksSaveData()
      {
         super();
         m_gotRewardDatas = new Vector.<LevelTaskRewardSaveData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_gotRewardDatas);
         m_gotRewardDatas = null;
      }
      
      public function initBySaveXML(param1:XML) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         if(param1 == null)
         {
            return;
         }
         ClearUtil.clearObject(m_gotRewardDatas);
         m_gotRewardDatas = new Vector.<LevelTaskRewardSaveData>();
         var _loc2_:Vector.<String> = MyFunction.getInstance().excreteStringToString(param1.@gRIds);
         _loc3_ = !!_loc2_ ? _loc2_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            m_gotRewardDatas.push(new LevelTaskRewardSaveData());
            m_gotRewardDatas[m_gotRewardDatas.length - 1].setRewardId(_loc2_[_loc4_]);
            _loc4_++;
         }
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc7_:int = 0;
         var _loc6_:XML = <LevelTask />;
         var _loc2_:Vector.<String> = new Vector.<String>();
         var _loc3_:int = int(m_gotRewardDatas.length);
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc2_.push(m_gotRewardDatas[_loc7_].getRewardId());
            _loc7_++;
         }
         var _loc1_:String = MyFunction.getInstance().combineStringsToArr(_loc2_);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
         if(_loc1_)
         {
            _loc6_.@gRIds = _loc1_;
         }
         return _loc6_;
      }
      
      public function getIsGotByRewardId(param1:String) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(m_gotRewardDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_gotRewardDatas[_loc3_].getRewardId() == param1)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function addGotRewardData(param1:LevelTaskRewardSaveData) : void
      {
         if(getIsGotByRewardId(param1.getRewardId()))
         {
            return;
         }
         m_gotRewardDatas.push(param1);
      }
   }
}

