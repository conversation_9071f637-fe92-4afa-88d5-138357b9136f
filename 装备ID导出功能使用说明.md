# 🎮 装备ID导出功能使用说明

## 🎯 功能介绍

我已经为你添加了一个强大的装备ID导出功能，可以把游戏中所有的装备ID和名称导出到桌面保存为txt文件。

### ✨ 新增功能：
- **功能类型26**: 导出所有装备ID到桌面
- **自动分类**: 按装备类型（武器、防具、饰品等）分类显示
- **详细信息**: 包含装备ID、名称、类型、等级、类名
- **多种保存方式**: 文件下载 + 控制台输出

## 📋 使用步骤

### 第一步：准备工作
1. 确保游戏已经完全加载
2. 确保外挂shell文件已经连接成功
3. 打开Flash Player的调试控制台（可选）

### 第二步：执行导出
```
玩家选择: P1 (或P2)
功能类型: 26
装备ID: 0 (不需要填写)
数量: 0 (不需要填写)
```

### 第三步：获取结果
导出功能会通过以下方式提供数据：

#### 方式1：文件下载（推荐）
- 系统会弹出文件保存对话框
- 选择保存位置（建议桌面）
- 文件名：`西游大战僵尸2_装备ID列表.txt`

#### 方式2：控制台输出（备用）
- 如果文件保存失败，会在调试控制台输出前100行数据
- 可以手动复制保存

#### 方式3：HTML接收器（高级）
- 打开 `装备ID导出器.html` 文件
- 在浏览器中等待数据传输
- 点击下载按钮保存文件

## 📊 导出文件格式

```
《西游大战僵尸2》装备ID完整列表
导出时间: 2024-01-01 12:00:00
总装备数量: 1500
格式: 装备ID - 装备名称 - 装备类型 - 等级
============================================================

【weapon】武器类
------------------------------
10101001 - 金箍棒 - 等级:1 - 类名:MonkeyWeapon1
10101002 - 金箍棒+1 - 等级:1 - 类名:MonkeyWeapon2
10101003 - 金箍棒+2 - 等级:1 - 类名:MonkeyWeapon3

【clothes】防具类
------------------------------
10201001 - 布衣 - 等级:1 - 类名:MonkeyClothes1
10201002 - 布衣+1 - 等级:1 - 类名:MonkeyClothes2

【necklace】饰品类
------------------------------
10301001 - 铜戒指 - 等级:1 - 类名:MonkeyNecklace1
10301002 - 铜戒指+1 - 等级:1 - 类名:MonkeyNecklace2

【potion】消耗品类
------------------------------
20001 - 小血瓶 - 等级:1 - 类名:SmallHealthPotion
20002 - 中血瓶 - 等级:1 - 类名:MediumHealthPotion

【material】材料类
------------------------------
10500001 - 针线 - 等级:1 - 类名:BasicMaterial1
10500002 - 魔鱼鳞 - 等级:1 - 类名:BasicMaterial2

============================================================
有效装备总数: 1500/1500
导出完成时间: 2024-01-01 12:00:05
```

## 🔧 故障排除

### 问题1：导出功能无响应
**可能原因**：
- 游戏未完全加载
- XMLSingle未初始化
- equipmentXML数据不可用

**解决方案**：
1. 等待游戏完全加载后再试
2. 先尝试功能25（列出前20个装备ID）测试
3. 重新启动游戏和外挂

### 问题2：文件保存失败
**可能原因**：
- Flash Player权限限制
- 浏览器安全设置

**解决方案**：
1. 查看控制台输出的备用数据
2. 使用HTML接收器
3. 手动复制控制台数据

### 问题3：数据不完整
**可能原因**：
- 游戏数据加载不完整
- XML文件损坏

**解决方案**：
1. 重新加载游戏
2. 检查游戏文件完整性
3. 对比导出的装备数量

## 🎯 使用建议

### 首次使用：
1. **先测试基础功能**: 使用功能25列出前20个装备ID
2. **确认数据正常**: 检查是否显示装备名称
3. **执行完整导出**: 使用功能26导出所有数据

### 日常使用：
1. **定期导出**: 游戏更新后重新导出
2. **备份文件**: 保存多个版本的装备ID列表
3. **分类使用**: 根据装备类型选择合适的ID

## 📈 预期效果

### 成功标志：
```
[DEBUG] 开始导出装备ID，总数量: 1500
[DEBUG] 装备ID导出完成！
[DEBUG] 文件保存位置: 桌面/西游大战僵尸2_装备ID列表.txt
[DEBUG] 有效装备数量: 1500/1500
```

### 失败标志：
```
[DEBUG] XMLSingle未初始化，无法导出装备ID
[DEBUG] equipmentXML不可用，无法导出装备ID
[DEBUG] 导出装备ID失败: Error #xxxx
```

## 🎮 实际应用

有了完整的装备ID列表后，你可以：

1. **精确添加装备**: 使用正确的装备ID
2. **避免错误**: 不再使用无效的装备ID
3. **分类管理**: 按类型选择需要的装备
4. **批量操作**: 快速找到同系列装备

这个功能解决了你之前遇到的Error #1009问题，因为现在你可以确保使用的都是游戏中真实存在的装备ID！
