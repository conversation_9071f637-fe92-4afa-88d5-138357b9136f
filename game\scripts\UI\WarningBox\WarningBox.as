package UI.WarningBox
{
   import GM_UI.GMData;
   import UI.Button.SureAndCancelBtn;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import com.greensock.TweenLite;
   import com.greensock.easing.Circ;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.display.Stage;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.clearTimeout;
   
   public class WarningBox extends MySprite
   {
      public static const NOTHING:int = 0;
      
      public static const OK:int = 1;
      
      public static const CANCEL:int = 2;
      
      private static const WARNING_BOX_WIDTH:Number = 500;
      
      private static const _START_X_1:Number = 77;
      
      private static const _START_X_2:Number = 35;
      
      private static const _Y:Number = 60;
      
      private static const _DELAY_TIME:int = 0;
      
      private static const _BTN_DISTANCE:Number = 5;
      
      public var task:Object;
      
      public var background:Sprite;
      
      private var _descriptionText:TextField;
      
      private var isClear:Boolean = false;
      
      private var _intervalId:uint;
      
      protected var _boxWidth:Number;
      
      protected var _boxHeight:Number;
      
      private var _btnWidth:Number;
      
      private var _btnHeight:Number;
      
      private var _flag:int;
      
      public function WarningBox()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(task)
         {
            for each(var _loc1_ in task)
            {
               task[_loc1_] = null;
            }
         }
         task = null;
         background = null;
         _descriptionText = null;
         isClear = true;
      }
      
      public function initBox(param1:String, param2:int) : void
      {
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         _flag = param2;
         task = {};
         _descriptionText.htmlText = param1;
         drawBox();
      }
      
      public function setTextFontSize(param1:int) : void
      {
         var _loc2_:TextFormat = _descriptionText.defaultTextFormat;
         _loc2_.size = param1;
         _descriptionText.defaultTextFormat = _loc2_;
         _descriptionText.htmlText = _descriptionText.htmlText;
      }
      
      public function getTextFontSize() : int
      {
         return !!_descriptionText ? (!!_descriptionText.defaultTextFormat.size ? int(_descriptionText.defaultTextFormat.size) : 12) : 16;
      }
      
      public function setBoxPosition(param1:Stage) : void
      {
         TweenLite.killTweensOf(this,true);
         x = (param1.stageWidth - width) / 2;
         y = (param1.stageHeight - height) / 2;
         alpha = 1;
         if(_flag == 0)
         {
            TweenLite.to(this,3,{
               "y":y + 80,
               "alpha":0,
               "ease":Circ.easeIn,
               "onComplete":removeBox,
               "delay":1,
               "onCompleteParams":[this]
            });
         }
         else
         {
            TweenLite.killTweensOf(this,true);
            alpha = 1;
         }
      }
      
      private function init(param1:String = "", param2:int = 1, param3:Number = 500, param4:Number = 0) : void
      {
         var _loc5_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         _descriptionText = new TextField();
         _boxWidth = param3;
         _boxHeight = param4;
         _descriptionText.selectable = false;
         _descriptionText.defaultTextFormat = new TextFormat(_loc5_.fontName,16,16777215);
         _descriptionText.htmlText = param1;
         _descriptionText.embedFonts = true;
         _descriptionText.x = 7;
         _descriptionText.y = 5;
         _flag = param2;
      }
      
      protected function drawBox() : void
      {
         var _loc6_:int = 0;
         var _loc2_:Number = NaN;
         var _loc7_:int = 0;
         var _loc3_:Sprite = null;
         var _loc4_:SureAndCancelBtn = new SureAndCancelBtn();
         _btnWidth = _loc4_.width;
         _btnHeight = _loc4_.height;
         _loc6_ = numChildren - 1;
         while(_loc6_ >= 0)
         {
            if(getChildAt(_loc6_) is SureAndCancelBtn)
            {
               removeChildAt(_loc6_);
            }
            _loc6_--;
         }
         var _loc1_:Array = [];
         if(_flag & 1)
         {
            _loc1_.push(createBtn("确定",1));
         }
         if(_flag & 2)
         {
            _loc1_.push(createBtn("取消",2));
         }
         var _loc5_:int = int(_loc1_.length);
         addChild(_descriptionText);
         setBoxSize(_loc5_);
         if(_loc5_ == 1)
         {
            _loc2_ = _descriptionText.x + _descriptionText.width / 2 - _btnWidth / 2;
         }
         else if(_loc5_ == 2)
         {
            _loc2_ = _descriptionText.x + _descriptionText.width / 2 - (2 * _btnWidth + 5) / 2;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc3_ = _loc1_[_loc7_] as Sprite;
            _loc3_.x = _loc2_ + _loc7_ * (_loc3_.width + 5);
            _loc3_.y = _descriptionText.y + _descriptionText.textHeight + 5;
            this.addChild(_loc1_[_loc7_]);
            _loc7_++;
         }
      }
      
      protected function setBoxSize(param1:int) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         if(!background)
         {
            background = new Sprite();
            background.graphics.beginFill(655360);
            background.graphics.drawRect(0,0,50,50);
            background.graphics.endFill();
            this.addChildAt(background,0);
         }
         _descriptionText.multiline = false;
         _descriptionText.wordWrap = false;
         if(param1 == 1)
         {
            if(_descriptionText.textWidth < _boxWidth - 15)
            {
               _descriptionText.width = Math.max(_descriptionText.textWidth,_btnWidth) + 5;
               _loc3_ = _descriptionText.width + 15;
            }
            else
            {
               _descriptionText.multiline = true;
               _descriptionText.wordWrap = true;
               _descriptionText.width = _boxWidth - 15;
               _loc3_ = _boxWidth;
            }
            if(_boxHeight == 0)
            {
               _descriptionText.height = _descriptionText.textHeight + 5;
               _loc2_ = _descriptionText.textHeight + _btnHeight + 20;
            }
            else
            {
               _descriptionText.height = _boxHeight;
               _loc2_ = _boxHeight;
            }
         }
         else if(param1 == 2)
         {
            if(_descriptionText.textWidth < _boxWidth - 15)
            {
               _descriptionText.width = Math.max(_descriptionText.textWidth,2 * _btnWidth + 5) + 5;
               _loc3_ = _descriptionText.width + 15;
            }
            else
            {
               _descriptionText.multiline = true;
               _descriptionText.wordWrap = true;
               _descriptionText.width = _boxWidth - 15;
               _loc3_ = _boxWidth;
            }
            if(_boxHeight == 0)
            {
               _descriptionText.height = _descriptionText.textHeight + 5;
               _loc2_ = _descriptionText.textHeight + _btnHeight + 20;
            }
            else
            {
               _descriptionText.height = _boxHeight;
               _loc2_ = _boxHeight;
            }
         }
         else
         {
            if(_descriptionText.textWidth < _boxWidth - 15)
            {
               _descriptionText.width = _descriptionText.textWidth + 5;
               _loc3_ = _descriptionText.textWidth + 15;
            }
            else
            {
               _descriptionText.multiline = true;
               _descriptionText.wordWrap = true;
               _descriptionText.width = _boxWidth - 15;
               _loc3_ = _boxWidth;
            }
            if(_boxHeight == 0)
            {
               _descriptionText.height = _descriptionText.textHeight + 5;
               _loc2_ = _descriptionText.textHeight + 15;
            }
            else
            {
               _descriptionText.height = _boxHeight;
               _loc2_ = _boxHeight;
            }
         }
         background.x = 0;
         background.y = 0;
         background.width = _loc3_;
         background.height = _loc2_;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("mouseDown",mouseDownBox,false,0,true);
         addEventListener("clickSureAndCancelBtn",clickBtn,true,0,true);
         addEventListener("mouseUp",mouseUpBox,false,0,true);
      }
      
      protected function hide() : void
      {
      }
      
      protected function removeBox(param1:DisplayObject) : void
      {
         if(param1.parent)
         {
            param1.parent.removeChild(param1);
         }
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("mouseDown",mouseDownBox,false);
         removeEventListener("clickSureAndCancelBtn",clickBtn,true);
         removeEventListener("mouseUp",mouseUpBox,false);
         clearTimeout(_intervalId);
         stopDrag();
      }
      
      protected function mouseDownBox(param1:MouseEvent) : void
      {
         startDrag();
      }
      
      protected function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
      }
      
      protected function createBtn(param1:String, param2:uint) : SureAndCancelBtn
      {
         var _loc3_:SureAndCancelBtn = new SureAndCancelBtn();
         _loc3_.showText(param1);
         _loc3_.detail = param2;
         return _loc3_;
      }
      
      protected function clickBtn(param1:UIBtnEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("warningBox",{
            "detail":param1.target.detail,
            "task":task
         }));
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

