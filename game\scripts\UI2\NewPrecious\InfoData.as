package UI2.NewPrecious
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class InfoData extends DataManagerParent
   {
      public var id:String;
      
      public var maxChNum:int;
      
      public var needLevel:int;
      
      public var lockDescription:String;
      
      public var showFrameLabel:String;
      
      public var levelXMLPath:String;
      
      public var description:String;
      
      public var equiplist:Vector.<int>;
      
      public var remainNum:int = 0;
      
      public var isOpen:int;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function InfoData()
      {
         super();
         equiplist = new Vector.<int>();
         m_equipmentVOs = new Vector.<EquipmentVO>();
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(equiplist);
         equiplist = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         super.clear();
      }
      
      public function initEquipList() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(equiplist.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_equipmentVOs.push(XMLSingle.getEquipmentVOByID(equiplist[_loc2_],XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc2_++;
         }
      }
      
      public function getItemByIndex(param1:int) : EquipmentVO
      {
         if(param1 >= 0 && param1 < m_equipmentVOs.length)
         {
            return m_equipmentVOs[param1];
         }
         return null;
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <one />;
         _loc1_.@id = id;
         _loc1_.@n = remainNum;
         return _loc1_;
      }
   }
}

