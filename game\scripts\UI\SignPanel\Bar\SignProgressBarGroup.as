package UI.SignPanel.Bar
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MySprite;
   import UI.UIInterface.ISegmentedBar;
   import flash.display.DisplayObject;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class SignProgressBarGroup extends MySprite
   {
      private var _continuousDayTexts:Vector.<TextField> = new Vector.<TextField>();
      
      private var _progressBars:Vector.<ISegmentedBar> = new Vector.<ISegmentedBar>();
      
      public function SignProgressBarGroup()
      {
         super();
      }
      
      public function init(param1:Number, param2:Number, param3:Number, param4:int) : void
      {
         var _loc5_:Number = NaN;
         var _loc9_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:Number = new SignProgressBar().width;
         var _loc7_:Number = (param3 - (param1 - param2 / 2) * _loc10_ * (param4 * param4 - param4) / 2 - param2 * _loc10_ * param4 * (param4 - 1) * (2 * param4 - 1) / 12) / (_loc10_ * param4);
         var _loc6_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         _loc9_ = 0;
         while(_loc9_ < param4)
         {
            _progressBars.push(new SignProgressBar());
            (_progressBars[_loc9_] as SignProgressBar).scaleX = _loc7_ + param1 * _loc9_ + (_loc9_ * _loc9_ - _loc9_) * param2 / 2;
            _loc5_ = 0;
            _loc8_ = 0;
            while(_loc8_ < _loc9_)
            {
               _loc5_ += (_progressBars[_loc8_] as SignProgressBar).width + 2;
               _loc8_++;
            }
            (_progressBars[_loc9_] as SignProgressBar).x = _loc5_;
            addChild(_progressBars[_loc9_] as DisplayObject);
            _loc9_++;
         }
         _loc9_ = 0;
         while(_loc9_ < param4)
         {
            _continuousDayTexts.push(new TextField());
            _continuousDayTexts[_loc9_].defaultTextFormat = new TextFormat(_loc6_.fontName,15,16776960);
            _continuousDayTexts[_loc9_].embedFonts = true;
            _continuousDayTexts[_loc9_].selectable = false;
            _continuousDayTexts[_loc9_].text = "";
            _continuousDayTexts[_loc9_].width = _continuousDayTexts[_loc9_].textWidth + 5;
            _continuousDayTexts[_loc9_].x = (_progressBars[_loc9_] as SignProgressBar).x + (_progressBars[_loc9_] as SignProgressBar).width - _continuousDayTexts[_loc9_].textWidth / 2;
            _continuousDayTexts[_loc9_].y = (_progressBars[_loc9_] as SignProgressBar).y - _continuousDayTexts[_loc9_].textHeight;
            addChild(_continuousDayTexts[_loc9_]);
            _loc9_++;
         }
      }
      
      public function setProgress(param1:Number) : void
      {
         MyFunction.getInstance().setSegmentedBar(param1,_progressBars);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_continuousDayTexts)
         {
            _loc1_ = int(_continuousDayTexts.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _continuousDayTexts[_loc2_] = null;
               _loc2_++;
            }
         }
         if(_progressBars)
         {
            _loc1_ = int(_progressBars.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               (_progressBars[_loc2_] as SignProgressBar).clear();
               _progressBars[_loc2_] = null;
               _loc2_++;
            }
         }
         _continuousDayTexts = null;
         _progressBars = null;
      }
   }
}

