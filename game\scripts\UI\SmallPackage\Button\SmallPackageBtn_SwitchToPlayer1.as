package UI.SmallPackage.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class SmallPackageBtn_SwitchToPlayer1 extends SwitchBtn
   {
      public function SmallPackageBtn_SwitchToPlayer1()
      {
         super();
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchPlayerInSmallPackage"));
      }
   }
}

