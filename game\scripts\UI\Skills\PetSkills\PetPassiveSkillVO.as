package UI.Skills.PetSkills
{
   import UI.Skills.SkillVO;
   
   public class PetPassiveSkillVO extends SkillVO
   {
      public var unit:String;
      
      public var passiveType:String;
      
      private var _originalValue:int;
      
      private var _promoteValue:int = 0;
      
      private var _value:int;
      
      private var _bloodPerLow:int;
      
      public function PetPassiveSkillVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.value = _value;
         _antiwear.bloodPerLow = _bloodPerLow;
         _antiwear.originalValue = _originalValue;
         _antiwear.promoteValue = _promoteValue;
      }
      
      override public function clone() : SkillVO
      {
         var _loc1_:PetPassiveSkillVO = new PetPassiveSkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribue(param1:SkillVO) : void
      {
         super.cloneAttribue(param1);
         (param1 as PetPassiveSkillVO).passiveType = this.passiveType;
         (param1 as PetPassiveSkillVO).value = this.value;
         (param1 as PetPassiveSkillVO).bloodPerLow = this._bloodPerLow;
         (param1 as PetPassiveSkillVO).originalValue = this.originalValue;
         (param1 as PetPassiveSkillVO).promoteValue = this.promoteValue;
         (param1 as PetPassiveSkillVO).unit = this.unit;
      }
      
      public function get value() : int
      {
         return _antiwear.value;
      }
      
      public function set value(param1:int) : void
      {
         _antiwear.value = param1;
      }
      
      public function get bloodPerLow() : int
      {
         return _antiwear.bloodPerLow;
      }
      
      public function set bloodPerLow(param1:int) : void
      {
         _antiwear.bloodPerLow = param1;
      }
      
      public function set originalValue(param1:int) : void
      {
         _antiwear.originalValue = param1;
      }
      
      public function get originalValue() : int
      {
         return _antiwear.originalValue;
      }
      
      public function get promoteValue() : int
      {
         return _antiwear.promoteValue;
      }
      
      public function set promoteValue(param1:int) : void
      {
         _antiwear.promoteValue = param1;
      }
   }
}

