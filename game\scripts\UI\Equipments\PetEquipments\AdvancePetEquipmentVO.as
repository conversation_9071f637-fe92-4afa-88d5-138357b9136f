package UI.Equipments.PetEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.ShiTu.PromoteValueObject;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetAwakeSkillVO;
   import UI.Skills.SkillVO;
   import YJFY.Utils.ClearUtil;
   
   public class AdvancePetEquipmentVO extends PetEquipmentVO
   {
      public var petAwakePassiveSkillVOs:Vector.<SkillVO>;
      
      protected var _isSet:Boolean;
      
      public function AdvancePetEquipmentVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(petAwakePassiveSkillVOs);
         petAwakePassiveSkillVOs = null;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:AdvancePetEquipmentVO = new AdvancePetEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc2_:* = undefined;
         super.cloneAttribute(param1);
         if(this.petAwakePassiveSkillVOs)
         {
            _loc2_ = new Vector.<SkillVO>();
            for each(var _loc4_ in this.petAwakePassiveSkillVOs)
            {
               _loc2_.push(_loc4_.clone());
            }
            (param1 as AdvancePetEquipmentVO).petAwakePassiveSkillVOs = _loc2_;
         }
      }
      
      public function setPetAwaProAttri() : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:PetAwakeSkillVO = null;
         if(essentialPercent == 0)
         {
            return;
         }
         if(_isSet)
         {
            return;
         }
         _isSet = true;
         var _loc2_:int = !!petAwakePassiveSkillVOs ? petAwakePassiveSkillVOs.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc1_ = petAwakePassiveSkillVOs[_loc5_] as PetAwakeSkillVO;
            if(_loc1_)
            {
               _loc3_ = int(_loc1_.attributes.length);
               _loc4_ = 0;
               while(_loc4_ < _loc3_)
               {
                  this[_loc1_.attributes[_loc4_]](_loc1_.attributeValues[_loc4_]);
                  _loc4_++;
               }
            }
            _loc5_++;
         }
      }
      
      public function recoverPetAwaProAttri() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:PetAwakeSkillVO = null;
         var _loc3_:PromoteValueObject = null;
         if(_isSet == false)
         {
            return;
         }
         _isSet = false;
         var _loc2_:int = !!petAwakePassiveSkillVOs ? petAwakePassiveSkillVOs.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc1_ = petAwakePassiveSkillVOs[_loc6_] as PetAwakeSkillVO;
            if(_loc1_)
            {
               _loc4_ = int(_loc1_.attributes.length);
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _loc3_ = _loc1_.attributeValues[_loc5_].getOppositeValueObject();
                  this[_loc1_.attributes[_loc5_]](_loc3_);
                  _loc3_.clear();
                  _loc5_++;
               }
            }
            _loc6_++;
         }
      }
      
      public function promotePlayerAttr(param1:PromoteValueObject) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(Boolean(_pet) && _pet.playerVO)
         {
            _loc2_ = int(param1.addAttributes.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_pet.playerVO.hasOwnProperty(param1.addAttributes[_loc3_]))
               {
                  _pet.playerVO[param1.addAttributes[_loc3_]](param1.addAttributeValueObjects[_loc3_]);
               }
               else
               {
                  _pet.playerVO.set(param1.addAttributes[_loc3_],(Number(_pet.playerVO.get(param1.addAttributes[_loc3_])) + Number(param1.addAttributeValueObjects[_loc3_].value)).toFixed(4));
               }
               _loc3_++;
            }
         }
      }
      
      public function promotePetAttr(param1:PromoteValueObject) : void
      {
         var _loc3_:int = 0;
         if(_pet == null)
         {
            return;
         }
         var _loc2_:int = int(param1.addAttributes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(this.hasOwnProperty(param1.addAttributes[_loc3_]))
            {
               this[param1.addAttributes[_loc3_]](param1.addAttributeValueObjects[_loc3_]);
            }
            else
            {
               this.set(param1.addAttributes[_loc3_],(Number(this.get(param1.addAttributes[_loc3_])) + Number(param1.addAttributeValueObjects[_loc3_].value)).toFixed(4));
            }
            _loc3_++;
         }
      }
      
      public function promoteActiveSkill(param1:PromoteValueObject) : void
      {
         var _loc3_:int = 0;
         if(activeSkillVO == null)
         {
            return;
         }
         var _loc2_:int = int(param1.addAttributes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(activeSkillVO.hasOwnProperty(param1.addAttributes[_loc3_]))
            {
               activeSkillVO[param1.addAttributes[_loc3_]](param1.addAttributeValueObjects[_loc3_]);
            }
            else
            {
               (activeSkillVO as PetActiveSkillVO).set(param1.addAttributes[_loc3_],(Number((activeSkillVO as PetActiveSkillVO).get(param1.addAttributes[_loc3_])) + Number(param1.addAttributeValueObjects[_loc3_].value)).toFixed(4));
            }
            _loc3_++;
         }
      }
      
      public function recoverAwakeSkill(param1:SkillVO, param2:int = 0) : SkillVO
      {
         if(petAwakePassiveSkillVOs == null || petAwakePassiveSkillVOs.length == 0)
         {
            return null;
         }
         recoverPetAwaProAttri();
         var _loc3_:SkillVO = petAwakePassiveSkillVOs[param2];
         petAwakePassiveSkillVOs[param2] = param1;
         setPetAwaProAttri();
         GamingUI.getInstance().refresh(25);
         return _loc3_;
      }
      
      override public function set essentialPercent(param1:Number) : void
      {
         super.essentialPercent = param1;
      }
   }
}

