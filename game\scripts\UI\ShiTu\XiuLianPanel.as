package UI.ShiTu
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import com.greensock.TweenLite;
   import com.greensock.easing.Expo;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class XiuLianPanel extends MySprite
   {
      private var m_show:Sprite;
      
      private var m_dragL:AbleDragSpriteLogicShell;
      
      private var m_currPKPointText:TextField;
      
      private var m_currMoneyText:TextField;
      
      private var m_xiuLianColumes:Vector.<XiuLianColume>;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_xiuLianOperationShow:XiuLianOperationShow;
      
      private var m_closeSubUI:CloseSubUI;
      
      private var m_player:Player;
      
      private var m_myControlPanel:MyControlPanel;
      
      public function XiuLianPanel()
      {
         super();
         m_dragL = new AbleDragSpriteLogicShell();
         m_quitBtn = new ButtonLogicShell2();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_xiuLianColumes = new Vector.<XiuLianColume>();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_dragL);
         m_dragL = null;
         m_currMoneyText = null;
         m_currPKPointText = null;
         ClearUtil.clearObject(m_xiuLianColumes);
         m_xiuLianColumes = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_xiuLianOperationShow);
         m_xiuLianOperationShow = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_player = null;
         m_myControlPanel = null;
         super.clear();
      }
      
      public function init(param1:CloseSubUI, param2:MyControlPanel) : void
      {
         m_closeSubUI = param1;
         m_myControlPanel = param2;
         m_show = MyFunction2.returnShowByClassName("XiuLianPanel") as Sprite;
         addChild(m_show);
         initShow();
         m_pageBtnGroup.initPageNumber(1,0);
         initShow2();
      }
      
      private function initShow() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:XiuLianColume = null;
         m_dragL.setShow(m_show);
         m_quitBtn.setShow(m_show["quitBtn3"]);
         m_quitBtn.setTipString("点击关闭");
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         var _loc3_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_currMoneyText = m_show["currMoneyText"];
         m_currPKPointText = m_show["currPKPointText"];
         MyFunction2.changeTextFieldFont(_loc3_.fontName,m_currMoneyText,true);
         MyFunction2.changeTextFieldFont(_loc3_.fontName,m_currPKPointText,true);
         var _loc4_:int = m_show.numChildren;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc1_ = m_show.getChildAt(_loc6_);
            if(_loc1_.name.substr(0,11) == "xiuLianCol_")
            {
               _loc5_++;
            }
            _loc6_++;
         }
         ClearUtil.clearObject(m_xiuLianColumes);
         m_xiuLianColumes.length = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc2_ = new XiuLianColume();
            _loc2_.setShow(m_show["xiuLianCol_" + (_loc6_ + 1)]);
            m_xiuLianColumes.push(_loc2_);
            _loc6_++;
         }
      }
      
      public function setPlayer(param1:Player) : void
      {
         m_player = param1;
         initShow2();
      }
      
      public function getXiuLianColumeNum() : uint
      {
         return m_xiuLianColumes.length;
      }
      
      public function getXiuLianColumeIndex(param1:int) : XiuLianColume
      {
         return m_xiuLianColumes[param1];
      }
      
      private function initShow2() : void
      {
         if(m_player == null)
         {
            return;
         }
         setPageBtn(m_pageBtnGroup.pageNum);
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_xiuLianColumes.length);
         m_currMoneyText.text = m_player.playerVO.money.toString();
         m_currPKPointText.text = m_player.getPKVO().getPKPoint().toString();
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_player == null || m_player.playerVO.xiuLianContents.length == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_player.playerVO.xiuLianContents.length % m_xiuLianColumes.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_player.playerVO.xiuLianContents.length / m_xiuLianColumes.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_player.playerVO.xiuLianContents.length / m_xiuLianColumes.length) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc2_:XiuLianContent = null;
         var _loc5_:int = param1 + m_xiuLianColumes.length;
         var _loc3_:int = !!m_player ? m_player.playerVO.xiuLianContents.length : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc3_)
         {
            _loc2_ = m_player.playerVO.xiuLianContents[_loc6_];
            m_xiuLianColumes[_loc4_].setXiuLianContent(_loc2_,m_player);
            m_xiuLianColumes[_loc4_].getShow().visible = true;
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_xiuLianColumes.length)
         {
            m_xiuLianColumes[_loc4_].getShow().visible = false;
            _loc4_++;
         }
      }
      
      public function playAddedAnimation() : void
      {
         TweenLite.from(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn
         });
      }
      
      public function playRemovedAnimation(param1:Function, param2:Array) : void
      {
         TweenLite.to(this,0.5,{
            "x":x - 20,
            "alpha":0,
            "ease":Expo.easeIn,
            "onComplete":param1,
            "onCompleteParams":param2
         });
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         switch(param1.button)
         {
            case m_pageBtnGroup:
               arrangeColume((m_pageBtnGroup.pageNum - 1) * m_xiuLianColumes.length);
               break;
            case m_quitBtn:
               m_closeSubUI.close();
         }
         _loc2_ = !!m_xiuLianColumes ? m_xiuLianColumes.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_xiuLianColumes[_loc3_].getBtn() == param1.button)
            {
               if(m_myControlPanel)
               {
                  openXiuLianOperation(m_xiuLianColumes[_loc3_].getXiuLianContent());
               }
               break;
            }
            _loc3_++;
         }
      }
      
      private function openXiuLianOperation(param1:XiuLianContent) : void
      {
         var _loc2_:CloseSubUI = null;
         if(m_xiuLianOperationShow == null)
         {
            m_xiuLianOperationShow = new XiuLianOperationShow();
            _loc2_ = new CloseSubUI();
            _loc2_.init(closeXiuLianOperation);
            m_xiuLianOperationShow.init(_loc2_);
            m_show.addChild(m_xiuLianOperationShow);
         }
         m_xiuLianOperationShow.setData(param1,m_player);
      }
      
      private function closeXiuLianOperation() : void
      {
         ClearUtil.clearObject(m_xiuLianOperationShow);
         m_xiuLianOperationShow = null;
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
   }
}

