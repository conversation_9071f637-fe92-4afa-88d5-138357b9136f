package UI.Button.PageBtn
{
   import UI.MySprite;
   import flash.text.TextField;
   
   public class PageBtnGroup_H_ShowPart extends MySprite
   {
      public var pageBtnUp:PageBtn;
      
      public var pageBtnDown:PageBtn;
      
      public var pageNumText:TextField;
      
      public var pageCountText:TextField;
      
      public function PageBtnGroup_H_ShowPart()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(pageBtnUp)
         {
            pageBtnUp.clear();
         }
         if(pageBtnDown)
         {
            pageBtnDown.clear();
         }
         pageBtnDown = null;
         pageBtnUp = null;
         pageNumText = null;
         pageCountText = null;
      }
   }
}

