package UI.TaskPanel
{
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ScrollBar;
   import UI.Task.TaskFunction;
   import UI.Task.TaskGoalVO;
   import UI.Task.TaskReward.TaskRewardVO_Equipment;
   import UI.Task.TaskReward.TaskRewardVO_Experience;
   import UI.Task.TaskReward.TaskRewardVO_LSHSHI;
   import UI.Task.TaskReward.TaskRewardVO_Money;
   import UI.Task.TaskReward.TaskRewardVO_ZHHJZH;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.TaskPanel.Bar.ITaskBar;
   import UI.TaskPanel.Button.ActivityTaskBtn_In;
   import UI.TaskPanel.Button.CompleteTaskBtn;
   import UI.TaskPanel.Button.EveryDayTaskBtn_In;
   import UI.TaskPanel.Button.GiveUpTaskBtn;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class InTaskPanel extends MySprite
   {
      public static const TASK_NUM_ONE_PAGE:int = 5;
      
      public static const TASK_START_NUM:int = 1;
      
      public static const EVERY_DAY_TASK_STATE:int = 0;
      
      public static const ACTIVITY_TASK_STATE:int = 1;
      
      public var completeTaskBtn:CompleteTaskBtn;
      
      public var giveUpTaskBtn:GiveUpTaskBtn;
      
      public var dataLayer:DataLayer_InTaskPanel;
      
      public var dataMask:Sprite;
      
      public var slider:Sprite;
      
      public var downControl:Sprite;
      
      public var upControl:Sprite;
      
      public var scroll_bg:Sprite;
      
      public var everyDayTaskBtn:EveryDayTaskBtn_In;
      
      public var activityTaskBtn:ActivityTaskBtn_In;
      
      private const TASK_COLUME_START_X:int = -12;
      
      private const TASK_COLUME_START_Y:int = 58;
      
      private var _currentSelectedTaskIndex:int;
      
      private var _myScroll:ScrollBar;
      
      private var _pageBtnGroup:PageBtnGroup;
      
      private var _currentState:int;
      
      private var _currentTaskVOs:Vector.<MTaskVO>;
      
      private var _currentTaskVO:MTaskVO;
      
      private var _popBox:PopUpBox_InTaskPanel;
      
      private var _loadTaskBarSwf:Boolean;
      
      public function InTaskPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function refreshTaskList() : void
      {
         var loadFinishLitener:LoadFinishListener1;
         if(_loadTaskBarSwf == false)
         {
            loadFinishLitener = new LoadFinishListener1(function():void
            {
               _loadTaskBarSwf = true;
               refreshTaskList1();
            },null);
            GamingUI.getInstance().loadQueue.load(["taskBar"],loadFinishLitener);
         }
         else
         {
            refreshTaskList1();
         }
      }
      
      private function refreshTaskList1() : void
      {
         if(Boolean(_popBox) && getChildByName(_popBox.name))
         {
            removeChild(_popBox);
            _popBox = null;
         }
         setPanelPage(_pageBtnGroup.pageNum);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(["taskBar"]);
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(dataLayer)
         {
            dataLayer.clear();
         }
         dataLayer = null;
         dataMask = null;
         slider = null;
         downControl = null;
         upControl = null;
         scroll_bg = null;
         _myScroll = null;
         if(_pageBtnGroup)
         {
            _pageBtnGroup.clear();
         }
         _pageBtnGroup = null;
         if(everyDayTaskBtn)
         {
            everyDayTaskBtn.clear();
         }
         everyDayTaskBtn = null;
         if(activityTaskBtn)
         {
            activityTaskBtn.clear();
         }
         activityTaskBtn = null;
         _currentTaskVOs = null;
         _currentTaskVO = null;
         if(_myScroll)
         {
            _myScroll.clear();
         }
         _myScroll = null;
         if(completeTaskBtn)
         {
            completeTaskBtn.clear();
         }
         completeTaskBtn = null;
         if(giveUpTaskBtn)
         {
            giveUpTaskBtn.clear();
         }
         giveUpTaskBtn = null;
         if(_popBox)
         {
            _popBox.clear();
         }
         _popBox = null;
      }
      
      private function setPanelPage(param1:int) : void
      {
         var _loc2_:int = 0;
         _loc2_ = int(_currentTaskVOs.length);
         if(!_loc2_)
         {
            _pageBtnGroup.initPageNumber(1,1);
            return;
         }
         if(_loc2_ % 5)
         {
            _pageBtnGroup.initPageNumber(param1,int(_loc2_ / 5) + 1);
         }
         else
         {
            _pageBtnGroup.initPageNumber(param1,_loc2_ / 5);
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickTaskColume",refreshTaskData,true,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("clickCompleteTaskBtn",completeTask,true,0,true);
         addEventListener("clickGiveUpTaskBtn",giveUpTask,true,0,true);
         addEventListener("clickTaskColume",switchTaskColume,true,0,true);
         addEventListener("switchEveryDayTask",switchToEveryDayTask,true,0,true);
         addEventListener("switchToActivityTask",switchToActivityTask,true,0,true);
         TaskFunction.getInstance().checkAutomaticPetTask();
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickTaskColume",refreshTaskData,true);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("clickAcceptTaskBtn",completeTask,true);
         removeEventListener("clickGiveUpTaskBtn",giveUpTask,true);
         removeEventListener("clickTaskColume",switchTaskColume,true);
         removeEventListener("switchEveryDayTask",switchToEveryDayTask,true);
         removeEventListener("switchToActivityTask",switchToActivityTask,true);
      }
      
      private function completeTask(param1:UIBtnEvent) : void
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:SaveTaskInfo = null;
         var _loc5_:SaveTaskInfo = null;
         try
         {
            switch(_currentTaskVOs[_currentSelectedTaskIndex].state)
            {
               case 4:
                  _currentTaskVOs[_currentSelectedTaskIndex].state = 3;
                  (_currentTaskVOs[_currentSelectedTaskIndex] as AccumulatedTaskVO).currentTaskCount++;
                  _loc2_ = int(_currentTaskVOs[_currentSelectedTaskIndex].currentTaskGoalVO_nums.length);
                  _loc4_ = 0;
                  while(_loc4_ < _loc2_)
                  {
                     _currentTaskVOs[_currentSelectedTaskIndex].currentTaskGoalVO_nums[_loc4_] = 0;
                     _loc4_++;
                  }
                  TaskFunction.getInstance().dealWithTaskGoalsAndTaskVOs(_currentTaskVOs[_currentSelectedTaskIndex]);
                  completeBtnState = false;
                  refreshTaskList();
                  _loc3_ = new SaveTaskInfo();
                  _loc3_.type = "4399";
                  _loc3_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc3_);
                  MyFunction2.saveGame();
                  break;
               case 1:
                  if(GamingUI.getInstance().player2)
                  {
                     if(!_popBox)
                     {
                        _popBox = new PopUpBox_InTaskPanel();
                        _popBox.addEventListener("click",clickPopBox,false,0,true);
                     }
                     _popBox.x = 200;
                     _popBox.y = 100;
                     addChildAt(_popBox,numChildren);
                  }
                  else if(TaskFunction.getInstance().dealWithTaskReward(_currentTaskVOs[_currentSelectedTaskIndex],GamingUI.getInstance().player1))
                  {
                     _currentTaskVOs[_currentSelectedTaskIndex].state = 2;
                     TaskFunction.getInstance().dealWithTaskGoalsAndTaskVOs(_currentTaskVOs[_currentSelectedTaskIndex]);
                     TasksManager.getInstance().wasteTaskVOs.push(_currentTaskVOs[_currentSelectedTaskIndex]);
                     _currentTaskVOs.splice(_currentSelectedTaskIndex,1);
                     completeBtnState = false;
                     refreshTaskList();
                     if(_currentState == 0)
                     {
                        GamingUI.getInstance().addMainLineTaskGoalGameEventStr("completeEveryDayTask");
                     }
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"您完成任务， 奖励领取成功！！",
                        "flag":0
                     }));
                     _loc5_ = new SaveTaskInfo();
                     _loc5_.type = "4399";
                     _loc5_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc5_);
                     MyFunction2.saveGame();
                  }
                  else
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"背包已满！！",
                        "flag":0
                     }));
                  }
                  break;
               default:
                  throw new Error();
            }
         }
         catch(error:Error)
         {
            trace(error.message);
            return;
         }
      }
      
      private function giveUpTask(param1:UIBtnEvent) : void
      {
         _currentTaskVOs.splice(_currentSelectedTaskIndex,1);
         refreshTaskList();
         TaskFunction.getInstance().reLoadAndInitExTask();
         TaskFunction.getInstance().dealWithTaskGoalsAndTaskVOs(_currentTaskVO);
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":"成功放弃任务！",
            "flag":0
         }));
      }
      
      private function clickPopBox(param1:MouseEvent) : void
      {
         var _loc3_:Boolean = false;
         var _loc2_:SaveTaskInfo = null;
         if(param1.target.parent == _popBox.btnOne || param1.target.parent == _popBox.btnTwo)
         {
            try
            {
               if(_currentTaskVOs[_currentSelectedTaskIndex].state != 1)
               {
                  return;
               }
            }
            catch(error:Error)
            {
               trace(error.message);
               return;
            }
            if(param1.target.parent == _popBox.btnOne)
            {
               _loc3_ = TaskFunction.getInstance().dealWithTaskReward(_currentTaskVOs[_currentSelectedTaskIndex],GamingUI.getInstance().player1);
            }
            else if(param1.target.parent == _popBox.btnTwo)
            {
               _loc3_ = TaskFunction.getInstance().dealWithTaskReward(_currentTaskVOs[_currentSelectedTaskIndex],GamingUI.getInstance().player2);
            }
            if(_loc3_)
            {
               _currentTaskVOs[_currentSelectedTaskIndex].state = 2;
               TaskFunction.getInstance().dealWithTaskGoalsAndTaskVOs(_currentTaskVOs[_currentSelectedTaskIndex]);
               TasksManager.getInstance().wasteTaskVOs.push(_currentTaskVOs[_currentSelectedTaskIndex]);
               _currentTaskVOs.splice(_currentSelectedTaskIndex,1);
               completeBtnState = false;
            }
            else
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"背包已满！！",
                  "flag":0
               }));
            }
            removeChild(_popBox);
            _popBox = null;
            if(_loc3_)
            {
               if(_currentState == 0)
               {
                  GamingUI.getInstance().addMainLineTaskGoalGameEventStr("completeEveryDayTask");
               }
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"您完成任务， 奖励领取成功！！",
                  "flag":0
               }));
               refreshTaskList();
               _loc2_ = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
            }
         }
      }
      
      private function switchToEveryDayTask(param1:UIBtnEvent) : void
      {
         activityTaskBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(everyDayTaskBtn),getChildIndex(activityTaskBtn));
         setChildIndex(activityTaskBtn,_loc2_);
         currentState = 0;
         setPanelPage(1);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function switchToActivityTask(param1:UIBtnEvent) : void
      {
         everyDayTaskBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(everyDayTaskBtn),getChildIndex(activityTaskBtn));
         setChildIndex(everyDayTaskBtn,_loc2_);
         currentState = 1;
         setPanelPage(1);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function set currentState(param1:int) : void
      {
         switch(param1)
         {
            case 0:
               _currentState = 0;
               _currentTaskVOs = TasksManager.getInstance().acceptedEveryDayTaskVOs;
               break;
            case 1:
               _currentState = 1;
               _currentTaskVOs = TasksManager.getInstance().acceptedActivityTaskVOs;
         }
      }
      
      private function switchTaskColume(param1:UIBtnEvent) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < numChildren)
         {
            if(getChildAt(_loc2_) is InTaskColume && getChildAt(_loc2_) != param1.target)
            {
               (getChildAt(_loc2_) as InTaskColume).gotoTwoFrame();
            }
            _loc2_++;
         }
      }
      
      private function set completeBtnState(param1:Boolean) : void
      {
         if(param1)
         {
            completeTaskBtn.mouseChildren = true;
            completeTaskBtn.mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(completeTaskBtn,0);
         }
         else
         {
            completeTaskBtn.mouseChildren = false;
            completeTaskBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(completeTaskBtn,-100);
         }
      }
      
      private function pageUp(param1:UIBtnEvent) : void
      {
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function pageDown(param1:UIBtnEvent) : void
      {
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      public function refreshTaskData(param1:* = null) : void
      {
         var _loc4_:* = undefined;
         var _loc5_:TextField = null;
         var _loc9_:MTaskVO = null;
         var _loc18_:ITaskBar = null;
         var _loc3_:FangZhengKaTongJianTi = null;
         var _loc13_:* = undefined;
         var _loc10_:int = 0;
         var _loc16_:int = 0;
         var _loc8_:TaskRewardVO_Equipment = null;
         var _loc14_:TaskRewardVO_Experience = null;
         var _loc12_:TaskRewardVO_Money = null;
         var _loc11_:TaskRewardVO_ZHHJZH = null;
         var _loc2_:TaskRewardVO_LSHSHI = null;
         var _loc7_:TaskProgressBarFactory = null;
         giveUpTaskBtn.mouseChildren = false;
         giveUpTaskBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(giveUpTaskBtn,-100);
         var _loc17_:String = "";
         var _loc19_:String = "";
         var _loc6_:Vector.<TextField> = new Vector.<TextField>();
         if(param1)
         {
            if(param1 is UIBtnEvent)
            {
               _loc9_ = param1.data;
            }
            else
            {
               _loc9_ = param1;
            }
         }
         else if(_currentTaskVOs.length)
         {
            try
            {
               _loc9_ = _currentTaskVOs[(_pageBtnGroup.pageNum - 1) * 5];
            }
            catch(error:Error)
            {
               if(_currentTaskVOs.length)
               {
                  _loc9_ = _currentTaskVOs[0];
               }
               else
               {
                  _loc9_ = null;
               }
               setPanelPage(_pageBtnGroup.pageNum - 1);
               arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
            }
         }
         if(_loc9_)
         {
            giveUpTaskBtn.mouseChildren = true;
            giveUpTaskBtn.mouseChildren = true;
            MyFunction.getInstance().changeSaturation(giveUpTaskBtn,0);
            _loc3_ = new FangZhengKaTongJianTi();
            _currentSelectedTaskIndex = _currentTaskVOs.indexOf(_loc9_);
            _currentTaskVO = _loc9_;
            if(_loc9_.state == 1 || _loc9_.state == 4)
            {
               completeBtnState = true;
            }
            else
            {
               completeBtnState = false;
            }
            _loc19_ = _loc9_.description;
            _loc13_ = XMLSingle.getTaskGoals(_loc9_.taskGoalVO_ids,XMLSingle.getInstance().taskXML);
            _loc10_ = 0;
            _loc16_ = int(_loc13_.length);
            _loc10_ = 0;
            while(_loc10_ < _loc16_)
            {
               _loc17_ += "" + (_loc10_ + 1) + "." + _loc13_[_loc10_].name + "  " + _loc9_.currentTaskGoalVO_nums[_loc10_] + "/" + _loc9_.taskGoalVO_nums[_loc10_] + "\n";
               _loc10_++;
            }
            _loc16_ = int(_loc9_.taskRewardVOs.length);
            _loc10_ = 0;
            while(_loc10_ < _loc16_)
            {
               switch(_loc9_.taskRewardVOs[_loc10_].type)
               {
                  case "equipmentReward":
                     _loc8_ = _loc9_.taskRewardVOs[_loc10_] as TaskRewardVO_Equipment;
                     _loc4_ = XMLSingle.getEquipmentVOsIDs(_loc8_.equipments_IDs,XMLSingle.getInstance().equipmentXML,_loc8_.equipments_Nums,_loc8_.isBinding);
                     break;
                  case "experienceReward":
                  case "moneyReward":
                  case "zhHJZHRward":
                  case "lSHSHRward":
                     _loc5_ = new TextField();
                     _loc5_.selectable = false;
                     _loc5_.wordWrap = true;
                     _loc5_.defaultTextFormat = new TextFormat(_loc3_.fontName,20,16777215);
                     _loc5_.embedFonts = true;
                     _loc5_.width = 475;
                     break;
                  default:
                     throw new Error("类型错误");
               }
               switch(_loc9_.taskRewardVOs[_loc10_].type)
               {
                  case "experienceReward":
                     _loc14_ = _loc9_.taskRewardVOs[_loc10_] as TaskRewardVO_Experience;
                     _loc5_.text = _loc14_.description + "×" + _loc14_.value;
                     break;
                  case "moneyReward":
                     _loc12_ = _loc9_.taskRewardVOs[_loc10_] as TaskRewardVO_Money;
                     _loc5_.text = _loc12_.description + "×" + _loc12_.value;
                     break;
                  case "zhHJZHRward":
                     _loc11_ = _loc9_.taskRewardVOs[_loc10_] as TaskRewardVO_ZHHJZH;
                     _loc5_.text = _loc11_.description + "×" + _loc11_.value;
                     break;
                  case "lSHSHRward":
                     _loc2_ = _loc9_.taskRewardVOs[_loc10_] as TaskRewardVO_LSHSHI;
                     _loc5_.text = _loc2_.description + "×" + _loc2_.value;
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               _loc5_.width = Math.min(_loc5_.textWidth + 5,475);
               _loc5_.height = _loc5_.textHeight + 5;
               _loc6_.push(_loc5_);
               _loc10_++;
            }
            if(_loc9_.type == "accumulatedTask" || _loc9_.type == "limitingTimeAccumulatedTask")
            {
               if(String(XMLSingle.getInstance().taskXML.Task.item.(@id == _loc9_.id)[0].@progressBar))
               {
                  _loc7_ = new TaskProgressBarFactory();
                  _loc18_ = _loc7_.createTaskBar(String(XMLSingle.getInstance().taskXML.Task.item.(@id == _loc9_.id)[0].@progressBar));
                  _loc18_.setInTaskPanel(this);
                  _loc7_.clear();
                  (_loc18_ as ITaskBar).change(1 - (_loc9_ as AccumulatedTaskVO).currentTaskCount / (_loc9_ as AccumulatedTaskVO).taskCount,(_loc9_ as AccumulatedTaskVO).taskCount);
               }
            }
         }
         dataLayer.refreshDataLayer(_loc19_,_loc17_,_loc4_,_loc6_,_loc18_);
         _myScroll.refresh();
      }
      
      private function arrangeTask(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc7_:* = 0;
         var _loc3_:InTaskColume = null;
         var _loc6_:DisplayObject = null;
         var _loc2_:int = param1 + 5;
         _loc7_ = 0;
         while(_loc7_ < numChildren)
         {
            _loc6_ = getChildAt(_loc7_);
            if(_loc6_ is InTaskColume)
            {
               removeChildAt(_loc7_);
               (_loc6_ as InTaskColume).clear();
               _loc7_--;
            }
            _loc7_++;
         }
         _loc4_ = int(_currentTaskVOs.length);
         _loc7_ = param1;
         while(_loc7_ < _loc2_ && _loc7_ < _loc4_)
         {
            _loc3_ = new InTaskColume();
            _loc3_.x = -12;
            _loc3_.y = 58 + _loc3_.height * (_loc7_ - param1);
            _loc3_.taskVO = _currentTaskVOs[_loc7_];
            addChild(_loc3_);
            _loc7_++;
         }
         var _loc5_:int = 0;
         _loc7_ = 0;
         while(_loc7_ < numChildren)
         {
            _loc6_ = getChildAt(_loc7_);
            if(_loc6_ is InTaskColume)
            {
               if(!_loc5_)
               {
                  (_loc6_ as InTaskColume).init(false);
               }
               else
               {
                  (_loc6_ as InTaskColume).init(true);
               }
               _loc5_++;
            }
            _loc7_++;
         }
         refreshTaskData();
      }
      
      private function init() : void
      {
         _myScroll = new ScrollBar(dataLayer,dataMask,slider,scroll_bg);
         _myScroll.direction = "L";
         _myScroll.tween = 5;
         _myScroll.elastic = false;
         _myScroll.lineAbleClick = true;
         _myScroll.mouseWheel = true;
         _myScroll.UP = upControl;
         _myScroll.DOWN = downControl;
         _myScroll.stepNumber = 15;
         _myScroll.refresh();
         currentState = 1;
         completeBtnState = false;
         _pageBtnGroup = new PageBtnGroup();
         setPanelPage(1);
         _pageBtnGroup.x = 100;
         _pageBtnGroup.y = 370;
         addChild(_pageBtnGroup);
         everyDayTaskBtn.init(true);
         activityTaskBtn.init(false);
      }
      
      public function get currentTaskVO() : MTaskVO
      {
         return _currentTaskVO;
      }
   }
}

