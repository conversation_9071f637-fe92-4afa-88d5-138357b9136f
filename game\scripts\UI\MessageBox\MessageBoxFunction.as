package UI.MessageBox
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   
   public class MessageBoxFunction
   {
      private static var _instance:MessageBoxFunction;
      
      private var _font:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
      
      public function MessageBoxFunction()
      {
         super();
         if(_instance == null)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : MessageBoxFunction
      {
         if(_instance == null)
         {
            _instance = new MessageBoxFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         _font = null;
         _instance = null;
      }
      
      public function toHTMLText(param1:String, param2:int = 12, param3:String = "") : String
      {
         var _loc4_:String = null;
         if(param3 == "" && MyFunction.getInstance().registerFont)
         {
            _loc4_ = "<font face=\'" + _font.fontName + "\' size=\'" + param2 + "\'>" + param1 + "</font>";
         }
         else
         {
            _loc4_ = "<font face=\'" + param3 + "\' size=\'" + param2 + "\'>" + param1 + "</font>";
         }
         return _loc4_;
      }
      
      public function addHTMLTextColor(param1:String, param2:String = "#ffffff") : String
      {
         if(Boolean(param2) == false)
         {
            param2 = "#ffffff";
         }
         if(param2.substr(0,2) == "0x")
         {
            param2 = "#" + param2.slice(2,param2.length - 1);
         }
         if(param2.substr(0,1) != "#")
         {
            param2 = "#" + param2;
         }
         return "<font color=\'" + param2 + "\'>" + param1 + "</font>";
      }
   }
}

