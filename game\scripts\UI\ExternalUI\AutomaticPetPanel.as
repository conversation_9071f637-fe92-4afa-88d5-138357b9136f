package UI.ExternalUI
{
   import UI.EnterFrameTime;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class AutomaticPetPanel
   {
      private var m_show:MovieClip;
      
      private var m_headShow:MovieClipPlayLogicShell;
      
      private var m_hpBar:CMSXChangeBarLogicShell;
      
      private var m_mpBar:CMSXChangeBarLogicShell;
      
      private var m_levelShow:MultiPlaceNumLogicShell;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_headShowBack:MovieClipPlayLogicShell;
      
      private var m_hpBarBack:CMSXChangeBarLogicShell;
      
      private var m_mpBarBack:CMSXChangeBarLogicShell;
      
      private var m_levelShowBack:MultiPlaceNumLogicShell;
      
      private var m_background:MovieClipPlayLogicShell;
      
      private var m_automaticPetBackVO:AutomaticPetVO;
      
      private var m_lastCurrentHp:uint;
      
      private var m_lastTotalHp:uint;
      
      private var m_lastCurrentMp:uint;
      
      private var m_lastTotalMp:uint;
      
      private var m_lastLevel:uint;
      
      private var m_lastBackCurrentHp:uint;
      
      private var m_lastBackTotalHp:uint;
      
      private var m_lastBackCurrentMp:uint;
      
      private var m_lastBackTotalMp:uint;
      
      private var m_lastBackLevel:uint;
      
      private var m_lastBackSkinId:String;
      
      public function AutomaticPetPanel()
      {
         super();
         m_headShow = new MovieClipPlayLogicShell();
         m_hpBar = new CMSXChangeBarLogicShell();
         m_mpBar = new CMSXChangeBarLogicShell();
         m_levelShow = new MultiPlaceNumLogicShell();
         m_headShowBack = new MovieClipPlayLogicShell();
         m_hpBarBack = new CMSXChangeBarLogicShell();
         m_mpBarBack = new CMSXChangeBarLogicShell();
         m_levelShowBack = new MultiPlaceNumLogicShell();
         m_background = new MovieClipPlayLogicShell();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_headShow);
         m_headShow = null;
         ClearUtil.clearObject(m_hpBar);
         m_hpBar = null;
         ClearUtil.clearObject(m_mpBar);
         m_mpBar = null;
         ClearUtil.clearObject(m_levelShow);
         m_levelShow = null;
         ClearUtil.clearObject(m_headShowBack);
         m_headShowBack = null;
         ClearUtil.clearObject(m_hpBarBack);
         m_hpBarBack = null;
         ClearUtil.clearObject(m_mpBarBack);
         m_mpBarBack = null;
         ClearUtil.clearObject(m_levelShowBack);
         m_levelShow = null;
         ClearUtil.clearObject(m_background);
         m_background = null;
         m_automaticPetVO = null;
         m_automaticPetBackVO = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function setAutomaticPetVO(param1:AutomaticPetVO, param2:AutomaticPetVO = null) : void
      {
         m_automaticPetVO = param1;
         m_lastCurrentHp = 0;
         m_lastCurrentMp = 0;
         m_lastTotalHp = 0;
         m_lastTotalMp = 0;
         m_lastLevel = 0;
         m_automaticPetBackVO = param2;
         m_lastBackCurrentHp = 0;
         m_lastBackCurrentMp = 0;
         m_lastBackTotalHp = 0;
         m_lastBackTotalMp = 0;
         m_lastBackLevel = 0;
         m_lastBackSkinId = "";
         if(m_automaticPetVO)
         {
            m_headShow.gotoAndStop(m_automaticPetVO.getId());
            m_levelShow.showNum(m_automaticPetVO.getLevel());
            m_lastLevel = m_automaticPetVO.getLevel();
         }
         if(m_automaticPetBackVO)
         {
            m_headShowBack.gotoAndStop(m_automaticPetBackVO.getId());
            m_levelShowBack.showNum(m_automaticPetBackVO.getLevel());
            m_lastBackLevel = m_automaticPetBackVO.getLevel();
            m_lastBackSkinId = m_automaticPetBackVO.getShowId();
            m_headShowBack.getShow().visible = true;
            m_hpBarBack.getShow().visible = true;
            m_mpBarBack.getShow().visible = true;
            m_levelShowBack.getShow().visible = true;
            m_background.getShow().visible = true;
         }
         else
         {
            m_headShowBack.getShow().visible = false;
            m_hpBarBack.getShow().visible = false;
            m_mpBarBack.getShow().visible = false;
            m_levelShowBack.getShow().visible = false;
            m_background.getShow().visible = false;
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_automaticPetVO)
         {
            if(m_lastCurrentHp != m_automaticPetVO.getCurrentHp() || m_lastTotalHp != m_automaticPetVO.getTotalHp())
            {
               m_lastCurrentHp = m_automaticPetVO.getCurrentHp();
               m_lastTotalHp = m_automaticPetVO.getTotalHp();
               m_hpBar.change(m_automaticPetVO.getCurrentHp() / m_automaticPetVO.getTotalHp());
               m_hpBar.setDataShow(m_automaticPetVO.getCurrentHp() + "/" + m_automaticPetVO.getTotalHp());
               if(m_automaticPetVO.getCurrentHp() || m_automaticPetVO.partnerUid)
               {
                  MyFunction.getInstance().changeSaturation(m_show,0);
               }
               else
               {
                  MyFunction.getInstance().changeSaturation(m_show,-100);
               }
            }
            if(m_lastCurrentMp != m_automaticPetVO.getCurrentMp() || m_lastTotalMp != m_automaticPetVO.getTotalMp())
            {
               m_lastCurrentMp = m_automaticPetVO.getCurrentMp();
               m_lastTotalMp = m_automaticPetVO.getTotalMp();
               m_mpBar.change(m_automaticPetVO.getCurrentMp() / m_automaticPetVO.getTotalMp());
               m_mpBar.setDataShow(m_automaticPetVO.getCurrentMp() + "/" + m_automaticPetVO.getTotalMp());
            }
            if(m_lastLevel != m_automaticPetVO.getLevel())
            {
               m_lastLevel = m_automaticPetVO.getLevel();
               m_levelShow.showNum(m_automaticPetVO.getLevel());
            }
         }
         if(m_automaticPetBackVO)
         {
            if(m_lastBackCurrentHp != m_automaticPetBackVO.getCurrentHp() || m_lastBackTotalHp != m_automaticPetBackVO.getTotalHp())
            {
               m_lastBackCurrentHp = m_automaticPetBackVO.getCurrentHp();
               m_lastBackTotalHp = m_automaticPetBackVO.getTotalHp();
               m_hpBarBack.change(m_automaticPetBackVO.getCurrentHp() / m_automaticPetBackVO.getTotalHp());
               m_hpBarBack.setDataShow(m_automaticPetBackVO.getCurrentHp() + "/" + m_automaticPetBackVO.getTotalHp());
               if(m_automaticPetBackVO.getCurrentHp() || m_automaticPetVO.getCurrentHp())
               {
                  MyFunction.getInstance().changeSaturation(m_show,0);
               }
               else
               {
                  MyFunction.getInstance().changeSaturation(m_show,-100);
               }
            }
            if(m_lastBackCurrentMp != m_automaticPetBackVO.getCurrentMp() || m_lastBackTotalMp != m_automaticPetBackVO.getTotalMp())
            {
               m_lastBackCurrentMp = m_automaticPetBackVO.getCurrentMp();
               m_lastBackTotalMp = m_automaticPetBackVO.getTotalMp();
               m_mpBarBack.change(m_automaticPetBackVO.getCurrentMp() / m_automaticPetBackVO.getTotalMp());
               m_mpBarBack.setDataShow(m_automaticPetBackVO.getCurrentMp() + "/" + m_automaticPetBackVO.getTotalMp());
            }
            if(m_lastBackLevel != m_automaticPetBackVO.getLevel())
            {
               m_lastBackLevel = m_automaticPetBackVO.getLevel();
               m_levelShowBack.showNum(m_automaticPetBackVO.getLevel());
            }
            if(m_lastBackSkinId != m_automaticPetBackVO.getShowId())
            {
               m_lastBackSkinId = m_automaticPetBackVO.getShowId();
               m_headShowBack.gotoAndStop(m_automaticPetBackVO.getShowId());
            }
         }
      }
      
      private function initShow() : void
      {
         m_headShow.setShow(m_show["petHeadShow"]);
         m_hpBar.setShow(m_show["hpBar"]);
         m_mpBar.setShow(m_show["mpBar"]);
         m_levelShow.setShow(m_show["levelShow"]);
         m_headShowBack.setShow(m_show["petHeadShowBack"]);
         m_hpBarBack.setShow(m_show["hpBarBack"]);
         m_mpBarBack.setShow(m_show["mpBarBack"]);
         m_levelShowBack.setShow(m_show["levelShowBack"]);
         m_background.setShow(m_show["background"]);
      }
   }
}

