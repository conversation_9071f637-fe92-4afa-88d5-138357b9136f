package UI
{
   import UI.LogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class ChangeFrameRatePanel extends MySprite
   {
      private var m_show:Sprite;
      
      private var m_btn:ButtonLogicShell2;
      
      private var m_text:TextField;
      
      private var m_gamingUI:GamingUI;
      
      private var m_oldFrameRate:int;
      
      public function ChangeFrameRatePanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
         m_oldFrameRate = m_gamingUI.stage.frameRate;
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(m_show);
         m_show = null;
         ClearUtil.clearObject(m_btn);
         m_btn = null;
         m_text = null;
         m_gamingUI = null;
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("ChangeFrameRatePanel") as Sprite;
         addChild(m_show);
         m_btn = new ButtonLogicShell2();
         m_btn.setShow(m_show["btn"]);
         m_text = m_show["text"];
         m_text.type = "input";
         m_text.selectable = true;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:* = param1.button;
         if(m_btn === _loc3_)
         {
            _loc2_ = Math.max(0,int(m_text.text));
            m_gamingUI.stage.frameRate = m_oldFrameRate * _loc2_;
         }
      }
   }
}

