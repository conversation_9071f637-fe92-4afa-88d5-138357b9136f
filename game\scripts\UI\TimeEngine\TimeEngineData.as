package UI.TimeEngine
{
   import flash.utils.Dictionary;
   
   public class TimeEngineData
   {
      public static const FUNCTION:String = "fun";
      
      public static const PARAMS:String = "params";
      
      public static const SPACING:String = "spacing";
      
      public var functionArr_Start:Dictionary;
      
      public var functionArr_StartArr:Array;
      
      public var functionArr_Process:Dictionary;
      
      public var functionArr_ProcessArr:Array;
      
      public var functionArr_End:Dictionary;
      
      public var functionArr_EndArr:Array;
      
      public var functionArr_Init:Dictionary;
      
      public var functionArr_InitArr:Array;
      
      public function TimeEngineData()
      {
         super();
         functionArr_Start = new Dictionary();
         functionArr_StartArr = [];
         functionArr_Process = new Dictionary();
         functionArr_ProcessArr = [];
         functionArr_End = new Dictionary();
         functionArr_EndArr = [];
         functionArr_Init = new Dictionary();
         functionArr_InitArr = [];
      }
      
      public function clear() : void
      {
         var _loc1_:* = null;
         for each(_loc1_ in functionArr_Start)
         {
            delete functionArr_Start[_loc1_];
         }
         functionArr_Start = null;
         for each(_loc1_ in functionArr_Process)
         {
            delete functionArr_Process[_loc1_];
         }
         functionArr_Process = null;
         for each(_loc1_ in functionArr_End)
         {
            delete functionArr_End[_loc1_];
         }
         functionArr_End = null;
         for each(_loc1_ in functionArr_Init)
         {
            delete functionArr_Init[_loc1_];
         }
         functionArr_Start = null;
         var _loc3_:int = 0;
         var _loc2_:int = int(functionArr_StartArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            functionArr_StartArr[_loc3_] = null;
            _loc3_++;
         }
         functionArr_StartArr = null;
         _loc2_ = int(functionArr_ProcessArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            functionArr_ProcessArr[_loc3_] = null;
            _loc3_++;
         }
         functionArr_ProcessArr = null;
         _loc2_ = int(functionArr_EndArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            functionArr_EndArr[_loc3_] = null;
            _loc3_++;
         }
         functionArr_EndArr = null;
         _loc2_ = int(functionArr_InitArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            functionArr_InitArr[_loc3_] = null;
            _loc3_++;
         }
         functionArr_InitArr = null;
      }
   }
}

