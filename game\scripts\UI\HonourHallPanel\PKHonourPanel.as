package UI.HonourHallPanel
{
   import UI.MyFunction;
   import UI.PKUI.PKFunction;
   import UI.TextTrace.traceText;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class PKHonourPanel
   {
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_honourHallXML:XML;
      
      private var m_hounourHallPanel:HonourHallPanel;
      
      private var _PKHonourColumes:Vector.<PKHonourColume>;
      
      private var m_timeStr:String;
      
      public function PKHonourPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         m_honourHallXML = null;
         m_hounourHallPanel = null;
         ClearUtil.nullArr(_PKHonourColumes);
         _PKHonourColumes = null;
         m_timeStr = "";
      }
      
      public function setHounorHallXML(param1:XML) : void
      {
         m_honourHallXML = param1;
      }
      
      public function setTimeStr(param1:String) : void
      {
         m_timeStr = param1;
      }
      
      public function setHonourHallPanel(param1:HonourHallPanel) : void
      {
         m_hounourHallPanel = param1;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         var _loc4_:int = 0;
         var _loc2_:PKHonourColume = null;
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_showMC.gotoAndStop("1");
         _PKHonourColumes = new Vector.<PKHonourColume>();
         _loc4_ = 0;
         while(_loc4_ < 10)
         {
            _loc2_ = new PKHonourColume();
            _loc2_.setShow(m_show["colume" + (_loc4_ + 1)]);
            _loc2_.setHonourHallPanel(m_hounourHallPanel);
            _PKHonourColumes.push(_loc2_);
            _loc4_++;
         }
      }
      
      public function initData(param1:int) : void
      {
         var i:int;
         var length:int;
         var myRank:int;
         var rankId:int = param1;
         PKFunction.getInstance().getOneRankListDataByUserName(rankId,GameData.getInstance().getLoginReturnData().getName(),null,function(param1:Array):void
         {
            var _loc2_:Boolean = false;
            if(!param1 || param1.length == 0)
            {
               traceText("暂无排名");
               myRank = -1;
            }
            else
            {
               length = param1.length;
               _loc2_ = false;
               i = 0;
               while(i < length)
               {
                  if(param1[i].index == GameData.getInstance().getSaveFileData().index)
                  {
                     myRank = param1[i].rank;
                     _loc2_ = true;
                     break;
                  }
                  i++;
               }
               if(!_loc2_)
               {
                  myRank = -1;
               }
            }
            initColume(m_honourHallXML.PKHonourPanel[0],myRank,m_timeStr);
            i = 0;
            while(i < length)
            {
               param1[i] = null;
               i++;
            }
            param1 = null;
         });
      }
      
      private function initColume(param1:XML, param2:int, param3:String) : void
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc9_:Object = null;
         var _loc5_:PKHonourColume = null;
         var _loc4_:* = undefined;
         var _loc8_:XMLList = param1.honour;
         var _loc6_:XML = XMLSingle.getInstance().equipmentXML;
         _loc7_ = int(_PKHonourColumes.length);
         _loc10_ = 0;
         while(_loc10_ < _loc7_)
         {
            _loc9_ = {};
            _loc9_.name = String(_loc8_[_loc10_].@name);
            _loc9_.description = String(_loc8_[_loc10_].@description);
            _loc9_["medalID"] = String(_loc8_[_loc10_].@medal);
            _loc5_ = _PKHonourColumes[_loc10_];
            _loc5_.initData(_loc9_,param3,_loc6_);
            _loc4_ = MyFunction.getInstance().excreteString(String(_loc8_[_loc10_].@rank));
            if(param2 >= _loc4_[0] && param2 <= _loc4_[1])
            {
               _loc5_.active();
            }
            _loc10_++;
         }
      }
   }
}

