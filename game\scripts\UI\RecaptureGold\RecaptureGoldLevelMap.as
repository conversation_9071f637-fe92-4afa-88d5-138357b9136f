package UI.RecaptureGold
{
   import UI.MyFunction;
   import UI.MyMovieClip;
   import UI.RecaptureGold.Parent.ICatchTarget;
   import flash.display.DisplayObject;
   
   public class RecaptureGoldLevelMap extends MyMovieClip
   {
      public function RecaptureGoldLevelMap()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function setLevelNum(param1:int, param2:XML) : void
      {
         gotoAndStop("L" + param1 + "_" + Math.ceil(MyFunction.getInstance().excreteString(param2.GameLevelData[0].LevelNum[0].@everyLevelNum)[param1 - 1] * Math.random()));
      }
      
      public function getAllCatchTarget() : Vector.<ICatchTarget>
      {
         var _loc4_:int = 0;
         var _loc3_:DisplayObject = null;
         var _loc1_:int = numChildren;
         var _loc2_:Vector.<ICatchTarget> = new Vector.<ICatchTarget>();
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = getChildAt(_loc4_);
            if(_loc3_ is ICatchTarget)
            {
               _loc2_.push(_loc3_);
            }
            _loc4_++;
         }
         return _loc2_;
      }
   }
}

