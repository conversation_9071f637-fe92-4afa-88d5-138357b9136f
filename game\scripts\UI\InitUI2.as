package UI
{
   import UI.ExchangeEquipment.ExEPlayerData;
   import UI.NicknameSystem.NicknameData;
   import UI.NicknameSystem.NicknameRankListFunction;
   import UI.NicknameSystem.NicknameSaveData;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import UI.Players.Player;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   
   public class InitUI2
   {
      public static var _instance:InitUI2 = null;
      
      public function InitUI2()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已存在了吗？！");
      }
      
      public static function getInstance() : InitUI2
      {
         if(!_instance)
         {
            _instance = new InitUI2();
         }
         return _instance;
      }
      
      public function InitMyNicknameData(param1:Player, param2:Function, param3:Array) : void
      {
         var player:Player = param1;
         var afterFun:Function = param2;
         var afterFunParams:Array = param3;
         trace("NicknameData.getInstance().myNicknameRankListId",NicknameData.getInstance().myNicknameRankListId);
         if(player.playerVO.level == 1 && NicknameData.getInstance().myNicknameRankListId == 0)
         {
            NicknameRankListFunction.getInstance().getOneRankListDataByUserNameFromAllRankLists(GameData.getInstance().getLoginReturnData().getName(),null,function(param1:Object):void
            {
               NicknameData.getInstance().myDataInNicknameRankList = param1;
               if(param1)
               {
                  NicknameData.getInstance().myNicknameRankListId = param1.rId;
               }
               afterFun.apply(null,afterFunParams);
            },null,null,function():void
            {
               afterFun.apply(null,afterFunParams);
            },null);
         }
         else
         {
            if(NicknameData.getInstance().myNicknameRankListId == 0)
            {
               return;
            }
            MyFunction2.loadXMLFunction("nickname",function(param1:XML):void
            {
               var xml:XML = param1;
               var nicknameXML:XML = xml;
               var payRankListIds:Vector.<int> = MyFunction.getInstance().excreteString(xml.RankListID[0].@payRankListID);
               var nicknameType:String = "whiteNickname";
               var length:int = int(payRankListIds.length);
               var i:int = 0;
               while(i < length)
               {
                  if(NicknameData.getInstance().myNicknameRankListId == payRankListIds[i])
                  {
                     nicknameType = "redNickname";
                     break;
                  }
                  i++;
               }
               trace("获取排行榜昵称，","排行榜id：",NicknameData.getInstance().myNicknameRankListId,"userName:",GameData.getInstance().getLoginReturnData().getName(),"存档索引：",GameData.getInstance().getSaveFileData().index);
               if(Boolean(NicknameData.getInstance().myDataInNicknameRankList) && Boolean(NicknameData.getInstance().myDataInNicknameRankList.extra))
               {
                  NicknameData.getInstance().myDataInNicknameRankList.nicknameType = nicknameType;
               }
               else
               {
                  NicknameRankListFunction.getInstance().getOneRankListDataByUserName(nicknameType,NicknameData.getInstance().myNicknameRankListId,GameData.getInstance().getLoginReturnData().getName(),GameData.getInstance().getSaveFileData().index,null,function(param1:Object):void
                  {
                     NicknameData.getInstance().myDataInNicknameRankList = param1;
                     afterFun.apply(null,afterFunParams);
                  },null,null,function():void
                  {
                     afterFun.apply(null,afterFunParams);
                  },null);
               }
            },null,false);
         }
      }
      
      public function InitNicknameCover(param1:XML) : void
      {
         var nnameCoverXML:XML;
         var rankId:int;
         var changeNum:int;
         var newNickname:String;
         var xml:XML = param1;
         if(!xml.hasOwnProperty("NNameCover"))
         {
            return;
         }
         nnameCoverXML = xml.NNameCover[0];
         rankId = int(nnameCoverXML.@rankId);
         changeNum = int(nnameCoverXML.@cN);
         newNickname = String(nnameCoverXML.@newN);
         NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,rankId,changeNum,newNickname,null,function(param1:Array):void
         {
            NicknameData.getInstance().myNicknameRankListId = rankId;
            NicknameData.getInstance().myDataInNicknameRankList = {
               "rId":rankId,
               "index":GameData.getInstance().getSaveFileData().index,
               "uId":GameData.getInstance().getLoginReturnData().getUid(),
               "userName":GameData.getInstance().getLoginReturnData().getName(),
               "score":changeNum,
               "extra":newNickname
            };
            if(GamingUI.getInstance().internalPanel)
            {
               GamingUI.getInstance().internalPanel.showWarningBox("增加昵称成功！",0);
               GamingUI.getInstance().refresh(1);
            }
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         },function(param1:String):void
         {
            if(GamingUI.getInstance().internalPanel)
            {
               GamingUI.getInstance().internalPanel.showWarningBox("增加昵称出错了！ 请您找存档客服解决该问题！",1);
            }
            var _loc3_:NicknameSaveData = new NicknameSaveData();
            _loc3_.currentRankId = rankId;
            _loc3_.currentChangeNicknameNum = changeNum;
            _loc3_.currentNickname = newNickname;
            _loc3_.errorString = param1;
            if(!NicknameData.getInstance().nicknameSaveData)
            {
               NicknameData.getInstance().nicknameSaveData = new Vector.<NicknameSaveData>();
            }
            NicknameData.getInstance().nicknameSaveData.push(_loc3_);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         },null,null,null);
      }
      
      public function initRecaptureGoldData(param1:XML, param2:String) : void
      {
         if(!param1.hasOwnProperty("RG"))
         {
            RecaptureGoldData.getInstance().rGNum = 0;
            RecaptureGoldData.getInstance().rGDate = MyFunction.getInstance().splitTimeString(param2);
            RecaptureGoldData.getInstance().buyNum = 0;
            return;
         }
         RecaptureGoldData.getInstance().rGDate = String(param1.RG[0].@rGDate);
         RecaptureGoldData.getInstance().rGNum = int(param1.RG[0].@rGNum);
         RecaptureGoldData.getInstance().buyNum = int(param1.RG[0].@buyNum);
      }
      
      public function initExEData(param1:XML, param2:String) : void
      {
         if(!param1.hasOwnProperty("ExE"))
         {
            ExEPlayerData.getInstance().haveExeNum = 0;
            ExEPlayerData.getInstance().exeNum = 0;
            return;
         }
         ExEPlayerData.getInstance().haveExeNum = int(param1.ExE[0].@haveNum);
         ExEPlayerData.getInstance().exeNum = int(param1.ExE[0].@exeNum);
      }
      
      public function initTuDiVO(param1:XML, param2:String) : void
      {
      }
      
      public function initOnLineGiftBagData(param1:XML, param2:String) : void
      {
         var _loc3_:XML = null;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < XMLSingle.getInstance().onLineGiftBagXML.levelGift.length())
         {
            if(XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc4_].@minLevel <= GamingUI.getInstance().player1.playerVO.level && XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc4_].@maxLevel >= GamingUI.getInstance().player1.playerVO.level)
            {
               _loc3_ = XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc4_].giftBag[0];
               break;
            }
            _loc4_++;
         }
         if(!param1.hasOwnProperty("OnLineGiftBag"))
         {
            OnLineGiftBagData.getInstance().setDayTimeStr("");
            OnLineGiftBagData.getInstance().getRecyleNum = 0;
         }
         else
         {
            OnLineGiftBagData.getInstance().setDayTimeStr(String(param1.OnLineGiftBag[0].@dayTime));
            OnLineGiftBagData.getInstance().getRecyleNum = uint(param1.OnLineGiftBag[0].@rN);
            if(OnLineGiftBagData.getInstance().getRecyleNum > ProgramStartData.getInstance().get_ten() + ProgramStartData.getInstance().get_two())
            {
               OnLineGiftBagData.getInstance().getRecyleNum = 0;
            }
         }
         if(MyFunction.getInstance().newDateIsNewDay(OnLineGiftBagData.getInstance().getDayTimeStr(),param2))
         {
            OnLineGiftBagData.getInstance().setDayTimeStr(param2);
            OnLineGiftBagData.getInstance().remainTime = int(_loc3_.@needTime) * 1000;
            OnLineGiftBagData.getInstance().currentGiftBagId = String(_loc3_.@id);
            OnLineGiftBagData.getInstance().getRecyleNum = 0;
         }
         else
         {
            OnLineGiftBagData.getInstance().remainTime = int(param1.OnLineGiftBag[0].@remainTime) * 1000;
            OnLineGiftBagData.getInstance().currentGiftBagId = String(param1.OnLineGiftBag[0].@id);
         }
         OnLineGiftBagData.getInstance().remainTime_vertify = OnLineGiftBagData.getInstance().remainTime;
         OnLineGiftBagData.getInstance().servertime_vertify = GamingUI.getInstance().getNewestTimeStrFromSever();
      }
   }
}

