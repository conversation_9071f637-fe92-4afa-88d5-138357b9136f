package UI2.broadcast
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BroadRewardPanel
   {
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_tippanel:TipPanel;
      
      private var m_data:BroadInfo;
      
      private var m_btnNO:ButtonLogicShell2;
      
      private var m_btnOK:ButtonLogicShell2;
      
      private var m_rewardEquipments:Vector.<Equipment>;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var m_itemlist:Vector.<EquipmentVO>;
      
      private var m_txtname:TextField;
      
      private var m_btnGetMc:MovieClip;
      
      public function BroadRewardPanel()
      {
         super();
         m_rewardEquipments = new Vector.<Equipment>();
         m_eqCells = new Vector.<Sprite>();
      }
      
      public function clear() : void
      {
         m_show.removeEventListener("clickButton",clickButton,true);
         m_btnGetMc.removeEventListener("click",callget);
         m_mc.removeEventListener("showMessageBox",showMessageBox,true);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,false);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
      }
      
      public function init(param1:MovieClip, param2:TipPanel) : void
      {
         m_show = param1;
         m_tippanel = param2;
         initParams();
      }
      
      private function initParams() : void
      {
         var _loc1_:int = 0;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_mc = m_show["rewardmb"] as MovieClip;
         m_mc.visible = false;
         m_mc.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         equipmentVOsData = new EquipmentVOsData();
         m_itemlist = new Vector.<EquipmentVO>();
         m_txtname = m_mc["txt_2"] as TextField;
         m_btnNO = new ButtonLogicShell2();
         m_btnNO.setShow(m_mc["btnno"]);
         m_btnNO.setTipString("放弃领取");
         m_btnOK = new ButtonLogicShell2();
         m_btnOK.setShow(m_mc["btnok"]);
         m_btnOK.setTipString("领取奖励");
         m_btnGetMc = m_show["btnget"] as MovieClip;
         m_btnGetMc.addEventListener("click",callget);
         _loc1_ = 0;
         while(_loc1_ < 4)
         {
            m_eqCells.push(m_mc["eqCell_" + (_loc1_ + 1)]);
            _loc1_++;
         }
      }
      
      private function callget(param1:MouseEvent) : void
      {
         m_mc.visible = true;
         refreshReward();
      }
      
      public function setData(param1:BroadInfo) : void
      {
         m_data = param1;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_btnNO:
               m_tippanel.hideVisible();
               break;
            case m_btnOK:
               getReward();
         }
      }
      
      private function getReward() : void
      {
         var _loc1_:int = 0;
         if(m_data == null)
         {
            return;
         }
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         m_itemlist = new Vector.<EquipmentVO>();
         _loc1_ = 0;
         while(_loc1_ < m_data.equipResult.length)
         {
            m_itemlist.push(XMLSingle.getEquipment(m_data.equipResult[_loc1_].id,XMLSingle.getInstance().equipmentXML));
            _loc1_++;
         }
         equipmentVOsData.setEquipmentVOs(m_itemlist);
         if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player1))
         {
            GamingUI.getInstance().showMessageTip("领取成功");
            m_tippanel.hideVisible();
         }
         else
         {
            GamingUI.getInstance().showMessageTip("背包已满");
         }
      }
      
      private function refreshReward() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Equipment = null;
         if(m_data == null)
         {
            return;
         }
         m_txtname.text = m_data.name;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments.length = 0;
         var _loc1_:int = Math.min(m_eqCells.length,m_data.equipResult.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipment(m_data.equipResult[_loc3_].id,XMLSingle.getInstance().equipmentXML));
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            m_rewardEquipments.push(_loc2_);
            m_eqCells[_loc3_].addChild(_loc2_);
            _loc3_++;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function hideBtn() : void
      {
         m_btnGetMc.mouseEnabled = false;
         m_btnGetMc.visible = false;
         m_mc.visible = false;
      }
      
      public function showBtn() : void
      {
         m_btnGetMc.mouseEnabled = true;
         m_btnGetMc.visible = true;
      }
   }
}

