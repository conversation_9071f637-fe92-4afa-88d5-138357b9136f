package UI.RecaptureGold.UI
{
   import UI.Button.QuitBtn3;
   import UI.MyFunction2;
   import UI.MySprite;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class OIPanel extends MySprite
   {
      private var quitBtn:QuitBtn3;
      
      public function OIPanel()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         quitBtn.removeEventListener("clickQuitBtn",quit,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
      }
      
      private function init() : void
      {
         var _loc1_:Sprite = MyFunction2.returnShowByClassName("OiPanel") as Sprite;
         addChild(_loc1_);
         quitBtn = new QuitBtn3();
         quitBtn.x = 385;
         quitBtn.y = 20;
         addChild(quitBtn);
         quitBtn.addEventListener("clickQuitBtn",quit,false,0,true);
      }
      
      private function quit(param1:Event) : void
      {
         clear();
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

