package UI.Task
{
   import UI.DataManagerParent;
   
   public class TaskGoalManager extends DataManagerParent
   {
      public static var _instance:TaskGoalManager = null;
      
      public var taskGoalVOs:Vector.<TaskGoalVO> = new Vector.<TaskGoalVO>();
      
      public function TaskGoalManager()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看到实例已经存在了吗！？");
      }
      
      public static function getInstance() : TaskGoalManager
      {
         if(!_instance)
         {
            _instance = new TaskGoalManager();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         var _loc2_:int = 0;
         var _loc1_:int = int(taskGoalVOs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            taskGoalVOs[_loc2_].clear();
            taskGoalVOs[_loc2_] = null;
            _loc2_++;
         }
         taskGoalVOs = null;
         _instance = null;
      }
   }
}

