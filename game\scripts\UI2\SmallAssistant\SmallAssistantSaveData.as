package UI2.SmallAssistant
{
   import UI2.SmallAssistant.ActiveTask.ActiveTasksSaveData;
   import UI2.SmallAssistant.LevelTask.LevelTasksSaveData;
   import YJFY.Utils.ClearUtil;
   
   public class SmallAssistantSaveData
   {
      private var m_activeTasksSaveData:ActiveTasksSaveData;
      
      private var m_levelTaskSaveData:LevelTasksSaveData;
      
      public function SmallAssistantSaveData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_activeTasksSaveData);
         m_activeTasksSaveData = null;
         ClearUtil.clearObject(m_levelTaskSaveData);
         m_levelTaskSaveData = null;
      }
      
      public function initBySaveXML(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : void
      {
         var _loc7_:XML = null;
         if(param1.hasOwnProperty("SmallAssistant"))
         {
            _loc7_ = param1.SmallAssistant[0];
         }
         var _loc6_:XML = Boolean(_loc7_) && Boolean(_loc7_.hasOwnProperty("ActiveTask")) ? _loc7_.ActiveTask[0] : null;
         var _loc8_:XML = Boolean(_loc7_) && Boolean(_loc7_.hasOwnProperty("LevelTask")) ? _loc7_.LevelTask[0] : null;
         ClearUtil.clearObject(m_activeTasksSaveData);
         m_activeTasksSaveData = new ActiveTasksSaveData();
         m_activeTasksSaveData.initBySaveXML(_loc6_,param2,param3,param4);
         m_activeTasksSaveData.resetDataIfNewDay(param5);
         ClearUtil.clearObject(m_levelTaskSaveData);
         m_levelTaskSaveData = new LevelTasksSaveData();
         m_levelTaskSaveData.initBySaveXML(_loc8_);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <SmallAssistant />;
         _loc1_.appendChild(m_activeTasksSaveData.exportToSaveXML());
         _loc1_.appendChild(m_levelTaskSaveData.exportToSaveXML());
         return _loc1_;
      }
      
      public function getActiveTasksSaveData() : ActiveTasksSaveData
      {
         return m_activeTasksSaveData;
      }
      
      public function getLevelTasksSaveData() : LevelTasksSaveData
      {
         return m_levelTaskSaveData;
      }
   }
}

