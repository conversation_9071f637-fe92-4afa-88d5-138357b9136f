package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   import UI.Players.Player;
   import UI.Players.PlayerListener;
   import UI2.Mount.MountData.MountSkillVO.MountPassiveSkillVO1;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountVOListener;
   import YJFY.Utils.ClearUtil;
   
   public class MountPassSkillVO_ProPlayer extends MountPassiveSkillVO1
   {
      private var m_mountVOChangeDataListener:MountVOListener;
      
      protected var m_targetPlayer:Player;
      
      private var m_playerVOChangeDataListener:PlayerListener;
      
      protected var _addValue:Number;
      
      private var m_mountVOLevel1:uint;
      
      private var m_playerLevel:uint;
      
      public function MountPassSkillVO_ProPlayer()
      {
         super();
         m_mountVOChangeDataListener = new MountVOListener();
         m_mountVOChangeDataListener.changeDataFun = mountVOchangeData;
         m_playerVOChangeDataListener = new PlayerListener();
         m_playerVOChangeDataListener.changeDataFun = playerChangeData;
         m_addValue = 0;
      }
      
      override public function clear() : void
      {
         recoverPlayerData();
         if(m_mountVO)
         {
            m_mountVO.removeMountVOListener(m_mountVOChangeDataListener);
         }
         ClearUtil.clearObject(m_mountVOChangeDataListener);
         m_mountVOChangeDataListener = null;
         m_targetPlayer = null;
         ClearUtil.clearObject(m_playerVOChangeDataListener);
         m_playerVOChangeDataListener = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.m_addValue = _addValue;
      }
      
      public function get m_addValue() : Number
      {
         return _antiwear.m_addValue;
      }
      
      public function set m_addValue(param1:Number) : void
      {
         _antiwear.m_addValue = param1;
      }
      
      override public function setMountVO(param1:MountVO) : void
      {
         recoverPlayerData();
         if(m_mountVO)
         {
            m_mountVO.removeMountVOListener(m_mountVOChangeDataListener);
         }
         super.setMountVO(param1);
         m_targetPlayer = null;
         if(m_mountVO)
         {
            m_mountVO.addMountVOListener(m_mountVOChangeDataListener);
            if(m_mountVO.getPlayerVO())
            {
               m_targetPlayer = m_mountVO.getPlayerVO().player;
            }
         }
         changeSkillLevel();
         changePlayerData();
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         recoverPlayerData();
         super.initSkillLevelData(param1);
         changePlayerData();
      }
      
      private function mountVOchangeData(param1:MountVO) : void
      {
         if(m_mountVO == param1)
         {
            changeSkillLevel();
            if(m_targetPlayer == null || m_targetPlayer.playerVO != m_mountVO.getPlayerVO())
            {
               recoverPlayerData();
               m_targetPlayer = !!m_mountVO.getPlayerVO() ? m_mountVO.getPlayerVO().player : null;
               changePlayerData();
            }
         }
      }
      
      private function playerChangeData(param1:Player) : void
      {
         if(m_targetPlayer == param1)
         {
            if(m_playerLevel != m_targetPlayer.playerVO.level)
            {
               m_playerLevel = m_targetPlayer.playerVO.level;
               recoverPlayerData();
               changePlayerData();
            }
         }
      }
      
      private function changeSkillLevel() : void
      {
         if(m_mountVO == null)
         {
            return;
         }
         if(m_mountVOLevel1 != m_mountVO.getLevel1())
         {
            m_mountVOLevel1 = m_mountVO.getLevel1();
            changeLevel(m_mountVO.getLevel1());
         }
      }
      
      protected function recoverPlayerData() : void
      {
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.removePlayerListener(m_playerVOChangeDataListener);
         m_playerLevel = 0;
         m_mountVOLevel1 = 0;
      }
      
      protected function changePlayerData() : void
      {
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.addPlayerListener(m_playerVOChangeDataListener);
         m_playerLevel = m_targetPlayer.playerVO.level;
         m_mountVOLevel1 = m_mountVO.getLevel1();
      }
   }
}

