package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_CancelApplyJoinReturn extends InformationBodyDetail
   {
      protected var m_isSuccess:int;
      
      protected var m_societyId:int;
      
      public function DOWN_CancelApplyJoinReturn()
      {
         super();
         m_informationBodyId = 3018;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_isSuccess = param1.readInt();
         m_societyId = param1.readInt();
      }
      
      public function getIsSuccess() : int
      {
         return m_isSuccess;
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
   }
}

