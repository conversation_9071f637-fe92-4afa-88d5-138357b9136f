<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西游大战僵尸2 - 装备ID导出器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.waiting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.receiving {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.complete {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .data-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 西游大战僵尸2 - 装备ID导出器</h1>
        
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>确保游戏已经完全加载</li>
                <li>在外挂中选择功能类型 <strong>26</strong>（导出装备ID）</li>
                <li>点击执行，等待数据传输完成</li>
                <li>数据接收完成后，点击"下载文件"保存到桌面</li>
            </ol>
        </div>

        <div id="status" class="status waiting">
            ⏳ 等待游戏数据传输...
        </div>

        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>

        <div id="progressText" style="text-align: center; margin: 10px 0;">
            准备接收数据...
        </div>

        <div class="buttons">
            <button id="downloadBtn" onclick="downloadFile()" disabled>📥 下载装备ID列表</button>
            <button onclick="clearData()">🗑️ 清空数据</button>
            <button onclick="location.reload()">🔄 重新开始</button>
        </div>

        <div class="data-preview">
            <div id="dataPreview">等待数据传输...</div>
        </div>
    </div>

    <script>
        let receivedData = '';
        let totalChunks = 0;
        let receivedChunks = 0;
        let dataChunks = {};

        // 更新状态显示
        function updateStatus(message, type = 'waiting') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        // 更新进度条
        function updateProgress(current, total) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            if (total > 0) {
                const percentage = Math.round((current / total) * 100);
                progressBar.style.width = percentage + '%';
                progressText.textContent = `接收进度: ${current}/${total} (${percentage}%)`;
            }
        }

        // 更新数据预览
        function updatePreview(data) {
            const preview = document.getElementById('dataPreview');
            const lines = data.split('\n');
            const previewLines = lines.slice(0, 50); // 只显示前50行
            
            let previewText = previewLines.join('\n');
            if (lines.length > 50) {
                previewText += '\n\n... (还有 ' + (lines.length - 50) + ' 行数据)';
            }
            
            preview.textContent = previewText;
        }

        // 下载文件
        function downloadFile() {
            if (!receivedData) {
                alert('没有可下载的数据！');
                return;
            }

            const blob = new Blob([receivedData], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '西游大战僵尸2_装备ID列表_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            updateStatus('✅ 文件下载完成！', 'complete');
        }

        // 清空数据
        function clearData() {
            receivedData = '';
            totalChunks = 0;
            receivedChunks = 0;
            dataChunks = {};
            
            updateStatus('⏳ 等待游戏数据传输...', 'waiting');
            updateProgress(0, 0);
            document.getElementById('dataPreview').textContent = '等待数据传输...';
            document.getElementById('downloadBtn').disabled = true;
        }

        // 模拟接收数据的函数（用于测试）
        function simulateDataReceive() {
            const testData = `《西游大战僵尸2》装备ID完整列表
导出时间: ${new Date().toString()}
总装备数量: 1000
格式: 装备ID - 装备名称 - 装备类型 - 等级
============================================================

【weapon】
------------------------------
10101001 - 金箍棒 - 等级:1 - 类名:MonkeyWeapon1
10101002 - 金箍棒+1 - 等级:1 - 类名:MonkeyWeapon2
10101003 - 金箍棒+2 - 等级:1 - 类名:MonkeyWeapon3

【clothes】
------------------------------
10201001 - 布衣 - 等级:1 - 类名:MonkeyClothes1
10201002 - 布衣+1 - 等级:1 - 类名:MonkeyClothes2

【necklace】
------------------------------
10301001 - 铜戒指 - 等级:1 - 类名:MonkeyNecklace1
10301002 - 铜戒指+1 - 等级:1 - 类名:MonkeyNecklace2

============================================================
有效装备总数: 1000/1000
导出完成时间: ${new Date().toString()}`;

            receivedData = testData;
            updateStatus('✅ 数据接收完成！', 'complete');
            updateProgress(1, 1);
            updatePreview(testData);
            document.getElementById('downloadBtn').disabled = false;
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus('⏳ 等待游戏数据传输...', 'waiting');
            
            // 添加测试按钮（开发用）
            const testBtn = document.createElement('button');
            testBtn.textContent = '🧪 测试数据接收';
            testBtn.onclick = simulateDataReceive;
            testBtn.style.backgroundColor = '#ff9800';
            document.querySelector('.buttons').appendChild(testBtn);
        };
    </script>
</body>
</html>
