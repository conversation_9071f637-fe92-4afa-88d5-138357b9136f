package UI2.SmallAssistant
{
   import UI.GamingUI;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI2.XydzjsSkipLogic.XydzjsSkipLogic;
   import UI2.XydzjsSkipLogic.XydzjsSkipLogicListener;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class TipShowPart
   {
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_switchBtns:Vector.<SwitchBtnLogicShell>;
      
      private var m_tipLines:Vector.<TipLine>;
      
      private var m_tipDatass:Vector.<Vector.<TipData>>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_xydzjsSkipLogic:XydzjsSkipLogic;
      
      private var m_xydzjsSkipLogicListener:XydzjsSkipLogicListener;
      
      private var m_currentSwitchBtn:SwitchBtnLogicShell;
      
      private var m_currentIndexOfSwitchBtn:int;
      
      private var m_currentTipDatas:Vector.<TipData>;
      
      private var m_show:MovieClip;
      
      private var m_smallAssistantData:SmallAssistantData;
      
      private var m_smallAssistantPanel:SmallAssistantPanel;
      
      public function TipShowPart()
      {
         super();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtns = new Vector.<SwitchBtnLogicShell>();
         m_tipLines = new Vector.<TipLine>();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_xydzjsSkipLogic = new XydzjsSkipLogic();
         m_xydzjsSkipLogicListener = new XydzjsSkipLogicListener();
         m_xydzjsSkipLogicListener.skipSuccessFun = skipSuccess;
         m_xydzjsSkipLogicListener.skipFailFun = skipFail;
         m_xydzjsSkipLogic.addSkipListener(m_xydzjsSkipLogicListener);
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_switchBtns);
         m_switchBtns = null;
         ClearUtil.clearObject(m_tipLines);
         m_tipLines = null;
         ClearUtil.clearObject(m_tipDatass);
         m_tipDatass = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_xydzjsSkipLogic);
         m_xydzjsSkipLogic = null;
         ClearUtil.clearObject(m_xydzjsSkipLogicListener);
         m_xydzjsSkipLogicListener = null;
         m_currentSwitchBtn = null;
         m_currentTipDatas = null;
         m_show = null;
         m_smallAssistantData = null;
         m_smallAssistantPanel = null;
      }
      
      public function init(param1:MovieClip, param2:SmallAssistantData, param3:SmallAssistantPanel) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_smallAssistantData = param2;
         m_smallAssistantPanel = param3;
         initShow();
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:* = param1.button;
         if(m_pageBtnGroup === _loc4_)
         {
            arrangeTipLineShow((m_pageBtnGroup.pageNum - 1) * m_tipLines.length);
         }
         _loc2_ = !!m_switchBtns ? m_switchBtns.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_switchBtns[_loc3_])
            {
               clickSwitchButton(m_switchBtns[_loc3_]);
               return;
            }
            _loc3_++;
         }
         _loc2_ = !!m_tipLines ? m_tipLines.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_tipLines[_loc3_].getGotoBtn())
            {
               m_xydzjsSkipLogic.skip(m_tipLines[_loc3_].getTipData().getSkipStr());
               return;
            }
            _loc3_++;
         }
      }
      
      private function initShow() : void
      {
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:SwitchBtnLogicShell = null;
         var _loc6_:TipLine = null;
         _loc3_ = m_show.numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc1_ = m_show.getChildAt(_loc7_);
            if(_loc1_.name.substr(0,13) == "tipSwitchBtn_")
            {
               _loc5_++;
            }
            else if(_loc1_.name.substr(0,8) == "tipLine_")
            {
               _loc4_++;
            }
            _loc7_++;
         }
         ClearUtil.clearObject(m_switchBtns);
         m_switchBtns = new Vector.<SwitchBtnLogicShell>();
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc2_ = new MySwitchBtnLogicShell();
            _loc2_.setShow(m_show["tipSwitchBtn_" + (_loc7_ + 1)]);
            m_switchBtnGroup.addSwitchBtn(_loc2_);
            m_switchBtns.push(_loc2_);
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc6_ = new TipLine();
            _loc6_.setShow(m_show["tipLine_" + (_loc7_ + 1)]);
            m_tipLines.push(_loc6_);
            _loc7_++;
         }
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_smallAssistantData == null)
         {
            return;
         }
         initTipDatass();
         m_switchBtns[0].turnActiveAndDispatchEvent();
      }
      
      private function clickSwitchButton(param1:SwitchBtnLogicShell) : void
      {
         m_currentSwitchBtn = param1;
         m_currentIndexOfSwitchBtn = m_switchBtns.indexOf(param1);
         if(m_currentIndexOfSwitchBtn == -1)
         {
            throw new Error("出错了");
         }
         m_currentTipDatas = m_tipDatass[m_currentIndexOfSwitchBtn];
         setPageBtn(1);
         arrangeTipLineShow((m_pageBtnGroup.pageNum - 1) * m_tipLines.length);
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_currentTipDatas == null || m_currentTipDatas.length == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_currentTipDatas.length % m_tipLines.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_currentTipDatas.length / m_tipLines.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_currentTipDatas.length / m_tipLines.length) + 1);
         }
      }
      
      private function arrangeTipLineShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc3_:TipData = null;
         var _loc5_:int = param1 + m_tipLines.length;
         var _loc2_:int = !!m_currentTipDatas ? m_currentTipDatas.length : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc2_)
         {
            _loc3_ = m_currentTipDatas[_loc6_];
            m_tipLines[_loc4_].setTipData(_loc3_);
            m_tipLines[_loc4_].getShow().visible = true;
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_tipLines.length)
         {
            m_tipLines[_loc4_].getShow().visible = false;
            _loc4_++;
         }
      }
      
      private function initTipDatass() : void
      {
         var _loc1_:XMLList = null;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:TipData = null;
         ClearUtil.clearObject(m_tipDatass);
         m_tipDatass = null;
         m_tipDatass = new Vector.<Vector.<TipData>>();
         var _loc2_:XMLList = m_smallAssistantData.getSmallAssistantXML().tips[0].tips;
         _loc3_ = int(_loc2_.length());
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc1_ = _loc2_[_loc7_].tip;
            _loc5_ = int(_loc1_.length());
            m_tipDatass.push(new Vector.<TipData>());
            _loc6_ = 0;
            while(_loc6_ < _loc5_)
            {
               _loc4_ = new TipData(_loc1_[_loc6_].@text1,_loc1_[_loc6_].@skipStr);
               m_tipDatass[_loc7_].push(_loc4_);
               _loc6_++;
            }
            _loc7_++;
         }
      }
      
      private function skipSuccess(param1:XydzjsSkipLogic, param2:String) : void
      {
      }
      
      private function skipFail(param1:XydzjsSkipLogic, param2:String) : void
      {
         GamingUI.getInstance().showMessageTip(param2);
      }
   }
}

