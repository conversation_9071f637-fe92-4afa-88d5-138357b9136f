package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_IsSuccessJoin extends InformationBodyDetail
   {
      protected var m_societyId:int;
      
      protected var m_societyNameLength:int;
      
      protected var m_societyName:String;
      
      protected var m_isAgree:int;
      
      public function DOWN_IsSuccessJoin()
      {
         super();
         m_informationBodyId = 3016;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_societyId = param1.readInt();
         m_societyNameLength = param1.readInt();
         if(m_societyNameLength)
         {
            m_societyName = param1.readUTFBytes(m_societyNameLength);
         }
         m_isAgree = param1.readInt();
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getSocietyNameLength() : int
      {
         return m_societyNameLength;
      }
      
      public function getSocietyName() : String
      {
         return m_societyName;
      }
      
      public function getIsAgree() : Boolean
      {
         return Boolean(m_isAgree);
      }
   }
}

