package UI.Farm.Land
{
   import UI.Farm.FarmFunction;
   import UI.MyFunction2;
   import UI.TimeEngine.TimeEngine;
   import UI.XMLSingle;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   public class LandDrive extends EventDispatcher
   {
      public static const CHANGE_STATE:String = "changeState";
      
      private var _landVO:LandVO;
      
      private var _landData:LandData;
      
      private var _timeEngine:TimeEngine;
      
      private var _listenerList:Array;
      
      public function LandDrive(param1:LandVO, param2:String, param3:XML, param4:XML)
      {
         super();
         _landVO = param1;
         init(param2,param3,param4);
      }
      
      public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         destory();
         _landVO = null;
         _landData = null;
         if(_timeEngine)
         {
            _timeEngine.clear();
         }
         _timeEngine = null;
         if(_listenerList)
         {
            _loc2_ = int(_listenerList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_listenerList[_loc3_])
               {
                  for(var _loc1_ in _listenerList[_loc3_])
                  {
                     _listenerList[_loc3_][_loc1_] = null;
                  }
               }
               _listenerList[_loc3_] = null;
               _loc3_++;
            }
         }
         _listenerList = null;
      }
      
      public function get landVO() : LandVO
      {
         return _landVO;
      }
      
      public function set landVO(param1:LandVO) : void
      {
         _landVO = param1;
         dispatchEvent(new Event("changeState"));
      }
      
      public function get landData() : LandData
      {
         return _landData;
      }
      
      public function set landData(param1:LandData) : void
      {
         _landData = param1;
      }
      
      public function set changeLandVOState(param1:int) : void
      {
         if(!_landVO)
         {
            return;
         }
         _timeEngine.stop();
         _landVO.state = param1;
         startTimeEngine();
         dispatchEvent(new Event("changeState"));
      }
      
      public function set changeLandDataPlantState(param1:String) : void
      {
         if(!_landData)
         {
            return;
         }
         _landData.plantState = param1;
         dispatchEvent(new Event("changeState"));
      }
      
      private function init(param1:String, param2:XML, param3:XML) : void
      {
         _timeEngine = new TimeEngine();
         _listenerList = [];
         _timeEngine.addProcessFun("landProgressFun",{"fun":landProgressFun});
         _landData = new LandData();
         initLandData(_landVO,param1,param2,param3);
         startTimeEngine();
      }
      
      private function initLandData(param1:LandVO, param2:String, param3:XML, param4:XML) : void
      {
         if(!param1.id)
         {
            return;
         }
         var _loc5_:Object = FarmFunction.getInstance().countLandStateRemainTime(param1,param2,param3,param4);
         switch(param1.state - 1)
         {
            case 0:
            case 1:
            case 2:
               if(_landData)
               {
                  _landData.remainTime = _loc5_.remainTime;
                  changeLandDataPlantState = _loc5_.plantState;
               }
               break;
            default:
               switch(param1.state - 4)
               {
                  case 0:
                     if(_landData)
                     {
                        _landData.vipAccLandRecoverTime = _loc5_.vipAccLandRecoverTime;
                        break;
                     }
               }
               if(_landData)
               {
                  _landData.remainTime = _loc5_.remainTime;
                  break;
               }
         }
         if(_loc5_.changeLandState)
         {
            changeLandVOState = _loc5_.changeLandState;
         }
      }
      
      private function startTimeEngine() : void
      {
         if(!landVO.id)
         {
            return;
         }
         if(_landVO.state == 1 || _landVO.state == 4)
         {
            _timeEngine.start();
         }
      }
      
      private function landProgressFun() : void
      {
         var landVO:LandVO;
         _landData.remainTime--;
         if(_landData.remainTime <= 0)
         {
            _landData.remainTime = 0;
            if(_landVO.state == 1 || _landVO.state == 4)
            {
               landVO = _landVO;
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  if(!Boolean(param1))
                  {
                     return;
                  }
                  initLandData(landVO,param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML);
               },null,false);
            }
         }
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(_listenerList.length);
         _loc5_ = 0;
         while(_loc5_ < _listenerList.length)
         {
            if(_listenerList[_loc5_].type == param1 && _listenerList[_loc5_].listener == param2)
            {
               _listenerList.splice(_loc5_,1);
               super.removeEventListener(param1,param2,param3);
               _loc5_--;
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
   }
}

