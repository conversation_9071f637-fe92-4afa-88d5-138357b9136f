package UI2.SmallAssistant.ActiveTask
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.IEquipmentVOsData;
   import YJFY.Utils.ClearUtil;
   
   public class ActiveTaskRewardData extends DataManagerParent implements IEquipmentVOsData
   {
      private var m_id:String;
      
      private var m_needMinActiveValue:uint;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function ActiveTaskRewardData()
      {
         super();
         m_equipmentVOs = new Vector.<EquipmentVO>();
      }
      
      override public function clear() : void
      {
         m_id = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.id = String(param1.@id);
         this.needMinActiveValue = uint(param1.@needMinActiveValue);
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,false);
      }
      
      public function getId() : String
      {
         return id;
      }
      
      public function getNeedMinActiveValue() : uint
      {
         return needMinActiveValue;
      }
      
      public function getEquipmentVONum() : uint
      {
         return m_equipmentVOs.length;
      }
      
      public function getEquipmentVOByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.id = m_id;
         _antiwear.needMinActiveValue = m_needMinActiveValue;
      }
      
      private function get id() : String
      {
         return _antiwear.id;
      }
      
      private function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      private function get needMinActiveValue() : uint
      {
         return _antiwear.needMinActiveValue;
      }
      
      private function set needMinActiveValue(param1:uint) : void
      {
         _antiwear.needMinActiveValue = param1;
      }
   }
}

