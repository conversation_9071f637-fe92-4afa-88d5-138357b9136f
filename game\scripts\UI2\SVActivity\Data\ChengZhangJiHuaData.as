package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.IEquipmentVOsData;
   import YJFY.Utils.ClearUtil;
   
   public class ChengZhangJiHuaData extends DataManagerParent implements IEquipmentVOsData
   {
      private var m_ticketPrice:uint;
      
      private var m_ticketPriceId:String;
      
      private var m_startTime:String;
      
      private var m_stopTime:String;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function ChengZhangJiHuaData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_startTime = null;
         m_stopTime = null;
         m_ticketPriceId = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.m_startTime = String(param1.@startTime);
         this.m_stopTime = String(param1.@endTime);
         this.ticketPrice = uint(param1.@ticketPrice);
         this.ticketPriceId = String(param1.@ticketPriceId);
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
      }
      
      public function getTicketPrice() : uint
      {
         return ticketPrice;
      }
      
      public function getTicketPriceId() : String
      {
         return ticketPriceId;
      }
      
      public function getEquipmentVONum() : uint
      {
         return m_equipmentVOs.length;
      }
      
      public function getEquipmentVOByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
      
      public function getStartTime() : String
      {
         return m_startTime;
      }
      
      public function getEndTime() : String
      {
         return m_stopTime;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.ticketPrice = m_ticketPrice;
         _antiwear.ticketPriceId = m_ticketPriceId;
         _antiwear.startTime = m_startTime;
         _antiwear.endTime = m_stopTime;
      }
      
      private function get ticketPrice() : uint
      {
         return _antiwear.ticketPrice;
      }
      
      private function set ticketPrice(param1:uint) : void
      {
         _antiwear.ticketPrice = param1;
      }
      
      private function get ticketPriceId() : String
      {
         return _antiwear.ticketPriceId;
      }
      
      private function set ticketPriceId(param1:String) : void
      {
         _antiwear.ticketPriceId = param1;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
      
      private function get endTime() : String
      {
         return _antiwear.endTime;
      }
      
      private function set endTime(param1:String) : void
      {
         _antiwear.endTime = param1;
      }
   }
}

