package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_ApplyForJoinDetail extends InformationBodyDetail
   {
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_societyId:int;
      
      public function UP_ApplyForJoinDetail()
      {
         super();
         m_informationBodyId = 3012;
      }
      
      public function initData(param1:Number, param2:int, param3:int) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_societyId = param3;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_societyId);
         return _loc1_;
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
   }
}

