package UI2.ResetPlayerType
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.ResetPlayerTypeLogic;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.GameData;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class ResetPlayerTypePanel extends MySprite
   {
      private const m_const_frameLabel:String = "action^stop^";
      
      private var m_playerAllTypes:Array;
      
      private var m_show:MovieClip;
      
      private var m_resetPlayerTypeData:ResetPlayerTypeData;
      
      private var m_isReadyOfResetPlayerTypeData:Boolean;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_playersShowBtnGroup:Sprite;
      
      private var m_upPlayerShowBtn:ButtonLogicShell2;
      
      private var m_downPlayerShowBtn:ButtonLogicShell2;
      
      private var m_resetBtn:ButtonLogicShell2;
      
      private var m_explainBtn:ButtonLogicShell2;
      
      private var m_playersShow:MovieClipPlayLogicShell;
      
      private var m_playerShow:MovieClipPlayLogicShell;
      
      private var m_playerOneAnimation:AnimationShowPlayLogicShell;
      
      private var m_playerOneAnimationFrameListener:AnimationPlayFrameLabelListener;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_currentPlayerTypeIndex:int;
      
      private var m_resetPlayerTypeLogic:ResetPlayerTypeLogic;
      
      private var m_explainPanel:ExplainPanel;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_gamingUI:GamingUI;
      
      private var m_player_wantResetType:Player;
      
      private var m_player1:Player;
      
      private var m_player2:Player;
      
      public function ResetPlayerTypePanel()
      {
         super();
         m_playerAllTypes = [];
         m_playerAllTypes.push("SunWuKong");
         m_playerAllTypes.push("BaiLongMa");
         m_playerAllTypes.push("ErLangShen");
         m_playerAllTypes.push("ChangE");
         m_playerAllTypes.push("Fox");
         m_playerAllTypes.push("TieShan");
         m_playerAllTypes.push("Houyi");
         m_playerAllTypes.push("ZiXia");
         m_quitBtn = new ButtonLogicShell2();
         m_upPlayerShowBtn = new ButtonLogicShell2();
         m_downPlayerShowBtn = new ButtonLogicShell2();
         m_resetBtn = new ButtonLogicShell2();
         m_explainBtn = new ButtonLogicShell2();
         m_playersShow = new MovieClipPlayLogicShell();
         m_playerShow = new MovieClipPlayLogicShell();
         m_playerOneAnimation = new AnimationShowPlayLogicShell();
         m_playerOneAnimationFrameListener = new AnimationPlayFrameLabelListener();
         m_playerOneAnimationFrameListener.reachFrameLabelFun2 = reachFrameLabel;
         m_playerOneAnimation.addFrameLabelListener(m_playerOneAnimationFrameListener);
         m_resetPlayerTypeData = new ResetPlayerTypeData();
         m_currentPlayerTypeIndex = -1;
         m_resetPlayerTypeLogic = new ResetPlayerTypeLogic();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         ClearUtil.clearObject(m_playerAllTypes);
         m_playerAllTypes = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_resetPlayerTypeData);
         m_resetPlayerTypeData = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_playersShowBtnGroup);
         m_playersShowBtnGroup = null;
         ClearUtil.clearObject(m_upPlayerShowBtn);
         m_upPlayerShowBtn = null;
         ClearUtil.clearObject(m_downPlayerShowBtn);
         m_downPlayerShowBtn = null;
         ClearUtil.clearObject(m_resetBtn);
         m_resetBtn = null;
         ClearUtil.clearObject(m_explainBtn);
         m_explainBtn = null;
         ClearUtil.clearObject(m_playersShow);
         m_playersShow = null;
         ClearUtil.clearObject(m_playerShow);
         m_playerShow = null;
         ClearUtil.clearObject(m_playerOneAnimation);
         m_playerOneAnimation = null;
         ClearUtil.clearObject(m_playerOneAnimationFrameListener);
         m_playerOneAnimationFrameListener = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_resetPlayerTypeLogic);
         m_resetPlayerTypeLogic = null;
         ClearUtil.clearObject(m_explainPanel);
         m_explainPanel = null;
         m_versionControl = null;
         m_loadUI = null;
         m_gamingUI = null;
         m_player_wantResetType = null;
         m_player1 = null;
         m_player2 = null;
         super.clear();
      }
      
      public function init(param1:Player, param2:GamingUI, param3:IVersionControl, param4:IProgressShow) : void
      {
         m_player_wantResetType = param1;
         m_gamingUI = param2;
         m_versionControl = param3;
         m_loadUI = param4;
         m_player1 = m_gamingUI.player1;
         m_player2 = m_gamingUI.player2;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/resetPlayerType.xml",getXMLSuccess,getFail);
         m_myLoader.getClass("UISprite2/ResetPlayerTypePanel.swf","ResetPlayerTypePanel",getShowSuccess,getFail);
         m_myLoader.load();
         m_isReadyOfResetPlayerTypeData = false;
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_playersShowBtnGroup = m_show["playersShowBtnGroup"];
         m_upPlayerShowBtn.setShow(m_playersShowBtnGroup["upBtn"]);
         m_downPlayerShowBtn.setShow(m_playersShowBtnGroup["downBtn"]);
         m_resetBtn.setShow(m_show["resetBtn"]);
         m_resetBtn.setTipString("点击开始转职");
         m_explainBtn.setShow(m_show["explainBtn"]);
         m_explainBtn.setTipString("点击查看具体说明");
         m_playersShow.setShow(m_show["playersShow"]);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_isReadyOfResetPlayerTypeData == false)
         {
            return;
         }
         var _loc1_:String = downPlayerType();
         changePlayerShow(_loc1_);
      }
      
      private function changePlayerShow(param1:String) : void
      {
         m_playersShow.gotoAndStop(param1);
         m_playerShow.setShow(m_playersShow.getShow()["playerShow"]);
         m_playerShow.gotoAndStop("2");
         m_playerOneAnimation.setShow(m_playerShow.getShow().getChildAt(0));
         m_playerOneAnimation.gotoAndPlay("1");
      }
      
      private function reachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         if(param2 == "action^stop^")
         {
            m_playerShow.gotoAndStop("1");
            m_playerOneAnimation.setShow(m_playerShow.getShow().getChildAt(0));
            m_playerOneAnimation.gotoAndPlay("1");
         }
      }
      
      private function downPlayerType() : String
      {
         ++m_currentPlayerTypeIndex;
         if(m_currentPlayerTypeIndex >= m_playerAllTypes.length)
         {
            m_currentPlayerTypeIndex = 0;
         }
         if(isFeasibleForCurrentPlayerType() == false)
         {
            downPlayerType();
         }
         return m_playerAllTypes[m_currentPlayerTypeIndex];
      }
      
      private function upPlayerType() : String
      {
         --m_currentPlayerTypeIndex;
         if(m_currentPlayerTypeIndex < 0)
         {
            m_currentPlayerTypeIndex = m_playerAllTypes.length - 1;
         }
         if(isFeasibleForCurrentPlayerType() == false)
         {
            upPlayerType();
         }
         return m_playerAllTypes[m_currentPlayerTypeIndex];
      }
      
      private function isFeasibleForCurrentPlayerType() : Boolean
      {
         if(m_currentPlayerTypeIndex == -1)
         {
            return false;
         }
         if(m_currentPlayerTypeIndex >= m_playerAllTypes.length)
         {
            return false;
         }
         var _loc1_:String = m_playerAllTypes[m_currentPlayerTypeIndex];
         if(m_player1.playerVO.playerType == _loc1_)
         {
            return false;
         }
         if(Boolean(m_player2) && m_player2.playerVO.playerType == _loc1_)
         {
            return false;
         }
         return true;
      }
      
      private function isAbleResetPlayerType() : Boolean
      {
         var _loc2_:int = 0;
         if(m_player_wantResetType.playerVO.level < m_resetPlayerTypeData.getMinLevel())
         {
            showWarningBox("等级不足,不能转职",0);
            return false;
         }
         var _loc1_:int = int(m_player_wantResetType.playerVO.inforEquipmentVOs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_player_wantResetType.playerVO.inforEquipmentVOs[_loc2_])
            {
               showWarningBox("必须卸下所有装备，才能转职",0);
               return false;
            }
            _loc2_++;
         }
         return true;
      }
      
      private function resetPlayerType() : void
      {
         if(isAbleResetPlayerType() == false)
         {
            return;
         }
         showWarningBox("是否花费" + m_resetPlayerTypeData.getNeedTicketPrice() + "点券转职。",3,{
            "type":"resetPlayerType",
            "okFunction":buyResetPlayerType
         });
      }
      
      private function buyResetPlayerType() : void
      {
         var price:int = int(m_resetPlayerTypeData.getNeedTicketPrice());
         var ticketId:String = m_resetPlayerTypeData.getNeedTiccketId();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买转职";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            resetPlayerType2();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function resetPlayerType2() : void
      {
         var _loc1_:String = m_playerAllTypes[m_currentPlayerTypeIndex];
         m_resetPlayerTypeLogic.resetPlayerType(m_player_wantResetType,_loc1_);
         m_gamingUI.closeResetPlayerTypePanel();
         Part1.getInstance().resetInitPlayerInCityMap();
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      private function openExplainPanel() : void
      {
         if(m_explainPanel == null)
         {
            m_explainPanel = new ExplainPanel();
            m_explainPanel.init(this);
            addChild(m_explainPanel);
            m_explainPanel.x = (stage.stageWidth - m_explainPanel.width) / 2;
            m_explainPanel.y = (stage.stageHeight - m_explainPanel.height) / 2;
         }
      }
      
      public function closeExplainPanel() : void
      {
         ClearUtil.clearObject(m_explainPanel);
         m_explainPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_upPlayerShowBtn:
               changePlayerShow(upPlayerType());
               break;
            case m_downPlayerShowBtn:
               changePlayerShow(downPlayerType());
               break;
            case m_quitBtn:
               m_gamingUI.closeResetPlayerTypePanel();
               break;
            case m_resetBtn:
               resetPlayerType();
               break;
            case m_explainBtn:
               openExplainPanel();
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_isReadyOfResetPlayerTypeData = true;
         initShow();
         initShow2();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_resetPlayerTypeData.initByXML(param1.resultXML);
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeResetPlayerTypePanel();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
   }
}

