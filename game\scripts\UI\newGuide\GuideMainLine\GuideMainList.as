package UI.newGuide.GuideMainLine
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MainLineTask.PhaseTaskList;
   import UI.MyFunction2;
   import UI.newGuide.NewGuidePanel;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class GuideMainList
   {
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_guidemainpanel:GuideMainPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_dataLayer:MovieClip;
      
      private var m_spritelist:Vector.<Sprite>;
      
      private var m_slider:MovieClip;
      
      private var m_sliderbg:MovieClip;
      
      private var m_upbtn:ButtonLogicShell2;
      
      private var m_downbtn:ButtonLogicShell2;
      
      private var m_bDown:Boolean = false;
      
      private var m_bIn:Boolean = false;
      
      private var m_itemHeigh:int = 60;
      
      private var m_rotio:Number;
      
      private var m_itemlist:Vector.<GuideMainItem>;
      
      private var m_bFirst:Boolean = false;
      
      private var m_minX:Number = 185;
      
      private var m_maxX:Number = 210;
      
      private var m_minY:Number = 62;
      
      private var m_maxY:Number = 68;
      
      public function GuideMainList()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
      }
      
      public function init(param1:NewGuidePanel, param2:GuideMainPanel, param3:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_guidemainpanel = param2;
         m_show = param3;
         m_mc = m_show["listpanel"];
         initShow();
      }
      
      public function initShow() : void
      {
         m_dataLayer = m_mc["container"];
         m_slider = m_mc["slider_L"];
         m_upbtn = new ButtonLogicShell2();
         m_upbtn.setShow(m_mc["upbtn"]);
         m_upbtn.setTipString("上翻");
         m_downbtn = new ButtonLogicShell2();
         m_downbtn.setShow(m_mc["downbtn"]);
         m_downbtn.setTipString("下翻");
      }
      
      public function show() : void
      {
         registerEvent();
         refreshlist();
         if(m_dataLayer)
         {
            m_dataLayer.y = 45;
         }
      }
      
      public function hide() : void
      {
         closeEvent();
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
      }
      
      private function registerEvent() : void
      {
         m_mc.addEventListener("enterFrame",render);
         m_slider.addEventListener("mouseDown",calldown);
         m_show.addEventListener("mouseUp",calldown);
      }
      
      private function closeEvent() : void
      {
         m_mc.removeEventListener("enterFrame",render);
         m_slider.removeEventListener("mouseDown",calldown);
         m_show.removeEventListener("mouseUp",calldown);
      }
      
      private function calldown(param1:MouseEvent) : void
      {
         if(param1.type == "mouseDown")
         {
            m_bDown = true;
         }
         else
         {
            m_bDown = false;
         }
      }
      
      private function render(param1:Event) : void
      {
      }
      
      private function moveMouse() : void
      {
         true;
         if(m_bDown)
         {
            if(m_mc.mouseY - 17 <= m_maxY && m_mc.mouseY - 17 >= m_minY)
            {
               m_slider.y = m_mc.mouseY - 17;
            }
         }
      }
      
      private function moveInfo() : void
      {
         m_rotio = (m_slider.y - m_minY) / (m_maxY - m_minY);
         m_dataLayer.y = -m_rotio * (m_dataLayer.numChildren * m_itemHeigh);
      }
      
      private function checkIN() : void
      {
         if(m_mc.mouseX <= m_maxX && m_mc.mouseX >= m_minX && m_mc.mouseY >= m_minY && m_mc.mouseY <= m_maxY)
         {
            m_bIn = true;
         }
         else
         {
            m_bIn = false;
         }
      }
      
      public function refreshlist() : void
      {
         var _loc2_:GuideMainItem = null;
         var _loc4_:Sprite = null;
         var _loc3_:PhaseTaskList = null;
         var _loc8_:* = undefined;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         m_itemlist = new Vector.<GuideMainItem>();
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
         m_spritelist = new Vector.<Sprite>();
         m_bFirst = false;
         var _loc1_:int = 0;
         var _loc6_:Vector.<PhaseTaskList> = NewMainTaskData.getInstance().phaseTaskLists;
         _loc7_ = 0;
         while(_loc7_ < _loc6_.length)
         {
            _loc3_ = _loc6_[_loc7_];
            if(_loc3_)
            {
               _loc8_ = _loc3_.taskVOs;
               _loc5_ = 0;
               while(_loc5_ < _loc8_.length)
               {
                  if(_loc8_[_loc5_].isGotReward == false && _loc8_[_loc5_].isWorking)
                  {
                     _loc4_ = MyFunction2.returnShowByClassName("listitem") as Sprite;
                     m_spritelist.push(_loc4_);
                     _loc2_ = new GuideMainItem();
                     _loc2_.init(m_newguidepanel,m_guidemainpanel,this,_loc8_[_loc5_],m_show,_loc4_,_loc8_[_loc5_].id);
                     _loc2_.show();
                     m_itemlist.push(_loc2_);
                     _loc4_.x = 0;
                     _loc4_.y = _loc1_ * m_itemHeigh;
                     m_dataLayer.addChild(_loc4_);
                     _loc1_++;
                  }
                  _loc5_++;
               }
            }
            _loc7_++;
         }
         arrangeList();
      }
      
      public function setFirst(param1:Boolean) : void
      {
         m_bFirst = param1;
      }
      
      public function refreshById(param1:String) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_itemlist.length)
         {
            m_itemlist[_loc2_].checkCurrent(param1);
            _loc2_++;
         }
      }
      
      public function arrangeList() : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:String = "-1";
         if(m_itemlist && m_itemlist.length > 0)
         {
            _loc3_ = 0;
            while(_loc3_ < m_itemlist.length)
            {
               if(m_itemlist[_loc3_].getData().isGotReward == false)
               {
                  m_itemlist[_loc3_].getShow().y = _loc2_ * m_itemHeigh;
                  m_itemlist[_loc3_].refreshInfo();
                  _loc2_++;
                  if(m_bFirst == false)
                  {
                     m_bFirst = true;
                     m_itemlist[_loc3_].refreshScript();
                  }
                  if(_loc1_ == "-1")
                  {
                     _loc1_ = m_itemlist[_loc3_].getID();
                  }
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < m_itemlist.length)
            {
               if(m_itemlist[_loc3_].getData().isGotReward == true)
               {
                  m_itemlist[_loc3_].getShow().y = _loc2_ * m_itemHeigh;
                  m_itemlist[_loc3_].refreshInfo();
                  _loc2_++;
                  if(m_bFirst == false)
                  {
                     m_bFirst = true;
                     m_itemlist[_loc3_].refreshScript();
                  }
                  if(_loc1_ == "-1")
                  {
                     _loc1_ = m_itemlist[_loc3_].getID();
                  }
               }
               _loc3_++;
            }
            if(_loc1_ != "-1")
            {
               refreshById(_loc1_);
            }
         }
      }
   }
}

