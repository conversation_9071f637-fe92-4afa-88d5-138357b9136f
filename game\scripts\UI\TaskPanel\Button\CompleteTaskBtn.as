package UI.TaskPanel.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class CompleteTaskBtn extends Btn
   {
      public function CompleteTaskBtn()
      {
         super();
         setTipString("点击完成任务");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickCompleteTaskBtn"));
      }
   }
}

