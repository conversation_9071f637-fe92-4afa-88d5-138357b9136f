package UI.AutomaticPetPanel
{
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Other.EquipmentAndNumShow2;
   import UI.Other.NeedEquipmentData;
   import UI.Other.NeedEquipmentsData;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.EntityShowContainer;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AdvancePanel
   {
      private var m_petNameText:TextField;
      
      private var m_currentAdvanceShow:MovieClipPlayLogicShell;
      
      private var m_nextAdvanceShow:MovieClipPlayLogicShell;
      
      private var m_attrShowMC:MovieClipPlayLogicShell;
      
      private var m_hpText:TextField;
      
      private var m_mpText:TextField;
      
      private var m_attackText:TextField;
      
      private var m_defenceText:TextField;
      
      private var m_hitRateText:TextField;
      
      private var m_dogdeRateText:TextField;
      
      private var m_criticalRateText:TextField;
      
      private var m_decriticalRateText:TextField;
      
      private var m_addHpText:TextField;
      
      private var m_addMpText:TextField;
      
      private var m_addAttackText:TextField;
      
      private var m_addDefenceText:TextField;
      
      private var m_addHitRateText:TextField;
      
      private var m_addDogdeRateText:TextField;
      
      private var m_addCriticalRateText:TextField;
      
      private var m_addDecriticalRateText:TextField;
      
      private var m_advanceDataShowMC:MovieClipPlayLogicShell;
      
      private var m_advanceNeedEqAndNumShows:Vector.<EquipmentAndNumShow2>;
      
      private var m_advanceSuccessRateText:TextField;
      
      private var m_startBtn:ButtonLogicShell2;
      
      private var m_showAnimationContianer:EntityShowContainer;
      
      private var m_nextAdvanceAutomaticPetVO:AutomaticPetVO;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_autoPetUpgradePingJieData:AutoPetUpgradePingJieData;
      
      private var m_needEquipments:Vector.<Equipment>;
      
      private var m_buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var m_preSkinBtn:ButtonLogicShell2;
      
      private var m_nextSkinBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_showContainer:MovieClip;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetsData:AutomaticPetsData;
      
      private var m_automaticPetPanel:AutomaticPetPanel;
      
      private var forceShowOneVO:AutomaticPetVO;
      
      public function AdvancePanel()
      {
         super();
         m_showAnimationContianer = new EntityShowContainer();
         m_showAnimationContianer.init();
         m_startBtn = new ButtonLogicShell2();
         m_attrShowMC = new MovieClipPlayLogicShell();
         m_font = new FangZhengKaTongJianTi();
         m_advanceDataShowMC = new MovieClipPlayLogicShell();
         m_autoPetUpgradePingJieData = new AutoPetUpgradePingJieData();
         m_needEquipments = new Vector.<Equipment>();
         m_preSkinBtn = new ButtonLogicShell2();
         m_nextSkinBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_petNameText = null;
         ClearUtil.clearObject(m_currentAdvanceShow);
         m_currentAdvanceShow = null;
         ClearUtil.clearObject(m_nextAdvanceShow);
         m_nextAdvanceShow = null;
         ClearUtil.clearObject(m_attrShowMC);
         m_attrShowMC = null;
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         ClearUtil.clearObject(m_font);
         m_font = null;
         m_addHpText = null;
         m_addMpText = null;
         m_addAttackText = null;
         m_addDefenceText = null;
         m_addHitRateText = null;
         m_addDogdeRateText = null;
         m_addCriticalRateText = null;
         m_addDecriticalRateText = null;
         ClearUtil.clearObject(m_startBtn);
         m_startBtn = null;
         ClearUtil.clearObject(m_showAnimationContianer);
         m_showAnimationContianer = null;
         ClearUtil.clearObject(m_nextAdvanceAutomaticPetVO);
         m_nextAdvanceAutomaticPetVO = null;
         ClearUtil.clearObject(m_autoPetUpgradePingJieData);
         m_autoPetUpgradePingJieData = null;
         ClearUtil.clearObject(m_needEquipments);
         m_needEquipments = null;
         ClearUtil.clearObject(m_buyMaterialBtn);
         m_buyMaterialBtn = null;
         ClearUtil.clearObject(m_preSkinBtn);
         m_preSkinBtn = null;
         ClearUtil.clearObject(m_nextSkinBtn);
         m_nextSkinBtn = null;
         m_show = null;
         m_showContainer = null;
         m_automaticPetVO = null;
         m_automaticPetsData = null;
         m_automaticPetPanel = null;
      }
      
      public function setShow(param1:MovieClip, param2:AutomaticPetPanel) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_automaticPetPanel = param2;
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO, param2:AutomaticPetsData) : void
      {
         if(forceShowOneVO && forceShowOneVO.partnerUid && forceShowOneVO.partnerUid == m_automaticPetVO.partnerUid)
         {
            m_automaticPetVO = forceShowOneVO;
         }
         else
         {
            m_automaticPetVO = param1;
         }
         forceShowOneVO = null;
         m_automaticPetsData = param2;
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_petNameText.text = m_automaticPetVO.getName();
         m_showContainer.addChild(m_showAnimationContianer.getShow());
         m_showAnimationContianer.refreshAutomaticPetShow(m_automaticPetVO);
         if(createNewNextAdvanceAutomatiPetVO())
         {
            initAttributePartTwoFrame();
            initAdvanceDataShowOneFrame();
         }
         else
         {
            initAttributePartOneFrame();
            initAdvanceDataShowTwoFrame();
         }
         if(m_automaticPetVO.partnerUid)
         {
            m_nextSkinBtn.getShow().visible = true;
            m_preSkinBtn.getShow().visible = true;
         }
         else
         {
            m_nextSkinBtn.getShow().visible = false;
            m_preSkinBtn.getShow().visible = false;
         }
      }
      
      private function initShow() : void
      {
         m_petNameText = m_show["petNameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_petNameText);
         m_showContainer = m_show["showContainer"];
         m_attrShowMC.setShow(m_show["attriShow"]);
         m_advanceDataShowMC.setShow(m_show["advanceDataShow"]);
         m_startBtn.setShow(m_show["startBtn"]);
         m_nextSkinBtn.setShow(m_show["btnNextSkin"]);
         m_nextSkinBtn.setTipString("点击切换妖将");
         m_preSkinBtn.setShow(m_show["btnPreSkin"]);
         m_preSkinBtn.setTipString("点击切换妖将");
      }
      
      private function initAdvanceDataShowOneFrame() : void
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc4_:EquipmentAndNumShow2 = null;
         clearAdvanceDataShowFrame();
         m_advanceDataShowMC.gotoAndStop("1");
         var _loc2_:int = m_advanceDataShowMC.getShow().numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc1_ = m_advanceDataShowMC.getShow().getChildAt(_loc5_);
            if(_loc1_.name.substr(0,13) == "materialShow_")
            {
               _loc3_++;
            }
            _loc5_++;
         }
         m_advanceNeedEqAndNumShows = new Vector.<EquipmentAndNumShow2>();
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = new EquipmentAndNumShow2();
            _loc4_.setShow(m_advanceDataShowMC.getShow()["materialShow_" + (_loc5_ + 1)]);
            m_advanceNeedEqAndNumShows.push(_loc4_);
            _loc5_++;
         }
         m_advanceSuccessRateText = m_advanceDataShowMC.getShow()["successRateTxt"];
         getNeedEqupmentsData();
         showAdvanceMaterial();
      }
      
      private function showAdvanceMaterial() : void
      {
         var _loc4_:int = 0;
         var _loc2_:Equipment = null;
         var _loc3_:NeedEquipmentData = null;
         ClearUtil.clearObject(m_needEquipments);
         m_needEquipments.length = 0;
         ClearUtil.clearObject(m_buyMaterialBtn);
         m_buyMaterialBtn = null;
         var _loc1_:int = int(m_advanceNeedEqAndNumShows.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            if(m_autoPetUpgradePingJieData.getNeedEquipmentsData().getNeedEquipmentDataNum() < _loc4_ + 1)
            {
               break;
            }
            _loc3_ = m_autoPetUpgradePingJieData.getNeedEquipmentsData().getNeedEquipmentDataByIndex(_loc4_);
            _loc2_ = MyFunction2.sheatheEquipmentShell(_loc3_.getEquipmentVO());
            _loc2_.addEventListener("rollOver",onOver,false,0,true);
            _loc2_.addEventListener("rollOut",onOut,false,0,true);
            m_needEquipments.push(_loc2_);
            m_advanceNeedEqAndNumShows[_loc4_].getEqContainer().addChild(_loc2_);
            m_advanceNeedEqAndNumShows[_loc4_].getCurrentNumText().text = _loc3_.getCurrentNum().toString();
            m_advanceNeedEqAndNumShows[_loc4_].getSlashText().text = "/";
            m_advanceNeedEqAndNumShows[_loc4_].getNumText().text = _loc3_.getNum().toString();
            if(_loc3_.getCurrentNum() < _loc3_.getNum())
            {
               m_advanceNeedEqAndNumShows[_loc4_].changeCurrentNumTextColor(16711680);
               if(_loc3_.getEquipmentVO().ticketPrice)
               {
                  m_buyMaterialBtn = new BuyMaterialGuideBtn(_loc3_.getEquipmentVO().id,initAdvanceDataShowOneFrame,1);
                  m_buyMaterialBtn.x = m_advanceNeedEqAndNumShows[_loc4_].getEqContainer().x + m_advanceNeedEqAndNumShows[_loc4_].getEqContainer().width / 2;
                  m_buyMaterialBtn.y = m_advanceNeedEqAndNumShows[_loc4_].getEqContainer().y + m_advanceNeedEqAndNumShows[_loc4_].getEqContainer().height;
                  m_advanceDataShowMC.getShow().addChild(m_buyMaterialBtn);
               }
            }
            else
            {
               m_advanceNeedEqAndNumShows[_loc4_].changeCurrentNumTextColor(52224);
            }
            _loc4_++;
         }
         m_advanceSuccessRateText.text = int(m_autoPetUpgradePingJieData.getSuccessRate() * 100) + "%";
      }
      
      private function initAdvanceDataShowTwoFrame() : void
      {
         clearAdvanceDataShowFrame();
         m_advanceDataShowMC.gotoAndStop("2");
      }
      
      private function clearAdvanceDataShowFrame() : void
      {
         ClearUtil.clearObject(m_advanceNeedEqAndNumShows);
         m_advanceNeedEqAndNumShows = null;
         m_advanceSuccessRateText = null;
      }
      
      private function initAttributePartOneFrame() : void
      {
         clearAttributePartFrame();
         m_attrShowMC.gotoAndStop("unableUpGrade");
         initAttributePartFrme();
         m_currentAdvanceShow = new MovieClipPlayLogicShell();
         m_currentAdvanceShow.setShow(m_attrShowMC.getShow()["currentPingJieShow"]);
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_currentAdvanceShow.gotoAndStop(m_automaticPetVO.getPingJieVO().getId());
      }
      
      private function initAttributePartTwoFrame() : void
      {
         if(m_automaticPetVO == null)
         {
            throw new Error("出错了");
         }
         clearAttributePartFrame();
         m_attrShowMC.gotoAndStop("ableUpgrade");
         initAttributePartFrme();
         m_addHpText = m_attrShowMC.getShow()["addHpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHpText);
         m_addMpText = m_attrShowMC.getShow()["addMpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addMpText);
         m_addAttackText = m_attrShowMC.getShow()["addAttackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addAttackText);
         m_addDefenceText = m_attrShowMC.getShow()["addDefenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDefenceText);
         m_addHitRateText = m_attrShowMC.getShow()["addHitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHitRateText);
         m_addDogdeRateText = m_attrShowMC.getShow()["addDogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDogdeRateText);
         m_addCriticalRateText = m_attrShowMC.getShow()["addCriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addCriticalRateText);
         m_addDecriticalRateText = m_attrShowMC.getShow()["addDecriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDecriticalRateText);
         m_currentAdvanceShow = new MovieClipPlayLogicShell();
         m_currentAdvanceShow.setShow(m_attrShowMC.getShow()["currentPingJieShow"]);
         m_nextAdvanceShow = new MovieClipPlayLogicShell();
         m_nextAdvanceShow.setShow(m_attrShowMC.getShow()["nextPingJieShow"]);
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_addHpText.text = "+" + (m_nextAdvanceAutomaticPetVO.getTotalHp() - m_automaticPetVO.getTotalHp());
         m_addMpText.text = "+" + (m_nextAdvanceAutomaticPetVO.getTotalMp() - m_automaticPetVO.getTotalMp());
         m_addAttackText.text = "+" + (m_nextAdvanceAutomaticPetVO.getAttack() - m_automaticPetVO.getAttack());
         m_addDefenceText.text = "+" + (m_nextAdvanceAutomaticPetVO.getDefence() - m_automaticPetVO.getDefence());
         m_addHitRateText.text = "+" + ((m_nextAdvanceAutomaticPetVO.getHitRate() - m_automaticPetVO.getHitRate()) * 100).toFixed(2) + "%";
         m_addDogdeRateText.text = "+" + ((m_nextAdvanceAutomaticPetVO.getDogdeRate() - m_automaticPetVO.getDogdeRate()) * 100).toFixed(2) + "%";
         m_addCriticalRateText.text = "+" + ((m_nextAdvanceAutomaticPetVO.getCriticalRate() - m_automaticPetVO.getCriticalRate()) * 100).toFixed(2) + "%";
         m_addDecriticalRateText.text = "+" + ((m_nextAdvanceAutomaticPetVO.getDeCriticalRate() - m_automaticPetVO.getDeCriticalRate()) * 100).toFixed(2) + "%";
         m_currentAdvanceShow.gotoAndStop(m_automaticPetVO.getPingJieVO().getId());
         m_nextAdvanceShow.gotoAndStop(m_nextAdvanceAutomaticPetVO.getPingJieVO().getId());
      }
      
      private function initAttributePartFrme() : void
      {
         m_hpText = m_attrShowMC.getShow()["hpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hpText);
         m_mpText = m_attrShowMC.getShow()["mpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_mpText);
         m_attackText = m_attrShowMC.getShow()["attackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_attackText);
         m_defenceText = m_attrShowMC.getShow()["defenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_defenceText);
         m_hitRateText = m_attrShowMC.getShow()["hitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hitRateText);
         m_dogdeRateText = m_attrShowMC.getShow()["dogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_dogdeRateText);
         m_criticalRateText = m_attrShowMC.getShow()["criticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_criticalRateText);
         m_decriticalRateText = m_attrShowMC.getShow()["decriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_decriticalRateText);
         m_hpText.text = m_automaticPetVO.getTotalHp().toString();
         m_mpText.text = m_automaticPetVO.getTotalMp().toString();
         m_attackText.text = m_automaticPetVO.getAttack().toString();
         m_defenceText.text = m_automaticPetVO.getDefence().toString();
         m_hitRateText.text = (m_automaticPetVO.getHitRate() * 100).toFixed(2) + "%";
         m_dogdeRateText.text = (m_automaticPetVO.getDogdeRate() * 100).toFixed(2) + "%";
         m_criticalRateText.text = (m_automaticPetVO.getCriticalRate() * 100).toFixed(2) + "%";
         m_decriticalRateText.text = (m_automaticPetVO.getDeCriticalRate() * 100).toFixed(2) + "%";
      }
      
      private function clearAttributePartFrame() : void
      {
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         ClearUtil.clearObject(m_currentAdvanceShow);
         m_currentAdvanceShow = null;
         ClearUtil.clearObject(m_nextAdvanceShow);
         m_nextAdvanceShow = null;
      }
      
      private function createNewNextAdvanceAutomatiPetVO() : Boolean
      {
         ClearUtil.clearObject(m_nextAdvanceAutomaticPetVO);
         m_nextAdvanceAutomaticPetVO = new AutomaticPetVO();
         m_nextAdvanceAutomaticPetVO.copy(m_automaticPetVO);
         return m_nextAdvanceAutomaticPetVO.getPingJieVO().upgrade();
      }
      
      private function getNeedEqupmentsData() : void
      {
         var _loc2_:XML = m_automaticPetPanel.getAutomaticPetDataXML();
         _loc2_ = _loc2_.automaticPets.automaticPet.(@id == m_automaticPetVO.getId())[0];
         if(_loc2_ == null)
         {
            _loc2_ = m_automaticPetPanel.getAutomaticPetDataXML().upgradePingJies[0];
         }
         else
         {
            _loc2_ = _loc2_.upgradePingJies[0];
         }
         _loc2_ = _loc2_.upgradePingJie.(@oldPingJie == m_automaticPetVO.getPingJieVO().getId())[0];
         if(_loc2_ == null)
         {
            throw new Error("there is not the upgradePingJie xml");
         }
         var _loc1_:NeedEquipmentsData = new NeedEquipmentsData();
         _loc1_.initByXML(_loc2_,GamingUI.getInstance().getNewestTimeStrFromSever());
         _loc1_.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         m_autoPetUpgradePingJieData.setData(_loc1_,_loc2_.@successRate);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         switch(param1.button)
         {
            case m_startBtn:
               startAdvance();
               §§goto(addr41);
            case m_nextSkinBtn:
            case m_preSkinBtn:
               break;
            default:
               addr41:
               return;
         }
         setData(GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName),m_automaticPetsData);
      }
      
      public function startAdvance() : void
      {
         if(m_autoPetUpgradePingJieData.getNeedEquipmentsData() == null)
         {
            return;
         }
         if(m_autoPetUpgradePingJieData.getNeedEquipmentsData().getIsEnough() == false)
         {
            m_automaticPetPanel.showWarningBox("材料不足, 不能进阶！",0);
            return;
         }
         m_autoPetUpgradePingJieData.getNeedEquipmentsData().minusNeedEquipmentsFromPlayer(GamingUI.getInstance().player1.playerVO,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO : null);
         var _loc2_:Number = Math.random();
         if(_loc2_ < m_autoPetUpgradePingJieData.getSuccessRate())
         {
            forceShowOneVO = m_automaticPetVO;
            m_automaticPetVO.getPingJieVO().upgrade();
            m_automaticPetVO.changeData();
            m_automaticPetPanel.showWarningBox("进阶成功",0);
         }
         else
         {
            m_automaticPetPanel.showWarningBox("进阶失败",0);
         }
         setData(m_automaticPetVO,m_automaticPetsData);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

