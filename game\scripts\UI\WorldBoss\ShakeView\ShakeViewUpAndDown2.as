package UI.WorldBoss.ShakeView
{
   import UI.WorldBoss.View;
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   
   public class ShakeViewUpAndDown2 extends ShakeView
   {
      public function ShakeViewUpAndDown2()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function shakeView(param1:View) : void
      {
         var view:View = param1;
         var complete:* = function():void
         {
            TweenLite.to(view.getShow(),0.1,{
               "scaleX":1,
               "scaleY":1,
               "x":0,
               "y":0,
               "ease":Linear.easeNone,
               "onComplete":complete2,
               "onCompleteParams":[]
            });
         };
         var complete2:* = function():void
         {
            _isShake = false;
            recover();
            view.setIsShake(false);
         };
         super.shakeView(view);
         _isShake = true;
         _isMyShake = true;
         view.setIsShake(true);
         TweenLite.to(view.getShow(),0.05,{
            "scaleX":0.9,
            "scaleY":0.9,
            "x":0.1 * view.getShow().width / 2,
            "y":0.1 * view.getShow().height / 2,
            "ease":Linear.easeNone,
            "onComplete":complete,
            "onCompleteParams":[]
         });
      }
      
      override protected function recover() : void
      {
         if(_isMyShake)
         {
            _view.getShow().y = 0;
         }
         super.recover();
      }
   }
}

