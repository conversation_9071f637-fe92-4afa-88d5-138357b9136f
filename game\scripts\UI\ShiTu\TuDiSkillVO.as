package UI.ShiTu
{
   import UI.DataManagerParent;
   import UI.MyFunction;
   import UI.XMLSingle;
   
   public class TuDi<PERSON>killVO extends DataManagerParent
   {
      public var keyStr:String;
      
      public var className:String;
      
      public var description:String;
      
      public var tuDiVO:TuDiVO;
      
      public var serialNumber:int;
      
      public var name:String;
      
      public var id2:String;
      
      private var _id:String;
      
      private var _baseLevel:int;
      
      private var _xiuLianLevel:int;
      
      private var _level:int;
      
      private var _baseAttackMutiple:Number;
      
      private var _xiuLianAttackMutiple:Number;
      
      private var _attackMutiple:Number;
      
      private var _baseAttackAdd:Number;
      
      private var _xiuLianAttackAdd:Number;
      
      private var _attackAdd:Number;
      
      private var _baseRequireEnergy:int;
      
      private var _xiuLianRequireEnergy:int;
      
      private var _otherRequireEnergy:int;
      
      private var _requireEnergy:int;
      
      public function TuDiSkillVO()
      {
         super();
      }
      
      public static function setTuDiSkillVO(param1:TuDiSkillVO, param2:XML) : TuDiSkillVO
      {
         var _loc4_:XML = param2.item.(@id == param1.id)[0];
         param1.name = String(_loc4_.@name);
         param1.className = String(_loc4_.@className);
         param1.id2 = String(_loc4_.@id2);
         param1.description = String(_loc4_.@description);
         var _loc3_:int = int(param2.initLevel.(@id2 == param1.id2)[0].@initLevel);
         param1.baseLevel = _loc3_;
         param1.serialNumber = int(_loc4_.@serialNumber);
         param1.baseAttackMutiple = Number(_loc4_.@attackMutiple);
         param1.baseAttackAdd = Number(_loc4_.@attackAdd);
         param1.baseRequireEnergy = int(_loc4_.@requireEnergy);
         return param1;
      }
      
      public static function createInitLevelTuDiSkillVOById2(param1:String, param2:XML) : TuDiSkillVO
      {
         var _loc3_:int = int(param2.initLevel.(@id2 == param1)[0].@initLevel);
         var _loc5_:XML = param2.item.(@id2 == param1 && @level == _loc3_)[0];
         var _loc4_:TuDiSkillVO = new TuDiSkillVO();
         _loc4_.id = String(_loc5_.@id);
         setTuDiSkillVO(_loc4_,param2);
         return _loc4_;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.id = _id;
         _antiwear.baseLevel = _baseLevel;
         _antiwear.xiuLianLevel = _xiuLianLevel;
         _antiwear.baseAttackMutiple = _baseAttackMutiple = 0;
         _antiwear.xiuLianAttackMutiple = _xiuLianAttackMutiple = 0;
         _antiwear.baseAttackAdd = _baseAttackAdd = 0;
         _antiwear.xiuLianAttackAdd = _xiuLianAttackAdd = 0;
         _antiwear.baseRequireEnergy = _baseRequireEnergy;
         _antiwear.xiuLianRequireEnergy = _xiuLianRequireEnergy;
         _antiwear.otherRequireEnergy = _otherRequireEnergy;
      }
      
      public function get id() : String
      {
         return _antiwear.id;
      }
      
      public function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      public function get baseLevel() : int
      {
         return _antiwear.baseLevel;
      }
      
      public function set baseLevel(param1:int) : void
      {
         _antiwear.baseLevel = param1;
      }
      
      public function get xiuLianLevel() : int
      {
         return _antiwear.xiuLianLevel;
      }
      
      public function set xiuLianLevel(param1:int) : void
      {
         _antiwear.xiuLianLevel = param1;
         var _loc2_:String = MyFunction.getInstance().excreteStringToString(id)[0] + "_" + level;
         id = _loc2_;
         TuDiSkillVO.setTuDiSkillVO(this,XMLSingle.getInstance().tuDiSkillXML);
      }
      
      public function get level() : int
      {
         return baseLevel + xiuLianLevel;
      }
      
      public function get baseAttackMutiple() : Number
      {
         return _antiwear.baseAttackMutiple;
      }
      
      public function set baseAttackMutiple(param1:Number) : void
      {
         _antiwear.baseAttackMutiple = param1;
      }
      
      public function get xiuLianAttackMutiple() : Number
      {
         return _antiwear.xiuLianAttackMutiple;
      }
      
      public function set xiuLianAttackMutiple(param1:Number) : void
      {
         _antiwear.xiuLianAttackMutiple = param1;
      }
      
      public function get attackMutiple() : Number
      {
         return baseAttackMutiple + xiuLianAttackMutiple;
      }
      
      public function get baseAttackAdd() : Number
      {
         return _antiwear.baseAttackAdd;
      }
      
      public function set baseAttackAdd(param1:Number) : void
      {
         _antiwear.baseAttackAdd = param1;
      }
      
      public function get xiuLianAttackAdd() : Number
      {
         return _antiwear.xiuLianAttackAdd;
      }
      
      public function set xiuLianAttackAdd(param1:Number) : void
      {
         _antiwear.xiuLianAttackAdd = param1;
      }
      
      public function get attackAdd() : Number
      {
         return baseAttackAdd + xiuLianAttackAdd;
      }
      
      public function get attack() : Number
      {
         return attackMutiple * tuDiVO.attack + attackAdd;
      }
      
      public function get baseRequireEnergy() : int
      {
         return _antiwear.baseRequireEnergy;
      }
      
      public function set baseRequireEnergy(param1:int) : void
      {
         _antiwear.baseRequireEnergy = param1;
      }
      
      public function get xiuLianRequireEnergy() : int
      {
         return _antiwear.xiuLianRequireEnergy;
      }
      
      public function set xiuLianRequireEnergy(param1:int) : void
      {
         _antiwear.xiuLianRequireEnergy = param1;
      }
      
      public function get otherRequireEnergy() : int
      {
         return _antiwear.otherRequireEnergy;
      }
      
      public function set otherRequireEnergy(param1:int) : void
      {
         _antiwear.otherRequireEnergy = param1;
      }
      
      public function get requireEnergy() : int
      {
         return baseRequireEnergy + xiuLianRequireEnergy + otherRequireEnergy;
      }
   }
}

