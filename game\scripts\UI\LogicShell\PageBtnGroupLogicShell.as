package UI.LogicShell
{
   import GM_UI.GMData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.PageBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.SoundData;
   
   public class PageBtnGroupLogicShell extends YJFY.ShowLogicShell.PageBtnGroupLogicShell
   {
      protected var m_buttonSoundData:SoundData;
      
      public function PageBtnGroupLogicShell()
      {
         super();
         m_buttonSoundData = new SoundData("buttonSound","buttonSound","NewGameFolder/FirstEnterSource.swf","ButtonSound");
         if(GMData.getInstance().isGMApplication == false)
         {
            Part1.getInstance().getSoundManager().addSound2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
         }
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_buttonSoundData);
         m_buttonSoundData = null;
         super.clear();
      }
      
      override protected function onClick(param1:ButtonEvent) : void
      {
         super.onClick(param1);
         if(GMData.getInstance().isGMApplication == false)
         {
            Part1.getInstance().getSoundManager().play2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
         }
      }
   }
}

