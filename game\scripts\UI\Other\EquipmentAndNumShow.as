package UI.Other
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class EquipmentAndNumShow
   {
      protected var m_show:Sprite;
      
      protected var m_eqContainer:Sprite;
      
      protected var m_numText:TextField;
      
      public function EquipmentAndNumShow()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         m_eqContainer = null;
         m_numText = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_eqContainer = m_show["eqContainer"];
         m_numText = m_show["numText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_numText);
         m_numText.selectable = false;
         m_numText.mouseEnabled = false;
         m_numText.text = "";
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function getEqContainer() : Sprite
      {
         return m_eqContainer;
      }
      
      public function getNumText() : TextField
      {
         return m_numText;
      }
   }
}

