package UI.WorldBoss.GetNextStepActionEntities
{
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Entity;
   import UI.WorldBoss.IRoundChangeListener;
   import UI.WorldBoss.PetEntity;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.IFriendEntity;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.StepAttackGame.NextEntities;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   
   public class GetNextStepActionEntites2 implements IGetNextStepActionEntities
   {
      private const m_const_fightRoundNumAttName:String = "fightRoundNum";
      
      private var m_world:StepAttackGameWorld;
      
      private var m_roundNum:int;
      
      private var m_roundListeners:Vector.<IRoundChangeListener>;
      
      public function GetNextStepActionEntites2()
      {
         super();
      }
      
      public function clear() : void
      {
         m_world = null;
         ClearUtil.nullArr(m_roundListeners,false,false,false);
         m_roundListeners = null;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_world = param1;
      }
      
      public function getNextAttackAndBeAttackedEntities() : NextEntities
      {
         var _loc2_:* = undefined;
         var _loc3_:* = undefined;
         var _loc4_:NextEntities = new NextEntities();
         var _loc1_:IEntity = m_world.getCurrentAttackEntity();
         _loc2_ = getNextAttackEntities();
         _loc3_ = getNextBeAttackEntities(_loc2_[0]);
         _loc4_.nextAttackEntity = _loc2_[0];
         _loc4_.nextBeAttackedEntities = _loc3_;
         ClearUtil.nullArr(_loc2_,false,false,false);
         return _loc4_;
      }
      
      private function getNextAttackEntities() : Vector.<IEntity>
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:IEntity = null;
         var _loc1_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc2_ = m_world.getFriendNum();
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = m_world.getFriendByIndex(_loc4_);
            if(int((_loc3_ as Entity).getExtraData()["fightRoundNum"]) < m_roundNum)
            {
               (_loc3_ as Entity).getExtraData()["fightRoundNum"] = m_roundNum;
               _loc1_.push(_loc3_);
               trace("获取友方单位","index:",_loc4_);
               break;
            }
            _loc4_++;
         }
         if(_loc1_.length == 0)
         {
            _loc2_ = m_world.getEnemyNum();
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc3_ = m_world.getEnemyByIndex(_loc4_);
               if(int((_loc3_ as Entity).getExtraData()["fightRoundNum"]) < m_roundNum)
               {
                  (_loc3_ as Entity).getExtraData()["fightRoundNum"] = m_roundNum;
                  _loc1_.push(_loc3_);
                  trace("获取到敌方单位","index",_loc4_);
                  break;
               }
               _loc4_++;
            }
         }
         if(_loc1_.length == 0)
         {
            m_roundNum++;
            _loc1_ = getNextAttackEntities();
         }
         trace("roundNum",m_roundNum);
         return _loc1_;
      }
      
      private function getNextBeAttackEntities(param1:IEntity) : Vector.<IEntity>
      {
         var _loc3_:Function = null;
         var _loc2_:Function = null;
         if(param1 is IFriendEntity)
         {
            _loc3_ = m_world.getEnemyNum;
            _loc2_ = m_world.getEnemyByIndex;
         }
         else
         {
            _loc3_ = m_world.getFriendNum;
            _loc2_ = m_world.getFriendByIndex;
         }
         return randomGetBeAttackedEntities(_loc3_,_loc2_,1);
      }
      
      private function randomGetBeAttackedEntities(param1:Function, param2:Function, param3:int) : Vector.<IEntity>
      {
         var _loc9_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = 0;
         var _loc8_:int = param1();
         var _loc7_:Vector.<IEntity> = new Vector.<IEntity>();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc9_ = 0;
         while(_loc9_ < _loc8_)
         {
            _loc6_ = param2(_loc9_);
            if(_loc6_ is PetEntity)
            {
               break;
            }
            if(_loc6_ is Boss && !(_loc6_ is AbleAttackedBoss))
            {
               break;
            }
            _loc7_.push(_loc6_);
            _loc9_++;
         }
         param3 = Math.min(_loc8_,param3);
         _loc9_ = 0;
         while(_loc9_ < param3)
         {
            _loc5_ = Math.random() * _loc7_.length;
            _loc4_.push(_loc7_[_loc5_]);
            _loc7_.splice(_loc5_,1);
            _loc9_++;
         }
         return _loc4_;
      }
      
      private function randomGetAttackEntities(param1:Function, param2:Function, param3:int) : Vector.<IEntity>
      {
         var _loc9_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = 0;
         var _loc8_:int = param1();
         var _loc7_:Vector.<IEntity> = new Vector.<IEntity>();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc9_ = 0;
         while(_loc9_ < _loc8_)
         {
            _loc6_ = param2(_loc9_);
            _loc7_.push(_loc6_);
            _loc9_++;
         }
         param3 = Math.min(_loc8_,param3);
         _loc9_ = 0;
         while(_loc9_ < param3)
         {
            _loc5_ = Math.random() * _loc7_.length;
            _loc4_.push(_loc7_[_loc5_]);
            _loc7_.splice(_loc5_,1);
            _loc9_++;
         }
         return _loc4_;
      }
      
      public function getRoundNum() : int
      {
         return m_roundNum;
      }
      
      public function addRoundChangeListener(param1:IRoundChangeListener) : void
      {
         if(m_roundListeners == null)
         {
            m_roundListeners = new Vector.<IRoundChangeListener>();
         }
         m_roundListeners.push(param1);
      }
      
      public function removeRoundChangeListener(param1:IRoundChangeListener) : void
      {
         if(m_roundListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_roundListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_roundListeners.splice(_loc2_,1);
            _loc2_ = int(m_roundListeners.indexOf(param1));
         }
      }
      
      private function changeRound() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IRoundChangeListener> = !!m_roundListeners ? m_roundListeners.slice(0) : null;
         var _loc2_:int = !!_loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].roundChange(m_roundNum);
            }
            _loc3_++;
         }
      }
   }
}

