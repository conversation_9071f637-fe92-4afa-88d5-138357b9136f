package UI.SocietySystem.MySocietyPanel
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class OtherOperationPanel
   {
      private var m_clear:ClearHelper;
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_ableDragLG:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_nameText:TextField;
      
      private var m_levelText:TextField;
      
      private var m_conValueText:TextField;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_removeMemberBtn:ButtonLogicShell2;
      
      private var m_memberData:MemberDataInMemberList;
      
      private var m_isMyPlayer:Boolean;
      
      private var m_isLeader:Boolean;
      
      public function OtherOperationPanel()
      {
         super();
         m_clear = new ClearHelper();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         if(Boolean(m_show) && m_show.parent)
         {
            m_show.parent.removeChild(m_show);
         }
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_ableDragLG);
         m_ableDragLG = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_nameText = null;
         m_levelText = null;
         m_conValueText = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_removeMemberBtn);
         m_removeMemberBtn = null;
         m_memberData = null;
      }
      
      public function setMemberData(param1:MemberDataInMemberList) : void
      {
         m_memberData = param1;
         if(m_show)
         {
            initShow3();
         }
      }
      
      public function setIsMyPlayer(param1:Boolean, param2:Boolean) : void
      {
         m_isLeader = param1;
         m_isMyPlayer = param2;
         if(m_showMC)
         {
            initShow2();
         }
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_ableDragLG = new AbleDragSpriteLogicShell();
         m_ableDragLG.setShow(m_show);
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_isMyPlayer || m_isLeader == false)
         {
            m_showMC.gotoAndStop("myPlayer");
         }
         else
         {
            m_showMC.gotoAndStop("otherPlayer");
         }
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_nameText);
         m_levelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_levelText);
         m_conValueText = m_show["conValueText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_conValueText);
         m_lookUpBtn = new ButtonLogicShell2();
         m_lookUpBtn.setShow(m_show["lookUpBtn"]);
         m_lookUpBtn.setTipString("查看玩家详细装备");
         if(m_show["removeMemberBtn"])
         {
            m_removeMemberBtn = new ButtonLogicShell2();
            m_removeMemberBtn.setShow(m_show["removeMemberBtn"]);
            m_removeMemberBtn.setTipString("点击踢出该玩家");
         }
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
         initShow3();
      }
      
      private function initShow3() : void
      {
         if(m_memberData == null)
         {
            return;
         }
         m_nameText.text = !!m_memberData.getName_member() ? m_memberData.getName_member() : m_memberData.getUid_member().toString();
         m_levelText.text = m_memberData.getLevel_member().toString();
         m_conValueText.text = m_memberData.getPersonalReConValue() + "/" + m_memberData.getPersonalTotalConValue();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getMemberData() : MemberDataInMemberList
      {
         return m_memberData;
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      public function getRemoveMemberBtn() : ButtonLogicShell2
      {
         return m_removeMemberBtn;
      }
   }
}

