package UI2.Mount.MountLogic
{
   import UI2.Mount.MountData.MountSystemData.MaterialData;
   import UI2.Mount.MountData.MountSystemData.MountSystemData;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.Lottery.IFilter;
   import YJFY.Lottery.LotteryDataOne;
   
   public class MaterialFilter implements IFilter
   {
      private var m_mountsVO:MountsVO;
      
      private var m_mountSystemData:MountSystemData;
      
      public function MaterialFilter()
      {
         super();
      }
      
      public function clear() : void
      {
         m_mountsVO = null;
         m_mountSystemData = null;
      }
      
      public function init(param1:MountsVO, param2:MountSystemData) : void
      {
         m_mountsVO = param1;
         m_mountSystemData = param2;
      }
      
      public function runFilter(param1:LotteryDataOne) : Boolean
      {
         var _loc3_:MountVO = null;
         var _loc2_:MaterialData = m_mountSystemData.getMaterialDataById(param1.getEquipmentId());
         if(_loc2_.getType() == "powerChip")
         {
            _loc3_ = m_mountsVO.getMountVOById(_loc2_.getMountId());
            if(_loc3_.getLevel1() >= _loc3_.getMaxLevel1())
            {
               return false;
            }
         }
         return true;
      }
   }
}

