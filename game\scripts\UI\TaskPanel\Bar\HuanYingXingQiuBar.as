package UI.TaskPanel.Bar
{
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.TaskPanel.InTaskPanel;
   import flash.display.Sprite;
   
   public class HuanYingXingQiuBar extends MySprite implements ITaskBar
   {
      private var _show:Sprite;
      
      private var _bar:CMSXChangeBarLogicShell;
      
      public function HuanYingXingQiuBar()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         _show = null;
         if(_bar)
         {
            _bar.clear();
         }
         _bar = null;
      }
      
      public function change(param1:Number, param2:int = 0) : void
      {
         _bar.change(param1);
         _bar.setDataShow(Math.round(param1 * param2) + "/" + param2);
      }
      
      public function setInTaskPanel(param1:InTaskPanel) : void
      {
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("HuanYingXingQiuBar") as Sprite;
         addChild(_show);
         _bar = new CMSXChangeBarLogicShell();
         _bar.setShow(_show);
      }
   }
}

