package UI.ExternalUI.SkillCells
{
   import UI.EnterFrameTime;
   import UI.MyFunction;
   import UI.Other.CDAnimationShape;
   import UI.Players.PlayerVO;
   import UI.Skills.Skill;
   import UI.Skills.SkillVO;
   import UI.UIInterface.IActiveSkillVO;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   
   public class UsedSkillCellLogicShell
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _skillLayer:Sprite;
      
      private var _mcUntile:Sprite;
      
      private var _blackLayer:Shape;
      
      private var _timeShow:MultiPlaceNumLogicShell;
      
      private var _isStop:Boolean = false;
      
      private var _cdAnimationShape:CDAnimationShape;
      
      private var _skill:Skill;
      
      private var _skillVO:SkillVO;
      
      private var _lastCd:uint;
      
      private var _currentCd:uint;
      
      private var _isShowHaveEnoughMp:Boolean;
      
      private var _playerVO:PlayerVO;
      
      public function UsedSkillCellLogicShell()
      {
         super();
      }
      
      public function setShow(param1:MovieClip, param2:MovieClip) : void
      {
         _show = param1;
         _showMC = new MovieClipPlayLogicShell();
         _showMC.setShow(_show);
         initCell(param2);
      }
      
      public function setPlayerVO(param1:PlayerVO) : void
      {
         _playerVO = param1;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         renderShow();
      }
      
      private function renderShow() : void
      {
         if(Boolean(_skill) && _skill.skillVO as IActiveSkillVO)
         {
            _currentCd = (_skill.skillVO as IActiveSkillVO).currentCDTime;
            if(_currentCd != _lastCd)
            {
               _lastCd = _currentCd;
               _timeShow.showNum((_skill.skillVO as IActiveSkillVO).currentCDTime);
               _cdAnimationShape.drawShape(6.283185307179586 * ((_skill.skillVO as IActiveSkillVO).coolDown - (_skill.skillVO as IActiveSkillVO).currentCDTime) / (_skill.skillVO as IActiveSkillVO).coolDown);
            }
            if((_skill.skillVO as IActiveSkillVO).currentCDTime <= 0)
            {
               _timeShow.getShow().visible = false;
               _blackLayer.visible = false;
            }
            else
            {
               _timeShow.getShow().visible = true;
               _blackLayer.visible = true;
            }
            if(_playerVO.maxMagic * _playerVO.magicPercent < (_skill.skillVO as IActiveSkillVO).manaCost && _isShowHaveEnoughMp)
            {
               setNoActive();
               _isShowHaveEnoughMp = false;
            }
            else if(_playerVO.maxMagic * _playerVO.magicPercent >= (_skill.skillVO as IActiveSkillVO).manaCost && _isShowHaveEnoughMp == false)
            {
               _isShowHaveEnoughMp = true;
               setActive();
            }
         }
         else
         {
            _timeShow.getShow().visible = false;
            _blackLayer.visible = false;
         }
      }
      
      public function setNoActive() : void
      {
         MyFunction.getInstance().changeSaturation2(_show,-37,-46,0);
      }
      
      public function setActive() : void
      {
         MyFunction.getInstance().changeSaturation2(_show,0,0,0);
      }
      
      public function setIsUntileUse(param1:Boolean) : void
      {
         _mcUntile.visible = param1;
      }
      
      public function clear() : void
      {
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         _skillLayer = null;
         _blackLayer = null;
         ClearUtil.clearObject(_timeShow);
         _timeShow;
         _skill = null;
         _cdAnimationShape = null;
         _playerVO = null;
         _mcUntile = null;
      }
      
      protected function initCell(param1:MovieClip) : void
      {
         _mcUntile = _show["mcUntile"];
         _mcUntile.mouseChildren = false;
         _mcUntile.mouseEnabled = false;
         _mcUntile.visible = false;
         _skillLayer = _show["skillLayer"];
         _timeShow = new MultiPlaceNumLogicShell();
         _timeShow.setShow(param1);
         _blackLayer = new Shape();
         _show.addChild(_blackLayer);
         _cdAnimationShape = new CDAnimationShape();
         _cdAnimationShape.y = 0;
         _cdAnimationShape.x = 0;
         _cdAnimationShape.init(_show.width / _show.scaleX,_show.height / _show.scaleY,6736930,0.8);
         _cdAnimationShape.drawShape(6.283185307179586);
         _show.addChild(_cdAnimationShape);
         _timeShow.getShow().visible = false;
         _blackLayer.visible = false;
         drawBlackLayer();
         _skillLayer.mask = _cdAnimationShape;
      }
      
      protected function drawBlackLayer() : void
      {
         _blackLayer.graphics.lineStyle();
         _blackLayer.graphics.beginFill(0,0.3);
         _blackLayer.graphics.drawRect(-(_show.width / _show.scaleX) / 2,-(_show.height / _show.scaleY) / 2,_show.width / _show.scaleX,_show.height / _show.scaleY);
         _blackLayer.graphics.endFill();
      }
      
      public function addSkillVO(param1:SkillVO) : void
      {
         removeSkillVO();
         if(!_skill && param1 != null)
         {
            _skillVO = param1;
            if(_skill)
            {
               _skill.clear();
            }
            _skill = null;
            _skill = new Skill(param1);
            _skill.x = 0;
            _skill.y = 0;
            _skillLayer.addChild(_skill);
            _lastCd = 0;
            renderShow();
         }
      }
      
      public function removeSkillVO(param1:SkillVO = null) : void
      {
         if(_skill)
         {
            if(param1 == _skillVO || param1 == null)
            {
               _skillLayer.removeChild(_skill as DisplayObject);
               _skill = null;
               _skillVO = null;
               _timeShow.getShow().visible = false;
               _blackLayer.visible = false;
               _cdAnimationShape.drawShape(6.283185307179586);
            }
         }
      }
      
      public function get skill() : Skill
      {
         return _skill;
      }
      
      public function get skillVO() : SkillVO
      {
         return _skillVO;
      }
   }
}

