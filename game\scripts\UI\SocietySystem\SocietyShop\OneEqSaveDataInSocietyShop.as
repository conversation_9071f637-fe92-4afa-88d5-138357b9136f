package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   
   public class OneEqSaveDataInSocietyShop extends DataManagerParent
   {
      private var m_eqId:String;
      
      private var m_num:int;
      
      public function OneEqSaveDataInSocietyShop(param1:String, param2:int)
      {
         super();
         this.eqId = param1;
         this.num = param2;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.eqId = m_eqId;
         _antiwear.num = m_num;
      }
      
      public function getEqId() : String
      {
         return eqId;
      }
      
      public function getNum() : int
      {
         return num;
      }
      
      public function addNum(param1:int) : void
      {
         if(param1 <= 0)
         {
            throw new Error("增加的数据不能小于等于0");
         }
         num += param1;
      }
      
      private function get eqId() : String
      {
         return _antiwear.eqId;
      }
      
      private function set eqId(param1:String) : void
      {
         _antiwear.eqId = param1;
      }
      
      private function get num() : int
      {
         return _antiwear.num;
      }
      
      private function set num(param1:int) : void
      {
         _antiwear.num = param1;
      }
   }
}

