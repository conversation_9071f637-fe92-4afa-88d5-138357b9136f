package UI.VIPPanel
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MySprite;
   import UI.UIInterface.ISegmentedBar;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class VIPProgressBarGroup extends MySprite
   {
      private var _vipLevelTexts:Vector.<TextField> = new Vector.<TextField>();
      
      private var _pointTicketValueTexts:Vector.<TextField> = new Vector.<TextField>();
      
      private var _progressBars:Vector.<ISegmentedBar> = new Vector.<ISegmentedBar>();
      
      public function VIPProgressBarGroup()
      {
         super();
      }
      
      public function init(param1:Number, param2:Number, param3:Number, param4:int) : void
      {
         var _loc5_:Number = NaN;
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc11_:Number = new VIPProgressBar().width;
         var _loc7_:Number = (param3 - (param1 - param2 / 2) * _loc11_ * (param4 * param4 - param4) / 2 - param2 * _loc11_ * param4 * (param4 - 1) * (2 * param4 - 1) / 12) / (_loc11_ * param4);
         var _loc6_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         var _loc8_:ProgressBackgroud = new ProgressBackgroud();
         _loc8_.x = 0;
         _loc8_.y = 0;
         _loc8_.width = param3;
         _loc8_.height;
         addChild(_loc8_);
         _loc10_ = 0;
         while(_loc10_ < param4)
         {
            _progressBars.push(new VIPProgressBar());
            (_progressBars[_loc10_] as VIPProgressBar).scaleX = _loc7_ + param1 * _loc10_ + (_loc10_ * _loc10_ - _loc10_) * param2 / 2;
            _loc5_ = 0;
            _loc9_ = 0;
            while(_loc9_ < _loc10_)
            {
               _loc5_ += (_progressBars[_loc9_] as VIPProgressBar).width + 2;
               _loc9_++;
            }
            (_progressBars[_loc10_] as VIPProgressBar).x = _loc5_;
            addChild(_progressBars[_loc10_] as DisplayObject);
            _loc10_++;
         }
         _loc10_ = 0;
         while(_loc10_ < param4 - 1)
         {
            _vipLevelTexts.push(new TextField());
            _vipLevelTexts[_loc10_].defaultTextFormat = new TextFormat(_loc6_.fontName,12 + _loc10_,16776960);
            _vipLevelTexts[_loc10_].embedFonts = true;
            _vipLevelTexts[_loc10_].selectable = false;
            _vipLevelTexts[_loc10_].text = _loc10_ + 1 + "级";
            _vipLevelTexts[_loc10_].width = _vipLevelTexts[_loc10_].textWidth + 5;
            _vipLevelTexts[_loc10_].x = (_progressBars[_loc10_] as VIPProgressBar).x + (_progressBars[_loc10_] as VIPProgressBar).width - _vipLevelTexts[_loc10_].textWidth / 2;
            _vipLevelTexts[_loc10_].y = (_progressBars[_loc10_] as VIPProgressBar).y - _vipLevelTexts[_loc10_].textHeight;
            addChild(_vipLevelTexts[_loc10_]);
            _loc10_++;
         }
         _loc10_ = 0;
         while(_loc10_ < param4 - 1)
         {
            _pointTicketValueTexts.push(new TextField());
            _pointTicketValueTexts[_loc10_].defaultTextFormat = new TextFormat(_loc6_.fontName,11 + _loc10_,16776960);
            _pointTicketValueTexts[_loc10_].embedFonts = true;
            _pointTicketValueTexts[_loc10_].selectable = false;
            _pointTicketValueTexts[_loc10_].text = XMLSingle.getPointTicketValueByVipLevel(_loc10_ + 1) + "点券";
            _pointTicketValueTexts[_loc10_].width = _pointTicketValueTexts[_loc10_].textWidth + 5;
            _pointTicketValueTexts[_loc10_].x = (_progressBars[_loc10_] as VIPProgressBar).x + (_progressBars[_loc10_] as VIPProgressBar).width - _pointTicketValueTexts[_loc10_].textWidth / 2;
            _pointTicketValueTexts[_loc10_].y = (_progressBars[_loc10_] as VIPProgressBar).y + (_progressBars[_loc10_] as VIPProgressBar).height;
            addChild(_pointTicketValueTexts[_loc10_]);
            _loc10_++;
         }
      }
      
      public function setVIPProgress(param1:Number) : void
      {
         MyFunction.getInstance().setSegmentedBar(param1,_progressBars);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_vipLevelTexts)
         {
            _loc1_ = int(_vipLevelTexts.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _vipLevelTexts[_loc2_] = null;
               _loc2_++;
            }
         }
         if(_pointTicketValueTexts)
         {
            _loc1_ = int(_pointTicketValueTexts.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _pointTicketValueTexts[_loc2_] = null;
               _loc2_++;
            }
         }
         if(_progressBars)
         {
            _loc1_ = int(_progressBars.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               (_progressBars[_loc2_] as VIPProgressBar).clear();
               _progressBars[_loc2_] = null;
               _loc2_++;
            }
         }
         _vipLevelTexts = null;
         _pointTicketValueTexts = null;
         _progressBars = null;
      }
   }
}

