package org.bytearray.gif.decoder
{
   import flash.display.BitmapData;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   import org.bytearray.gif.errors.FileTypeError;
   import org.bytearray.gif.frames.GIFFrame;
   
   public class GIFDecoder
   {
      private static var STATUS_OK:int = 0;
      
      private static var STATUS_FORMAT_ERROR:int = 1;
      
      private static var STATUS_OPEN_ERROR:int = 2;
      
      private static var MaxStackSize:int = 4096;
      
      private static var frameRect:Rectangle = new Rectangle();
      
      private var inStream:ByteArray;
      
      private var status:int;
      
      private var width:int;
      
      private var height:int;
      
      private var gctFlag:Boolean;
      
      private var gctSize:int;
      
      private var loopCount:int = 1;
      
      private var gct:Array;
      
      private var lct:Array;
      
      private var act:Array;
      
      private var bgIndex:int;
      
      private var bgColor:int;
      
      private var lastBgColor:int;
      
      private var pixelAspect:int;
      
      private var lctFlag:Boolean;
      
      private var interlace:Boolean;
      
      private var lctSize:int;
      
      private var ix:int;
      
      private var iy:int;
      
      private var iw:int;
      
      private var ih:int;
      
      private var lastRect:Rectangle;
      
      private var image:BitmapData;
      
      private var bitmap:BitmapData;
      
      private var lastImage:BitmapData;
      
      private var block:ByteArray;
      
      private var blockSize:int = 0;
      
      private var dispose:int = 0;
      
      private var lastDispose:int = 0;
      
      private var transparency:Boolean = false;
      
      private var delay:int = 0;
      
      private var transIndex:int;
      
      private var prefix:Array;
      
      private var suffix:Array;
      
      private var pixelStack:Array;
      
      private var pixels:Array;
      
      private var frames:Array;
      
      private var frameCount:int;
      
      public function GIFDecoder()
      {
         super();
         block = new ByteArray();
      }
      
      public function get disposeValue() : int
      {
         return dispose;
      }
      
      public function getDelay(param1:int) : int
      {
         delay = -1;
         if(param1 >= 0 && param1 < frameCount)
         {
            delay = frames[param1].delay;
         }
         return delay;
      }
      
      public function getFrameCount() : int
      {
         return frameCount;
      }
      
      public function getImage() : GIFFrame
      {
         return getFrame(0);
      }
      
      public function getLoopCount() : int
      {
         return loopCount;
      }
      
      private function getPixels(param1:BitmapData) : Array
      {
         var _loc3_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:Array = new Array(4 * image.width * image.height);
         var _loc2_:int = 0;
         var _loc5_:int = image.width;
         var _loc7_:int = image.height;
         _loc8_ = 0;
         while(_loc8_ < _loc7_)
         {
            _loc4_ = 0;
            while(_loc4_ < _loc5_)
            {
               _loc3_ = int(param1.getPixel(_loc8_,_loc4_));
               _loc6_[_loc2_++] = (_loc3_ & 16711680) >> 16;
               _loc6_[_loc2_++] = (_loc3_ & 65280) >> 8;
               _loc6_[_loc2_++] = _loc3_ & 255;
               _loc4_++;
            }
            _loc8_++;
         }
         return _loc6_;
      }
      
      private function setPixels(param1:Array) : void
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         param1.position = 0;
         var _loc5_:int = image.width;
         var _loc6_:int = image.height;
         bitmap.lock();
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc4_ = 0;
            while(_loc4_ < _loc5_)
            {
               _loc3_ = int(param1[_loc2_++]);
               bitmap.setPixel32(_loc4_,_loc7_,_loc3_);
               _loc4_++;
            }
            _loc7_++;
         }
         bitmap.unlock();
      }
      
      private function transferPixels() : void
      {
         var _loc5_:int = 0;
         var _loc8_:Array = null;
         var _loc4_:Number = NaN;
         var _loc10_:int = 0;
         var _loc6_:* = 0;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc15_:int = 0;
         var _loc2_:* = 0;
         var _loc13_:int = 0;
         var _loc11_:Array = getPixels(bitmap);
         if(lastDispose > 0)
         {
            if(lastDispose == 3)
            {
               _loc5_ = frameCount - 2;
               lastImage = _loc5_ > 0 ? getFrame(_loc5_ - 1).bitmapData : null;
            }
            if(lastImage != null)
            {
               _loc8_ = getPixels(lastImage);
               _loc11_ = _loc8_.slice();
               if(lastDispose == 2)
               {
                  _loc4_ = transparency ? 0 : lastBgColor;
                  image.fillRect(lastRect,_loc4_);
               }
            }
         }
         var _loc9_:int = 1;
         var _loc12_:int = 8;
         var _loc14_:int = 0;
         _loc10_ = 0;
         while(_loc10_ < ih)
         {
            _loc6_ = _loc10_;
            if(interlace)
            {
               if(_loc14_ >= ih)
               {
                  _loc9_++;
                  switch(_loc9_ - 2)
                  {
                     case 0:
                        _loc14_ = 4;
                        break;
                     case 1:
                        _loc14_ = 2;
                        _loc12_ = 4;
                        break;
                     case 2:
                        _loc14_ = 1;
                        _loc12_ = 2;
                  }
               }
               _loc6_ = _loc14_;
               _loc14_ += _loc12_;
            }
            _loc6_ += iy;
            if(_loc6_ < height)
            {
               _loc7_ = _loc6_ * width;
               _loc3_ = _loc7_ + ix;
               _loc1_ = _loc3_ + iw;
               if(_loc7_ + width < _loc1_)
               {
                  _loc1_ = _loc7_ + width;
               }
               _loc15_ = _loc10_ * iw;
               while(_loc3_ < _loc1_)
               {
                  _loc2_ = pixels[_loc15_++] & 255;
                  _loc13_ = int(act[_loc2_]);
                  if(_loc13_ != 0)
                  {
                     _loc11_[_loc3_] = _loc13_;
                  }
                  _loc3_++;
               }
            }
            _loc10_++;
         }
         setPixels(_loc11_);
      }
      
      public function getFrame(param1:int) : GIFFrame
      {
         var _loc2_:GIFFrame = null;
         if(param1 >= 0 && param1 < frameCount)
         {
            return frames[param1];
         }
         throw new RangeError("Wrong frame number passed");
      }
      
      public function getFrameSize() : Rectangle
      {
         var _loc1_:Rectangle = GIFDecoder.frameRect;
         _loc1_.y = 0;
         _loc1_.x = 0;
         _loc1_.width = width;
         _loc1_.height = height;
         return _loc1_;
      }
      
      public function read(param1:ByteArray) : int
      {
         init();
         if(param1 != null)
         {
            this.inStream = param1;
            readHeader();
            if(!hasError())
            {
               readContents();
               if(frameCount < 0)
               {
                  status = STATUS_FORMAT_ERROR;
               }
            }
         }
         else
         {
            status = STATUS_OPEN_ERROR;
         }
         return status;
      }
      
      private function decodeImageData() : void
      {
         var _loc4_:int = 0;
         var _loc12_:* = 0;
         var _loc13_:int = 0;
         var _loc5_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:* = 0;
         var _loc8_:* = 0;
         var _loc14_:int = 0;
         var _loc6_:* = 0;
         var _loc2_:int = 0;
         var _loc11_:* = 0;
         var _loc7_:* = 0;
         var _loc3_:int = 0;
         var _loc17_:* = 0;
         var _loc18_:int = 0;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc19_:int = iw * ih;
         if(pixels == null || pixels.length < _loc19_)
         {
            pixels = new Array(_loc19_);
         }
         if(prefix == null)
         {
            prefix = new Array(MaxStackSize);
         }
         if(suffix == null)
         {
            suffix = new Array(MaxStackSize);
         }
         if(pixelStack == null)
         {
            pixelStack = new Array(MaxStackSize + 1);
         }
         _loc3_ = readSingleByte();
         _loc12_ = 1 << _loc3_;
         _loc9_ = (1 << _loc3_) + 1;
         _loc4_ = _loc12_ + 2;
         _loc8_ = -1;
         _loc5_ = _loc3_ + 1;
         _loc13_ = (1 << _loc5_) - 1;
         _loc6_ = 0;
         while(_loc6_ < _loc12_)
         {
            prefix[_loc6_] = 0;
            suffix[_loc6_] = _loc6_;
            _loc6_++;
         }
         _loc15_ = 0;
         _loc16_ = 0;
         _loc18_ = 0;
         _loc17_ = 0;
         _loc2_ = 0;
         _loc14_ = 0;
         _loc7_ = 0;
         _loc11_ = 0;
         loop1:
         while(true)
         {
            if(_loc11_ < _loc19_)
            {
               while(true)
               {
                  if(_loc18_ == 0)
                  {
                     if(_loc14_ < _loc5_)
                     {
                        if(_loc2_ == 0)
                        {
                           _loc2_ = readBlock();
                           if(_loc2_ <= 0)
                           {
                              break;
                           }
                           _loc15_ = 0;
                        }
                        _loc7_ += (int(block[_loc15_]) & 255) << _loc14_;
                        _loc14_ += 8;
                        _loc15_++;
                        _loc2_--;
                        continue;
                     }
                     _loc6_ = _loc7_ & _loc13_;
                     _loc7_ >>= _loc5_;
                     _loc14_ -= _loc5_;
                     if(_loc6_ > _loc4_ || _loc6_ == _loc9_)
                     {
                        break;
                     }
                     if(_loc6_ == _loc12_)
                     {
                        _loc5_ = _loc3_ + 1;
                        _loc13_ = (1 << _loc5_) - 1;
                        _loc4_ = _loc12_ + 2;
                        _loc8_ = -1;
                        continue;
                     }
                     if(_loc8_ == -1)
                     {
                        pixelStack[_loc18_++] = suffix[_loc6_];
                        _loc8_ = _loc6_;
                        _loc17_ = _loc6_;
                        continue;
                     }
                     _loc10_ = _loc6_;
                     if(_loc6_ == _loc4_)
                     {
                        pixelStack[_loc18_++] = _loc17_;
                        _loc6_ = _loc8_;
                     }
                     while(_loc6_ > _loc12_)
                     {
                        pixelStack[_loc18_++] = suffix[_loc6_];
                        _loc6_ = int(prefix[_loc6_]);
                     }
                     _loc17_ = suffix[_loc6_] & 255;
                     if(_loc4_ >= MaxStackSize)
                     {
                        break;
                     }
                     pixelStack[_loc18_++] = _loc17_;
                     prefix[_loc4_] = _loc8_;
                     suffix[_loc4_] = _loc17_;
                     _loc4_++;
                     if((_loc4_ & _loc13_) == 0 && _loc4_ < MaxStackSize)
                     {
                        _loc5_++;
                        _loc13_ += _loc4_;
                     }
                     _loc8_ = _loc10_;
                  }
                  continue loop1;
               }
            }
            _loc11_ = _loc16_;
            while(_loc11_ < _loc19_)
            {
               pixels[_loc11_] = 0;
               _loc11_++;
            }
            return;
            _loc18_--;
            pixels[_loc16_++] = pixelStack[_loc18_];
            _loc11_++;
         }
      }
      
      private function hasError() : Boolean
      {
         return status != STATUS_OK;
      }
      
      private function init() : void
      {
         status = STATUS_OK;
         frameCount = 0;
         frames = [];
         gct = null;
         lct = null;
      }
      
      private function readSingleByte() : int
      {
         var _loc1_:int = 0;
         try
         {
            _loc1_ = int(inStream.readUnsignedByte());
         }
         catch(e:Error)
         {
            status = STATUS_FORMAT_ERROR;
         }
         return _loc1_;
      }
      
      private function readBlock() : int
      {
         var _loc1_:int = 0;
         blockSize = readSingleByte();
         var _loc2_:int = 0;
         if(blockSize > 0)
         {
            try
            {
               _loc1_ = 0;
               while(_loc2_ < blockSize)
               {
                  inStream.readBytes(block,_loc2_,blockSize - _loc2_);
                  if(blockSize - _loc2_ == -1)
                  {
                     break;
                  }
                  _loc2_ += blockSize - _loc2_;
               }
            }
            catch(e:Error)
            {
            }
            if(_loc2_ < blockSize)
            {
               status = STATUS_FORMAT_ERROR;
            }
         }
         return _loc2_;
      }
      
      private function readColorTable(param1:int) : Array
      {
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc4_:* = 0;
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         var _loc6_:int = 3 * param1;
         var _loc8_:Array = null;
         var _loc5_:ByteArray = new ByteArray();
         var _loc7_:* = 0;
         try
         {
            inStream.readBytes(_loc5_,0,_loc6_);
            _loc7_ = _loc6_;
         }
         catch(e:Error)
         {
         }
         if(_loc7_ < _loc6_)
         {
            status = STATUS_FORMAT_ERROR;
         }
         else
         {
            _loc8_ = new Array(256);
            _loc10_ = 0;
            _loc9_ = 0;
            while(_loc10_ < param1)
            {
               _loc4_ = _loc5_[_loc9_++] & 255;
               _loc2_ = _loc5_[_loc9_++] & 255;
               _loc3_ = _loc5_[_loc9_++] & 255;
               _loc8_[_loc10_++] = 4278190080 | _loc4_ << 16 | _loc2_ << 8 | _loc3_;
            }
         }
         return _loc8_;
      }
      
      private function readContents() : void
      {
         var _loc3_:int = 0;
         var _loc1_:String = null;
         var _loc4_:int = 0;
         var _loc2_:Boolean = false;
         while(!(_loc2_ || hasError()))
         {
            _loc3_ = readSingleByte();
            switch(_loc3_)
            {
               case 44:
                  readImage();
                  break;
               case 33:
                  _loc3_ = readSingleByte();
                  switch(_loc3_ - 249)
                  {
                     case 0:
                        readGraphicControlExt();
                        continue;
                     case 6:
                        readBlock();
                        _loc1_ = "";
                        _loc4_ = 0;
                        while(_loc4_ < 11)
                        {
                           _loc1_ += block[_loc4_];
                           _loc4_++;
                        }
                        if(_loc1_ == "NETSCAPE2.0")
                        {
                           readNetscapeExt();
                        }
                        else
                        {
                           skip();
                        }
                        continue;
                     default:
                        skip();
                        continue;
                  }
                  break;
               case 59:
                  _loc2_ = true;
                  break;
               case 0:
                  break;
               default:
                  status = STATUS_FORMAT_ERROR;
                  break;
            }
         }
      }
      
      private function readGraphicControlExt() : void
      {
         readSingleByte();
         var _loc1_:int = readSingleByte();
         dispose = (_loc1_ & 28) >> 2;
         if(dispose == 0)
         {
            dispose = 1;
         }
         transparency = (_loc1_ & 1) != 0;
         delay = readShort() * 10;
         transIndex = readSingleByte();
         readSingleByte();
      }
      
      private function readHeader() : void
      {
         var _loc2_:int = 0;
         var _loc1_:String = "";
         _loc2_ = 0;
         while(_loc2_ < 6)
         {
            _loc1_ += String.fromCharCode(readSingleByte());
            _loc2_++;
         }
         if(_loc1_.indexOf("GIF") != 0)
         {
            status = STATUS_FORMAT_ERROR;
            throw new FileTypeError("Invalid file type");
         }
         readLSD();
         if(gctFlag && !hasError())
         {
            gct = readColorTable(gctSize);
            bgColor = gct[bgIndex];
         }
      }
      
      private function readImage() : void
      {
         ix = readShort();
         iy = readShort();
         iw = readShort();
         ih = readShort();
         var _loc2_:int = readSingleByte();
         lctFlag = (_loc2_ & 128) != 0;
         interlace = (_loc2_ & 64) != 0;
         lctSize = 2 << (_loc2_ & 7);
         if(lctFlag)
         {
            lct = readColorTable(lctSize);
            act = lct;
         }
         else
         {
            act = gct;
            if(bgIndex == transIndex)
            {
               bgColor = 0;
            }
         }
         var _loc1_:int = 0;
         if(transparency)
         {
            _loc1_ = int(act[transIndex]);
            act[transIndex] = 0;
         }
         if(act == null)
         {
            status = STATUS_FORMAT_ERROR;
         }
         if(hasError())
         {
            return;
         }
         decodeImageData();
         skip();
         if(hasError())
         {
            return;
         }
         frameCount++;
         bitmap = new BitmapData(width,height);
         image = bitmap;
         transferPixels();
         frames.push(new GIFFrame(bitmap,delay));
         if(transparency)
         {
            act[transIndex] = _loc1_;
         }
         resetFrame();
      }
      
      private function readLSD() : void
      {
         width = readShort();
         height = readShort();
         var _loc1_:int = readSingleByte();
         gctFlag = (_loc1_ & 128) != 0;
         gctSize = 2 << (_loc1_ & 7);
         bgIndex = readSingleByte();
         pixelAspect = readSingleByte();
      }
      
      private function readNetscapeExt() : void
      {
         var _loc1_:* = 0;
         var _loc2_:* = 0;
         do
         {
            readBlock();
            if(block[0] == 1)
            {
               _loc1_ = block[1] & 255;
               _loc2_ = block[2] & 255;
               loopCount = _loc2_ << 8 | _loc1_;
            }
         }
         while(blockSize > 0 && !hasError());
         
      }
      
      private function readShort() : int
      {
         return readSingleByte() | readSingleByte() << 8;
      }
      
      private function resetFrame() : void
      {
         lastDispose = dispose;
         lastRect = new Rectangle(ix,iy,iw,ih);
         lastImage = image;
         lastBgColor = bgColor;
         lct = null;
      }
      
      private function skip() : void
      {
         do
         {
            readBlock();
         }
         while(blockSize > 0 && !hasError());
         
      }
   }
}

