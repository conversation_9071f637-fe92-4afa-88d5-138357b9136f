package UI.Event
{
   import flash.events.Event;
   
   public class UIBtnEvent extends Event
   {
      public static const CLICK_A_KEY_SOLD_BTN:String = "clickAKeySoldBtn";
      
      public static const CLICK_TIDY_BTN:String = "clickTidyBtn";
      
      public static const CLICK_BTN:String = "clickBtn";
      
      public static const SWITCH_PANEL:String = "switchPanel";
      
      public static const SWITCH_TO_PACKAGE:String = "switchToPackage";
      
      public static const SWITCH_TO_SHOP:String = "switchToShop";
      
      public static const SWITCH_TO_STORAGE:String = "switchToStorage";
      
      public static const SWITCH_TO_SKILL:String = "switchToSkill";
      
      public static const SWITCH_TO_TASK:String = "switchToTask";
      
      public static const SWITCH_TO_MONKEY:String = "switchToMonkey";
      
      public static const SWITCH_TO_DRAGON:String = "switchToDragon";
      
      public static const SWITCH_TO_ERLANGSHEN:String = "switchToErLangShen";
      
      public static const SWITCH_TO_CHANGE:String = "switchToChangE";
      
      public static const SWITCH_TO_FOX:String = "switchToFox";
      
      public static const SWITCH_TO_TIESHAN:String = "switchToTieShan";
      
      public static const SWITCH_TO_HOUYI:String = "switchToHouyi";
      
      public static const SWITCH_TO_ZIXIA:String = "switchToZiXia";
      
      public static const SWITCH_TO_ACTIVESKILL:String = "switchToActiveSkill";
      
      public static const SWITCH_TO_PASSIVESKILL:String = "switchToPassiveSkill";
      
      public static const CLICK_PAGE_BTN:String = "clickPageBtn";
      
      public static const PAGE_UP:String = "pageUp";
      
      public static const PAGE_DOWN:String = "pageDown";
      
      public static const CLICK_SUREANDCANCEL_BTN:String = "clickSureAndCancelBtn";
      
      public static const CLICK_QUIT_BTN:String = "clickQuitBtn";
      
      public static const CLICK_NUMBER_BTN:String = "clickNumberBtn";
      
      public static const CLICK_NUMBER_BTNEX:String = "clickNumberBtnEx";
      
      public static const CLICK_EQUIP_BTN:String = "clickEquipBtn";
      
      public static const CLICK_SELL_BTN:String = "clickSellBtn";
      
      public static const CLICK_SAVEE_QUIPMENT_BTN:String = "clickSaveEquipmentBtn";
      
      public static const CLICK_PUT_IN_BTN:String = "clickPutInBtn";
      
      public static const CLICK_TAKE_DOWN_BTN:String = "clickTakeDownBtn";
      
      public static const CLICK_SEND_BTN:String = "clickSendBtn";
      
      public static const CLICK_TAKE_OUT_BTN:String = "clickTakeOutBtn";
      
      public static const CLICK_FAST_USE_BTN:String = "clickFastUseBtn";
      
      public static const CLICK_DETAIL_BTN:String = "clickDetailBtn";
      
      public static const CLICK_UPGRADE_BTN:String = "clickUpgradeBtn";
      
      public static const ENTER_INTO_PACKAGE:String = "enterIntoPackage";
      
      public static const ENTER_INTO_PET:String = "enterIntoPet";
      
      public static const ENTER_INTO_SYSTEM:String = "enterIntoSystem";
      
      public static const ENTER_INTO_SHOPWALL:String = "enterIntoShop";
      
      public static const ENTER_INTO_PROTECT:String = "enterIntoProtect";
      
      public static const ENTER_INTO_SIGN_PANEL:String = "enterIntoSignPanel";
      
      public static const ENTER_INTO_EXCHAGE_PANEL:String = "enterIntoExchangePanel";
      
      public static const ENTER_INTO_CODEKEY_PANEL:String = "enterIntoCodekeyPanel";
      
      public static const ENTER_INTO_BBS_PANEL:String = "enterIntoBBSPanel";
      
      public static const ENTER_INTO_ZHOUNIAN_PANEL:String = "enterIntoZhouNianPanel";
      
      public static const ENTER_INTO_FARM:String = " enterIntoFarm";
      
      public static const ENTER_INTO_RECAPTUREGOLD:String = "enterIntoRecaptureGold";
      
      public static const ENTER_INFO_HUANLEZP:String = "enterinfohuanlezp";
      
      public static const ENTER_INFO_MIDAUTUMN:String = "enterinfomidautumn";
      
      public static const ENTER_INFO_DOUBLEEGG:String = "enterinfodoubleegg";
      
      public static const ENTER_INFO_JHSBP:String = "enterinfojhsbp";
      
      public static const ENTER_INFO_ZHUOQUGIFT:String = "enterinfozhuoqugift";
      
      public static const ENTER_INFO_REALNAME:String = "enterinforealname";
      
      public static const CLICK_TAKE_BACK_PET_BTN:String = "clickTakeBackPetBtn";
      
      public static const SWITCH_TO_PET_INFORMATION:String = "switchToPetInformation";
      
      public static const SWITCH_TO_PET_SKILL:String = "switchToPetSkill";
      
      public static const CLICK_PLAYER_BTN:String = "clickPlayerBtn";
      
      public static const CLICK_PET_BTN:String = "clickPetBtn";
      
      public static const CLICK_TUDI_BTN:String = "clickTuDiBtn";
      
      public static const CLICK_AUTO_PET_BTN:String = "clickAutoPetBtn";
      
      public static const CLICK_MOUNT_BTN:String = "clickMountBtn";
      
      public static const CLICK_SHOP_SWITCH_BTN:String = "clickShopSwitchBtn";
      
      public static const CLICK_SHOPWall_SWITCH_BTN:String = "clickShopWallSwitchBtn";
      
      public static const SWITCH_TO_MAKE_PANEL:String = "switchToMakePanel";
      
      public static const SWITCH_TO_UPGRADE_PANEL:String = "switchToUpgradePanel";
      
      public static const CLICK_MAKE_EQ_BTN:String = "clickMakeEquipmentBtn";
      
      public static const CLICK_UPGRADE_EQ_BTN:String = "clickUpgradeEquipmentBtn";
      
      public static const CLICK_GET_GIFT_BTN:String = "clickGetGiftBtn";
      
      public static const CLICK_VIP_RECHARGE_BTN:String = "clickviprechargeBtn";
      
      public static const CLICK_RECHARGE_BTN:String = "clickRechargeBtn";
      
      public static const CLICK_LOCK_CELL:String = "clickLockCell";
      
      public static const CLICK_ACCEPT_TASK_BTN:String = "clickAcceptTaskBtn";
      
      public static const CLICK_COMPLETE_TASK_BTN:String = "clickCompleteTaskBtn";
      
      public static const CLICK_GIVE_UP_TASK_BTN:String = "clickGiveUpTaskBtn";
      
      public static const SWITCH_TASK_COLUME:String = "clickTaskColume";
      
      public static const SWITCH_TO_EVERY_DAY_TASK:String = "switchEveryDayTask";
      
      public static const SWITCH_TO_ACTIVITY_TASK:String = "switchToActivityTask";
      
      public static const CLICK_START_MIRAGE_BTN:String = "clickStartMirageBtn";
      
      public static const CLICK_GOTOVIP_MIRAGE_BTN:String = "clickGotoVipMirageBtn";
      
      public static const CLICK_PROTECT_MIRAGE_BTN:String = "clickGotoProtectMirageBtn";
      
      public static const SWITCH_PLAYER_IN_SMALL_PACKAGE:String = "switchPlayerInSmallPackage";
      
      public static const SWITCH_SHOP:String = "switchShop";
      
      public static const SWITCH_RANKLIST_COLUME:String = "switchRankListColume";
      
      public static const CLICK_LOOK_UP_BTN:String = "clickLookUpBtn";
      
      public static const CLICK_RESET_TASK_BTN:String = "clickResetTaskBtn";
      
      public static const SWITCH_MIRAGEPANEL_ICON_BTN:String = "switchMiragePanelIconBtn";
      
      public static const CLICK_REFINE_FACTORY_BTN:String = "clickRefineFactoryBtn";
      
      public static const CLICK_OPEN_ALL_BTN:String = "clickOpenAllBtn";
      
      public static const SWITCH_REFINE_FACTORY_COLUME:String = "switchRefineFactoryColume";
      
      public static const SWITCH_TO_REFINE_ATTACK_DAN:String = "switchToRefineAttackDan";
      
      public static const SWITCH_TO_REFINE_DEFENCE_DAN:String = "switchToRefineDefenceDan";
      
      public static const CLICK_START_REFINE_BTN:String = "clickStartRefineBtn";
      
      public static const CLICK_PUT_ALL_BTN:String = "clickPutAllBtn";
      
      public static const CLICK_COMPLETE_REFINE_AT_ONCE_BTN:String = "clickCompleteRefineAtOnceBtn";
      
      public static const CLICK_GET_PROTECT_BTN:String = "clickGetProtectBtn";
      
      public static const CLICk_GET_PROTECT_GIFTS_BTN:String = "clickGetProtectGiftBtn";
      
      public static const SWITCH_BUY_PROTECT_SELECT_SLOT:String = "switchBuyProtectSelectSlot";
      
      public static const SWITCH_TO_ATTACK_DAN:String = "switchToAttackDan";
      
      public static const SWITCH_TO_DEFENCE_DAN:String = "switchToDefenceDan";
      
      public static const SWITCH_TO_TUDI:String = "switchToTuDi";
      
      public static const SWITCH_TO_SHIFU:String = "switchToShiFu";
      
      public static const CLICK_LOOK_PLAYER_ATTRIBUTE_BTN:String = "clickLookPlayerAttributeBtn";
      
      public static const CLICk_LOOK_DAN_MEDICINES_BTN:String = "clickLookDanMedicinesBtn";
      
      public static const CLICK_EXCHANGE_GIFT_BAG_BTN:String = "clickExchangeGiftBagBtn";
      
      public static const CLICK_EXCHANGE_NUMBER_BTN:String = "clickExchangeNumberBtn";
      
      public static const SWITCH_EXCHANGE_GIFT_BAG_PANEL:String = "switchExchangeGiftBagPanel";
      
      public static const CLICK_GOTO_GROUP_BTN:String = "clickGotoGroupBtn";
      
      public static const CLICK_GET_SIGN_AWARD_BTN:String = "clickGetSignAwardBtn";
      
      public static const SWITCH_TO_PK_HONOUR_PANEL:String = "switchToPKHonourPanel";
      
      public static const CLICK_GET_HONOUR_AWARD_BTN:String = "clickGetHonourAwardBtn";
      
      public static const SWITCH_PLAYER_IN_PK_PANEL_ONE:String = "switchPlayerInPKPanelOne";
      
      public static const CLICK_LOOK_PK_HONOUR_PANEL_BTN:String = "clickLookPkHonourPanelBtn";
      
      public static const CLICK_BUY_NUM_BTN:String = "clickBuyNumBtn";
      
      public var data:*;
      
      public function UIBtnEvent(param1:String, param2:* = null, param3:Boolean = false, param4:Boolean = false)
      {
         this.data = param2;
         super(param1,param3,param4);
         trace(formatToString("UIBtnEvent","type","bubbles","cancelable","data"));
      }
      
      override public function clone() : Event
      {
         return new UIBtnEvent(type,data,bubbles,cancelable);
      }
   }
}

