package UI.PKUI
{
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class PKPanel extends MySprite
   {
      private static var _instance:PKPanel = null;
      
      public var myPanel:PKPanelOne;
      
      public var goldPanel:PKPanelOne;
      
      public var isTwoMode:Boolean;
      
      private var _myPlayers:Vector.<Player>;
      
      private var _goldPlayers:Vector.<Player>;
      
      public function PKPanel()
      {
         if(!_instance)
         {
            super();
            init();
            addEventListener("addedToStage",addToStage,false,0,true);
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了么？！");
      }
      
      public static function getInstance() : PKPanel
      {
         if(!_instance)
         {
            _instance = new PKPanel();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(myPanel)
         {
            myPanel.clear();
         }
         if(goldPanel)
         {
            goldPanel.clear();
         }
         myPanel = null;
         goldPanel = null;
      }
      
      public function showMyPanel() : void
      {
         if(!myPanel)
         {
            myPanel = new PKPanelOne();
            myPanel.isTwoMode = isTwoMode;
            myPanel.initPKPanel(_myPlayers,PlayerDataForPK.getInstance().myNicknameData);
            myPanel.x = -100;
            myPanel.y = 30;
         }
         if(!getChildByName(myPanel.name))
         {
            addChild(myPanel);
         }
      }
      
      public function hideMyPanel() : void
      {
         if(myPanel)
         {
            if(getChildByName(myPanel.name))
            {
               removeChild(myPanel);
            }
            myPanel.clear();
            myPanel = null;
         }
      }
      
      public function showGoldPanel() : void
      {
         if(!goldPanel)
         {
            goldPanel = new PKPanelOne();
            goldPanel.isTwoMode = isTwoMode;
            goldPanel.initPKPanel(_goldPlayers,PlayerDataForPK.getInstance().goldNicknameData);
            goldPanel.x = 400;
            goldPanel.y = 30;
         }
         if(!getChildByName(goldPanel.name))
         {
            addChild(goldPanel);
         }
      }
      
      public function hideGoldPanel() : void
      {
         if(goldPanel)
         {
            if(getChildByName(goldPanel.name))
            {
               removeChild(goldPanel);
            }
            goldPanel.clear();
            goldPanel = null;
         }
      }
      
      public function initMyPnale(param1:Vector.<Player>, param2:DisplayObject, param3:DisplayObject) : void
      {
         _myPlayers = param1;
      }
      
      public function initGoldPanel(param1:Vector.<Player>, param2:DisplayObject, param3:DisplayObject) : void
      {
         _goldPlayers = param1;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickQuitBtn",quit,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickQuitBtn",quit,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         if(MyFunction2.judgeParentsIsTheObject(param1.target as DisplayObject,myPanel,GamingUI.getInstance()))
         {
            MessageBoxEngine.getInstance().showMessageBox(param1,!!myPanel ? myPanel.currentPlayer : null);
         }
         else if(MyFunction2.judgeParentsIsTheObject(param1.target as DisplayObject,goldPanel,GamingUI.getInstance()))
         {
            MessageBoxEngine.getInstance().showMessageBox(param1,!!goldPanel ? goldPanel.currentPlayer : null);
         }
         else
         {
            MessageBoxEngine.getInstance().showMessageBox(param1,!!param1.data ? param1.data.player : null);
         }
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         if(param1.target.parent == myPanel)
         {
            hideMyPanel();
         }
         else if(param1.target.parent == goldPanel)
         {
            hideGoldPanel();
         }
      }
      
      private function init() : void
      {
      }
   }
}

