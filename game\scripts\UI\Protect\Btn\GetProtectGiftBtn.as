package UI.Protect.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GetProtectGiftBtn extends Btn
   {
      public function GetProtectGiftBtn()
      {
         super();
         setTipString("点击获取保佑礼包");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGetProtectGiftBtn"));
      }
   }
}

