package UI.Players.ImplicitProPlayer
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class ImplicitProPlayerFactory
   {
      private var m_classes:Array;
      
      public function ImplicitProPlayerFactory()
      {
         super();
         m_classes = [ImplicitProPlayer,SuitOfAbleEquipmentNum];
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_classes);
         m_classes = null;
      }
      
      public function getAndInitOneImplicitProPlayer(param1:XML) : ImplicitProPlayer
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1.@className) as Class;
         var _loc3_:ImplicitProPlayer = new _loc2_();
         _loc3_.initByXML(param1);
         return _loc3_;
      }
   }
}

