package UI2.SVActivity.Data
{
   import YJFY.Utils.ClearUtil;
   
   public class SVActivityData
   {
      private var m_chengZhangJiHuaData:ChengZhangJiHuaData;
      
      private var m_jiLeiHaoLiData:JiLeiHaoLiData;
      
      private var m_xinJueSeLiBao:ChengZhangJiHuaData;
      
      private var m_zhengdianmiaosha:ZhengdianmiaoshaData;
      
      private var m_jinhousonli:JinHouSonLiData;
      
      public function SVActivityData()
      {
         super();
         m_chengZhangJiHuaData = new ChengZhangJiHuaData();
         m_jiLeiHaoLiData = new JiLeiHaoLiData();
         m_xinJueSeLiBao = new ChengZhangJiHuaData();
         m_zhengdianmiaosha = new ZhengdianmiaoshaData();
         m_jinhousonli = new JinHouSonLiData();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_chengZhangJiHuaData);
         m_chengZhangJiHuaData = null;
         ClearUtil.clearObject(m_jiLeiHaoLiData);
         m_jiLeiHaoLiData = null;
         ClearUtil.clearObject(m_xinJueSeLiBao);
         m_xinJueSeLiBao = null;
         ClearUtil.clearObject(m_zhengdianmiaosha);
         m_zhengdianmiaosha = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         m_chengZhangJiHuaData.initByXML(param1.chengZhangJiHua[0]);
         m_jiLeiHaoLiData.initByXML(param1.chongZhiFanLi[0]);
         m_xinJueSeLiBao.initByXML(param1.xinJueSeLiBao[0]);
         m_zhengdianmiaosha.initByXML(param1.zhengdianmiaosha[0]);
         m_jinhousonli.initByXML(param1.jinHouSonLi[0]);
      }
      
      public function getChengZhangJiHuaData() : ChengZhangJiHuaData
      {
         return m_chengZhangJiHuaData;
      }
      
      public function getJiLeiHaoLiData() : JiLeiHaoLiData
      {
         return m_jiLeiHaoLiData;
      }
      
      public function getXinJueSeLiBao() : ChengZhangJiHuaData
      {
         return m_xinJueSeLiBao;
      }
      
      public function getZhengdianmiaosha() : ZhengdianmiaoshaData
      {
         return m_zhengdianmiaosha;
      }
      
      public function getJinHouSonLi() : JinHouSonLiData
      {
         return m_jinhousonli;
      }
   }
}

