package UI.EquipmentDetailShow
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EggEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class EggDetailShow extends EquipmentDetailShow
   {
      private var m_eggCell:Sprite;
      
      private var m_petCell:Sprite;
      
      private var m_egg:Equipment;
      
      private var m_pet:Equipment;
      
      private var m_eggNotKnowIcon:Sprite;
      
      public function EggDetailShow()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_eqCell = null;
         m_petCell = null;
         ClearUtil.clearObject(m_egg);
         m_egg = null;
         ClearUtil.clearDisplayObjectInContainer(m_eggNotKnowIcon);
         if(m_eggNotKnowIcon)
         {
            if(m_eggNotKnowIcon.parent)
            {
               m_eggNotKnowIcon.parent.removeChild(m_eggNotKnowIcon);
            }
         }
         m_eggNotKnowIcon = null;
      }
      
      override protected function initShow() : void
      {
         var _loc1_:Sprite = null;
         super.initShow();
         m_showMC.gotoAndStop("egg");
         m_eggCell = m_show["eggCell"];
         m_petCell = m_show["petCell"];
         m_egg = MyFunction2.sheatheEquipmentShell(m_equipmentVO);
         m_egg.addEventListener("rollOver",onOverForNormal,false,0,true);
         m_egg.addEventListener("rollOut",onOutForNormal,false,0,true);
         m_eggCell.addChild(m_egg);
         var _loc2_:Vector.<EquipmentVO> = (m_equipmentVO as EggEquipmentVO).getTargetPetVOs();
         if(_loc2_.length == 1)
         {
            if(_loc2_[0] as PetEquipmentVO)
            {
               ClearUtil.clearObject((_loc2_[0] as PetEquipmentVO).passiveSkillVOs);
               (_loc2_[0] as PetEquipmentVO).passiveSkillVOs = null;
               ClearUtil.clearObject((_loc2_[0] as PetEquipmentVO).talentVO);
               (_loc2_[0] as PetEquipmentVO).talentVO = null;
            }
            m_pet = MyFunction2.sheatheEquipmentShell(_loc2_[0]);
            _loc2_[0] = null;
            ClearUtil.nullArr(_loc2_);
            _loc2_ = null;
            m_pet.addEventListener("rollOver",onOverForShop,false,0,true);
            m_pet.addEventListener("rollOut",onOutForShop,false,0,true);
            m_petCell.addChild(m_pet);
         }
         else if(_loc2_.length > 1)
         {
            _loc1_ = MyFunction2.returnShowByClassName("EggNotKnowIcon") as Sprite;
            m_petCell.addChild(_loc1_);
         }
      }
   }
}

