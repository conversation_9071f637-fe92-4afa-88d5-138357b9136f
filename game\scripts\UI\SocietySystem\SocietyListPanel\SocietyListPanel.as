package UI.SocietySystem.SocietyListPanel
{
   import UI.DirtyWordFilter.DirtyWordFilterEngine;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.SocietySystem.MemberListPanel.MemberListPanel;
   import UI.SocietySystem.SeverLink.InformationBody;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_ApplyJoinReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_CancelApplyJoinReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_CreateSocietySuccess;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_IsSuccessJoin;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyListOfApplyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.SocietyDataInList;
   import UI.SocietySystem.SeverLink.SocketListener;
   import UI.SocietySystem.SeverLink.SocketManager;
   import UI.SocietySystem.SocietySystem;
   import UI.VersionControl;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   
   public class SocietyListPanel extends MySprite
   {
      public static const CREATE_SOCIETY:String = "createSociety";
      
      public static const APPLY_JOIN_SOCIETY:String = "applyJoinSociety";
      
      public static const LOOK_UP_SOCIETY_LIST:String = "lookUpSocietyList";
      
      private var m_columeNumOnePage:int;
      
      private var m_clear:ClearHelper;
      
      private var m_type:String;
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_socketListener:SocketListener;
      
      private var m_societyColumes:Vector.<SocietyColume>;
      
      private var m_groupWebBtn:ButtonLogicShell2;
      
      private var m_createSocietyBtn:ButtonLogicShell2;
      
      private var m_mySocietyBtn:ButtonLogicShell2;
      
      private var m_pageGroupBtn:PageBtnGroupLogicShell;
      
      private var m_createSocietyPanel:CreateSocietyPanel;
      
      private var m_memberListPanel:MemberListPanel;
      
      private var m_memberListPanelShow:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_playerDatas:PlayerDatas;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_dirtyWordFilterEngine:DirtyWordFilterEngine;
      
      private var m_gamingUI:GamingUI;
      
      private var m_societySystemXML:XML;
      
      private var m_versionControl:VersionControl;
      
      private var m_socketManager:SocketManager;
      
      private var m_societySystem:SocietySystem;
      
      private var m_memberDataList:DOWN_TheSocietyMemberList;
      
      private var m_societyListDetail:DOWN_TheSocietyList;
      
      private var m_pageIndex_gettingSocietyList:int;
      
      private var m_num_gettingSocietyList:int;
      
      private var m_pageIndex_gettingMemberList:int;
      
      private var m_num_gettingMemberList:int;
      
      private var m_societyId_gettingMemberList:int;
      
      private var m_societyListOfApply:DOWN_TheSocietyListOfApplyReturn;
      
      private var m_moneyOfPlayer1_createSociety:int;
      
      private var m_moneyOfPlayer2_createSociety:int;
      
      public function SocietyListPanel()
      {
         super();
         m_clear = new ClearHelper();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_socketListener);
         m_socketListener = null;
         ClearUtil.nullArr(m_societyColumes);
         m_societyColumes = null;
         ClearUtil.clearObject(m_groupWebBtn);
         m_groupWebBtn = null;
         ClearUtil.clearObject(m_createSocietyBtn);
         m_createSocietyBtn = null;
         ClearUtil.clearObject(m_mySocietyBtn);
         m_mySocietyBtn = null;
         ClearUtil.clearObject(m_pageGroupBtn);
         m_pageGroupBtn = null;
         ClearUtil.clearObject(m_createSocietyPanel);
         m_createSocietyPanel = null;
         ClearUtil.clearObject(m_memberListPanel);
         m_memberListPanel = null;
         ClearUtil.clearDisplayObjectInContainer(m_memberListPanelShow);
         m_memberListPanelShow = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.clearObject(m_playerDatas);
         m_playerDatas = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.clearObject(m_players);
         m_players = null;
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         ClearUtil.clearObject(m_dirtyWordFilterEngine);
         m_dirtyWordFilterEngine = null;
         m_memberDataList = null;
         m_societyListDetail = null;
         m_gamingUI = null;
         m_societySystemXML = null;
         m_versionControl = null;
         m_socketManager = null;
         m_societySystem = null;
         m_societyListOfApply = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setSocketManager(param1:SocketManager) : void
      {
         if(Boolean(m_socketManager) && m_socketListener)
         {
            m_socketManager.removeSocketListener(m_socketListener);
         }
         m_socketManager = param1;
         if(m_socketListener)
         {
            m_socketManager.addSocketListener(m_socketListener);
         }
      }
      
      public function init(param1:String) : void
      {
         m_myLoader = new YJFYLoader();
         m_loadUI = Part1.getInstance().getLoadUI();
         stage.addChild(m_loadUI);
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         if(m_socketListener == null)
         {
            m_socketListener = new SocketListener();
            m_socketListener.getSocketDataFun = getSocketData;
            m_socketListener.ioErrorFun = ioError;
            m_socketListener.securityErrorFun = securityError;
            m_socketManager.addSocketListener(m_socketListener);
         }
         if(Boolean(m_gamingUI.player1.getSocietyDataVO().getSocietyId()) == false)
         {
            if(m_societySystem.getSocietyListOfApply() == null)
            {
               m_societySystem.showSocietyListOfApply();
            }
            else
            {
               m_societyListOfApply = m_societySystem.getSocietyListOfApply();
            }
         }
         if(m_show == null)
         {
            m_myLoader.getClass("SocietyListPanel.swf","SocietyLisPanel",getListPanelSuccess,getListPanelFail);
            m_myLoader.load();
         }
         else
         {
            initShow2();
         }
         m_type = param1;
         if(m_type != "createSociety" && m_type != "applyJoinSociety" && m_type != "lookUpSocietyList")
         {
            throw new Error("type value error!");
         }
      }
      
      private function changeType(param1:String) : void
      {
         if(m_type == param1)
         {
            return;
         }
         m_type = param1;
         if(m_type != "createSociety" && m_type != "applyJoinSociety" && m_type != "lookUpSocietyList")
         {
            throw new Error("type value error!");
         }
         clearShow();
         initShow();
      }
      
      private function getSocketData(param1:InformationBody) : void
      {
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:DOWN_CreateSocietySuccess = null;
         var _loc3_:SaveTaskInfo = null;
         var _loc7_:DOWN_IsSuccessJoin = null;
         var _loc4_:DOWN_ApplyJoinReturn = null;
         var _loc5_:DOWN_CancelApplyJoinReturn = null;
         if(m_clear == null)
         {
            return;
         }
         m_societySystem.addWasteInforBody(param1);
         switch(param1.id_informationBody)
         {
            case 3005:
               m_societyListDetail = param1.getDetail() as DOWN_TheSocietyList;
               m_societyListDetail.initOther(m_pageIndex_gettingSocietyList,m_num_gettingSocietyList);
               m_societySystem.addSocietyListDataInSocietyListPanel(m_societyListDetail);
               m_pageGroupBtn.initPageNumber(m_pageIndex_gettingSocietyList + 1,m_columeNumOnePage);
               if(m_show == null)
               {
                  m_myLoader.getClass("SocietyListPanel.swf","SocietyLisPanel",getListPanelSuccess,getListPanelFail);
                  m_myLoader.load();
                  break;
               }
               initShow3();
               break;
            case 3023:
               _loc2_ = param1.getDetail() as DOWN_CreateSocietySuccess;
               if(_loc2_.getIsSuccess())
               {
                  showWarningBox("创建帮会成功！",0);
                  changeType("lookUpSocietyList");
                  m_societySystem.clearSocietyBufferData();
                  m_societySystem.getSocietyList(m_pageGroupBtn.pageNum - 1,m_columeNumOnePage);
                  m_pageIndex_gettingSocietyList = m_pageGroupBtn.pageNum - 1;
                  m_num_gettingSocietyList = m_columeNumOnePage;
                  break;
               }
               showWarningBox("创建帮会失败!",0);
               GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + m_moneyOfPlayer1_createSociety;
               if(GamingUI.getInstance().player2)
               {
                  GamingUI.getInstance().player2.playerVO.money = GamingUI.getInstance().player2.playerVO.money + m_moneyOfPlayer2_createSociety;
               }
               _loc3_ = new SaveTaskInfo();
               _loc3_.type = "4399";
               _loc3_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc3_);
               MyFunction2.saveGame();
               break;
            case 3016:
               _loc7_ = param1.getDetail() as DOWN_IsSuccessJoin;
               if(_loc7_.getIsAgree())
               {
                  showWarningBox("加入\"" + _loc7_.getSocietyName() + "\"帮会成功！",0);
                  changeType("lookUpSocietyList");
                  break;
               }
               showWarningBox("加入\"" + _loc7_.getSocietyName() + "\"帮会失败！",0);
               arrangeColume(0);
               break;
            case 3009:
               m_memberDataList = param1.getDetail() as DOWN_TheSocietyMemberList;
               m_memberDataList.initOther(m_pageIndex_gettingMemberList,m_num_gettingMemberList);
               m_societySystem.addMemberDataListInSocietyListPanel(m_societyId_gettingMemberList.toString(),m_memberDataList);
               if(m_memberListPanel == null)
               {
                  m_myLoader.getClass("SocietyListPanel.swf","SocietyMemberListPanel",getMemberListPanelSuccess,getMemberListPanelFail);
                  m_myLoader.load();
                  break;
               }
               initMemberListPanelShow();
               break;
            case 3013:
               _loc4_ = param1.getDetail() as DOWN_ApplyJoinReturn;
               if(_loc4_.getData() == 1)
               {
                  trace("downApplySuccess",_loc4_);
                  _loc6_ = !!m_societyColumes ? m_societyColumes.length : 0;
                  _loc8_ = 0;
                  while(_loc8_ < _loc6_)
                  {
                     if(m_societyColumes[_loc8_].getSocietyData() != null)
                     {
                        if(m_societyColumes[_loc8_].getSocietyData().getSocietyId() == _loc4_.getSocietyId())
                        {
                           m_societyColumes[_loc8_].setType2("cancelApplyAndLookUpBtn");
                        }
                     }
                     _loc8_++;
                  }
                  break;
               }
               if(_loc4_.getData() == 2)
               {
                  showWarningBox("申请加入帮会需要1点券， 点券不足， 申请加入失败。",0);
                  break;
               }
               if(_loc4_.getData() == 3)
               {
                  showWarningBox("申请的帮会现已不存在。",0);
                  m_societySystem.clearSocietyBufferData();
                  m_societySystem.getSocietyList(m_pageGroupBtn.pageNum - 1,m_columeNumOnePage);
                  break;
               }
               if(_loc4_.getData() == 4)
               {
                  showWarningBox("申请的帮会申请者已达到最大上限。申请失败。",0);
                  break;
               }
               if(_loc4_.getData() == 5)
               {
                  showWarningBox("申请加入发生未知错误，导致申请失败。",0);
               }
               break;
            case 3018:
               _loc5_ = param1.getDetail() as DOWN_CancelApplyJoinReturn;
               _loc6_ = !!m_societyColumes ? m_societyColumes.length : 0;
               _loc8_ = 0;
               while(_loc8_ < _loc6_)
               {
                  if(m_societyColumes[_loc8_].getSocietyData() != null)
                  {
                     if(m_societyColumes[_loc8_].getSocietyData().getSocietyId() == _loc5_.getSocietyId())
                     {
                        m_societyColumes[_loc8_].setType2("applyAndLookUpBtn");
                     }
                  }
                  _loc8_++;
               }
               break;
            case 3037:
               m_societyListOfApply = m_societySystem.getSocietyListOfApply();
               m_societySystem.setTheSocietyListOfApply(m_societyListOfApply);
               initShow3();
         }
      }
      
      private function getMemberListPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_memberListPanelShow = new _loc2_();
         addChild(m_memberListPanelShow);
         m_memberListPanel = new MemberListPanel();
         m_memberListPanel.setShow(m_memberListPanelShow,m_societySystemXML);
         initMemberListPanelShow();
      }
      
      private function initMemberListPanelShow() : void
      {
         if(m_societySystem.getMemberDataListBySocietyIdAndIndexInSocietyListPanel(m_societyId_gettingMemberList.toString(),m_memberListPanel.getPageBtnGroup().pageNum - 1) == null || m_societySystem.getMemberDataListBySocietyIdAndIndexInSocietyListPanel(m_societyId_gettingMemberList.toString().toString(),m_memberListPanel.getPageBtnGroup().pageNum - 1).getDataDeadTime() < m_gamingUI.getEnterFrameTime().getOnLineTimeForThisInit())
         {
            m_societySystem.getTheSocietyMemberList(m_societyId_gettingMemberList,m_memberListPanel.getPageBtnGroup().pageNum - 1,m_memberListPanel.getOnePageColumeNum());
            m_societyId_gettingMemberList = m_societyId_gettingMemberList;
            m_pageIndex_gettingMemberList = m_memberListPanel.getPageBtnGroup().pageNum - 1;
            m_num_gettingMemberList = m_memberListPanel.getOnePageColumeNum();
         }
         else
         {
            m_memberDataList = m_societySystem.getMemberDataListBySocietyIdAndIndexInSocietyListPanel(m_societyId_gettingMemberList.toString(),m_memberListPanel.getPageBtnGroup().pageNum - 1);
            m_memberListPanel.setMemberDataList(m_memberDataList);
         }
      }
      
      private function getMemberListPanelFail(param1:YJFYLoaderData) : void
      {
         showWarningBox("加载失败",0);
      }
      
      private function getListPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         initShow();
         m_loadUI.tranToTransparentcy();
      }
      
      private function clearShow() : void
      {
         m_clear.clearObject(m_createSocietyBtn);
         m_createSocietyBtn = null;
         m_clear.clearObject(m_mySocietyBtn);
         m_mySocietyBtn = null;
         m_clear.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_clear.clearObject(m_groupWebBtn);
         m_groupWebBtn = null;
         m_clear.clearObject(m_pageGroupBtn);
         m_pageGroupBtn = null;
         m_clear.nullArr(m_societyColumes);
         m_societyColumes = null;
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc3_:DisplayObject = null;
         var _loc1_:SocietyColume = null;
         if(m_type == "createSociety")
         {
            m_showMC.gotoAndStop("createSociety");
            m_createSocietyBtn = new ButtonLogicShell2();
            m_createSocietyBtn.setShow(m_show["createSocietyBtn"]);
            m_createSocietyBtn.setTipString("点击创建帮会");
         }
         else if(m_type == "applyJoinSociety")
         {
            m_showMC.gotoAndStop("applyJoin");
         }
         else
         {
            m_showMC.gotoAndStop("other");
            m_mySocietyBtn = new ButtonLogicShell2();
            m_mySocietyBtn.setShow(m_show["mySocietyBtn"]);
            m_mySocietyBtn.setTipString("前往自己的帮会");
         }
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击关闭");
         m_groupWebBtn = new ButtonLogicShell2();
         m_groupWebBtn.setShow(m_show["groupWebBtn"]);
         m_groupWebBtn.setTipString("打开群组网页");
         m_pageGroupBtn = new PageBtnGroupLogicShell();
         m_pageGroupBtn.setShow(m_show["pageBtnGroup"]);
         m_pageGroupBtn.initPageNumber(1,1);
         var _loc2_:int = m_show.numChildren;
         m_columeNumOnePage = 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = m_show.getChildAt(_loc4_);
            if(_loc3_.name.substr(0,13) == "societyColume")
            {
               m_columeNumOnePage++;
            }
            _loc4_++;
         }
         m_societyColumes = new Vector.<SocietyColume>();
         _loc4_ = 0;
         while(_loc4_ < m_columeNumOnePage)
         {
            _loc1_ = new SocietyColume();
            _loc1_.setShow(m_show["societyColume" + (_loc4_ + 1)]);
            _loc1_.setType1(m_type);
            (_loc1_.getShow() as MovieClip).buttonMode = false;
            m_societyColumes.push(_loc1_);
            _loc1_.getShow().visible = false;
            _loc4_++;
         }
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_show == null)
         {
            return;
         }
         if(m_societySystem.getSocietyListDataInSocietyListPanelByIndex(m_pageGroupBtn.pageNum - 1) == null || m_societySystem.getSocietyListDataInSocietyListPanelByIndex(m_pageGroupBtn.pageNum - 1).getDataDeadTime() < m_gamingUI.getEnterFrameTime().getOnLineTimeForThisInit())
         {
            m_societySystem.getSocietyList(m_pageGroupBtn.pageNum - 1,m_columeNumOnePage);
            m_pageIndex_gettingSocietyList = m_pageGroupBtn.pageNum - 1;
            m_num_gettingSocietyList = m_columeNumOnePage;
         }
         else
         {
            m_societyListDetail = m_societySystem.getSocietyListDataInSocietyListPanelByIndex(m_pageGroupBtn.pageNum - 1);
            initShow3();
         }
      }
      
      private function initShow3() : void
      {
         if(m_societyListDetail == null)
         {
            return;
         }
         setPageBtn(m_pageGroupBtn.pageNum,m_societyListDetail.getSocietyTotalNum());
         arrangeColume(0);
      }
      
      private function getListPanelFail(param1:YJFYLoaderData) : void
      {
         showWarningBox("加载失败",0);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:GamingUI = null;
         var _loc6_:String = null;
         var _loc3_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc8_:int = 0;
         var _loc7_:MemberDataInMemberList = null;
         var _loc10_:* = param1.button;
         switch(_loc10_)
         {
            case m_quitBtn:
               m_gamingUI.closeSocietyListPanel();
               return;
            case m_pageGroupBtn:
               initShow2();
               return;
            case m_groupWebBtn:
               navigateToURL(new URLRequest("http://my.4399.com/forums-thread-tagid-81260-id-38874727.html"),"_blank");
               return;
            case m_createSocietyBtn:
               m_myLoader.getClass("SocietyListPanel.swf","CreateSocietyPanel",getCreatePanelShowSuccess,getCreatePanelShowFail);
               m_myLoader.load();
               return;
            case m_mySocietyBtn:
               _loc2_ = m_gamingUI;
               _loc2_.closeSocietyListPanel();
               _loc2_.openMySocietyPanel();
               return;
            default:
               if((!!m_createSocietyPanel ? m_createSocietyPanel.getCancelBtn() : null) !== _loc10_)
               {
                  if((!!m_createSocietyPanel ? m_createSocietyPanel.getQuitBtn() : null) !== _loc10_)
                  {
                     if((!!m_createSocietyPanel ? m_createSocietyPanel.getSureBtn() : null) !== _loc10_)
                     {
                        if((!!m_memberListPanel ? m_memberListPanel.getQuitBtn() : null) !== _loc10_)
                        {
                           if((!!m_memberListPanel ? m_memberListPanel.getPageBtnGroup() : null) !== _loc10_)
                           {
                              var _loc4_:int = !!m_societyColumes ? m_societyColumes.length : 0;
                              _loc9_ = 0;
                              while(_loc9_ < _loc4_)
                              {
                                 if(param1.button == m_societyColumes[_loc9_].getApplyBtn())
                                 {
                                    _loc5_ = int(m_societySystemXML.applyJoinData[0].@maxApplyNum);
                                    _loc8_ = !!m_societyListOfApply ? m_societyListOfApply.getSocietyNumOfApply() : 0;
                                    if(_loc8_ < _loc5_)
                                    {
                                       m_societySystem.applyJoinSociety(m_societyColumes[_loc9_].getSocietyData().getSocietyId());
                                    }
                                    else
                                    {
                                       showWarningBox("您申请加入的帮会已经达到" + _loc5_ + "个。不能再申请了！",0);
                                    }
                                    return;
                                 }
                                 if(param1.button == m_societyColumes[_loc9_].getCancelApplyBtn())
                                 {
                                    m_societySystem.cancelApplyJoinSociety(m_societyColumes[_loc9_].getSocietyData().getSocietyId());
                                    return;
                                 }
                                 if(param1.button == m_societyColumes[_loc9_].getLookUpBtn())
                                 {
                                    m_societyId_gettingMemberList = m_societyColumes[_loc9_].getSocietyData().getSocietyId();
                                    if(m_memberListPanel == null)
                                    {
                                       m_myLoader.getClass("SocietyListPanel.swf","SocietyMemberListPanel",getMemberListPanelSuccess,getMemberListPanelFail);
                                       m_myLoader.load();
                                    }
                                    else
                                    {
                                       initMemberListPanelShow();
                                    }
                                    return;
                                 }
                                 _loc9_++;
                              }
                              _loc4_ = !!m_memberListPanel ? m_memberListPanel.getColumeNum() : 0;
                              _loc9_ = 0;
                              while(_loc9_ < _loc4_)
                              {
                                 if(m_memberListPanel.getColumeByIndex(_loc9_) != null)
                                 {
                                    if(m_memberListPanel.getColumeByIndex(_loc9_).getMemberData() != null)
                                    {
                                       if(param1.button == m_memberListPanel.getColumeByIndex(_loc9_).getLookUpBtn())
                                       {
                                          if(m_playerDatas == null)
                                          {
                                             m_playerDatas = new PlayerDatas();
                                             if(m_getPlayerDataListener == null)
                                             {
                                                m_getPlayerDataListener = new GetPlayerDataListener();
                                             }
                                             m_getPlayerDataListener.getPlayerDataFun = getPlayerData;
                                             m_playerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
                                             m_playerDatas.init();
                                          }
                                          _loc7_ = m_memberListPanel.getColumeByIndex(_loc9_).getMemberData();
                                          m_playerDatas.getPlayerData(_loc7_.getUid_member().toString(),_loc7_.getIdx_member());
                                       }
                                    }
                                 }
                                 _loc9_++;
                              }
                              return;
                           }
                           initMemberListPanelShow();
                           return;
                        }
                        if(Boolean(m_memberListPanelShow) && m_memberListPanelShow.parent)
                        {
                           m_memberListPanelShow.parent.removeChild(m_memberListPanelShow);
                        }
                        ClearUtil.clearDisplayObjectInContainer(m_memberListPanelShow);
                        m_memberListPanelShow = null;
                        ClearUtil.clearObject(m_memberListPanel);
                        m_memberListPanel = null;
                        ClearUtil.clearObject(m_otherPlayerShowPanel);
                        m_otherPlayerShowPanel = null;
                        return;
                     }
                     _loc6_ = m_createSocietyPanel.getNameText().text;
                     _loc3_ = int(m_societySystemXML.societyName[0].@maxLength);
                     if(Boolean(_loc6_) == false)
                     {
                        showWarningBox("帮会的名称不能为空。创建帮会失败。",0);
                        return;
                     }
                     if(_loc6_.length > _loc3_)
                     {
                        _loc6_ = _loc6_.substr(0,_loc3_);
                     }
                     if(m_dirtyWordFilterEngine == null)
                     {
                        m_dirtyWordFilterEngine = new DirtyWordFilterEngine(filterCreateSocietyName,[_loc6_]);
                     }
                     else
                     {
                        filterCreateSocietyName(_loc6_);
                     }
                     return;
                  }
               }
               ClearUtil.clearObject(m_createSocietyPanel);
               m_createSocietyPanel = null;
               return;
         }
      }
      
      private function getPlayerData(param1:InitPlayersData) : void
      {
         openPlayerShowPanel(param1.player1,param1.player2,param1.nickNameData);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         addChild(m_otherPlayerShowPanel);
      }
      
      private function getCreatePanelShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         if(m_createSocietyPanel == null)
         {
            _loc2_ = param1.resultClass;
            m_createSocietyPanel = new CreateSocietyPanel();
            m_createSocietyPanel.setNameMaxLength(int(m_societySystemXML.societyName[0].@maxLength));
            m_createSocietyPanel.setShow(new _loc2_());
            if(stage)
            {
               m_createSocietyPanel.getShow().x = (stage.stageWidth - m_createSocietyPanel.getShow().width) / 2;
               m_createSocietyPanel.getShow().y = (stage.stageHeight - m_createSocietyPanel.getShow().height) / 2;
            }
         }
         addChild(m_createSocietyPanel.getShow());
      }
      
      private function getCreatePanelShowFail(param1:YJFYLoaderData) : void
      {
         showWarningBox("加载失败！",0);
      }
      
      private function filterCreateSocietyName(param1:String) : void
      {
         m_dirtyWordFilterEngine.filterWords(param1,checkBadReturn);
      }
      
      private function checkBadReturn(param1:String) : void
      {
         showWarningBox("帮会名称为：“" + param1 + "“，确定要创建帮会吗？",3,{
            "type":"createSociety",
            "okFunction":createSociety,
            "okFunctionParams":[param1]
         });
         ClearUtil.clearObject(m_createSocietyPanel);
         m_createSocietyPanel = null;
      }
      
      private function createSociety(param1:String) : void
      {
         m_clear.clearObject(m_createSocietyPanel);
         m_createSocietyPanel = null;
         var _loc8_:int = int(m_societySystemXML.createSociety[0].@needLevel);
         var _loc7_:int = Math.max(GamingUI.getInstance().player1.playerVO.level,!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO.level : 0);
         if(_loc7_ < _loc8_)
         {
            showWarningBox("人物等级不足， 不能创建帮会！",0);
            GamingUI.getInstance().showMessageTip("人物等级不足。 不能创建帮会！");
            return;
         }
         var _loc4_:int = int(m_societySystemXML.createSociety[0].@needPointTicket);
         if(CurrentTicketPointManager.getInstance().getCurrentTicketPoint() < _loc4_)
         {
            showWarningBox("点券不足, 不能创建帮会！",0);
            GamingUI.getInstance().showMessageTip("点券不足, 不能创建帮会！");
            return;
         }
         var _loc3_:int = int(m_societySystemXML.createSociety[0].@needMoney);
         var _loc2_:int = GamingUI.getInstance().player1.playerVO.money + (!!GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO.money : 0);
         if(_loc2_ < _loc3_)
         {
            showWarningBox("元宝不足， 不能创建帮会！",0);
            GamingUI.getInstance().showMessageTip("元宝不足， 不能创建帮会！");
            return;
         }
         var _loc6_:int = GamingUI.getInstance().player1.playerVO.money - _loc3_;
         if(_loc6_ < 0)
         {
            m_moneyOfPlayer1_createSociety = _loc3_ + _loc6_;
            m_moneyOfPlayer2_createSociety = -_loc6_;
            GamingUI.getInstance().player1.playerVO.money = 0;
            GamingUI.getInstance().player2.playerVO.money = GamingUI.getInstance().player2.playerVO.money + _loc6_;
         }
         else
         {
            m_moneyOfPlayer1_createSociety = _loc3_;
            m_moneyOfPlayer2_createSociety = 0;
            GamingUI.getInstance().player1.playerVO.money = _loc6_;
         }
         m_societySystem.createSociety(param1);
         var _loc5_:SaveTaskInfo = new SaveTaskInfo();
         _loc5_.type = "4399";
         _loc5_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc5_);
         MyFunction2.saveGame();
      }
      
      private function setPageBtn(param1:int, param2:int) : void
      {
         if(param2 == 0)
         {
            m_pageGroupBtn.initPageNumber(1,1);
         }
         else if(param2 % m_columeNumOnePage == 0)
         {
            m_pageGroupBtn.initPageNumber(param1,param2 / m_columeNumOnePage);
         }
         else
         {
            m_pageGroupBtn.initPageNumber(param1,int(param2 / m_columeNumOnePage) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc7_:* = 0;
         var _loc5_:int = 0;
         var _loc3_:SocietyColume = null;
         var _loc2_:SocietyDataInList = null;
         var _loc6_:int = param1 + m_columeNumOnePage;
         var _loc4_:int = m_societyListDetail.getSocietyNum();
         _loc7_ = param1;
         while(_loc7_ < _loc6_ && _loc7_ < _loc4_)
         {
            _loc3_ = m_societyColumes[_loc5_];
            _loc3_.getShow().visible = true;
            _loc2_ = m_societyListDetail.getSocietyDataByIndex(_loc7_);
            _loc3_.setSocietyDataInList(_loc2_,m_societyListDetail.getPageIndex() * m_societyListDetail.getNum() + (_loc7_ + 1),m_societyListOfApply);
            _loc5_++;
            _loc7_++;
         }
         while(_loc5_ < m_columeNumOnePage)
         {
            m_societyColumes[_loc5_].getShow().visible = false;
            _loc5_++;
         }
      }
      
      private function ioError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("输入/输出流发生错误，导致加载失败");
      }
      
      private function securityError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("安全错误，导致加载失败");
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               m_clear.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         m_clear.nullObject(param1.data);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,!!m_otherPlayerShowPanel ? m_otherPlayerShowPanel.currentPlayer : null);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

