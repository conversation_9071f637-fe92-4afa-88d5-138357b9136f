package UI.Other
{
   import flash.display.Shape;
   
   public class CDAnimationShape extends Shape
   {
      public static const DRAW_DIRECTION_CW:int = 1;
      
      public static const DRAW_DIRECTION_CCW:int = -1;
      
      private var _width:Number;
      
      private var _height:Number;
      
      private var _angle:Number;
      
      private var _color:int;
      
      private var _alpha:Number;
      
      public function CDAnimationShape()
      {
         super();
      }
      
      public function init(param1:Number, param2:Number, param3:Number, param4:Number) : void
      {
         _width = param1;
         _height = param2;
         _angle = Math.atan(param1 / 2 / (param2 / 2));
         _color = param3;
         _alpha = param4;
      }
      
      public function drawShape(param1:Number, param2:int = 1) : void
      {
         var _loc3_:* = undefined;
         var _loc4_:* = undefined;
         if(param1 >= 0 && param1 <= _angle)
         {
            _loc3_ = new Vector.<int>(3,true);
            _loc3_[0] = 1;
            _loc3_[1] = 2;
            _loc3_[2] = 2;
            _loc4_ = new Vector.<Number>(6,true);
            _loc4_[0] = 0;
            _loc4_[1] = 0;
            _loc4_[2] = 0;
            _loc4_[3] = -_height / 2;
            _loc4_[4] = param2 * (_height / 2) * Math.tan(param1);
            _loc4_[5] = -_height / 2;
         }
         else if(param1 > _angle && param1 < 3.141592653589793 - _angle)
         {
            _loc3_ = new Vector.<int>(4,true);
            _loc3_[0] = 1;
            _loc3_[1] = 2;
            _loc3_[2] = 2;
            _loc3_[3] = 2;
            _loc4_ = new Vector.<Number>(8,true);
            _loc4_[0] = 0;
            _loc4_[1] = 0;
            _loc4_[2] = 0;
            _loc4_[3] = -_height / 2;
            _loc4_[4] = param2 * _width / 2;
            _loc4_[5] = -_height / 2;
            _loc4_[6] = param2 * _width / 2;
            _loc4_[7] = -(_width / 2) * Math.tan(1.5707963267948966 - param1);
         }
         else if(param1 >= 3.141592653589793 - _angle && param1 < 3.141592653589793 + _angle)
         {
            _loc3_ = new Vector.<int>(5,true);
            _loc3_[0] = 1;
            _loc3_[1] = 2;
            _loc3_[2] = 2;
            _loc3_[3] = 2;
            _loc3_[4] = 2;
            _loc4_ = new Vector.<Number>(10,true);
            _loc4_[0] = 0;
            _loc4_[1] = 0;
            _loc4_[2] = 0;
            _loc4_[3] = -_height / 2;
            _loc4_[4] = param2 * _width / 2;
            _loc4_[5] = -_height / 2;
            _loc4_[6] = param2 * _width / 2;
            _loc4_[7] = _height / 2;
            _loc4_[8] = param2 * (_height / 2) * Math.tan(3.141592653589793 - param1);
            _loc4_[9] = _height / 2;
         }
         else if(param1 >= 3.141592653589793 + _angle && param1 < 6.283185307179586 - _angle)
         {
            _loc3_ = new Vector.<int>(6,true);
            _loc3_[0] = 1;
            _loc3_[1] = 2;
            _loc3_[2] = 2;
            _loc3_[3] = 2;
            _loc3_[4] = 2;
            _loc3_[5] = 2;
            _loc4_ = new Vector.<Number>(12,true);
            _loc4_[0] = 0;
            _loc4_[1] = 0;
            _loc4_[2] = 0;
            _loc4_[3] = -_height / 2;
            _loc4_[4] = param2 * _width / 2;
            _loc4_[5] = -_height / 2;
            _loc4_[6] = param2 * _width / 2;
            _loc4_[7] = _height / 2;
            _loc4_[8] = param2 * -_width / 2;
            _loc4_[9] = _height / 2;
            _loc4_[10] = param2 * -_width / 2;
            _loc4_[11] = _width / 2 * Math.tan(4.71238898038469 - param1);
         }
         else
         {
            _loc3_ = new Vector.<int>(7,true);
            _loc3_[0] = 1;
            _loc3_[1] = 2;
            _loc3_[2] = 2;
            _loc3_[3] = 2;
            _loc3_[4] = 2;
            _loc3_[5] = 2;
            _loc3_[6] = 2;
            _loc4_ = new Vector.<Number>(14,true);
            _loc4_[0] = 0;
            _loc4_[1] = 0;
            _loc4_[2] = 0;
            _loc4_[3] = -_height / 2;
            _loc4_[4] = param2 * _width / 2;
            _loc4_[5] = -_height / 2;
            _loc4_[6] = param2 * _width / 2;
            _loc4_[7] = _height / 2;
            _loc4_[8] = param2 * -_width / 2;
            _loc4_[9] = _height / 2;
            _loc4_[10] = param2 * -_width / 2;
            _loc4_[11] = -_height / 2;
            _loc4_[12] = param2 * -(_height / 2) * Math.tan(6.283185307179586 - param1);
            _loc4_[13] = -_height / 2;
         }
         graphics.clear();
         graphics.beginFill(_color,_alpha);
         graphics.drawPath(_loc3_,_loc4_);
      }
      
      override public function set visible(param1:Boolean) : void
      {
         super.visible = param1;
         if(!param1)
         {
            graphics.clear();
         }
      }
   }
}

