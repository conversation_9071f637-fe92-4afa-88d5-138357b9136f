package UI.ShopWall
{
   import UI.EquipmentCells.ShopEquipmentCell;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ShopWallSlot extends MySprite
   {
      public var nameText:TextField;
      
      public var descriptionText:TextField;
      
      public var priceText:TextField;
      
      public var border:Sprite;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _equipmentCell:IEquipmentCell;
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      public function ShopWallSlot()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
         setTipString("点击购买该物品");
         buttonMode = true;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(!_loc1_ is Equipment && _loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         _equipmentVO = null;
         nameText = null;
         descriptionText = null;
         priceText = null;
         border = null;
         if(_equipmentCell)
         {
            _equipmentCell.clear();
         }
         _equipmentCell = null;
         ClearUtil.clearObject(m_smallToolTip);
         m_smallToolTip = null;
      }
      
      public function initSlot(param1:EquipmentVO) : void
      {
         _equipmentVO = param1;
         _equipmentCell = new ShopEquipmentCell();
         _equipmentCell.x = 0 + _equipmentCell.width / 2;
         _equipmentCell.y = 7 + _equipmentCell.height / 2;
         addChild(_equipmentCell as DisplayObject);
         _equipmentCell.addEquipmentVO(_equipmentVO);
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         nameText.defaultTextFormat = new TextFormat(_loc2_.fontName,22,16765098);
         nameText.mouseEnabled = false;
         descriptionText.defaultTextFormat = new TextFormat(_loc2_.fontName,18,16765098);
         descriptionText.mouseEnabled = false;
         priceText.defaultTextFormat = new TextFormat(_loc2_.fontName,30,12990485);
         priceText.mouseEnabled = false;
         nameText.embedFonts = true;
         priceText.embedFonts = true;
         descriptionText.embedFonts = true;
         nameText.text = _equipmentVO.name;
         descriptionText.text = _equipmentVO.description;
         priceText.text = _equipmentVO.ticketPrice.toString();
         border.visible = false;
         buttonMode = true;
      }
      
      public function setTipString(param1:String) : void
      {
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               m_smallToolTip = new SmallToolTip();
               m_smallToolTip.setFont(new FangZhengKaTongJianTi());
               m_smallToolTip.setTipStr(m_tipString);
               m_smallToolTip.init();
            }
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",mouseOverSlot,false,0,true);
         addEventListener("rollOut",mouseOutSlot,false,0,true);
         addEventListener("click",click,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("rollOver",mouseOverSlot,false);
         removeEventListener("rollOut",mouseOutSlot,false);
         removeEventListener("click",click,false);
      }
      
      protected function mouseOverSlot(param1:MouseEvent) : void
      {
         border.visible = true;
         if(m_smallToolTip)
         {
            if(stage)
            {
               stage.addChild(m_smallToolTip);
               if(stage.mouseX + 10 + m_smallToolTip.width > stage.stageWidth)
               {
                  m_smallToolTip.x = stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = stage.mouseX + 10;
               }
               if(stage.mouseY + 10 + m_smallToolTip.height > stage.stageHeight)
               {
                  m_smallToolTip.y = stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = stage.mouseY + 10;
               }
            }
         }
      }
      
      protected function mouseOutSlot(param1:MouseEvent) : void
      {
         border.visible = false;
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
      }
      
      protected function click(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         dispatchEvent(new UIDataEvent("showBox",{"equipmentVO":_equipmentVO.clone()}));
      }
   }
}

