package UI
{
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import flash.events.EventDispatcher;
   
   public class DataManagerParent extends EventDispatcher
   {
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function DataManagerParent()
      {
         super();
         init();
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      protected function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
      }
   }
}

