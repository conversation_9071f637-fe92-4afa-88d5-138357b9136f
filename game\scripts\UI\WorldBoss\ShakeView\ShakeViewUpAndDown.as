package UI.WorldBoss.ShakeView
{
   import UI.WorldBoss.View;
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   
   public class ShakeViewUpAndDown extends ShakeView
   {
      private var _yRange:Number;
      
      private var _downDuration:Number;
      
      private var _upDuration:Number;
      
      private var _orignalY:Number;
      
      public function ShakeViewUpAndDown()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function shakeView(param1:View) : void
      {
         var view:View = param1;
         var complete:* = function():void
         {
            TweenLite.to(view.getShow(),_upDuration,{
               "y":_orignalY,
               "ease":Linear.easeNone,
               "onComplete":complete2,
               "onCompleteParams":[]
            });
         };
         var complete2:* = function():void
         {
            _isShake = false;
            recover();
            view.setIsShake(false);
         };
         super.shakeView(view);
         if(_isShake)
         {
            return;
         }
         _isShake = true;
         _isMyShake = true;
         view.setIsShake(true);
         _orignalY = view.getShow().y;
         TweenLite.to(view.getShow(),_downDuration,{
            "y":_orignalY + _yRange,
            "ease":Linear.easeNone,
            "onComplete":complete,
            "onCompleteParams":[]
         });
      }
      
      public function setYRange(param1:Number) : void
      {
         _yRange = param1;
      }
      
      public function setDownDuration(param1:Number) : void
      {
         _downDuration = param1;
      }
      
      public function setUpDuration(param1:Number) : void
      {
         _upDuration = param1;
      }
      
      override protected function recover() : void
      {
         if(_isMyShake)
         {
            _view.getShow().y = _orignalY;
         }
         super.recover();
      }
   }
}

