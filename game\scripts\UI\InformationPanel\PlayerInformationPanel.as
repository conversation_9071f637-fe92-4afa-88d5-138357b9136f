package UI.InformationPanel
{
   import UI.BorderEngine;
   import UI.DanMedicinePanel.DanMedicinePanel;
   import UI.EquipmentCellContainer;
   import UI.EquipmentCells.UsedEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.InformationPanel.Btn.LookDanMedicinesBtn;
   import UI.InformationPanel.Btn.LookPlayerAttributeBtn;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.Number.NumberContainer;
   import UI.Other.HuLuBitmapData;
   import UI.Other.MedalBitmapData;
   import UI.Other.ShiZhuangBitmapData;
   import UI.Other.TaoZhuangBitmapData;
   import UI.Other.WuQiBitmapData;
   import UI.Other.XiangLianBitmapData;
   import UI.Players.Player;
   import UI.ShiTu.XiuLianPanel;
   import UI.UIInterface.OldInterface.ICell;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI2.medalPanel.MedalPanel;
   import YJFY.EntityShowContainer;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PlayerInformationPanel extends MySprite
   {
      private const LEVEL_NUMBER_SPACE:Number = 45;
      
      public var usedEquipmentCell_6:UsedEquipmentCell;
      
      public var nameText:TextField;
      
      public var nicknameText:TextField;
      
      public var scoreText:TextField;
      
      public var numberContainer:NumberContainer;
      
      public var showPanel:ShowPanel;
      
      public var scoreStars:ScoreStar;
      
      public var bloodBar:MovieClip;
      
      public var magicBar:MovieClip;
      
      public var experienceBar:MovieClip;
      
      public var lookAttributeBtn:LookPlayerAttributeBtn;
      
      public var lookDanMedicineBtn:LookDanMedicinesBtn;
      
      public var lookUpXiuLianBtn:MovieClip;
      
      public var lookMedalBtn:MovieClip;
      
      private var m_lookUpXiuLianBtn:ButtonLogicShell2;
      
      private var m_lookMedalBtn:ButtonLogicShell2;
      
      public var usedEquipmentCell_1:UsedEquipmentCell;
      
      public var usedEquipmentCell_2:UsedEquipmentCell;
      
      public var usedEquipmentCell_3:UsedEquipmentCell;
      
      public var usedEquipmentCell_4:UsedEquipmentCell;
      
      public var usedEquipmentCell_5:UsedEquipmentCell;
      
      protected var _bloodBar:CMSXChangeBarLogicShell;
      
      protected var _magicBar:CMSXChangeBarLogicShell;
      
      protected var _experienceBar:CMSXChangeBarLogicShell;
      
      protected var _useEquipmentCellContainer:EquipmentCellContainer;
      
      protected var _useEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _font:FangZhengKaTongJianTi;
      
      protected var _currentPlayer:Player;
      
      private var _playerAttributePanel:PlayerAttributePanel;
      
      private var _danMedicinePanel:DanMedicinePanel;
      
      private var _xiuLianPanel:XiuLianPanel;
      
      private var _medalPanel:MedalPanel;
      
      private var _cellsLayer:Sprite;
      
      private var _playerShowContainer:EntityShowContainer;
      
      public function PlayerInformationPanel()
      {
         super();
         initPlayerInformationPanel();
         addEventListener("addedToStage",addToStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function setPlayerPanel(param1:Player) : void
      {
         refreshEquipments(param1.playerVO.inforEquipmentVOs);
         nameText.text = GameData.getInstance().getLoginReturnData().getUid();
         scoreText.text = param1.playerVO.eqScore.toFixed(2);
         scoreStars.setStar(param1.playerVO.eqScore);
         _bloodBar.change(param1.playerVO.bloodPercent);
         _bloodBar.setDataShow("" + Math.round(param1.playerVO.bloodPercent * param1.playerVO.bloodVolume) + "/" + param1.playerVO.bloodVolume);
         _magicBar.change(param1.playerVO.magicPercent);
         _magicBar.setDataShow("" + Math.round(param1.playerVO.magicPercent * param1.playerVO.maxMagic) + "/" + param1.playerVO.maxMagic);
         _experienceBar.change(param1.playerVO.experiencePercent);
         _experienceBar.setDataShow("" + Math.round(param1.playerVO.experiencePercent * param1.playerVO.experienceVolume) + "/" + param1.playerVO.experienceVolume);
         numberContainer.initNumber(param1.playerVO.level,"UI.Number.LevelNumber.LevelNumber",45);
         usedEquipmentCell_1.owner = param1.playerVO.playerType;
         usedEquipmentCell_2.owner = param1.playerVO.playerType;
         usedEquipmentCell_3.owner = param1.playerVO.playerType;
         usedEquipmentCell_4.owner = param1.playerVO.playerType;
         usedEquipmentCell_5.owner = param1.playerVO.playerType;
         usedEquipmentCell_6.owner = param1.playerVO.playerType;
         usedEquipmentCell_1.player = param1;
         usedEquipmentCell_2.player = param1;
         usedEquipmentCell_3.player = param1;
         usedEquipmentCell_4.player = param1;
         usedEquipmentCell_5.player = param1;
         usedEquipmentCell_6.player = param1;
         currentPlayer = param1;
         _playerShowContainer.refreshPlayerShow(param1.playerVO);
         addChildPlayer(_playerShowContainer.getShow());
         usedEquipmentCell_1.isShowBtn = false;
         usedEquipmentCell_2.isShowBtn = false;
         usedEquipmentCell_3.isShowBtn = false;
         usedEquipmentCell_4.isShowBtn = false;
         usedEquipmentCell_5.isShowBtn = false;
         usedEquipmentCell_6.isShowBtn = false;
      }
      
      protected function initPlayerInformationPanel() : void
      {
         _cellsLayer = new Sprite();
         addChild(_cellsLayer);
         addToVector();
         _font = new FangZhengKaTongJianTi();
         numberContainer = new NumberContainer();
         numberContainer.x = 260;
         numberContainer.y = 35;
         addChildAt(numberContainer,0);
         nameText.defaultTextFormat = new TextFormat(null,15,16777215);
         nameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         nicknameText.defaultTextFormat = new TextFormat(null,15,16777215);
         nicknameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         scoreText.defaultTextFormat = new TextFormat(_font.fontName,16,16763904);
         scoreText.embedFonts = true;
         _useEquipmentVOs = new Vector.<EquipmentVO>(6);
         usedEquipmentCell_1.cellType = "precious";
         usedEquipmentCell_2.cellType = "clothes";
         usedEquipmentCell_3.cellType = "gourd";
         usedEquipmentCell_4.cellType = "fashion";
         usedEquipmentCell_5.cellType = "weapon";
         usedEquipmentCell_6.cellType = "necklace";
         usedEquipmentCell_1.setShading(new MedalBitmapData());
         usedEquipmentCell_2.setShading(new TaoZhuangBitmapData());
         usedEquipmentCell_3.setShading(new HuLuBitmapData());
         usedEquipmentCell_4.setShading(new ShiZhuangBitmapData());
         usedEquipmentCell_5.setShading(new WuQiBitmapData());
         usedEquipmentCell_6.setShading(new XiangLianBitmapData());
         var _loc1_:Vector.<ICell> = new Vector.<ICell>();
         for each(var _loc2_ in useEquipmentCellContainer.equipmentCells)
         {
            _loc1_.push(_loc2_ as ICell);
         }
         BorderEngine.getInstance().addFrameListener(_loc1_);
         _bloodBar = new CMSXChangeBarLogicShell();
         _bloodBar.setShow(bloodBar);
         _magicBar = new CMSXChangeBarLogicShell();
         _magicBar.setShow(magicBar);
         _experienceBar = new CMSXChangeBarLogicShell();
         _experienceBar.setShow(experienceBar);
         _playerShowContainer = new EntityShowContainer();
         _playerShowContainer.init();
         m_lookUpXiuLianBtn = new ButtonLogicShell2();
         m_lookUpXiuLianBtn.setShow(lookUpXiuLianBtn);
         m_lookMedalBtn = new ButtonLogicShell2();
         m_lookMedalBtn.setShow(lookMedalBtn);
      }
      
      public function set nickname(param1:Object) : void
      {
         var _loc2_:String = null;
         if(param1)
         {
            if(param1.nicknameType == "redNickname")
            {
               _loc2_ = "#ff0000";
            }
            else
            {
               _loc2_ = "#ffffff";
            }
            nicknameText.htmlText = "<font color=\'" + _loc2_ + "\'  size=\'15\' >" + (!!param1.extra ? param1.extra : "") + "</font>";
         }
         else
         {
            nicknameText.htmlText = "<font color=\'#666666\' size=\'15\' >暂无昵称</font>";
         }
      }
      
      public function addEquipmentVO(param1:EquipmentVO, param2:int) : void
      {
         var _loc3_:* = undefined;
         param1.playerVO = _currentPlayer.playerVO;
         _useEquipmentCellContainer.equipmentCells[param2].addEquipmentVO(param1);
         if(param2 >= _useEquipmentVOs.length)
         {
            _loc3_ = new Vector.<EquipmentVO>(param2 - _useEquipmentVOs.length + 1);
            _useEquipmentVOs.concat(_loc3_);
         }
         _useEquipmentVOs[param2] = param1;
         _currentPlayer.changeData();
      }
      
      public function removeEquipmentVO(param1:int = 0) : EquipmentVO
      {
         _useEquipmentCellContainer.equipmentCells[param1].removeEquipmentVO();
         var _loc2_:EquipmentVO = _useEquipmentVOs[param1];
         _useEquipmentVOs[param1] = null;
         _loc2_.playerVO = null;
         _currentPlayer.changeData();
         return _loc2_;
      }
      
      public function showCorrespondingCell(param1:EquipmentVO) : void
      {
         if(param1.equipmentType == "medal")
         {
            usedEquipmentCell_1.showBorder();
         }
         else if(param1.equipmentType == "clothes")
         {
            usedEquipmentCell_2.showBorder();
         }
         else if(param1.equipmentType == "gourd")
         {
            usedEquipmentCell_3.showBorder();
         }
         else if(param1.equipmentType == "fashion" || param1.equipmentType == "forever_fashion")
         {
            usedEquipmentCell_4.showBorder();
         }
         else if(param1.equipmentType == "weapon")
         {
            usedEquipmentCell_5.showBorder();
         }
         else if(param1.equipmentType == "necklace")
         {
            usedEquipmentCell_6.showBorder();
         }
      }
      
      public function refreshEquipments(param1:Vector.<EquipmentVO>) : void
      {
         _useEquipmentVOs = param1;
         for each(var _loc2_ in _useEquipmentCellContainer.equipmentCells)
         {
            _loc2_.removeEquipmentVO();
         }
         var _loc4_:int = 0;
         var _loc3_:int = !!param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1[_loc4_])
            {
               if(param1[_loc4_].equipmentType.match((_useEquipmentCellContainer.equipmentCells[_loc4_] as UsedEquipmentCell).cellType))
               {
                  _useEquipmentCellContainer.equipmentCells[_loc4_].addEquipmentVO(param1[_loc4_]);
               }
               else
               {
                  param1[_loc4_] = null;
               }
            }
            _loc4_++;
         }
      }
      
      public function hideBorders() : void
      {
         for each(var _loc1_ in _useEquipmentCellContainer.equipmentCells)
         {
            _loc1_.hideBorder();
         }
      }
      
      public function set currentPlayer(param1:Player) : void
      {
         _currentPlayer = param1;
         if(_playerAttributePanel)
         {
            _playerAttributePanel.refreshPanel(_currentPlayer);
         }
         if(_xiuLianPanel)
         {
            _xiuLianPanel.setPlayer(_currentPlayer);
         }
         if(_medalPanel)
         {
            _medalPanel.setPlayer(_currentPlayer);
         }
      }
      
      public function get useEquipmentCellContainer() : EquipmentCellContainer
      {
         return _useEquipmentCellContainer;
      }
      
      public function get danMedicinePanel() : DanMedicinePanel
      {
         return _danMedicinePanel;
      }
      
      public function addChildPlayer(param1:DisplayObject) : void
      {
         while(showPanel.numChildren > 3)
         {
            showPanel.removeChild(showPanel.getChildAt(showPanel.numChildren - 1));
         }
         param1.x = 70;
         param1.y = 150;
         param1.scaleX = 1.5;
         param1.scaleY = 1.5;
         showPanel.addChild(param1);
         showPanel.mouseChildren = false;
         showPanel.mouseEnabled = false;
      }
      
      public function set isVIPState(param1:Boolean) : void
      {
         showPanel.isVIPState = param1;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         removeEventListener("clickButton",clickButton,true);
         if(showPanel)
         {
            showPanel.clear();
         }
         showPanel = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_cellsLayer)
         {
            while(_cellsLayer.numChildren > 0)
            {
               _loc1_ = _cellsLayer.getChildAt(0);
               _cellsLayer.removeChildAt(0);
               if(_loc1_.hasOwnProperty("clear"))
               {
                  _loc1_["clear"]();
               }
            }
         }
         nameText = null;
         scoreText = null;
         if(numberContainer)
         {
            numberContainer.clear();
         }
         numberContainer = null;
         if(scoreStars)
         {
            scoreStars.clear();
         }
         scoreStars = null;
         if(usedEquipmentCell_1)
         {
            usedEquipmentCell_1.clear();
         }
         if(usedEquipmentCell_2)
         {
            usedEquipmentCell_2.clear();
         }
         if(usedEquipmentCell_3)
         {
            usedEquipmentCell_3.clear();
         }
         if(usedEquipmentCell_4)
         {
            usedEquipmentCell_4.clear();
         }
         if(usedEquipmentCell_5)
         {
            usedEquipmentCell_5.clear();
         }
         if(usedEquipmentCell_6)
         {
            usedEquipmentCell_6.clear();
         }
         usedEquipmentCell_1 = null;
         usedEquipmentCell_2 = null;
         usedEquipmentCell_3 = null;
         usedEquipmentCell_4 = null;
         usedEquipmentCell_5 = null;
         usedEquipmentCell_6 = null;
         _useEquipmentVOs = null;
         _font = null;
         if(_useEquipmentCellContainer)
         {
            _useEquipmentCellContainer.clear();
         }
         _useEquipmentCellContainer = null;
         if(_bloodBar)
         {
            _bloodBar.clear();
         }
         if(_magicBar)
         {
            _magicBar.clear();
         }
         if(_experienceBar)
         {
            _experienceBar.clear();
         }
         _bloodBar = null;
         _magicBar = null;
         _experienceBar = null;
         bloodBar = null;
         magicBar = null;
         experienceBar = null;
         _currentPlayer = null;
         if(_playerAttributePanel)
         {
            _playerAttributePanel.clear();
            _playerAttributePanel.removeEventListener("clickQuitBtn",quit,true);
         }
         _playerAttributePanel = null;
         _cellsLayer = null;
         ClearUtil.clearObject(m_lookUpXiuLianBtn);
         m_lookUpXiuLianBtn = null;
         ClearUtil.clearObject(m_lookMedalBtn);
         m_lookMedalBtn = null;
         ClearUtil.clearObject(_xiuLianPanel);
         _xiuLianPanel = null;
         ClearUtil.clearObject(_medalPanel);
         _medalPanel = null;
      }
      
      private function addToVector() : void
      {
         usedEquipmentCell_1 = new UsedEquipmentCell();
         usedEquipmentCell_2 = new UsedEquipmentCell();
         usedEquipmentCell_3 = new UsedEquipmentCell();
         usedEquipmentCell_4 = new UsedEquipmentCell();
         usedEquipmentCell_5 = new UsedEquipmentCell();
         usedEquipmentCell_6 = new UsedEquipmentCell();
         _useEquipmentCellContainer = new EquipmentCellContainer();
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_1);
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_2);
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_3);
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_4);
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_5);
         _useEquipmentCellContainer.addEquipmentCell(usedEquipmentCell_6);
         var _loc2_:int = 0;
         var _loc1_:int = int(_useEquipmentCellContainer.equipmentCells.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _useEquipmentCellContainer.equipmentCells[_loc2_].x = 270 + int(_loc2_ / 3) * 70;
            _useEquipmentCellContainer.equipmentCells[_loc2_].y = 165 + _loc2_ % 3 * 60;
            _cellsLayer.addChild(_useEquipmentCellContainer.equipmentCells[_loc2_] as DisplayObject);
            _loc2_++;
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickLookPlayerAttributeBtn",lookPlayerAttribute,true,0,true);
         addEventListener("clickLookDanMedicinesBtn",lookDanMedicine,true,0,true);
         addEventListener("changeCheckBoxState",changeCheckBoxState,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickLookPlayerAttributeBtn",lookPlayerAttribute,true);
         removeEventListener("clickLookDanMedicinesBtn",lookDanMedicine,true);
         removeEventListener("changeCheckBoxState",changeCheckBoxState,true);
      }
      
      private function changeCheckBoxState(param1:UIPassiveEvent) : void
      {
         _currentPlayer.playerVO.isShowFashionShow = param1.data.isCheck;
         _playerShowContainer.refreshPlayerShow(_currentPlayer.playerVO);
         _currentPlayer.changeData();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_lookUpXiuLianBtn:
               lookUpXianLian();
               break;
            case m_lookMedalBtn:
               m_lookMedal();
         }
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         switch(e.currentTarget)
         {
            case _playerAttributePanel:
               if(_playerAttributePanel)
               {
                  _playerAttributePanel.playRemovedAnimation(function():void
                  {
                     onOut(null);
                     if(parent is MyControlPanel)
                     {
                        (parent as MyControlPanel).myPackage.removeDragTarget(_playerAttributePanel);
                     }
                     if(Boolean(_playerAttributePanel) && _playerAttributePanel.parent)
                     {
                        _playerAttributePanel.parent.removeChild(_playerAttributePanel);
                     }
                     if(_playerAttributePanel)
                     {
                        _playerAttributePanel.removeEventListener("clickQuitBtn",quit,true);
                     }
                     if(_playerAttributePanel)
                     {
                        _playerAttributePanel.clear();
                     }
                     _playerAttributePanel = null;
                  },[]);
               }
               break;
            case _danMedicinePanel:
               if(_danMedicinePanel)
               {
                  _danMedicinePanel.playRemovedAnimation(function():void
                  {
                     onOut(null);
                     if(parent is MyControlPanel)
                     {
                        (parent as MyControlPanel).myPackage.removeDragTarget(_danMedicinePanel);
                     }
                     if(Boolean(_danMedicinePanel) && _danMedicinePanel.parent)
                     {
                        _danMedicinePanel.parent.removeChild(_danMedicinePanel);
                     }
                     if(_danMedicinePanel)
                     {
                        _danMedicinePanel.removeEventListener("clickQuitBtn",quit,true);
                     }
                     if(_danMedicinePanel)
                     {
                        _danMedicinePanel.clear();
                     }
                     _danMedicinePanel = null;
                  },[]);
                  break;
               }
         }
      }
      
      private function lookPlayerAttribute(param1:UIBtnEvent) : void
      {
         if(_playerAttributePanel)
         {
            return;
         }
         _playerAttributePanel = new PlayerAttributePanel();
         _playerAttributePanel.refreshPanel(_currentPlayer);
         _playerAttributePanel.x = this.x + 375;
         _playerAttributePanel.y = this.y + 53;
         if(parent is MyControlPanel)
         {
            (parent as MyControlPanel).myPackage.addDragTarget(_playerAttributePanel);
         }
         _playerAttributePanel.addEventListener("clickQuitBtn",quit,true,0,true);
         if(parent is MyControlPanel)
         {
            parent.addChildAt(_playerAttributePanel,parent.getChildIndex((parent as MyControlPanel).topLayer));
         }
         else
         {
            parent.addChildAt(_playerAttributePanel,parent.numChildren);
         }
         _playerAttributePanel.playAddedAnimation();
      }
      
      private function lookDanMedicine(param1:UIBtnEvent) : void
      {
         if(_danMedicinePanel)
         {
            return;
         }
         _danMedicinePanel = new DanMedicinePanel();
         _danMedicinePanel.player = _currentPlayer;
         _danMedicinePanel.refreshPanel(_currentPlayer.playerVO.attackDanMedicineEquipmentVOGrid,_currentPlayer.playerVO.defenceDanMedicineEquipmentVOGrid);
         _danMedicinePanel.x = this.x + 375;
         _danMedicinePanel.y = this.y + 40;
         if(parent is MyControlPanel)
         {
            (parent as MyControlPanel).myPackage.addDragTarget(_danMedicinePanel);
         }
         _danMedicinePanel.addEventListener("clickQuitBtn",quit,true,0,true);
         if(parent is MyControlPanel)
         {
            parent.addChildAt(_danMedicinePanel,parent.getChildIndex((parent as MyControlPanel).topLayer));
         }
         else
         {
            parent.addChildAt(_danMedicinePanel,parent.numChildren);
         }
         _danMedicinePanel.playAddedAnimation();
      }
      
      private function lookUpXianLian() : void
      {
         if(_xiuLianPanel)
         {
            return;
         }
         _xiuLianPanel = new XiuLianPanel();
         var _loc1_:CloseSubUI = new CloseSubUI();
         _loc1_.init(closeLookUpXianLian);
         var _loc2_:MyControlPanel = parent as MyControlPanel;
         _xiuLianPanel.init(_loc1_,_loc2_);
         _xiuLianPanel.setPlayer(_currentPlayer);
         _xiuLianPanel.x = this.x + 385;
         _xiuLianPanel.y = this.y + 20;
         if(parent is MyControlPanel)
         {
            (parent as MyControlPanel).myPackage.addDragTarget(_xiuLianPanel);
         }
         if(parent is MyControlPanel)
         {
            parent.addChildAt(_xiuLianPanel,parent.getChildIndex((parent as MyControlPanel).topLayer));
         }
         else
         {
            parent.addChildAt(_xiuLianPanel,parent.numChildren);
         }
         _xiuLianPanel.playAddedAnimation();
      }
      
      private function m_lookMedal() : void
      {
         if(_medalPanel)
         {
            return;
         }
         _medalPanel = new MedalPanel();
         var _loc1_:CloseSubUI = new CloseSubUI();
         _loc1_.init(closeLookUpMedal);
         var _loc2_:MyControlPanel = parent as MyControlPanel;
         _medalPanel.init(_loc1_,_loc2_);
         _medalPanel.setPlayer(_currentPlayer);
         _medalPanel.x = this.x + 385;
         _medalPanel.y = this.y + 20;
         if(parent is MyControlPanel)
         {
            (parent as MyControlPanel).myPackage.addDragTarget(_medalPanel);
         }
         if(parent is MyControlPanel)
         {
            parent.addChildAt(_medalPanel,parent.getChildIndex((parent as MyControlPanel).topLayer));
         }
         else
         {
            parent.addChildAt(_medalPanel,parent.numChildren);
         }
         _medalPanel.playAddedAnimation();
      }
      
      private function closeLookUpXianLian() : void
      {
         if(_xiuLianPanel)
         {
            _xiuLianPanel.playRemovedAnimation(function():void
            {
               onOut(null);
               if(parent is MyControlPanel)
               {
                  (parent as MyControlPanel).myPackage.removeDragTarget(_xiuLianPanel);
               }
               if(Boolean(_xiuLianPanel) && _xiuLianPanel.parent)
               {
                  _xiuLianPanel.parent.removeChild(_xiuLianPanel);
               }
               if(_xiuLianPanel)
               {
                  _xiuLianPanel.clear();
               }
               _xiuLianPanel = null;
            },[]);
         }
      }
      
      private function closeLookUpMedal() : void
      {
         if(_medalPanel)
         {
            _medalPanel.playRemovedAnimation(function():void
            {
               onOut(null);
               if(parent is MyControlPanel)
               {
                  (parent as MyControlPanel).myPackage.removeDragTarget(_medalPanel);
               }
               if(Boolean(_medalPanel) && _medalPanel.parent)
               {
                  _medalPanel.parent.removeChild(_medalPanel);
               }
               if(_medalPanel)
               {
                  _medalPanel.clear();
               }
               _medalPanel = null;
            },[]);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":{"target":"charactText"},
            "player":_currentPlayer
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

