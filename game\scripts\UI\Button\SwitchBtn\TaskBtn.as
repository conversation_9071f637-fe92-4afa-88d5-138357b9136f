package UI.Button.SwitchBtn
{
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   
   public class TaskBtn extends SwitchBtn
   {
      public function TaskBtn()
      {
         super();
         setTipString("已接受的任务\n需接受土地公公的日常任务");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToTask"));
      }
      
      override protected function dispatchTipEvent() : void
      {
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":"待开放",
            "flag":0
         }));
      }
   }
}

