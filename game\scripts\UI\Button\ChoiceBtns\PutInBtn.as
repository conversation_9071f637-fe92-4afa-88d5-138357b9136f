package UI.Button.ChoiceBtns
{
   import UI.Event.UIBtnEvent;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.ButtonEvent;
   import flash.display.Sprite;
   
   public class PutInBtn extends ChoiceBtn
   {
      public function PutInBtn()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         m_btn = new ButtonLogicShell();
         m_btn.setShow(MyFunction2.returnShowByClassName("PutInBtn") as Sprite);
         addChild(m_btn.getShow());
      }
      
      override protected function clickButton(param1:ButtonEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickPutInBtn"));
      }
   }
}

