package UI.Button.SwitchBtn
{
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MyFunction;
   import UI.XMLSingle;
   
   public class ShopBtn extends SwitchBtn
   {
      public function ShopBtn()
      {
         super();
         setTipString("商店");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToShop"));
      }
      
      override protected function dispatchTipEvent() : void
      {
         var _loc2_:* = undefined;
         var _loc6_:XMLList = XMLSingle.getInstance().vipXML.item;
         var _loc4_:int = int(_loc6_.length());
         var _loc7_:int = 0;
         var _loc3_:Boolean = false;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc2_ = MyFunction.getInstance().excreteString(_loc6_[_loc7_].@privileges);
            for each(var _loc1_ in _loc2_)
            {
               if(_loc1_ == 21)
               {
                  _loc3_ = true;
                  break;
               }
            }
            if(_loc3_)
            {
               break;
            }
            _loc7_++;
         }
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":"VIP" + _loc6_[_loc7_].@vipLevel + "级可开启该功能！",
            "flag":0
         }));
      }
   }
}

