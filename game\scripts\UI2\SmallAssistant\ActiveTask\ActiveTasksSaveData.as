package UI2.SmallAssistant.ActiveTask
{
   import UI.DataManagerParent;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MainLineTask.TaskGoalVOFactory;
   import UI.MainLineTask.TaskGoalVO_MainTask;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class ActiveTasksSaveData extends DataManagerParent
   {
      private var m_time:String;
      
      private var m_gotRewardDatas:Vector.<ActiveTaskRewardSaveData>;
      
      private var m_taskVOs:Vector.<MainLineTaskVO>;
      
      private var m_activeTasksSaveDataListeners:Vector.<IActiveTasksSaveDataListener>;
      
      public function ActiveTasksSaveData()
      {
         super();
         m_gotRewardDatas = new Vector.<ActiveTaskRewardSaveData>();
         m_taskVOs = new Vector.<MainLineTaskVO>();
         m_activeTasksSaveDataListeners = new Vector.<IActiveTasksSaveDataListener>();
      }
      
      override public function clear() : void
      {
         m_time = null;
         ClearUtil.clearObject(m_gotRewardDatas);
         m_gotRewardDatas = null;
         ClearUtil.clearObject(m_taskVOs);
         m_taskVOs = null;
         ClearUtil.nullArr(m_activeTasksSaveDataListeners,false,false,false);
         m_activeTasksSaveDataListeners = null;
         super.clear();
      }
      
      public function addActiveTasksSaveDataListener(param1:IActiveTasksSaveDataListener) : void
      {
         ListenerUtil.addListener(m_activeTasksSaveDataListeners,param1);
      }
      
      public function removeActiveTasksSaveDataListener(param1:IActiveTasksSaveDataListener) : void
      {
         ListenerUtil.removeListener(m_activeTasksSaveDataListeners,param1);
      }
      
      public function initBySaveXML(param1:XML, param2:XML, param3:XML, param4:XML) : void
      {
         var _loc10_:int = 0;
         var _loc11_:MainLineTaskVO = null;
         var _loc5_:String = null;
         var _loc7_:XML = null;
         var _loc6_:* = undefined;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         ClearUtil.clearObject(m_taskVOs);
         m_taskVOs = new Vector.<MainLineTaskVO>();
         var _loc15_:XMLList = param2.task;
         var _loc13_:int = int(_loc15_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc13_)
         {
            _loc5_ = String(_loc15_[_loc10_].@id);
            _loc7_ = param4.task.(@id == _loc5_)[0];
            _loc11_ = new MainLineTaskVO();
            _loc11_.initByXML(_loc7_,param3);
            m_taskVOs.push(_loc11_);
            _loc10_++;
         }
         if(param1 == null)
         {
            return;
         }
         _loc15_ = param1.task;
         _loc13_ = int(!!_loc15_ ? _loc15_.length() : 0);
         var _loc14_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _loc10_ = 0;
         while(_loc10_ < _loc13_)
         {
            _loc5_ = String(_loc15_[_loc10_].@id);
            _loc9_ = int(m_taskVOs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc9_)
            {
               _loc11_ = m_taskVOs[_loc8_];
               if(_loc11_.id == _loc5_)
               {
                  _loc6_ = _loc14_.createTaskGoalsByXML(_loc15_[_loc10_],param3);
                  _loc11_.addTaskGoalsToCurrentTaskGoalVOs2(_loc6_);
                  ClearUtil.nullArr(_loc6_,false,false,false);
                  break;
               }
               _loc8_++;
            }
            _loc14_.clear();
            _loc10_++;
         }
         ClearUtil.clearObject(m_gotRewardDatas);
         m_gotRewardDatas = new Vector.<ActiveTaskRewardSaveData>();
         var _loc12_:Vector.<String> = MyFunction.getInstance().excreteStringToString(param1.@gRIds);
         _loc13_ = !!_loc12_ ? _loc12_.length : 0;
         _loc10_ = 0;
         while(_loc10_ < _loc13_)
         {
            m_gotRewardDatas.push(new ActiveTaskRewardSaveData());
            m_gotRewardDatas[m_gotRewardDatas.length - 1].setRewardId(_loc12_[_loc10_]);
            _loc10_++;
         }
         ClearUtil.clearObject(_loc12_);
         _loc12_ = null;
         time = MyFunction2.resetErrorTime(String(param1.@t));
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc11_:int = 0;
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc10_:MainLineTaskVO = null;
         var _loc5_:XML = null;
         var _loc6_:XML = null;
         var _loc9_:TaskGoalVO_MainTask = null;
         var _loc1_:XML = <ActiveTask />;
         if(time)
         {
            _loc1_.@t = time;
         }
         var _loc3_:Vector.<String> = new Vector.<String>();
         var _loc4_:int = int(m_gotRewardDatas.length);
         _loc11_ = 0;
         while(_loc11_ < _loc4_)
         {
            _loc3_.push(m_gotRewardDatas[_loc11_].getRewardId());
            _loc11_++;
         }
         var _loc2_:String = MyFunction.getInstance().combineStringsToArr(_loc3_);
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
         if(_loc2_)
         {
            _loc1_.@gRIds = _loc2_;
         }
         _loc4_ = int(m_taskVOs.length);
         _loc11_ = 0;
         while(_loc11_ < _loc4_)
         {
            _loc10_ = m_taskVOs[_loc11_];
            _loc5_ = <task />;
            _loc5_.@id = _loc10_.id;
            _loc1_.appendChild(_loc5_);
            _loc7_ = _loc10_.getCurrentTaskGoalNum();
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               _loc9_ = _loc10_.getCurrentTaskGoalByIndex(_loc8_);
               if(_loc9_)
               {
                  _loc6_ = <taskGoal />;
                  _loc6_.@id = _loc9_.id;
                  _loc6_.@num = _loc9_.num;
                  _loc5_.appendChild(_loc6_);
               }
               _loc8_++;
            }
            _loc11_++;
         }
         return _loc1_;
      }
      
      public function resetDataIfNewDay(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(new TimeUtil().newDateIsNewDay(this.time,param1))
         {
            this.time = param1;
            ClearUtil.clearObject(m_gotRewardDatas);
            m_gotRewardDatas.length = 0;
            _loc2_ = int(m_taskVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               m_taskVOs[_loc3_].reset();
               _loc3_++;
            }
         }
      }
      
      public function getTaskVONum() : uint
      {
         return m_taskVOs.length;
      }
      
      public function getTaskVOByIndex(param1:int) : MainLineTaskVO
      {
         return m_taskVOs[param1];
      }
      
      public function addTaskGoalByGameEventStr(param1:String) : void
      {
         var _loc6_:int = 0;
         var _loc5_:Boolean = false;
         var _loc2_:TaskGoalVOFactory = new TaskGoalVOFactory();
         var _loc4_:TaskGoalVO_MainTask = _loc2_.createOneTaskGoalByGameEventStr(param1,XMLSingle.getInstance().mainLineTaskGoalsXML);
         _loc2_.clear();
         var _loc3_:int = !!m_taskVOs ? m_taskVOs.length : 0;
         while(_loc4_ && _loc6_ < _loc3_)
         {
            _loc5_ = Boolean(m_taskVOs[_loc6_].addNewTaskGoal2(_loc4_));
            if(_loc5_)
            {
               dispatchSuccessAddOneTaskGoal();
               _loc4_ = null;
            }
            _loc6_++;
         }
      }
      
      public function getIsGotByRewardId(param1:String) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(m_gotRewardDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_gotRewardDatas[_loc3_].getRewardId() == param1)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function addGotRewardData(param1:ActiveTaskRewardSaveData) : void
      {
         if(getIsGotByRewardId(param1.getRewardId()))
         {
            return;
         }
         m_gotRewardDatas.push(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.time = m_time;
      }
      
      private function dispatchSuccessAddOneTaskGoal() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IActiveTasksSaveDataListener> = m_activeTasksSaveDataListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].successAddOneTaskGoal(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function get time() : String
      {
         return _antiwear.time;
      }
      
      private function set time(param1:String) : void
      {
         _antiwear.time = param1;
      }
   }
}

