package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddRiotCritAndAttckSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addAttack:Number;
      
      private var m_addCrit:Number;
      
      public function AddRiotCritAndAttckSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("attack_mountAdd1",m_targetPlayer.playerVO.get2("attack_mountAdd1") - m_addValue);
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") - addCrit);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addAttack;
         m_targetPlayer.playerVO.set2("attack_mountAdd1",m_targetPlayer.playerVO.get2("attack_mountAdd1") + m_addValue);
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") + addCrit);
      }
      
      public function getAddAttack() : uint
      {
         return addAttack;
      }
      
      public function getAddCrit() : Number
      {
         return addCrit;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addAttack = uint(param1.data.(@att == "addAttack")[0].@value);
         this.addCrit = Number(param1.data.(@att == "addCrit")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.m_addAttack = m_addAttack;
         _antiwear.m_addCrit = m_addCrit;
      }
      
      private function get addAttack() : uint
      {
         return _antiwear.addAttack;
      }
      
      private function set addAttack(param1:uint) : void
      {
         _antiwear.addAttack = param1;
      }
      
      private function get addCrit() : Number
      {
         return _antiwear.m_addCrit;
      }
      
      private function set addCrit(param1:Number) : void
      {
         _antiwear.m_addCrit = param1;
      }
   }
}

