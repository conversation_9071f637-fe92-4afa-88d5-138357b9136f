package UI.VIPPanel
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class RechargeVIPBtn extends Btn
   {
      public function RechargeVIPBtn()
      {
         super();
         setTipString("点击前往充值");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickviprechargeBtn"));
      }
   }
}

