package UI.Equipments.EquipmentXMLPartData
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class EquipmentXMLPartData
   {
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function EquipmentXMLPartData()
      {
         super();
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function init(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc4_:OneEquipmentData = null;
         var _loc3_:XMLList = param1.item;
         var _loc2_:int = int(_loc3_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = new OneEquipmentData();
            _loc4_.initDataByXML(_loc3_[_loc5_]);
            _antiwear[String(_loc3_[_loc5_].@id) + "_price"] = Number(_loc3_[_loc5_].@price);
            _antiwear[String(_loc3_[_loc5_].@id) + "_pkPrice"] = Number(_loc3_[_loc5_].@pkPrice);
            _loc5_++;
         }
      }
      
      public function getEquipmentPrice(param1:String) : Number
      {
         return _antiwear[param1 + "_price"];
      }
      
      public function getEquipmentPKPrice(param1:String) : Number
      {
         return _antiwear[param1 + "_pkPrice"];
      }
   }
}

