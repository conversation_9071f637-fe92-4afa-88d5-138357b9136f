package UI.EquipmentMakeAndUpgrade
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class ShowEqMagicText extends MySprite
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["OpenEquipMagic"];
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_myMagicPanel:EquipmentMagicCreat;
      
      public function ShowEqMagicText()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_myMagicPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_myMagicPanel.closeTextShowPanel();
         }
      }
      
      public function init(param1:EquipmentMagicCreat) : void
      {
         var myEquipmentMagicCreat:EquipmentMagicCreat = param1;
         m_myMagicPanel = myEquipmentMagicCreat;
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("ShowMagicTextPanel") as MovieClip;
            }
            addChild(_show);
            _showMC = new MovieClipPlayLogicShell();
            _showMC.setShow(_show);
            _showMC.gotoAndStop("1");
            initShow();
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(_show["quitBtn"]);
         this.x = 380.35;
         this.y = 82;
      }
   }
}

