package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.GamingUI;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class RGoldRankListBtn extends Btn
   {
      public function RGoldRankListBtn()
      {
         super();
         setTipString("进入夺金排行榜");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("gotoRGRankList"));
         GamingUI.getInstance().openRecaptureGoldRankList();
      }
   }
}

