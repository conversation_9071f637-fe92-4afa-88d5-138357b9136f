package UI.WorldBoss
{
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class WorlBossStartPanel extends MySprite
   {
      private var m_worldBossData:WorldBossData;
      
      private var m_worldBossPanel:WorldBossPanel;
      
      private var m_worldXML:XML;
      
      private var m_show:MovieClip;
      
      private var m_choicePlayerCardBtn:ButtonLogicShell2;
      
      private var m_playerCardContaner:Sprite;
      
      private var m_petCardContainer:Sprite;
      
      private var m_powerBar:CMSXChangeBarLogicShell;
      
      private var m_powerText:TextField;
      
      private var m_lostPowerText:TextField;
      
      private var m_middlePanel:MovieClipPlayLogicShell;
      
      private var m_fightBtnShow:MovieClipPlayLogicShell;
      
      private var m_fightBtn:ButtonLogicShell2;
      
      private var m_tipMessageText:TextField;
      
      private var m_gotoRankListBtn:ButtonLogicShell2;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_bossShowMC:MovieClipPlayLogicShell;
      
      private var m_bossShowBtnGroup:Sprite;
      
      private var m_lookDetailBtn:ButtonLogicShell2;
      
      private var m_upBtn:ButtonLogicShell2;
      
      private var m_downBtn:ButtonLogicShell2;
      
      private var m_rePowerBtn:ButtonLogicShell;
      
      private var m_guWuBtn:ButtonLogicShell2;
      
      private var m_currentAddAttackPercentTxt:TextField;
      
      private var m_totalBossHurtText:TextField;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_choicePlayerPanel:ChoicePlayerPanel;
      
      private var m_bossDetailPanel:BossDetailPanel;
      
      private var m_choicePlayer:Player;
      
      private var m_playerEntity:PlayerEntity;
      
      private var m_petEntity:PetEntity;
      
      private var m_rewards:Vector.<Equipment>;
      
      private var m_rewardVOs:Vector.<EquipmentVO>;
      
      private var m_rewardContainers:Vector.<Sprite>;
      
      private var m_gamingUI:GamingUI;
      
      private var m_bossIds:Array;
      
      private var m_currentBossIndex:int;
      
      private var m_totalBossHurt:Number;
      
      private var m_isStart:Boolean;
      
      public function WorlBossStartPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         if(Boolean(m_bossShowMC) && m_bossShowMC.getShow())
         {
            m_bossShowMC.getShow().removeEventListener("rollOver",onRoll,false);
            m_bossShowMC.getShow().removeEventListener("rollOut",onRoll,false);
         }
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearDisplayObjectInContainer(m_playerCardContaner,false,false);
         m_playerCardContaner = null;
         ClearUtil.clearDisplayObjectInContainer(m_petCardContainer,false,false);
         m_petCardContainer = null;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         m_show = null;
         super.clear();
         m_worldBossData = null;
         m_worldBossPanel = null;
         m_worldXML = null;
         ClearUtil.clearObject(m_choicePlayerCardBtn);
         m_choicePlayerCardBtn = null;
         ClearUtil.clearObject(m_powerBar);
         m_powerBar = null;
         m_powerText = null;
         m_lostPowerText = null;
         ClearUtil.clearObject(m_middlePanel);
         m_middlePanel = null;
         ClearUtil.clearObject(m_fightBtnShow);
         m_fightBtnShow = null;
         ClearUtil.clearObject(m_fightBtn);
         m_fightBtn = null;
         m_tipMessageText = null;
         ClearUtil.clearObject(m_gotoRankListBtn);
         m_gotoRankListBtn = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_bossShowMC);
         m_bossShowMC = null;
         ClearUtil.clearDisplayObjectInContainer(m_bossShowBtnGroup);
         m_bossShowBtnGroup = null;
         ClearUtil.clearObject(m_lookDetailBtn);
         m_lookDetailBtn = null;
         ClearUtil.clearObject(m_upBtn);
         m_upBtn = null;
         ClearUtil.clearObject(m_downBtn);
         m_downBtn = null;
         ClearUtil.clearObject(m_rePowerBtn);
         m_rePowerBtn = null;
         ClearUtil.clearObject(m_guWuBtn);
         m_guWuBtn = null;
         m_currentAddAttackPercentTxt = null;
         m_totalBossHurtText = null;
         m_myLoader = null;
         ClearUtil.clearObject(m_choicePlayerPanel);
         m_choicePlayerPanel = null;
         m_choicePlayer = null;
         ClearUtil.clearObject(m_playerEntity);
         m_playerEntity = null;
         ClearUtil.clearObject(m_petEntity);
         m_petEntity = null;
         ClearUtil.nullArr(m_rewards);
         m_rewards = null;
         m_rewardVOs = null;
         ClearUtil.nullArr(m_rewardContainers);
         m_rewardContainers = null;
         m_gamingUI = null;
         m_bossIds = null;
         ClearUtil.clearObject(m_bossDetailPanel);
         m_bossDetailPanel = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setWorldBossData(param1:WorldBossData) : void
      {
         m_worldBossData = param1;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setWorldBossPanel(param1:WorldBossPanel) : void
      {
         m_worldBossPanel = param1;
      }
      
      public function setWorldBossXML(param1:XML) : void
      {
         m_worldXML = param1;
      }
      
      public function setBossIds(param1:Array, param2:String) : void
      {
         m_bossIds = param1;
         if(Boolean(param2) == false)
         {
            m_currentBossIndex = 0;
         }
         else
         {
            m_currentBossIndex = param1.indexOf(param2);
         }
      }
      
      public function initShow() : void
      {
         m_myLoader.getClass("WorldBoss.swf","StartWorldBossPanel",getStartPanelSuccess,getFailFun);
         m_myLoader.load();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_worldBossData)
         {
            if(m_rePowerBtn)
            {
               if(m_worldBossData.getPowerValue() >= m_worldBossData.getMaxPowerValue())
               {
                  m_rePowerBtn.getShow().visible = false;
               }
               else
               {
                  m_rePowerBtn.getShow().visible = true;
               }
            }
            if(m_powerBar)
            {
               m_powerBar.change(m_worldBossData.getPowerValue() / m_worldBossData.getMaxPowerValue());
            }
            if(m_powerText)
            {
               m_powerText.text = Math.round(m_worldBossData.getPowerValue()) + "/" + m_worldBossData.getMaxPowerValue();
            }
         }
      }
      
      public function setRewardVOs(param1:Vector.<EquipmentVO>) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Equipment = null;
         m_rewardVOs = param1;
         ClearUtil.nullArr(m_rewards);
         m_rewards = null;
         var _loc2_:int = !!m_rewardVOs ? m_rewardVOs.length : 0;
         m_rewards = new Vector.<Equipment>();
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_rewardVOs[_loc4_])
            {
               _loc3_ = MyFunction2.sheatheEquipmentShell(m_rewardVOs[_loc4_]);
               _loc3_.addEventListener("rollOver",onOver,false,0,true);
               _loc3_.addEventListener("rollOut",onOut,false,0,true);
               m_rewards.push(_loc3_);
            }
            _loc4_++;
         }
         refreshRewardVOsShow();
      }
      
      public function setLostPowerValue(param1:int) : void
      {
         if(param1 <= 0)
         {
            m_lostPowerText.text = "";
         }
         else
         {
            m_lostPowerText.text = param1.toString();
         }
      }
      
      public function setTotalBossHurtTextShow(param1:Number) : void
      {
         m_totalBossHurt = param1;
         if(m_totalBossHurtText)
         {
            m_totalBossHurtText.text = param1.toString();
         }
      }
      
      private function getStartPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow1();
      }
      
      private function initShow1() : void
      {
         m_choicePlayerCardBtn = new ButtonLogicShell2();
         m_choicePlayerCardBtn.setShow(m_show["choicePlayerCardBtn"]);
         m_choicePlayerCardBtn.setTipString("点击选择");
         m_playerCardContaner = m_choicePlayerCardBtn.getShow()["cardContainer"];
         m_petCardContainer = m_show["petCardContainer"];
         m_powerBar = new CMSXChangeBarLogicShell();
         m_powerBar.setShow(m_show["powerBar"]);
         m_middlePanel = new MovieClipPlayLogicShell();
         m_middlePanel.setShow(m_show["middlePanel"]);
         m_powerText = m_show["powerText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_powerText);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击退出");
         m_bossShowMC = new MovieClipPlayLogicShell();
         m_bossShowMC.setShow(m_show["BossShowMc"]);
         m_bossShowBtnGroup = m_show["bossShowBtnGroup"];
         m_bossShowBtnGroup.visible = false;
         m_bossShowMC.getShow().addEventListener("rollOver",onRoll,false,0,true);
         m_bossShowMC.getShow().addEventListener("rollOut",onRoll,false,0,true);
         m_bossShowBtnGroup.addEventListener("rollOver",onRoll,false,0,true);
         m_bossShowBtnGroup.addEventListener("rollOut",onRoll,false,0,true);
         m_upBtn = new ButtonLogicShell2();
         m_upBtn.setShow(m_bossShowBtnGroup["upBtn"]);
         m_upBtn.setTipString("点击查看和选择其他世界Boss");
         m_downBtn = new ButtonLogicShell2();
         m_downBtn.setShow(m_bossShowBtnGroup["downBtn"]);
         m_downBtn.setTipString("点击查看和选择其他世界Boss");
         m_rePowerBtn = new ButtonLogicShell();
         m_rePowerBtn.setShow(m_show["rePowerBtn"]);
         m_rePowerBtn.setTipString("点击购买回复体力");
         m_guWuBtn = new ButtonLogicShell2();
         m_guWuBtn.setShow(m_show["guWuBtn"]);
         m_guWuBtn.setTipString("点击购买鼓舞");
         m_currentAddAttackPercentTxt = m_show["currentAddAttackText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_currentAddAttackPercentTxt);
         initMiddlePanelFrame(1,false,null);
         initShow2();
      }
      
      private function initShow2() : void
      {
         setProAttackPercentShow();
         refreshRewardVOsShow();
         setBossShow();
         ClearUtil.clearDisplayObjectInContainer(m_playerCardContaner);
         ClearUtil.clearDisplayObjectInContainer(m_petCardContainer);
         m_powerBar.change(m_worldBossData.getPowerValue() / m_worldBossData.getMaxPowerValue());
         m_powerText.text = m_worldBossData.getPowerValue().toFixed(0) + "/" + m_worldBossData.getMaxPowerValue();
         if(GamingUI.getInstance().player2 == null)
         {
            choicePlayer(GamingUI.getInstance().player1);
         }
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function setProAttackPercentShow() : void
      {
         if(m_currentAddAttackPercentTxt != null && m_worldBossData != null)
         {
            m_currentAddAttackPercentTxt.text = Math.round(m_worldBossData.getProAttackPercent() * 100).toString();
         }
      }
      
      private function clearMiddlePanelFrame() : void
      {
         m_lostPowerText = null;
         ClearUtil.nullArr(m_rewardContainers,false,false,false);
         m_rewardContainers = null;
         ClearUtil.clearObject(m_fightBtnShow);
         m_fightBtnShow = null;
         ClearUtil.clearObject(m_fightBtn);
         m_fightBtn = null;
         ClearUtil.clearObject(m_gotoRankListBtn);
         m_gotoRankListBtn = null;
         ClearUtil.clearObject(m_lookDetailBtn);
         m_lookDetailBtn = null;
         m_totalBossHurtText = null;
         m_tipMessageText = null;
      }
      
      private function initMiddlePanelFrame(param1:int, param2:Boolean, param3:IsCanFightReturn) : void
      {
         var _loc4_:int = 0;
         clearMiddlePanelFrame();
         m_middlePanel.gotoAndStop(param1.toString());
         m_lostPowerText = m_middlePanel.getShow()["lostPowerValueText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_lostPowerText);
         m_rewardContainers = new Vector.<Sprite>();
         _loc4_ = 0;
         while(_loc4_ < 3)
         {
            m_rewardContainers.push(m_middlePanel.getShow()["eqC" + (_loc4_ + 1)]);
            _loc4_++;
         }
         if(param1 == 2)
         {
            m_fightBtnShow = new MovieClipPlayLogicShell();
            m_fightBtnShow.setShow(m_middlePanel.getShow()["fightBtnShow"]);
            if(param2)
            {
               m_fightBtnShow.gotoAndStop("2");
               m_fightBtn = new ButtonLogicShell2();
               m_fightBtn.setShow(m_fightBtnShow.getShow()["fightBtn"]);
               m_fightBtn.setTipString("开始攻击世界Boss");
            }
            else
            {
               m_fightBtnShow.gotoAndStop("1");
            }
            m_tipMessageText = m_middlePanel.getShow()["tipMessageText"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_tipMessageText);
            m_gotoRankListBtn = new ButtonLogicShell2();
            m_gotoRankListBtn.setShow(m_middlePanel.getShow()["gotoRankListBtn"]);
            m_gotoRankListBtn.setTipString("查看世界Boss排行榜");
            m_lookDetailBtn = new ButtonLogicShell2();
            m_lookDetailBtn.setShow(m_middlePanel.getShow()["lookDetailBtn"]);
            m_lookDetailBtn.setTipString("查看世界Boss详细信息");
            m_totalBossHurtText = m_middlePanel.getShow()["totalBossHurtText"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_totalBossHurtText);
            m_tipMessageText.text = param3.message;
            m_totalBossHurtText.text = m_totalBossHurt.toString();
         }
      }
      
      private function refreshRewardVOsShow() : void
      {
         var _loc2_:int = 0;
         if(m_rewards == null)
         {
            return;
         }
         var _loc1_:int = !!m_rewardContainers ? m_rewardContainers.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_rewards.length < _loc2_ + 1)
            {
               break;
            }
            if(m_rewards[_loc2_] != null && m_rewardContainers[_loc2_] != null)
            {
               m_rewardContainers[_loc2_].addChild(m_rewards[_loc2_]);
            }
            _loc2_++;
         }
      }
      
      private function setBossShow() : void
      {
         m_bossShowMC.gotoAndStop("" + (m_currentBossIndex + 1));
         m_worldBossPanel.selectBoss(m_bossIds[m_currentBossIndex]);
         var _loc1_:IsCanFightReturn = new IsCanFightReturn();
         initMiddlePanelFrame(int(Boolean(m_bossIds[m_currentBossIndex])) + 1,m_worldBossPanel.isCanStartFight(_loc1_),_loc1_);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:* = undefined;
         var _loc4_:int = 0;
         var _loc5_:* = param1.button;
         loop0:
         switch(_loc5_)
         {
            case m_choicePlayerCardBtn:
               if(GamingUI.getInstance().player2)
               {
                  if(m_choicePlayerPanel == null)
                  {
                     m_choicePlayerPanel = new ChoicePlayerPanel();
                  }
                  addChild(m_choicePlayerPanel);
                  m_choicePlayerPanel.setLoader(m_myLoader);
                  m_choicePlayerPanel.init();
                  break;
               }
               choicePlayer(GamingUI.getInstance().player1);
               break;
            case m_fightBtn:
               if(m_worldBossPanel.isCanStartFight() != false)
               {
                  if(m_playerEntity)
                  {
                     if(m_worldBossPanel.isAbleGetRewards())
                     {
                        if(m_worldBossPanel.getRewards() == false)
                        {
                           m_worldBossPanel.showWarningBox("玩家1背包已放不下奖励，是否要放弃奖励，开始战斗！",3,{
                              "type":"startFight",
                              "okFunction":startFight
                           });
                           break;
                        }
                        startFight();
                        break;
                     }
                     startFight();
                     break;
                  }
                  m_worldBossPanel.showWarningBox("请选择出战人物！",0);
               }
               break;
            case m_gotoRankListBtn:
               m_worldBossPanel.openRankList();
               break;
            case m_quitBtn:
               if(m_gamingUI)
               {
                  m_gamingUI.closeWorldBossPanel();
               }
               break;
            case m_upBtn:
               if(m_currentBossIndex <= 0)
               {
                  m_currentBossIndex = m_bossIds.length - 1;
               }
               else
               {
                  m_currentBossIndex--;
               }
               setBossShow();
               break;
            case m_downBtn:
               if(m_currentBossIndex >= m_bossIds.length - 1)
               {
                  m_currentBossIndex = 0;
               }
               else
               {
                  m_currentBossIndex++;
               }
               setBossShow();
               break;
            default:
               if((!!m_choicePlayerPanel ? m_choicePlayerPanel.btn1 : null) === _loc5_)
               {
                  choicePlayer(GamingUI.getInstance().player1);
                  ClearUtil.clearObject(m_choicePlayerPanel);
                  m_choicePlayerPanel = null;
                  break;
               }
               switch(_loc5_)
               {
                  case !!m_choicePlayerPanel ? m_choicePlayerPanel.btn2 : null:
                     choicePlayer(GamingUI.getInstance().player2);
                     ClearUtil.clearObject(m_choicePlayerPanel);
                     m_choicePlayerPanel = null;
                     break loop0;
                  case m_rePowerBtn:
                     _loc2_ = int(m_worldXML.@rePowerPointTicket);
                     m_worldBossPanel.showWarningBox("是否花费" + _loc2_ + "点券回复满体力值。",3,{
                        "type":"recoverPower",
                        "okFunction":buyRecoverPower
                     });
                     break loop0;
                  case m_guWuBtn:
                     _loc3_ = MyFunction.getInstance().excreteString(m_worldXML.@proAttackPointTickets);
                     _loc4_ = _loc3_[Math.min(_loc3_.length - 1,m_worldBossData.getBuyNum())];
                     m_worldBossPanel.showWarningBox("是否花费" + _loc4_ + "点券购买鼓舞。",3,{
                        "type":"buyGuWu",
                        "okFunction":buyGuWu
                     });
                     break loop0;
                  case m_lookDetailBtn:
                     ClearUtil.clearObject(m_bossDetailPanel);
                     m_bossDetailPanel = new BossDetailPanel();
                     m_bossDetailPanel.setLoader(m_myLoader);
                     m_bossDetailPanel.setBossId(m_bossIds[m_currentBossIndex]);
                     addChild(m_bossDetailPanel);
                     m_bossDetailPanel.init();
               }
         }
      }
      
      private function buyGuWu() : void
      {
         var worldSaveBossData:WorldBossSaveData;
         var prices:Vector.<int> = MyFunction.getInstance().excreteString(m_worldXML.@proAttackPointTickets);
         var ticketIds:Vector.<String> = MyFunction.getInstance().excreteStringToString(m_worldXML.@proAttackPointTicketIds);
         var price:int = prices[Math.min(prices.length - 1,m_worldBossData.getBuyNum())];
         var ticketId:String = ticketIds[Math.min(m_worldBossData.getBuyNum(),ticketIds.length - 1)];
         var proAttackValue:Number = Number(m_worldXML.@proAttackValue);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买鼓舞";
         worldSaveBossData = m_worldBossData.getSaveData();
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_worldBossPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            worldSaveBossData.addPromoteAttackPercent(proAttackValue);
            setProAttackPercentShow();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },!!m_worldBossPanel ? m_worldBossPanel.showWarningBox : null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function buyRecoverPower() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var worldBossData:WorldBossData;
            var timeStr:String = param1;
            var dataObj:Object = {};
            var ticketId:String = String(m_worldXML.@rePowerPointTicketId);
            var price:int = int(m_worldXML.@rePowerPointTicket);
            dataObj["propId"] = ticketId;
            dataObj["count"] = 1;
            dataObj["price"] = price;
            dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
            dataObj["tag"] = "购买回复体力值";
            worldBossData = m_worldBossData;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
            {
               if(param1["propId"] != ticketId)
               {
                  m_worldBossPanel.showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               worldBossData.setPowerValue(worldBossData.getMaxPowerValue());
               WorldBossSaveData.getInstance().reSetPowerValueSaveData(worldBossData,timeStr);
               setBossShow();
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },!!m_worldBossPanel ? m_worldBossPanel.showWarningBox : null,WarningBoxSingle.getInstance().getTextFontSize());
         },!!m_worldBossPanel ? m_worldBossPanel.showWarningBox : null,true);
      }
      
      private function startFight() : void
      {
         var _loc2_:PlayerEntity = null;
         var _loc1_:PetEntity = null;
         if(m_worldBossPanel.isCanStartFight())
         {
            m_isStart = true;
            _loc2_ = m_playerEntity;
            _loc1_ = m_petEntity;
            m_playerEntity = null;
            m_petEntity = null;
            m_worldBossPanel.startFight(_loc2_,_loc1_);
         }
      }
      
      private function choicePlayer(param1:Player) : void
      {
         var _loc2_:PetEntity = null;
         if(param1 == m_choicePlayer)
         {
            return;
         }
         ClearUtil.clearObject(m_playerEntity);
         m_playerEntity = null;
         ClearUtil.clearObject(m_petEntity);
         m_petEntity = null;
         ClearUtil.clearDisplayObjectInContainer(m_playerCardContaner);
         ClearUtil.clearDisplayObjectInContainer(m_petCardContainer);
         m_choicePlayer = param1;
         m_playerEntity = new PlayerEntity();
         m_playerEntity.setIsShowBar(false);
         m_playerEntity.setLoader(m_myLoader);
         m_playerEntity.setWorldBossXML(m_worldXML);
         m_playerEntity.setPlayerVO(m_choicePlayer.playerVO);
         m_playerCardContaner.addChild(m_playerEntity);
         if(m_choicePlayer.playerVO.pet != null && m_choicePlayer.playerVO.pet.petEquipmentVO != null)
         {
            _loc2_ = new PetEntity();
            _loc2_.setLoader(m_myLoader);
            _loc2_.setWorldBossXML(m_worldXML);
            _loc2_.setPetVO(m_choicePlayer.playerVO.pet.petEquipmentVO);
            m_petEntity = _loc2_;
            m_petCardContainer.addChild(m_petEntity);
         }
      }
      
      private function onRoll(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOut":
               m_bossShowBtnGroup.visible = false;
               break;
            case "rollOver":
               m_bossShowBtnGroup.visible = true;
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         if(m_isStart)
         {
            return;
         }
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
               "equipment":param1.currentTarget,
               "messageBoxMode":1
            }));
         }
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

