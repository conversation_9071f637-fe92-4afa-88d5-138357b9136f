package UI.UIInterface
{
   import UI.Equipments.EquipmentVO.GetServerTimeListenerForLimitEquipmentVO;
   
   public interface ILimitEquipmentVO
   {
      function get TimeLimit() : Number;
      
      function set TimeLimit(param1:Number) : void;
      
      function get initTime() : String;
      
      function set initTime(param1:String) : void;
      
      function get remainingTime() : int;
      
      function set remainingTime(param1:int) : void;
      
      function setGetServerTimeListener(param1:GetServerTimeListenerForLimitEquipmentVO) : void;
   }
}

