package UI.ConsumptionGuide.Button
{
   import UI.ConsumptionGuide.GuideUseEquipment;
   
   public class BuyPlayerExperiencePotionGuideBtn extends GuideBtn
   {
      public function BuyPlayerExperiencePotionGuideBtn(param1:int, param2:Function = null)
      {
         super(param1,param2);
         var _loc3_:GuideUseEquipment = new GuideUseEquipment();
         _loc3_.setShowWarningFun(_showWarningFun);
         _loc3_.setUseAfterFun(param2);
         _loc3_.addUseEqId("11000002");
         _loc3_.addUseEqId("11000004");
         _loc3_.addUseEqId("11000014");
         setGuideEquipment(_loc3_);
      }
   }
}

