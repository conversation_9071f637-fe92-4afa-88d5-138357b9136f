package UI2.Mount.MountData.MountSkillVO
{
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddAttackSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddCritSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddDefenceSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddHpSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddRegHpSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddRegMpSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddRiotCritAndAttckSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddRiotCritSkillVO;
   import UI2.Mount.MountData.MountPassSkillVO_ProPlayer.AddRiotSkillVO;
   import YJFY.Utils.ClearUtil;
   
   public class MountSkillVOFactory
   {
      private var m_classNamePairs:Object;
      
      public function MountSkillVOFactory()
      {
         super();
         m_classNamePairs = {
            "AddRegMpSkillVO":AddRegMpSkillVO,
            "AddDefenceSkillVO":AddDefenceSkillVO,
            "AddRegHpSkillVO":AddRegHpSkillVO,
            "AddAttackSkillVO":AddAttackSkillVO,
            "AddHpSkillVO":AddHpSkillVO,
            "AddRiotSkillVO":AddRiotSkillVO,
            "AddCritSkillVO":AddCritSkillVO,
            "AddRiotCritSkillVO":AddRiotCritSkillVO,
            "AddRiotCritAndAttckSkillVO":AddRiotCritAndAttckSkillVO
         };
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_classNamePairs);
         m_classNamePairs = null;
      }
      
      public function createSkillVOByClassId(param1:String) : MountSkillVO
      {
         var _loc2_:Class = m_classNamePairs[param1];
         return new _loc2_();
      }
      
      public function createNewMountSkillVO(param1:String, param2:uint) : MountSkillVO
      {
         var _loc5_:MountSkillVO = null;
         var _loc4_:XML = XMLSingle.getInstance().mountSkillsXML.skill.(@skillId == param1)[0];
         if(_loc4_ == null)
         {
            throw new Error("try to create skill (skillId:",param1 + ") fail");
         }
         var _loc3_:String = String(_loc4_.@classId);
         _loc5_ = createSkillVOByClassId(_loc3_);
         _loc5_.initFromXML(param1,param2,XMLSingle.getInstance().mountSkillsXML);
         return _loc5_;
      }
   }
}

