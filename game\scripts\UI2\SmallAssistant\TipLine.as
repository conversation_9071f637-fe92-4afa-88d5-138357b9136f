package UI2.SmallAssistant
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class TipLine
   {
      private var m_text1:TextField;
      
      private var m_gotoBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_tipData:TipData;
      
      public function TipLine()
      {
         super();
         m_gotoBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         m_text1 = null;
         ClearUtil.clearObject(m_gotoBtn);
         m_gotoBtn = null;
         m_show = null;
         m_tipData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setTipData(param1:TipData) : void
      {
         m_tipData = param1;
         initShow2();
      }
      
      public function getTipData() : TipData
      {
         return m_tipData;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getGotoBtn() : ButtonLogicShell2
      {
         return m_gotoBtn;
      }
      
      private function initShow() : void
      {
         m_text1 = m_show["tipText1"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_text1);
         m_gotoBtn.setShow(m_show["gotoBtn"]);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_tipData == null)
         {
            return;
         }
         m_text1.text = m_tipData.getText1();
      }
   }
}

