package UI.EquipEquipmentFuns
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.PocketEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.MyControlPanel;
   import UI.MyFunction2;
   import UI.Other.UseGiftBagPanel;
   import UI.Players.Player;
   import UI.XMLSingle;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   
   public class EquipPocket
   {
      private const m_const_pocketType_giftBag:String = "giftBag";
      
      private const m_const_pocketType_hongBao:String = "hongBao";
      
      private const m_const_giftBagPath:String = "giftBag/giftBag";
      
      private var _useGiftBagPanel:UseGiftBagPanel;
      
      public function EquipPocket()
      {
         super();
      }
      
      public function clear() : void
      {
         if(_useGiftBagPanel)
         {
            _useGiftBagPanel.clear();
         }
         _useGiftBagPanel = null;
      }
      
      public function EquipPotionEquipmentVOAction(param1:PocketEquipmentVO, param2:Player, param3:IEquipEqListener) : void
      {
         var myControlPanel:MyControlPanel;
         var xmlName:String;
         var str:String;
         var xml:XML;
         var pocketEquipmentVO:PocketEquipmentVO = param1;
         var player:Player = param2;
         var equipEqListener:IEquipEqListener = param3;
         if(!Boolean(pocketEquipmentVO.value) || pocketEquipmentVO.value.substr(pocketEquipmentVO.value.length - 4,4) != ".xml")
         {
            return;
         }
         if(player == null || player.playerVO == null)
         {
            return;
         }
         myControlPanel = GamingUI.getInstance().internalPanel;
         if(myControlPanel == null)
         {
            return;
         }
         xmlName = pocketEquipmentVO.value.substring(0,pocketEquipmentVO.value.length - 4);
         str = xmlName;
         if(xmlName.substr(0,15) == "giftBag/giftBag")
         {
            xmlName = xmlName.substring(0,15);
         }
         MyFunction2.loadXMLFunction(xmlName,function(param1:XML):void
         {
            xml = param1;
            if(xmlName.substr(0,15) == "giftBag/giftBag")
            {
               str = str.substring(8);
               xml = xml[0].data.(@value == str)[0];
            }
            var _loc2_:String = String(xml.@pocketType);
            switch(_loc2_)
            {
               case "giftBag":
                  useGiftBag_Pocket(pocketEquipmentVO,xml,player,myControlPanel,equipEqListener);
                  break;
               case "hongBao":
                  useHongBao_Pocket(pocketEquipmentVO,xml,player,myControlPanel,equipEqListener);
                  break;
               default:
                  throw new Error();
            }
         },equipEqListener.showWarning,true);
      }
      
      private function useHongBao_Pocket(param1:PocketEquipmentVO, param2:XML, param3:Player, param4:MyControlPanel, param5:IEquipEqListener) : void
      {
         var i:int;
         var xmllist:XMLList;
         var length:int;
         var totalProWeight:Number;
         var r:Number;
         var startPer:Number;
         var endPer:Number;
         var saveinfo:SaveTaskInfo;
         var pocketEquipmentVO:PocketEquipmentVO = param1;
         var xml:XML = param2;
         var player:Player = param3;
         var myControlPanel:MyControlPanel = param4;
         var equipEqListener:IEquipEqListener = param5;
         var pocketEquipmentName:String = pocketEquipmentVO.name;
         var clonePocketEquipmetVO:EquipmentVO = pocketEquipmentVO.clone();
         var index:int = int(player.playerVO.packageEquipmentVOs.indexOf(pocketEquipmentVO));
         if(index == -1)
         {
            if(equipEqListener)
            {
               equipEqListener.showWarning("物品已不存在, 使用失败！",0);
               equipEqListener.actionAfterUnableUse();
            }
            return;
         }
         if(pocketEquipmentVO is StackEquipmentVO)
         {
            (pocketEquipmentVO as StackEquipmentVO).num -= 1;
            if((pocketEquipmentVO as StackEquipmentVO).num == 0)
            {
               player.playerVO.packageEquipmentVOs[index] = null;
               pocketEquipmentVO.clear();
               pocketEquipmentVO = null;
            }
         }
         else
         {
            player.playerVO.packageEquipmentVOs[index] = null;
            pocketEquipmentVO.clear();
            pocketEquipmentVO = null;
         }
         xmllist = xml.item;
         length = int(!!xmllist ? xmllist.length() : 0);
         totalProWeight = 0;
         i = 0;
         while(i < length)
         {
            totalProWeight += Number(xmllist[i].@proWeight);
            ++i;
         }
         r = Math.random();
         startPer = 0;
         endPer = 0;
         i = 0;
         while(i < length)
         {
            endPer = startPer + Number(xmllist[i].@proWeight) / totalProWeight;
            if(r >= startPer && r < endPer)
            {
               player.playerVO.packageEquipmentVOs[index] = XMLSingle.getEquipmentVOByID(int(xmllist[i].@id),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false);
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               saveinfo.successFunc = function():void
               {
                  GamingUI.getInstance().refresh(2);
                  myControlPanel.showWarning("打开" + pocketEquipmentName + "获得了" + player.playerVO.packageEquipmentVOs[index].name,0);
               };
               saveinfo.failFunc = function():void
               {
                  player.playerVO.packageEquipmentVOs[index] = clonePocketEquipmetVO;
                  myControlPanel.showWarning(pocketEquipmentName + "使用失败",0);
               };
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame();
               break;
            }
            startPer = endPer;
            i++;
         }
      }
      
      private function useGiftBag_Pocket(param1:PocketEquipmentVO, param2:XML, param3:Player, param4:MyControlPanel, param5:IEquipEqListener) : void
      {
         var pocketEquipmentVO:PocketEquipmentVO = param1;
         var xml:XML = param2;
         var player:Player = param3;
         var myControlPanel:MyControlPanel = param4;
         var equipEqListener:IEquipEqListener = param5;
         if(_useGiftBagPanel)
         {
            if(_useGiftBagPanel.parent)
            {
               _useGiftBagPanel.parent.removeChild(_useGiftBagPanel);
            }
            _useGiftBagPanel.clear();
            _useGiftBagPanel = null;
         }
         _useGiftBagPanel = new UseGiftBagPanel();
         myControlPanel.addChildAt(_useGiftBagPanel,myControlPanel.getChildIndex(myControlPanel.topLayer));
         _useGiftBagPanel.init2(xml,player,function():void
         {
            var index:int = int(player.playerVO.packageEquipmentVOs.indexOf(pocketEquipmentVO));
            if(index == -1)
            {
               if(equipEqListener)
               {
                  equipEqListener.showWarning("物品已不存在, 使用失败！",0);
                  equipEqListener.actionAfterUnableUse();
               }
               return;
            }
            MyFunction2.addEquipmentVOs(_useGiftBagPanel.giftBagEquipments,player,equipEqListener.showWarning,function():void
            {
               if(pocketEquipmentVO is StackEquipmentVO)
               {
                  (pocketEquipmentVO as StackEquipmentVO).num -= 1;
                  if((pocketEquipmentVO as StackEquipmentVO).num == 0)
                  {
                     player.playerVO.packageEquipmentVOs[index] = null;
                     pocketEquipmentVO.clear();
                     pocketEquipmentVO = null;
                  }
               }
               else
               {
                  player.playerVO.packageEquipmentVOs[index] = null;
                  pocketEquipmentVO.clear();
                  pocketEquipmentVO = null;
               }
               if(_useGiftBagPanel)
               {
                  _useGiftBagPanel.clear();
               }
               _useGiftBagPanel = null;
               if(equipEqListener)
               {
                  equipEqListener.actionAfterUse();
               }
            },["背包空间不足!, 使用失败！",0],null);
         },null,function():void
         {
            if(_useGiftBagPanel)
            {
               _useGiftBagPanel.clear();
            }
            _useGiftBagPanel = null;
         },null);
      }
   }
}

