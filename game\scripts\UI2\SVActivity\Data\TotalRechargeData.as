package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   
   public class TotalRechargeData extends DataManagerParent
   {
      private var m_totalRecharge:Number;
      
      public function TotalRechargeData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function getTotalRecharge() : Number
      {
         return totalRecharge;
      }
      
      public function setTotalRecharge(param1:Number) : void
      {
         totalRecharge = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.totalRecharge = m_totalRecharge = 0;
      }
      
      private function get totalRecharge() : Number
      {
         return _antiwear.totalRecharge;
      }
      
      private function set totalRecharge(param1:Number) : void
      {
         _antiwear.totalRecharge = param1;
      }
   }
}

