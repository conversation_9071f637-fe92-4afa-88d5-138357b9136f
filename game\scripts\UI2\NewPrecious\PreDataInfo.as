package UI2.NewPrecious
{
   import UI.DataManagerParent;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class PreDataInfo extends DataManagerParent
   {
      private static var _instance:PreDataInfo;
      
      private var m_dataTime:String;
      
      private var m_list:Vector.<InfoData>;
      
      private var m_savelist:Vector.<InfoData>;
      
      private var m_vip:Vector.<int>;
      
      private var m_freenum:Vector.<int>;
      
      private var m_ticketId:int;
      
      private var m_money:int;
      
      private var m_usenum:int = 0;
      
      private var m_id:String;
      
      private var m_rulenum:int;
      
      public var ruleShowed:Boolean = false;
      
      public function PreDataInfo()
      {
         super();
         m_list = new Vector.<InfoData>();
         m_savelist = new Vector.<InfoData>();
         m_vip = new Vector.<int>();
         m_freenum = new Vector.<int>();
      }
      
      public static function getInstance() : PreDataInfo
      {
         if(_instance == null)
         {
            _instance = new PreDataInfo();
         }
         return _instance;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.dataTime = m_dataTime;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_list);
         m_list = null;
         ClearUtil.clearObject(m_savelist);
         m_savelist = null;
         ClearUtil.clearObject(m_vip);
         m_vip = null;
         ClearUtil.clearObject(m_freenum);
         m_freenum = null;
         _instance = null;
         m_dataTime = null;
         super.clear();
      }
      
      public function initXML(param1:XML) : void
      {
         var _loc3_:InfoData = null;
         var _loc2_:XMLList = null;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         ClearUtil.clearObject(m_list);
         m_list = null;
         m_list = new Vector.<InfoData>();
         var _loc5_:XMLList = param1.info;
         var _loc4_:int = int(_loc5_.length());
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc3_ = new InfoData();
            _loc3_.id = String(_loc5_[_loc7_].@fuBenId);
            _loc3_.maxChNum = int(_loc5_[_loc7_].@maxChNum);
            _loc3_.needLevel = int(_loc5_[_loc7_].@needLevel);
            _loc3_.lockDescription = String(_loc5_[_loc7_].@lockDescription);
            _loc3_.showFrameLabel = String(_loc5_[_loc7_].@showFrameLabel);
            _loc3_.levelXMLPath = String(_loc5_[_loc7_].@levelXMLPath);
            _loc3_.description = String(_loc5_[_loc7_].@description);
            _loc3_.remainNum = _loc3_.maxChNum;
            _loc2_ = _loc5_[_loc7_].showEq;
            _loc6_ = 0;
            while(_loc6_ < _loc2_.length())
            {
               _loc3_.equiplist.push(int(_loc2_[_loc6_].@id));
               _loc6_++;
            }
            _loc3_.initEquipList();
            m_list.push(_loc3_);
            _loc7_++;
         }
         loadVipNum(param1);
      }
      
      public function loadVipNum(param1:XML) : void
      {
         m_ticketId = int(param1.buyvalue[0].@ticketId);
         m_money = int(param1.buyvalue[0].@value);
      }
      
      public function getRuleNum() : int
      {
         return m_rulenum;
      }
      
      public function setRuleNum(param1:int) : void
      {
         m_rulenum = param1;
      }
      
      public function getAmount() : int
      {
         return m_list.length;
      }
      
      public function getTicketId() : int
      {
         return m_ticketId;
      }
      
      public function getMoney() : int
      {
         return m_money;
      }
      
      public function getDataByIndex(param1:int) : InfoData
      {
         if(param1 >= 0 && param1 < m_list.length)
         {
            return m_list[param1];
         }
         return null;
      }
      
      public function setNumByIndex(param1:String) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:InfoData = null;
         var _loc2_:Boolean = false;
         _loc5_ = 0;
         while(_loc5_ < m_savelist.length)
         {
            if(m_savelist[_loc5_].id == param1)
            {
               m_savelist[_loc5_].remainNum--;
               _loc2_ = true;
               break;
            }
            _loc5_++;
         }
         if(_loc2_ == false)
         {
            _loc3_ = 0;
            while(_loc3_ < m_list.length)
            {
               if(m_list[_loc3_].id == param1)
               {
                  _loc4_ = new InfoData();
                  _loc4_.id = param1;
                  _loc4_.remainNum = m_list[_loc3_].maxChNum - 1;
                  m_savelist.push(_loc4_);
               }
               _loc3_++;
            }
         }
      }
      
      public function getNumByIndex(param1:String) : int
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         _loc2_ = 0;
         while(_loc2_ < m_savelist.length)
         {
            if(m_savelist[_loc2_].id == param1)
            {
               return m_savelist[_loc2_].remainNum;
            }
            _loc2_++;
         }
         _loc2_ = 0;
         while(_loc2_ < m_list.length)
         {
            if(m_list[_loc2_].id == param1)
            {
               return m_list[_loc2_].maxChNum;
            }
            _loc2_++;
         }
         return 0;
      }
      
      public function setCurrId(param1:String) : void
      {
         m_id = param1;
      }
      
      public function getCurrId() : String
      {
         return m_id;
      }
      
      public function refreshdata(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(checknewDay(dataTime,param1) == true)
         {
            dataTime = param1;
            _loc3_ = 0;
            while(_loc3_ < m_savelist.length)
            {
               _loc2_ = 0;
               while(_loc2_ < m_list.length)
               {
                  if(m_savelist[_loc3_].id == m_list[_loc2_].id)
                  {
                     m_savelist[_loc3_].remainNum = m_list[_loc3_].maxChNum;
                  }
                  _loc2_++;
               }
               _loc3_++;
            }
            m_usenum = 0;
         }
      }
      
      private function checknewDay(param1:String, param2:String) : Boolean
      {
         var _loc3_:Boolean = false;
         if(TimeUtil.getTimeUtil().stringToDate(param1).getFullYear() != TimeUtil.getTimeUtil().stringToDate(param2).getFullYear() || TimeUtil.getTimeUtil().stringToDate(param1).getMonth() != TimeUtil.getTimeUtil().stringToDate(param2).getMonth() || TimeUtil.getTimeUtil().stringToDate(param1).getDay() != TimeUtil.getTimeUtil().stringToDate(param2).getDay())
         {
            _loc3_ = true;
         }
         return _loc3_;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc4_:InfoData = null;
         var _loc3_:XMLList = null;
         var _loc2_:int = 0;
         if(param1.hasOwnProperty("lingfu"))
         {
            m_savelist.length = 0;
            m_usenum = int(param1.lingfu[0].@usenum);
            m_rulenum = int(param1.lingfu[0].@rulenum);
            dataTime = MyFunction2.resetErrorTime(String(param1.lingfu[0].@t));
            _loc3_ = param1.lingfu[0].one;
            _loc2_ = int(!!_loc3_ ? _loc3_.length() : 0);
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc4_ = new InfoData();
               _loc4_.id = String(_loc3_[_loc5_].@id);
               _loc4_.remainNum = int(_loc3_[_loc5_].@n);
               m_savelist.push(_loc4_);
               _loc5_++;
            }
         }
         else
         {
            m_savelist.length = 0;
            dataTime = TimeUtil.getTimeUtil().getTimeStr();
            m_rulenum = 0;
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc2_:int = 0;
         var _loc1_:XML = <lingfu />;
         if(dataTime)
         {
            _loc1_.@t = dataTime;
         }
         _loc1_.@usenum = m_usenum;
         _loc1_.@rulenum = m_rulenum;
         _loc2_ = 0;
         while(_loc2_ < m_savelist.length)
         {
            _loc1_.appendChild(m_savelist[_loc2_].exportSaveXML());
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function getUseNum() : int
      {
         return m_usenum;
      }
      
      public function setUseNum(param1:int) : void
      {
         m_usenum = param1;
      }
      
      public function isResetData(param1:String) : Boolean
      {
         if(new TimeUtil().newDateIsNewDay(dataTime,param1))
         {
            return true;
         }
         return false;
      }
      
      public function resetData(param1:String) : void
      {
         this.dataTime = param1;
      }
      
      private function get dataTime() : String
      {
         return _antiwear.dataTime;
      }
      
      private function set dataTime(param1:String) : void
      {
         _antiwear.dataTime = param1;
      }
   }
}

