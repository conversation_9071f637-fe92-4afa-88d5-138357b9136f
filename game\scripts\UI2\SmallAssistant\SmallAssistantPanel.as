package UI2.SmallAssistant
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MySprite;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import YJFY.GameEvent;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class SmallAssistantPanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_showsMC:MovieClipPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_activeTaskBtn:MySwitchBtnLogicShell;
      
      private var m_activeTaskNumShowMC:MovieClipPlayLogicShell;
      
      private var m_activeTaskRedNum:MultiPlaceNumLogicShell2;
      
      private var m_activeTaskYellowNum:MultiPlaceNumLogicShell2;
      
      private var m_levelTaskBtn:MySwitchBtnLogicShell;
      
      private var m_levelTaskNumShowMC:MovieClipPlayLogicShell;
      
      private var m_levelTaskRedNum:MultiPlaceNumLogicShell2;
      
      private var m_levelTaskYellowNum:MultiPlaceNumLogicShell2;
      
      private var m_tipBtn:MySwitchBtnLogicShell;
      
      private var m_LineGiftBtn:MySwitchBtnLogicShell;
      
      private var m_activeTaskShowPart:ActiveTaskShowPart;
      
      private var m_levelTaskShowPart:LevelTaskShowPart;
      
      private var m_tipShowPart:TipShowPart;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_tipmc:MovieClip;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_smallAssistant:SmallAssistant;
      
      private var m_smallAssistantData:SmallAssistantData;
      
      public function SmallAssistantPanel()
      {
         super();
         m_showsMC = new MovieClipPlayLogicShell();
         m_quitBtn = new ButtonLogicShell2();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_activeTaskBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_activeTaskBtn);
         m_levelTaskBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_levelTaskBtn);
         m_tipBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_tipBtn);
         m_LineGiftBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_LineGiftBtn);
         m_activeTaskNumShowMC = new MovieClipPlayLogicShell();
         m_levelTaskNumShowMC = new MovieClipPlayLogicShell();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         GameEvent.eventDispacher.addEventListener("smallonlinetip",refreshOnlineTip);
      }
      
      override public function clear() : void
      {
         GameEvent.eventDispacher.removeEventListener("smallonlinetip",refreshOnlineTip);
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showsMC);
         m_showsMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_activeTaskBtn);
         m_activeTaskBtn = null;
         ClearUtil.clearObject(m_activeTaskNumShowMC);
         m_activeTaskNumShowMC = null;
         ClearUtil.clearObject(m_activeTaskRedNum);
         m_activeTaskRedNum = null;
         ClearUtil.clearObject(m_activeTaskYellowNum);
         m_activeTaskYellowNum = null;
         ClearUtil.clearObject(m_levelTaskBtn);
         m_levelTaskBtn = null;
         ClearUtil.clearObject(m_levelTaskNumShowMC);
         m_levelTaskNumShowMC = null;
         ClearUtil.clearObject(m_levelTaskRedNum);
         m_levelTaskRedNum = null;
         ClearUtil.clearObject(m_levelTaskYellowNum);
         m_levelTaskYellowNum = null;
         ClearUtil.clearObject(m_tipBtn);
         m_tipBtn = null;
         ClearUtil.clearObject(m_LineGiftBtn);
         m_LineGiftBtn = null;
         ClearUtil.clearObject(m_activeTaskShowPart);
         m_activeTaskShowPart = null;
         ClearUtil.clearObject(m_levelTaskShowPart);
         m_levelTaskShowPart = null;
         ClearUtil.clearObject(m_tipShowPart);
         m_tipShowPart = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         m_versionControl = null;
         m_smallAssistant = null;
         m_smallAssistantData = null;
         super.clear();
      }
      
      public function init(param1:SmallAssistant, param2:IProgressShow, param3:IVersionControl, param4:SmallAssistantData) : void
      {
         m_smallAssistant = param1;
         m_loadUI = param2;
         m_versionControl = param3;
         m_smallAssistantData = param4;
         if(m_show)
         {
            throw new Error("出错了");
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getClass("UISprite2/SmallAssistant.swf","SmallAssistantPanel",getShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      public function refreshNumTipShow() : void
      {
         var _loc2_:* = 0;
         var _loc1_:* = 0;
         _loc2_ = m_smallAssistantData.getActiveTasksRedTipNum();
         if(_loc2_)
         {
            activeTaskNumShowMCInitRedFrame();
            m_activeTaskRedNum.showNum(_loc2_);
         }
         else
         {
            _loc1_ = m_smallAssistantData.getActiveTaskYellowTipNum();
            if(_loc1_)
            {
               activeTaskNumShowMCInitYellowFrame();
               m_activeTaskYellowNum.showNum(_loc1_);
            }
            else
            {
               activeTaskNumShowMCInitNothingFrame();
            }
         }
         _loc1_ = m_smallAssistantData.getLevelTasksYellowTipNum();
         if(_loc1_)
         {
            levelTaskNumShowMCInitYellowFrame();
            m_levelTaskYellowNum.showNum(_loc1_);
         }
         else
         {
            levelTaskNumShowMCInitNothingFrame();
         }
      }
      
      private function initShow() : void
      {
         m_showsMC.setShow(m_show["showsMC"]);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_activeTaskBtn.setShow(m_show["activeTaskBtn"]);
         m_activeTaskNumShowMC.setShow(m_show["numShow_activeTask"]);
         activeTaskNumShowMCInitNothingFrame();
         m_levelTaskBtn.setShow(m_show["levelTaskBtn"]);
         m_levelTaskNumShowMC.setShow(m_show["numShow_levelTask"]);
         levelTaskNumShowMCInitNothingFrame();
         m_tipBtn.setShow(m_show["tipBtn"]);
         m_LineGiftBtn.setShow(m_show["btnLineGift"]);
         m_tipmc = m_show["numShow_online"] as MovieClip;
         refreshOnlineTip(null);
         initShow2();
      }
      
      private function initShow2() : void
      {
         refreshNumTipShow();
         m_activeTaskBtn.turnActiveAndDispatchEvent();
      }
      
      public function refreshOnlineTip(param1:Event) : void
      {
         if(OnLineGiftBagData.getInstance().remainTime <= 0)
         {
            m_tipmc.visible = true;
         }
         else
         {
            m_tipmc.visible = false;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_activeTaskBtn:
               showFrameInit_ActiveTask();
               break;
            case m_levelTaskBtn:
               showFrameInit_LevelTask();
               break;
            case m_tipBtn:
               showFrameInit_tipShow();
               break;
            case m_LineGiftBtn:
               showFrameInit_LineGift();
               break;
            case m_quitBtn:
               m_smallAssistant.closeSmallAssistantPanel();
         }
      }
      
      private function showsFrameClear() : void
      {
         ClearUtil.clearObject(m_activeTaskShowPart);
         m_activeTaskShowPart = null;
         ClearUtil.clearObject(m_levelTaskShowPart);
         m_levelTaskShowPart = null;
         ClearUtil.clearObject(m_tipShowPart);
         m_tipShowPart = null;
         GamingUI.getInstance().closeOnLineGiftBagPanel2();
      }
      
      private function showFrameInit_ActiveTask() : void
      {
         showsFrameClear();
         m_showsMC.gotoAndStop("activeTask");
         m_activeTaskShowPart = new ActiveTaskShowPart();
         m_activeTaskShowPart.init(m_showsMC.getShow()["activeTaskShow"],m_smallAssistantData,this);
      }
      
      private function showFrameInit_LevelTask() : void
      {
         showsFrameClear();
         m_showsMC.gotoAndStop("levelTask");
         m_levelTaskShowPart = new LevelTaskShowPart();
         m_levelTaskShowPart.init(m_showsMC.getShow()["levelTaskShow"],m_smallAssistantData,this);
      }
      
      private function showFrameInit_tipShow() : void
      {
         showsFrameClear();
         m_showsMC.gotoAndStop("tip");
         m_tipShowPart = new TipShowPart();
         m_tipShowPart.init(m_showsMC.getShow()["tipShow"],m_smallAssistantData,this);
      }
      
      private function showFrameInit_LineGift() : void
      {
         showsFrameClear();
         m_showsMC.gotoAndStop("lineGift");
         GamingUI.getInstance().openOnLineGiftBagPanel2(m_showsMC.getShow()["lineMC"] as MovieClip);
      }
      
      private function activeTaskNumShowMCFrameClear() : void
      {
         ClearUtil.clearObject(m_activeTaskRedNum);
         m_activeTaskRedNum = null;
         ClearUtil.clearObject(m_activeTaskYellowNum);
         m_activeTaskYellowNum = null;
      }
      
      private function activeTaskNumShowMCInitNothingFrame() : void
      {
         activeTaskNumShowMCFrameClear();
         m_activeTaskNumShowMC.gotoAndStop("nothing");
      }
      
      private function activeTaskNumShowMCInitRedFrame() : void
      {
         activeTaskNumShowMCFrameClear();
         m_activeTaskNumShowMC.gotoAndStop("red");
         m_activeTaskRedNum = new MultiPlaceNumLogicShell2();
         m_activeTaskRedNum.setShow(m_activeTaskNumShowMC.getShow()["num"]);
      }
      
      private function activeTaskNumShowMCInitYellowFrame() : void
      {
         activeTaskNumShowMCFrameClear();
         m_activeTaskNumShowMC.gotoAndStop("yellow");
         m_activeTaskYellowNum = new MultiPlaceNumLogicShell2();
         m_activeTaskYellowNum.setShow(m_activeTaskNumShowMC.getShow()["num"]);
      }
      
      private function levelTaskNumShowMCFrameClear() : void
      {
         ClearUtil.clearObject(m_levelTaskRedNum);
         m_levelTaskRedNum = null;
         ClearUtil.clearObject(m_levelTaskYellowNum);
         m_levelTaskYellowNum = null;
      }
      
      private function levelTaskNumShowMCInitNothingFrame() : void
      {
         levelTaskNumShowMCFrameClear();
         m_levelTaskNumShowMC.gotoAndStop("nothing");
      }
      
      private function levelTaskNumShowMCInitRedFrame() : void
      {
         levelTaskNumShowMCFrameClear();
         m_levelTaskNumShowMC.gotoAndStop("red");
         m_levelTaskRedNum = new MultiPlaceNumLogicShell2();
         m_levelTaskRedNum.setShow(m_levelTaskNumShowMC.getShow()["num"]);
      }
      
      private function levelTaskNumShowMCInitYellowFrame() : void
      {
         levelTaskNumShowMCFrameClear();
         m_levelTaskNumShowMC.gotoAndStop("yellow");
         m_levelTaskYellowNum = new MultiPlaceNumLogicShell2();
         m_levelTaskYellowNum.setShow(m_levelTaskNumShowMC.getShow()["num"]);
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         m_smallAssistant.closeSmallAssistantPanel();
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

