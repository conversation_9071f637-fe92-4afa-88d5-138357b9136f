package UI.SocietySystem.SeverLink
{
   import UI.EnterFrameTime;
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.InformationBodySeverDetail;
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.UP_OtherSeverDetail;
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.UP_SeverDetail;
   import UI.SocietySystem.SeverLink.InformationBodySeverDetails.UP_VerifySeverDetail;
   import UI.SocietySystem.SeverLink.Test.SocietySystemTestData;
   import UI.VersionControl;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.Socket;
   import flash.utils.ByteArray;
   import flash.utils.setTimeout;
   
   public class SocketManager
   {
      public static const MAX_CHECK_VALUE:uint = 4000000000;
      
      private const m_SendHeartBeatDefautInterval:uint = 600000;
      
      private const m_const_connectSuccessDuration:int = 200;
      
      private const m_const_downInforDuration:int = 200;
      
      private var m_clear:ClearHelper;
      
      private var m_societySystemTestData:SocietySystemTestData;
      
      private var m_main:Sprite;
      
      private var m_socket:Socket;
      
      private var m_socketListeners:Vector.<ISocketListener>;
      
      private var m_currentInformationLength:int;
      
      private var m_heartBeat:UP_InformationBody;
      
      private var m_sendHeartBeatInterval:uint;
      
      private var m_lastSendTime:Number;
      
      private var m_checkValue:uint;
      
      private var m_session:int;
      
      private var m_encryptByteArr:ByteArray;
      
      private var m_writtenInformationBodys:Vector.<InformationBody>;
      
      private var m_versionControl:VersionControl;
      
      public function SocketManager()
      {
         super();
         m_encryptByteArr = new ByteArray();
         m_encryptByteArr.endian = "littleEndian";
         m_encryptByteArr.writeByte(70);
         m_encryptByteArr.position = 0;
         m_clear = new ClearHelper();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         ClearUtil.nullArr(m_writtenInformationBodys);
         m_writtenInformationBodys = null;
         SocietySystemTestData;
         m_main = null;
         try
         {
            if(m_socket)
            {
               m_socket.close();
            }
            trace("调用socket close（when clear）");
         }
         catch(error:Error)
         {
            trace(error.message);
         }
         m_socket = null;
         ClearUtil.nullArr(m_socketListeners);
         m_socketListeners = null;
         ClearUtil.clearObject(m_heartBeat);
         m_heartBeat = null;
         m_encryptByteArr.clear();
         m_encryptByteArr = null;
         ClearUtil.nullArr(m_writtenInformationBodys);
         m_writtenInformationBodys = null;
      }
      
      public function addSocketListener(param1:ISocketListener) : void
      {
         if(m_socketListeners == null)
         {
            m_socketListeners = new Vector.<ISocketListener>();
         }
         m_socketListeners.push(param1);
      }
      
      public function removeSocketListener(param1:ISocketListener) : void
      {
         if(m_socketListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_socketListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_socketListeners.splice(_loc2_,1);
            _loc2_ = int(m_socketListeners.indexOf(param1));
         }
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function init() : void
      {
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            m_societySystemTestData = new SocietySystemTestData();
            m_societySystemTestData.socketManager = this;
         }
         m_socket = new Socket();
         m_socket.endian = "littleEndian";
         m_socket.addEventListener("socketData",eventHandler);
         m_socket.addEventListener("close",eventHandler);
         m_socket.addEventListener("connect",eventHandler);
         m_socket.addEventListener("ioError",eventHandler);
         m_socket.addEventListener("securityError",eventHandler);
      }
      
      public function connect(param1:String, param2:int) : void
      {
         var host:String = param1;
         var port:int = param2;
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            setTimeout(function():void
            {
               m_socket.dispatchEvent(new Event("connect"));
            },200);
         }
         else
         {
            m_socket.connect(host,port);
         }
      }
      
      public function getSocket() : Socket
      {
         return m_socket;
      }
      
      public function writeInformationBody(param1:UP_InformationBody) : void
      {
         var _loc2_:InformationBodySeverDetail = null;
         var _loc3_:ByteArray = null;
         if(m_versionControl.getLineMode().getLineMode() == "onLine")
         {
            if(param1.id_informationBody != 3044)
            {
               if(param1.id_informationBody == 2000)
               {
                  _loc2_ = new UP_VerifySeverDetail();
                  (_loc2_ as UP_VerifySeverDetail).setCheckValue(m_checkValue);
               }
               else
               {
                  _loc2_ = new UP_OtherSeverDetail();
                  (_loc2_ as UP_OtherSeverDetail).setSession(m_session);
                  (_loc2_ as UP_SeverDetail).setCheckValue(m_checkValue);
               }
            }
            param1.setSeverDetail(_loc2_);
            trace("写入信息 id：" + param1.id_informationBody);
            trace("checkValue:",m_checkValue);
            trace("session:",m_session);
            _loc3_ = param1.getThisByteArray();
            m_checkValue += 1;
            if(m_checkValue > 4000000000)
            {
               m_checkValue = 0;
            }
            if(param1.id_informationBody != 3044)
            {
               new InformationBodyDetailUtil().encrytInormationBodyByteArray(_loc3_,m_encryptByteArr);
            }
            m_socket.writeBytes(_loc3_);
         }
         if(m_writtenInformationBodys == null)
         {
            m_writtenInformationBodys = new Vector.<InformationBody>();
         }
         m_writtenInformationBodys.push(param1);
      }
      
      public function flush() : void
      {
         var writtenInformationBodys:Vector.<InformationBody> = m_writtenInformationBodys;
         m_writtenInformationBodys = null;
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            setTimeout(function():void
            {
               var _loc2_:int = 0;
               var _loc1_:int = !!writtenInformationBodys ? writtenInformationBodys.length : 0;
               _loc2_ = 0;
               while(_loc2_ < _loc1_)
               {
                  if(writtenInformationBodys[_loc2_])
                  {
                     getSocketData(m_societySystemTestData.createDownInformationByUp(writtenInformationBodys[_loc2_]));
                  }
                  _loc2_++;
               }
               ClearUtil.nullArr(writtenInformationBodys);
               writtenInformationBodys = null;
            },200);
         }
         else
         {
            m_socket.flush();
            ClearUtil.nullArr(writtenInformationBodys);
            writtenInformationBodys = null;
         }
      }
      
      public function openHeartBeat(param1:UP_InformationBody) : void
      {
         m_heartBeat = param1;
         sendHeartBeat();
      }
      
      public function closeHeartBeat() : void
      {
         ClearUtil.clearObject(m_heartBeat);
         m_lastSendTime = NaN;
      }
      
      public function setSendHeartBeatInterval(param1:uint) : void
      {
         m_sendHeartBeatInterval = param1;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc2_:int = 0;
         if(m_heartBeat)
         {
            if(isNaN(m_lastSendTime))
            {
               m_lastSendTime = param1.getOnLineTimeForThisInit();
            }
            _loc2_ = !!m_sendHeartBeatInterval ? m_sendHeartBeatInterval : 600000;
            if(param1.getOnLineTimeForThisInit() - m_lastSendTime > _loc2_)
            {
               sendHeartBeat();
               m_lastSendTime = param1.getOnLineTimeForThisInit();
            }
         }
      }
      
      public function testDownInformationBody(param1:InformationBody) : void
      {
         getSocketData(param1);
      }
      
      private function sendHeartBeat() : void
      {
         writeInformationBody(m_heartBeat);
         flush();
      }
      
      private function close() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<ISocketListener> = !!m_socketListeners ? m_socketListeners.slice(0) : null;
         var _loc1_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].close();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      private function getSocketData(param1:InformationBody) : void
      {
         var _loc4_:int = 0;
         if(param1 == null)
         {
            return;
         }
         var _loc3_:Vector.<ISocketListener> = !!m_socketListeners ? m_socketListeners.slice(0) : null;
         var _loc2_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_[_loc4_])
            {
               _loc3_[_loc4_].getSocketData(param1);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc3_,false,false,false);
      }
      
      private function resetCheckValue() : void
      {
         m_checkValue = 0;
         trace("重设checkValue值为0");
      }
      
      private function connectComplete() : void
      {
         var _loc3_:int = 0;
         resetCheckValue();
         var _loc2_:Vector.<ISocketListener> = !!m_socketListeners ? m_socketListeners.slice(0) : null;
         var _loc1_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].connectComplete();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      private function ioError(param1:String) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<ISocketListener> = !!m_socketListeners ? m_socketListeners.slice(0) : null;
         var _loc2_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_[_loc4_])
            {
               _loc3_[_loc4_].ioError(param1);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc3_,false,false,false);
      }
      
      private function securityError(param1:String) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<ISocketListener> = !!m_socketListeners ? m_socketListeners.slice(0) : null;
         var _loc2_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_[_loc4_])
            {
               _loc3_[_loc4_].securityError(param1);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc3_,false,false,false);
      }
      
      private function eventHandler(param1:Event) : void
      {
         var _loc5_:ByteArray = null;
         var _loc2_:int = 0;
         var _loc3_:ByteArray = null;
         var _loc4_:InformationBody = null;
         var _loc6_:ByteArray = null;
         trace("have socket event, type of event:",param1.type);
         loop1:
         switch(param1.type)
         {
            case "socketData":
               if(m_socket == null || m_socket.connected == false)
               {
                  m_currentInformationLength = 0;
                  return;
               }
               while(true)
               {
                  if(!(Boolean(m_socket) && m_socket.bytesAvailable))
                  {
                     break loop1;
                  }
                  if(m_currentInformationLength)
                  {
                     if(m_socket.bytesAvailable + 4 < m_currentInformationLength)
                     {
                        break loop1;
                     }
                     _loc5_ = new ByteArray();
                     _loc5_.endian = "littleEndian";
                     m_socket.readBytes(_loc5_,0,m_currentInformationLength - 4);
                     _loc5_.position = 0;
                     _loc2_ = _loc5_.readInt();
                     if(_loc2_ != 3045)
                     {
                        new InformationBodyDetailUtil().decryptByteArray(_loc5_,m_encryptByteArr);
                     }
                     _loc5_.position = 4;
                     trace("获取到 informationBody id：",_loc2_);
                     _loc3_ = new ByteArray();
                     _loc3_.endian = "littleEndian";
                     _loc5_.readBytes(_loc3_,0,m_currentInformationLength - 4 - 4);
                     _loc3_.position = 0;
                     _loc4_ = new InformationBody();
                     _loc4_.id_informationBody = _loc2_;
                     _loc4_.setDetailByByteArray(_loc3_);
                     m_currentInformationLength = 0;
                     getSocketData(_loc4_);
                  }
                  else
                  {
                     if(m_socket.bytesAvailable < 4)
                     {
                        break loop1;
                     }
                     trace("有数据，m_socket.bytesAvailable >= InformationBody.DATA_BYTE_LEN_FOR_INFORBODY_LEN");
                     _loc6_ = new ByteArray();
                     _loc6_.endian = "littleEndian";
                     m_socket.readBytes(_loc6_,0,4);
                     _loc6_.position = 0;
                     m_currentInformationLength = _loc6_.readInt();
                     trace("m_currentInformationLength:",m_currentInformationLength);
                  }
               }
               break;
            case "close":
               close();
               break;
            case "connect":
               connectComplete();
               break;
            case "ioError":
               ioError((param1 as IOErrorEvent).text);
               break;
            case "securityError":
               securityError((param1 as SecurityErrorEvent).text);
               trace("(e as SecurityErrorEvent).text",(param1 as SecurityErrorEvent).text);
         }
      }
      
      public function setSession(param1:int) : void
      {
         m_session = param1;
         trace("重设sesssion:",m_session);
      }
      
      public function setEncryptByteArr(param1:int) : void
      {
         trace("更改加密至","新的加密值为:",param1);
         m_encryptByteArr.clear();
         m_encryptByteArr.position = 0;
         m_encryptByteArr.writeByte(param1);
         m_encryptByteArr.position = 0;
      }
   }
}

