package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_UpdatePlayerData extends InformationBodyDetail
   {
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_option:int;
      
      private var m_newPlayerLevel:int;
      
      private var m_newNickNameLength:int;
      
      private var m_newNickName:String;
      
      private var m_upPlayerDataOption:UpdatePlayerDataOption;
      
      public function UP_UpdatePlayerData()
      {
         super();
         m_informationBodyId = 3040;
      }
      
      public function initData(param1:Number, param2:int, param3:int, param4:int, param5:String) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_option = param3;
         m_newPlayerLevel = param4;
         m_newNickName = param5;
         m_newNickNameLength = new InformationBodyDetailUtil().getStringLength(param5);
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_option);
         if(m_upPlayerDataOption == null)
         {
            m_upPlayerDataOption = new UpdatePlayerDataOption();
         }
         if(m_option & 1)
         {
            _loc1_.writeInt(m_newPlayerLevel);
         }
         if(m_option & 2)
         {
            _loc1_.writeInt(m_newNickNameLength);
            if(m_newNickNameLength)
            {
               _loc1_.writeUTFBytes(m_newNickName);
            }
         }
         return _loc1_;
      }
   }
}

