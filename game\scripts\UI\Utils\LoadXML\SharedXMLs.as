package UI.Utils.LoadXML
{
   import YJFY.Utils.ClearUtil;
   
   public class SharedXMLs
   {
      private var m_xmlObj:Object;
      
      public function SharedXMLs()
      {
         super();
         m_xmlObj = {};
      }
      
      public function clear() : void
      {
         ClearUtil.nullObject(m_xmlObj);
         m_xmlObj = null;
      }
      
      public function addXML(param1:String, param2:XML) : void
      {
         m_xmlObj[param1] = param2;
      }
      
      public function removeXML(param1:String) : void
      {
         m_xmlObj[param1] = null;
      }
      
      public function getXML(param1:String) : XML
      {
         return m_xmlObj[param1];
      }
   }
}

