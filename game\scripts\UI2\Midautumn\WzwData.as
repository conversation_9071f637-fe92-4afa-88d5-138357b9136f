package UI2.Midautumn
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   
   public class WzwData extends DataManagerParent
   {
      private static var _instance:WzwData;
      
      private var m_bCanIn:Boolean = false;
      
      private var m_ranklist:Vector.<RankItem>;
      
      private var m_currRank:int;
      
      private var m_bestRank:Array;
      
      private var m_maxRank:int = -1;
      
      private var m_useNum:int;
      
      private var m_freeNum:int;
      
      private var m_rid:int;
      
      private var m_point:Array;
      
      private var m_toplist:Vector.<RankItem>;
      
      public function WzwData()
      {
         super();
         m_ranklist = new Vector.<RankItem>();
         m_bestRank = [];
         m_point = [];
         m_toplist = new Vector.<RankItem>();
      }
      
      public static function getInstance() : WzwData
      {
         if(_instance == null)
         {
            _instance = new WzwData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_ranklist);
         m_ranklist = null;
         ClearUtil.clearObject(m_bestRank);
         m_bestRank = null;
         ClearUtil.clearObject(m_point);
         m_point = null;
         ClearUtil.clearObject(m_toplist);
         m_toplist = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      public function getCanIn() : Boolean
      {
         return m_bCanIn;
      }
      
      public function getFreeNum() : int
      {
         return m_freeNum;
      }
      
      public function setFreeNum(param1:int) : void
      {
         m_freeNum = param1;
      }
      
      public function getUseNum() : int
      {
         return m_useNum;
      }
      
      public function setUseNum(param1:int) : void
      {
         m_useNum = param1;
      }
      
      public function getCurrRank() : int
      {
         return m_currRank;
      }
      
      public function setCurrRank(param1:int) : void
      {
         m_currRank = param1;
      }
      
      public function getRid() : int
      {
         return m_rid;
      }
      
      public function setRid(param1:int) : void
      {
         m_rid = param1;
      }
      
      public function initData(param1:Object) : void
      {
         var _loc3_:RankItem = null;
         var _loc6_:int = 0;
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         m_freeNum = int(param1.FreeTime);
         m_bCanIn = Boolean(param1.NotAllow);
         m_currRank = int(param1.RankIndex);
         var _loc7_:Array = param1.Data as Array;
         m_toplist.length = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc7_.length)
         {
            _loc3_ = new RankItem();
            _loc3_.rank = _loc6_ + 1;
            _loc3_.num = String(_loc7_[_loc6_].Rank);
            _loc3_.name = String(_loc7_[_loc6_].Name);
            m_toplist.push(_loc3_);
            _loc3_.numlist.length = 0;
            _loc2_ = _loc3_.num.split(",");
            _loc4_ = 0;
            while(_loc4_ < _loc2_.length)
            {
               _loc3_.numlist.push(_loc2_[_loc4_]);
               _loc4_++;
            }
            _loc6_++;
         }
         m_bestRank.length = 0;
         _loc7_ = String(param1.BestRank).split(",");
         _loc5_ = 0;
         while(_loc5_ < _loc7_.length)
         {
            m_bestRank.push(_loc7_[_loc5_]);
            _loc5_++;
         }
      }
      
      public function initTopThree(param1:Object) : void
      {
         var _loc3_:RankItem = null;
         var _loc5_:int = 0;
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         var _loc6_:Array = param1.Data as Array;
         m_toplist.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc6_.length)
         {
            if(_loc5_ < 3)
            {
               _loc3_ = new RankItem();
               _loc3_.rank = _loc5_ + 1;
               _loc3_.num = String(_loc6_[_loc5_].Rank);
               _loc3_.name = String(_loc6_[_loc5_].Name);
               m_toplist.push(_loc3_);
               _loc3_.numlist.length = 0;
               _loc2_ = _loc3_.num.split(",");
               _loc4_ = 0;
               while(_loc4_ < _loc2_.length)
               {
                  _loc3_.numlist.push(_loc2_[_loc4_]);
                  _loc4_++;
               }
            }
            _loc5_++;
         }
      }
      
      public function getTopByIndex(param1:int) : RankItem
      {
         if(param1 >= 0 && param1 < m_toplist.length)
         {
            return m_toplist[param1];
         }
         return null;
      }
      
      public function initPoint(param1:Object) : void
      {
         var _loc2_:int = 0;
         m_point.length = 0;
         var _loc3_:Array = String(param1.Rank).split(",");
         _loc2_ = 0;
         while(_loc2_ < _loc3_.length)
         {
            m_point.push(_loc3_[_loc2_]);
            _loc2_++;
         }
         m_currRank = int(param1.RankIndex);
         m_freeNum = int(param1.FreeTime);
         m_bCanIn = Boolean(param1.NotAllow);
      }
      
      public function getPointByIndex(param1:int) : int
      {
         if(param1 >= 0 && param1 < m_point.length)
         {
            return m_point[param1];
         }
         return null;
      }
      
      public function getTotalPage() : int
      {
         if(m_ranklist.length % 6 == 0)
         {
            return m_ranklist.length / 6;
         }
         return m_ranklist.length / 6 + 1;
      }
      
      public function initList(param1:Object, param2:int) : void
      {
         var _loc5_:Array = null;
         var _loc3_:RankItem = null;
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         m_ranklist.length = 0;
         var _loc7_:Array = param1.Data as Array;
         m_bestRank.length = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc7_.length)
         {
            _loc3_ = new RankItem();
            _loc3_.name = String(_loc7_[_loc6_].Name);
            _loc3_.num = String(_loc7_[_loc6_].Rank);
            _loc3_.rank = param2 + _loc6_ + 1;
            _loc5_ = _loc3_.num.split(",");
            _loc3_.numlist.length = 0;
            _loc4_ = 0;
            while(_loc4_ < _loc5_.length)
            {
               _loc3_.numlist.push(int(_loc5_[_loc4_]));
               _loc4_++;
            }
            m_ranklist.push(_loc3_);
            _loc6_++;
         }
      }
      
      public function getMaxRank() : int
      {
         return m_maxRank;
      }
      
      public function getDataByRank(param1:int) : RankItem
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_ranklist.length)
         {
            if(m_ranklist[_loc2_].rank == param1)
            {
               return m_ranklist[_loc2_];
            }
            _loc2_++;
         }
         return null;
      }
      
      public function getDataByIndex(param1:int) : RankItem
      {
         if(param1 >= 0 && param1 < m_ranklist.length)
         {
            return m_ranklist[param1];
         }
         return null;
      }
      
      public function getNumByIndex(param1:int) : int
      {
         if(param1 >= 0 && param1 < m_bestRank.length)
         {
            return m_bestRank[param1];
         }
         return null;
      }
      
      public function getIsHaveBest() : Boolean
      {
         if(m_bestRank.length > 1)
         {
            return true;
         }
         return false;
      }
   }
}

