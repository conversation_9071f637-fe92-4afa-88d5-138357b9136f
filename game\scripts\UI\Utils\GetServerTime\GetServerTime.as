package UI.Utils.GetServerTime
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.TextTrace.traceText;
   import YJFY.Utils.ClearUtil;
   
   public class GetServerTime
   {
      private var m_getServerTimeListeners:Vector.<IGetServerTimeListener>;
      
      private var m_getServerTimeNum:int;
      
      private var m_isLockScreen:Boolean;
      
      private var m_serverTime:String;
      
      private var m_isGetServerTime:Boolean;
      
      public function GetServerTime()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_getServerTimeListeners);
         m_getServerTimeListeners = null;
      }
      
      public function addGetServerTimeListener(param1:IGetServerTimeListener) : void
      {
         if(m_getServerTimeListeners == null)
         {
            m_getServerTimeListeners = new Vector.<IGetServerTimeListener>();
         }
         m_getServerTimeListeners.push(param1);
      }
      
      public function getServerTime(param1:Boolean) : void
      {
         m_serverTime = null;
         if(m_getServerTimeListeners == null || m_getServerTimeListeners.length == 0)
         {
            trace("获取服务器时间侦听个数为0，调用服务器失败！");
            return;
         }
         if(m_isGetServerTime)
         {
            return;
         }
         m_getServerTimeNum = 0;
         m_isGetServerTime = true;
         if(param1)
         {
            m_isLockScreen = true;
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            if(GamingUI.getInstance().manBan && GamingUI.getInstance().manBan.text)
            {
               GamingUI.getInstance().manBan.text.text = "处理中...";
            }
         }
         MyFunction2.getServerTimeFunction(dealWithTime,null);
      }
      
      private function dealWithTime(param1:String) : void
      {
         if(!param1 || !param1.length)
         {
            if(m_getServerTimeNum < 5)
            {
               m_getServerTimeNum++;
               traceText("请求服务器时间次数：",m_getServerTimeNum);
               MyFunction2.getServerTimeFunction(dealWithTime,null);
            }
            else
            {
               getServerTimeFail();
               if(m_isLockScreen)
               {
                  m_isLockScreen = false;
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               }
            }
            return;
         }
         getServerTimeSuccess(param1);
         if(m_isLockScreen)
         {
            m_isLockScreen = false;
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
      
      private function getServerTimeFail() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         m_serverTime = null;
         m_isGetServerTime = false;
         _loc1_ = !!m_getServerTimeListeners ? m_getServerTimeListeners.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_getServerTimeListeners[_loc2_].failGetServerTime();
            _loc2_++;
         }
         ClearUtil.nullArr(m_getServerTimeListeners,false,false,false);
         m_getServerTimeListeners = null;
      }
      
      private function getServerTimeSuccess(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         m_serverTime = param1;
         m_isGetServerTime = false;
         _loc2_ = !!m_getServerTimeListeners ? m_getServerTimeListeners.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_getServerTimeListeners[_loc3_].successGetServerTime(m_serverTime);
            _loc3_++;
         }
         ClearUtil.nullArr(m_getServerTimeListeners,false,false,false);
         m_getServerTimeListeners = null;
      }
   }
}

