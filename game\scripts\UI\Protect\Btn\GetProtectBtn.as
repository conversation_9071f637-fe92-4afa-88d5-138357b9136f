package UI.Protect.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GetProtectBtn extends Btn
   {
      public function GetProtectBtn()
      {
         super();
         setTipString("点击获取保佑");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGetProtectBtn"));
      }
   }
}

