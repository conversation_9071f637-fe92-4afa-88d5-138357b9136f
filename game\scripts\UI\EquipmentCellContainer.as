package UI
{
   import UI.UIInterface.OldInterface.IEquipmentCell;
   
   public class EquipmentCellContainer
   {
      private var _equipmentCells:Vector.<IEquipmentCell> = new Vector.<IEquipmentCell>();
      
      public function EquipmentCellContainer()
      {
         super();
      }
      
      public function addEquipmentCell(param1:IEquipmentCell) : void
      {
         _equipmentCells.push(param1);
      }
      
      public function removeEquipmentCell(param1:IEquipmentCell) : void
      {
         if(_equipmentCells.indexOf(param1) > -1)
         {
            _equipmentCells.splice(_equipmentCells.indexOf(param1),1);
         }
      }
      
      public function concatEquipmentCellContainer(param1:EquipmentCellContainer) : void
      {
         if(!param1)
         {
            return;
         }
         _equipmentCells = _equipmentCells.concat(param1.equipmentCells);
      }
      
      public function clearCells() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(_equipmentCells.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _equipmentCells[_loc2_] = null;
            _loc2_++;
         }
         _equipmentCells = null;
         _equipmentCells = new Vector.<IEquipmentCell>();
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(_equipmentCells)
         {
            _loc1_ = int(_equipmentCells.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(_equipmentCells[_loc2_])
               {
                  _equipmentCells[_loc2_].clear();
               }
               _equipmentCells[_loc2_] = null;
               _loc2_++;
            }
         }
         _equipmentCells = null;
      }
      
      public function get equipmentCells() : Vector.<IEquipmentCell>
      {
         return _equipmentCells;
      }
   }
}

