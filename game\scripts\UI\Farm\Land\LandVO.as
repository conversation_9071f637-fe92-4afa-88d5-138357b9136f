package UI.Farm.Land
{
   import UI.Farm.FarmShowObjectVO;
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class LandVO extends FarmShowObjectVO
   {
      public static const IDLE_STATE:int = 0;
      
      public static const PLANTING_STATE:int = 1;
      
      public static const HARVEST_STATE:int = 2;
      
      public static const HARVEST_POST_STATE:int = 3;
      
      public static const RECOVERING_STATE:int = 4;
      
      private var _equipmentIDInLand:int;
      
      private var _date:String;
      
      private var _state:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function LandVO()
      {
         super();
         init();
      }
      
      private function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.equipmentIDInLand = _equipmentIDInLand;
         _antiwear.date = _date;
         _antiwear.state = _state;
      }
      
      public function get state() : int
      {
         return _antiwear.state;
      }
      
      public function set state(param1:int) : void
      {
         _antiwear.state = param1;
      }
      
      public function get equipmentIDInLand() : int
      {
         return _antiwear.equipmentIDInLand;
      }
      
      public function set equipmentIDInLand(param1:int) : void
      {
         _antiwear.equipmentIDInLand = param1;
      }
      
      public function get date() : String
      {
         return _antiwear.date;
      }
      
      public function set date(param1:String) : void
      {
         _antiwear.date = param1;
      }
   }
}

