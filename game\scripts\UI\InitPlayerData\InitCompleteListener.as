package UI.InitPlayerData
{
   public class InitCompleteListener implements IInitCompleteListener
   {
      public var initCompleteFun:Function;
      
      public var addTarget:InitPlayersData;
      
      public function InitCompleteListener()
      {
         super();
      }
      
      public function clear() : void
      {
         initCompleteFun = null;
         addTarget = null;
      }
      
      public function initComplete() : void
      {
         if(<PERSON><PERSON>an(initCompleteFun))
         {
            initCompleteFun();
         }
         addTarget.removeInitCompleteListener(this);
         clear();
      }
   }
}

