package UI
{
   import UI.CheatData.CheatData;
   import UI.Equipments.AbleEquipments.AbleEquipment;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.DropEquipment;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipment;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipment;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.MessageBox.MessageBoxEngine;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import YJFY.API_4399.GetTimeAPI.GetTimeListener;
   import YJFY.API_4399.RankListAPI.SubmitData;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.VerifyUserState.VerifyUserStateListener;
   import com.greensock.loading.LoaderMax;
   import deng.fzip.FZipLoading;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.events.Event;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.getClassByAlias;
   import flash.system.ApplicationDomain;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.setTimeout;
   
   public class MyFunction2
   {
      public function MyFunction2()
      {
         super();
      }
      
      public static function transformMoneyConcise1(param1:int) : String
      {
         if(param1 < 10000)
         {
            return param1.toString();
         }
         return "" + int(param1 / 10000) + "万";
      }
      
      public static function transformMoneyConcise2(param1:int) : String
      {
         if(param1 < 10000)
         {
            return param1.toString();
         }
         return "" + int(param1 / 10000) + "万" + (!!(param1 % 10000) ? param1 % 10000 : "");
      }
      
      public static function judgmentValueInNormal(param1:Number, param2:Number, param3:Number) : Boolean
      {
         return Math.abs(param1 - param2) <= param3;
      }
      
      public static function returnShowByClassName(param1:String) : DisplayObject
      {
         var _loc2_:Class = null;
         var _loc3_:DisplayObject = null;
         _loc2_ = LoaderMax.getClass(param1);
         if(!_loc2_ && ApplicationDomain.currentDomain.hasDefinition(param1))
         {
            _loc2_ = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         }
         if(!_loc2_)
         {
            try
            {
               _loc2_ = getClassByAlias(param1);
            }
            catch(error:Error)
            {
            }
         }
         if(_loc2_)
         {
            _loc3_ = new _loc2_();
         }
         return _loc3_;
      }
      
      public static function returnClassByClassName(param1:String) : Class
      {
         var _loc2_:Class = null;
         _loc2_ = LoaderMax.getClass(param1);
         if(!_loc2_ && ApplicationDomain.currentDomain.hasDefinition(param1))
         {
            _loc2_ = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         }
         if(!_loc2_)
         {
            try
            {
               _loc2_ = getClassByAlias(param1);
            }
            catch(e:Error)
            {
               _loc2_ = null;
            }
         }
         return _loc2_;
      }
      
      public static function setTextFieldAttribute(param1:String, param2:TextField, param3:DisplayObjectContainer, param4:Number = 0, param5:Number = 0, param6:String = null, param7:int = 20, param8:uint = 16777215, param9:Boolean = false, param10:Boolean = false, param11:Boolean = false) : void
      {
         param2.defaultTextFormat = new TextFormat(param6,param7,param8);
         param2.selectable = param9;
         if(param6)
         {
            param2.embedFonts = true;
         }
         param2.multiline = param10;
         param2.wordWrap = param11;
         param2.text = param1;
         param2.width = param2.textWidth + 5;
         param2.height = param2.textHeight + 5;
         param2.x = param4;
         param2.y = param5;
         param3.addChild(param2);
      }
      
      public static function loadXMLFunction(param1:String, param2:Function, param3:Function, param4:Boolean = true) : void
      {
         var xmlVar:String;
         var oldXML:XML;
         var strInfo:String;
         var loader:URLLoader;
         var xmlName:String = param1;
         var onComplete:Function = param2;
         var failFunction:Function = param3;
         var isFalse:Boolean = param4;
         var complete:* = function(param1:Event):void
         {
            param1.currentTarget.removeEventListener("complete",complete,false);
            param1.currentTarget.removeEventListener("securityError",failHandler,false);
            param1.currentTarget.removeEventListener("ioError",failHandler,false);
            var _loc2_:String = (param1.currentTarget as URLLoader).data;
            if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
            {
               try
               {
                  _loc2_ = MyBase64.getReallyDate(_loc2_);
               }
               catch(e:Error)
               {
                  throw new Error("配置表解密错误" + xmlName,43999);
               }
            }
            var _loc3_:XML = new XML(_loc2_);
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
            onComplete(_loc3_);
         };
         var failHandler:* = function(param1:Event):void
         {
            param1.currentTarget.removeEventListener("complete",complete,false);
            param1.currentTarget.removeEventListener("securityError",failHandler,false);
            param1.currentTarget.removeEventListener("ioError",failHandler,false);
            if(Boolean(failFunction))
            {
               if(Boolean(failFunction))
               {
                  if(failFunction.length == 0)
                  {
                     failFunction();
                  }
                  else if(failFunction.length == 2)
                  {
                     failFunction("网络连接失败！",0);
                  }
               }
            }
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
         };
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            oldXML = GamingUI.getInstance().getSharedXMLs().getXML("CodeXML/UIData/" + xmlName + ".xml");
         }
         else
         {
            oldXML = GamingUI.getInstance().getSharedXMLs().getXML("XML/UIData/" + xmlName + ".xml");
         }
         if(oldXML)
         {
            onComplete(oldXML);
            return;
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            try
            {
               strInfo = MyBase64.getReallyDate(FZipLoading.dic["UIData/" + xmlName + ".xml"]);
            }
            catch(e:Error)
            {
               throw new Error("配置表解密错误UIData/" + xmlName + ".xml",43999);
            }
            oldXML = new XML(strInfo);
            GamingUI.getInstance().getSharedXMLs().addXML("CodeXML/UIData/" + xmlName + ".xml",oldXML);
            onComplete(oldXML);
            return;
         }
         xmlVar = GamingUI.getInstance().getVersionControl().getXMLVersionStr();
         loader = new URLLoader();
         loader.addEventListener("securityError",failHandler,false);
         loader.addEventListener("ioError",failHandler,false);
         loader.addEventListener("complete",complete,false);
         if(isFalse)
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
            {
               GamingUI.getInstance().manBan.text.text = "处理中...";
            }
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            loader.load(new URLRequest("CodeXML" + xmlVar + "/UIData/" + xmlName + ".xml?v=" + Math.random() * 999999));
         }
         else
         {
            loader.load(new URLRequest("XML" + xmlVar + "/UIData/" + xmlName + ".xml?v=" + Math.random() * 999999));
         }
      }
      
      public static function getUserStateIsRightFunction(param1:Function, param2:Function, param3:Boolean = true) : void
      {
         var getServerTimeNum:int;
         var trueFun:Function = param1;
         var falseFun:Function = param2;
         var isFalse:Boolean = param3;
         var verifyUserStateListener:VerifyUserStateListener = new VerifyUserStateListener();
         verifyUserStateListener.verifyUserState = GamingUI.getInstance().getVerifyUserState();
         verifyUserStateListener.IsNotLinkErrorFun = function():void
         {
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
            setTimeout(function():void
            {
               if(Boolean(falseFun))
               {
                  if(falseFun.length == 2)
                  {
                     falseFun("网络连接失败!",0);
                  }
                  else if(falseFun.length == 0)
                  {
                     falseFun();
                  }
               }
            },100);
         };
         verifyUserStateListener.userLoginOutErrorFun = function():void
         {
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
            setTimeout(function():void
            {
               try
               {
                  if(Boolean(falseFun))
                  {
                     falseFun("登录异常，请重新登录!",0);
                  }
               }
               catch(error:Error)
               {
                  if(Boolean(falseFun))
                  {
                     falseFun();
                  }
               }
            },100);
         };
         verifyUserStateListener.isMultipleErrorFun = function():void
         {
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
            setTimeout(function():void
            {
               GamingUI.getInstance().lockGamingUI("游戏多开了！");
               try
               {
                  if(Boolean(falseFun))
                  {
                     falseFun("出错， 游戏同时开启多个了!",0);
                  }
               }
               catch(error:Error)
               {
                  if(Boolean(falseFun))
                  {
                     falseFun();
                  }
               }
            },100);
         };
         verifyUserStateListener.userStateIsOkFun = function():void
         {
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
            if(Boolean(trueFun))
            {
               trueFun();
            }
         };
         if(isFalse)
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "处理中...";
         }
         GamingUI.getInstance().getVerifyUserState().addVerifyUserStateListener(verifyUserStateListener);
         GamingUI.getInstance().getVerifyUserState().VerifyUserSate();
      }
      
      public static function getServerTimeFunction(param1:Function, param2:Function, param3:Boolean = true) : void
      {
         var funAfterGetTime:Function = param1;
         var getTimeFailFun:Function = param2;
         var isFalse:Boolean = param3;
         var getTimeListener:GetTimeListener = new GetTimeListener();
         getTimeListener.getTimeAPI = GamingUI.getInstance().getAPI4399().getTimeAPI;
         getTimeListener.getTimeFun = function(param1:String):void
         {
            var timeStr:String = param1;
            if(Boolean(timeStr) == false)
            {
               setTimeout(function():void
               {
                  if(Boolean(getTimeFailFun))
                  {
                     if(getTimeFailFun.length == 2)
                     {
                        getTimeFailFun("网络连接失败!",0);
                     }
                     else if(getTimeFailFun.length == 0)
                     {
                        getTimeFailFun();
                     }
                  }
               },100);
            }
            else
            {
               GamingUI.getInstance().updateNewestTimeStrFromSever(timeStr);
               if(isFalse)
               {
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               }
               if(Boolean(funAfterGetTime))
               {
                  if(funAfterGetTime.length == 1)
                  {
                     funAfterGetTime(timeStr);
                  }
                  else if(funAfterGetTime.length == 0)
                  {
                     funAfterGetTime();
                  }
               }
            }
         };
         GamingUI.getInstance().getAPI4399().getTimeAPI.addGetTimeListener(getTimeListener);
         GamingUI.getInstance().getAPI4399().getTimeAPI.getTime();
      }
      
      public static function loadXMLAndGetServerTimeFunction(param1:String, param2:Function, param3:Function, param4:Boolean = true) : void
      {
         var loader:URLLoader;
         var xmlName:String = param1;
         var funAfterComplete:Function = param2;
         var getFailFun:Function = param3;
         var isFalse:Boolean = param4;
         var complete:* = function(param1:Event):void
         {
            var getServerTimeNum:int;
            var strInfo:String;
            var xml:XML;
            var e:Event = param1;
            var dealWith:* = function(param1:String):void
            {
               if(isFalse)
               {
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               }
               funAfterComplete(xml,param1);
            };
            e.currentTarget.removeEventListener("complete",complete,false);
            e.currentTarget.removeEventListener("securityError",failHandler,false);
            e.currentTarget.removeEventListener("ioError",failHandler,false);
            strInfo = (e.currentTarget as URLLoader).data;
            if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
            {
               try
               {
                  strInfo = MyBase64.getReallyDate(strInfo);
               }
               catch(e:Error)
               {
                  throw new Error("配置表解密错误," + xmlName,43999);
               }
            }
            xml = new XML(strInfo);
            getServerTimeFunction(function(param1:String):void
            {
               dealWith(param1);
            },getFailFun,isFalse);
         };
         var failHandler:* = function(param1:Event):void
         {
            param1.currentTarget.removeEventListener("complete",complete,false);
            param1.currentTarget.removeEventListener("securityError",failHandler,false);
            param1.currentTarget.removeEventListener("ioError",failHandler,false);
            if(Boolean(getFailFun))
            {
               getFailFun("网络连接出问题!",0);
            }
            if(isFalse)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            }
         };
         var completeIsZip:* = function():void
         {
            var getServerTimeNum:int;
            var strInfo:String;
            var xml:XML;
            var dealWith:* = function(param1:String):void
            {
               if(isFalse)
               {
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
               }
               funAfterComplete(xml,param1);
            };
            if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
            {
               try
               {
                  strInfo = FZipLoading.dic["UIData/" + xmlName + ".xml"];
                  strInfo = MyBase64.getReallyDate(strInfo);
               }
               catch(e:Error)
               {
                  throw new Error("配置表解密错误," + xmlName,43999);
               }
            }
            xml = new XML(strInfo);
            getServerTimeFunction(function(param1:String):void
            {
               dealWith(param1);
            },getFailFun,isFalse);
         };
         var xmlVar:String = GamingUI.getInstance().getVersionControl().getXMLVersionStr();
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            completeIsZip();
         }
         else
         {
            loader = new URLLoader();
            loader.addEventListener("securityError",failHandler,false);
            loader.addEventListener("ioError",failHandler,false);
            loader.addEventListener("complete",complete,false);
         }
         if(isFalse)
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan) && Boolean(GamingUI.getInstance().manBan.text))
            {
               GamingUI.getInstance().manBan.text.text = "处理中...";
            }
         }
         if(!loader)
         {
            return;
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            loader.load(new URLRequest("CodeXML" + xmlVar + "/UIData/" + xmlName + ".xml?v=" + Math.random() * 999999));
         }
         else
         {
            loader.load(new URLRequest("XML" + xmlVar + "/UIData/" + xmlName + ".xml?v=" + Math.random() * 999999));
         }
      }
      
      public static function addEquipmentVOs(param1:Vector.<EquipmentVO>, param2:Player, param3:Function, param4:Function, param5:Array, param6:Array, param7:int = 0) : void
      {
         if(!param5)
         {
            param5 = [];
         }
         if(!param6)
         {
            param6 = [];
         }
         var _loc8_:int = int(param2.playerVO.packageEquipmentVOs.length);
         var _loc11_:int = 0;
         var _loc9_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>(_loc8_,true);
         _loc11_ = 0;
         while(_loc11_ < _loc8_)
         {
            if(param2.playerVO.packageEquipmentVOs[_loc11_])
            {
               _loc9_[_loc11_] = param2.playerVO.packageEquipmentVOs[_loc11_].clone();
            }
            else
            {
               _loc9_[_loc11_] = null;
            }
            _loc11_++;
         }
         var _loc10_:int = !!param1 ? param1.length : 0;
         _loc11_ = 0;
         while(_loc11_ < _loc10_)
         {
            if(param1[_loc11_] != null)
            {
               if(!MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_loc9_,param1[_loc11_].clone()))
               {
                  if(Boolean(param3))
                  {
                     param3.apply(null,param5);
                  }
                  return;
               }
            }
            _loc11_++;
         }
         _loc11_ = 0;
         while(_loc11_ < _loc10_)
         {
            if(param1[_loc11_] != null)
            {
               MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param2.playerVO.packageEquipmentVOs,param1[_loc11_],param7);
            }
            _loc11_++;
         }
         if(Boolean(param4))
         {
            param4.apply(null,param6);
         }
         GamingUI.getInstance().refresh(2);
         _loc8_ = int(_loc9_.length);
         _loc11_ = 0;
         while(_loc11_ < _loc8_)
         {
            if(_loc9_[_loc11_])
            {
               _loc9_[_loc11_].clear();
               _loc9_[_loc11_] = null;
            }
            _loc11_++;
         }
         _loc9_ = null;
      }
      
      public static function falseAddEquipmentVOs(param1:Vector.<EquipmentVO>, param2:Player, param3:Function, param4:Function, param5:Array, param6:Array, param7:int = 0) : void
      {
         if(!param5)
         {
            param5 = [];
         }
         if(!param6)
         {
            param6 = [];
         }
         var _loc8_:int = int(param2.playerVO.packageEquipmentVOs.length);
         var _loc11_:int = 0;
         var _loc9_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>(_loc8_,true);
         _loc11_ = 0;
         while(_loc11_ < _loc8_)
         {
            if(param2.playerVO.packageEquipmentVOs[_loc11_])
            {
               _loc9_[_loc11_] = param2.playerVO.packageEquipmentVOs[_loc11_].clone();
            }
            else
            {
               _loc9_[_loc11_] = null;
            }
            _loc11_++;
         }
         var _loc10_:int = int(param1.length);
         _loc11_ = 0;
         while(_loc11_ < _loc10_)
         {
            if(!MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_loc9_,param1[_loc11_].clone(),0,param7))
            {
               ClearUtil.nullArr(_loc9_);
               _loc9_ = null;
               param3.apply(null,param5);
               return;
            }
            _loc11_++;
         }
         ClearUtil.nullArr(_loc9_);
         _loc9_ = null;
         param4.apply(null,param6);
      }
      
      public static function trueAddEquipmentVOs(param1:Vector.<EquipmentVO>, param2:Player, param3:Function, param4:Array) : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = int(param1.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param2.playerVO.packageEquipmentVOs,param1[_loc6_].clone());
            _loc6_++;
         }
         if(Boolean(param3))
         {
            param3.apply(null,param4);
         }
      }
      
      public static function getEquimentVOMaxNumInEquipmentVOs(param1:EquipmentVO, param2:Vector.<EquipmentVO>) : int
      {
         var _loc5_:int = 0;
         var _loc3_:int = int(param2.length);
         var _loc4_:int = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(param2[_loc5_])
            {
               if(param2[_loc5_].id == param1.id)
               {
                  if(param1 is StackEquipmentVO)
                  {
                     _loc4_ += (param2[_loc5_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc4_++;
                  }
               }
            }
            _loc5_++;
         }
         return _loc4_;
      }
      
      public static function getPlayerBgEmptNum(param1:Player) : int
      {
         var _loc5_:int = 0;
         var _loc2_:Vector.<EquipmentVO> = param1.playerVO.packageEquipmentVOs;
         var _loc4_:int = int(_loc2_.length);
         var _loc3_:int = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(!_loc2_[_loc5_])
            {
               _loc3_++;
            }
            _loc5_++;
         }
         return _loc3_;
      }
      
      public static function getEquipmentVOsEnbleContainEquipmenVOtMaxNum(param1:Vector.<EquipmentVO>, param2:EquipmentVO, param3:int = 0) : int
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc9_:int = 0;
         var _loc4_:int = 0;
         if(param2)
         {
            _loc7_ = int(param1.length);
            if(param2 is StackEquipmentVO)
            {
               _loc4_ = (param2 as StackEquipmentVO).maxSuperposition;
            }
            else
            {
               _loc4_ = 1;
            }
            _loc10_ = 0;
            while(_loc10_ < _loc7_)
            {
               if(!param1[_loc10_])
               {
                  if(_loc9_ == param3)
                  {
                     _loc5_ += _loc4_;
                  }
                  else
                  {
                     _loc9_++;
                  }
               }
               else if(param2 is StackEquipmentVO && param1[_loc10_].id == param2.id)
               {
                  _loc5_ += (param1[_loc10_] as StackEquipmentVO).maxSuperposition - (param1[_loc10_] as StackEquipmentVO).num;
               }
               _loc10_++;
            }
         }
         return _loc5_;
      }
      
      public static function judgeParentsIsTheObject(param1:DisplayObject, param2:Object, param3:DisplayObjectContainer) : Object
      {
         var _loc4_:Boolean = false;
         var _loc5_:Object = null;
         if(param1.parent == param3 || param1.parent == null)
         {
            _loc4_ = false;
         }
         else if(param1.parent == param2)
         {
            _loc4_ = true;
            _loc5_ = param1.parent;
         }
         else
         {
            _loc5_ = judgeParentsIsTheObject(param1.parent,param2,param3);
         }
         return _loc5_;
      }
      
      public static function judgeParentsIsTheClass(param1:DisplayObject, param2:Class, param3:DisplayObjectContainer) : DisplayObject
      {
         var _loc4_:DisplayObject = null;
         if(param1.parent == param3 || param1.parent == null)
         {
            _loc4_ = null;
         }
         else if(param1.parent is param2)
         {
            _loc4_ = param1.parent;
         }
         else
         {
            _loc4_ = judgeParentsIsTheClass(param1.parent,param2,param3);
         }
         return _loc4_;
      }
      
      public static function transformTimeToString(param1:int) : String
      {
         var _loc3_:int = 0;
         var _loc4_:String = "";
         var _loc6_:int = param1 / 86400;
         _loc3_ = param1 - _loc6_ * 86400;
         var _loc5_:int = _loc3_ / 3600;
         _loc3_ -= _loc5_ * 3600;
         var _loc2_:int = _loc3_ / 60;
         _loc3_ -= _loc2_ * 60;
         if(_loc6_)
         {
            _loc4_ += _loc6_ + "天";
         }
         if(_loc5_)
         {
            _loc4_ += _loc5_ + "小时";
         }
         if(_loc2_)
         {
            _loc4_ += _loc2_ + "分钟";
         }
         if(_loc3_)
         {
            _loc4_ += _loc3_ + "秒";
         }
         return _loc4_;
      }
      
      public static function concatEquipmentVOVector(... rest) : Vector.<EquipmentVO>
      {
         var _loc4_:* = undefined;
         var _loc3_:* = undefined;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         if(!rest || !rest.length || rest.length == 1)
         {
            throw new Error();
         }
         if(rest.length == 2)
         {
            _loc4_ = rest[0].concat(rest[1]);
         }
         else
         {
            _loc3_ = concatEquipmentVOVector.apply(null,rest.slice(1,rest.length));
            _loc4_ = rest[0].concat(_loc3_);
            _loc2_ = int(_loc3_.length);
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc3_[_loc5_] = null;
               _loc5_++;
            }
            _loc3_ = null;
         }
         return _loc4_;
      }
      
      public static function changeXMLToIDs(param1:XML) : Vector.<int>
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc4_:XMLList = param1.children();
         _loc3_ = int(_loc4_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_.push(_loc4_[_loc5_].@id);
            _loc5_++;
         }
         return _loc2_;
      }
      
      public static function makeOneNumsByXML(param1:XML) : Vector.<int>
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc4_:XMLList = param1.children();
         _loc3_ = int(_loc4_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_.push(1);
            _loc5_++;
         }
         return _loc2_;
      }
      
      public static function randomOneNumFromNums(... rest) : Number
      {
         var _loc3_:int = int(rest.length);
         var _loc2_:int = Math.random() * _loc3_;
         return rest[_loc2_];
      }
      
      public static function saveGame() : void
      {
         Part1.getInstance().saveGame();
      }
      
      public static function saveGame2() : void
      {
         Part1.getInstance().saveGame2();
      }
      
      public static function getLocalTime() : String
      {
         var _loc2_:Date = new Date();
         var _loc4_:String = "";
         _loc4_ = _loc4_ + (_loc2_.getFullYear() + "-");
         var _loc6_:int = _loc2_.getMonth() + 1;
         _loc4_ += String(int(_loc6_ / 10)) + String(int(_loc6_ % 10)) + "-";
         var _loc5_:int = int(_loc2_.getDate());
         _loc4_ += String(int(_loc5_ / 10)) + String(int(_loc5_ % 10)) + " ";
         var _loc7_:int = int(_loc2_.getHours());
         _loc4_ += String(int(_loc7_ / 10)) + String(int(_loc7_ % 10)) + ":";
         var _loc1_:int = int(_loc2_.getMinutes());
         _loc4_ += String(int(_loc1_ / 10)) + String(int(_loc1_ % 10)) + ":";
         var _loc3_:int = int(_loc2_.getSeconds());
         return _loc4_ + (String(int(_loc3_ / 10)) + String(int(_loc3_ % 10)));
      }
      
      public static function changeTextFieldFont(param1:String, param2:TextField, param3:Boolean = true) : void
      {
         if(param1 == null)
         {
            param3 = false;
         }
         if(param2 == null)
         {
            return;
         }
         var _loc4_:TextFormat = param2.defaultTextFormat;
         _loc4_.font = param1;
         param2.defaultTextFormat = _loc4_;
         param2.embedFonts = param3;
      }
      
      public static function changeTextFieldColor(param1:Object, param2:TextField) : void
      {
         var _loc3_:TextFormat = param2.defaultTextFormat;
         _loc3_.color = param1;
         param2.defaultTextFormat = _loc3_;
      }
      
      public static function returnTuDiKeyStrBySerialNumber(param1:int, param2:PlayerVO) : String
      {
         var _loc3_:* = null;
         var _loc4_:XML = null;
         if(param2.playerID == "playerOne")
         {
            _loc4_ = XMLSingle.getInstance().tuDiDataXML.Key[0].Player1[0];
         }
         else if(param2.playerID == "playerTwo")
         {
            _loc4_ = XMLSingle.getInstance().tuDiDataXML.Key[0].Player2[0];
         }
         return String(_loc4_.item.(@serialNumber == param1)[0].@keyStr);
      }
      
      public static function getOneEquipmentVOById(param1:String, param2:Vector.<EquipmentVO>) : EquipmentVO
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:* = undefined;
         _loc4_ = !!param2 ? param2.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            if(Boolean(param2[_loc6_]) && String(param2[_loc6_].id) == param1)
            {
               if(true)
               {
                  _loc3_ = new Vector.<EquipmentVO>();
               }
               _loc3_.push(param2[_loc6_]);
            }
            _loc6_++;
         }
         if(_loc3_ == null || _loc3_.length == 0)
         {
            return null;
         }
         _loc3_.sort(sortFun);
         var _loc5_:EquipmentVO = _loc3_[0];
         ClearUtil.nullArr(_loc3_,false,false,false);
         return _loc5_;
      }
      
      public static function countEquipmentVONumById(param1:String, param2:Vector.<EquipmentVO>) : int
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         _loc4_ = !!param2 ? param2.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(Boolean(param2[_loc5_]) && String(param2[_loc5_].id) == param1)
            {
               if(param2[_loc5_] is StackEquipmentVO)
               {
                  _loc3_ += (param2[_loc5_] as StackEquipmentVO).num;
               }
               else
               {
                  _loc3_ += 1;
               }
            }
            _loc5_++;
         }
         return _loc3_;
      }
      
      private static function sortFun(param1:EquipmentVO, param2:EquipmentVO) : int
      {
         if(!(param1 is StackEquipmentVO && param2 is StackEquipmentVO))
         {
            return 0;
         }
         return (param1 as StackEquipmentVO).num - (param2 as StackEquipmentVO).num;
      }
      
      public static function IsHaveEqInEqsById(param1:String, param2:Vector.<EquipmentVO>) : Boolean
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         _loc4_ = !!param2 ? param2.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(Boolean(param2[_loc5_]) && String(param2[_loc5_].id) == param1)
            {
               _loc3_ = true;
               break;
            }
            _loc5_++;
         }
         return _loc3_;
      }
      
      public static function filterBit(param1:int, param2:int) : int
      {
         return param2 & ~param1;
      }
      
      public static function sheatheEquipmentShell(param1:EquipmentVO) : Equipment
      {
         var _loc3_:Equipment = null;
         var _loc2_:int = 0;
         switch(param1.equipmentType)
         {
            case "undeterminded":
               break;
            case "precious":
            case "weapon":
            case "gourd":
            case "necklace":
            case "clothes":
            case "forever_fashion":
               _loc3_ = new AbleEquipment(param1 as AbleEquipmentVO);
               break;
            case "pet":
               _loc3_ = new PetEquipment(param1 as PetEquipmentVO);
               break;
            case "scroll":
            case "egg":
            case "danMedicine":
            case "petSkillBook":
            case "medal":
            case "fashion":
            case "contract":
               _loc3_ = new Equipment(param1);
               break;
            case "dropEq":
               _loc3_ = new DropEquipment(param1);
               break;
            case "buffEquipment":
            case "forceDan":
            case "grass":
            case "potion":
            case "pocket":
            case "material":
            case "insetGem":
               try
               {
                  _loc2_ = (param1 as StackEquipmentVO).num;
               }
               catch(error:Error)
               {
                  _loc2_ = 0;
               }
               if(_loc2_)
               {
                  _loc3_ = new StackEquipment(param1 as StackEquipmentVO);
               }
               break;
            default:
               throw new Error();
         }
         return _loc3_;
      }
      
      public static function brushBtn(param1:int, param2:EquipmentVO) : int
      {
         var _loc4_:XMLList = null;
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:* = 0;
         if(param1 & 1)
         {
            switch(param2.equipmentType)
            {
               case "material":
               case "scroll":
               case "egg":
               case "grass":
               case "insetGem":
               case "undeterminded":
                  break;
               default:
                  _loc5_ |= 1;
            }
         }
         if(param1 & 2)
         {
            _loc5_ |= 2;
         }
         if(param1 & 4)
         {
            _loc5_ |= 4;
         }
         if(param1 & 8)
         {
            _loc5_ |= 8;
         }
         if(param1 & 16)
         {
            _loc5_ |= 16;
         }
         if(param1 & 32)
         {
            _loc5_ |= 32;
         }
         if(param1 & 64)
         {
            _loc5_ |= 64;
         }
         if(param1 & 128)
         {
            if(param2.id == 11000008 || param2.id == 11000017 || param2.id == 11000007 || param2.id == 11000016)
            {
               _loc5_ |= 128;
            }
         }
         if(param1 & 256)
         {
            _loc4_ = XMLSingle.getInstance().dataXML.ChoiceBtns[0].DetailBtn[0].show;
            _loc3_ = int(!!_loc4_ ? _loc4_.length() : 0);
            _loc6_ = 0;
            while(_loc6_ < _loc3_)
            {
               if(String(_loc4_[_loc6_].@equipmentType) == param2.equipmentType)
               {
                  _loc5_ |= 256;
                  break;
               }
               _loc6_++;
            }
         }
         return _loc5_;
      }
      
      public static function addLenghForVector(param1:*, param2:int) : void
      {
         var _loc3_:int = 0;
         if(param1.length < param2)
         {
            _loc3_ = int(param1.length);
            while(_loc3_ < param2)
            {
               param1.push(null);
               _loc3_++;
            }
         }
      }
      
      public static function doIsCheat() : void
      {
         // 检测功能已禁用 - Detection disabled
         return;
      }
      
      public static function submitCheatData() : void
      {
         // 作弊数据提交功能已禁用 - Cheat data submission disabled
         return;
      }
      
      public static function resetErrorTime(param1:String) : String
      {
         if(int(param1.substr(0,4)) > 2050)
         {
            param1 = "2015" + param1.substring(4);
         }
         return param1;
      }
   }
}

