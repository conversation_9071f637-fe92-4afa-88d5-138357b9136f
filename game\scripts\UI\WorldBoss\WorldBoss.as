package UI.WorldBoss
{
   import UI.WorldBoss.AnimationQueueData.AnimationQueueData;
   import UI.WorldBoss.Boss.Boss;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class WorldBoss
   {
      public static const s_const_bossMaxNum:int = 6;
      
      public static const s_const_bossMaxNumAttName:String = "bossMaxNum";
      
      public static const s_const_rightPositionWidth:int = 2;
      
      public static const s_const_rigthPositonHeight:int = 3;
      
      private var m_worldBossStageShow:Sprite;
      
      private var m_leftPositions:Vector.<Vector.<Position>>;
      
      private var m_rightPositions:Vector.<Vector.<Position>>;
      
      private var m_leftMiddlePosition:Sprite;
      
      private var m_rightMiddlePosition:Sprite;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_world:StepAttackGameWorld;
      
      private var m_getNextStepActionEntities:IGetNextStepActionEntities;
      
      private var m_judgeGameIsEnd:JudgeGameIsEnd;
      
      private var m_bosses:Vector.<Boss>;
      
      private var m_ownSideEntities:Vector.<OwnSideEntity>;
      
      private var m_animationQueueData:AnimationQueueData;
      
      private var m_isStartPlay:Boolean;
      
      private var m_worldBossPanel:WorldBossPanel;
      
      private var m_countDownShow:MovieClipPlayLogicShell;
      
      public function WorldBoss()
      {
         super();
         m_animationQueueData = new AnimationQueueData();
      }
      
      public function clear() : void
      {
         if(m_worldBossStageShow)
         {
            m_worldBossStageShow.removeEventListener("enterFrame",render,false);
         }
         ClearUtil.clearObject(ValueAnimationManager.getInstance());
         m_worldBossStageShow = null;
         ClearUtil.nullArr(m_leftPositions);
         m_leftPositions = null;
         ClearUtil.nullArr(m_rightPositions);
         m_rightPositions = null;
         ClearUtil.clearDisplayObjectInContainer(m_leftMiddlePosition);
         m_leftMiddlePosition = null;
         ClearUtil.clearDisplayObjectInContainer(m_rightMiddlePosition);
         m_rightMiddlePosition = null;
         m_myLoader = null;
         ClearUtil.clearObject(m_world);
         m_world = null;
         ClearUtil.clearObject(m_getNextStepActionEntities);
         m_getNextStepActionEntities = null;
         ClearUtil.clearObject(m_judgeGameIsEnd);
         m_judgeGameIsEnd = null;
         ClearUtil.nullArr(m_bosses);
         m_bosses = null;
         ClearUtil.nullArr(m_ownSideEntities);
         m_ownSideEntities = null;
         ClearUtil.clearObject(m_animationQueueData);
         m_animationQueueData = null;
         m_worldBossPanel = null;
         if(m_countDownShow != null && m_countDownShow.getShow().parent != null)
         {
            m_countDownShow.getShow().parent.removeChild(m_countDownShow.getShow());
         }
         ClearUtil.clearObject(m_countDownShow);
         m_countDownShow = null;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setWorldBossPanel(param1:WorldBossPanel) : void
      {
         m_worldBossPanel = param1;
      }
      
      public function setGetNextStepActionEntities(param1:IGetNextStepActionEntities) : void
      {
         m_getNextStepActionEntities = param1;
      }
      
      public function setWorldBossStageShow(param1:Sprite) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:Position = null;
         m_worldBossStageShow = param1;
         m_worldBossStageShow.addEventListener("enterFrame",render,false,0,true);
         m_leftMiddlePosition = m_worldBossStageShow["middlePosition_1"];
         ClearUtil.clearDisplayObjectInContainer(m_leftMiddlePosition);
         m_rightMiddlePosition = m_worldBossStageShow["middlePosition_2"];
         ClearUtil.clearDisplayObjectInContainer(m_rightMiddlePosition);
         m_leftPositions = new Vector.<Vector.<Position>>(2);
         _loc5_ = 0;
         while(_loc5_ < 2)
         {
            m_leftPositions[_loc5_] = new Vector.<Position>();
            _loc4_ = 0;
            while(_loc4_ < 3)
            {
               _loc2_ = new Position();
               _loc2_.container = m_worldBossStageShow["leftPosition_" + (_loc5_ + 1) + (_loc4_ + 1)];
               ClearUtil.clearDisplayObjectInContainer(_loc2_.container);
               _loc2_.positionX = _loc5_ + 1;
               _loc2_.positionY = _loc4_ + 1;
               m_leftPositions[_loc5_].push(_loc2_);
               _loc4_++;
            }
            _loc5_++;
         }
         m_rightPositions = new Vector.<Vector.<Position>>(2);
         _loc5_ = 0;
         while(_loc5_ < 2)
         {
            m_rightPositions[_loc5_] = new Vector.<Position>();
            _loc4_ = 0;
            while(_loc4_ < 3)
            {
               _loc2_ = new Position();
               _loc2_.container = m_worldBossStageShow["rightPosition_" + (_loc5_ + 1) + (_loc4_ + 1)];
               ClearUtil.clearDisplayObjectInContainer(_loc2_.container);
               _loc2_.positionX = _loc5_ + 1;
               _loc2_.positionY = _loc4_ + 1;
               m_rightPositions[_loc5_].push(_loc2_);
               _loc4_++;
            }
            _loc5_++;
         }
      }
      
      public function getLeftPosition(param1:int, param2:int) : Position
      {
         var _loc3_:Position = m_leftPositions[param1 - 1][param2 - 1];
         if(_loc3_.positionX != param1 || _loc3_.positionY != param2)
         {
            throw new Error();
         }
         return _loc3_;
      }
      
      public function getRightPosition(param1:int, param2:int) : Position
      {
         var _loc3_:Position = m_rightPositions[param1 - 1][param2 - 1];
         if(_loc3_.positionX != param1 || _loc3_.positionY != param2)
         {
            throw new Error();
         }
         return _loc3_;
      }
      
      public function getAnimationQueueData() : AnimationQueueData
      {
         return m_animationQueueData;
      }
      
      public function getLeftMiddlePosition() : Sprite
      {
         return m_leftMiddlePosition;
      }
      
      public function getRightMiddlePosition() : Sprite
      {
         return m_rightMiddlePosition;
      }
      
      public function setBosses(param1:Vector.<Boss>) : void
      {
         ClearUtil.nullArr(m_bosses);
         m_bosses = param1;
      }
      
      public function setOwnSideEntities(param1:Vector.<OwnSideEntity>) : void
      {
         ClearUtil.nullArr(m_ownSideEntities);
         m_ownSideEntities = param1;
      }
      
      public function startFight() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         m_world = new StepAttackGameWorld();
         m_world.getExtraData()["bossMaxNum"] = 6;
         m_world.setGetNextStepActionEntities(m_getNextStepActionEntities);
         m_getNextStepActionEntities.setWorld(m_world);
         m_animationQueueData.addPlayAnimationDataListener(m_worldBossPanel);
         m_judgeGameIsEnd = new JudgeGameIsEnd();
         m_world.setJudgeStepAttackGameIsEnd(m_judgeGameIsEnd);
         m_judgeGameIsEnd.setWorld(m_world);
         _loc1_ = int(m_ownSideEntities.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_world.addFriend(m_ownSideEntities[_loc2_]);
            m_ownSideEntities[_loc2_].getPositionSprite().addChild(m_ownSideEntities[_loc2_]);
            _loc2_++;
         }
         _loc1_ = int(m_bosses.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_world.addEnemy(m_bosses[_loc2_]);
            m_bosses[_loc2_].getPositionSprite().addChild(m_bosses[_loc2_]);
            _loc2_++;
         }
      }
      
      private function getCountDownShowSuccessFun(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_countDownShow = new MovieClipPlayLogicShell();
         m_countDownShow.setShow(new _loc2_() as MovieClip);
         m_countDownShow.getShow().mouseChildren = false;
         m_countDownShow.getShow().mouseEnabled = false;
         playCountDown();
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function playCountDown() : void
      {
         m_worldBossStageShow.addChild(m_countDownShow.getShow());
         m_countDownShow.gotoAndPlay("start",null,null,function():void
         {
            m_countDownShow.getShow().parent.removeChild(m_countDownShow.getShow());
            m_isStartPlay = true;
         },null);
      }
      
      private function judeIsReadyFight() : Boolean
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         if(m_ownSideEntities == null)
         {
            return false;
         }
         if(m_bosses == null)
         {
            return false;
         }
         var _loc2_:Boolean = true;
         _loc1_ = !!m_ownSideEntities ? m_ownSideEntities.length : 0;
         if(_loc1_ == 0)
         {
            _loc2_ = false;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if((m_ownSideEntities[_loc3_] as Entity).getIsReadyFight() == false)
            {
               _loc2_ = false;
               break;
            }
            _loc3_++;
         }
         if(_loc2_)
         {
            _loc1_ = !!m_bosses ? m_bosses.length : 0;
            if(_loc1_ == 0)
            {
               _loc2_ = false;
            }
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if((m_bosses[_loc3_] as Entity).getIsReadyFight() == false)
               {
                  _loc2_ = false;
                  break;
               }
               _loc3_++;
            }
         }
         return _loc2_;
      }
      
      private function judeIsReadyPlay() : Boolean
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         if(m_ownSideEntities == null)
         {
            return false;
         }
         if(m_bosses == null)
         {
            return false;
         }
         var _loc2_:Boolean = true;
         _loc1_ = !!m_ownSideEntities ? m_ownSideEntities.length : 0;
         if(_loc1_ == 0)
         {
            _loc2_ = false;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if((m_ownSideEntities[_loc3_] as Entity).getIsReadyPlay() == false)
            {
               _loc2_ = false;
               break;
            }
            _loc3_++;
         }
         if(_loc2_)
         {
            _loc1_ = !!m_bosses ? m_bosses.length : 0;
            if(_loc1_ == 0)
            {
               _loc2_ = false;
            }
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if((m_bosses[_loc3_] as Entity).getIsReadyPlay() == false)
               {
                  _loc2_ = false;
                  break;
               }
               _loc3_++;
            }
         }
         return _loc2_;
      }
      
      private function go() : void
      {
         if(m_world == null)
         {
            return;
         }
         m_world.startAttack();
         m_worldBossPanel.dealWithDataAndSaveGame();
         ClearUtil.clearObject(m_world);
         m_world = null;
         ClearUtil.clearObject(m_getNextStepActionEntities);
         m_getNextStepActionEntities = null;
         ClearUtil.clearObject(m_judgeGameIsEnd);
         m_judgeGameIsEnd = null;
         if(m_countDownShow == null)
         {
            m_myLoader.getClass("WorldBoss.swf","CountDownShow",getCountDownShowSuccessFun,getFailFun);
            m_myLoader.load();
         }
         else
         {
            playCountDown();
         }
      }
      
      private function playGo() : void
      {
         m_animationQueueData.playEndFun = function():void
         {
            trace("游戏结束");
            m_isStartPlay = false;
            if(m_worldBossPanel)
            {
               m_worldBossPanel.gameOverShow();
            }
         };
         m_animationQueueData.playAnimation();
      }
      
      private function render(param1:Event) : void
      {
         if(judeIsReadyFight())
         {
            go();
         }
         if(m_isStartPlay && Boolean(m_animationQueueData) && m_animationQueueData.getIsPlay() == false && judeIsReadyPlay())
         {
            playGo();
         }
      }
   }
}

