package UI.TaskPanel.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GiveUpTaskBtn extends Btn
   {
      public function GiveUpTaskBtn()
      {
         super();
         setTipString("点击放弃任务");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGiveUpTaskBtn"));
      }
   }
}

