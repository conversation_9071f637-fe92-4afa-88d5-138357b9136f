package UI.SocietySystem.MySocietyPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteDirection;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import flash.display.MovieClip;
   
   public class SocietyWelfarePanel
   {
      private var m_show:MovieClip;
      
      private var m_scrollLS:ScrollSpriteLogicShell;
      
      private var m_societyShopColume:ButtonLogicShell2;
      
      private var m_shopJoinBtn:ButtonLogicShell2;
      
      public function SocietyWelfarePanel()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
      }
      
      private function initShow() : void
      {
         m_scrollLS = new ScrollSpriteLogicShell();
         var _loc1_:ScrollSpriteDirection = new ScrollSpriteDirection();
         _loc1_.setDirection("L");
         m_scrollLS.setShow(m_show,_loc1_);
         m_societyShopColume = new ButtonLogicShell2();
         m_societyShopColume.setShow(m_scrollLS.getDataLayer()["societyShopColume"]);
         m_shopJoinBtn = new ButtonLogicShell2();
         m_shopJoinBtn.setShow(m_societyShopColume.getShow()["joinBtn"]);
         m_shopJoinBtn.setTipString("点击进入商店");
      }
      
      public function getShopJoinBtn() : ButtonLogicShell2
      {
         return m_shopJoinBtn;
      }
   }
}

