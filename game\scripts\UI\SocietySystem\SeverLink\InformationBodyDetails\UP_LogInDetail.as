package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class UP_LogInDetail extends InformationBodyDetail
   {
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_nickNameLength:int;
      
      private var m_nickName:String;
      
      private var m_playerLevel:int;
      
      public function UP_LogInDetail()
      {
         super();
         m_informationBodyId = 3000;
      }
      
      public function initData(param1:Number, param2:int, param3:String, param4:int) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_nickName = param3;
         m_nickNameLength = new InformationBodyDetailUtil().getStringLength(param3);
         m_playerLevel = param4;
         trace("初始化登录信息","uid",m_uid,"idx",m_idx,"nickName",m_nickName,"人物等级:",m_playerLevel);
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid = _loc2_.uid;
         m_idx = _loc2_.idx;
         m_nickNameLength = param1.readInt();
         if(m_nickNameLength)
         {
            m_nickName = param1.readUTFBytes(m_nickNameLength);
         }
         m_playerLevel = param1.readInt();
      }
      
      override public function getThisByteArray() : ByteArray
      {
         trace("get byteArr from up_logInDetail   m_uid:" + m_uid + "   m_idx:" + m_idx + "player level:" + m_playerLevel + "\n");
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_nickNameLength);
         if(m_nickNameLength)
         {
            _loc1_.writeUTFBytes(m_nickName);
         }
         _loc1_.writeInt(m_playerLevel);
         return _loc1_;
      }
   }
}

