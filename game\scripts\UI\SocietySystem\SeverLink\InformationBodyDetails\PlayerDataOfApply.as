package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class PlayerDataOfApply
   {
      protected var m_uid_applicant:Number;
      
      protected var m_idx_applicant:int;
      
      protected var m_nameLength_applicant:int;
      
      protected var m_name_applicant:String;
      
      protected var m_level_applicant:int;
      
      public function PlayerDataOfApply()
      {
         super();
      }
      
      public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid_applicant = _loc2_.uid;
         trace("申请人的uid:" + m_uid_applicant);
         m_idx_applicant = _loc2_.idx;
         trace("申请人的idx:" + m_idx_applicant);
         m_nameLength_applicant = param1.readInt();
         trace("申请者的名称长度:",m_nameLength_applicant);
         if(m_nameLength_applicant)
         {
            m_name_applicant = param1.readUTFBytes(m_nameLength_applicant);
         }
         trace("申请者的名称:",m_name_applicant);
         m_level_applicant = param1.readInt();
      }
      
      public function getUid_applicant() : Number
      {
         return m_uid_applicant;
      }
      
      public function getIdx_applicant() : int
      {
         return m_idx_applicant;
      }
      
      public function getNameLength_applicant() : int
      {
         return m_nameLength_applicant;
      }
      
      public function getName_applicant() : String
      {
         return m_name_applicant;
      }
      
      public function getLevel_applicant() : int
      {
         return m_level_applicant;
      }
   }
}

