package UI.Skills.PetSkills
{
   import UI.Skills.SkillVO;
   import UI.UIInterface.IActiveSkillVO;
   
   public class PetActiveSkillVO extends SkillVO implements IActiveSkillVO
   {
      private var _originalHurt:int;
      
      private var _passiveAddHurt:int;
      
      private var _additionHurt:int;
      
      private var _hurCoefficient:int;
      
      private var _originalCoolDown:int;
      
      private var _passiveCoolDown:int;
      
      private var _originalManaCost:int;
      
      private var _manaCost:int;
      
      private var _originalEssenceCost:int;
      
      private var _passiveSkillChangeEssenseCost:int;
      
      private var _isCD:Boolean;
      
      private var _currentCDTime:int;
      
      private var _originalPkHurt:int;
      
      private var _pkHurt:int;
      
      private var _additionPkHurt:int;
      
      private var _pkHurtCoefficient:int;
      
      private var _subOrangeWalkSpeed:int;
      
      private var _subOrangeDefence:int;
      
      private var _diePrecentBase:int;
      
      private var _diePrecentHuanhua:int;
      
      private var _passiveYiShuJinTong:int;
      
      private var _passiveYiShuJinTong2:int;
      
      private var _buffTime:int;
      
      public function PetActiveSkillVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.originalHurt = _originalHurt;
         _antiwear.passiveAddHurt = _passiveAddHurt;
         _antiwear.additionHurt = _additionHurt;
         _antiwear.hurCoefficient = _hurCoefficient;
         _antiwear.originalCoolDown = _originalCoolDown;
         _antiwear.passiveCoolDown = _passiveCoolDown;
         _antiwear.passiveYiShuJinTong = _passiveYiShuJinTong;
         _antiwear.passiveYiShuJinTong2 = _passiveYiShuJinTong2;
         _antiwear.originalManaCost = _originalManaCost;
         _antiwear.isCD = _isCD;
         _antiwear.originalEssenceCost = _originalEssenceCost;
         _antiwear.passiveSkillChangeEssenseCost = _passiveSkillChangeEssenseCost;
         _antiwear.currentCDTime = _currentCDTime;
         _antiwear.originalPkHurt = _originalPkHurt;
         _antiwear.pkHurt = _pkHurt;
         _antiwear.additionPkHurt = _additionPkHurt;
         _antiwear.pkHurtCoefficient = _pkHurtCoefficient;
         _antiwear.subOrangeWalkSpeed = _subOrangeWalkSpeed;
         _antiwear.subOrangeDefence = _subOrangeDefence;
         _antiwear.diePrecentBase = _diePrecentBase;
         _antiwear.diePrecentHuanhua = _diePrecentHuanhua;
         _antiwear.buffTime = _buffTime;
      }
      
      override public function clone() : SkillVO
      {
         var _loc1_:PetActiveSkillVO = new PetActiveSkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribue(param1:SkillVO) : void
      {
         super.cloneAttribue(param1);
         (param1 as PetActiveSkillVO).originalHurt = this.originalHurt;
         (param1 as PetActiveSkillVO).passiveAddHurt = this.passiveAddHurt;
         (param1 as PetActiveSkillVO).additionHurt = this.additionHurt;
         (param1 as PetActiveSkillVO).hurCoefficient = this.hurCoefficient;
         (param1 as PetActiveSkillVO).originalCoolDown = this.originalCoolDown;
         (param1 as PetActiveSkillVO).passiveCoolDown = this.passiveCoolDown;
         (param1 as PetActiveSkillVO).passiveYiShuJinTong = this.passiveYiShuJinTong;
         (param1 as PetActiveSkillVO).passiveYiShuJinTong2 = this.passiveYiShuJinTong2;
         (param1 as PetActiveSkillVO).originalManaCost = this.originalManaCost;
         (param1 as PetActiveSkillVO).originalEssenceCost = this.originalEssenceCost;
         (param1 as PetActiveSkillVO).passiveSkillChangeEssenseCost = this.passiveSkillChangeEssenseCost;
         (param1 as PetActiveSkillVO).currentCDTime = this.currentCDTime;
         (param1 as PetActiveSkillVO).originalPkHurt = this.originalPkHurt;
         (param1 as PetActiveSkillVO).additionPkHurt = this.additionPkHurt;
         (param1 as PetActiveSkillVO).pkHurtCoefficient = this.pkHurtCoefficient;
         (param1 as PetActiveSkillVO).subOrangeWalkSpeed = this.subOrangeWalkSpeed;
         (param1 as PetActiveSkillVO).subOrangeDefence = this.subOrangeDefence;
         (param1 as PetActiveSkillVO).diePrecentBase = this.diePrecentBase;
         (param1 as PetActiveSkillVO).diePrecentHuanhua = this.diePrecentHuanhua;
         (param1 as PetActiveSkillVO).buffTime = this.buffTime;
         (param1 as PetActiveSkillVO).set("pkHurt2",get("pkHurt2"));
         (param1 as PetActiveSkillVO).set("coolDown2",get("coolDown2"));
         (param1 as PetActiveSkillVO).set("essenceCost2",get("essenceCost2"));
      }
      
      public function startCD() : void
      {
         currentCDTime = coolDown;
      }
      
      public function get diePrecent() : int
      {
         return diePrecentBase + diePrecentHuanhua;
      }
      
      public function get hurt() : int
      {
         return originalHurt + passiveAddHurt;
      }
      
      public function get pkHurt() : int
      {
         return originalPkHurt + int(get("pkHurt2"));
      }
      
      public function get subWalkSpeed() : int
      {
         return subOrangeWalkSpeed + passiveYiShuJinTong + passiveYiShuJinTong2;
      }
      
      public function get subDefence() : int
      {
         return subOrangeDefence + passiveYiShuJinTong + passiveYiShuJinTong2;
      }
      
      public function get coolDown() : int
      {
         return Math.max(originalCoolDown + passiveCoolDown + int(get("coolDown2")),1);
      }
      
      public function get manaCost() : int
      {
         return originalManaCost;
      }
      
      public function get essenceCost() : int
      {
         var _loc1_:int = originalEssenceCost + passiveSkillChangeEssenseCost + int(get("essenceCost2"));
         if(_loc1_ <= 0)
         {
            _loc1_ = 0;
         }
         return _loc1_;
      }
      
      public function set(param1:String, param2:String) : void
      {
         _antiwear[param1] = param2;
      }
      
      public function get(param1:String) : String
      {
         var _loc2_:String = null;
         try
         {
            _loc2_ = _antiwear[param1];
         }
         catch(e:Error)
         {
            _loc2_ = "";
         }
         return !!_loc2_ ? _loc2_ : "";
      }
      
      public function get additionHurt() : int
      {
         return _antiwear.additionHurt;
      }
      
      public function set additionHurt(param1:int) : void
      {
         _antiwear.additionHurt = param1;
      }
      
      public function get passiveAddHurt() : int
      {
         return _antiwear.passiveAddHurt;
      }
      
      public function set passiveAddHurt(param1:int) : void
      {
         _antiwear.passiveAddHurt = param1;
      }
      
      public function get originalHurt() : int
      {
         return _antiwear.originalHurt;
      }
      
      public function set originalHurt(param1:int) : void
      {
         _antiwear.originalHurt = param1;
      }
      
      public function get hurCoefficient() : int
      {
         return _antiwear.hurCoefficient;
      }
      
      public function set hurCoefficient(param1:int) : void
      {
         _antiwear.hurCoefficient = param1;
      }
      
      public function get originalCoolDown() : int
      {
         return _antiwear.originalCoolDown;
      }
      
      public function set originalCoolDown(param1:int) : void
      {
         _antiwear.originalCoolDown = param1;
      }
      
      public function get passiveCoolDown() : int
      {
         return _antiwear.passiveCoolDown;
      }
      
      public function set passiveCoolDown(param1:int) : void
      {
         _antiwear.passiveCoolDown = param1;
      }
      
      public function get passiveYiShuJinTong() : int
      {
         return _antiwear.passiveYiShuJinTong;
      }
      
      public function set passiveYiShuJinTong(param1:int) : void
      {
         _antiwear.passiveYiShuJinTong = param1;
      }
      
      public function get passiveYiShuJinTong2() : int
      {
         return _antiwear.passiveYiShuJinTong2;
      }
      
      public function set passiveYiShuJinTong2(param1:int) : void
      {
         _antiwear.passiveYiShuJinTong2 = param1;
      }
      
      public function set coolDown(param1:int) : void
      {
         throw new Error("这个冷却属性不能设置！");
      }
      
      public function get originalManaCost() : int
      {
         return _antiwear.originalManaCost;
      }
      
      public function set originalManaCost(param1:int) : void
      {
         _antiwear.originalManaCost = param1;
      }
      
      public function get isCD() : Boolean
      {
         return _antiwear.currentCDTime;
      }
      
      public function get originalEssenceCost() : int
      {
         return _antiwear.originalEssenceCost;
      }
      
      public function set originalEssenceCost(param1:int) : void
      {
         _antiwear.originalEssenceCost = param1;
      }
      
      public function get passiveSkillChangeEssenseCost() : int
      {
         return _antiwear.passiveSkillChangeEssenseCost;
      }
      
      public function set passiveSkillChangeEssenseCost(param1:int) : void
      {
         _antiwear.passiveSkillChangeEssenseCost = param1;
      }
      
      public function get currentCDTime() : int
      {
         return _antiwear.currentCDTime;
      }
      
      public function set currentCDTime(param1:int) : void
      {
         _antiwear.currentCDTime = Math.max(0,param1);
      }
      
      public function get additionPkHurt() : int
      {
         return _antiwear.additionPkHurt;
      }
      
      public function set additionPkHurt(param1:int) : void
      {
         _antiwear.additionPkHurt = param1;
      }
      
      public function get pkHurtCoefficient() : int
      {
         return _antiwear.pkHurtCoefficient;
      }
      
      public function set pkHurtCoefficient(param1:int) : void
      {
         _antiwear.pkHurtCoefficient = param1;
      }
      
      public function get originalPkHurt() : int
      {
         return _antiwear.originalPkHurt;
      }
      
      public function set originalPkHurt(param1:int) : void
      {
         _antiwear.originalPkHurt = param1;
      }
      
      public function get subOrangeWalkSpeed() : int
      {
         return _antiwear.subOrangeWalkSpeed;
      }
      
      public function set subOrangeWalkSpeed(param1:int) : void
      {
         _antiwear.subOrangeWalkSpeed = param1;
      }
      
      public function get subOrangeDefence() : int
      {
         return _antiwear.subOrangeDefence;
      }
      
      public function set subOrangeDefence(param1:int) : void
      {
         _antiwear.subOrangeDefence = param1;
      }
      
      public function get diePrecentBase() : int
      {
         return _antiwear.diePrecentBase;
      }
      
      public function set diePrecentBase(param1:int) : void
      {
         _antiwear.diePrecentBase = param1;
      }
      
      public function get diePrecentHuanhua() : int
      {
         return _antiwear.diePrecentHuanhua;
      }
      
      public function set diePrecentHuanhua(param1:int) : void
      {
         _antiwear.diePrecentHuanhua = param1;
      }
      
      public function get buffTime() : int
      {
         return _antiwear.buffTime;
      }
      
      public function set buffTime(param1:int) : void
      {
         _antiwear.buffTime = param1;
      }
   }
}

