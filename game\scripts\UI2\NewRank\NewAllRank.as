package UI2.NewRank
{
   import Json.MyJSON;
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.InitCompleteListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.PKUI.PKRankListColume;
   import UI.Players.Player;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SaveData;
   import YJFY.EntityShowContainer;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import com.utils.MD5;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.text.Font;
   import flash.text.TextField;
   
   public class NewAllRank extends MySprite
   {
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/getrank";
      
      private var m_show:MovieClip;
      
      private var m_ani:MovieClip;
      
      private var m_myloader:YJFYLoader;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_inFight:ButtonLogicShell2;
      
      private var m_lookBtn:ButtonLogicShell2;
      
      private var m_pagedown:ButtonLogicShell2;
      
      private var m_pageup:ButtonLogicShell2;
      
      private var m_role_1:MySwitchBtnLogicShell;
      
      private var m_role_2:MySwitchBtnLogicShell;
      
      private var m_role_3:MySwitchBtnLogicShell;
      
      private var m_role_4:MySwitchBtnLogicShell;
      
      private var m_role_5:MySwitchBtnLogicShell;
      
      private var m_role_6:MySwitchBtnLogicShell;
      
      private var m_role_7:MySwitchBtnLogicShell;
      
      private var m_role_8:MySwitchBtnLogicShell;
      
      private var m_role_9:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_otherPanel:PKPanelOne;
      
      private var m_playerlist:Vector.<Player>;
      
      private var m_lookPlayer:InitPlayersData;
      
      private var m_textPage:TextField;
      
      private var m_rankflag:MovieClip;
      
      private var m_itemlist:Vector.<MovieClip>;
      
      private var m_currRank:TextField;
      
      private var m_bestRank:TextField;
      
      private var m_textUid:TextField;
      
      protected var _player1ShowContainer:EntityShowContainer;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_rankLookListAPIListener:RankListAPIListener;
      
      private var m_uid:String;
      
      private var m_idx:int;
      
      private var m_playerData:Object;
      
      private var m_initMode:int;
      
      private var m_data:ItemAll;
      
      private var m_sprite:Sprite;
      
      protected var m_columes:Vector.<PKRankListColume>;
      
      protected var m_switchItemBtnGroup:SwitchBtnGroupLogicShell;
      
      private var font:Font;
      
      public function NewAllRank()
      {
         super();
         m_playerData = {};
         font = new FangZhengKaTongJianTi();
         m_quitBtn = new ButtonLogicShell2();
         m_pagedown = new ButtonLogicShell2();
         m_pageup = new ButtonLogicShell2();
         m_lookBtn = new ButtonLogicShell2();
         m_inFight = new ButtonLogicShell2();
         m_itemlist = new Vector.<MovieClip>();
         m_playerlist = new Vector.<Player>();
         m_sprite = new Sprite();
         m_rankLookListAPIListener = new RankListAPIListener();
         m_rankLookListAPIListener.getSaveDataErrorFun = rankListError1;
         m_rankLookListAPIListener.getSaveDataSuccessFun = getLookPlayerRankInfoSuccess;
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.getSaveDataErrorFun = rankListError1;
         m_rankListAPIListener.getSaveDataSuccessFun = getPlayerRankInfoSuccess;
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         _player1ShowContainer = new EntityShowContainer();
         _player1ShowContainer.init();
         addEventListener("clickButton",clickButton,true,0,true);
         m_initMode = 2777;
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         init();
      }
      
      private function rankListError1(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
         Part1.getInstance().closeNewRank();
         Part1.getInstance().returnCity();
      }
      
      private function getPlayerRankInfoSuccess(param1:SaveData) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         getSaveXMLSuccess(param1.getSaveData());
      }
      
      private function getLookPlayerRankInfoSuccess(param1:SaveData) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankLookListAPIListener);
         getSaveXMLLookSuccess(param1.getSaveData());
      }
      
      private function getSaveXMLLookSuccess(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(m_playerData == null)
         {
            return;
         }
         var _loc2_:InitCompleteListener = new InitCompleteListener();
         _loc2_.initCompleteFun = initLookComplete;
         initPlayerEquipData(m_uid,m_idx,param1,_loc2_);
      }
      
      private function initPlayerEquipData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         ClearUtil.clearObject(m_lookPlayer);
         m_lookPlayer = null;
         m_lookPlayer = new InitPlayersData();
         m_lookPlayer.uid = param1;
         m_lookPlayer.idx = param2;
         param4.addTarget = m_lookPlayer;
         m_lookPlayer.addInitCompleteListener(param4);
         m_lookPlayer.initPlayerData(param3,param2,m_initMode,true);
      }
      
      private function getSaveXMLSuccess(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(m_playerData == null)
         {
            return;
         }
         var _loc2_:InitCompleteListener = new InitCompleteListener();
         _loc2_.initCompleteFun = initComplete;
         initPlayerData(m_uid,m_idx,param1,_loc2_);
      }
      
      private function initLookComplete() : void
      {
         var _loc1_:InitPlayersData = getOnePlayerData(m_uid,m_idx);
         _loc1_.player1.playerVO.playerUid = m_uid;
         if(_loc1_.player2)
         {
            _loc1_.player2.playerVO.playerUid = m_uid;
         }
         if(!m_otherPanel)
         {
            ClearUtil.clearObject(m_playerlist);
            m_playerlist.length = 0;
            if(m_data.Sid % 10 == 1)
            {
               m_lookPlayer.player1.playerVO.playerUid = m_lookPlayer.uid;
               m_playerlist.push(m_lookPlayer.player1);
               m_otherPanel = new PKPanelOne();
               m_otherPanel.isTwoMode = false;
               m_otherPanel.initPKPanel(m_playerlist,m_lookPlayer.nickNameData);
            }
            else if(m_lookPlayer.player2 != null)
            {
               if(m_data.Sid % 10 == 2)
               {
                  m_lookPlayer.player2.playerVO.playerUid = m_lookPlayer.uid;
                  m_playerlist.push(m_lookPlayer.player2);
                  m_otherPanel = new PKPanelOne();
                  m_otherPanel.isTwoMode = false;
                  m_otherPanel.initPKPanel(m_playerlist,m_lookPlayer.nickNameData);
               }
            }
         }
         if(m_otherPanel != null && !getChildByName(m_otherPanel.name))
         {
            this.addChild(m_otherPanel);
         }
      }
      
      private function initComplete() : void
      {
         var _loc1_:InitPlayersData = getOnePlayerData(m_uid,m_idx);
         _loc1_.player1.playerVO.playerUid = m_uid;
         if(_loc1_.player2)
         {
            _loc1_.player2.playerVO.playerUid = m_uid;
         }
         if(_loc1_.player1.playerVO.name == m_data.Role)
         {
            _player1ShowContainer.getShow().visible = true;
            _player1ShowContainer.refreshPlayerShow(_loc1_.player1.playerVO);
         }
         else if(_loc1_.player2 != null && _loc1_.player2.playerVO.name == m_data.Role)
         {
            _player1ShowContainer.getShow().visible = true;
            _player1ShowContainer.refreshPlayerShow(_loc1_.player2.playerVO);
         }
         else
         {
            _player1ShowContainer.getShow().visible = false;
         }
      }
      
      private function getRoleByIndex(param1:int) : String
      {
         var _loc2_:String = null;
         if(param1 == 1)
         {
            _loc2_ = "SunWuKong";
         }
         else if(param1 == 2)
         {
            _loc2_ = "BaiLongMa";
         }
         else if(param1 == 3)
         {
            _loc2_ = "ErLangShen";
         }
         else if(param1 == 4)
         {
            _loc2_ = "ChangE";
         }
         else if(param1 == 5)
         {
            _loc2_ = "Fox";
         }
         else if(param1 == 6)
         {
            _loc2_ = "TieShan";
         }
         else if(param1 == 7)
         {
            _loc2_ = "Houyi";
         }
         else if(param1 == 9)
         {
            _loc2_ = "Zixia";
         }
         return _loc2_;
      }
      
      private function addInfoPanel() : void
      {
         var _loc1_:InitPlayersData = null;
         if(!m_otherPanel)
         {
            ClearUtil.clearObject(m_playerlist);
            m_playerlist.length = 0;
            if(m_playerData != null && m_playerData[m_uid] != null)
            {
               _loc1_ = m_playerData[m_uid][m_idx];
               if(_loc1_ != null && int(m_data.Sid % 10) == 1)
               {
                  _loc1_.player1.playerVO.playerUid = m_uid;
                  m_playerlist.push(_loc1_.player1);
                  m_otherPanel = new PKPanelOne();
                  m_otherPanel.isTwoMode = false;
                  m_otherPanel.initPKPanel(m_playerlist,_loc1_.nickNameData);
               }
               else if(_loc1_ != null && int(m_data.Sid % 10) == 2)
               {
                  _loc1_.player2.playerVO.playerUid = m_uid;
                  m_playerlist.push(_loc1_.player2);
                  m_otherPanel = new PKPanelOne();
                  m_otherPanel.isTwoMode = false;
                  m_otherPanel.initPKPanel(m_playerlist,_loc1_.nickNameData);
               }
            }
         }
         if(m_otherPanel != null && !getChildByName(m_otherPanel.name))
         {
            this.addChild(m_otherPanel);
         }
      }
      
      private function initPlayerData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         if(m_playerData[param1] == null)
         {
            m_playerData[param1] = [];
         }
         var _loc5_:InitPlayersData = new InitPlayersData();
         m_playerData[param1][param2] = _loc5_;
         _loc5_.uid = param1;
         _loc5_.idx = param2;
         param4.addTarget = _loc5_;
         _loc5_.addInitCompleteListener(param4);
         _loc5_.initPlayerData(param3,param2,m_initMode,true);
      }
      
      private function getOnePlayerData(param1:String, param2:int) : InitPlayersData
      {
         if(m_playerData[param1] == null)
         {
            return null;
         }
         return m_playerData[param1][param2];
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(font);
         font = null;
         ClearUtil.nullArr(m_columes);
         m_columes = null;
         ClearUtil.clearObject(m_switchItemBtnGroup);
         m_switchItemBtnGroup = null;
         ClearUtil.clearObject(m_lookPlayer);
         m_lookPlayer = null;
         ClearUtil.clearObject(m_rankLookListAPIListener);
         m_rankLookListAPIListener = null;
         ClearUtil.clearObject(m_sprite);
         m_sprite = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_lookBtn);
         m_lookBtn = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_playerlist);
         m_playerlist = null;
         ClearUtil.clearObject(m_otherPanel);
         m_otherPanel = null;
         ClearUtil.clearObject(m_role_1);
         m_role_1 = null;
         ClearUtil.clearObject(m_role_2);
         m_role_2 = null;
         ClearUtil.clearObject(m_role_3);
         m_role_3 = null;
         ClearUtil.clearObject(m_role_4);
         m_role_4 = null;
         ClearUtil.clearObject(m_role_5);
         m_role_5 = null;
         ClearUtil.clearObject(m_role_6);
         m_role_6 = null;
         ClearUtil.clearObject(m_role_7);
         m_role_7 = null;
         ClearUtil.clearObject(m_role_8);
         m_role_8 = null;
         ClearUtil.clearObject(m_role_9);
         m_role_9 = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(m_inFight);
         m_inFight = null;
         ClearUtil.clearObject(_player1ShowContainer);
         _player1ShowContainer = null;
         ClearUtil.clearObject(m_playerData);
         m_playerData = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         this.removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
         removeEventListener("clickButton",clickButton,true);
      }
      
      public function init() : void
      {
         m_myloader.getClass("allnewrank.swf","allnewrank",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         this.addChild(m_show);
         this.initShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function initShow() : void
      {
         var _loc1_:PKRankListColume = null;
         m_quitBtn.setShow(m_show["closebtn"]);
         m_quitBtn.setTipString("关闭");
         m_inFight.setShow(m_show["infight"]);
         m_inFight.setTipString("进入挑战");
         m_lookBtn.setShow(m_show["btnlookequip"]);
         m_lookBtn.setTipString("查看装备");
         m_role_1 = new MySwitchBtnLogicShell();
         m_role_2 = new MySwitchBtnLogicShell();
         m_role_3 = new MySwitchBtnLogicShell();
         m_role_4 = new MySwitchBtnLogicShell();
         m_role_5 = new MySwitchBtnLogicShell();
         m_role_6 = new MySwitchBtnLogicShell();
         m_role_7 = new MySwitchBtnLogicShell();
         m_role_8 = new MySwitchBtnLogicShell();
         m_role_9 = new MySwitchBtnLogicShell();
         m_role_1.setShow(m_show["role_8"]);
         m_role_2.setShow(m_show["role_1"]);
         m_role_3.setShow(m_show["role_2"]);
         m_role_4.setShow(m_show["role_3"]);
         m_role_5.setShow(m_show["role_4"]);
         m_role_6.setShow(m_show["role_5"]);
         m_role_7.setShow(m_show["role_6"]);
         m_role_8.setShow(m_show["role_7"]);
         m_role_9.setShow(m_show["role_9"]);
         m_role_1.turnActivate();
         m_role_2.turnUnActivate();
         m_role_3.turnUnActivate();
         m_role_4.turnUnActivate();
         m_role_5.turnUnActivate();
         m_role_6.turnUnActivate();
         m_role_7.turnUnActivate();
         m_role_8.turnUnActivate();
         m_role_9.turnUnActivate();
         m_role_2.setTipString("孙悟空");
         m_role_3.setTipString("白龙马");
         m_role_4.setTipString("二郎神");
         m_role_5.setTipString("嫦娥");
         m_role_6.setTipString("灵狐");
         m_role_7.setTipString("铁扇");
         m_role_8.setTipString("后羿");
         m_role_1.setTipString("总榜");
         m_role_9.setTipString("紫霞仙子");
         m_switchBtnGroup.addSwitchBtn(m_role_1);
         m_switchBtnGroup.addSwitchBtn(m_role_2);
         m_switchBtnGroup.addSwitchBtn(m_role_3);
         m_switchBtnGroup.addSwitchBtn(m_role_4);
         m_switchBtnGroup.addSwitchBtn(m_role_5);
         m_switchBtnGroup.addSwitchBtn(m_role_6);
         m_switchBtnGroup.addSwitchBtn(m_role_7);
         m_switchBtnGroup.addSwitchBtn(m_role_8);
         m_switchBtnGroup.addSwitchBtn(m_role_9);
         m_textPage = m_show["pagetext"] as TextField;
         m_pagedown.setShow(m_show["pagedown"]);
         m_pagedown.setTipString("下一页");
         m_pageup.setShow(m_show["pageup"]);
         m_pageup.setTipString("上一页");
         m_rankflag = m_show["rankflag"] as MovieClip;
         m_rankflag.visible = false;
         var _loc2_:int = 0;
         m_columes = new Vector.<PKRankListColume>();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         _loc2_ = 0;
         while(_loc2_ < 7)
         {
            _loc1_ = new PKRankListColume();
            _loc1_.setShow(m_show["item_" + (_loc2_ + 1)]);
            _loc1_.setTipString("点击查看该玩家");
            _loc1_.getShow().visible = false;
            m_columes.push(_loc1_);
            m_switchBtnGroup.addSwitchBtn(_loc1_);
            _loc2_++;
         }
         m_currRank = m_show["textcurrank"];
         m_bestRank = m_show["textmax"];
         m_textUid = m_show["textuid"];
         m_ani = m_show["touming"] as MovieClip;
         m_sprite.addChild(_player1ShowContainer.getShow());
         m_sprite.scaleY = 1.5;
         m_sprite.scaleX = 1.5;
         m_ani.addChild(m_sprite);
         this.addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         RankDataInfo.getInstance().setNowRankId(1);
         sendInfo();
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(m_otherPanel)
         {
            ClearUtil.clearObject(m_otherPanel);
            m_otherPanel = null;
         }
      }
      
      private function callback(param1:MouseEvent) : void
      {
         var _loc2_:int = int(String(param1.currentTarget.name).split("_")[1]);
         hideall();
         (m_itemlist[_loc2_ - 1]["check"] as MovieClip).visible = true;
         rankinfo((RankDataInfo.getInstance().getPageIndex() - 1) * 7 + _loc2_ - 1);
      }
      
      private function hideall() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_itemlist.length)
         {
            (m_itemlist[_loc1_]["check"] as MovieClip).visible = false;
            _loc1_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         switch(param1.button)
         {
            case m_quitBtn:
               if(RankDataInfo.getInstance().getInType() == 1)
               {
                  GamingUI.getInstance().closeNo1Rank();
                  break;
               }
               if(RankDataInfo.getInstance().getInType() == 2)
               {
                  if(this.parent)
                  {
                     Part1.getInstance().continueGame();
                     this.parent.removeChild(this);
                  }
               }
               break;
            case m_lookBtn:
               if(RankDataInfo.getInstance().getDataByIndex((RankDataInfo.getInstance().getPageIndex() - 1) * 7 + 0))
               {
                  getLookData();
               }
               break;
            case m_pagedown:
               if(RankDataInfo.getInstance().getPageIndex() < RankDataInfo.getInstance().getPageNum())
               {
                  RankDataInfo.getInstance().setPageIndex(RankDataInfo.getInstance().getPageIndex() + 1);
                  refreshList();
               }
               break;
            case m_pageup:
               if(RankDataInfo.getInstance().getPageIndex() > 1)
               {
                  RankDataInfo.getInstance().setPageIndex(RankDataInfo.getInstance().getPageIndex() - 1);
                  refreshList();
               }
               break;
            case m_inFight:
               if(RankDataInfo.getInstance().getInType() == 1)
               {
                  GamingUI.getInstance().closeNo1Rank();
                  Part1.getInstance().openNewRank();
                  break;
               }
               if(RankDataInfo.getInstance().getInType() == 2)
               {
                  if(this.parent)
                  {
                     Part1.getInstance().continueGame();
                     this.parent.removeChild(this);
                  }
               }
               break;
            case m_role_1:
               if(RankDataInfo.getInstance().getNowRankId() != 1)
               {
                  RankDataInfo.getInstance().setNowRankId(1);
                  sendInfo();
               }
               break;
            case m_role_2:
               if(RankDataInfo.getInstance().getNowRankId() != 2)
               {
                  RankDataInfo.getInstance().setNowRankId(2);
                  sendInfo();
               }
               break;
            case m_role_3:
               if(RankDataInfo.getInstance().getNowRankId() != 3)
               {
                  RankDataInfo.getInstance().setNowRankId(3);
                  sendInfo();
               }
               break;
            case m_role_4:
               if(RankDataInfo.getInstance().getNowRankId() != 4)
               {
                  RankDataInfo.getInstance().setNowRankId(4);
                  sendInfo();
               }
               break;
            case m_role_5:
               if(RankDataInfo.getInstance().getNowRankId() != 5)
               {
                  RankDataInfo.getInstance().setNowRankId(5);
                  sendInfo();
               }
               break;
            case m_role_6:
               if(RankDataInfo.getInstance().getNowRankId() != 6)
               {
                  RankDataInfo.getInstance().setNowRankId(6);
                  sendInfo();
               }
               break;
            case m_role_7:
               if(RankDataInfo.getInstance().getNowRankId() != 7)
               {
                  RankDataInfo.getInstance().setNowRankId(7);
                  sendInfo();
               }
               break;
            case m_role_8:
               if(RankDataInfo.getInstance().getNowRankId() != 8)
               {
                  RankDataInfo.getInstance().setNowRankId(8);
                  sendInfo();
               }
               break;
            case m_role_9:
               if(RankDataInfo.getInstance().getNowRankId() != 9)
               {
                  RankDataInfo.getInstance().setNowRankId(9);
                  sendInfo();
                  break;
               }
         }
         if(m_columes != null)
         {
            _loc2_ = int(m_columes.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(param1.button == m_columes[_loc3_])
               {
                  rankinfo((RankDataInfo.getInstance().getPageIndex() - 1) * 7 + _loc3_);
                  return;
               }
               _loc3_++;
            }
         }
      }
      
      private function sendInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorHandler);
         _loc1_.addEventListener("securityError",onErrorHandler);
         GamingUI.getInstance().manBan.text.text = "信息加载中， 请勿关闭游戏！";
         var _loc2_:URLRequest = new URLRequest(this.m_strOpenUrl);
         _loc2_.method = "POST";
         var _loc4_:Object = {};
         _loc4_.RankID = RankDataInfo.getInstance().getNowRankId();
         _loc4_.Begin = 1;
         _loc4_.End = 100;
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.JSON = MyJSON.encode(_loc4_);
         _loc3_.MD5 = MD5.hash(MyJSON.encode(_loc4_) + "dd9c8b2f13cadff77e0984e863470cd3");
         _loc2_.data = _loc3_;
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().initAllRankData(_loc2_);
         RankDataInfo.getInstance().setPageIndex(1);
         refreshList();
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function refreshList() : void
      {
         var _loc3_:ItemAll = null;
         var _loc2_:MovieClip = null;
         var _loc1_:TextField = null;
         var _loc5_:int = 0;
         refreshPageIndex();
         var _loc4_:int = RankDataInfo.getInstance().getPageIndex();
         _loc5_ = 0;
         while(_loc5_ < 7)
         {
            _loc3_ = RankDataInfo.getInstance().getDataByIndex((_loc4_ - 1) * 7 + _loc5_);
            if(_loc3_)
            {
               m_columes[_loc5_].getShow().visible = true;
               _loc2_ = m_columes[_loc5_].getShow()["placeIcon"] as MovieClip;
               _loc1_ = m_columes[_loc5_].getShow()["rankText"] as TextField;
               MyFunction2.changeTextFieldFont(font.fontName,_loc1_);
               if((_loc4_ - 1) * 7 + _loc5_ == 0)
               {
                  _loc2_.gotoAndStop(1);
                  _loc2_.visible = true;
                  _loc1_.visible = false;
               }
               else if((_loc4_ - 1) * 7 + _loc5_ == 1)
               {
                  _loc2_.gotoAndStop(2);
                  _loc2_.visible = true;
                  _loc1_.visible = false;
               }
               else if((_loc4_ - 1) * 7 + _loc5_ == 2)
               {
                  _loc2_.gotoAndStop(3);
                  _loc2_.visible = true;
                  _loc1_.visible = false;
               }
               else
               {
                  _loc2_.visible = false;
                  _loc1_.visible = true;
               }
               _loc1_.text = String((_loc4_ - 1) * 7 + _loc5_ + 1);
               _loc1_ = m_columes[_loc5_].getShow()["playerNameText"] as TextField;
               _loc1_.text = _loc3_.Name;
               MyFunction2.changeTextFieldFont(font.fontName,_loc1_);
               _loc1_ = m_columes[_loc5_].getShow()["winMatchNumText"] as TextField;
               _loc1_.text = String(_loc3_.BestRank);
               MyFunction2.changeTextFieldFont(font.fontName,_loc1_);
            }
            else
            {
               m_columes[_loc5_].getShow().visible = false;
            }
            if(_loc5_ == 0)
            {
               rankinfo((_loc4_ - 1) * 7 + _loc5_);
            }
            _loc5_++;
         }
      }
      
      private function refreshPageIndex() : void
      {
         if(RankDataInfo.getInstance().getPageNum() == 0)
         {
            m_textPage.text = "1/1";
         }
         else
         {
            m_textPage.text = String(RankDataInfo.getInstance().getPageIndex()) + "/" + String(RankDataInfo.getInstance().getPageNum());
         }
         MyFunction2.changeTextFieldFont(font.fontName,m_textPage);
      }
      
      private function rankinfo(param1:int) : void
      {
         if(RankDataInfo.getInstance().getDataByIndex(param1) != null)
         {
            m_textUid.text = RankDataInfo.getInstance().getUidByIndex(param1);
            m_bestRank.text = String(RankDataInfo.getInstance().getDataByIndex(param1).BestRank);
            m_currRank.text = String(param1 + 1);
            MyFunction2.changeTextFieldFont(font.fontName,m_textUid);
            MyFunction2.changeTextFieldFont(font.fontName,m_bestRank);
            MyFunction2.changeTextFieldFont(font.fontName,m_currRank);
            m_data = RankDataInfo.getInstance().getDataByIndex(param1);
            m_uid = RankDataInfo.getInstance().getUidByIndex(param1);
            m_idx = RankDataInfo.getInstance().getIdxByIndex(param1);
            RankDataInfo.getInstance().setFightSid(m_data.Sid);
            if(param1 == 0)
            {
               m_rankflag.visible = true;
               m_rankflag.gotoAndStop(1);
            }
            else if(param1 == 1)
            {
               m_rankflag.visible = true;
               m_rankflag.gotoAndStop(2);
            }
            else if(param1 == 2)
            {
               m_rankflag.visible = true;
               m_rankflag.gotoAndStop(3);
            }
            else
            {
               m_rankflag.visible = false;
            }
            getSaveData();
         }
         else
         {
            m_textUid.text = "";
            m_bestRank.text = "";
            m_currRank.text = "";
            _player1ShowContainer.getShow().visible = false;
            m_rankflag.visible = false;
         }
      }
      
      private function getSaveData() : void
      {
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getSaveData(m_uid,m_idx);
      }
      
      private function getLookData() : void
      {
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankLookListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getSaveData(m_uid,m_idx);
      }
      
      private function sendDel() : void
      {
      }
   }
}

