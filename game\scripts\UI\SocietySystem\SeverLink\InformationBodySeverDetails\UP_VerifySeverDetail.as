package UI.SocietySystem.SeverLink.InformationBodySeverDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.external.ExternalInterface;
   import flash.utils.ByteArray;
   
   public class UP_VerifySeverDetail extends UP_SeverDetail
   {
      private var m_checkCookieLen:int;
      
      private var m_checkCookie:String;
      
      public function UP_VerifySeverDetail()
      {
         super();
      }
      
      override public function setCheckValue(param1:int) : void
      {
         var _loc2_:Object = null;
         super.setCheckValue(param1);
         if(ExternalInterface.available)
         {
            _loc2_ = ExternalInterface.call("JK.Cookie.getAll");
            m_checkCookie = _loc2_["Pauth"];
         }
         else
         {
            m_checkCookie = "";
         }
         m_checkCookieLen = new InformationBodyDetailUtil().getStringLength(m_checkCookie);
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_checkValue);
         _loc1_.writeInt(m_checkCookieLen);
         if(m_checkCookieLen)
         {
            _loc1_.writeUTFBytes(m_checkCookie);
            trace("发送Pauth：",m_checkCookie);
         }
         _loc1_.position = 0;
         return _loc1_;
      }
   }
}

