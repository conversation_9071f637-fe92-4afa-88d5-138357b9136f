package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddRegMpSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addRegMp:Number;
      
      public function AddRegMpSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("regMp_mountAdd1",m_targetPlayer.playerVO.get2("regMp_mountAdd1") - m_addValue);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addRegMp;
         m_targetPlayer.playerVO.set2("regMp_mountAdd1",m_targetPlayer.playerVO.get2("regMp_mountAdd1") + m_addValue);
      }
      
      public function getAddRegMp() : Number
      {
         return addRegMp;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addRegMp = Number(param1.data.(@att == "addRegMp")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addRegMp = m_addRegMp;
      }
      
      private function get addRegMp() : Number
      {
         return _antiwear.addRegMp;
      }
      
      private function set addRegMp(param1:Number) : void
      {
         _antiwear.addRegMp = param1;
      }
   }
}

