package UI2.TipDataShow
{
   import UI.EnterFrameTime;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class TipDataShow
   {
      private const m_const_start_bg_label:String = "start_bg";
      
      private const m_const_startEnd_bg_label:String = "startEnd_bg";
      
      private const m_const_start_txt_label:String = "start_txt";
      
      private const m_const_startEnd_txt_label:String = "startEnd_txt";
      
      private const m_const_end_txt_label:String = "end_txt";
      
      private const m_const_endEnd_txt_label:String = "endEnd_txt";
      
      private const m_const_end_bg_label:String = "end_bg";
      
      private const m_const_endEnd_bg_label:String = "endEnd_bg";
      
      private const m_const_start_bg_state:String = "start_bg";
      
      private const m_const_start_txt_state:String = "start_txt";
      
      private const m_const_normal_state:String = "normal";
      
      private const m_const_end_bg_state:String = "end_bg";
      
      private const m_const_end_txt_state:String = "end_txt";
      
      private var m_showMC:AnimationShowPlayLogicShell;
      
      private var m_frameLabelListener:AnimationPlayFrameLabelListener;
      
      private var m_tipData:TipData;
      
      private var m_isInAnimation:Boolean;
      
      private var m_currentState:String;
      
      private var m_show:MovieClip;
      
      private var m_text:TextField;
      
      private var m_currentOneTipData:OneTipData;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      public function TipDataShow()
      {
         super();
         m_showMC = new AnimationShowPlayLogicShell();
         m_frameLabelListener = new AnimationPlayFrameLabelListener();
         m_frameLabelListener.reachFrameLabelFun = reachFrameLabel;
         m_showMC.addFrameLabelListener(m_frameLabelListener);
         m_tipData = new TipData();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_frameLabelListener);
         m_frameLabelListener = null;
         ClearUtil.clearObject(m_tipData);
         m_tipData = null;
         m_currentState = null;
         m_show = null;
         m_text = null;
         m_currentOneTipData = null;
         m_enterFrameTime = null;
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_currentOneTipData)
         {
            if(m_currentOneTipData.getIsHideTime(param1.getElapsedTime()))
            {
               toNextOneTipData();
            }
         }
         else
         {
            updateOneTipData();
         }
      }
      
      private function initShow() : void
      {
         m_showMC.setShow(m_show);
      }
      
      private function initShow2() : void
      {
         m_tipData.initByXML(XMLSingle.getInstance().textXML.tipData[0]);
      }
      
      private function showOneTipData(param1:OneTipData) : void
      {
         if(showMCGotoAndPlay("start_txt"))
         {
            m_currentState = "start_txt";
            m_text = m_show["textMC"]["text"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_text);
            m_text.text = param1.showText(m_enterFrameTime.getElapsedTime());
         }
      }
      
      private function toNextOneTipData() : void
      {
         if(showMCGotoAndPlay("end_txt"))
         {
            m_currentState = "end_txt";
         }
      }
      
      private function toNextOneTipData2() : void
      {
         updateOneTipData();
      }
      
      private function updateOneTipData() : void
      {
         var _loc1_:OneTipData = m_currentOneTipData;
         m_currentOneTipData = m_tipData.getNextShowOneTipData(m_currentOneTipData);
         while(Boolean(m_currentOneTipData) && currentOneTipDataIsAbleShow() == false)
         {
            m_currentOneTipData = m_tipData.getNextShowOneTipData(m_currentOneTipData);
         }
         if(currentOneTipDataIsAbleShow())
         {
            if(_loc1_)
            {
               showOneTipData(m_currentOneTipData);
            }
            else if(showMCGotoAndPlay("start_bg"))
            {
               m_currentState = "start_bg";
            }
            return;
         }
         if(m_currentState == "normal")
         {
            endShowTipData();
         }
      }
      
      private function endShowTipData() : void
      {
         m_text = null;
         if(showMCGotoAndPlay("end_bg"))
         {
            m_currentState = "end_bg";
         }
      }
      
      private function currentOneTipDataIsAbleShow() : Boolean
      {
         return Boolean(m_currentOneTipData) && m_currentOneTipData.getIsAbleShowText(m_enterFrameTime.getElapsedTime());
      }
      
      private function showMCGotoAndPlay(param1:String) : Boolean
      {
         if(m_isInAnimation)
         {
            return false;
         }
         m_showMC.gotoAndPlay(param1);
         m_isInAnimation = true;
         return true;
      }
      
      private function reachFrameLabel(param1:String) : void
      {
         switch(param1)
         {
            case "startEnd_bg":
               m_isInAnimation = false;
               showOneTipData(m_currentOneTipData);
               break;
            case "startEnd_txt":
               m_isInAnimation = false;
               m_currentState = "normal";
               m_showMC.stop();
               break;
            case "endEnd_txt":
               m_isInAnimation = false;
               toNextOneTipData2();
               break;
            case "endEnd_bg":
               m_isInAnimation = false;
               m_currentState == null;
               m_showMC.stop();
         }
      }
   }
}

