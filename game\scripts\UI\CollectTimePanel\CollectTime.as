package UI.CollectTimePanel
{
   import UI.Players.PlayerVO;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class CollectTime
   {
      public function CollectTime()
      {
         super();
      }
      
      public function transformTimeToExp(param1:String, param2:String, param3:CollectTimeData, param4:PlayerVO, param5:CollectTimeReturnData) : void
      {
         param5.setIsStartCollet(false);
         param5.setCollectTime(0);
         param5.setAbleTransformExp(0);
         param5.setAbleTransformExpByTicket(0);
         if(Boolean(param1) == false)
         {
            param5.setIsStartCollet(false);
            param5.setIsLessMinTime(true);
            param5.setIsMoreMaxTime(false);
            return;
         }
         var _loc6_:Number = new TimeUtil().timeInterval(param1,param2);
         transformTimeToExp2(_loc6_,param3,param4,param5);
      }
      
      public function transformTimeToExp2(param1:Number, param2:CollectTimeData, param3:PlayerVO, param4:CollectTimeReturnData) : void
      {
         param1 = Math.max(0,param1);
         param4.setCollectTime(param1 * 3600000);
         if(param1 <= param2.getMinTime())
         {
            param4.setAbleTransformExp(Math.pow(param3.level,2) / param2.getCofficient() * param4.getCollectTime());
            param4.setAbleTransformExpByTicket(param4.getAbleTransformExp() * param2.getMultiOfTicket());
            param4.setIsStartCollet(true);
            param4.setIsLessMinTime(true);
            param4.setIsMoreMaxTime(false);
            return;
         }
         if(param1 >= param2.getMaxTime())
         {
            param4.setCollectTime(param2.getMaxTime() * 3600000);
            param4.setAbleTransformExp(Math.pow(param3.level,2) / param2.getCofficient() * param4.getCollectTime());
            param4.setAbleTransformExpByTicket(param4.getAbleTransformExp() * param2.getMultiOfTicket());
            param4.setIsStartCollet(true);
            param4.setIsLessMinTime(false);
            param4.setIsMoreMaxTime(true);
            return;
         }
         param4.setIsStartCollet(true);
         param4.setAbleTransformExp(Math.pow(param3.level,2) / param2.getCofficient() * param4.getCollectTime());
         param4.setAbleTransformExpByTicket(param4.getAbleTransformExp() * param2.getMultiOfTicket());
         param4.setIsLessMinTime(false);
         param4.setIsMoreMaxTime(false);
      }
   }
}

