package UI
{
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   
   public class EquipmentArea extends MySprite
   {
      private var _equipmentCells:Vector.<IEquipmentCell> = new Vector.<IEquipmentCell>();
      
      protected var _widthSize:int;
      
      protected var _heightSize:int;
      
      protected var _cellClass:Class;
      
      private var m_num:int = 0;
      
      public function EquipmentArea(param1:int, param2:int, param3:Class)
      {
         super();
         _cellClass = param3;
         _widthSize = param1;
         _heightSize = param2;
         m_num = 0;
      }
      
      public function initArea(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc2_:IEquipmentCell = null;
         m_num = param1;
         var _loc3_:* = param1;
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.nullArr(_equipmentCells);
         _equipmentCells = new Vector.<IEquipmentCell>();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = new _cellClass();
            _loc2_.x = _loc4_ % _widthSize * _loc2_.width + _loc2_.width / 2;
            _loc2_.y = int(_loc4_ / _widthSize) * _loc2_.height + _loc2_.height / 2;
            _loc2_.name = "EquipmentCell" + _loc4_;
            _loc2_.id = _loc4_;
            _equipmentCells.push(_loc2_);
            this.addChild(_loc2_ as DisplayObject);
            _loc4_++;
         }
      }
      
      public function isLoaded() : Boolean
      {
         if(numChildren >= m_num)
         {
            return true;
         }
         return false;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(_equipmentCells);
         _equipmentCells = null;
         _cellClass = null;
      }
      
      public function get equipmentCells() : Vector.<IEquipmentCell>
      {
         return _equipmentCells;
      }
   }
}

