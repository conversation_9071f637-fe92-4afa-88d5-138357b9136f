package UI.WorldBoss.<PERSON><PERSON>hao
{
   import UI.WorldBoss.Boss.Boss;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   
   public class <PERSON><PERSON><PERSON>
   {
      protected var m_world:StepAttackGameWorld;
      
      protected var m_boss:Boss;
      
      public function <PERSON><PERSON><PERSON>()
      {
         super();
      }
      
      public function clear() : void
      {
         m_world = null;
         m_boss = null;
      }
      
      public function initByXML(param1:XML) : void
      {
      }
      
      public function isRunDa<PERSON>hao() : Boolean
      {
         return false;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_world = param1;
      }
      
      public function setBoss(param1:Boss) : void
      {
         m_boss = param1;
      }
   }
}

