package UI.newTask.NewMainTask
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MainLineTask.PhaseTaskList;
   import UI.MainLineTask.TaskGoalVOFactory;
   import UI.MainLineTask.TaskGoalVO_MainTask;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class NewMainTaskData extends DataManagerParent
   {
      private static var _instance:NewMainTaskData;
      
      public var phaseTaskLists:Vector.<PhaseTaskList>;
      
      public var currentPhase:String;
      
      public var currentPhaseTaskList:PhaseTaskList;
      
      public var currentTask:MainLineTaskVO;
      
      public var m_bComp:int = 0;
      
      private var m_savexml:XML;
      
      public function NewMainTaskData()
      {
         super();
      }
      
      public static function getInstance() : NewMainTaskData
      {
         if(_instance == null)
         {
            _instance = new NewMainTaskData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(phaseTaskLists);
         phaseTaskLists = null;
      }
      
      public function getIsComp() : Boolean
      {
         var _loc1_:int = 0;
         if(m_savexml.hasOwnProperty("NewMainLineTask"))
         {
            if(m_savexml.NewMainLineTask[0].hasOwnProperty("compatibility"))
            {
               _loc1_ = int(m_savexml.NewMainLineTask[0].compatibility[0].@value);
            }
            else
            {
               _loc1_ = 0;
            }
         }
         else
         {
            _loc1_ = 0;
         }
         if(_loc1_ == 1)
         {
            return true;
         }
         return false;
      }
      
      public function initByXML(param1:XML, param2:XML, param3:XML, param4:XML) : void
      {
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:PhaseTaskList = null;
         m_savexml = param1;
         if(param1.hasOwnProperty("NewMainLineTask"))
         {
            if(param1.NewMainLineTask[0].hasOwnProperty("compatibility"))
            {
               m_bComp = int(param1.NewMainLineTask[0].compatibility[0].@value);
            }
            else
            {
               m_bComp = 0;
            }
         }
         else
         {
            m_bComp = 0;
         }
         if(phaseTaskLists)
         {
            ClearUtil.clearObject(phaseTaskLists);
            phaseTaskLists = null;
         }
         var _loc7_:XMLList = param2.tasks;
         _loc6_ = int(!!_loc7_ ? _loc7_.length() : 0);
         phaseTaskLists = new Vector.<PhaseTaskList>();
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            _loc5_ = new PhaseTaskList();
            _loc5_.initByXML(_loc7_[_loc8_],param4,param3);
            phaseTaskLists.push(_loc5_);
            _loc8_++;
         }
         if(m_bComp == 0)
         {
            if(!param1.hasOwnProperty("MainLineTask") && !param1.hasOwnProperty("NewMainLineTask"))
            {
               currentPhase = phaseTaskLists[0].phase;
               currentPhaseTaskList = phaseTaskLists[0];
               currentTask = currentPhaseTaskList.taskVOs[0];
               currentTask.isWorking = true;
               m_bComp = 1;
               return;
            }
            if(!param1.hasOwnProperty("MainLineTask") && param1.hasOwnProperty("NewMainLineTask"))
            {
               compatibility3(param1,param3);
               m_bComp = 1;
               return;
            }
            if(param1.hasOwnProperty("MainLineTask") && !param1.hasOwnProperty("NewMainLineTask"))
            {
               compatibility(param1,param3);
               m_bComp = 1;
               return;
            }
            if(param1.hasOwnProperty("MainLineTask") && param1.hasOwnProperty("NewMainLineTask"))
            {
               compatibility2(param1,param3);
               m_bComp = 1;
               return;
            }
         }
         else
         {
            if(!param1.hasOwnProperty("MainLineTask") && !param1.hasOwnProperty("NewMainLineTask"))
            {
               currentPhase = phaseTaskLists[0].phase;
               currentPhaseTaskList = phaseTaskLists[0];
               currentTask = currentPhaseTaskList.taskVOs[0];
               currentTask.isWorking = true;
               m_bComp = 1;
               return;
            }
            compatibility3(param1,param3);
            m_bComp = 1;
         }
      }
      
      private function compatibility3(param1:XML, param2:XML) : void
      {
         var _loc11_:int = 0;
         var _loc4_:int = 0;
         var _loc12_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:MainLineTaskVO = null;
         var _loc3_:String = null;
         var _loc16_:String = null;
         var _loc5_:* = undefined;
         var _loc6_:XML = param1.NewMainLineTask[0];
         currentPhase = String(_loc6_.@phase);
         var _loc9_:int = 0;
         var _loc13_:int = !!phaseTaskLists ? phaseTaskLists.length : 0;
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            if(currentPhase == phaseTaskLists[_loc9_].phase)
            {
               currentPhaseTaskList = phaseTaskLists[_loc9_];
               _loc11_ = 0;
               while(_loc11_ < _loc9_)
               {
                  _loc12_ = int(phaseTaskLists[_loc11_].taskVOs.length);
                  _loc4_ = 0;
                  while(_loc4_ < _loc12_)
                  {
                     phaseTaskLists[_loc11_].taskVOs[_loc4_].isGotReward = true;
                     phaseTaskLists[_loc11_].taskVOs[_loc4_].isWorking = false;
                     _loc4_++;
                  }
                  _loc11_++;
               }
               break;
            }
            _loc9_++;
         }
         var _loc15_:XMLList = _loc6_.task;
         _loc13_ = int(!!_loc15_ ? _loc15_.length() : 0);
         var _loc14_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            _loc3_ = String(_loc15_[_loc9_].@id);
            _loc16_ = String(_loc15_[_loc9_].@isWorking);
            _loc8_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
            _loc7_ = 0;
            while(_loc7_ < _loc8_)
            {
               _loc10_ = currentPhaseTaskList.taskVOs[_loc7_];
               if(_loc10_.id == _loc3_)
               {
                  _loc10_.isGotReward = Boolean(int(_loc15_[_loc9_].@isGotReward));
                  if(_loc10_.isGotReward == false)
                  {
                     if(_loc16_ == "1")
                     {
                        _loc10_.isWorking = true;
                        currentTask = _loc10_;
                        _loc5_ = _loc14_.createTaskGoalsByXML(_loc15_[_loc9_],param2);
                        _loc10_.addTaskGoalsToCurrentTaskGoalVOs(_loc5_);
                        ClearUtil.nullArr(_loc5_,false,false,false);
                        break;
                     }
                     if(_loc16_ == "0")
                     {
                        _loc10_.isWorking = false;
                        _loc5_ = _loc14_.createTaskGoalsByXML(_loc15_[_loc9_],param2);
                        _loc10_.addTaskGoalsToCurrentTaskGoalVOs(_loc5_);
                        ClearUtil.nullArr(_loc5_,false,false,false);
                     }
                  }
                  break;
               }
               _loc7_++;
            }
            _loc9_++;
         }
         _loc14_.clear();
      }
      
      private function compatibility(param1:XML, param2:XML) : void
      {
         var _loc11_:int = 0;
         var _loc4_:int = 0;
         var _loc12_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:MainLineTaskVO = null;
         var _loc3_:String = null;
         var _loc5_:* = undefined;
         var _loc6_:XML = param1.MainLineTask[0];
         currentPhase = String(_loc6_.@phase);
         var _loc9_:int = 0;
         var _loc13_:int = !!phaseTaskLists ? phaseTaskLists.length : 0;
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            if(currentPhase == phaseTaskLists[_loc9_].phase)
            {
               currentPhaseTaskList = phaseTaskLists[_loc9_];
               _loc11_ = 0;
               while(_loc11_ < _loc9_)
               {
                  _loc12_ = int(phaseTaskLists[_loc11_].taskVOs.length);
                  _loc4_ = 0;
                  while(_loc4_ < _loc12_)
                  {
                     phaseTaskLists[_loc11_].taskVOs[_loc4_].isGotReward = true;
                     _loc4_++;
                  }
                  _loc11_++;
               }
               break;
            }
            _loc9_++;
         }
         var _loc16_:XMLList = _loc6_.task;
         _loc13_ = int(!!_loc16_ ? _loc16_.length() : 0);
         var _loc15_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            _loc3_ = String(_loc16_[_loc9_].@id);
            _loc8_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
            _loc7_ = 0;
            while(_loc7_ < _loc8_)
            {
               _loc10_ = currentPhaseTaskList.taskVOs[_loc7_];
               if(_loc10_.id == _loc3_)
               {
                  _loc10_.isGotReward = Boolean(int(_loc16_[_loc9_].@isGotReward));
                  _loc5_ = _loc15_.createTaskGoalsByXML(_loc16_[_loc9_],param2);
                  _loc10_.addTaskGoalsToCurrentTaskGoalVOs(_loc5_);
                  ClearUtil.nullArr(_loc5_,false,false,false);
                  break;
               }
               _loc7_++;
            }
            _loc9_++;
         }
         _loc15_.clear();
         var _loc14_:Boolean = false;
         _loc8_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc8_)
         {
            if(currentPhaseTaskList.taskVOs[_loc7_].isGotReward == false)
            {
               if(_loc14_ == false)
               {
                  currentPhaseTaskList.taskVOs[_loc7_].isWorking = true;
                  _loc14_ = true;
               }
               else
               {
                  currentPhaseTaskList.taskVOs[_loc7_].isWorking = false;
               }
            }
            _loc7_++;
         }
      }
      
      private function compatibility2(param1:XML, param2:XML) : void
      {
         var _loc11_:int = 0;
         var _loc4_:int = 0;
         var _loc12_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:MainLineTaskVO = null;
         var _loc3_:String = null;
         var _loc17_:String = null;
         var _loc5_:* = undefined;
         compatibility(param1,param2);
         var _loc6_:XML = param1.NewMainLineTask[0];
         currentPhase = String(_loc6_.@phase);
         var _loc9_:int = 0;
         var _loc13_:int = !!phaseTaskLists ? phaseTaskLists.length : 0;
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            if(currentPhase == phaseTaskLists[_loc9_].phase)
            {
               currentPhaseTaskList = phaseTaskLists[_loc9_];
               _loc11_ = 0;
               while(_loc11_ < _loc9_)
               {
                  _loc12_ = int(phaseTaskLists[_loc11_].taskVOs.length);
                  _loc4_ = 0;
                  while(_loc4_ < _loc12_)
                  {
                     phaseTaskLists[_loc11_].taskVOs[_loc4_].isGotReward = true;
                     _loc4_++;
                  }
                  _loc11_++;
               }
               break;
            }
            _loc9_++;
         }
         var _loc16_:XMLList = _loc6_.task;
         _loc13_ = int(!!_loc16_ ? _loc16_.length() : 0);
         var _loc15_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            _loc3_ = String(_loc16_[_loc9_].@id);
            _loc17_ = String(_loc16_[_loc9_].@isWorking);
            _loc8_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
            _loc7_ = 0;
            while(_loc7_ < _loc8_)
            {
               _loc10_ = currentPhaseTaskList.taskVOs[_loc7_];
               if(_loc10_.id == _loc3_)
               {
                  if(_loc10_.isGotReward || Boolean(int(_loc16_[_loc9_].@isGotReward)))
                  {
                     _loc10_.isGotReward = true;
                  }
                  else
                  {
                     _loc10_.isGotReward = false;
                  }
                  if(_loc10_.isGotReward == false)
                  {
                     _loc5_ = _loc15_.createTaskGoalsByXML(_loc16_[_loc9_],param2);
                     _loc10_.addTaskGoalsToCurrentTaskGoalVOs(_loc5_);
                     ClearUtil.nullArr(_loc5_,false,false,false);
                  }
                  break;
               }
               _loc7_++;
            }
            _loc9_++;
         }
         _loc15_.clear();
         var _loc14_:Boolean = false;
         _loc8_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc8_)
         {
            if(currentPhaseTaskList.taskVOs[_loc7_].isGotReward == false)
            {
               if(_loc14_ == false)
               {
                  currentPhaseTaskList.taskVOs[_loc7_].isWorking = true;
                  _loc14_ = true;
               }
               else
               {
                  currentPhaseTaskList.taskVOs[_loc7_].isWorking = false;
               }
            }
            _loc7_++;
         }
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc4_:XML = null;
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         var _loc9_:MainLineTaskVO = null;
         var _loc3_:XML = null;
         var _loc5_:XML = null;
         var _loc8_:TaskGoalVO_MainTask = null;
         var _loc1_:XML = null;
         _loc2_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
         _loc10_ = 0;
         while(_loc10_ < _loc2_)
         {
            _loc9_ = currentPhaseTaskList.taskVOs[_loc10_];
            if(_loc9_)
            {
               if(_loc4_ == null)
               {
                  _loc4_ = <NewMainLineTask></NewMainLineTask>;
                  _loc4_.@phase = currentPhase;
               }
            }
            if(_loc9_.isGotReward)
            {
               if(_loc4_ == null)
               {
                  _loc4_ = <NewMainLineTask></NewMainLineTask>;
                  _loc4_.@phase = currentPhase;
               }
               _loc3_ = <task />;
               _loc3_.@id = _loc9_.id;
               _loc3_.@isGotReward = "1";
               _loc3_.@isWorking = "0";
               _loc4_.appendChild(_loc3_);
            }
            else if(_loc9_.isWorking == true)
            {
               if(_loc4_ == null)
               {
                  _loc4_ = <NewMainLineTask></NewMainLineTask>;
                  _loc4_.@phase = currentPhase;
               }
               _loc3_ = <task />;
               _loc3_.@id = _loc9_.id;
               _loc3_.@isWorking = "1";
               _loc6_ = _loc9_.getCurrentTaskGoalNum();
               _loc7_ = 0;
               while(_loc7_ < _loc6_)
               {
                  _loc8_ = _loc9_.getCurrentTaskGoalByIndex(_loc7_);
                  if(_loc8_)
                  {
                     _loc5_ = <taskGoal />;
                     _loc5_.@id = _loc8_.id;
                     _loc5_.@num = _loc8_.num;
                     _loc3_.appendChild(_loc5_);
                  }
                  _loc7_++;
               }
               _loc4_.appendChild(_loc3_);
            }
            else if(_loc9_.isWorking == false)
            {
               if(_loc4_ == null)
               {
                  _loc4_ = <NewMainLineTask></NewMainLineTask>;
                  _loc4_.@phase = currentPhase;
               }
               _loc3_ = <task />;
               _loc3_.@id = _loc9_.id;
               _loc3_.@isWorking = "0";
               _loc6_ = _loc9_.getCurrentTaskGoalNum();
               _loc7_ = 0;
               while(_loc7_ < _loc6_)
               {
                  _loc8_ = _loc9_.getCurrentTaskGoalByIndex(_loc7_);
                  if(_loc8_)
                  {
                     _loc5_ = <taskGoal />;
                     _loc5_.@id = _loc8_.id;
                     _loc5_.@num = _loc8_.num;
                     _loc3_.appendChild(_loc5_);
                  }
                  _loc7_++;
               }
               _loc4_.appendChild(_loc3_);
            }
            _loc1_ = <compatibility></compatibility>;
            _loc1_.@value = m_bComp;
            _loc4_.appendChild(_loc1_);
            _loc10_++;
         }
         return _loc4_;
      }
      
      public function addTaskGoalByGameEventStr(param1:String) : void
      {
         var _loc6_:int = 0;
         var _loc5_:Boolean = false;
         var _loc2_:TaskGoalVOFactory = new TaskGoalVOFactory();
         var _loc4_:TaskGoalVO_MainTask = _loc2_.createOneTaskGoalByGameEventStr(param1,XMLSingle.getInstance().mainLineTaskGoalsXML);
         _loc2_.clear();
         var _loc3_:int = Boolean(currentPhaseTaskList) && currentPhaseTaskList.taskVOs ? currentPhaseTaskList.taskVOs.length : 0;
         while(_loc4_ && _loc6_ < _loc3_)
         {
            _loc5_ = Boolean(currentPhaseTaskList.taskVOs[_loc6_].addNewTaskGoal(_loc4_));
            if(_loc5_)
            {
               _loc4_ = null;
            }
            _loc6_++;
         }
      }
      
      public function gotoNextTask() : Boolean
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:MainLineTaskVO = null;
         var _loc2_:int = 0;
         _loc4_ = int(currentPhaseTaskList.taskVOs.length);
         var _loc3_:Boolean = true;
         var _loc1_:Boolean = true;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = currentPhaseTaskList.taskVOs[_loc6_];
            if(_loc5_.isGotReward == false)
            {
               if(_loc3_)
               {
                  _loc3_ = false;
               }
               break;
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            _loc2_ = int(phaseTaskLists.indexOf(currentPhaseTaskList));
            if(_loc2_ == -1)
            {
               throw new Error();
            }
            if(_loc2_ < phaseTaskLists.length - 1 && phaseTaskLists[_loc2_ + 1].needLevel <= GamingUI.getInstance().player1.playerVO.level)
            {
               currentPhaseTaskList = phaseTaskLists[_loc2_ + 1];
               currentPhase = currentPhaseTaskList.phase;
               currentTask = currentPhaseTaskList.taskVOs[0];
               currentTask.isWorking = true;
            }
            else
            {
               _loc3_ = false;
               _loc1_ = false;
            }
         }
         else
         {
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               _loc5_ = currentPhaseTaskList.taskVOs[_loc6_];
               if(_loc5_.isGotReward == false)
               {
                  currentTask = _loc5_;
                  currentTask.isWorking = true;
                  break;
               }
               _loc6_++;
            }
         }
         return _loc1_;
      }
   }
}

