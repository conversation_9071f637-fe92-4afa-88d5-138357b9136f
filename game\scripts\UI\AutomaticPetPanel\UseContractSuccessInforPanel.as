package UI.AutomaticPetPanel
{
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class UseContractSuccessInforPanel extends MySprite
   {
      private var m_show:Sprite;
      
      private var m_dragLogic:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_automaticPetHeadShow:MovieClipPlayLogicShell;
      
      private var m_automaticPetNameText:TextField;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_myControlPanel:MyControlPanel;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      public function UseContractSuccessInforPanel()
      {
         super();
         m_dragLogic = new AbleDragSpriteLogicShell();
         m_quitBtn = new ButtonLogicShell2();
         m_automaticPetHeadShow = new MovieClipPlayLogicShell();
         m_lookUpBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_dragLogic);
         m_dragLogic = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_automaticPetHeadShow);
         m_automaticPetHeadShow = null;
         m_automaticPetNameText = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         m_versionControl = null;
         m_myControlPanel = null;
         m_automaticPetVO = null;
         super.clear();
      }
      
      public function init(param1:MyControlPanel, param2:IProgressShow, param3:IVersionControl) : void
      {
         m_myControlPanel = param1;
         m_loadUI = param2;
         m_versionControl = param3;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getClass("UseEquipmentPanel.swf","UseContractSuccessInforPanel",getShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      public function setAutomaticPetVO(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         initShow2();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         m_show = new param1.resultClass();
         if(parent)
         {
            m_show.x = (parent.width - m_show.width) / 2;
            m_show.y = (parent.height - m_show.height) / 2;
         }
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         m_myControlPanel.closeUseContractSuccessInforPanel();
      }
      
      private function initShow() : void
      {
         m_dragLogic.setShow(m_show);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_automaticPetHeadShow.setShow(m_show["automaticPetHeadShow"]);
         m_automaticPetNameText = m_show["automaticPetNameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_automaticPetNameText);
         m_lookUpBtn.setShow(m_show["lookUPBtn"]);
      }
      
      private function initShow2() : void
      {
         var _loc1_:AutomaticPetVO = null;
         if(m_automaticPetVO == null || m_show == null)
         {
            return;
         }
         m_automaticPetHeadShow.gotoAndStop(m_automaticPetVO.getId());
         if(m_automaticPetVO.partnerName)
         {
            _loc1_ = GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName);
            if(_loc1_)
            {
               m_automaticPetNameText.text = m_automaticPetVO.getName() + "/" + _loc1_.getName();
            }
            else
            {
               m_automaticPetNameText.text = m_automaticPetVO.getName();
            }
         }
         else
         {
            m_automaticPetNameText.text = m_automaticPetVO.getName();
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quitBtn:
               m_myControlPanel.closeUseContractSuccessInforPanel();
               break;
            case m_lookUpBtn:
               GamingUI.getInstance().closeInternalPanel();
               GamingUI.getInstance().openAutomaticPetPanel();
         }
      }
   }
}

