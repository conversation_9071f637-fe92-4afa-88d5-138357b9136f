package UI.Equipments.EquipmentVO
{
   public class ContractEquipmntVO extends EquipmentVO
   {
      private var m_targetAutomaticPetId:String;
      
      public function ContractEquipmntVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.targetAutomaticPetId = m_targetAutomaticPetId;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:ContractEquipmntVO = new ContractEquipmntVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as ContractEquipmntVO).targetAutomaticPetId = this.targetAutomaticPetId;
      }
      
      public function get targetAutomaticPetId() : String
      {
         return _antiwear.targetAutomaticPetId;
      }
      
      public function set targetAutomaticPetId(param1:String) : void
      {
         _antiwear.targetAutomaticPetId = param1;
      }
   }
}

