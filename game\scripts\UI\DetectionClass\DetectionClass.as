package UI.DetectionClass
{
   import GM_UI.GMData;
   import UI.CheatData.CheatData;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.DanMedicineEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.PKUI.PlayerDataForPK;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class DetectionClass
   {
      private static var _instance:DetectionClass = null;
      
      public var accelerateNum:int;
      
      private var _isStartRequestTime:Boolean;
      
      private var _startTime:String;
      
      private var _endTime:String;
      
      private var _requestTimeIntervalTime:int;
      
      private var _interval1:uint;
      
      private var _interval2:uint;
      
      private var _vipPetArr:Array = [10400124,10400224,10400324,10400425];
      
      private var _lastCheckTime:int;
      
      private var _isCheckP1Storage:Boolean;
      
      private var _isCheckP2Storage:Boolean;
      
      private var _isCheckPublicStorage:Boolean;
      
      public function DetectionClass()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗！？");
      }
      
      public static function getInstance() : DetectionClass
      {
         if(!_instance)
         {
            _instance = new DetectionClass();
         }
         return _instance;
      }
      
      public function detectionVersion() : Boolean
      {
         // 版本检测已禁用 - Version detection disabled
         return false;
      }
      
      public function detectionUidAndIdx() : Boolean
      {
         // UID和索引检测已禁用 - UID and index detection disabled
         return false;
      }
      
      public function getXMLByType(param1:String) : XML
      {
         var _loc3_:int = 0;
         var _loc2_:XMLList = XMLSingle.getInstance().dataXML.detection[0].type;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            if(_loc2_[_loc3_].@value == param1)
            {
               return _loc2_[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function detectionVersionMatchTime() : void
      {
         // 版本时间匹配检测已禁用 - Version time match detection disabled
         return;
      }
      
      public function detectionAllPlayer1(param1:Player, param2:Player, param3:Vector.<EquipmentVO>) : void
      {
         // 玩家检测已禁用 - Player detection disabled
         return;
      }
      
      public function detectionDan(param1:Player) : void
      {
         // 丹药检测已禁用 - Dan medicine detection disabled
         return;
      }
      
      public function detectionEquipmentVos(param1:Vector.<EquipmentVO>) : void
      {
         // 装备检测已禁用 - Equipment detection disabled
         return;
      }
      
      private function detectionTopEquip(param1:EquipmentVO, param2:int) : void
      {
         // 顶级装备检测已禁用 - Top equipment detection disabled
         return;
      }
      
      public function detectionAllPlayer2(param1:Player, param2:Vector.<EquipmentVO>) : void
      {
         // 玩家2检测已禁用 - Player2 detection disabled
         return;
      }
      
      public function detectionPlayerVO(param1:PlayerVO) : void
      {
         // 玩家VO检测已禁用 - PlayerVO detection disabled
         return;
      }
      
      public function detectionPKData() : void
      {
         // PK数据检测已禁用 - PK data detection disabled
         return;
      }
      
      public function addEquipmentVOFix(param1:EquipmentVO) : void
      {
         if(param1 && !param1.prefix && param1.id && param1.id > 100)
         {
            param1.prefix = param1.id.toString().substr(0,2);
            param1.suffix = param1.id.toString().substr(2);
         }
      }
      
      public function detectionEquipmentVO(param1:EquipmentVO) : void
      {
         // 装备VO检测已禁用 - EquipmentVO detection disabled
         return;


      }
      
      private function detectionPetEquipmentVO(param1:PetEquipmentVO) : void
      {
         // 宠物装备检测已禁用 - Pet equipment detection disabled
         return;
      }
      
      private function detectionSkillVO(param1:SkillVO, param2:PetEquipmentVO) : void
      {
         // 技能检测已禁用 - Skill detection disabled
         return;
      }
      
      public function resetCheckTime() : void
      {
         _lastCheckTime = 0;
      }
   }
}

