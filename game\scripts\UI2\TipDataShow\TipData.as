package UI2.TipDataShow
{
   import YJFY.Utils.ClearUtil;
   
   public class TipData
   {
      private var m_oneTipDatas:Vector.<OneTipData>;
      
      public function TipData()
      {
         super();
         m_oneTipDatas = new Vector.<OneTipData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_oneTipDatas);
         m_oneTipDatas = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:OneTipData = null;
         var _loc3_:XMLList = param1.oneTipData;
         _loc2_ = int(_loc3_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = new OneTipData();
            _loc4_.initByXML(_loc3_[_loc5_]);
            m_oneTipDatas.push(_loc4_);
            _loc5_++;
         }
      }
      
      public function getNextShowOneTipData(param1:OneTipData) : OneTipData
      {
         if(m_oneTipDatas.length == 0)
         {
            return null;
         }
         if(param1 == null)
         {
            return m_oneTipDatas[0];
         }
         var _loc2_:int = int(m_oneTipDatas.indexOf(param1));
         if(_loc2_ == -1)
         {
            throw new Error("error, lastShowOneTipData is not in m_oneTipDatas.");
         }
         if(_loc2_ < m_oneTipDatas.length - 1)
         {
            return m_oneTipDatas[_loc2_ + 1];
         }
         return null;
      }
   }
}

