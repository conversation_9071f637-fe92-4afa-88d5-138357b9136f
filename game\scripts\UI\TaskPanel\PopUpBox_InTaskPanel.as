package UI.TaskPanel
{
   import UI.Button.Btn;
   import UI.MySprite;
   import flash.events.Event;
   
   public class PopUpBox_InTaskPanel extends MySprite
   {
      public var btnOne:Btn;
      
      public var btnTwo:Btn;
      
      public function PopUpBox_InTaskPanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(btnOne)
         {
            btnOne.clear();
         }
         if(btnTwo)
         {
            btnTwo.clear();
         }
         btnOne = null;
         btnTwo = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
      }
   }
}

