package UI.MiragePanel
{
   import UI.Button.PageBtn.PageBtnGroup_Vertical;
   import UI.Button.QuitBtn;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipment;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MiragePanel.Border.SelectAssistantPetBorder;
   import UI.MiragePanel.Border.SelectMainPetBorder;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Privilege.PrivilegeVO;
   import UI.Protect.ProtectData;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.Skill;
   import UI.SmallPackage.SmallPackage;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.EntityShowContainer;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class MiragePanel extends MySprite
   {
      public var mainPetContainer:Sprite;
      
      public var assistantPetContainer:Sprite;
      
      public var quitBtn:QuitBtn;
      
      public var promoteIconContainer:Sprite;
      
      private var ICONS_NUM_ONE_PAGE:int = 5;
      
      private var _mainPet:Pet;
      
      private var _assistantPet:Pet;
      
      private var _targetClickObject:*;
      
      private var _selectMainPetBorder:SelectMainPetBorder;
      
      private var _selectAssistantPetBorder:SelectAssistantPetBorder;
      
      private var _intervalId:uint;
      
      private var _mirageDataShow:MirageDataShow;
      
      private var _PopResetPanel:PopResetPanel;
      
      private var _mirageTarget:Object;
      
      private var _promoteIcons:Array;
      
      private var _pageBtnGroupForIcons:PageBtnGroup_Vertical;
      
      private var _promoteXML:XML;
      
      private var _mainPetShowContainer:EntityShowContainer;
      
      private var _assistantPetShowContainer:EntityShowContainer;
      
      public function MiragePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function openMiragePanel() : void
      {
         PackageEquipmentCell.packageBtnSate = 272;
         var _loc1_:EqIsShow = new EqIsShow();
         SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc1_);
         MyFunction2.getServerTimeFunction(dealWithDate,showWarningBox,false);
      }
      
      public function closeMiragePanel() : void
      {
         clearPanel();
         if(getChildByName(SmallPackage.getInstance().name))
         {
            removeChild(SmallPackage.getInstance());
         }
      }
      
      public function refreshPetShow() : void
      {
         if(Boolean(_mainPet) && _mainPet.petEquipmentVO)
         {
            _mainPetShowContainer.refreshPetShow(_mainPet.petEquipmentVO);
            _mainPetShowContainer.getShow().x = 160;
            _mainPetShowContainer.getShow().y = 250;
            _mainPetShowContainer.getShow().scaleX = 3;
            _mainPetShowContainer.getShow().scaleY = 3;
            mainPetContainer.addChild(_mainPetShowContainer.getShow());
         }
         if(Boolean(_assistantPet) && _assistantPet.petEquipmentVO)
         {
            _assistantPetShowContainer.refreshPetShow(_assistantPet.petEquipmentVO);
            _assistantPetShowContainer.getShow().x = 60;
            _assistantPetShowContainer.getShow().y = 95;
            _assistantPetShowContainer.getShow().scaleX = 1.2;
            _assistantPetShowContainer.getShow().scaleY = 1.2;
            assistantPetContainer.addChild(_assistantPetShowContainer.getShow());
         }
      }
      
      private function clearPanel() : void
      {
         _mainPet.petEquipmentVO = null;
         _assistantPet.petEquipmentVO = null;
         while(mainPetContainer.numChildren > 1)
         {
            mainPetContainer.removeChildAt(1);
         }
         while(assistantPetContainer.numChildren > 1)
         {
            assistantPetContainer.removeChildAt(1);
         }
         while(promoteIconContainer.numChildren > 1)
         {
            promoteIconContainer.removeChildAt(1);
         }
         _mirageDataShow.clearData();
         if(getChildByName(_pageBtnGroupForIcons.name))
         {
            removeChild(_pageBtnGroupForIcons);
         }
      }
      
      private function refreshPanelAfterMirage() : void
      {
         _assistantPet.petEquipmentVO = null;
         while(assistantPetContainer.numChildren > 1)
         {
            assistantPetContainer.removeChildAt(1);
         }
         var _loc2_:int = 0;
         var _loc1_:int = int(_promoteIcons.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _promoteIcons[_loc2_][1] = "lockState";
            _promoteIcons[searchInpromoteIcons("精气")][0] = new EssenceIcon(Math.round(_mainPet.petEquipmentVO.essentialPercent * _mainPet.petEquipmentVO.essentialVolume));
            _loc2_++;
         }
         setPage(1);
         arrangePromoteIcon((_pageBtnGroupForIcons.pageNum - 1) * ICONS_NUM_ONE_PAGE);
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:DisplayObject = null;
         super.clear();
         ClearUtil.clearObject(_PopResetPanel);
         _PopResetPanel = null;
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         while(mainPetContainer.numChildren > 0)
         {
            _loc2_ = mainPetContainer.getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            mainPetContainer.removeChildAt(0);
         }
         while(assistantPetContainer.numChildren > 0)
         {
            _loc2_ = assistantPetContainer.getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            assistantPetContainer.removeChildAt(0);
         }
         while(promoteIconContainer.numChildren > 0)
         {
            _loc2_ = promoteIconContainer.getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            promoteIconContainer.removeChildAt(0);
         }
         mainPetContainer = null;
         assistantPetContainer = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _mainPet = null;
         _assistantPet = null;
         _mirageTarget = null;
         if(_mirageDataShow)
         {
            _mirageDataShow.clear();
         }
         _mirageDataShow = null;
         SmallPackage.getInstance().clear();
         _selectAssistantPetBorder = null;
         _selectMainPetBorder = null;
         ClearUtil.clearObject(_mainPetShowContainer);
         _mainPetShowContainer = null;
         ClearUtil.clearObject(_assistantPetShowContainer);
         _assistantPetShowContainer = null;
         if(_pageBtnGroupForIcons)
         {
            _pageBtnGroupForIcons.clear();
         }
         _pageBtnGroupForIcons = null;
         if(_promoteIcons)
         {
            _loc1_ = int(_promoteIcons.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _promoteIcons[_loc3_][0].clear();
               _promoteIcons[_loc3_][1] = null;
               _promoteIcons[_loc3_] = null;
               _loc3_++;
            }
            _promoteIcons = null;
         }
         _promoteXML = null;
      }
      
      private function dealWithDate(param1:String) : void
      {
         var str:String = param1;
         var newDate:String = MyFunction.getInstance().splitTimeString(str);
         if(Boolean(str) && Boolean(str.length) && newDate != MirageData.getInstance().mirageDate)
         {
            MirageData.getInstance().currentMirageNum = 0;
            MirageData.getInstance().mirageDate = newDate;
         }
         MyFunction2.loadXMLFunction("skill2",function(param1:XML):void
         {
            _promoteXML = param1;
         },showWarningBox,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",showSmallPackage,false,0,true);
         addEventListener("clickPutInBtn",putInEquipment,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("clickStartMirageBtn",startMirage,true,0,true);
         addEventListener("clickGotoVipMirageBtn",gotoVipFun,true,0,true);
         addEventListener("clickGotoProtectMirageBtn",gotoProtectFun,true,0,true);
         addEventListener("switchPlayerInSmallPackage",switchPlayerPanel,true,0,true);
         addEventListener("switchMiragePanelIconBtn",showPromoteMessage,true,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("clickQuitBtn",hideShineBorder,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",showSmallPackage,false);
         removeEventListener("clickPutInBtn",putInEquipment,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("clickStartMirageBtn",startMirage,true);
         removeEventListener("clickGotoVipMirageBtn",gotoVipFun,true);
         removeEventListener("clickGotoProtectMirageBtn",gotoProtectFun,true);
         removeEventListener("switchPlayerInSmallPackage",switchPlayerPanel,true);
         removeEventListener("switchMiragePanelIconBtn",showPromoteMessage,true);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("clickQuitBtn",hideShineBorder,true);
      }
      
      private function pageUp(param1:UIBtnEvent) : void
      {
         if(param1.target is PageBtnGroup_Vertical)
         {
            arrangePromoteIcon((_pageBtnGroupForIcons.pageNum - 1) * ICONS_NUM_ONE_PAGE);
         }
      }
      
      protected function pageDown(param1:UIBtnEvent) : void
      {
         if(param1.target is PageBtnGroup_Vertical)
         {
            arrangePromoteIcon((_pageBtnGroupForIcons.pageNum - 1) * ICONS_NUM_ONE_PAGE);
         }
      }
      
      private function switchPlayerPanel(param1:UIBtnEvent) : void
      {
         clearPanel();
         var _loc2_:EqIsShow = new EqIsShow();
         SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc2_);
      }
      
      private function gotoVipFun(param1:UIBtnEvent) : void
      {
         GamingUI.getInstance().closeMirage();
         GamingUI.getInstance().openVipPanel();
      }
      
      private function gotoProtectFun(param1:UIBtnEvent) : void
      {
         GamingUI.getInstance().closeMirage();
         GamingUI.getInstance().enterToProtect(null);
      }
      
      private function startMirage(param1:UIBtnEvent) : void
      {
         var protectBaoji:int;
         var multiple:int;
         var rand:int;
         var currentMoney:Number;
         var promoteMoney:Number;
         var miragePanel:MiragePanel;
         var e:UIBtnEvent = param1;
         var bBaoJi:Boolean = false;
         var vipBaoji:int = 0;
         var datalist:Vector.<PrivilegeVO> = GamingUI.getInstance().player1.vipVO.privilegeVOs;
         var t:int = 0;
         while(t < datalist.length)
         {
            if(datalist[t].id == 177 || datalist[t].id == 178 || datalist[t].id == 179 || datalist[t].id == 180 || datalist[t].id == 181 || datalist[t].id == 182)
            {
               vipBaoji = datalist[t].value;
            }
            t++;
         }
         protectBaoji = ProtectData.getInstance().huanhuaValue;
         multiple = 1;
         rand = 1 + Math.random() * 100;
         if(vipBaoji + protectBaoji > 0 && rand < vipBaoji + protectBaoji)
         {
            multiple = 2;
            bBaoJi = true;
         }
         if(!MirageFunction.getInstance().getCurrentHaveMirageNum())
         {
            showWarningBox("当前可幻化次数为0， 不可幻化！",0);
            return;
         }
         currentMoney = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1.playerVO.money : GamingUI.getInstance().player2.playerVO.money;
         promoteMoney = MirageFunction.getInstance().getPromoteMoney(_mirageTarget as DisplayObject);
         if(currentMoney < promoteMoney)
         {
            showWarningBox("金额不足, 不能幻化！",0);
            return;
         }
         if(!MirageFunction.getInstance().getRate(_mirageTarget as DisplayObject,_mirageDataShow.numBtnGroup.num))
         {
            showWarningBox("幻化成功率为0， 不能幻化！",0);
            return;
         }
         miragePanel = this;
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var promoteValue:int;
            var promoteAttributeName:String;
            var promoteUint:String;
            var beforeValue:int;
            var afterValue:int;
            var beforePromoteValue:int;
            var afterPromoteValue:int;
            mouseChildren = false;
            mouseEnabled = false;
            var r:Number = Math.random() * 100;
            MirageData.getInstance().currentMirageNum++;
            deleEquipmentVOs();
            deleMoney();
            if(r < MirageFunction.getInstance().getRate(_mirageTarget as DisplayObject,_mirageDataShow.numBtnGroup.num))
            {
               if(_mirageTarget is EssenceIcon)
               {
                  promoteValue = MirageFunction.getInstance().getPromoteValue(_mirageTarget as DisplayObject,_mainPet,_assistantPet);
                  beforePromoteValue = Math.round(_mainPet.petEquipmentVO.essentialPercent * _mainPet.petEquipmentVO.essentialVolume);
                  _mainPet.petEquipmentVO.essentialPercent += promoteValue / _mainPet.petEquipmentVO.essentialVolume;
                  afterPromoteValue = Math.round(_mainPet.petEquipmentVO.essentialPercent * _mainPet.petEquipmentVO.essentialVolume);
                  promoteAttributeName = "精气";
                  promoteUint = "点";
               }
               else if(_mirageTarget is Skill)
               {
                  promoteValue = MirageFunction.getInstance().getPromoteValue(_mirageTarget as DisplayObject,_mainPet,_assistantPet);
                  beforePromoteValue = ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).promoteValue;
                  beforeValue = ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).value;
                  ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).promoteValue += promoteValue * multiple;
                  afterPromoteValue = ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).promoteValue;
                  MyFunction.getInstance().refreshPet(_mainPet.petEquipmentVO);
                  afterValue = ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).value;
                  promoteAttributeName = (_mirageTarget as Skill).skillVO.description;
                  promoteUint = ((_mirageTarget as Skill).skillVO as PetPassiveSkillVO).unit;
               }
               GamingUI.getInstance().addMainLineTaskGoalGameEventStr("mirage");
               MirageFunction.getInstance().startMirageEffectAnimation(miragePanel,_mainPetShowContainer.getShow(),_assistantPetShowContainer.getShow(),true,bBaoJi,function():void
               {
                  if(_mirageTarget is EssenceIcon)
                  {
                     showWarningBox("                    幻化成功！\n<font size=\'18\' color=\'#ffd581\'>" + promoteAttributeName + "：</font><font size=\'15\' color=\'#00ff00\'>" + "（" + beforePromoteValue + "——>" + afterPromoteValue + "）" + promoteUint + "</font><font size=\'12\' color=\'#ffd581\'>" + "</font>",1);
                  }
                  else if(_mirageTarget is Skill)
                  {
                     if(bBaoJi)
                     {
                        showWarningBox("                    幻化成功！暴击\n<font size=\'18\' color=\'#ffd581\'>" + promoteAttributeName + "：</font><font size=\'15\' color=\'#00ff00\'>" + "<font size=\'15\' color=\'#ffffff\'>" + beforeValue + "（幻化提升：" + "</font>" + beforePromoteValue + "<font size=\'15\' color=\'#ffffff\'>" + "）" + "</font>" + "——>" + "<font size=\'15\' color=\'#ffffff\'>" + afterValue + "（幻化提升：" + "</font>" + afterPromoteValue + "<font size=\'15\' color=\'#ffffff\'>" + "）" + "</font>" + "<font size=\'15\' color=\'#ffffff\'>" + promoteUint + "</font>" + "</font><font size=\'18\' color=\'#ffd581\'>" + "</font>",1);
                     }
                     else
                     {
                        showWarningBox("                    幻化成功！\n<font size=\'18\' color=\'#ffd581\'>" + promoteAttributeName + "：</font><font size=\'15\' color=\'#00ff00\'>" + "<font size=\'15\' color=\'#ffffff\'>" + beforeValue + "（幻化提升：" + "</font>" + beforePromoteValue + "<font size=\'15\' color=\'#ffffff\'>" + "）" + "</font>" + "——>" + "<font size=\'15\' color=\'#ffffff\'>" + afterValue + "（幻化提升：" + "</font>" + afterPromoteValue + "<font size=\'15\' color=\'#ffffff\'>" + "）" + "</font>" + "<font size=\'15\' color=\'#ffffff\'>" + promoteUint + "</font>" + "</font><font size=\'18\' color=\'#ffd581\'>" + "</font>",1);
                     }
                  }
                  refreshPanelAfterMirage();
                  mouseChildren = true;
                  mouseEnabled = true;
                  var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                  _loc1_.type = "4399";
                  _loc1_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc1_);
                  MyFunction2.saveGame2();
               });
            }
            else
            {
               MirageFunction.getInstance().startMirageEffectAnimation(miragePanel,_mainPetShowContainer.getShow(),_assistantPetShowContainer.getShow(),false,bBaoJi,function():void
               {
                  showWarningBox("幻化失败！",0);
                  refreshPanelAfterMirage();
                  mouseChildren = true;
                  mouseEnabled = true;
                  var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                  _loc1_.type = "4399";
                  _loc1_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc1_);
                  MyFunction2.saveGame2();
               });
            }
            _mirageDataShow.clearData();
         },function():void
         {
            showWarningBox("网络已断开，请重新连接网络后重试！",0);
         },true);
      }
      
      private function deleMoney() : void
      {
         var _loc1_:Player = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1 : GamingUI.getInstance().player2;
         _loc1_.playerVO.money -= MirageFunction.getInstance().getPromoteMoney(_mirageTarget as DisplayObject);
      }
      
      private function deleEquipmentVOs() : void
      {
         var _loc1_:* = undefined;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1)
         {
            _loc1_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
         }
         else
         {
            if(SmallPackage.getInstance().currentSmallPackage != SmallPackage.getInstance().smallPackage_2)
            {
               throw new Error();
            }
            _loc1_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs;
         }
         MyFunction.getInstance().minusEquipmentVOs(_loc1_,_mirageDataShow.numBtnGroup.num,10500000);
         _loc2_ = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(Boolean(_loc1_[_loc3_]) && _loc1_[_loc3_] == _assistantPet.petEquipmentVO)
            {
               _loc1_[_loc3_] = null;
               GamingUI.getInstance().refresh(2);
               return;
            }
            _loc3_++;
         }
         throw new Error();
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function putInEquipment(param1:UIBtnEvent) : void
      {
         var _loc12_:int = 0;
         var _loc10_:int = 0;
         var _loc15_:int = 0;
         var _loc11_:int = 0;
         var _loc14_:Pet = null;
         var _loc9_:Equipment = null;
         var _loc5_:EquipmentVO = null;
         var _loc7_:EquipmentVO = null;
         var _loc3_:EquipmentVO = null;
         var _loc4_:IEquipmentCell = param1.target.parent.parent;
         var _loc8_:Boolean = false;
         var _loc2_:int = int(SmallPackage.getInstance().currentSmallPackage.area.equipmentCells.indexOf(param1.target.parent.parent));
         switch(_targetClickObject)
         {
            case mainPetContainer:
               _loc8_ = true;
               _loc14_ = _mainPet;
               if(_loc4_.child.level < (_loc4_.child as PetEquipmentVO).maxLevel)
               {
                  showWarningBox("主宠还没到第三阶段， 不能幻化！",0);
                  return;
               }
               while(assistantPetContainer.numChildren > 1)
               {
                  assistantPetContainer.removeChildAt(1);
               }
               if(Boolean(_assistantPet) && _assistantPet.petEquipmentVO)
               {
                  _loc9_ = new PetEquipment(_assistantPet.petEquipmentVO);
                  SmallPackage.getInstance().currentSmallPackage.addEquipmentVOs(_loc9_.equipmentVO,1);
                  _assistantPet.petEquipmentVO = null;
               }
               break;
            case assistantPetContainer:
               _loc8_ = false;
               _loc14_ = _assistantPet;
               if(!_mainPet.petEquipmentVO)
               {
                  showWarningBox("请先放入主宠！",0);
                  return;
               }
               if(_mainPet.petEquipmentVO.petSeries != (_loc4_.child as PetEquipmentVO).petSeries)
               {
                  showWarningBox("副宠不符合要求！",0);
                  return;
               }
               if(_loc4_.child.level < (_loc4_.child as PetEquipmentVO).maxLevel)
               {
                  showWarningBox("副宠还没到第三阶段， 不能幻化！",0);
                  return;
               }
               break;
            default:
               showWarningBox("请先选择宠物放入的位置， 点击要放入的地方！",0);
               return;
         }
         var _loc13_:int = 0;
         if(_loc14_.petEquipmentVO)
         {
            _loc5_ = _loc14_.petEquipmentVO;
            _loc7_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
            SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(_loc5_,_loc2_);
            SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
            _loc14_.petEquipmentVO = _loc7_ as PetEquipmentVO;
         }
         else
         {
            _loc3_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
            SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
            _loc14_.petEquipmentVO = _loc3_ as PetEquipmentVO;
         }
         switch(_targetClickObject)
         {
            case mainPetContainer:
               if(_promoteIcons)
               {
                  _loc15_ = int(_promoteIcons.length);
                  _loc12_ = 0;
                  while(_loc12_ < _loc15_)
                  {
                     _promoteIcons[_loc12_][0].clear();
                     _promoteIcons[_loc12_][1] = null;
                     _promoteIcons[_loc12_] = null;
                     _loc12_++;
                  }
                  _promoteIcons = null;
               }
               _promoteIcons = [];
               _promoteIcons.push([new EssenceIcon(Math.round(_mainPet.petEquipmentVO.essentialPercent * _mainPet.petEquipmentVO.essentialVolume)),"lockState"]);
               _loc15_ = int(_mainPet.petEquipmentVO.passiveSkillVOs.length);
               _loc12_ = 0;
               while(_loc12_ < _loc15_)
               {
                  _promoteIcons.push([new Skill(_mainPet.petEquipmentVO.passiveSkillVOs[_loc12_]),"lockState"]);
                  _loc12_++;
               }
               if(!getChildByName(_pageBtnGroupForIcons.name))
               {
                  addChild(_pageBtnGroupForIcons);
               }
               setPage(1);
               arrangePromoteIcon((_pageBtnGroupForIcons.pageNum - 1) * ICONS_NUM_ONE_PAGE);
               break;
            case assistantPetContainer:
               if(_promoteIcons)
               {
                  _loc15_ = int(_promoteIcons.length);
                  _loc12_ = 0;
                  while(_loc12_ < _loc15_)
                  {
                     _promoteIcons[_loc12_][1] = "lockState";
                     _loc12_++;
                  }
                  break;
               }
         }
         _mirageDataShow.clearData();
         if(Boolean(_mainPet.petEquipmentVO) && _assistantPet.petEquipmentVO)
         {
            if(MirageFunction.getInstance().getCurrentHaveMirageNum() > 0)
            {
               if(_assistantPet.petEquipmentVO.essentialPercent && _mainPet.petEquipmentVO.essentialPercent != 1)
               {
                  _promoteIcons[searchInpromoteIcons("精气")][1] = "openState";
               }
               _loc15_ = int(_mainPet.petEquipmentVO.passiveSkillVOs.length);
               _loc12_ = 0;
               while(_loc12_ < _loc15_)
               {
                  _loc11_ = int(_assistantPet.petEquipmentVO.passiveSkillVOs.length);
                  _loc10_ = 0;
                  while(_loc10_ < _loc11_)
                  {
                     if(Boolean(_mainPet.petEquipmentVO.passiveSkillVOs[_loc12_]) && Boolean(_assistantPet.petEquipmentVO.passiveSkillVOs[_loc10_]) && _mainPet.petEquipmentVO.passiveSkillVOs[_loc12_].className == _assistantPet.petEquipmentVO.passiveSkillVOs[_loc10_].className && MirageFunction.getInstance().judgeSkillEnalbeMirage(_mainPet.petEquipmentVO.passiveSkillVOs[_loc12_],_mainPet,_promoteXML))
                     {
                        _promoteIcons[searchInpromoteIcons(_mainPet.petEquipmentVO.passiveSkillVOs[_loc12_].name)][1] = "openState";
                        break;
                     }
                     _loc10_++;
                  }
                  _loc12_++;
               }
               if(!getChildByName(_pageBtnGroupForIcons.name))
               {
                  addChild(_pageBtnGroupForIcons);
               }
               setPage(1);
               arrangePromoteIcon((_pageBtnGroupForIcons.pageNum - 1) * ICONS_NUM_ONE_PAGE);
            }
            else
            {
               if(_PopResetPanel)
               {
                  _PopResetPanel.showPanel();
               }
               else
               {
                  _PopResetPanel = new PopResetPanel();
                  addChildAt(_PopResetPanel,numChildren);
               }
               _mirageDataShow.clearData();
               if(getChildByName(_pageBtnGroupForIcons.name))
               {
                  removeChild(_pageBtnGroupForIcons);
               }
            }
         }
         hideShineBorder();
         hideSmallPackage();
         refreshPetShow();
      }
      
      private function setPage(param1:int) : void
      {
         if(!Boolean(_promoteIcons) || !_promoteIcons.length)
         {
            _pageBtnGroupForIcons.initPageNumber(1,1);
            _pageBtnGroupForIcons.visible = false;
            return;
         }
         var _loc2_:int = int(_promoteIcons.length);
         _pageBtnGroupForIcons.visible = true;
         if(_loc2_ % ICONS_NUM_ONE_PAGE == 0)
         {
            _pageBtnGroupForIcons.initPageNumber(param1,_loc2_ / ICONS_NUM_ONE_PAGE);
         }
         else
         {
            _pageBtnGroupForIcons.initPageNumber(param1,int(_loc2_ / ICONS_NUM_ONE_PAGE) + 1);
         }
      }
      
      private function arrangePromoteIcon(param1:int) : void
      {
         var _loc4_:MiragePanelIcon = null;
         var _loc7_:DisplayObject = null;
         var _loc6_:int = 0;
         var _loc9_:* = 0;
         _loc9_ = 0;
         while(_loc9_ < promoteIconContainer.numChildren)
         {
            _loc7_ = promoteIconContainer.getChildAt(_loc9_);
            if(_loc7_ is MiragePanelIcon)
            {
               promoteIconContainer.removeChildAt(_loc9_);
               _loc7_.removeEventListener("rollOver",onRoll,false);
               _loc7_.removeEventListener("rollOut",onRoll,false);
               (_loc7_ as MiragePanelIcon).clear();
               _loc9_--;
            }
            _loc9_++;
         }
         var _loc8_:int = param1 + ICONS_NUM_ONE_PAGE;
         var _loc5_:int = int(_promoteIcons.length);
         _loc9_ = param1;
         while(_loc9_ < _loc5_ && _loc9_ < _loc8_)
         {
            _loc4_ = new MiragePanelIcon();
            _loc4_.x = 30 + (_loc9_ - param1) * 50;
            _loc4_.y = 30;
            if(_promoteIcons[_loc9_][0] is Skill)
            {
               _loc4_.target = (_promoteIcons[_loc9_][0] as Skill).imperfectClone() as DisplayObject;
            }
            else if(_promoteIcons[_loc9_][0] is EssenceIcon)
            {
               _loc4_.target = (_promoteIcons[_loc9_][0] as EssenceIcon).clone();
            }
            _loc4_.state = _promoteIcons[_loc9_][1];
            _loc4_.addEventListener("rollOver",onRoll,false,0,true);
            _loc4_.addEventListener("rollOut",onRoll,false,0,true);
            promoteIconContainer.addChild(_loc4_);
            _loc9_++;
         }
         _loc9_ = 0;
         while(_loc9_ < promoteIconContainer.numChildren)
         {
            _loc7_ = promoteIconContainer.getChildAt(_loc9_);
            if(_loc7_ is MiragePanelIcon)
            {
               if((_loc7_ as MiragePanelIcon).state == "openState")
               {
                  if(!_loc6_)
                  {
                     (_loc7_ as MiragePanelIcon).init(false);
                     showPromoteMessage(null,(_loc7_ as MiragePanelIcon).target);
                  }
                  else
                  {
                     (_loc7_ as MiragePanelIcon).init(true);
                  }
                  _loc6_++;
               }
               else
               {
                  (_loc7_ as MiragePanelIcon).init(true);
               }
            }
            _loc9_++;
         }
      }
      
      private function onRoll(param1:MouseEvent) : void
      {
         var _loc2_:Object = null;
         if(param1.target is MiragePanelIcon)
         {
            _loc2_ = param1.target.target;
         }
         if(_loc2_)
         {
            switch(param1.type)
            {
               case "rollOver":
                  promoteIconContainer.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_loc2_}));
                  break;
               case "rollOut":
                  promoteIconContainer.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
            }
         }
      }
      
      private function showPromoteMessage(param1:UIBtnEvent = null, param2:DisplayObject = null) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:DisplayObject = null;
         if(param1)
         {
            _mirageTarget = param1.target.target;
            _loc5_ = 0;
            _loc3_ = promoteIconContainer.numChildren;
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc4_ = promoteIconContainer.getChildAt(_loc5_);
               if(_loc4_ is MiragePanelIcon && _loc4_ != param1.target)
               {
                  (_loc4_ as MiragePanelIcon).gotoTwoFrame();
               }
               _loc5_++;
            }
         }
         else
         {
            _mirageTarget = param2;
         }
         if(_mirageTarget)
         {
            _mirageDataShow.clearData();
            _mirageDataShow.showData(_promoteXML,_mirageTarget as DisplayObject,_mainPet,_assistantPet,showWarningBox);
         }
      }
      
      private function showSmallPackage(param1:MouseEvent) : void
      {
         if(param1.target == mainPetContainer || param1.target == assistantPetContainer)
         {
            _targetClickObject = param1.target;
            var _loc2_:* = param1.target;
            if(mainPetContainer !== _loc2_)
            {
               SmallPackage.getInstance().x = assistantPetContainer.x + assistantPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
               addChild(_selectAssistantPetBorder);
               if(getChildByName(_selectMainPetBorder.name))
               {
                  removeChild(_selectMainPetBorder);
               }
            }
            else
            {
               SmallPackage.getInstance().x = mainPetContainer.x + mainPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
               addChild(_selectMainPetBorder);
               if(getChildByName(_selectAssistantPetBorder.name))
               {
                  removeChild(_selectAssistantPetBorder);
               }
            }
            if(!getChildByName(SmallPackage.getInstance().name))
            {
               addChild(SmallPackage.getInstance());
            }
            SmallPackage.getInstance().visible = true;
         }
      }
      
      private function hideSmallPackage() : void
      {
         SmallPackage.getInstance().visible = false;
      }
      
      private function hideShineBorder(param1:UIBtnEvent = null) : void
      {
         _targetClickObject = null;
         if(getChildByName(_selectMainPetBorder.name))
         {
            removeChild(_selectMainPetBorder);
         }
         if(getChildByName(_selectAssistantPetBorder.name))
         {
            removeChild(_selectAssistantPetBorder);
         }
      }
      
      private function searchInpromoteIcons(param1:String) : int
      {
         if(!Boolean(_promoteIcons) || !_promoteIcons.length)
         {
            return -1;
         }
         var _loc3_:int = 0;
         var _loc2_:int = int(_promoteIcons.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1 == "精气")
            {
               if(_promoteIcons[_loc3_][0] is EssenceIcon)
               {
                  return _loc3_;
               }
            }
            else if(_promoteIcons[_loc3_][0] is Skill && (_promoteIcons[_loc3_][0] as Skill).skillVO.name == param1)
            {
               return _loc3_;
            }
            _loc3_++;
         }
         return -1;
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 300;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function init() : void
      {
         _mainPetShowContainer = new EntityShowContainer();
         _mainPetShowContainer.init();
         _assistantPetShowContainer = new EntityShowContainer();
         _assistantPetShowContainer.init();
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _mainPet = new Pet(null);
         _assistantPet = new Pet(null);
         mainPetContainer.mouseChildren = false;
         assistantPetContainer.mouseChildren = false;
         mainPetContainer.addChild(_mainPetShowContainer.getShow());
         assistantPetContainer.addChild(_assistantPetShowContainer.getShow());
         _selectMainPetBorder = new SelectMainPetBorder();
         _selectAssistantPetBorder = new SelectAssistantPetBorder();
         _selectMainPetBorder.x = 351.8;
         _selectMainPetBorder.y = -21.35;
         _selectAssistantPetBorder.x = 452;
         _selectAssistantPetBorder.y = 180.5;
         hideShineBorder();
         _mirageDataShow = new MirageDataShow();
         _mirageDataShow.x = 500;
         _mirageDataShow.y = 100;
         _mirageDataShow.mouseEnabled = false;
         addChild(_mirageDataShow);
         _pageBtnGroupForIcons = new PageBtnGroup_Vertical();
         _pageBtnGroupForIcons.x = 330;
         _pageBtnGroupForIcons.y = 435;
         setPage(1);
      }
   }
}

import UI.Equipments.EquipmentVO.EquipmentVO;
import UI.Equipments.PetEquipments.PetEquipmentVO;
import UI.SmallPackage.IEqIsShowInSmallPackage;

class EqIsShow implements IEqIsShowInSmallPackage
{
   public var isMaxLevel:Boolean;
   
   public var petSeries:String;
   
   public function EqIsShow()
   {
      super();
   }
   
   public function clear() : void
   {
   }
   
   public function judeIsShow(param1:EquipmentVO) : Boolean
   {
      if(!(param1 is PetEquipmentVO))
      {
         return false;
      }
      if(Boolean(petSeries) && (param1 as PetEquipmentVO).petSeries != petSeries)
      {
         return false;
      }
      if(isMaxLevel && (param1 as PetEquipmentVO).level < (param1 as PetEquipmentVO).maxLevel)
      {
         return false;
      }
      return true;
   }
}
