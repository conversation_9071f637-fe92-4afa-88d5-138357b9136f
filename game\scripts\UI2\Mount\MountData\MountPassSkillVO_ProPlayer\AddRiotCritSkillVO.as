package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddRiotCritSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addRiot:Number;
      
      private var m_addCrit:Number;
      
      public function AddRiotCritSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("riot_mountAdd1",m_targetPlayer.playerVO.get2("riot_mountAdd1") - m_addValue);
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") - addCrit);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addRiot;
         m_targetPlayer.playerVO.set2("riot_mountAdd1",m_targetPlayer.playerVO.get2("riot_mountAdd1") + m_addValue);
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") + addCrit);
      }
      
      public function getAddRiot() : Number
      {
         return addRiot;
      }
      
      public function getAddCrit() : Number
      {
         return addCrit;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addRiot = Number(param1.data.(@att == "addRiot")[0].@value);
         this.addCrit = Number(param1.data.(@att == "addCrit")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.m_addRiot = m_addRiot;
         _antiwear.m_addCrit = m_addCrit;
      }
      
      private function get addRiot() : Number
      {
         return _antiwear.m_addRiot;
      }
      
      private function set addRiot(param1:Number) : void
      {
         _antiwear.m_addRiot = param1;
      }
      
      private function get addCrit() : Number
      {
         return _antiwear.m_addCrit;
      }
      
      private function set addCrit(param1:Number) : void
      {
         _antiwear.m_addCrit = param1;
      }
   }
}

