package UI.MessageBox
{
   import UI.AutomaticPetPanel.SkillShow;
   import UI.AutomaticPetPanel.SkillShowListener;
   import UI2.Mount.MountData.MountSkillVO.MountPassiveSkillVO1;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVO;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class ShowMountSkillMessage
   {
      private var m_skillShow:SkillShow;
      
      private var m_skillShowListener:SkillShowListener;
      
      private var m_description:String;
      
      private var m_box:MessageBox;
      
      public function ShowMountSkillMessage()
      {
         super();
         m_skillShow = new SkillShow();
         m_skillShow.init(Part1.getInstance().getLoadUI(),Part1.getInstance().getVersionControl(),null,null);
         m_skillShowListener = new SkillShowListener();
         m_skillShowListener.showSkillCompleteFun = showSkillComplete;
         m_skillShow.addSkillShowListener(m_skillShowListener);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         ClearUtil.clearObject(m_skillShowListener);
         m_skillShowListener = null;
         m_box = null;
      }
      
      public function showMessage(param1:MountSkillVO, param2:MessageBox) : void
      {
         var _loc5_:String = null;
         var _loc4_:String = null;
         var _loc3_:String = null;
         m_box = param2;
         if(param1)
         {
            _loc5_ = "";
            _loc4_ = "";
            _loc3_ = "";
            _loc5_ = MessageBoxFunction.getInstance().toHTMLText(param1.getName(),20);
            _loc4_ = MessageBoxFunction.getInstance().toHTMLText("星级：",18) + MessageBoxFunction.getInstance().toHTMLText(param1.getLevel().toString(),16);
            _loc3_ += _loc5_ + "<br>" + "<br>";
            _loc3_ += _loc4_ + "<br>";
            if(param1 is MountPassiveSkillVO1)
            {
               _loc3_ += MessageBoxFunction.getInstance().toHTMLText("效果：",18) + MessageBoxFunction.getInstance().toHTMLText((param1 as MountPassiveSkillVO1).getDescription2(),16) + "<br>";
            }
            _loc3_ += MessageBoxFunction.getInstance().toHTMLText("-----------------------",14) + "<br>" + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(param1.getDescription(),14);
            m_description = _loc3_;
            m_skillShow.resetShow(param1.getIconSwfPath(),param1.getIconClassName());
         }
      }
      
      private function showSkillComplete(param1:SkillShow) : void
      {
         m_box.boxWidth = 250;
         m_box.addSprite(m_skillShow);
         m_box.htmlText = m_description;
      }
   }
}

