package UI.MainLineTask.TaskDetectors
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class TaskDetectorFactory
   {
      private var m_classes:Array;
      
      public function TaskDetectorFactory()
      {
         super();
         m_classes = [TaskDetector,TaskDetector_Player,TaskDetector_PKWinNum,TaskDetector_HaveEq];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_classes);
         m_classes = null;
      }
      
      public function createTaskDetector(param1:String) : TaskDetector
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
      
      public function createTaskDetectorByXML(param1:XML) : TaskDetector
      {
         var _loc3_:String = String(param1.@className);
         var _loc2_:TaskDetector = createTaskDetector(_loc3_);
         _loc2_.initByXML(param1);
         return _loc2_;
      }
   }
}

