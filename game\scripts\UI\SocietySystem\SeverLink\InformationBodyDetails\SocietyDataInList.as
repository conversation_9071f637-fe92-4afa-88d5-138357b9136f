package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class SocietyDataInList
   {
      protected var m_societyId:int;
      
      protected var m_societyNameLength:int;
      
      protected var m_societyName:String;
      
      protected var m_societyLevel:int;
      
      protected var m_playerNumInSociety:int;
      
      protected var m_maxPlayerNumInSociety:int;
      
      protected var m_uid_leader:Number;
      
      protected var m_idx_leader:int;
      
      protected var m_Namelength_leader:int;
      
      protected var m_Name_leader:String;
      
      protected var m_totalConValue:int;
      
      protected var m_announcementOfSocietyLength:int;
      
      protected var m_announcementOfSociety:String;
      
      public function SocietyDataInList()
      {
         super();
      }
      
      public function initFromByteArray(param1:ByteArray) : void
      {
         m_societyId = param1.readInt();
         trace("    societyId:",m_societyId);
         m_societyNameLength = param1.readInt();
         trace("    societyNameLength:",m_societyNameLength);
         if(m_societyNameLength)
         {
            m_societyName = param1.readUTFBytes(m_societyNameLength);
         }
         trace("    societyName:",m_societyName);
         m_societyLevel = param1.readInt();
         trace("    societyLevel:",m_societyLevel);
         m_playerNumInSociety = param1.readInt();
         trace("    playerNumInSociety:",m_playerNumInSociety);
         m_maxPlayerNumInSociety = param1.readInt();
         trace("    maxPlayerNumInSociety:",m_maxPlayerNumInSociety);
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid_leader = _loc2_.uid;
         m_idx_leader = _loc2_.idx;
         m_Namelength_leader = param1.readInt();
         if(m_Namelength_leader)
         {
            m_Name_leader = param1.readUTFBytes(m_Namelength_leader);
         }
         m_totalConValue = param1.readInt();
         m_announcementOfSocietyLength = param1.readInt();
         if(m_announcementOfSocietyLength)
         {
            m_announcementOfSociety = param1.readUTFBytes(m_announcementOfSocietyLength);
         }
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getSocietyNameLength() : int
      {
         return m_societyNameLength;
      }
      
      public function getSocietyName() : String
      {
         return m_societyName;
      }
      
      public function getSocietyLevel() : int
      {
         return m_societyLevel;
      }
      
      public function getPlayerNumInSociety() : int
      {
         return m_playerNumInSociety;
      }
      
      public function getMaxPlayerNumInSociety() : int
      {
         return m_maxPlayerNumInSociety;
      }
      
      public function getUid_Leader() : Number
      {
         return m_uid_leader;
      }
      
      public function getIdx_Leader() : int
      {
         return m_idx_leader;
      }
      
      public function getNameLength_Leader() : int
      {
         return m_Namelength_leader;
      }
      
      public function getName_Leader() : String
      {
         return m_Name_leader;
      }
      
      public function getTotalConValue() : int
      {
         return m_totalConValue;
      }
      
      public function getAnnouncementOfSocietyLength() : int
      {
         return m_announcementOfSocietyLength;
      }
      
      public function getAnnouncementOfSociety() : String
      {
         return m_announcementOfSociety;
      }
   }
}

