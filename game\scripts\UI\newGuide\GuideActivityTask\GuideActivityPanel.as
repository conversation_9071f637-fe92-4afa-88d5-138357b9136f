package UI.newGuide.GuideActivityTask
{
   import UI.newGuide.NewGuidePanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GuideActivityPanel
   {
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_guideactivitylist:GuideActivityList;
      
      private var m_guideactivitybtnshell:GuideActivityBtnShell;
      
      public function GuideActivityPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_guideactivitylist);
         m_guideactivitylist = null;
         ClearUtil.clearObject(m_guideactivitybtnshell);
         m_guideactivitybtnshell = null;
      }
      
      public function show() : void
      {
         m_guideactivitylist.show();
         m_guideactivitybtnshell.show();
      }
      
      public function refreshlist() : void
      {
         m_guideactivitylist.refreshlist();
      }
      
      public function hide() : void
      {
         m_guideactivitylist.hide();
         m_guideactivitybtnshell.hide();
      }
      
      public function init(param1:NewGuidePanel, param2:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_show = param2;
         initParams();
      }
      
      private function initParams() : void
      {
         m_guideactivitylist = new GuideActivityList();
         m_guideactivitylist.init(m_newguidepanel,this,m_show);
         m_guideactivitybtnshell = new GuideActivityBtnShell();
         m_guideactivitybtnshell.init(m_newguidepanel,this,m_show);
      }
      
      public function refreshScript(param1:GuideActivityItem) : void
      {
         m_guideactivitybtnshell.refreshScript(param1.getData());
      }
   }
}

