package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_VerifyReturn;
   
   public class TEST_DOWN_VerifyReturn extends DOWN_VerifyReturn
   {
      public function TEST_DOWN_VerifyReturn()
      {
         super();
         m_informationBodyId = 2001;
      }
      
      public function initData(param1:int, param2:int, param3:int) : void
      {
         m_isSuccess = param1;
         m_session = param2;
         m_encryValue = param3;
      }
   }
}

