package UI.newTask.NewActivityTask
{
   import UI.Task.TaskVO.MTaskVO;
   import UI.newTask.NewTaskPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class NewActivityPanel
   {
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_newactivitylist:NewActivityList;
      
      private var m_newactivitydetail:NewActivityDetail;
      
      private var m_newactivitybtnshell:NewActivityBtnShell;
      
      public function NewActivityPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_newactivitylist);
         m_newactivitylist = null;
         ClearUtil.clearObject(m_newactivitydetail);
         m_newactivitydetail = null;
         ClearUtil.clearObject(m_newactivitybtnshell);
         m_newactivitybtnshell = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip) : void
      {
         m_newtaskpanel = param1;
         m_show = param2;
         m_mc = m_show["tasklistmc"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_newactivitylist = new NewActivityList();
         m_newactivitylist.init(m_newtaskpanel,m_show,this);
         m_newactivitydetail = new NewActivityDetail();
         m_newactivitydetail.init(m_newtaskpanel,m_show,this);
         m_newactivitybtnshell = new NewActivityBtnShell();
         m_newactivitybtnshell.init(m_newtaskpanel,m_show,this);
      }
      
      public function show(param1:String) : void
      {
         m_newactivitydetail.show();
         m_newactivitybtnshell.show();
         m_newactivitylist.show(param1);
      }
      
      public function hide() : void
      {
         m_newactivitylist.hide();
         m_newactivitydetail.hide();
         m_newactivitybtnshell.hide();
      }
      
      public function refreshlist() : void
      {
         m_newactivitylist.refreshlist();
      }
      
      public function refreshScript(param1:MTaskVO) : void
      {
         m_newactivitydetail.refreshScript(param1);
         m_newactivitybtnshell.refreshScript(param1);
      }
   }
}

