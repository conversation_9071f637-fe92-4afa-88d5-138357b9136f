package UI2.codekey
{
   import Json.MyJSON;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.ExchangeGiftBag.GiftMountData;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PlayerDataForPK;
   import UI.TextTrace.traceText;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.utils.MD5;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class CodekeyZhuoquGiftBagPanel extends MySprite
   {
      private var yaYaLeURL:String = "http://my.4399.com/jifen/api-apply";
      
      private var website:String = "http://my.4399.com/forums/thread-59758478";
      
      public var _show:MovieClip;
      
      private var m_mcInfo:MovieClip;
      
      private var m_txtCode:TextField;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_getGift:ButtonLogicShell2;
      
      private var m_getinweb:ButtonLogicShell2;
      
      private var m_day1:MySwitchBtnLogicShell;
      
      private var m_day5:MySwitchBtnLogicShell;
      
      private var m_day14:MySwitchBtnLogicShell;
      
      private var m_day25:MySwitchBtnLogicShell;
      
      private var m_day30:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_bDay1:Boolean;
      
      private var m_bDay5:Boolean;
      
      private var m_bDay14:Boolean;
      
      private var m_bDay25:Boolean;
      
      private var m_bDay30:Boolean;
      
      private var type:String;
      
      private var giftXML:XML;
      
      private var _exchangeGiftBagXML:XML;
      
      private var _currentServeTime:String;
      
      private var _newEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _mountData:GiftMountData;
      
      public function CodekeyZhuoquGiftBagPanel()
      {
         super();
         name = "CodekeyZhuoquGiftBagPanel";
         initParams();
         loadXML();
      }
      
      private function getGiftDay1() : void
      {
         var _loc9_:String = null;
         var _loc6_:URLVariables = null;
         var _loc7_:String = null;
         var _loc3_:String = null;
         var _loc2_:String = null;
         var _loc1_:String = null;
         var _loc8_:String = null;
         var _loc5_:URLRequest = new URLRequest();
         _loc5_.method = "POST";
         if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
         {
            if(type == "1")
            {
               _loc2_ = "34451";
               _loc8_ = "fc8d8ae9b6d361ae6c293051718fa16e";
            }
            else if(type == "5")
            {
               _loc2_ = "34452";
               _loc8_ = "7af48cfc0ff2ac16cd22c2107f98ea94";
            }
            else if(type == "14")
            {
               _loc2_ = "34453";
               _loc8_ = "281cc7bebbfef55175ee1aaa5abe6dea";
            }
            else if(type == "25")
            {
               _loc2_ = "34454";
               _loc8_ = "68a11fc0a0bc802445334434b86fe9ce";
            }
            else if(type == "30")
            {
               _loc2_ = "34251";
               _loc8_ = "66e8e3bd4d75c1258948eb778491c0f0";
            }
         }
         else if(type == "1")
         {
            _loc2_ = "34339";
            _loc8_ = "55622566b9c29bf6c761f85ee4227f73";
         }
         else if(type == "5")
         {
            _loc2_ = "34340";
            _loc8_ = "15c5e4b45219af550988a2fcd7116f51";
         }
         else if(type == "14")
         {
            _loc2_ = "34341";
            _loc8_ = "a4d9395888cb00ccf85a691d7d3d84d6";
         }
         else if(type == "25")
         {
            _loc2_ = "34342";
            _loc8_ = "657d3115fe3ad1bb331d2c049ac30cad";
         }
         else if(type == "30")
         {
            _loc2_ = "34342";
            _loc8_ = "657d3115fe3ad1bb331d2c049ac30cad";
         }
         _loc7_ = GameData.getInstance().getLoginReturnData().getUid();
         _loc3_ = m_txtCode.text;
         _loc1_ = "1";
         _loc6_ = new URLVariables();
         _loc6_.uid = _loc7_;
         _loc6_.activation = _loc3_;
         _loc6_.product_id = _loc2_;
         _loc6_.type = _loc1_;
         _loc6_.token = MD5.hash(_loc3_ + _loc2_ + _loc1_ + _loc7_ + _loc8_);
         _loc5_.data = _loc6_;
         _loc9_ = yaYaLeURL;
         _loc5_.url = _loc9_;
         var _loc4_:URLLoader = new URLLoader();
         _loc4_.addEventListener("complete",DoUIEventDay1);
         _loc4_.addEventListener("ioError",DoUIEventDay1);
         _loc4_.addEventListener("securityError",DoUIEventDay1);
         _loc4_.load(_loc5_);
      }
      
      private function DoUIEventDay1(param1:Event) : void
      {
         var _loc5_:Object = null;
         var _loc2_:SaveTaskInfo = null;
         var _loc4_:Object = param1.target.data;
         var _loc3_:String = _loc4_ as String;
         m_getGift.unLock();
         if(param1.type == "complete")
         {
            _loc5_ = MyJSON.decode(_loc3_);
            if(_loc5_.code == 100)
            {
               if(Boolean(_newEquipmentVOs) || _mountData)
               {
                  if(_newEquipmentVOs)
                  {
                     MyFunction2.trueAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,showWarningBox,["礼包领取成功！",0]);
                  }
                  if(_mountData)
                  {
                     GamingUI.getInstance().player1.getMountsVO().addGetMaterialPointNum(_mountData.getZhaoHuanNum());
                     GamingUI.getInstance().player1.getMountsVO().addStrengthenPointNum(_mountData.getLingShouShiNum());
                     GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + _mountData.getYuanbaoNum();
                     PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint + _mountData.getPkNum();
                  }
                  showWarningBox("礼包领取成功",0);
                  _loc2_ = new SaveTaskInfo();
                  _loc2_.type = "4399";
                  _loc2_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc2_);
                  MyFunction2.saveGame();
               }
            }
            else
            {
               showWarningBox(_loc5_.msg,0);
            }
         }
         else if(param1.type == "ioError")
         {
            traceText("流错误！");
            traceText((param1 as IOErrorEvent).text);
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
         else if(param1.type == "securityError")
         {
            traceText("出现安全错误！系统尝试从安全沙箱外部的服务器加载数据！");
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
      
      private function showresult(param1:int) : void
      {
         switch(param1 - 104)
         {
            case 0:
            case 3:
            case 6:
               showWarningBox("此兑换码已被使用！",0);
               break;
            case 1:
               showWarningBox("" + param1,0);
               break;
            case 4:
               showWarningBox("" + param1,0);
               break;
            case 7:
               showWarningBox("" + param1,0);
               break;
            case 8:
               showWarningBox("您已领取过初级礼包！",0);
               break;
            case 9:
               showWarningBox("您已领取过中级礼包！",0);
               break;
            case 10:
               showWarningBox("您已领取过高级礼包！",0);
               break;
            default:
               showWarningBox("" + param1,0);
         }
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(_newEquipmentVOs);
         _newEquipmentVOs = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_getGift);
         m_getGift = null;
         ClearUtil.clearObject(m_getinweb);
         m_getinweb = null;
         ClearUtil.clearObject(m_day1);
         m_day1 = null;
         ClearUtil.clearObject(m_day5);
         m_day5 = null;
         ClearUtil.clearObject(m_day14);
         m_day14 = null;
         ClearUtil.clearObject(m_day25);
         m_day25 = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         GamingUI.getInstance().clearZhuoquGiftPanel();
         super.clear();
      }
      
      private function initParams() : void
      {
         m_quitBtn = new ButtonLogicShell2();
         m_getGift = new ButtonLogicShell2();
         m_getinweb = new ButtonLogicShell2();
         m_day1 = new MySwitchBtnLogicShell();
         m_day5 = new MySwitchBtnLogicShell();
         m_day14 = new MySwitchBtnLogicShell();
         m_day25 = new MySwitchBtnLogicShell();
         m_day30 = new MySwitchBtnLogicShell();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtnGroup.addSwitchBtn(m_day1);
         m_switchBtnGroup.addSwitchBtn(m_day5);
         m_switchBtnGroup.addSwitchBtn(m_day14);
         m_switchBtnGroup.addSwitchBtn(m_day25);
         m_switchBtnGroup.addSwitchBtn(m_day30);
         m_quitBtn.setTipString("关闭");
         m_getGift.setTipString("领取礼包");
         m_getinweb.setTipString("获取兑换码");
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("qrcodezhuoqu") as MovieClip;
         addChild(_show);
         addEventListener("clickButton",clickButton,true,0,true);
         m_quitBtn.setShow(_show["quitBtn"]);
         m_getGift.setShow(_show["btngetgift"]);
         m_getinweb.setShow(_show["btninweb"]);
         m_day1.setShow(_show["btn_1"]);
         m_day5.setShow(_show["btn_2"]);
         m_day14.setShow(_show["btn_3"]);
         m_day25.setShow(_show["btn_4"]);
         m_day30.setShow(_show["btn_5"]);
         if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
         {
            m_mcInfo = _show["baginfomc5"];
            (_show["baginfomc"] as MovieClip).visible = false;
         }
         else
         {
            m_mcInfo = _show["baginfomc"];
            (_show["baginfomc5"] as MovieClip).visible = false;
         }
         m_mcInfo.gotoAndStop("1");
         m_txtCode = _show["codekeytext"] as TextField;
         m_txtCode.type = "input";
         m_switchBtnGroup.addEnd();
      }
      
      private function loadXML() : void
      {
         MyFunction2.loadXMLAndGetServerTimeFunction("ExchangeGiftBag",function(param1:XML, param2:String):void
         {
            var _loc10_:int = 0;
            var _loc5_:String = null;
            var _loc3_:String = null;
            var _loc9_:String = null;
            _exchangeGiftBagXML = param1;
            _currentServeTime = param2;
            giftXML = _exchangeGiftBagXML.ZhuoquQianDaoQiGiftBagBtn[0];
            var _loc8_:XMLList = _exchangeGiftBagXML.children();
            var _loc7_:int = int(_loc8_.length());
            _loc10_ = 0;
            while(_loc10_ < _loc7_)
            {
               _loc5_ = String(_loc8_[_loc10_].@btnName);
               if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
               {
                  if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoQiGiftBagBtn")
                  {
                     _loc3_ = String(_loc8_[_loc10_].@startTime);
                     _loc9_ = String(_loc8_[_loc10_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                     {
                        m_bDay1 = true;
                     }
                     else
                     {
                        m_bDay1 = false;
                     }
                  }
                  else if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoShiGiftBagBtn")
                  {
                     _loc3_ = String(_loc8_[_loc10_].@startTime);
                     _loc9_ = String(_loc8_[_loc10_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                     {
                        m_bDay5 = true;
                     }
                     else
                     {
                        m_bDay5 = false;
                     }
                  }
                  else if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoErShiGiftBagBtn")
                  {
                     _loc3_ = String(_loc8_[_loc10_].@startTime);
                     _loc9_ = String(_loc8_[_loc10_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                     {
                        m_bDay14 = true;
                     }
                     else
                     {
                        m_bDay14 = false;
                     }
                  }
                  else if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoManqinGiftBagBtn")
                  {
                     _loc3_ = String(_loc8_[_loc10_].@startTime);
                     _loc9_ = String(_loc8_[_loc10_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                     {
                        m_bDay25 = true;
                     }
                     else
                     {
                        m_bDay25 = false;
                     }
                  }
                  else if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoManqinGiftBagBtn")
                  {
                     _loc3_ = String(_loc8_[_loc10_].@startTime);
                     _loc9_ = String(_loc8_[_loc10_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                     {
                        m_bDay30 = true;
                     }
                     else
                     {
                        m_bDay30 = false;
                     }
                  }
               }
               else if(Boolean(_loc5_) && _loc5_ == "WuyueQianDaoQiGiftBagBtn5")
               {
                  _loc3_ = String(_loc8_[_loc10_].@startTime);
                  _loc9_ = String(_loc8_[_loc10_].@endTime);
                  if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                  {
                     m_bDay1 = true;
                  }
                  else
                  {
                     m_bDay1 = false;
                  }
               }
               else if(Boolean(_loc5_) && _loc5_ == "WuyueQianDaoShiGiftBagBtn5")
               {
                  _loc3_ = String(_loc8_[_loc10_].@startTime);
                  _loc9_ = String(_loc8_[_loc10_].@endTime);
                  if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                  {
                     m_bDay5 = true;
                  }
                  else
                  {
                     m_bDay5 = false;
                  }
               }
               else if(Boolean(_loc5_) && _loc5_ == "WuyueQianDaoErShiGiftBagBtn5")
               {
                  _loc3_ = String(_loc8_[_loc10_].@startTime);
                  _loc9_ = String(_loc8_[_loc10_].@endTime);
                  if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                  {
                     m_bDay14 = true;
                  }
                  else
                  {
                     m_bDay14 = false;
                  }
               }
               else if(Boolean(_loc5_) && _loc5_ == "ZhuoquQianDaoManqinGiftBagBtn5")
               {
                  _loc3_ = String(_loc8_[_loc10_].@startTime);
                  _loc9_ = String(_loc8_[_loc10_].@endTime);
                  if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                  {
                     m_bDay25 = true;
                  }
                  else
                  {
                     m_bDay25 = false;
                  }
               }
               else if(Boolean(_loc5_) && _loc5_ == "WuyueQianDaoManqinGiftBagBtn5")
               {
                  _loc3_ = String(_loc8_[_loc10_].@startTime);
                  _loc9_ = String(_loc8_[_loc10_].@endTime);
                  if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc9_) || new TimeUtil().timeInterval(_currentServeTime,_loc9_) > 0))
                  {
                     m_bDay30 = true;
                  }
                  else
                  {
                     m_bDay30 = false;
                  }
               }
               _loc10_++;
            }
            init();
         },showWarningBox,true);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function loadData() : void
      {
         var i:int;
         var length:int;
         var id:int;
         var ids:Vector.<int>;
         var nums:Vector.<int>;
         ClearUtil.clearObject(_newEquipmentVOs);
         _newEquipmentVOs = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         i = 0;
         ids = new Vector.<int>();
         nums = new Vector.<int>();
         if(giftXML)
         {
            length = int(giftXML.children().length());
            i = 0;
            while(i < length)
            {
               id = int(giftXML.children()[i].@id);
               ids.push(id);
               nums.push(int(giftXML.children()[i].@num));
               i++;
            }
            _newEquipmentVOs = XMLSingle.getEquipmentVOsIDs(ids,XMLSingle.getInstance().equipmentXML,nums,true);
            MyFunction2.falseAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,function():void
            {
               showWarningBox("玩家1背包放不下礼包！",0);
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            },sendRequest,null,null);
            _mountData = new GiftMountData();
            _mountData.initByXML(giftXML);
         }
      }
      
      private function sendRequest() : void
      {
         getGiftDay1();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         _show["manqin_mc"].visible = false;
         switch(param1.button)
         {
            case m_quitBtn:
               clear();
               return;
            case m_day1:
               m_mcInfo.gotoAndStop("1");
               type = "1";
               if(m_bDay1)
               {
                  m_getGift.unLock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),0);
               }
               else
               {
                  m_getGift.lock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),-100);
               }
               if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
               {
                  giftXML = _exchangeGiftBagXML.ZhuoquQianDaoQiGiftBagBtn[3];
                  break;
               }
               giftXML = _exchangeGiftBagXML.ZhuoquQianDaoQiGiftBagBtn5[3];
               break;
            case m_day5:
               m_mcInfo.gotoAndStop("2");
               type = "5";
               if(m_bDay5)
               {
                  m_getGift.unLock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),0);
               }
               else
               {
                  m_getGift.lock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),-100);
               }
               if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
               {
                  giftXML = _exchangeGiftBagXML.ZhuoquQianDaoShiGiftBagBtn[3];
                  break;
               }
               giftXML = _exchangeGiftBagXML.ZhuoquQianDaoShiGiftBagBtn5[3];
               break;
            case m_day14:
               m_mcInfo.gotoAndStop("3");
               type = "14";
               if(m_bDay14)
               {
                  m_getGift.unLock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),0);
               }
               else
               {
                  m_getGift.lock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),-100);
               }
               if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
               {
                  giftXML = _exchangeGiftBagXML.ZhuoquQianDaoErShiGiftBagBtn[3];
                  break;
               }
               giftXML = _exchangeGiftBagXML.ZhuoquQianDaoErShiGiftBagBtn5[3];
               break;
            case m_day25:
               m_mcInfo.gotoAndStop("4");
               type = "25";
               if(m_bDay25)
               {
                  m_getGift.unLock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),0);
               }
               else
               {
                  m_getGift.lock();
                  MyFunction.getInstance().changeSaturation(m_getGift.getShow(),-100);
               }
               if(new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-01 00:00:00") <= 0 && new TimeUtil().timeInterval(TimeUtil.timeStr,"2019-11-30 23:59:59") >= 0)
               {
                  giftXML = _exchangeGiftBagXML.ZhuoquQianDaoManqinGiftBagBtn[3];
                  break;
               }
               giftXML = _exchangeGiftBagXML.ZhuoquQianDaoManqinGiftBagBtn5[3];
               break;
            case m_day30:
               _show["manqin_mc"].visible = true;
               break;
            case m_getGift:
               if(m_txtCode.text == null)
               {
                  return;
               }
               m_getGift.lock();
               loadData();
               break;
            case m_getinweb:
               navigateToURL(new URLRequest(website),"_blank");
         }
      }
   }
}

