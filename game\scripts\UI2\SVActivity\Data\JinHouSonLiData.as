package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class JinHouSonLiData extends DataManagerParent
   {
      public function JinHouSonLiData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.startTime = String(param1.@startTime);
         this.endTime = String(param1.@endTime);
         this.ticketPriceOne = uint(param1.@ticketPriceOne);
         this.ticketPriceIdOne = String(param1.@ticketPriceIdOne);
         this.ticketPriceTen = uint(param1.@ticketPriceTen);
         this.ticketPriceIdTen = String(param1.@ticketPriceIdTen);
         this.timesMax = Math.ceil(TimeUtil.getTimeUtil().timeIntervalBySecond(startTime,endTime) / (AntiwearNumber.nums["consts_12"] * 3600));
      }
      
      public function getTicketPriceOne() : int
      {
         return ticketPriceOne;
      }
      
      public function getTicketPriceIdOne() : String
      {
         return ticketPriceIdOne;
      }
      
      public function getTicketPriceTen() : int
      {
         return ticketPriceTen;
      }
      
      public function getTicketPriceIdTen() : String
      {
         return ticketPriceIdTen;
      }
      
      public function getStartTime() : String
      {
         return startTime;
      }
      
      public function getEndTime() : String
      {
         return endTime;
      }
      
      public function getTimesMax() : int
      {
         return timesMax;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
      
      private function get endTime() : String
      {
         return _antiwear.endTime;
      }
      
      private function set endTime(param1:String) : void
      {
         _antiwear.endTime = param1;
      }
      
      private function get ticketPriceOne() : uint
      {
         return _antiwear.ticketPriceOne;
      }
      
      private function set ticketPriceOne(param1:uint) : void
      {
         _antiwear.ticketPriceOne = param1;
      }
      
      private function get ticketPriceIdOne() : String
      {
         return _antiwear.ticketPriceIdOne;
      }
      
      private function set ticketPriceIdOne(param1:String) : void
      {
         _antiwear.ticketPriceIdOne = param1;
      }
      
      private function get ticketPriceTen() : uint
      {
         return _antiwear.ticketPriceTen;
      }
      
      private function set ticketPriceTen(param1:uint) : void
      {
         _antiwear.ticketPriceTen = param1;
      }
      
      private function get ticketPriceIdTen() : String
      {
         return _antiwear.ticketPriceIdTen;
      }
      
      private function set ticketPriceIdTen(param1:String) : void
      {
         _antiwear.ticketPriceIdTen = param1;
      }
      
      private function get timesMax() : uint
      {
         return _antiwear.timesMax;
      }
      
      private function set timesMax(param1:uint) : void
      {
         _antiwear.timesMax = param1;
      }
   }
}

