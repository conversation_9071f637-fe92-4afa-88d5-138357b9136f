package UI.EquipmentMakeAndUpgrade
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class MagicChargeBlessShow extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_needTicketText:TextField;
      
      private var m_totalTicketText:TextField;
      
      private var m_buyGuanYinProtectSureBtn:ButtonLogicShell;
      
      private var m_buyGuanYinProtecteCancelBtn:ButtonLogicShell;
      
      private var m_quitBtn:ButtonLogicShell;
      
      private var m_reChargeBtn:ButtonLogicShell;
      
      private var m_MagicInherit:EquipMagicInherit;
      
      public function MagicChargeBlessShow()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_needTicketText = null;
         m_totalTicketText = null;
         ClearUtil.clearObject(m_buyGuanYinProtectSureBtn);
         m_buyGuanYinProtectSureBtn = null;
         ClearUtil.clearObject(m_buyGuanYinProtecteCancelBtn);
         m_buyGuanYinProtecteCancelBtn = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_reChargeBtn);
         m_reChargeBtn = null;
         m_MagicInherit = null;
         super.clear();
      }
      
      public function init(param1:EquipMagicInherit) : void
      {
         m_MagicInherit = param1;
         if(m_show == null)
         {
            m_show = MyFunction2.returnShowByClassName("BlessBtnTip") as MovieClip;
            addChild(m_show);
            m_buyGuanYinProtectSureBtn = new ButtonLogicShell();
            m_buyGuanYinProtectSureBtn.setShow(m_show["yesBtn"]);
            m_quitBtn = new ButtonLogicShell();
            m_quitBtn.setShow(m_show["quitBtn"]);
            m_buyGuanYinProtecteCancelBtn = new ButtonLogicShell();
            m_buyGuanYinProtecteCancelBtn.setShow(m_show["noBtn"]);
            m_reChargeBtn = new ButtonLogicShell();
            m_reChargeBtn.setShow(m_show["addValue"]);
            m_totalTicketText = m_show["value"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_totalTicketText);
            m_needTicketText = m_show["price"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_needTicketText);
            this.x = 456;
            this.y = 156;
         }
         var _loc3_:uint = uint(XMLSingle.getInstance().dataXML.blessProtectData[0].@ticket);
         var _loc2_:Number = CurrentTicketPointManager.getInstance().getCurrentTicketPoint();
         m_needTicketText.text = _loc3_.toString();
         m_totalTicketText.text = _loc2_.toString();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_buyGuanYinProtecteCancelBtn:
            case m_buyGuanYinProtectSureBtn:
               if(param1.button == m_buyGuanYinProtectSureBtn)
               {
                  closeBuyGuanYinProtectShow();
                  buyGuanYinProtect();
                  break;
               }
               closeBuyGuanYinProtectShow();
               m_MagicInherit.start();
               break;
            case m_reChargeBtn:
               AnalogServiceHoldFunction.getInstance().payMoney_As3();
               closeBuyGuanYinProtectShow();
               break;
            case m_quitBtn:
               closeBuyGuanYinProtectShow();
         }
      }
      
      private function buyGuanYinProtect() : void
      {
         var price:int = int(XMLSingle.getInstance().dataXML.blessProtectData[0].@ticket);
         var ticketId:String = String(XMLSingle.getInstance().dataXML.blessProtectData[0].@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买圣器护佑";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               GamingUI.getInstance().showMessageTip("购买物品id前后端不相同！");
               throw new Error("购买物品id前后端不相同！");
            }
            m_MagicInherit.blessProtect();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function closeBuyGuanYinProtectShow() : void
      {
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_buyGuanYinProtecteCancelBtn);
         m_buyGuanYinProtecteCancelBtn = null;
         ClearUtil.clearObject(m_buyGuanYinProtectSureBtn);
         m_buyGuanYinProtectSureBtn = null;
         ClearUtil.clearObject(m_reChargeBtn);
         m_reChargeBtn = null;
         m_totalTicketText = null;
         m_needTicketText = null;
      }
   }
}

