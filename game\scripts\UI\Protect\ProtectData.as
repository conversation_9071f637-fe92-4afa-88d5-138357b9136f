package UI.Protect
{
   import UI.DataManagerParent;
   import UI.Privilege.PrivilegeVO;
   
   public class ProtectData extends DataManagerParent
   {
      public static var _instance:ProtectData;
      
      private var _getGiftDate:String;
      
      private var _privilegeVOs:Vector.<PrivilegeVO>;
      
      private var _isOpenShop:Boolean;
      
      private var _isOpenStorage:Boolean;
      
      private var _isLostEquipmentInMake:Boolean = true;
      
      private var _isLostEquipmentInUpgrade:Boolean = true;
      
      private var _addExperienceValue:int;
      
      private var _resurgenceValue:int;
      
      private var _huanhuaValue:int;
      
      public function ProtectData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : ProtectData
      {
         if(!_instance)
         {
            _instance = new ProtectData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         var _loc2_:int = 0;
         var _loc1_:int = !!_privilegeVOs ? _privilegeVOs.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _privilegeVOs[_loc2_].clear();
            _privilegeVOs[_loc2_] = null;
            _loc2_++;
         }
         _privilegeVOs = null;
         _instance = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.getGiftDate = _getGiftDate;
         _antiwear.isOpenStorage = _isOpenStorage;
         _antiwear.isOpenShop = _isOpenShop;
         _antiwear.isLostEquipmentInMake = _isLostEquipmentInMake;
         _antiwear.isLostEquipmentInUpgrade = _isLostEquipmentInUpgrade;
         _antiwear.addExperienceValue = _addExperienceValue;
         _antiwear.resurgenceValue = _resurgenceValue;
         _antiwear.huanhuaValue = _huanhuaValue;
      }
      
      public function get getGiftDate() : String
      {
         return _antiwear.getGiftDate;
      }
      
      public function set getGiftDate(param1:String) : void
      {
         _antiwear.getGiftDate = param1;
      }
      
      public function get isOpenStorage() : Boolean
      {
         return _antiwear.isOpenStorage;
      }
      
      public function set isOpenStorage(param1:Boolean) : void
      {
         _antiwear.isOpenStorage = param1;
      }
      
      public function get isOpenShop() : Boolean
      {
         return _antiwear.isOpenShop;
      }
      
      public function set isOpenShop(param1:Boolean) : void
      {
         _antiwear.isOpenShop = param1;
      }
      
      public function get isLostEquipmentInMake() : Boolean
      {
         return _antiwear.isLostEquipmentInMake;
      }
      
      public function set isLostEquipmentInMake(param1:Boolean) : void
      {
         _antiwear.isLostEquipmentInMake = param1;
      }
      
      public function get isLostEquipmentInUpgrade() : Boolean
      {
         return _antiwear.isLostEquipmentInUpgrade;
      }
      
      public function set isLostEquipmentInUpgrade(param1:Boolean) : void
      {
         _antiwear.isLostEquipmentInUpgrade = param1;
      }
      
      public function get addExperienceValue() : int
      {
         return _antiwear.addExperienceValue;
      }
      
      public function set addExperienceValue(param1:int) : void
      {
         _antiwear.addExperienceValue = param1;
      }
      
      public function get resurgenceValue() : int
      {
         return _antiwear.resurgenceValue;
      }
      
      public function set resurgenceValue(param1:int) : void
      {
         _antiwear.resurgenceValue = param1;
      }
      
      public function get huanhuaValue() : int
      {
         return _antiwear.huanhuaValue;
      }
      
      public function set huanhuaValue(param1:int) : void
      {
         _antiwear.huanhuaValue = param1;
      }
      
      public function get privilegeVOs() : Vector.<PrivilegeVO>
      {
         return _privilegeVOs;
      }
      
      public function set privilegeVOs(param1:Vector.<PrivilegeVO>) : void
      {
         var _loc4_:int = 0;
         var _loc2_:String = null;
         _privilegeVOs = param1;
         isOpenShop = false;
         isOpenStorage = false;
         isLostEquipmentInMake = true;
         isLostEquipmentInUpgrade = true;
         resurgenceValue = 0;
         huanhuaValue = 0;
         var _loc3_:int = int(_privilegeVOs.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = _privilegeVOs[_loc4_].className;
            switch(_loc2_.substr(0,_loc2_.length - 2))
            {
               case "Privilege_Shop":
                  isOpenShop = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Storage":
                  isOpenStorage = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Make":
                  isLostEquipmentInMake = !Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Upgrade":
                  isLostEquipmentInUpgrade = !Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Experience":
                  addExperienceValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_yaojiangchongsheng":
                  resurgenceValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_huanhuaBaoji":
                  huanhuaValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_VIPLogo":
                  break;
               default:
                  throw new Error("没有符合条件的特权！");
            }
            _loc4_++;
         }
      }
   }
}

