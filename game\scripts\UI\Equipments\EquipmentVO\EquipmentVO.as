package UI.Equipments.EquipmentVO
{
   import UI.MyFunction2;
   import UI.Players.PlayerVO;
   import UI.XMLSingle;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class EquipmentVO
   {
      public var extraData:Object;
      
      public var className:String;
      
      public var messageBoxColor:int;
      
      public var isAbleAKeySell:Boolean;
      
      public var description:String;
      
      public var name:String;
      
      public var owner:String;
      
      public var equipmentType:String;
      
      private var _id:int;
      
      private var _level:int;
      
      private var _ticketPrice:Number;
      
      private var _isAbleSell:Boolean = false;
      
      private var _ticketId:String;
      
      private var _isBinding:Boolean;
      
      private var _isPutInOperate:Boolean;
      
      private var _isOverdue:int;
      
      private var _isShopItem:Boolean;
      
      protected var _playerVO:PlayerVO;
      
      protected var _binaryEn:binaryEncrypt;
      
      public var _antiwear:Antiwear;
      
      public function EquipmentVO()
      {
         super();
         init();
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.level = _level;
         _antiwear.ticketPrice = _ticketPrice = 0;
         _antiwear.isAbleSell = _isAbleSell;
         _antiwear.ticketId = _ticketId;
         _antiwear.isBinding = _isBinding;
         _antiwear.prefix = "";
         _antiwear.suffix = "";
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function clone() : EquipmentVO
      {
         var _loc1_:EquipmentVO = new EquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      protected function cloneAttribute(param1:EquipmentVO) : void
      {
         param1.id = this.id;
         param1.level = this.level;
         param1.name = this.name;
         param1.description = this.description;
         param1.ticketPrice = this.ticketPrice;
         param1.ticketId = this.ticketId;
         param1.owner = this.owner;
         param1.equipmentType = this.equipmentType;
         param1.className = this.className;
         param1.isAbleSell = this.isAbleSell;
         param1.messageBoxColor = this.messageBoxColor;
         param1.isAbleAKeySell = this.isAbleAKeySell;
         param1.isBinding = this.isBinding;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get level() : int
      {
         return _antiwear.level;
      }
      
      public function set level(param1:int) : void
      {
         _antiwear.level = param1;
      }
      
      public function get ticketPrice() : Number
      {
         return _antiwear.ticketPrice;
      }
      
      public function set ticketPrice(param1:Number) : void
      {
         _antiwear.ticketPrice = param1;
      }
      
      public function get price() : Number
      {
         return XMLSingle.getInstance().getEqupipmentXMLPartData().getEquipmentPrice(id.toString());
      }
      
      public function get pkPrice() : Number
      {
         return XMLSingle.getInstance().getEqupipmentXMLPartData().getEquipmentPKPrice(id.toString());
      }
      
      public function get isAbleSell() : Boolean
      {
         return _antiwear.isAbleSell;
      }
      
      public function set isAbleSell(param1:Boolean) : void
      {
         _antiwear.isAbleSell = param1;
      }
      
      public function get ticketId() : String
      {
         return _antiwear.ticketId;
      }
      
      public function set ticketId(param1:String) : void
      {
         _antiwear.ticketId = param1;
      }
      
      public function get isBinding() : Boolean
      {
         return _antiwear.isBinding;
      }
      
      public function set isBinding(param1:Boolean) : void
      {
         _antiwear.isBinding = param1;
      }
      
      public function get isPutInOperate() : Boolean
      {
         return _isPutInOperate;
      }
      
      public function set isPutInOperate(param1:Boolean) : void
      {
         _isPutInOperate = param1;
      }
      
      public function get isOverdue() : int
      {
         return _isOverdue;
      }
      
      public function set isOverdue(param1:int) : void
      {
         _isOverdue = param1;
      }
      
      public function get isShopItem() : Boolean
      {
         return _isShopItem;
      }
      
      public function set isShopItem(param1:Boolean) : void
      {
         _isShopItem = param1;
      }
      
      public function get playerVO() : PlayerVO
      {
         return _playerVO;
      }
      
      public function set playerVO(param1:PlayerVO) : void
      {
         _playerVO = param1;
      }
      
      public function getIsBinding() : Boolean
      {
         return isBinding;
      }
      
      public function get prefix() : String
      {
         return _antiwear.prefix;
      }
      
      public function set prefix(param1:String) : void
      {
         _antiwear.prefix = param1;
      }
      
      public function get suffix() : String
      {
         return _antiwear.suffix;
      }
      
      public function set suffix(param1:String) : void
      {
         _antiwear.suffix = param1;
      }
   }
}

