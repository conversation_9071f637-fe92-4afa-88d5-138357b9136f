package UI.SocietySystem.SeverLink.Test
{
   import UI.GamingUI;
   import UI.NicknameSystem.NicknameData;
   import UI.SocietySystem.SeverLink.InformationBody;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.PlayerDataOfApply;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.SocietyDataInList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_AgreeOrRefuseJoinDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ApplyForJoinDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_CancelApplyForjoin;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ChatInforUp;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_CreateSocietyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_LeaveTheSocietyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_LogInDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_LogOutDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_RemoveMember;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowApplyPlayerListDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowPlayerTheSocietyData;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowSocietyList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.UP_ShowTheSocietyMemberList;
   import UI.SocietySystem.SeverLink.SocketManager;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_AddConValueReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_ApplyJoinReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_CancelApplyJoinReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_CancelDissolveSocietyReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_ChangeAnnouncementReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_CreateSocietySuccess;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_DecConValueReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_DissolveSocietyReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_DissolveTheSociety_Broadcast;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_GetChatInfor;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_GetLogicServerReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_HaveNewMember;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_LeaveTheSocietySuccess;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_LogInSuccess;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_LogOut;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_PlayerListOfApply;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_PlayerTheSocietyData;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_RemoveMemberReturnToLeader;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_TheSocietyList;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_TheSocietyListOfApplyReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_DOWN_VerifyReturn;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_MemberDataInMemberList;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_PLayerDataOfApply;
   import UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest.TEST_SocietyDataInList;
   import YJFY.GameData;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class SocietySystemTestData
   {
      public var socketManager:SocketManager;
      
      private var m_dissolveSocietyInterval:uint;
      
      private var m_dissoelveTime:Number;
      
      private var m_clearHelper:ClearHelper;
      
      private var m_societyDatas:Array = [{
         "societyId":"1",
         "societyName":"僵尸帮1",
         "societyLevel":"10",
         "nowPlayerNum":"50",
         "maxPlayerNum":"200",
         "leaderUid":"123",
         "leaderIdx":"1",
         "leaderName":"僵尸王1号",
         "soiceytTotalConValue":"10000",
         "announcement":""
      },{
         "societyId":"2",
         "societyName":"僵尸帮2",
         "societyLevel":"9",
         "nowPlayerNum":"50",
         "maxPlayerNum":"200",
         "leaderUid":"123",
         "leaderName":"僵尸王2号",
         "soiceytTotalConValue":"9000",
         "announcement":""
      },{
         "societyId":"3",
         "societyName":"僵尸帮3",
         "societyLevel":"8",
         "nowPlayerNum":"50",
         "maxPlayerNum":"200",
         "leaderUid":"123",
         "leaderName":"僵尸王3号",
         "soiceytTotalConValue":"8000",
         "announcement":""
      },{
         "societyId":"4",
         "societyName":"僵尸帮4",
         "societyLevel":"7",
         "nowPlayerNum":"50",
         "maxPlayerNum":"100",
         "leaderUid":"123",
         "leaderName":"僵尸王4号",
         "soiceytTotalConValue":"7000",
         "announcement":""
      },{
         "societyId":"5",
         "societyName":"僵尸帮5",
         "societyLevel":"6",
         "nowPlayerNum":"50",
         "maxPlayerNum":"100",
         "leaderUid":"123",
         "leaderName":"僵尸王5号",
         "soiceytTotalConValue":"6000",
         "announcement":""
      },{
         "societyId":"6",
         "societyName":"僵尸帮6",
         "societyLevel":"5",
         "nowPlayerNum":"50",
         "maxPlayerNum":"100",
         "leaderUid":"123",
         "leaderName":"僵尸王6号",
         "soiceytTotalConValue":"5000",
         "announcement":""
      },{
         "societyId":"7",
         "societyName":"僵尸帮7",
         "societyLevel":"4",
         "nowPlayerNum":"50",
         "maxPlayerNum":"50",
         "leaderUid":"123",
         "leaderName":"僵尸王7号",
         "soiceytTotalConValue":"4000",
         "announcement":""
      },{
         "societyId":"8",
         "societyName":"僵尸帮8",
         "societyLevel":"3",
         "nowPlayerNum":"50",
         "maxPlayerNum":"50",
         "leaderUid":"123",
         "leaderName":"僵尸王8号",
         "soiceytTotalConValue":"3000",
         "announcement":""
      },{
         "societyId":"9",
         "societyName":"僵尸帮9",
         "societyLevel":"2",
         "nowPlayerNum":"50",
         "maxPlayerNum":"50",
         "leaderUid":"123",
         "leaderName":"僵尸王9号",
         "soiceytTotalConValue":"2000",
         "announcement":""
      },{
         "societyId":"10",
         "societyName":"僵尸帮10",
         "societyLevel":"1",
         "nowPlayerNum":"50",
         "maxPlayerNum":"50",
         "leaderUid":"123",
         "leaderName":"僵尸王10号",
         "soiceytTotalConValue":"1000",
         "announcement":""
      }];
      
      private var m_normalMembers:Array = [{
         "memberUid":"123",
         "memberIdx":"1",
         "memberName":"僵尸王",
         "memberLevel":"45",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"126",
         "memberIdx":"1",
         "memberName":"僵尸王1",
         "memberLevel":"45",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"122",
         "memberIdx":"1",
         "memberName":"僵尸王2",
         "memberLevel":"5",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"1256",
         "memberIdx":"1",
         "memberName":"僵尸王3",
         "memberLevel":"65",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"12455",
         "memberIdx":"1",
         "memberName":"僵尸王4",
         "memberLevel":"45",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"124545",
         "memberIdx":"1",
         "memberName":"僵尸王541",
         "memberLevel":"12",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"124654",
         "memberIdx":"1",
         "memberName":"",
         "memberLevel":"44",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"12244",
         "memberIdx":"1",
         "memberName":"",
         "memberLevel":"56",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"63565",
         "memberIdx":"1",
         "memberName":"",
         "memberLevel":"4",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"124153",
         "memberIdx":"1",
         "memberName":"僵尸王",
         "memberLevel":"32",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"564156",
         "memberIdx":"1",
         "memberName":"僵尸王65",
         "memberLevel":"65",
         "isOnLine":"0",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      },{
         "memberUid":"456",
         "memberIdx":"1",
         "memberName":"",
         "memberLevel":"45",
         "isOnLine":"1",
         "personalReConValue":"100",
         "personalTotalConValue":"200"
      }];
      
      private var m_applyPlayers:Array = [{
         "memberUid":"89894",
         "memberIdx":"1",
         "memberName":"僵尸王69",
         "memberLevel":"45"
      },{
         "memberUid":"111623",
         "memberIdx":"1",
         "memberName":"僵尸王66",
         "memberLevel":"42"
      },{
         "memberUid":"11123",
         "memberIdx":"1",
         "memberName":"僵尸王8",
         "memberLevel":"45"
      },{
         "memberUid":"1165123",
         "memberIdx":"1",
         "memberName":"僵尸王22",
         "memberLevel":"43"
      },{
         "memberUid":"1216543",
         "memberIdx":"1",
         "memberName":"僵尸王77",
         "memberLevel":"45"
      }];
      
      private var m_societyMemberDatas:Object;
      
      private var m_mySocietyId:int;
      
      public function SocietySystemTestData()
      {
         super();
         m_clearHelper = new ClearHelper();
         m_societyMemberDatas = {};
         m_societyMemberDatas["1"] = m_normalMembers.slice(0);
         m_societyMemberDatas["2"] = m_normalMembers.slice(0);
         m_societyMemberDatas["3"] = m_normalMembers.slice(0);
         m_societyMemberDatas["4"] = m_normalMembers.slice(0);
         m_societyMemberDatas["5"] = m_normalMembers.slice(0);
         m_societyMemberDatas["6"] = m_normalMembers.slice(0);
         m_societyMemberDatas["7"] = m_normalMembers.slice(0);
         m_societyMemberDatas["8"] = m_normalMembers.slice(0);
         m_societyMemberDatas["9"] = m_normalMembers.slice(0);
         m_societyMemberDatas["10"] = m_normalMembers.slice(0);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clearHelper);
         m_clearHelper = null;
         ClearUtil.nullArr(m_societyDatas);
         m_societyDatas = null;
         ClearUtil.nullArr(m_normalMembers);
         m_normalMembers = null;
         ClearUtil.nullObject(m_societyMemberDatas);
         m_societyMemberDatas = null;
         clearTimeout(m_dissolveSocietyInterval);
         m_dissolveSocietyInterval = 0;
         socketManager = null;
      }
      
      public function createDownInformationByUp(param1:InformationBody) : InformationBody
      {
         var _loc57_:int = 0;
         var _loc42_:int = 0;
         var _loc41_:UP_LogInDetail = null;
         var _loc10_:TEST_DOWN_LogInSuccess = null;
         var _loc37_:TEST_DOWN_GetLogicServerReturn = null;
         var _loc19_:UP_LogOutDetail = null;
         var _loc25_:TEST_DOWN_LogOut = null;
         var _loc49_:UP_ShowSocietyList = null;
         var _loc46_:TEST_DOWN_TheSocietyList = null;
         var _loc15_:* = undefined;
         var _loc3_:TEST_SocietyDataInList = null;
         var _loc9_:UP_ShowPlayerTheSocietyData = null;
         var _loc32_:TEST_DOWN_PlayerTheSocietyData = null;
         var _loc55_:Object = null;
         var _loc51_:Object = null;
         var _loc43_:int = 0;
         var _loc13_:UP_ShowTheSocietyMemberList = null;
         var _loc48_:TEST_DOWN_TheSocietyMemberList = null;
         var _loc8_:int = 0;
         var _loc45_:Array = null;
         var _loc62_:TEST_MemberDataInMemberList = null;
         var _loc35_:* = undefined;
         var _loc21_:UP_LeaveTheSocietyDetail = null;
         var _loc56_:TEST_DOWN_LeaveTheSocietySuccess = null;
         var _loc26_:UP_ApplyForJoinDetail = null;
         var _loc39_:TEST_DOWN_ApplyJoinReturn = null;
         var _loc23_:UP_AgreeOrRefuseJoinDetail = null;
         var _loc60_:String = null;
         var _loc38_:Object = null;
         var _loc50_:TEST_DOWN_HaveNewMember = null;
         var _loc11_:UP_ShowApplyPlayerListDetail = null;
         var _loc14_:TEST_PLayerDataOfApply = null;
         var _loc6_:* = undefined;
         var _loc7_:TEST_DOWN_PlayerListOfApply = null;
         var _loc4_:UP_CreateSocietyDetail = null;
         var _loc59_:Object = null;
         var _loc31_:Object = null;
         var _loc61_:TEST_DOWN_CreateSocietySuccess = null;
         var _loc36_:TEST_DOWN_VerifyReturn = null;
         var _loc28_:TEST_DOWN_ChangeAnnouncementReturn = null;
         var _loc33_:TEST_DOWN_LeaveTheSocietySuccess = null;
         var _loc24_:TEST_DOWN_DissolveSocietyReturn = null;
         var _loc54_:TEST_DOWN_CancelDissolveSocietyReturn = null;
         var _loc58_:UP_RemoveMember = null;
         var _loc40_:Array = null;
         var _loc2_:Number = NaN;
         var _loc47_:int = 0;
         var _loc52_:TEST_DOWN_RemoveMemberReturnToLeader = null;
         var _loc29_:TEST_DOWN_TheSocietyListOfApplyReturn = null;
         var _loc34_:UP_CancelApplyForjoin = null;
         var _loc5_:TEST_DOWN_CancelApplyJoinReturn = null;
         var _loc20_:TEST_DOWN_AddConValueReturn = null;
         var _loc18_:TEST_DOWN_DecConValueReturn = null;
         var _loc30_:UP_ChatInforUp = null;
         var _loc17_:String = null;
         var _loc22_:int = 0;
         var _loc44_:TEST_DOWN_GetChatInfor = null;
         var _loc16_:String = null;
         var _loc27_:InformationBody = new InformationBody();
         switch(param1.id_informationBody)
         {
            case 3000:
               _loc41_ = param1.getDetail() as UP_LogInDetail;
               _loc10_ = new TEST_DOWN_LogInSuccess();
               _loc10_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
               _loc27_.id_informationBody = 3001;
               _loc27_.setDetail(_loc10_);
               break;
            case 3044:
               _loc37_ = new TEST_DOWN_GetLogicServerReturn();
               _loc37_.initData();
               _loc27_.id_informationBody = 3045;
               _loc27_.setDetail(_loc37_);
               break;
            case 3002:
               _loc19_ = param1.getDetail() as UP_LogOutDetail;
               _loc25_ = new TEST_DOWN_LogOut();
               _loc25_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index);
               _loc27_.id_informationBody = 3108;
               _loc27_.setDetail(_loc25_);
               break;
            case 3004:
               _loc49_ = param1.getDetail() as UP_ShowSocietyList;
               _loc46_ = new TEST_DOWN_TheSocietyList();
               _loc15_ = new Vector.<SocietyDataInList>();
               _loc57_ = _loc49_.getPageIndex() * _loc49_.getNum();
               _loc42_ = _loc57_ + _loc49_.getNum();
               while(_loc57_ < _loc42_ && _loc57_ < m_societyDatas.length)
               {
                  _loc3_ = new TEST_SocietyDataInList();
                  _loc3_.initData(m_societyDatas[_loc57_]["societyId"],m_societyDatas[_loc57_]["societyName"],m_societyDatas[_loc57_]["societyLevel"],m_societyDatas[_loc57_]["nowPlayerNum"],m_societyDatas[_loc57_]["maxPlayerNum"],m_societyDatas[_loc57_]["leaderUid"],m_societyDatas[_loc57_]["leaderIdx"],m_societyDatas[_loc57_]["leaderName"],m_societyDatas[_loc57_]["soiceytTotalConValue"],m_societyDatas[_loc57_]["announcement"]);
                  _loc15_.push(_loc3_);
                  _loc57_++;
               }
               _loc46_.initData(m_societyDatas.length,_loc15_);
               _loc27_.id_informationBody = 3005;
               _loc27_.setDetail(_loc46_);
               break;
            case 3006:
               _loc9_ = param1.getDetail() as UP_ShowPlayerTheSocietyData;
               _loc32_ = new TEST_DOWN_PlayerTheSocietyData();
               if(m_mySocietyId)
               {
                  _loc42_ = int(m_societyDatas.length);
                  _loc57_ = 0;
                  while(_loc57_ < _loc42_)
                  {
                     if(int(m_societyDatas[_loc57_]["societyId"]) == m_mySocietyId)
                     {
                        _loc55_ = m_societyDatas[_loc57_];
                        break;
                     }
                     _loc57_++;
                  }
                  _loc42_ = int(m_societyMemberDatas[m_mySocietyId].length);
                  _loc57_ = 0;
                  while(_loc57_ < _loc42_)
                  {
                     if(m_societyMemberDatas[m_mySocietyId][_loc57_]["memberUid"] == GameData.getInstance().getLoginReturnData().getUid())
                     {
                        _loc51_ = m_societyMemberDatas[m_mySocietyId][_loc57_];
                        break;
                     }
                     _loc57_++;
                  }
                  if(isNaN(m_dissoelveTime) || m_dissoelveTime == 0)
                  {
                     _loc43_ = 0;
                  }
                  else
                  {
                     _loc43_ = m_dissoelveTime - GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
                  }
                  _loc32_.initData(_loc55_["societyId"],_loc55_["societyName"],_loc51_["personalTotalConValue"],_loc51_["personalReConValue"],_loc55_["societyLevel"],_loc55_["nowPlayerNum"],_loc55_["maxPlayerNum"],_loc55_["leaderUid"],_loc55_["leaderIdx"],_loc55_["leaderName"],_loc55_["soiceytTotalConValue"],_loc55_["announcement"],_loc43_);
               }
               else
               {
                  _loc32_.initData(0,null,0,0,0,0,0,0,0,null,0,null,0);
               }
               _loc27_.id_informationBody = 3007;
               _loc27_.setDetail(_loc32_);
               break;
            case 3008:
               _loc13_ = param1.getDetail() as UP_ShowTheSocietyMemberList;
               _loc48_ = new TEST_DOWN_TheSocietyMemberList();
               _loc8_ = _loc13_.getSocietyId();
               _loc45_ = m_societyMemberDatas[_loc8_];
               _loc57_ = _loc13_.getPageIndex() * _loc13_.getNum();
               _loc42_ = _loc57_ + _loc13_.getNum();
               _loc35_ = new Vector.<MemberDataInMemberList>();
               while(_loc57_ < _loc42_ && _loc57_ < _loc45_.length)
               {
                  _loc62_ = new TEST_MemberDataInMemberList();
                  _loc62_.initData(_loc45_[_loc57_]["memberUid"],_loc45_[_loc57_]["memberIdx"],_loc45_[_loc57_]["memberName"],_loc45_[_loc57_]["memberLevel"],_loc45_[_loc57_]["isOnLine"],_loc45_[_loc57_]["personalReConValue"],_loc45_[_loc57_]["personalTotalConValue"],_loc57_ + 1);
                  _loc35_.push(_loc62_);
                  _loc57_++;
               }
               _loc48_.initData(_loc45_.length,_loc35_);
               _loc27_.id_informationBody = 3009;
               _loc27_.setDetail(_loc48_);
               break;
            case 3010:
               _loc21_ = param1.getDetail() as UP_LeaveTheSocietyDetail;
               _loc56_ = new TEST_DOWN_LeaveTheSocietySuccess();
               _loc56_.initData(true);
               _loc27_.id_informationBody = 3011;
               _loc27_.setDetail(_loc56_);
               break;
            case 3012:
               _loc26_ = param1.getDetail() as UP_ApplyForJoinDetail;
               _loc39_ = new TEST_DOWN_ApplyJoinReturn();
               _loc39_.initData(1,_loc26_.getSocietyId());
               _loc27_.id_informationBody = 3013;
               _loc27_.setDetail(_loc39_);
               break;
            case 3015:
               _loc23_ = param1.getDetail() as UP_AgreeOrRefuseJoinDetail;
               _loc60_ = _loc23_.getUidApplicant().toString();
               _loc42_ = int(m_applyPlayers.length);
               _loc57_ = 0;
               while(_loc57_ < _loc42_)
               {
                  if(m_applyPlayers[_loc57_]["memberUid"] == _loc60_)
                  {
                     _loc38_ = m_applyPlayers[_loc57_];
                     m_applyPlayers.splice(_loc57_,1);
                     break;
                  }
                  _loc57_++;
               }
               if(_loc38_ == null)
               {
                  return null;
               }
               _loc38_["isOnLine"] = "1";
               _loc38_["isOnLine"] = "0";
               _loc38_["isOnLine"] = "0";
               if(_loc23_.getIsAgree() == 0)
               {
                  return null;
               }
               m_societyMemberDatas["11"].push(_loc38_);
               _loc50_ = new TEST_DOWN_HaveNewMember();
               _loc27_.id_informationBody = 3021;
               _loc27_.setDetail(_loc50_);
               break;
            case 3019:
               _loc11_ = param1.getDetail() as UP_ShowApplyPlayerListDetail;
               _loc42_ = !!m_applyPlayers ? m_applyPlayers.length : 0;
               _loc6_ = new Vector.<PlayerDataOfApply>();
               _loc57_ = 0;
               while(_loc57_ < _loc42_)
               {
                  _loc14_ = new TEST_PLayerDataOfApply();
                  _loc14_.initData(m_applyPlayers[_loc57_]["memberUid"],m_applyPlayers[_loc57_]["memberIdx"],m_applyPlayers[_loc57_]["memberName"],m_applyPlayers[_loc57_]["memberLevel"]);
                  _loc6_.push(_loc14_);
                  _loc57_++;
               }
               _loc7_ = new TEST_DOWN_PlayerListOfApply();
               _loc7_.initData(_loc6_.length,_loc6_);
               _loc27_.id_informationBody = 3020;
               _loc27_.setDetail(_loc7_);
               break;
            case 3022:
               _loc4_ = param1.getDetail() as UP_CreateSocietyDetail;
               _loc59_ = {
                  "societyId":"11",
                  "societyName":_loc4_.getSocietyName(),
                  "societyLevel":"3",
                  "nowPlayerNum":"1",
                  "maxPlayerNum":"50",
                  "leaderUid":GameData.getInstance().getLoginReturnData().getUid(),
                  "leaderIdx":GameData.getInstance().getSaveFileData().index,
                  "leaderName":"",
                  "soiceytTotalConValue":"0",
                  "announcement":""
               };
               m_societyDatas.push(_loc59_);
               _loc31_ = {
                  "memberUid":GameData.getInstance().getLoginReturnData().getUid(),
                  "memberIdx":GameData.getInstance().getSaveFileData().index,
                  "memberName":"",
                  "memberLevel":GamingUI.getInstance().player1.playerVO.level,
                  "isOnLine":"1",
                  "personalReConValue":"0",
                  "personalTotalConValue":"0"
               };
               m_societyMemberDatas["11"] = [_loc31_];
               _loc57_ = 0;
               while(_loc57_ < m_normalMembers.length)
               {
                  m_societyMemberDatas["11"].push(m_normalMembers[_loc57_]);
                  _loc57_++;
               }
               m_mySocietyId = 11;
               _loc61_ = new TEST_DOWN_CreateSocietySuccess();
               _loc61_.initData(true);
               _loc27_.id_informationBody = 3023;
               _loc27_.setDetail(_loc61_);
               break;
            case 3003:
               break;
            case 2000:
               _loc36_ = new TEST_DOWN_VerifyReturn();
               _loc36_.initData(1,0,0);
               _loc27_.id_informationBody = 2001;
               _loc27_.setDetail(_loc36_);
               break;
            case 3031:
               _loc28_ = new TEST_DOWN_ChangeAnnouncementReturn();
               _loc28_.initData(true);
               _loc27_.id_informationBody = 3032;
               _loc27_.setDetail(_loc28_);
               break;
            case 3010:
               _loc33_ = new TEST_DOWN_LeaveTheSocietySuccess();
               _loc33_.initData(true);
               _loc27_.id_informationBody = 3011;
               _loc27_.setDetail(_loc33_);
               break;
            case 3024:
               m_dissoelveTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + 60000;
               m_dissolveSocietyInterval = setTimeout(downDissolveSoceityInfor,60000);
               _loc24_ = new TEST_DOWN_DissolveSocietyReturn();
               _loc24_.initData(true);
               _loc27_.id_informationBody = 3025;
               _loc27_.setDetail(_loc24_);
               break;
            case 3038:
               clearTimeout(m_dissolveSocietyInterval);
               m_dissolveSocietyInterval = 0;
               m_dissoelveTime = NaN;
               _loc54_ = new TEST_DOWN_CancelDissolveSocietyReturn();
               _loc54_.initData(true);
               _loc27_.id_informationBody = 3039;
               _loc27_.setDetail(_loc54_);
               break;
            case 3033:
               _loc58_ = param1.getDetail() as UP_RemoveMember;
               _loc40_ = m_societyMemberDatas["11"];
               _loc42_ = !!_loc40_ ? _loc40_.length : 0;
               _loc57_ = 0;
               while(_loc57_ < _loc42_)
               {
                  if(_loc40_[_loc57_]["memberUid"] == _loc58_.getUidMember() && _loc40_[_loc57_]["memberIdx"] == _loc58_.getIdxMember())
                  {
                     _loc2_ = Number(_loc40_[_loc57_]["memberUid"]);
                     _loc47_ = int(_loc40_[_loc57_]["memberIdx"]);
                     _loc40_.splice(_loc57_,1);
                     break;
                  }
                  _loc57_++;
               }
               _loc52_ = new TEST_DOWN_RemoveMemberReturnToLeader();
               _loc52_.initData(true,_loc2_,_loc47_);
               _loc27_.id_informationBody = 3034;
               _loc27_.setDetail(_loc52_);
               break;
            case 3036:
               _loc29_ = new TEST_DOWN_TheSocietyListOfApplyReturn();
               _loc29_.initData(0,null);
               _loc27_.id_informationBody = 3037;
               _loc27_.setDetail(_loc29_);
               break;
            case 3017:
               _loc34_ = param1.getDetail() as UP_CancelApplyForjoin;
               _loc5_ = new TEST_DOWN_CancelApplyJoinReturn();
               _loc5_.initdata(1,_loc34_.getSocietyId());
               _loc27_.id_informationBody = 3018;
               _loc27_.setDetail(_loc5_);
               break;
            case 3027:
               _loc20_ = new TEST_DOWN_AddConValueReturn();
               _loc20_.initData(1);
               _loc27_.id_informationBody = 3028;
               _loc27_.setDetail(_loc20_);
               break;
            case 3029:
               _loc18_ = new TEST_DOWN_DecConValueReturn();
               _loc18_.initData(1);
               _loc27_.id_informationBody = 3030;
               _loc27_.setDetail(_loc18_);
               break;
            case 3042:
               _loc30_ = param1.getDetail() as UP_ChatInforUp;
               _loc17_ = _loc30_.getChatStr();
               _loc22_ = _loc30_.getExtraData();
               _loc44_ = new TEST_DOWN_GetChatInfor();
               _loc16_ = !!NicknameData.getInstance().myDataInNicknameRankList ? NicknameData.getInstance().myDataInNicknameRankList.extra : "";
               _loc44_.initData(Number(GameData.getInstance().getLoginReturnData().getUid()),GameData.getInstance().getSaveFileData().index,_loc16_,_loc17_,_loc22_);
               _loc27_.id_informationBody = 3043;
               _loc27_.setDetail(_loc44_);
         }
         return _loc27_;
      }
      
      private function downDissolveSoceityInfor() : void
      {
         clearTimeout(m_dissolveSocietyInterval);
         m_dissolveSocietyInterval = 0;
         m_dissoelveTime = NaN;
         var _loc2_:InformationBody = new InformationBody();
         var _loc1_:TEST_DOWN_DissolveTheSociety_Broadcast = new TEST_DOWN_DissolveTheSociety_Broadcast();
         _loc2_.id_informationBody = 3026;
         _loc2_.setDetail(_loc1_);
         socketManager.testDownInformationBody(_loc2_);
      }
   }
}

