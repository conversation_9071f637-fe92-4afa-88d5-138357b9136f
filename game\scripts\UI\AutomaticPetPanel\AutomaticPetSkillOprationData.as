package UI.AutomaticPetPanel
{
   import UI.DataManagerParent;
   import UI.Other.NeedEquipmentsData;
   import UI.Players.PlayerVO;
   import YJFY.Utils.ClearUtil;
   
   public class AutomaticPetSkillOprationData extends DataManagerParent
   {
      private var m_needEquipmentsData:NeedEquipmentsData;
      
      private var m_successRate:Number;
      
      private var m_needMoney:uint;
      
      public function AutomaticPetSkillOprationData()
      {
         super();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_needEquipmentsData);
         m_needEquipmentsData = null;
         super.clear();
      }
      
      public function setData(param1:NeedEquipmentsData, param2:Number, param3:uint) : void
      {
         ClearUtil.clearObject(m_needEquipmentsData);
         m_needEquipmentsData = param1;
         this.successRate = param2;
         this.needMoney = param3;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.successRate = m_successRate;
         _antiwear.needMoney = m_needMoney;
      }
      
      public function getSuccessRate() : Number
      {
         return successRate;
      }
      
      public function getNeedMoney() : uint
      {
         return needMoney;
      }
      
      public function getNeedEquipmentsData() : NeedEquipmentsData
      {
         return m_needEquipmentsData;
      }
      
      public function getIsEnoughForNeedEquipments() : Boolean
      {
         return m_needEquipmentsData.getIsEnough();
      }
      
      public function getIsEnoughForMoney(param1:PlayerVO, param2:PlayerVO) : Boolean
      {
         var _loc3_:uint = uint(param1.money + (!!param2 ? param2.money : 0));
         if(_loc3_ < m_needMoney)
         {
            return false;
         }
         return true;
      }
      
      public function minusNeedEquipmentsAndMoneyFromPlayer(param1:PlayerVO, param2:PlayerVO) : void
      {
         m_needEquipmentsData.minusNeedEquipmentsFromPlayer(param1,param2);
         var _loc3_:uint = Math.max(0,m_needMoney - param1.money);
         param1.money = Math.max(0,param1.money - m_needMoney);
         if(_loc3_)
         {
            if(param2.money < _loc3_)
            {
               throw new Error("出错了");
            }
            param2.money -= _loc3_;
         }
      }
      
      private function get successRate() : Number
      {
         return _antiwear.successRate;
      }
      
      private function set successRate(param1:Number) : void
      {
         _antiwear.successRate = param1;
      }
      
      private function get needMoney() : uint
      {
         return _antiwear.needMoney;
      }
      
      private function set needMoney(param1:uint) : void
      {
         _antiwear.needMoney = param1;
      }
   }
}

