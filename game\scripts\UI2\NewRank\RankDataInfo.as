package UI2.NewRank
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class RankDataInfo extends DataManagerParent
   {
      private static var _instance:RankDataInfo;
      
      private var m_list:Vector.<ListItem>;
      
      private var m_allList:Vector.<ItemAll>;
      
      private var m_recordlist:Vector.<RecordInfo>;
      
      private var m_curRank:int;
      
      private var m_curFightRank:int;
      
      private var m_preRank:int;
      
      private var m_maxRank:int;
      
      private var m_yestRank:int;
      
      private var m_nremainnum1:int;
      
      private var m_nremainnum2:int;
      
      private var m_totalnum:int;
      
      private var m_money:Number;
      
      private var m_priceid:Number;
      
      private var m_ntype:int;
      
      private var m_nSid:Number;
      
      public var m_rewardlist:Vector.<NewRankItem>;
      
      public var m_rewardrole:Vector.<NewRankItem>;
      
      private var m_showrank:int;
      
      private var m_showplayer:String;
      
      private var m_curFightSid:Number;
      
      private var m_mySid:Number;
      
      private var m_myuid:String;
      
      private var m_myidx:int;
      
      private var m_foeuid:String;
      
      private var m_foeidx:int;
      
      private var m_get:Boolean;
      
      private var m_pageindex:int;
      
      private var m_allpage:int;
      
      private var m_fightType:int = 2;
      
      private var m_win:Boolean;
      
      private var m_mydata:ListItem;
      
      private var m_intype:int;
      
      private var dataxml:XML;
      
      private var m_year:int;
      
      private var m_month:int;
      
      private var m_day:int;
      
      public function RankDataInfo()
      {
         super();
         m_list = new Vector.<ListItem>();
         m_recordlist = new Vector.<RecordInfo>();
         m_rewardlist = new Vector.<NewRankItem>();
         m_rewardrole = new Vector.<NewRankItem>();
         m_allList = new Vector.<ItemAll>();
         m_ntype = 1;
         initXML(XMLSingle.getInstance().NewRank);
      }
      
      public static function getInstance() : RankDataInfo
      {
         if(_instance == null)
         {
            _instance = new RankDataInfo();
         }
         return _instance;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear._curRank = m_curRank;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_list);
         m_list = null;
         ClearUtil.clearObject(m_recordlist);
         m_recordlist = null;
         ClearUtil.clearObject(m_allList);
         m_allList = null;
         ClearUtil.clearObject(m_rewardrole);
         m_rewardrole = null;
         super.clear();
      }
      
      public function getNowRankId() : int
      {
         return m_showrank;
      }
      
      public function setNowRankId(param1:int) : void
      {
         m_showrank = param1;
      }
      
      public function getNowPlayerType() : String
      {
         return m_showplayer;
      }
      
      public function setNowPlayerType(param1:String) : void
      {
         m_showplayer = param1;
      }
      
      public function getFightSid() : Number
      {
         return m_curFightSid;
      }
      
      public function setFightSid(param1:Number) : void
      {
         m_curFightSid = param1;
         m_foeuid = String(uint(m_curFightSid / 100));
         m_foeidx = m_curFightSid / 10 % 10;
      }
      
      public function getMySid() : Number
      {
         return m_mySid;
      }
      
      public function setMySid(param1:Number) : void
      {
         m_mySid = param1;
         m_myuid = String(uint(m_mySid / 100));
         m_myidx = m_mySid / 10 % 10;
      }
      
      public function getMyUid() : String
      {
         return m_myuid;
      }
      
      public function getMyIdx() : int
      {
         return m_myidx;
      }
      
      public function getFoeUid() : String
      {
         return m_foeuid;
      }
      
      public function getFoeIdx() : int
      {
         return m_foeidx;
      }
      
      public function getInType() : int
      {
         return m_intype;
      }
      
      public function setInType(param1:int) : void
      {
         m_intype = param1;
      }
      
      public function initRecord(param1:Object) : void
      {
         var _loc2_:RecordInfo = null;
         var _loc4_:int = 0;
         ClearUtil.clearObject(m_recordlist);
         m_recordlist.length = 0;
         if(param1.Records == null)
         {
            return;
         }
         var _loc3_:int = int(param1.Records.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = new RecordInfo();
            _loc2_.m_year = int(param1.Records[_loc4_].Year);
            _loc2_.m_month = int(param1.Records[_loc4_].Mon);
            _loc2_.m_day = int(param1.Records[_loc4_].Day);
            _loc2_.m_oldrank = int(param1.Records[_loc4_].OldRank);
            _loc2_.m_newrank = int(param1.Records[_loc4_].NewRank);
            _loc2_.m_mysid = Number(param1.Records[_loc4_].Sid);
            _loc2_.m_forsid = Number(param1.Records[_loc4_].Other);
            _loc2_.m_challenge = Boolean(param1.Records[_loc4_].Challenge);
            _loc2_.m_myname = String(param1.Records[_loc4_].Name);
            _loc2_.m_foename = String(param1.Records[_loc4_].OtherName);
            if(param1.Records[_loc4_].Win)
            {
               _loc2_.m_win = 1;
            }
            else
            {
               _loc2_.m_win = 2;
            }
            m_recordlist.push(_loc2_);
            _loc4_++;
         }
         if(param1.BestRank != null || int(param1.BestRank) != 0)
         {
            m_maxRank = int(param1.BestRank);
         }
      }
      
      public function getRecordslen() : int
      {
         return m_recordlist.length;
      }
      
      public function getRecordByIndex(param1:int) : RecordInfo
      {
         if(param1 >= 0 && param1 < m_recordlist.length)
         {
            return m_recordlist[param1];
         }
         return null;
      }
      
      public function initNoMy(param1:Object) : void
      {
         var _loc2_:ListItem = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         ClearUtil.clearObject(m_list);
         m_list.length = 0;
         if(param1.Ranks != null)
         {
            _loc3_ = int(param1.Ranks.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               _loc2_ = new ListItem();
               _loc2_.m_name = String(param1.Ranks[_loc4_].Name);
               _loc2_.m_role = String(param1.Ranks[_loc4_].Role);
               _loc2_.m_sid = Number(param1.Ranks[_loc4_].Sid);
               _loc2_.m_rank = _loc4_ + 1;
               _loc2_.m_isMy = 3;
               m_list.push(_loc2_);
               _loc4_++;
            }
            if(m_list != null && m_list.length >= 2)
            {
               sortrank();
            }
         }
      }
      
      public function initData(param1:Object) : void
      {
         var _loc7_:ListItem = null;
         var _loc8_:ListItem = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc9_:int = 0;
         var _loc2_:int = 0;
         ClearUtil.clearObject(m_list);
         m_list.length = 0;
         var _loc10_:int = 0;
         var _loc4_:Boolean = false;
         m_curRank = int(param1.Rank);
         m_maxRank = int(param1.BestRank);
         m_yestRank = int(param1.YestRank);
         m_get = Boolean(param1.Prized);
         if(param1.TOP10 != null)
         {
            _loc5_ = int(param1.TOP10.length);
            _loc10_ = 0;
            while(_loc10_ < _loc5_)
            {
               _loc7_ = new ListItem();
               _loc7_.m_name = String(param1.TOP10[_loc10_].Name);
               _loc7_.m_role = String(param1.TOP10[_loc10_].Role);
               _loc7_.m_sid = Number(param1.TOP10[_loc10_].Sid);
               _loc7_.m_rank = _loc10_ + 1;
               _loc7_.m_isMy = 2;
               _loc7_.m_isFight = 2;
               m_list.push(_loc7_);
               _loc10_++;
            }
         }
         if(param1.Enemy != null)
         {
            _loc6_ = int(param1.Enemy.length);
            _loc10_ = 0;
            while(_loc10_ < _loc6_)
            {
               _loc4_ = false;
               _loc3_ = 0;
               while(_loc3_ < m_list.length)
               {
                  if(Number(param1.Enemy[_loc10_].Sid) == m_list[_loc3_].m_sid)
                  {
                     _loc4_ = true;
                  }
                  _loc3_++;
               }
               if(_loc4_ == false)
               {
                  _loc8_ = new ListItem();
                  _loc8_.m_name = String(param1.Enemy[_loc10_].Name);
                  _loc8_.m_role = String(param1.Enemy[_loc10_].Role);
                  _loc8_.m_sid = Number(param1.Enemy[_loc10_].Sid);
                  _loc8_.m_rank = int(param1.EnemyRank[_loc10_]);
                  _loc8_.m_isFight = 2;
                  _loc8_.m_isMy = 2;
                  m_list.push(_loc8_);
               }
               _loc10_++;
            }
         }
         if(param1.Self != null)
         {
            _loc4_ = false;
            _loc10_ = 0;
            while(_loc10_ < m_list.length)
            {
               if(Number(param1.Self.Sid) == m_list[_loc10_].m_sid)
               {
                  m_list[_loc10_].m_isMy = 1;
                  _loc4_ = true;
                  m_mydata = m_list[_loc10_];
               }
               _loc10_++;
            }
            if(_loc4_ == false)
            {
               _loc7_ = new ListItem();
               _loc7_.m_name = String(param1.Self.Name);
               _loc7_.m_role = String(param1.Self.Role);
               _loc7_.m_sid = Number(param1.Self.Sid);
               _loc7_.m_rank = int(param1.Rank);
               _loc7_.m_isMy = 1;
               m_list.push(_loc7_);
               m_nSid = Number(param1.Self.Sid);
               m_mydata = _loc7_;
            }
         }
         if(param1.Enemy != null)
         {
            _loc10_ = 0;
            while(_loc10_ < m_list.length)
            {
               _loc9_ = int(param1.Enemy.length);
               _loc2_ = 0;
               while(_loc2_ < _loc9_)
               {
                  if(m_list[_loc10_].m_sid == Number(param1.Enemy[_loc2_].Sid))
                  {
                     m_list[_loc10_].m_isFight = 1;
                  }
                  _loc2_++;
               }
               _loc10_++;
            }
         }
         if(m_list != null && m_list.length >= 2)
         {
            sortrank();
         }
      }
      
      public function getCurData() : ListItem
      {
         return m_mydata;
      }
      
      public function sortrank() : void
      {
         m_list.sort(function(param1:ListItem, param2:ListItem):int
         {
            if(param1.m_rank > param2.m_rank)
            {
               return 1;
            }
            if(param1.m_rank < param2.m_rank)
            {
               return -1;
            }
            return 0;
         });
      }
      
      public function initXML(param1:XML) : void
      {
         var _loc5_:NewRankItem = null;
         var _loc10_:* = null;
         var _loc2_:XMLList = null;
         var _loc6_:XMLList = null;
         ClearUtil.clearObject(m_rewardlist);
         m_rewardlist = null;
         ClearUtil.clearObject(m_rewardrole);
         m_rewardrole = null;
         m_rewardlist = new Vector.<NewRankItem>();
         m_rewardrole = new Vector.<NewRankItem>();
         m_totalnum = int(param1.fightnum.@total);
         m_money = Number(param1.money.@value);
         m_priceid = Number(param1.money.@ticketPriceId);
         var _loc8_:XMLList = param1.RankList.children();
         for each(_loc10_ in _loc8_)
         {
            _loc5_ = new NewRankItem();
            _loc5_.gradeid = int(_loc10_.@gradeid);
            _loc5_.maxrank = int(_loc10_.@max);
            _loc5_.minrank = int(_loc10_.@min);
            _loc2_ = param1.rewards.children();
            for each(var _loc4_ in _loc2_)
            {
               if(_loc5_.gradeid == int(_loc4_.@gradeid))
               {
                  _loc5_.itemlist = XMLSingle.getEquipmentVOs(_loc4_,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
               }
            }
            m_rewardlist.push(_loc5_);
         }
         var _loc9_:XMLList = param1.RankListRole.children();
         for each(_loc10_ in _loc9_)
         {
            _loc6_ = param1.rewardsrole.children();
            for each(var _loc3_ in _loc6_)
            {
               if(int(_loc10_.@gradeid) == int(_loc3_.@gradeid))
               {
                  _loc5_ = new NewRankItem();
                  _loc5_.gradeid = int(_loc10_.@gradeid);
                  _loc5_.maxrank = int(_loc10_.@max);
                  _loc5_.minrank = int(_loc10_.@min);
                  _loc5_.role = String(_loc3_.@role);
                  _loc5_.itemlist = XMLSingle.getEquipmentVOs(_loc3_,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
                  m_rewardrole.push(_loc5_);
               }
            }
         }
      }
      
      public function getprice() : int
      {
         return int(m_money);
      }
      
      public function getpriceid() : int
      {
         return int(m_priceid);
      }
      
      public function getRewardList() : NewRankItem
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_rewardlist.length)
         {
            if(m_rewardlist[_loc2_].minrank <= m_yestRank && m_yestRank <= m_rewardlist[_loc2_].maxrank)
            {
               return m_rewardlist[_loc2_];
            }
            _loc2_++;
         }
         return null;
      }
      
      public function getYestGrade() : String
      {
         var _loc3_:int = 0;
         _loc3_ = 0;
         var _loc2_:String = null;
         var _loc1_:NewRankItem = null;
         if(m_showrank == 1)
         {
            _loc3_ = 0;
            while(_loc3_ < m_rewardlist.length)
            {
               if(m_rewardlist[_loc3_].minrank <= m_yestRank && m_yestRank <= m_rewardlist[_loc3_].maxrank)
               {
                  _loc1_ = m_rewardlist[_loc3_];
               }
               _loc3_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < m_rewardrole.length)
            {
               if(m_rewardrole[_loc3_].minrank <= m_yestRank && m_yestRank <= m_rewardrole[_loc3_].maxrank)
               {
                  _loc1_ = m_rewardrole[_loc3_];
               }
               _loc3_++;
            }
         }
         if(_loc1_ != null)
         {
            if(_loc1_.gradeid == 1)
            {
               _loc2_ = "王者";
            }
            else if(_loc1_.gradeid == 2)
            {
               _loc2_ = "大师";
            }
            else if(_loc1_.gradeid == 3)
            {
               _loc2_ = "钻石";
            }
            else if(_loc1_.gradeid == 4)
            {
               _loc2_ = "白金";
            }
            else if(_loc1_.gradeid == 5)
            {
               _loc2_ = "黄金";
            }
            else if(_loc1_.gradeid == 6)
            {
               _loc2_ = "白银";
            }
            else if(_loc1_.gradeid == 7)
            {
               _loc2_ = "青铜";
            }
            else if(_loc1_.gradeid == 8)
            {
               _loc2_ = "黑铁";
            }
         }
         return _loc2_;
      }
      
      public function getNoReward() : NewRankItem
      {
         if(m_rewardlist.length >= 1)
         {
            return m_rewardlist[m_rewardlist.length - 1];
         }
         return null;
      }
      
      public function getRewarRoleList(param1:String) : NewRankItem
      {
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_rewardrole.length)
         {
            if(m_rewardrole[_loc3_].minrank <= m_yestRank && m_yestRank <= m_rewardrole[_loc3_].maxrank && param1 == m_rewardrole[_loc3_].role)
            {
               return m_rewardrole[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function getNoRewardRoleList(param1:String) : NewRankItem
      {
         var _loc2_:int = 0;
         if(m_rewardrole.length >= 1)
         {
            _loc2_ = m_rewardrole.length - 1;
            while(_loc2_ >= 0)
            {
               if(param1 == m_rewardrole[_loc2_].role)
               {
                  return m_rewardrole[_loc2_];
               }
               _loc2_--;
            }
         }
         return null;
      }
      
      public function getGrade() : int
      {
         var _loc2_:int = 1;
         var _loc1_:Boolean = false;
         var _loc3_:int = 0;
         if(m_showrank == 1)
         {
            _loc3_ = 0;
            while(_loc3_ < m_rewardlist.length)
            {
               if(m_rewardlist[_loc3_].minrank <= m_curRank && m_curRank <= m_rewardlist[_loc3_].maxrank)
               {
                  _loc2_ = 9 - m_rewardlist[_loc3_].gradeid;
                  _loc1_ = true;
               }
               _loc3_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < m_rewardrole.length)
            {
               if(m_rewardrole[_loc3_].minrank <= m_curRank && m_curRank <= m_rewardrole[_loc3_].maxrank)
               {
                  _loc2_ = 9 - m_rewardrole[_loc3_].gradeid;
                  _loc1_ = true;
               }
               _loc3_++;
            }
         }
         if(_loc1_ == false)
         {
            return 9;
         }
         return _loc2_;
      }
      
      public function getGradeRole() : int
      {
         var _loc2_:int = 0;
         var _loc1_:int = 1;
         _loc2_ = 0;
         while(_loc2_ < m_rewardrole.length)
         {
            if(m_rewardrole[_loc2_].minrank <= m_curRank && m_curRank <= m_rewardrole[_loc2_].maxrank)
            {
               _loc1_ = 9 - m_rewardrole[_loc2_].gradeid;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function setPlayerType(param1:int) : void
      {
         m_ntype = param1;
      }
      
      public function getPlayerType() : int
      {
         return m_ntype;
      }
      
      public function getLength() : int
      {
         if(m_list != null)
         {
            return m_list.length;
         }
         return 0;
      }
      
      public function getYestRank() : int
      {
         return m_yestRank;
      }
      
      public function getMaxRank() : int
      {
         return m_maxRank;
      }
      
      public function getCurRank() : int
      {
         return m_curRank;
      }
      
      public function setCurFightRank(param1:int) : void
      {
         m_curFightRank = param1;
      }
      
      public function getCurFightRank() : int
      {
         return m_curFightRank;
      }
      
      public function setPreRank(param1:int) : void
      {
         m_preRank = param1;
      }
      
      public function getPreRank() : int
      {
         return m_preRank;
      }
      
      public function setFightWin(param1:Boolean) : void
      {
         m_win = param1;
      }
      
      public function getFightWin() : Boolean
      {
         return m_win;
      }
      
      public function getGradeIndex() : int
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         if(m_win)
         {
            _loc3_ = int(m_rewardlist.length);
            _loc2_ = 0;
            _loc1_ = 0;
            _loc4_ = 0;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_rewardlist[_loc4_].minrank <= m_preRank && m_preRank <= m_rewardlist[_loc4_].maxrank)
               {
                  _loc2_ = m_rewardlist[_loc4_].gradeid;
                  break;
               }
               _loc4_++;
            }
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_rewardlist[_loc4_].minrank <= m_curFightRank && m_curFightRank <= m_rewardlist[_loc4_].maxrank)
               {
                  _loc1_ = m_rewardlist[_loc4_].gradeid;
                  break;
               }
               _loc4_++;
            }
            if(_loc2_ != _loc1_)
            {
               return 9 - _loc1_;
            }
            return -1;
         }
         return -1;
      }
      
      public function getSid() : Number
      {
         return m_nSid;
      }
      
      public function getItemByIndex(param1:int) : ListItem
      {
         if(m_list != null && param1 >= 0 && param1 < m_list.length)
         {
            return m_list[param1];
         }
         return null;
      }
      
      public function getItemByRank(param1:int) : ListItem
      {
         var _loc4_:int = 0;
         var _loc2_:ListItem = null;
         var _loc3_:int = int(m_list.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_list[_loc4_].m_rank == param1)
            {
               _loc2_ = m_list[_loc4_];
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function setremainnum1(param1:int) : void
      {
         m_nremainnum1 = param1;
      }
      
      public function setremainnum2(param1:int) : void
      {
         m_nremainnum2 = param1;
      }
      
      public function getremainnum1() : int
      {
         return m_nremainnum1;
      }
      
      public function getremainnum2() : int
      {
         return m_nremainnum2;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc4_:XML = null;
         var _loc9_:String = null;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc8_:int = 0;
         var _loc2_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         m_year = -1;
         m_month = -1;
         m_day = -1;
         if(param1.hasOwnProperty("NewRank"))
         {
            _loc4_ = param1.NewRank[0];
            _loc9_ = String(_loc4_.@nowtime).split(" ")[0];
            _loc7_ = int(_loc9_.split("-")[0]);
            _loc3_ = int(_loc9_.split("-")[1]);
            _loc8_ = int(_loc9_.split("-")[2]);
            m_year = _loc7_;
            m_month = _loc3_;
            m_day = _loc8_;
            _loc2_ = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[0]);
            _loc6_ = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[1]);
            _loc5_ = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[2]);
            if(_loc7_ != _loc2_ || _loc3_ != _loc6_ || _loc8_ != _loc5_)
            {
               m_nremainnum1 = m_totalnum;
               m_nremainnum2 = m_totalnum;
            }
            else
            {
               m_nremainnum1 = _loc4_.@remainnum1;
               m_nremainnum2 = _loc4_.@remainnum2;
            }
         }
         else
         {
            m_nremainnum1 = m_totalnum;
            m_nremainnum2 = m_totalnum;
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:int = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[0]);
         var _loc4_:int = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[1]);
         var _loc2_:int = int(TimeUtil.getTimeUtil().splitTimeString(TimeUtil.timeStr).split("-")[2]);
         if(m_year == -1 || _loc1_ != m_year || _loc4_ != m_month || _loc2_ != m_day)
         {
            m_nremainnum1 = m_totalnum;
            m_nremainnum2 = m_totalnum;
            m_year = _loc1_;
            m_month = _loc4_;
            m_day = _loc2_;
         }
         var _loc3_:XML = <NewRank />;
         _loc3_.@remainnum1 = m_nremainnum1;
         _loc3_.@remainnum2 = m_nremainnum2;
         _loc3_.@nowtime = TimeUtil.timeStr;
         return _loc3_;
      }
      
      public function setReamin() : void
      {
         if(m_showplayer == "playerOne")
         {
            m_nremainnum1--;
         }
         else
         {
            m_nremainnum2--;
         }
      }
      
      public function setAddRemain() : void
      {
         if(m_showplayer == "playerOne")
         {
            m_nremainnum1++;
         }
         else
         {
            m_nremainnum2++;
         }
      }
      
      public function getReamin() : int
      {
         if(m_showplayer == "playerOne")
         {
            return m_nremainnum1;
         }
         return m_nremainnum2;
      }
      
      public function getIsGet() : Boolean
      {
         return m_get;
      }
      
      public function setIsGet(param1:Boolean) : void
      {
         m_get = param1;
      }
      
      private function get curRank() : int
      {
         return _antiwear._curRank;
      }
      
      private function set curRank(param1:int) : void
      {
         _antiwear._curRank = param1;
      }
      
      public function initAllRankData(param1:Object) : void
      {
         var _loc2_:ItemAll = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         ClearUtil.clearObject(m_allList);
         m_allList.length = 0;
         if(param1.Ranks)
         {
            _loc3_ = int(param1.Ranks.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               _loc2_ = new ItemAll();
               _loc2_.BestRank = int(param1.BestRanks[_loc4_]);
               _loc2_.Name = String(param1.Ranks[_loc4_].Name);
               _loc2_.Role = String(param1.Ranks[_loc4_].Role);
               _loc2_.Sid = Number(param1.Ranks[_loc4_].Sid);
               m_allList.push(_loc2_);
               _loc4_++;
            }
         }
      }
      
      public function getDataByIndex(param1:int) : ItemAll
      {
         if(param1 >= 0 && param1 < m_allList.length)
         {
            return m_allList[param1];
         }
         return null;
      }
      
      public function getAmount() : int
      {
         return m_allList.length;
      }
      
      public function setPageIndex(param1:int) : void
      {
         m_pageindex = param1;
      }
      
      public function getPageIndex() : int
      {
         return m_pageindex;
      }
      
      public function getPageNum() : int
      {
         var _loc1_:int = 0;
         if(m_allList.length % 9 == 0)
         {
            _loc1_ = m_allList.length / 9;
         }
         else
         {
            _loc1_ = m_allList.length / 9 + 1;
         }
         return _loc1_;
      }
      
      public function getUidByIndex(param1:int) : String
      {
         var _loc2_:String = null;
         if(param1 >= 0 && param1 < m_allList.length)
         {
            _loc2_ = String((m_allList[param1].Sid / 100).toFixed(0));
         }
         return _loc2_;
      }
      
      public function getIdxByIndex(param1:int) : int
      {
         var _loc2_:int = 0;
         if(param1 >= 0 && param1 < m_allList.length)
         {
            _loc2_ = m_allList[param1].Sid / 10 % 10;
         }
         return _loc2_;
      }
      
      public function setFightType(param1:int) : void
      {
         m_fightType = param1;
      }
      
      public function getFightType() : int
      {
         return m_fightType;
      }
   }
}

