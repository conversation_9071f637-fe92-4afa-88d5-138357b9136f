package UI.Buff.Buff.AllTimeBuff
{
   import UI.Buff.Buff.BuffVO;
   
   public class AllTimeBuffVO extends BuffVO
   {
      private var _startDate:String;
      
      private var _totalTime:uint;
      
      public function AllTimeBuffVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startDate = _startDate;
         _antiwear.totalTime = _totalTime;
      }
      
      public function get startDate() : String
      {
         return _antiwear.startDate;
      }
      
      public function set startDate(param1:String) : void
      {
         _antiwear.startDate = param1;
      }
      
      public function get totalTime() : uint
      {
         return _antiwear.totalTime;
      }
      
      public function set totalTime(param1:uint) : void
      {
         _antiwear.totalTime = param1;
      }
   }
}

