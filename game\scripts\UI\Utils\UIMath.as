package UI.Utils
{
   public class UIMath
   {
      public function UIMath()
      {
         super();
      }
      
      public static function getDigitForInt(param1:int) : int
      {
         var _loc2_:int = 0;
         if(param1 < 0)
         {
            param1 = Math.abs(param1);
         }
         _loc2_++;
         if(param1 > 9)
         {
            _loc2_ += getDigitForInt(int(param1 / 10));
         }
         return _loc2_;
      }
   }
}

