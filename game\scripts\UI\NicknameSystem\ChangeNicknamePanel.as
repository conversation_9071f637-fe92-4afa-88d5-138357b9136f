package UI.NicknameSystem
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.QuitBtn3;
   import UI.DirtyWordFilter.DirtyWordFilterEngine;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyControlPanel;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.NicknameSystem.Btn.FreeChangeNicknameBtn;
   import UI.NicknameSystem.Btn.RedNicknameBtn;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Expo;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ChangeNicknamePanel extends MySprite
   {
      public var currentNicknameText:TextField;
      
      public var descriptionText:TextField;
      
      public var newNickNameText:TextField;
      
      public var freeChangeNicknameBtn:FreeChangeNicknameBtn;
      
      public var redNicknameBtn:RedNicknameBtn;
      
      public var explainText:TextField;
      
      public var quitBtn:QuitBtn3;
      
      private var _submitNicknameStr:String;
      
      private var _nicknameXML:XML;
      
      private var _dirtyWordFilterEngine:DirtyWordFilterEngine;
      
      public function ChangeNicknamePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function playAddedAnimation() : void
      {
         TweenLite.from(this,0.5,{
            "alpha":0,
            "ease":Expo.easeIn
         });
      }
      
      public function playRemovedAnimation(param1:Function, param2:Array) : void
      {
         TweenLite.to(this,0.5,{
            "alpha":0,
            "ease":Expo.easeIn,
            "onComplete":param1,
            "onCompleteParams":param2
         });
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         TweenLite.killTweensOf(this);
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         currentNicknameText = null;
         descriptionText = null;
         newNickNameText = null;
         explainText = null;
         if(freeChangeNicknameBtn)
         {
            freeChangeNicknameBtn.clear();
         }
         freeChangeNicknameBtn = null;
         if(redNicknameBtn)
         {
            redNicknameBtn.clear();
         }
         redNicknameBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _nicknameXML = null;
         if(_dirtyWordFilterEngine)
         {
            _dirtyWordFilterEngine.clear();
         }
         _dirtyWordFilterEngine = null;
      }
      
      public function changeNickname(param1:UIPassiveEvent) : void
      {
         var myParent:MyControlPanel;
         var submitNicknameStr:String;
         var nicknameXML:XML;
         var buyDataObj:Object;
         var changeNum:int;
         var e:UIPassiveEvent = param1;
         if(e.data.detail == 1)
         {
            if(e.data.task.type == "freeChangeNickname")
            {
               myParent = e.data.task.myParent as MyControlPanel;
               submitNicknameStr = e.data.task.submitNicknameStr;
               nicknameXML = e.data.task.nicknameXML;
               if(Boolean(NicknameData.getInstance().myDataInNicknameRankList) && NicknameData.getInstance().myDataInNicknameRankList.extra == null)
               {
                  NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().myDataInNicknameRankList.rId,NicknameData.getInstance().myDataInNicknameRankList.score,submitNicknameStr,null,function(param1:Array):void
                  {
                     NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().myDataInNicknameRankList.rId;
                     NicknameData.getInstance().myDataInNicknameRankList = {
                        "rId":NicknameData.getInstance().myDataInNicknameRankList.rId,
                        "nicknameType":"whiteNickname",
                        "index":GameData.getInstance().getSaveFileData().index,
                        "uId":GameData.getInstance().getLoginReturnData().getUid(),
                        "userName":GameData.getInstance().getLoginReturnData().getName(),
                        "score":NicknameData.getInstance().myDataInNicknameRankList.score,
                        "extra":submitNicknameStr
                     };
                     if(myParent)
                     {
                        myParent.showWarningBox("增加昵称成功！",0);
                        GamingUI.getInstance().refresh(1);
                     }
                     var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  },function(param1:String):void
                  {
                     if(myParent)
                     {
                        myParent.showWarningBox("增加昵称出现错误！ 请重试！",1);
                     }
                  },null,null,null);
               }
               else
               {
                  NicknameRankListFunction.getInstance().makeSureRankListIsFull(0,null,function():void
                  {
                     if(NicknameData.getInstance().freeRankListId == 0)
                     {
                        if(myParent)
                        {
                           myParent.showWarningBox("改名失败，当前名额已满，请关注版本更新！",0);
                        }
                     }
                     else if(NicknameData.getInstance().myDataInNicknameRankList == null && GamingUI.getInstance().player1.playerVO.level >= int(nicknameXML.FreeChangeNickname[0].@needLevel))
                     {
                        NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().freeRankListId,1,_submitNicknameStr,null,function(param1:Array):void
                        {
                           NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().freeRankListId;
                           NicknameData.getInstance().myDataInNicknameRankList = {
                              "rId":NicknameData.getInstance().freeRankListId,
                              "nicknameType":"whiteNickname",
                              "index":GameData.getInstance().getSaveFileData().index,
                              "uId":GameData.getInstance().getLoginReturnData().getUid(),
                              "userName":GameData.getInstance().getLoginReturnData().getName(),
                              "score":1,
                              "extra":submitNicknameStr
                           };
                           if(myParent)
                           {
                              myParent.showWarningBox("增加昵称成功！",0);
                              GamingUI.getInstance().refresh(1);
                           }
                           var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                           _loc2_.type = "4399";
                           _loc2_.isHaveData = false;
                           SaveTaskList.getInstance().addData(_loc2_);
                           MyFunction2.saveGame();
                        },function(param1:String):void
                        {
                           if(myParent)
                           {
                              myParent.showWarningBox("增加昵称出现错误！请重试！",1);
                           }
                        },null,null,null);
                     }
                     else if(GamingUI.getInstance().player1.playerVO.level < int(nicknameXML.FreeChangeNickname[0].@needLevel))
                     {
                        if(myParent)
                        {
                           myParent.showWarningBox("改名失败，要" + int(nicknameXML.FreeChangeNickname[0].@needLevel) + "以上才能获得免费昵称！",0);
                        }
                     }
                     else if(myParent)
                     {
                        myParent.showWarningBox("改名失败，您已存在一个免费的昵称！",0);
                     }
                  },null,null);
               }
            }
            else if(e.data.task.type == "changeRedNickname")
            {
               myParent = e.data.task.myParent as MyControlPanel;
               submitNicknameStr = e.data.task.submitNicknameStr;
               nicknameXML = e.data.task.nicknameXML;
               buyDataObj = e.data.task.buyDataObj;
               changeNum = int(e.data.task.changeNum);
               if(Boolean(NicknameData.getInstance().myDataInNicknameRankList) && NicknameData.getInstance().myDataInNicknameRankList.extra == null)
               {
                  NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().myDataInNicknameRankList.rId,NicknameData.getInstance().myDataInNicknameRankList.score,submitNicknameStr,null,function(param1:Array):void
                  {
                     NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().myDataInNicknameRankList.rId;
                     NicknameData.getInstance().myDataInNicknameRankList = {
                        "rId":NicknameData.getInstance().myDataInNicknameRankList.rId,
                        "nicknameType":"redNickname",
                        "index":GameData.getInstance().getSaveFileData().index,
                        "uId":GameData.getInstance().getLoginReturnData().getUid(),
                        "userName":GameData.getInstance().getLoginReturnData().getName(),
                        "score":NicknameData.getInstance().myDataInNicknameRankList.score,
                        "extra":submitNicknameStr
                     };
                     if(myParent)
                     {
                        myParent.showWarningBox("增加昵称成功！",0);
                        GamingUI.getInstance().refresh(1);
                     }
                     var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  },function(param1:String):void
                  {
                     if(myParent)
                     {
                        myParent.showWarningBox("增加昵称出现错误！请重试！ ",1);
                     }
                  },null,null,null);
               }
               else if(NicknameData.getInstance().myDataInNicknameRankList == null || NicknameRankListFunction.getInstance().getNicknameTypeByRankListId(NicknameData.getInstance().myDataInNicknameRankList.rId,nicknameXML) == "whiteNickname")
               {
                  NicknameRankListFunction.getInstance().makeSureRankListIsFull(1,null,function():void
                  {
                     if(NicknameData.getInstance().payRankListId == 0)
                     {
                        if(myParent)
                        {
                           myParent.showWarningBox("改名失败，当前名额已满，请关注版本更新！",0);
                        }
                     }
                     else if(GamingUI.getInstance().player1.vipVO.isHaveFreeRedNickname)
                     {
                        NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().payRankListId,1,submitNicknameStr,null,function(param1:Array):void
                        {
                           NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().payRankListId;
                           NicknameData.getInstance().myDataInNicknameRankList = {
                              "rId":NicknameData.getInstance().payRankListId,
                              "nicknameType":"redNickname",
                              "index":GameData.getInstance().getSaveFileData().index,
                              "uId":GameData.getInstance().getLoginReturnData().getUid(),
                              "userName":GameData.getInstance().getLoginReturnData().getName(),
                              "score":1,
                              "extra":submitNicknameStr
                           };
                           if(myParent)
                           {
                              myParent.showWarningBox("增加昵称成功！",0);
                              GamingUI.getInstance().refresh(1);
                           }
                           var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                           _loc2_.type = "4399";
                           _loc2_.isHaveData = false;
                           SaveTaskList.getInstance().addData(_loc2_);
                           MyFunction2.saveGame();
                        },function(param1:String):void
                        {
                           if(myParent)
                           {
                              myParent.showWarningBox("增加昵称出现错误！请重试！",1);
                           }
                           trace("errorStr nickname rank",param1);
                           trace("NicknameData.getInstance().payRankListId",NicknameData.getInstance().payRankListId);
                           trace("submitNicknameStr",submitNicknameStr);
                        },null,null,null);
                     }
                     else
                     {
                        AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
                        {
                           var obj:Object = param1;
                           if(obj["propId"] != buyDataObj["propId"])
                           {
                              throw new Error("购买物品id前后端不相同！");
                           }
                           NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().payRankListId,changeNum,submitNicknameStr,null,function(param1:Array):void
                           {
                              NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().payRankListId;
                              NicknameData.getInstance().myDataInNicknameRankList = {
                                 "rId":NicknameData.getInstance().payRankListId,
                                 "nicknameType":"redNickname",
                                 "index":GameData.getInstance().getSaveFileData().index,
                                 "uId":GameData.getInstance().getLoginReturnData().getUid(),
                                 "userName":GameData.getInstance().getLoginReturnData().getName(),
                                 "score":changeNum,
                                 "extra":submitNicknameStr
                              };
                              if(myParent)
                              {
                                 myParent.showWarningBox("增加昵称成功！",0);
                                 GamingUI.getInstance().refresh(1);
                              }
                              var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                              _loc2_.type = "4399";
                              _loc2_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc2_);
                              MyFunction2.saveGame();
                           },function(param1:String):void
                           {
                              if(myParent)
                              {
                                 myParent.showWarningBox("增加昵称出错了！ 请您找存档客服解决该问题！",1);
                              }
                              var _loc3_:NicknameSaveData = new NicknameSaveData();
                              _loc3_.currentRankId = NicknameData.getInstance().myDataInNicknameRankList.rId;
                              _loc3_.currentChangeNicknameNum = 1;
                              _loc3_.currentNickname = submitNicknameStr;
                              _loc3_.errorString = param1;
                              if(!NicknameData.getInstance().nicknameSaveData)
                              {
                                 NicknameData.getInstance().nicknameSaveData = new Vector.<NicknameSaveData>();
                              }
                              NicknameData.getInstance().nicknameSaveData.push(_loc3_);
                              var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                              _loc2_.type = "4399";
                              _loc2_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc2_);
                              MyFunction2.saveGame();
                           },null,null,null);
                        },!!myParent ? myParent.showWarningBox : null,WarningBoxSingle.getInstance().getTextFontSize());
                     }
                  },null,null);
               }
               else
               {
                  AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
                  {
                     var obj:Object = param1;
                     if(obj["propId"] != buyDataObj["propId"])
                     {
                        throw new Error("购买物品id前后端不相同！");
                     }
                     NicknameRankListFunction.getInstance().submitMyDataToRankLists(GameData.getInstance().getSaveFileData().index,NicknameData.getInstance().myDataInNicknameRankList.rId,changeNum,submitNicknameStr,null,function(param1:Array):void
                     {
                        NicknameData.getInstance().myNicknameRankListId = NicknameData.getInstance().myDataInNicknameRankList.rId;
                        NicknameData.getInstance().myDataInNicknameRankList = {
                           "rId":NicknameData.getInstance().myDataInNicknameRankList.rId,
                           "nicknameType":"redNickname",
                           "index":GameData.getInstance().getSaveFileData().index,
                           "uId":GameData.getInstance().getLoginReturnData().getUid(),
                           "userName":GameData.getInstance().getLoginReturnData().getName(),
                           "score":changeNum,
                           "extra":submitNicknameStr
                        };
                        if(myParent)
                        {
                           GamingUI.getInstance().refresh(1);
                           myParent.showWarningBox("增加昵称成功！",0);
                        }
                        var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                        _loc2_.type = "4399";
                        _loc2_.isHaveData = false;
                        SaveTaskList.getInstance().addData(_loc2_);
                        MyFunction2.saveGame();
                     },function(param1:String):void
                     {
                        if(myParent)
                        {
                           myParent.showWarningBox("增加昵称出错了！ 请您找存档客服解决该问题！",1);
                        }
                        var _loc3_:NicknameSaveData = new NicknameSaveData();
                        _loc3_.currentRankId = NicknameData.getInstance().myDataInNicknameRankList.rId;
                        _loc3_.currentChangeNicknameNum = changeNum;
                        _loc3_.currentNickname = submitNicknameStr;
                        _loc3_.errorString = param1;
                        if(!NicknameData.getInstance().nicknameSaveData)
                        {
                           NicknameData.getInstance().nicknameSaveData = new Vector.<NicknameSaveData>();
                        }
                        NicknameData.getInstance().nicknameSaveData.push(_loc3_);
                        var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                        _loc2_.type = "4399";
                        _loc2_.isHaveData = false;
                        SaveTaskList.getInstance().addData(_loc2_);
                        MyFunction2.saveGame();
                     },null,null,null);
                  },!!myParent ? myParent.showWarningBox : null,WarningBoxSingle.getInstance().getTextFontSize());
               }
            }
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickFreeChangeNicknameBtn",clickFreeChangeNicknameBtn,true,0,true);
         addEventListener("clickRedNicknameBtn",clickRedNicknameBtn,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickFreeChangeNicknameBtn",clickFreeChangeNicknameBtn,true);
         removeEventListener("clickRedNicknameBtn",clickRedNicknameBtn,true);
      }
      
      private function clickFreeChangeNicknameBtn(param1:UIBtnEvent) : void
      {
         var _loc2_:MyControlPanel = this.parent as MyControlPanel;
         if(!newNickNameText.text)
         {
            if(_loc2_)
            {
               _loc2_.showWarningBox("新昵称不能为空！",0);
            }
            return;
         }
         quitBtn.dispatchEvent(new UIBtnEvent("clickQuitBtn"));
         _dirtyWordFilterEngine.filterWords(newNickNameText.text,checkBadReturn1);
      }
      
      private function checkBadReturn1(param1:String) : void
      {
         _submitNicknameStr = param1;
         var _loc2_:String = newNickNameText.text;
         var _loc3_:MyControlPanel = this.parent as MyControlPanel;
         if(_loc3_)
         {
            _loc3_.showWarningBox("新昵称为：“" + _submitNicknameStr + "“" + (_submitNicknameStr == _loc2_ ? "" : "（您的名字中有不文明用语将被替换为星号）") + "，确定要改名吗？",3,{
               "type":"freeChangeNickname",
               "submitNicknameStr":_submitNicknameStr,
               "okFunction":changeNickname,
               "myParent":_loc3_,
               "nicknameXML":_nicknameXML
            });
         }
      }
      
      private function clickRedNicknameBtn(param1:UIBtnEvent) : void
      {
         var _loc2_:MyControlPanel = this.parent as MyControlPanel;
         if(!newNickNameText.text)
         {
            if(_loc2_)
            {
               _loc2_.showWarningBox("新昵称不能为空！",0);
            }
            return;
         }
         quitBtn.dispatchEvent(new UIBtnEvent("clickQuitBtn"));
         _dirtyWordFilterEngine.filterWords(newNickNameText.text,checkBadReturn2);
      }
      
      private function checkBadReturn2(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc7_:* = undefined;
         var _loc9_:* = undefined;
         var _loc5_:int = 0;
         var _loc4_:String = null;
         _submitNicknameStr = param1;
         var _loc6_:String = newNickNameText.text;
         var _loc2_:Object = {};
         _loc2_["count"] = 1;
         _loc2_["idx"] = GameData.getInstance().getSaveFileData().index;
         _loc2_["tag"] = "购买红名";
         var _loc8_:MyControlPanel = this.parent as MyControlPanel;
         if(Boolean(NicknameData.getInstance().myDataInNicknameRankList) && NicknameData.getInstance().myDataInNicknameRankList.extra == null)
         {
            _loc2_["propId"] = "";
            _loc2_["price"] = 0;
            if(_loc8_)
            {
               _loc8_.showWarningBox("你的红昵称丢失, 将获得免费改名一次！ \n新昵称为：“" + _submitNicknameStr + "“" + (_submitNicknameStr == _loc6_ ? "" : "（您的名字中有不文明用语将被替换为星号）") + "，确定要改名吗？",3,{
                  "type":"changeRedNickname",
                  "submitNicknameStr":_submitNicknameStr,
                  "okFunction":changeNickname,
                  "myParent":_loc8_,
                  "nicknameXML":_nicknameXML,
                  "buyDataObj":_loc2_,
                  "changeNum":0
               });
            }
         }
         else if(!Boolean(NicknameData.getInstance().myDataInNicknameRankList) && GamingUI.getInstance().player1.vipVO.isHaveFreeRedNickname)
         {
            _loc2_["propId"] = "";
            _loc2_["price"] = 0;
            if(_loc8_)
            {
               _loc8_.showWarningBox("VIP" + GamingUI.getInstance().player1.vipVO.vipLevel + " 以上可以免费获得一个红名！\n新昵称为：“" + _submitNicknameStr + "“" + (_submitNicknameStr == _loc6_ ? "" : "（您的名字中有不文明用语将被替换为星号）") + "，确定要改名吗？",3,{
                  "type":"changeRedNickname",
                  "submitNicknameStr":_submitNicknameStr,
                  "okFunction":changeNickname,
                  "myParent":_loc8_,
                  "nicknameXML":_nicknameXML,
                  "buyDataObj":_loc2_,
                  "changeNum":0
               });
            }
         }
         else
         {
            _loc3_ = !!NicknameData.getInstance().myDataInNicknameRankList ? NicknameData.getInstance().myDataInNicknameRankList.score + 1 : 1;
            _loc7_ = MyFunction.getInstance().excreteString(_nicknameXML.PayPrices[0].@tickets);
            _loc9_ = MyFunction.getInstance().excreteStringToString(_nicknameXML.PayPrices[0].@ticketIds);
            _loc5_ = _loc7_[Math.min(_loc3_ - 1,_loc7_.length - 1)];
            _loc4_ = _loc9_[Math.min(_loc3_ - 1,_loc9_.length - 1)];
            _loc2_["propId"] = _loc4_;
            _loc2_["price"] = _loc5_;
            if(_loc8_)
            {
               _loc8_.showWarningBox("这是您的第" + _loc3_ + "次改名！ 需要" + _loc5_ + "点券 \n新昵称为：“" + _submitNicknameStr + "“" + (_submitNicknameStr == _loc6_ ? "" : "（您的名字中有不文明用语将被替换为星号）") + "，确定要改名吗？",3,{
                  "type":"changeRedNickname",
                  "submitNicknameStr":_submitNicknameStr,
                  "okFunction":changeNickname,
                  "myParent":_loc8_,
                  "nicknameXML":_nicknameXML,
                  "buyDataObj":_loc2_,
                  "changeNum":_loc3_
               });
            }
         }
      }
      
      private function init() : void
      {
         quitBtn = new QuitBtn3();
         quitBtn.x = 428;
         quitBtn.y = 62;
         addChild(quitBtn);
         newNickNameText.defaultTextFormat = new TextFormat(null,18,16776960);
         newNickNameText.type = "input";
         newNickNameText.selectable = true;
         newNickNameText.multiline = false;
         newNickNameText.wordWrap = false;
         freeChangeNicknameBtn.mouseChildren = false;
         freeChangeNicknameBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(freeChangeNicknameBtn,-100);
         redNicknameBtn.mouseChildren = false;
         redNicknameBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(redNicknameBtn,-100);
         MyFunction2.loadXMLFunction("nickname",completeLoadNicknameXML,null,true);
      }
      
      private function initPanelByMyDataInNicknameRankList(param1:Object) : void
      {
         var myParent:MyControlPanel;
         var data:Object = param1;
         NicknameData.getInstance().myDataInNicknameRankList = data;
         myParent = parent as MyControlPanel;
         if(myParent)
         {
            GamingUI.getInstance().refresh(1);
         }
         if(!freeChangeNicknameBtn || !redNicknameBtn)
         {
            return;
         }
         _dirtyWordFilterEngine = new DirtyWordFilterEngine(function():void
         {
            var _loc5_:int = 0;
            var _loc3_:int = 0;
            var _loc4_:Boolean = false;
            var _loc1_:* = undefined;
            var _loc2_:int = int(_nicknameXML.NicknameData[0].@maxLength);
            newNickNameText.maxChars = _loc2_;
            newNickNameText.restrict = "^ ";
            explainText.text = "请输入您的新昵称（最多" + _loc2_ + "个字符，请使用文明用语，不符合要求的用语将被过滤）：";
            if(NicknameData.getInstance().myDataInNicknameRankList)
            {
               if(!NicknameData.getInstance().myDataInNicknameRankList.extra)
               {
                  _loc1_ = MyFunction.getInstance().excreteString(_nicknameXML.RankListID[0].@freeRankListID);
                  _loc3_ = int(_loc1_.length);
                  _loc5_ = 0;
                  while(_loc5_ < _loc3_)
                  {
                     if(_loc1_[_loc5_] == NicknameData.getInstance().myDataInNicknameRankList.rId)
                     {
                        _loc4_ = true;
                        break;
                     }
                     _loc5_++;
                  }
                  if(_loc4_)
                  {
                     descriptionText.text = "你的昵称已经丢失，你可以免费补回昵称，请点击“免费改名”按钮";
                     freeChangeNicknameBtn.mouseChildren = true;
                     freeChangeNicknameBtn.mouseEnabled = true;
                     MyFunction.getInstance().changeSaturation(freeChangeNicknameBtn,0);
                  }
                  else
                  {
                     descriptionText.text = "你的昵称已经丢失，你可以免费补回昵称，请点击“个性化红名”按钮";
                     redNicknameBtn.mouseChildren = true;
                     redNicknameBtn.mouseEnabled = true;
                     MyFunction.getInstance().changeSaturation(redNicknameBtn,0);
                  }
               }
               else
               {
                  descriptionText.text = "·第一次改名可以使用免费改名。\n·个性化红名需要花费点券。\n·VIP5及以上玩家第一次改名可以免费使用个性化红名。";
                  redNicknameBtn.mouseChildren = true;
                  redNicknameBtn.mouseEnabled = true;
                  MyFunction.getInstance().changeSaturation(redNicknameBtn,0);
                  currentNicknameText.text = NicknameData.getInstance().myDataInNicknameRankList.extra;
               }
            }
            else if(GamingUI.getInstance().player1.vipVO.isHaveFreeRedNickname)
            {
               descriptionText.text = "你是VIP" + GamingUI.getInstance().player1.vipVO.vipLevel + "，可以免费获取一个红名，请点击“个性化红名”按钮";
               redNicknameBtn.mouseChildren = true;
               redNicknameBtn.mouseEnabled = true;
               MyFunction.getInstance().changeSaturation(redNicknameBtn,0);
            }
            else
            {
               descriptionText.text = "·第一次改名可以使用免费改名。\n·个性化红名需要花费点券。\n·VIP5及以上玩家第一次改名可以免费使用个性化红名。";
               freeChangeNicknameBtn.mouseChildren = true;
               freeChangeNicknameBtn.mouseEnabled = true;
               MyFunction.getInstance().changeSaturation(freeChangeNicknameBtn,0);
               redNicknameBtn.mouseChildren = true;
               redNicknameBtn.mouseEnabled = true;
               MyFunction.getInstance().changeSaturation(redNicknameBtn,0);
            }
         },[]);
      }
      
      private function completeLoadNicknameXML(param1:XML) : void
      {
         if(!freeChangeNicknameBtn || !redNicknameBtn)
         {
            return;
         }
         currentNicknameText.text = "无";
         _nicknameXML = param1;
         var _loc2_:MyControlPanel = parent as MyControlPanel;
         if(NicknameData.getInstance().myNicknameRankListId)
         {
            NicknameRankListFunction.getInstance().getOneRankListDataByUserName(NicknameRankListFunction.getInstance().getNicknameTypeByRankListId(NicknameData.getInstance().myNicknameRankListId,param1),NicknameData.getInstance().myNicknameRankListId,GameData.getInstance().getLoginReturnData().getName(),GameData.getInstance().getSaveFileData().index,null,initPanelByMyDataInNicknameRankList,null,null,!!_loc2_ ? _loc2_.showWarningBox : null,["获取数据出错了！",0]);
         }
         else
         {
            initPanelByMyDataInNicknameRankList(null);
         }
      }
   }
}

