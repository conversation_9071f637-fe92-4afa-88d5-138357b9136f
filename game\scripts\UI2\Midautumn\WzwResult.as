package UI2.Midautumn
{
   import UI.EnterFrameTime;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class WzwResult extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_view:WzwView;
      
      private var m_btnClose:ButtonLogicShell2;
      
      private var m_title:MovieClip;
      
      private var m_rank:TextField;
      
      private var m_vect:Vector.<DiceItem>;
      
      private var m_calLogic:CalculateLogic;
      
      private var m_allshow:MovieClip;
      
      private var m_down:Boolean = false;
      
      private var m_nX:Number;
      
      private var m_nY:Number;
      
      public function WzwResult()
      {
         super();
         m_btnClose = new ButtonLogicShell2();
         m_vect = new Vector.<DiceItem>();
         m_calLogic = new CalculateLogic();
      }
      
      override public function clear() : void
      {
         m_allshow.removeEventListener("mouseDown",calldown,true);
         m_allshow.removeEventListener("mouseUp",callup,true);
         m_show.removeEventListener("clickButton",clickButton);
         ClearUtil.clearObject(m_btnClose);
         m_btnClose = null;
         ClearUtil.clearObject(m_vect);
         m_vect = null;
         super.clear();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_down)
         {
            m_show.x = m_allshow.mouseX - m_nX;
            m_show.y = m_allshow.mouseY - m_nY;
         }
      }
      
      public function init(param1:MovieClip, param2:WzwView) : void
      {
         m_allshow = param1;
         m_show = param1["mainmc"] as MovieClip;
         m_view = param2;
         m_btnClose.setShow(m_show["btnOK"]);
         m_btnClose.setTipString("确定");
         m_title = m_show["titlemc"] as MovieClip;
         m_rank = m_show["txtRank"] as TextField;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_allshow.addEventListener("mouseDown",calldown);
         m_allshow.addEventListener("mouseUp",callup);
      }
      
      private function calldown(param1:MouseEvent) : void
      {
         m_down = true;
         m_nX = m_allshow.mouseX - m_show.x;
         m_nY = m_allshow.mouseY - m_show.y;
      }
      
      private function callup(param1:MouseEvent) : void
      {
         m_down = false;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_btnClose === _loc2_)
         {
            m_view.hideResult();
         }
      }
      
      public function refreshInfo() : void
      {
         var _loc1_:DiceItem = null;
         var _loc2_:int = 0;
         m_vect.length = 0;
         _loc2_ = 0;
         while(_loc2_ < 6)
         {
            _loc1_ = new DiceItem();
            _loc1_.setResult(WzwData.getInstance().getPointByIndex(_loc2_));
            m_vect.push(_loc1_);
            _loc2_++;
         }
         m_calLogic.init(m_vect);
         if(m_calLogic.getResult() == 9)
         {
            m_title.gotoAndStop("1");
         }
         else
         {
            m_title.gotoAndStop(String(m_calLogic.getResult() + 1));
         }
         m_rank.text = String(WzwData.getInstance().getCurrRank());
      }
   }
}

