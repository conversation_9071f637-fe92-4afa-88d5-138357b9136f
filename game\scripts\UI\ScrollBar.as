package UI
{
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   
   public class ScrollBar
   {
      public static const H:String = "H";
      
      public static const L:String = "L";
      
      public const name:String = "滚动条窗口";
      
      private var _speed:Number = 15;
      
      private var _upBtn:Sprite;
      
      private var _downBtn:Sprite;
      
      private var _tween:Number;
      
      private var _elastic:Boolean;
      
      private var _lineAbleClick:Boolean;
      
      private var _mouseWheel:Boolean;
      
      private var _direction:String;
      
      private var _scale9Grid:Rectangle;
      
      private var _target:DisplayObject;
      
      private var _maskTarget:DisplayObject;
      
      private var _scrollBar:Sprite;
      
      private var _scrollLine:Sprite;
      
      private var _timer:Timer = null;
      
      private var _scrollBarOriginalPoint:Point;
      
      private var _parentMC:DisplayObjectContainer;
      
      private var _rectangle:Rectangle;
      
      private var _distanceX:Number;
      
      private var _distanceY:Number;
      
      private var _targetPoint:Number = NaN;
      
      private var _coor:String;
      
      private var _length:String;
      
      private var _mouse:String;
      
      private var _oldLength:Point;
      
      private var _abled:Boolean = true;
      
      private var _state:String;
      
      public function ScrollBar(param1:DisplayObjectContainer, param2:*, param3:Sprite, param4:Sprite, param5:Number = 5, param6:Boolean = false, param7:Boolean = false, param8:Boolean = false, param9:String = "L")
      {
         super();
         if(!(param2 is DisplayObject || param2 is Rectangle))
         {
            throw new Error("没有传入遮罩对象");
         }
         _target = param1;
         _maskTarget = param2;
         _scrollBar = param3;
         _scrollLine = param4;
         _tween = param5 < 1 || param5 > 20 ? 1 : param5;
         _elastic = param6;
         _lineAbleClick = param7;
         _mouseWheel = param8;
         _direction = param9;
         _parentMC = _scrollBar.parent;
         _coor = param9 == "H" ? "x" : "y";
         _length = _coor == "x" ? "width" : "height";
         _mouse = _coor == "x" ? "mouseX" : "mouseY";
         _oldLength = new Point(_scrollBar.width,_scrollBar.height);
         _scale9Grid = new Rectangle(_scrollBar.width / 3,_scrollBar.height / 3,_scrollBar.width / 3,_scrollBar.height / 3);
         _scrollBar.addEventListener("addedToStage",addToStage,false,0,true);
         makeScrollPane();
      }
      
      public function clear() : void
      {
         _upBtn = null;
         _downBtn = null;
         _scale9Grid = null;
         _target = null;
         _maskTarget = null;
         _scrollBar = null;
         _scrollLine = null;
         _timer = null;
         _scrollBarOriginalPoint = null;
         _parentMC = null;
         _rectangle = null;
         _oldLength = null;
      }
      
      public function set tween(param1:Number) : void
      {
         _tween = param1 < 1 || param1 > 20 ? 1 : param1;
      }
      
      public function set elastic(param1:Boolean) : void
      {
         _elastic = param1;
         if(_abled)
         {
            makeScrollBar();
         }
      }
      
      public function set lineAbleClick(param1:Boolean) : void
      {
         _lineAbleClick = param1;
         if(_lineAbleClick)
         {
            _scrollLine.addEventListener("mouseDown",scrollLineMouseDownHandler,false,0,true);
         }
         else
         {
            _scrollLine.removeEventListener("mouseDown",scrollLineMouseDownHandler);
         }
      }
      
      public function set stepNumber(param1:Number) : void
      {
         _speed = param1;
      }
      
      public function set mouseWheel(param1:Boolean) : void
      {
         _mouseWheel = param1;
         if(_mouseWheel && _abled)
         {
            if(_parentMC.stage)
            {
               _parentMC.stage.addEventListener("mouseWheel",mouseWheelHandler,false,0,true);
            }
            else if(_abled)
            {
               if(_parentMC.stage)
               {
                  _parentMC.stage.removeEventListener("mouseWheel",mouseWheelHandler);
               }
            }
         }
      }
      
      public function set direction(param1:String) : void
      {
         _direction = param1;
         _coor = _direction == "H" ? "x" : "y";
         _length = _coor == "x" ? "width" : "height";
         _mouse = _coor == "x" ? "mouseX" : "mouseY";
         if(_abled)
         {
            makeScrollBar();
         }
      }
      
      public function set scale9Grid(param1:Rectangle) : void
      {
         _scale9Grid = param1;
         try
         {
            _scrollBar.scale9Grid = _scale9Grid;
         }
         catch(e:Event)
         {
            _scrollBar.scale9Grid = null;
         }
      }
      
      public function set speed(param1:Number) : void
      {
         _speed = param1 < 5 || param1 > 35 ? 15 : param1;
      }
      
      public function set UP(param1:Sprite) : void
      {
         _upBtn = param1;
         if(!_abled)
         {
            _upBtn.visible = false;
            _upBtn.removeEventListener("mouseDown",upDownBtnMouseDownHandler);
            return;
         }
         _upBtn.addEventListener("mouseDown",upDownBtnMouseDownHandler,false,0,true);
      }
      
      public function set DOWN(param1:Sprite) : void
      {
         _downBtn = param1;
         if(!_abled)
         {
            _downBtn.visible = false;
            _downBtn.removeEventListener("mouseDown",upDownBtnMouseDownHandler);
            return;
         }
         _downBtn.addEventListener("mouseDown",upDownBtnMouseDownHandler,false,0,true);
      }
      
      public function refresh() : void
      {
         checkAbled();
      }
      
      private function makeScrollPane() : void
      {
         initAllThing();
         _scrollBarOriginalPoint = new Point(_scrollBar.x,_scrollBar.y);
         makeMask();
         checkAbled();
      }
      
      private function checkAbled() : void
      {
         if(_maskTarget[_length] >= _target[_length])
         {
            _scrollBar.visible = false;
            _scrollLine.visible = false;
            if(_downBtn)
            {
               _downBtn.visible = false;
            }
            if(_upBtn)
            {
               _upBtn.visible = false;
            }
            _abled = false;
            if(_upBtn && _downBtn)
            {
               _upBtn.removeEventListener("mouseDown",upDownBtnMouseDownHandler);
               _downBtn.removeEventListener("mouseDown",upDownBtnMouseDownHandler);
            }
            if(_mouseWheel)
            {
               makeMouseWheel("stop");
            }
         }
         else
         {
            _scrollBar.visible = true;
            _scrollLine.visible = true;
            if(_downBtn)
            {
               _downBtn.visible = true;
            }
            if(_upBtn)
            {
               _upBtn.visible = true;
            }
            _abled = true;
            makeScrollBar();
            if(_upBtn && _downBtn)
            {
               _upBtn.addEventListener("mouseDown",upDownBtnMouseDownHandler,false,0,true);
               _downBtn.addEventListener("mouseDown",upDownBtnMouseDownHandler,false,0,true);
            }
            if(_lineAbleClick)
            {
               makeScrollLine();
            }
            if(_mouseWheel)
            {
               makeMouseWheel();
            }
            timeListener();
         }
      }
      
      private function timeListener() : void
      {
         if(_timer != null)
         {
            return;
         }
         _timer = new Timer(33.333333333333336,0);
         _timer.addEventListener("timer",timeHandler,false,0,true);
         _timer.start();
      }
      
      private function initAllThing() : void
      {
         setRegistration(_maskTarget as DisplayObjectContainer);
         setRegistration(_target as DisplayObjectContainer);
         setRegistration(_scrollLine);
         setRegistration(_scrollBar);
      }
      
      private function makeMask() : void
      {
         if(_maskTarget.parent == null)
         {
            _parentMC.addChild(_maskTarget);
         }
         _target.mask = _maskTarget;
      }
      
      private function makeScrollLine() : void
      {
         _scrollLine.buttonMode = false;
         _scrollLine.addEventListener("mouseDown",scrollLineMouseDownHandler,false,0,true);
      }
      
      private function makeMouseWheel(param1:String = "start") : void
      {
         _state = param1;
         if(_state == "start")
         {
            if(_parentMC.stage)
            {
               _parentMC.stage.addEventListener("mouseWheel",mouseWheelHandler,false,0,true);
            }
            else if(_state == "stop")
            {
               if(_parentMC.stage)
               {
                  _parentMC.stage.removeEventListener("mouseWheel",mouseWheelHandler);
               }
            }
         }
      }
      
      private function makeScrollBar() : void
      {
         _scrollBar.buttonMode = true;
         _scrollBar.mouseChildren = false;
         scrollBarLength();
         if(_coor == "y")
         {
            _rectangle = new Rectangle(_scrollBarOriginalPoint.x,_scrollBarOriginalPoint.y,0,_scrollLine.getRect(_parentMC)[_length] - _scrollBar.getRect(_parentMC)[_length]);
         }
         else
         {
            _rectangle = new Rectangle(_scrollBarOriginalPoint.x,_scrollBarOriginalPoint.y,_scrollLine.getRect(_parentMC)[_length] - _scrollBar.getRect(_parentMC)[_length],0);
         }
         _scrollBar.addEventListener("mouseDown",scrollBarMouseDownHandler,false,0,true);
         _scrollBar.addEventListener("mouseUp",scrollBarMouseUpHandler,false,0,true);
      }
      
      private function scrollBarLength() : void
      {
         if(_elastic)
         {
            try
            {
               _scrollBar.scale9Grid = _scale9Grid;
            }
            catch(e:Event)
            {
               _scrollBar.scale9Grid = null;
            }
         }
         else
         {
            _scrollBar.scale9Grid = null;
            _scrollBar.width = _oldLength.x;
            _scrollBar.height = _oldLength.y;
            _scrollBar[_length] = _elastic ? _scrollLine[_length] * _maskTarget[_length] / _target[_length] : _oldLength[_coor];
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         _scrollBar.addEventListener("removedFromStage",removeFromStage,false,0,true);
         _scrollBar.parent.stage.addEventListener("mouseUp",scrollBarMouseUpHandler,false,0,true);
         _scrollBar.root.addEventListener("mouseLeave",scrollBarMouseUpHandler,false,0,true);
         if(_mouseWheel && _abled)
         {
            _parentMC.stage.addEventListener("mouseWheel",mouseWheelHandler,false,0,true);
         }
         if(_state == "start")
         {
            _parentMC.stage.addEventListener("mouseWheel",mouseWheelHandler,false,0,true);
         }
      }
      
      private function removeFromStage(param1:Event) : void
      {
         _scrollBar.removeEventListener("removedFromStage",removeFromStage,false);
         _scrollBar.parent.stage.removeEventListener("mouseUp",scrollBarMouseUpHandler,false);
         _scrollBar.root.removeEventListener("mouseLeave",scrollBarMouseUpHandler,false);
         if(_mouseWheel && _abled)
         {
            _parentMC.stage.removeEventListener("mouseWheel",mouseWheelHandler,false);
         }
         if(_state == "start")
         {
            _parentMC.stage.removeEventListener("mouseWheel",mouseWheelHandler,false);
         }
      }
      
      private function scrollBarMouseDownHandler(param1:MouseEvent) : void
      {
         _distanceX = _scrollBar.x - _parentMC.mouseX;
         _distanceY = _scrollBar.y - _parentMC.mouseY;
         _scrollBar.addEventListener("enterFrame",scrolBarEnterFrameHandler,false,0,true);
      }
      
      private function scrollBarMouseUpHandler(param1:*) : void
      {
         _scrollBar.removeEventListener("enterFrame",scrolBarEnterFrameHandler);
      }
      
      private function scrolBarEnterFrameHandler(param1:Event) : void
      {
         makeDragBar();
      }
      
      private function timeHandler(param1:TimerEvent) : void
      {
         scrollMachine();
      }
      
      private function scrollLineMouseDownHandler(param1:MouseEvent) : void
      {
         if(_parentMC[_mouse] > _scrollBar[_coor])
         {
            var _loc2_:* = _coor;
            var _loc3_:* = _scrollBar[_loc2_] + 3 * _speed;
            _scrollBar[_loc2_] = _loc3_;
         }
         else
         {
            _loc3_ = _coor;
            _loc2_ = _scrollBar[_loc3_] - 3 * _speed;
            _scrollBar[_loc3_] = _loc2_;
         }
         judgeBoundary();
      }
      
      private function upDownBtnMouseDownHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget == _downBtn)
         {
            var _loc2_:* = _coor;
            var _loc3_:* = _scrollBar[_loc2_] + 3 * _speed;
            _scrollBar[_loc2_] = _loc3_;
         }
         else
         {
            _loc3_ = _coor;
            _loc2_ = _scrollBar[_loc3_] - 3 * _speed;
            _scrollBar[_loc3_] = _loc2_;
         }
         judgeBoundary();
      }
      
      private function mouseWheelHandler(param1:MouseEvent) : void
      {
         if(param1.delta < 0)
         {
            var _loc2_:* = _coor;
            var _loc3_:* = _scrollBar[_loc2_] + _speed;
            _scrollBar[_loc2_] = _loc3_;
         }
         else
         {
            _loc3_ = _coor;
            _loc2_ = _scrollBar[_loc3_] - _speed;
            _scrollBar[_loc3_] = _loc2_;
         }
         judgeBoundary();
      }
      
      private function makeDragBar() : void
      {
         _scrollBar.x = _parentMC.mouseX + _distanceX;
         _scrollBar.y = _parentMC.mouseY + _distanceY;
         judgeBoundary();
      }
      
      private function judgeBoundary() : void
      {
         if(_scrollBar.x < _rectangle.x)
         {
            _scrollBar.x = _rectangle.x;
         }
         if(_scrollBar.x > _rectangle.right)
         {
            _scrollBar.x = _rectangle.right;
         }
         if(_scrollBar.y < _rectangle.y)
         {
            _scrollBar.y = _rectangle.y;
         }
         if(_scrollBar.y > _rectangle.bottom)
         {
            _scrollBar.y = _rectangle.bottom;
         }
      }
      
      private function scrollMachine() : void
      {
         _targetPoint = _maskTarget[_coor] - (_scrollBar[_coor] - _scrollBarOriginalPoint[_coor]) * (_target[_length] - _maskTarget[_length]) / (_scrollLine[_length] - _scrollBar[_length]);
         if(Math.abs(_target[_coor] - _targetPoint) < 0.3)
         {
            if(_target[_coor] != _targetPoint)
            {
               _target[_coor] = _targetPoint;
            }
            return;
         }
         if(_tween != 0)
         {
            var _loc1_:String = _coor;
            var _loc2_:* = _target[_loc1_] + (_targetPoint - _target[_coor]) / _tween;
            _target[_loc1_] = _loc2_;
         }
         else
         {
            _target[_coor] = _targetPoint;
         }
      }
      
      private function drawMaskTarget(param1:Rectangle) : Sprite
      {
         var _loc2_:Sprite = new Sprite();
         _loc2_.graphics.beginFill(16777215);
         _loc2_.graphics.drawRect(param1.x,param1.y,param1.width,param1.height);
         _loc2_.graphics.endFill();
         return _loc2_;
      }
      
      private function setRegistration(param1:DisplayObjectContainer) : void
      {
         var _loc7_:* = 0;
         var _loc4_:DisplayObject = null;
         var _loc5_:Rectangle = param1.getRect(param1);
         var _loc2_:Number = _loc5_.x;
         var _loc3_:Number = _loc5_.y;
         var _loc6_:Number = param1.numChildren;
         if(_loc6_ == 0)
         {
            return;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc4_ = param1.getChildAt(_loc7_);
            _loc4_.x = _loc4_.x - _loc2_;
            _loc4_.y -= _loc3_;
            _loc7_++;
         }
         if(param1.parent != null)
         {
            param1.x += _loc2_;
            param1.y += _loc3_;
         }
      }
   }
}

