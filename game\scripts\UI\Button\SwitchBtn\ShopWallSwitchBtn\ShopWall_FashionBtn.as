package UI.Button.SwitchBtn.ShopWallSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class ShopWall_FashionBtn extends SwitchBtn
   {
      public function ShopWall_FashionBtn()
      {
         super();
         setTipString("时装");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopWallSwitchBtn"));
      }
   }
}

