package UI.TaskPanel.Bar
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Task.TaskFunction;
   import UI.TaskPanel.InTaskPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   
   public class SignTaskBar extends MySprite implements ITaskBar
   {
      private var _show:Sprite;
      
      private var _bar:CMSXChangeBarLogicShell;
      
      private var _btn:ButtonLogicShell2;
      
      private var _inTaskPanel:InTaskPanel;
      
      public function SignTaskBar()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         _show = null;
         if(_bar)
         {
            _bar.clear();
         }
         _bar = null;
         if(_btn)
         {
            _btn.clear();
         }
         _btn = null;
         _inTaskPanel = null;
      }
      
      public function change(param1:Number, param2:int = 0) : void
      {
         _bar.change(param1);
         _bar.setDataShow(Math.round(param1 * param2) + "/" + param2);
      }
      
      public function setInTaskPanel(param1:InTaskPanel) : void
      {
         _inTaskPanel = param1;
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("BarForSignTask") as Sprite;
         addChild(_show);
         _bar = new CMSXChangeBarLogicShell();
         _bar.setShow(_show);
         _btn = new ButtonLogicShell2();
         _btn.setShow(_show["btn"]);
         _btn.setTipString("点击签到");
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickBtn,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickBtn,true);
      }
      
      private function clickBtn(param1:ButtonEvent) : void
      {
         TaskFunction.getInstance().addTaskGoalVOByString("gotoQunZu");
         navigateToURL(new URLRequest("http://my.4399.com/forums-thread-tagid-81260-id-45495350.html"),"_blank");
         if(_inTaskPanel)
         {
            _inTaskPanel.refreshTaskData(_inTaskPanel.currentTaskVO);
         }
      }
   }
}

