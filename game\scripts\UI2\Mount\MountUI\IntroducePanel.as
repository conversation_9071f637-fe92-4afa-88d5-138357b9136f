package UI2.Mount.MountUI
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import flash.display.MovieClip;
   
   public class IntroducePanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_closeSubUI:CloseSubUI;
      
      public function IntroducePanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         super.clear();
      }
      
      public function init(param1:CloseSubUI) : void
      {
         m_closeSubUI = param1;
         m_show = MyFunction2.returnShowByClassName("IntroducePanel") as MovieClip;
         addChild(m_show);
         initShow();
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(m_show["quitBtn"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_closeSubUI.close();
         }
      }
   }
}

