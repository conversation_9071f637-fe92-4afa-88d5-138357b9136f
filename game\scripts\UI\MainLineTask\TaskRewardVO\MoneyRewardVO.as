package UI.MainLineTask.TaskRewardVO
{
   public class MoneyRewardVO extends TaskRewardVO
   {
      private var _money:int;
      
      public function MoneyRewardVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.money = _money;
      }
      
      public function getMoney() : int
      {
         return _antiwear.money;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         _antiwear.money = int(param1.@value);
      }
   }
}

