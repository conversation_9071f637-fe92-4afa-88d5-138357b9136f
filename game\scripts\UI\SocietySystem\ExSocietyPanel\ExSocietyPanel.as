package UI.SocietySystem.ExSocietyPanel
{
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MySprite;
   import UI.SocietySystem.SeverLink.InformationBody;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   import UI.SocietySystem.SeverLink.SocketListener;
   import UI.SocietySystem.SeverLink.SocketManager;
   import UI.SocietySystem.SocialChatPanel.NewGotChatInfor;
   import UI.SocietySystem.SocialChatPanel.SocietyChatPanel;
   import UI.SocietySystem.SocietySystem;
   import UI.SocietySystem.SocietySystemListener;
   import UI.VersionControl;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class ExSocietyPanel extends MySprite
   {
      private var m_clear:ClearHelper;
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_socketListener:SocketListener;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_societyChatPanel:SocietyChatPanel;
      
      private var m_openChatBtn:ButtonLogicShell2;
      
      private var m_hideChatBtn:ButtonLogicShell2;
      
      private var m_remainTimeOfUnableShow:MultiPlaceNumLogicShell;
      
      private var m_unaleSendChatInterval:int;
      
      private var m_isUnableSendChat:Boolean;
      
      private var m_societySystemListener:SocietySystemListener;
      
      private var m_haveNewChatInforTip:Sprite;
      
      private var m_societySystem:SocietySystem;
      
      private var m_socketManager:SocketManager;
      
      private var m_gamingUI:GamingUI;
      
      private var m_societySystemXML:XML;
      
      private var m_versionControl:VersionControl;
      
      public function ExSocietyPanel()
      {
         super();
         m_clear = new ClearHelper();
         addEventListener("clickButton",clickButton,true,0,true);
         m_societySystemListener = new SocietySystemListener();
         m_societySystemListener.downGetChatInforFun = downGetChatInfor;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         removeEventListener("clickButton",clickButton,true);
         super.clear();
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_socketListener);
         m_socketListener = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.clearObject(m_societyChatPanel);
         m_societyChatPanel = null;
         ClearUtil.clearObject(m_openChatBtn);
         m_openChatBtn = null;
         ClearUtil.clearObject(m_hideChatBtn);
         m_hideChatBtn = null;
         ClearUtil.clearObject(m_remainTimeOfUnableShow);
         m_remainTimeOfUnableShow = null;
         ClearUtil.clearObject(m_societySystemListener);
         m_societySystemListener = null;
         ClearUtil.clearObject(m_haveNewChatInforTip);
         m_haveNewChatInforTip = null;
         m_societySystem = null;
         m_socketManager = null;
         m_gamingUI = null;
         m_societySystemXML = null;
         m_versionControl = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
         m_unaleSendChatInterval = int(m_societySystemXML.chatData[0].@unaleSendChatInterval);
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         if(m_societySystem)
         {
            m_societySystem.removeSocietySystemListener(m_societySystemListener);
         }
         m_societySystem = param1;
         m_societySystem.addSocietySystemListener(m_societySystemListener);
      }
      
      public function setSocketManager(param1:SocketManager) : void
      {
         if(Boolean(m_socketManager) && m_socketListener)
         {
            m_socketManager.removeSocketListener(m_socketListener);
         }
         m_socketManager = param1;
         if(m_socketListener)
         {
            m_socketManager.addSocketListener(m_socketListener);
         }
      }
      
      public function init() : void
      {
         m_myLoader = new YJFYLoader();
         m_loadUI = Part1.getInstance().getLoadUI();
         stage.addChild(m_loadUI);
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_loadUI.tranToTransparentcy();
         if(m_socketListener == null)
         {
            m_socketListener = new SocketListener();
            m_socketListener.getSocketDataFun = getSocketData;
            m_socketListener.ioErrorFun = ioError;
            m_socketListener.securityErrorFun = securityError;
            m_socketManager.addSocketListener(m_socketListener);
         }
         if(m_show == null)
         {
            m_myLoader.getClass("ExSocietyPanel.swf","ExSocietyPanel",getExSocietyPanelSuccess,getFail);
            m_myLoader.load();
         }
         else
         {
            initShow2();
         }
      }
      
      public function showChatPanel() : void
      {
         initFrame1();
      }
      
      public function hideChatPanel() : void
      {
         initFrame2();
      }
      
      public function refreshRemainTimeOfUnableSendChat() : void
      {
         var _loc1_:int = caculateRemainTime(GamingUI.getInstance().getEnterFrameTime());
         if(_loc1_ > 0)
         {
            if(m_remainTimeOfUnableShow)
            {
               m_remainTimeOfUnableShow.getShow().visible = true;
               m_remainTimeOfUnableShow.showNum(_loc1_);
            }
            m_isUnableSendChat = true;
         }
         else
         {
            if(m_remainTimeOfUnableShow)
            {
               m_remainTimeOfUnableShow.getShow().visible = false;
            }
            m_isUnableSendChat = false;
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc2_:int = 0;
         if(m_societyChatPanel)
         {
            m_societyChatPanel.render(param1);
         }
         if(m_isUnableSendChat)
         {
            _loc2_ = caculateRemainTime(param1);
            if(_loc2_ <= 0)
            {
               m_isUnableSendChat = false;
               if(m_remainTimeOfUnableShow)
               {
                  m_remainTimeOfUnableShow.getShow().visible = false;
               }
               return;
            }
            if(m_remainTimeOfUnableShow)
            {
               m_remainTimeOfUnableShow.showNum(_loc2_);
            }
         }
      }
      
      private function getExSocietyPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         initShow2();
      }
      
      private function initShow2() : void
      {
         initFrame2();
      }
      
      private function caculateRemainTime(param1:EnterFrameTime) : int
      {
         return m_unaleSendChatInterval - (param1.getOnLineTimeForThisInit() - m_societySystem.getUnAbleSendChatTime()) / 1000;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_hideChatBtn:
               hideChatPanel();
               break;
            case m_openChatBtn:
               showChatPanel();
         }
      }
      
      private function clearFrame() : void
      {
         ClearUtil.clearObject(m_societyChatPanel);
         m_societyChatPanel = null;
         ClearUtil.clearObject(m_openChatBtn);
         m_openChatBtn = null;
         ClearUtil.clearObject(m_hideChatBtn);
         m_hideChatBtn = null;
         ClearUtil.clearObject(m_remainTimeOfUnableShow);
         m_remainTimeOfUnableShow = null;
      }
      
      private function initFrame1() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("showChatPanel");
         m_societyChatPanel = new SocietyChatPanel();
         m_societyChatPanel.set4399API(m_gamingUI.getAPI4399());
         m_societyChatPanel.setLoader(m_myLoader);
         m_societyChatPanel.setSocietySystem(m_societySystem);
         m_societyChatPanel.setSocietySystemXML(m_societySystemXML);
         m_societyChatPanel.setShow(m_show["chatPanel"],"out");
         m_hideChatBtn = new ButtonLogicShell2();
         m_hideChatBtn.setShow(m_show["hideChatBtn"]);
         m_hideChatBtn.setTipString("隐藏聊天框");
      }
      
      private function initFrame2() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("hideChatPanel");
         m_openChatBtn = new ButtonLogicShell2();
         m_openChatBtn.setShow(m_show["openChatBtn"]);
         m_openChatBtn.setTipString("打开聊天框");
         m_openChatBtn.getShow().visible = false;
         m_openChatBtn.getShow().mouseEnabled = false;
         m_remainTimeOfUnableShow = new MultiPlaceNumLogicShell();
         m_remainTimeOfUnableShow.setShow(m_show["remainTimeOfUnableShow"]);
         m_remainTimeOfUnableShow.setIsShowZero(false);
         m_haveNewChatInforTip = m_show["haveNewChatInforTip"];
         m_haveNewChatInforTip.visible = false;
         var _loc1_:int = caculateRemainTime(GamingUI.getInstance().getEnterFrameTime());
         if(_loc1_ > 0)
         {
            if(m_remainTimeOfUnableShow)
            {
               m_remainTimeOfUnableShow.getShow().visible = true;
               m_remainTimeOfUnableShow.showNum(_loc1_);
            }
            m_isUnableSendChat = true;
         }
         else
         {
            if(m_remainTimeOfUnableShow)
            {
               m_remainTimeOfUnableShow.getShow().visible = false;
            }
            m_isUnableSendChat = false;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("加载显示失败");
      }
      
      private function getSocketData(param1:InformationBody) : void
      {
         var _loc3_:DOWN_GetChatInfor = null;
         var _loc2_:NewGotChatInfor = null;
         if(m_clear == null)
         {
            return;
         }
         m_societySystem.addWasteInforBody(param1);
         switch(param1.id_informationBody - 3043)
         {
            case 0:
               _loc3_ = param1.getDetail() as DOWN_GetChatInfor;
               if(m_societyChatPanel)
               {
                  _loc2_ = new NewGotChatInfor();
                  _loc2_.init(_loc3_);
                  m_societyChatPanel.getOutputRichText().appendRichText(_loc2_.getChatHeadStr(),_loc2_.getChatHeadFontColor(),_loc2_.getChatHeadFontSize());
                  m_societyChatPanel.getOutputRichText().appendRichText(_loc2_.getChatStr(),_loc2_.getChatFontColor(),_loc2_.getChatFontSize());
                  ClearUtil.clearObject(_loc2_);
                  _loc2_ = null;
                  break;
               }
         }
      }
      
      private function ioError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("输入/输出流发生错误，导致加载失败");
      }
      
      private function securityError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("安全错误，导致加载失败");
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      private function downGetChatInfor(param1:DOWN_GetChatInfor) : void
      {
         m_haveNewChatInforTip.visible = true;
      }
      
      public function hideHaveNewChatInforTip() : void
      {
         if(m_haveNewChatInforTip)
         {
            m_haveNewChatInforTip.visible = false;
         }
      }
   }
}

