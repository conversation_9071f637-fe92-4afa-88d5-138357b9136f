package UI.WorldBoss.AnimationQueueData.AnimationData
{
   import UI.WorldBoss.Boss.Boss;
   
   public class DaZhaoAnimationData extends AttackAnimationData
   {
      public function DaZhaoAnimationData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function playAnimation() : void
      {
         (attackEntity as Boss).playDaZhaoAnimation(this,playEnd);
      }
   }
}

