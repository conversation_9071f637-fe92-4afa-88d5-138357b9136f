package UI.Event
{
   import flash.events.Event;
   
   public class UIDataEvent extends Event
   {
      public static const BUY_EQUIPMNT:String = "buyEquipment";
      
      public static const CANCEL_BUY:String = "cancelBuy";
      
      public static const SHOW_BOX:String = "showBox";
      
      public static const CHANGE_OF_POSITION:String = "changeOfPosition";
      
      public static const DRAG_SELL_EQUIPMENT:String = "dragSellEquipment";
      
      public var data:*;
      
      public function UIDataEvent(param1:String, param2:* = null, param3:Boolean = false, param4:<PERSON>olean = false)
      {
         this.data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new UIDataEvent(type,data,bubbles,cancelable);
      }
   }
}

