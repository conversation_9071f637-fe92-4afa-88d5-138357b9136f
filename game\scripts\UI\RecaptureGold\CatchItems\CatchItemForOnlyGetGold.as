package UI.RecaptureGold.CatchItems
{
   import UI.MessageTextShow.MessageTextAnimationFunObject;
   import UI.MessageTextShow.MessageTextFilters;
   import UI.MessageTextShow.MessageTextFormat;
   import UI.MessageTextShow.MessageTextShow;
   import UI.MyFunction;
   import UI.RecaptureGold.Parent.CatchItem;
   import UI.RecaptureGold.RecaptureGoldFunction;
   import UI.SoundManager.SoundManager;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import flash.utils.getQualifiedClassName;
   
   public class CatchItemForOnlyGetGold extends CatchItem
   {
      public function CatchItemForOnlyGetGold()
      {
         super();
      }
      
      public function getFunForEnbleGetGoldItem(param1:XML, param2:Sprite, param3:SoundManager, param4:Number = 1) : Number
      {
         var _loc8_:int = 0;
         var _loc5_:XML = null;
         var _loc7_:String = getQualifiedClassName(this);
         var _loc14_:XMLList = param1.TargetData[0].item;
         var _loc11_:int = int(_loc14_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc11_)
         {
            if(_loc14_[_loc8_].@classNameForCatch == _loc7_)
            {
               _loc5_ = _loc14_[_loc8_];
            }
            _loc8_++;
         }
         var _loc9_:Point = this.parent.localToGlobal(new Point(this.x,this.y));
         var _loc15_:Vector.<int> = MyFunction.getInstance().excreteString(_loc5_.@getGoldNum);
         var _loc12_:int = _loc15_[0] + Math.round(Math.random() * (_loc15_[1] - _loc15_[0]));
         _loc12_ = _loc12_ * param4;
         var _loc13_:MessageTextShow = new MessageTextShow("金子 +" + _loc12_,MessageTextFormat.TEXT_FORMAT_1,MessageTextFilters.FILTERS_1,["from","to"],[0.5,2],[MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_3,MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_1]);
         _loc13_.x = _loc9_.x;
         _loc13_.y = _loc9_.y;
         param2.addChild(_loc13_);
         _loc13_.play();
         var _loc6_:String = String(param1.SoundData[0].@soundForGetGold);
         var _loc10_:SoundTransform = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc9_,param2);
         param3.play(_loc6_,0,0,_loc10_);
         return _loc12_ * param4;
      }
   }
}

