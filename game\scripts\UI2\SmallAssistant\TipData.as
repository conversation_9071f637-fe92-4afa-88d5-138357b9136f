package UI2.SmallAssistant
{
   public class TipData
   {
      private var m_text1:String;
      
      private var m_skipStr:String;
      
      public function TipData(param1:String, param2:String)
      {
         super();
         m_text1 = param1;
         m_skipStr = param2;
      }
      
      public function getText1() : String
      {
         return m_text1;
      }
      
      public function getSkipStr() : String
      {
         return m_skipStr;
      }
   }
}

