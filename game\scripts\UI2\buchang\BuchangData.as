package UI2.buchang
{
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class BuchangData
   {
      private static var _instance:BuchangData;
      
      private var dataTime:String;
      
      private var m_bShow:int;
      
      private var m_bGet:int;
      
      public function BuchangData()
      {
         super();
      }
      
      public static function getInstance() : BuchangData
      {
         if(_instance == null)
         {
            _instance = new BuchangData();
         }
         return _instance;
      }
      
      public function getBShow() : int
      {
         return m_bShow;
      }
      
      public function setBShow(param1:int) : void
      {
         m_bShow = param1;
      }
      
      public function getBGet() : int
      {
         return m_bGet;
      }
      
      public function setBGet(param1:int) : void
      {
         m_bGet = param1;
      }
      
      public function initSaveData(param1:XML) : void
      {
         if(param1.hasOwnProperty("buchangdata"))
         {
            dataTime = String(param1.buchangdata[0].@time);
            m_bShow = int(param1.buchangdata[0].@bshow);
            m_bGet = int(param1.buchangdata[0].@bget);
         }
         else
         {
            m_bShow = 2;
            m_bGet = 2;
            dataTime = TimeUtil.getTimeUtil().getTimeStr();
         }
      }
      
      public function exploreData() : XML
      {
         var _loc1_:XML = <buchangdata />;
         if(dataTime)
         {
            _loc1_.@time = dataTime;
         }
         _loc1_.@bshow = String(m_bShow);
         _loc1_.@bget = String(m_bGet);
         return _loc1_;
      }
   }
}

