package UI.WorldBoss.GetNextStepActionEntities
{
   import UI.Players.PlayerVO;
   import UI.WorldBoss.IRoundChangeListener;
   import UI.WorldBoss.PetEntity;
   import UI.WorldBoss.PlayerEntity;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.StepAttackGame.NextEntities;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   
   public class GetNextStepActionEntities1 implements IGetNextStepActionEntities
   {
      private var m_world:StepAttackGameWorld;
      
      private var m_roundNum:int;
      
      private var m_roundListeners:Vector.<IRoundChangeListener>;
      
      public function GetNextStepActionEntities1()
      {
         super();
      }
      
      public function clear() : void
      {
         m_world = null;
         ClearUtil.nullArr(m_roundListeners,false,false,false);
         m_roundListeners = null;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_world = param1;
      }
      
      public function getNextAttackAndBeAttackedEntities() : NextEntities
      {
         var _loc2_:* = undefined;
         var _loc3_:* = undefined;
         var _loc4_:NextEntities = new NextEntities();
         var _loc1_:IEntity = m_world.getCurrentAttackEntity();
         if(_loc1_ is PlayerEntity)
         {
            if((_loc1_ as PlayerEntity).getPlayerVO().pet != null && (_loc1_ as PlayerEntity).getPlayerVO().pet.petEquipmentVO != null)
            {
               _loc2_ = getPetEntity(m_world.getFriendNum,m_world.getFriendByIndex,(_loc1_ as PlayerEntity).getPlayerVO());
               _loc3_ = randomGetBeAttackedEntities(m_world.getEnemyNum,m_world.getEnemyByIndex,1);
            }
            else
            {
               _loc2_ = randomGetAttackEntities(m_world.getEnemyNum,m_world.getEnemyByIndex,1);
               _loc3_ = randomGetBeAttackedEntities(m_world.getFriendNum,m_world.getFriendByIndex,1);
            }
         }
         else if(_loc1_ is PetEntity)
         {
            _loc2_ = randomGetAttackEntities(m_world.getEnemyNum,m_world.getEnemyByIndex,1);
            _loc3_ = randomGetBeAttackedEntities(m_world.getFriendNum,m_world.getFriendByIndex,1);
         }
         else
         {
            m_roundNum++;
            changeRound();
            _loc2_ = randomGetPlayerEntity(m_world.getFriendNum,m_world.getFriendByIndex,1);
            _loc3_ = randomGetBeAttackedEntities(m_world.getEnemyNum,m_world.getEnemyByIndex,1);
         }
         _loc4_.nextAttackEntity = _loc2_[0];
         _loc4_.nextBeAttackedEntities = _loc3_;
         return _loc4_;
      }
      
      private function randomGetBeAttackedEntities(param1:Function, param2:Function, param3:int) : Vector.<IEntity>
      {
         var _loc9_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = 0;
         var _loc8_:int = param1();
         var _loc7_:Vector.<IEntity> = new Vector.<IEntity>();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc9_ = 0;
         while(_loc9_ < _loc8_)
         {
            _loc6_ = param2(_loc9_);
            if(_loc6_ is PetEntity)
            {
               break;
            }
            _loc7_.push(_loc6_);
            _loc9_++;
         }
         param3 = Math.min(_loc8_,param3);
         _loc9_ = 0;
         while(_loc9_ < param3)
         {
            _loc5_ = Math.random() * _loc7_.length;
            _loc4_.push(_loc7_[_loc5_]);
            _loc7_.splice(_loc5_,1);
            _loc9_++;
         }
         return _loc4_;
      }
      
      private function randomGetAttackEntities(param1:Function, param2:Function, param3:int) : Vector.<IEntity>
      {
         var _loc9_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = 0;
         var _loc8_:int = param1();
         var _loc7_:Vector.<IEntity> = new Vector.<IEntity>();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc9_ = 0;
         while(_loc9_ < _loc8_)
         {
            _loc6_ = param2(_loc9_);
            _loc7_.push(_loc6_);
            _loc9_++;
         }
         param3 = Math.min(_loc8_,param3);
         _loc9_ = 0;
         while(_loc9_ < param3)
         {
            _loc5_ = Math.random() * _loc7_.length;
            _loc4_.push(_loc7_[_loc5_]);
            _loc7_.splice(_loc5_,1);
            _loc9_++;
         }
         return _loc4_;
      }
      
      public function getPetEntity(param1:Function, param2:Function, param3:PlayerVO) : Vector.<IEntity>
      {
         var _loc7_:int = 0;
         var _loc5_:IEntity = null;
         var _loc6_:int = param1();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc5_ = param2(_loc7_);
            if(_loc5_ is PetEntity && (_loc5_ as PetEntity).getPetVO() == param3.pet.petEquipmentVO)
            {
               _loc4_.push(_loc5_);
               return _loc4_;
            }
            _loc7_++;
         }
         return _loc4_;
      }
      
      public function randomGetPlayerEntity(param1:Function, param2:Function, param3:int) : Vector.<IEntity>
      {
         var _loc9_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = 0;
         var _loc8_:int = param1();
         var _loc7_:Vector.<IEntity> = new Vector.<IEntity>();
         var _loc4_:Vector.<IEntity> = new Vector.<IEntity>();
         _loc9_ = 0;
         while(_loc9_ < _loc8_)
         {
            _loc6_ = param2(_loc9_);
            if(_loc6_ is PlayerEntity)
            {
               _loc7_.push(_loc6_);
            }
            _loc9_++;
         }
         param3 = Math.min(_loc8_,param3);
         _loc9_ = 0;
         while(_loc9_ < param3)
         {
            _loc5_ = Math.random() * _loc7_.length;
            _loc4_.push(_loc7_[_loc5_]);
            _loc7_.splice(_loc5_,1);
            _loc9_++;
         }
         return _loc4_;
      }
      
      public function getRoundNum() : int
      {
         return m_roundNum;
      }
      
      public function addRoundChangeListener(param1:IRoundChangeListener) : void
      {
         if(m_roundListeners == null)
         {
            m_roundListeners = new Vector.<IRoundChangeListener>();
         }
         m_roundListeners.push(param1);
      }
      
      public function removeRoundChangeListener(param1:IRoundChangeListener) : void
      {
         if(m_roundListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_roundListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_roundListeners.splice(_loc2_,1);
            _loc2_ = int(m_roundListeners.indexOf(param1));
         }
      }
      
      private function changeRound() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IRoundChangeListener> = !!m_roundListeners ? m_roundListeners.slice(0) : null;
         var _loc2_:int = !!_loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].roundChange(m_roundNum);
            }
            _loc3_++;
         }
      }
   }
}

