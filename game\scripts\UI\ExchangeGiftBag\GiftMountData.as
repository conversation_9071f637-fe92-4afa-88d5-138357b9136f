package UI.ExchangeGiftBag
{
   import UI.DataManagerParent;
   
   public class GiftMountData extends DataManagerParent
   {
      private var m_zhaoHuanNum:uint;
      
      private var m_lingShouShiNum:uint;
      
      private var m_pkNum:uint;
      
      private var m_yuanbaoNum:uint;
      
      private var m_expNum:uint;
      
      public function GiftMountData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         zhaoHuanNum = uint(param1.@zhaoHuanNum);
         lingShouShiNum = uint(param1.@lingShouShiNum);
         pkNum = uint(param1.@pkNum);
         yuanbaoNum = uint(param1.@yuanbaoNum);
         expNum = uint(param1.@expNum);
      }
      
      public function getZhaoHuanNum() : uint
      {
         return zhaoHuanNum;
      }
      
      public function getLingShouShiNum() : uint
      {
         return lingShouShiNum;
      }
      
      public function getPkNum() : uint
      {
         return pkNum;
      }
      
      public function getYuanbaoNum() : uint
      {
         return yuanbaoNum;
      }
      
      public function getExpNum() : uint
      {
         return expNum;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.zhaoHuanNum = m_zhaoHuanNum;
         _antiwear.lingShouShiNum = m_lingShouShiNum;
         _antiwear.pkNum = m_pkNum;
         _antiwear.yuanbaoNum = m_yuanbaoNum;
         _antiwear.expNum = m_expNum;
      }
      
      private function get zhaoHuanNum() : uint
      {
         return _antiwear.zhaoHuanNum;
      }
      
      private function set zhaoHuanNum(param1:uint) : void
      {
         _antiwear.zhaoHuanNum = param1;
      }
      
      private function get lingShouShiNum() : uint
      {
         return _antiwear.lingShouShiNum;
      }
      
      private function set lingShouShiNum(param1:uint) : void
      {
         _antiwear.lingShouShiNum = param1;
      }
      
      private function get pkNum() : uint
      {
         return _antiwear.pkNum;
      }
      
      private function set pkNum(param1:uint) : void
      {
         _antiwear.pkNum = param1;
      }
      
      private function get yuanbaoNum() : uint
      {
         return _antiwear.yuanbaoNum;
      }
      
      private function set yuanbaoNum(param1:uint) : void
      {
         _antiwear.yuanbaoNum = param1;
      }
      
      private function get expNum() : uint
      {
         return _antiwear.expNum;
      }
      
      private function set expNum(param1:uint) : void
      {
         _antiwear.expNum = param1;
      }
   }
}

