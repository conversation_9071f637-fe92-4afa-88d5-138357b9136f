package UI.Equipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class DropEquipment extends Equipment
   {
      public function DropEquipment(param1:EquipmentVO)
      {
         super(param1);
         m_path = "drop.";
         setShow(_equipmentVO);
      }
      
      override public function imperfectClone() : Equipment
      {
         return new DropEquipment(equipmentVO);
      }
      
      override public function clone() : Equipment
      {
         return new DropEquipment(equipmentVO.clone());
      }
   }
}

