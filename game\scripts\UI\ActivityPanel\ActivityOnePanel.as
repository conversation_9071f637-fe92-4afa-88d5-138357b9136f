package UI.ActivityPanel
{
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class ActivityOnePanel
   {
      private var m_columes:Vector.<ExchangeColume>;
      
      private var m_show:MovieClip;
      
      private var m_activityOneData:ActivityOneData;
      
      public function ActivityOnePanel()
      {
         super();
         m_columes = new Vector.<ExchangeColume>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_columes);
         m_columes = null;
         m_show = null;
         m_activityOneData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow1();
      }
      
      public function setActivityOneData(param1:ActivityOneData) : void
      {
         m_activityOneData = param1;
         initShow2();
      }
      
      public function refreshShow() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_columes.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_columes[_loc2_].refreshShow();
            _loc2_++;
         }
      }
      
      private function initShow1() : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:ExchangeColume = null;
         var _loc3_:int = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = m_show.getChildAt(_loc5_);
            if(_loc1_.name.substr(0,6) == "colume")
            {
               _loc4_++;
            }
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = new ExchangeColume();
            _loc2_.setActivityOnePanel(this);
            _loc2_.setShow(m_show["colume" + (_loc5_ + 1)]);
            m_columes.push(_loc2_);
            _loc5_++;
         }
         initShow2();
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         if(m_activityOneData == null)
         {
            return;
         }
         var _loc1_:int = m_activityOneData.getExchangeOneDataNum();
         var _loc2_:int = int(m_columes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_ && _loc3_ < _loc2_)
         {
            m_columes[_loc3_].setExchangeOneData(m_activityOneData.getExchangeOneDataByIndex(_loc3_));
            _loc3_++;
         }
      }
   }
}

