package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_RemoveMemberReturnToLeader;
   
   public class TEST_DOWN_RemoveMemberReturnToLeader extends DOWN_RemoveMemberReturnToLeader
   {
      public function TEST_DOWN_RemoveMemberReturnToLeader()
      {
         super();
         m_informationBodyId = 3034;
      }
      
      public function initData(param1:<PERSON><PERSON><PERSON>, param2:Number, param3:int) : void
      {
         m_isSuccess = int(param1);
         m_uid_member = param2;
         m_idx_member = param3;
      }
   }
}

