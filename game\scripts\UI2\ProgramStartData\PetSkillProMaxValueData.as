package UI2.ProgramStartData
{
   import UI.DataManagerParent;
   
   public class PetSkillProMaxValueData extends DataManagerParent
   {
      private var m_petSkillName:String;
      
      private var m_petSkillProMaxValue:uint;
      
      public function PetSkillProMaxValueData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_petSkillName = null;
         super.clear();
      }
      
      public function initData(param1:String, param2:uint) : void
      {
         this.petSkillName = param1;
         this.petSkillProMaxValue = param2;
      }
      
      public function getPetSkillName() : String
      {
         return this.petSkillName;
      }
      
      public function getPetSkillProMaxValue() : uint
      {
         return this.petSkillProMaxValue;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.petSkillName = m_petSkillName;
         _antiwear.petSkillProMaxValue = m_petSkillProMaxValue;
      }
      
      private function get petSkillName() : String
      {
         return _antiwear.petSkillName;
      }
      
      private function set petSkillName(param1:String) : void
      {
         _antiwear.petSkillName = param1;
      }
      
      private function get petSkillProMaxValue() : uint
      {
         return _antiwear.petSkillProMaxValue;
      }
      
      private function set petSkillProMaxValue(param1:uint) : void
      {
         _antiwear.petSkillProMaxValue = param1;
      }
   }
}

