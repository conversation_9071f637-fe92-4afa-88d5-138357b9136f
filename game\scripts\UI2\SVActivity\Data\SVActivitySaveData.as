package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.MyFunction;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class SVActivitySaveData extends DataManagerParent
   {
      private var m_isOpened:Boolean;
      
      private var m_isBuy:Boolean;
      
      private var m_getRewardTime:String;
      
      private var m_getGiftBagIdsStr:String;
      
      private var m_isBuy_xinJueSeLiBao:Boolean;
      
      private var m_isBuy1:Boolean;
      
      private var m_getRewardTime1:String;
      
      private var m_getGiftBagIdsStr1:String;
      
      private var m_isBuy_xinJueSeLiBao1:Boolean;
      
      public var isResetGiftBagIds:Boolean = false;
      
      private var m_HuanleZhuanpanTimes:int = 0;
      
      private var m_hounianFreeTimes:int = 0;
      
      private var m_hounianContdownTime:int = 43200;
      
      private var m_hounianAddTimes:int = 0;
      
      public function SVActivitySaveData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_getRewardTime = null;
         m_getGiftBagIdsStr = null;
         m_getRewardTime1 = null;
         m_getGiftBagIdsStr1 = null;
         super.clear();
      }
      
      public function initBySaveXML(param1:XML, param2:String) : void
      {
         this.isBuy = false;
         this.isBuy_xinJueSeLiBao = false;
         this.getRewardTime = null;
         this.getGiftBagIdsStr = null;
         this.isBuy1 = false;
         this.isBuy_xinJueSeLiBao1 = false;
         this.getRewardTime1 = null;
         this.getGiftBagIdsStr1 = null;
         this.HuanleZhuanPanTimes = 0;
         this.HuanleZhuanPanUsedTimes = 0;
         this.hounianFreeTimes = 0;
         this.hounianAddTimes = 0;
         this.hounianCountDownTime = 0;
         if(param1.hasOwnProperty("svA") == false)
         {
            return;
         }
         var _loc3_:XML = param1.svA[0];
         if(_loc3_)
         {
            this.isOpened = Boolean(int(_loc3_.@isOpened));
            this.isResetGiftBagIds = Boolean(int(_loc3_.@isResetGiftBagIds));
            this.isBuy = Boolean(int(_loc3_.@isBuy));
            this.getRewardTime = String(_loc3_.@gT);
            this.getGiftBagIdsStr = String(_loc3_.@gIds);
            this.isBuy_xinJueSeLiBao = Boolean(int(_loc3_.@isBuyX));
            this.isBuy1 = Boolean(int(_loc3_.@isBuy20180702));
            this.getRewardTime1 = String(_loc3_.@gT20180702);
            this.getGiftBagIdsStr1 = String(_loc3_.@gIds20180702);
            this.isBuy_xinJueSeLiBao1 = Boolean(int(_loc3_.@isBuyX20180719));
            this.hounianFreeTimes = int(_loc3_.@hounianFreeTimes20180702);
            this.hounianCountDownTime = int(_loc3_.@hounianCountDownTime20180702);
            if(hounianCountDownTime == 0)
            {
               hounianCountDownTime = 43200;
            }
            if(hounianCountDownTime > 43200)
            {
               hounianCountDownTime = 43200;
            }
            this.HuanleZhuanPanTimes = int(_loc3_.@huanletimes20180702);
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <svA />;
         if(this.isOpened)
         {
            _loc1_.@isOpened = int(isOpened);
         }
         if(this.isResetGiftBagIds)
         {
            _loc1_.@isResetGiftBagIds = int(isResetGiftBagIds);
         }
         if(this.isBuy)
         {
            _loc1_.@isBuy = int(isBuy);
         }
         if(getRewardTime)
         {
            _loc1_.@gT = getRewardTime;
         }
         if(getGiftBagIdsStr)
         {
            _loc1_.@gIds = getGiftBagIdsStr;
         }
         if(isBuy_xinJueSeLiBao)
         {
            _loc1_.@isBuyX = int(isBuy_xinJueSeLiBao);
         }
         if(this.isBuy1)
         {
            _loc1_.@isBuy20180702 = int(isBuy1);
         }
         if(getRewardTime1)
         {
            _loc1_.@gT20180702 = getRewardTime1;
         }
         if(getGiftBagIdsStr1)
         {
            _loc1_.@gIds20180702 = getGiftBagIdsStr1;
         }
         if(isBuy_xinJueSeLiBao1)
         {
            _loc1_.@isBuyX20180719 = int(isBuy_xinJueSeLiBao1);
         }
         runCheckOnlineTime();
         if(hounianFreeTimes)
         {
            _loc1_.@hounianFreeTimes20180702 = hounianFreeTimes;
         }
         if(hounianCountDownTime)
         {
            _loc1_.@hounianCountDownTime20180702 = hounianCountDownTime;
         }
         if(HuanleZhuanPanTimes)
         {
            _loc1_.@huanletimes20180702 = HuanleZhuanPanTimes;
         }
         return _loc1_;
      }
      
      public function getIsOpened() : Boolean
      {
         return isOpened;
      }
      
      public function open() : void
      {
         isOpened = true;
      }
      
      public function runCheckOnlineTime() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(!GamingUI.getInstance().getEnterFrameTime())
         {
            return;
         }
         var _loc1_:int = int(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() / 1000) - hounianAddTimes;
         if(_loc1_ % 43200 == hounianCountDownTime)
         {
            hounianCountDownTime = 43200;
            hounianAddTimes += _loc1_;
            hounianFreeTimes++;
            hounianFreeTimes += int(_loc1_ / 43200);
         }
         else if(_loc1_ > hounianCountDownTime)
         {
            _loc3_ = _loc1_ - hounianCountDownTime;
            hounianAddTimes += hounianCountDownTime;
            hounianFreeTimes++;
            _loc2_ = _loc3_ / 43200;
            hounianFreeTimes += _loc2_;
            hounianCountDownTime = 43200 - _loc3_ % 43200;
         }
         else
         {
            hounianCountDownTime -= _loc1_;
            hounianAddTimes += _loc1_;
         }
      }
      
      public function getIsBuy() : Boolean
      {
         return isBuy1;
      }
      
      public function buy() : void
      {
         isBuy1 = true;
      }
      
      public function getIsBuy_xinJueSeLiBao() : Boolean
      {
         return isBuy_xinJueSeLiBao1;
      }
      
      public function buy_xinJueSeLiBao() : void
      {
         isBuy_xinJueSeLiBao1 = true;
      }
      
      public function getGetGiftBagIds() : Vector.<String>
      {
         return MyFunction.getInstance().excreteStringToString(getGiftBagIdsStr1);
      }
      
      public function setGetGiftBagIds(param1:Vector.<String>) : void
      {
         getGiftBagIdsStr1 = MyFunction.getInstance().combineStringsToArr(param1);
      }
      
      public function getIsAbleGetRewardOfChengZhangJiHua_newWeek(param1:String) : Boolean
      {
         return new TimeUtil().newTimeIsNewWeek(getRewardTime1,param1);
      }
      
      public function setNewTimeOfGetRewardOfChengZhangJiHua(param1:String) : void
      {
         getRewardTime1 = param1;
      }
      
      public function isGotThisGiftBagOfJiLeiHaoLi(param1:String) : Boolean
      {
         if(Boolean(getGiftBagIdsStr1) == false)
         {
            return false;
         }
         var _loc3_:Vector.<String> = MyFunction.getInstance().excreteStringToString(getGiftBagIdsStr1);
         var _loc2_:int = int(_loc3_.indexOf(param1));
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
         if(_loc2_ != -1)
         {
            return true;
         }
         return false;
      }
      
      public function addGotGiftBagId(param1:String) : void
      {
         var _loc3_:Vector.<String> = MyFunction.getInstance().excreteStringToString(getGiftBagIdsStr1);
         var _loc2_:int = int(_loc3_.indexOf(param1));
         if(_loc2_ != -1)
         {
            throw new Error("出错了, addGotGiftBagId function in SvActivitySaveData ");
         }
         _loc3_.push(param1);
         getGiftBagIdsStr1 = MyFunction.getInstance().combineStringsToArr(_loc3_);
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
      }
      
      public function getHounianFreeTimes() : int
      {
         return hounianFreeTimes;
      }
      
      public function setHounianFreeTimes(param1:int) : void
      {
         hounianFreeTimes = param1;
         if(hounianFreeTimes < 0)
         {
            hounianFreeTimes = 0;
         }
      }
      
      public function getHounianCountDownTime() : int
      {
         return hounianCountDownTime;
      }
      
      public function getHuanLFreeTimes() : int
      {
         return HuanleZhuanPanTimes;
      }
      
      public function getHuanLUsedTimes() : int
      {
         return HuanleZhuanPanUsedTimes;
      }
      
      public function setHuanLUsedTimes(param1:int) : void
      {
         HuanleZhuanPanUsedTimes = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.isBuy1 = m_isBuy1;
         _antiwear.getRewardTime1 = m_getRewardTime1;
         _antiwear.getGiftBagIdsStr1 = m_getGiftBagIdsStr1;
         _antiwear.isBuy_xinJueSeLiBao1 = m_isBuy_xinJueSeLiBao1;
      }
      
      private function get isOpened() : Boolean
      {
         return m_isOpened;
      }
      
      private function set isOpened(param1:Boolean) : void
      {
         m_isOpened = param1;
      }
      
      private function get isBuy() : Boolean
      {
         return _antiwear.isBuy;
      }
      
      private function set isBuy(param1:Boolean) : void
      {
         _antiwear.isBuy = param1;
      }
      
      private function get getRewardTime() : String
      {
         return _antiwear.getRewardTime;
      }
      
      private function set getRewardTime(param1:String) : void
      {
         _antiwear.getRewardTime = param1;
      }
      
      private function get getGiftBagIdsStr() : String
      {
         return _antiwear.getGiftBagIdsStr;
      }
      
      private function set getGiftBagIdsStr(param1:String) : void
      {
         _antiwear.getGiftBagIdsStr = param1;
      }
      
      private function get isBuy_xinJueSeLiBao() : Boolean
      {
         return _antiwear.isBuy_xinJueSeLiBao;
      }
      
      private function set isBuy_xinJueSeLiBao(param1:Boolean) : void
      {
         _antiwear.isBuy_xinJueSeLiBao = param1;
      }
      
      private function get HuanleZhuanPanTimes() : int
      {
         return _antiwear.m_HuanleZhuanpanTimes;
      }
      
      private function set HuanleZhuanPanTimes(param1:int) : void
      {
         _antiwear.m_HuanleZhuanpanTimes = param1;
      }
      
      private function get HuanleZhuanPanUsedTimes() : int
      {
         return _antiwear.HuanleZhuanPanUsedTimes;
      }
      
      private function set HuanleZhuanPanUsedTimes(param1:int) : void
      {
         _antiwear.HuanleZhuanPanUsedTimes = param1;
      }
      
      private function get isBuy1() : Boolean
      {
         return _antiwear.isBuy1;
      }
      
      private function set isBuy1(param1:Boolean) : void
      {
         _antiwear.isBuy1 = param1;
      }
      
      private function get getRewardTime1() : String
      {
         return _antiwear.getRewardTime1;
      }
      
      private function set getRewardTime1(param1:String) : void
      {
         _antiwear.getRewardTime1 = param1;
      }
      
      private function get getGiftBagIdsStr1() : String
      {
         return _antiwear.getGiftBagIdsStr1;
      }
      
      private function set getGiftBagIdsStr1(param1:String) : void
      {
         _antiwear.getGiftBagIdsStr1 = param1;
      }
      
      private function get isBuy_xinJueSeLiBao1() : Boolean
      {
         return _antiwear.isBuy_xinJueSeLiBao1;
      }
      
      private function set isBuy_xinJueSeLiBao1(param1:Boolean) : void
      {
         _antiwear.isBuy_xinJueSeLiBao1 = param1;
      }
      
      private function get hounianFreeTimes() : int
      {
         return _antiwear.m_hounianFreeTimes;
      }
      
      private function set hounianFreeTimes(param1:int) : void
      {
         _antiwear.m_hounianFreeTimes = param1;
      }
      
      private function get hounianCountDownTime() : int
      {
         return _antiwear.m_hounianContdownTime;
      }
      
      private function set hounianCountDownTime(param1:int) : void
      {
         _antiwear.m_hounianContdownTime = param1;
      }
      
      private function get hounianAddTimes() : int
      {
         return _antiwear.m_hounianAddTimes;
      }
      
      private function set hounianAddTimes(param1:int) : void
      {
         _antiwear.m_hounianAddTimes = param1;
      }
   }
}

