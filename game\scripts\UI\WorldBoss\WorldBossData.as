package UI.WorldBoss
{
   import UI.DataManagerParent;
   import UI.EnterFrameTime;
   import UI.Players.VipVO;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class WorldBossData extends DataManagerParent
   {
      private var m_worldBossSaveData:WorldBossSaveData;
      
      private var m_powerValue:Number = 0;
      
      private var m_maxPowerValue:int;
      
      private var m_recoverPowerValuePerS:Number;
      
      private var m_lastGameTime:Number;
      
      public function WorldBossData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_worldBossSaveData = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.powerValue = m_powerValue;
         _antiwear.maxPowerValue = m_maxPowerValue;
         _antiwear.recoverPowerValuePerS = m_recoverPowerValuePerS;
      }
      
      private function get powerValue() : Number
      {
         return _antiwear.powerValue;
      }
      
      private function set powerValue(param1:Number) : void
      {
         _antiwear.powerValue = param1;
      }
      
      private function get maxPowerValue() : int
      {
         return _antiwear.maxPowerValue;
      }
      
      private function set maxPowerValue(param1:int) : void
      {
         _antiwear.maxPowerValue = param1;
      }
      
      private function get recoverPowerValuePerS() : Number
      {
         return _antiwear.recoverPowerValuePerS;
      }
      
      private function set recoverPowerValuePerS(param1:Number) : void
      {
         _antiwear.recoverPowerValuePerS = param1;
      }
      
      public function getPowerValue() : Number
      {
         return powerValue;
      }
      
      public function setPowerValue(param1:Number) : void
      {
         powerValue = Math.max(0,Math.min(maxPowerValue,param1));
      }
      
      public function getMaxPowerValue() : int
      {
         return maxPowerValue;
      }
      
      public function setMaxPowerValue(param1:int) : void
      {
         maxPowerValue = param1;
      }
      
      public function getBossHurtDataNum() : int
      {
         return m_worldBossSaveData.getBossHurtDataNum();
      }
      
      public function getBossHurtDataByIndex(param1:int) : BossHurtData
      {
         return m_worldBossSaveData.getBossHurtDataByIndex(param1);
      }
      
      public function getBossHurtDataByBossId(param1:String) : BossHurtData
      {
         return m_worldBossSaveData.getBossHurtDataByBossId(param1);
      }
      
      public function addBossHurtData(param1:BossHurtData) : void
      {
         m_worldBossSaveData.addBossHurtData(param1);
      }
      
      public function addPromoteAttackPercent(param1:Number) : void
      {
         m_worldBossSaveData.addPromoteAttackPercent(param1);
      }
      
      public function getProAttackPercent() : Number
      {
         return m_worldBossSaveData.getProAttackPercent();
      }
      
      public function getBuyNum() : int
      {
         return m_worldBossSaveData.getBuyNum();
      }
      
      public function setZeroPromoteAttackPercent() : void
      {
         m_worldBossSaveData.setZeroPromoteAttackPercent();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(isNaN(m_lastGameTime))
         {
            m_lastGameTime = param1.getOnLineTimeForThisInit();
         }
         var _loc2_:Number = param1.getOnLineTimeForThisInit() - m_lastGameTime;
         setPowerValue(getPowerValue() + _loc2_ / 1000 * recoverPowerValuePerS);
         m_lastGameTime = param1.getOnLineTimeForThisInit();
      }
      
      public function initFromSaveData(param1:int, param2:String, param3:VipVO, param4:String, param5:XML, param6:WorldBossSaveData) : void
      {
         maxPowerValue = int(param5.@maxPowerValue) + param3.addWorldBossPowerValue;
         recoverPowerValuePerS = Number(param5.@recoverPowerValuePerS);
         m_worldBossSaveData = param6;
         var _loc7_:int = new TimeUtil().timeInterval(param2,param4) * 3600 * recoverPowerValuePerS;
         powerValue = Math.max(0,Math.min(maxPowerValue,param1 + _loc7_));
      }
      
      public function getSaveData() : WorldBossSaveData
      {
         return m_worldBossSaveData;
      }
   }
}

