package UI.EquipmentMakeAndUpgrade
{
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.getQualifiedClassName;
   
   public class GemInsetPanel extends MySprite implements IEquipmentProcessPanel
   {
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _oldContainer:Sprite;
      
      private var _gemContainer:Sprite;
      
      private var _luckStoneContainer:Sprite;
      
      private var _newContainer:Sprite;
      
      private var _numBtnGroup:NumberBtnGroupLogicShell;
      
      private var _successRateText:TextField;
      
      private var _moneyText:TextField;
      
      private var _startInsetBtn:ButtonLogicShell2;
      
      private var _successAnimation:MovieClipPlayLogicShell;
      
      private var _failAnimation:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["insetPanel"];
      
      private var _gemInsetXML:XML;
      
      private var _oldEquipment:Equipment;
      
      private var _newEquipment:Equipment;
      
      private var _player:Player;
      
      private var _baseSuccessRate:Number;
      
      private var _successRate:Number;
      
      private var _needMoney:int;
      
      private var _holeIndex:int;
      
      private var _gemEquipment:Equipment;
      
      private var _currentInsetXML:XML;
      
      private var _luckStoneShow:LuckStoneShow;
      
      private var _buyLuckStoneGuideBtn:BuyMaterialGuideBtn;
      
      private var _buyGoldPocketBtn:BuyGoldPocketGuideBtn;
      
      private var _MyControlPanel:MyControlPanel;
      
      private var _equipPosClass:Array;
      
      public function GemInsetPanel(param1:MyControlPanel = null)
      {
         super();
         _MyControlPanel = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         ClearUtil.clearDisplayObjectInContainer(_oldContainer);
         _oldContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_gemContainer);
         _gemContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer);
         _luckStoneContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         _successRateText = null;
         _moneyText = null;
         if(_startInsetBtn)
         {
            _startInsetBtn.clear();
         }
         _startInsetBtn = null;
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         _successAnimation = null;
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         _failAnimation = null;
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         _gemInsetXML = null;
         if(_oldEquipment)
         {
            _oldEquipment.clear();
         }
         _oldEquipment = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         _player = null;
         if(_gemEquipment)
         {
            _gemEquipment.clear();
         }
         _gemEquipment = null;
         _currentInsetXML = null;
         _luckStoneShow = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
         _equipPosClass = null;
      }
      
      public function isCanPutEquipmentVO(param1:EquipmentVO) : IsCanPutInfo
      {
         var _loc3_:int = 0;
         var _loc5_:String = null;
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:IsCanPutInfo = new IsCanPutInfo();
         if(param1 is InsetGemEquipmentVO)
         {
            if(_oldEquipment == null)
            {
               _loc2_.isCanPut = false;
               _loc2_.message = "请先放入有孔的装备！";
            }
            else if(_oldEquipment.equipmentVO)
            {
               _loc5_ = getQualifiedClassName(_oldEquipment.equipmentVO);
               _loc3_ = int(_loc5_.indexOf("::"));
               if(_loc3_ == -1)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "解析错误！";
                  return _loc2_;
               }
               _loc5_ = _loc5_.substring(_loc3_ + 2);
               _loc3_ = int(_equipPosClass.indexOf(_loc5_));
               if(_loc3_ == -1)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "该装备无法镶嵌！";
               }
               else if(!(param1 as InsetGemEquipmentVO).canInsetPos)
               {
                  _loc2_.isCanPut = false;
                  _loc2_.message = "该宝石未开放镶嵌部位";
               }
               else
               {
                  _loc3_++;
                  _loc3_ = int((param1 as InsetGemEquipmentVO).canInsetPos.indexOf(_loc3_.toString()));
                  if(_loc3_ == -1)
                  {
                     _loc2_.isCanPut = false;
                     _loc2_.message = "该宝石无法镶嵌到此装备上！";
                  }
                  else
                  {
                     _loc2_.isCanPut = true;
                  }
               }
            }
         }
         else if(param1 is AbleEquipmentVO)
         {
            _loc4_ = (param1 as AbleEquipmentVO).getHoleNum();
            if(_loc4_ == 0)
            {
               _loc2_.message = "该装备没有打孔，不能镶嵌宝石！";
            }
            else
            {
               _loc2_.message = "所有孔已镶嵌了宝石！";
            }
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               if((param1 as AbleEquipmentVO).getInsetGem(_loc6_) == null)
               {
                  _loc2_.isCanPut = true;
                  _loc2_.message = "";
               }
               _loc6_++;
            }
         }
         else if(_oldEquipment == null)
         {
            _loc2_.isCanPut = false;
            _loc2_.message = "请放入有孔的装备！";
         }
         else
         {
            _loc2_.isCanPut = false;
            _loc2_.message = "请放入镶嵌宝石！";
         }
         return _loc2_;
      }
      
      private function init() : void
      {
         _luckStoneShow = new LuckStoneShow();
         _equipPosClass = ["PreciousEquipmentVO","ForeverFashionEquipmentVO","ClothesEquipmentVO","WeaponEquipmentVO","GourdEquipmentVO","NecklaceEquipmentVO"];
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            MyFunction2.loadXMLFunction("gemInset",function(param1:XML):void
            {
               _gemInsetXML = param1;
               if(_show == null)
               {
                  _show = MyFunction2.returnShowByClassName("GemInsetPanel") as MovieClip;
               }
               addChild(_show);
               _showMC = new MovieClipPlayLogicShell();
               _showMC.setShow(_show);
               initNotPutInFrame();
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      public function putInEquipmentVO(param1:EquipmentVO, param2:Player) : void
      {
         var _loc4_:XML = null;
         var _loc3_:* = 0;
         if(param1 is InsetGemEquipmentVO)
         {
            clearGem();
            _gemEquipment = MyFunction2.sheatheEquipmentShell(param1);
            _gemEquipment.addEventListener("rollOver",onOver,false,0,true);
            _gemEquipment.addEventListener("rollOut",onOut,false,0,true);
         }
         else
         {
            if(!(param1 is AbleEquipmentVO))
            {
               return;
            }
            _currentInsetXML = null;
            clearOldEquipment();
            _oldEquipment = MyFunction2.sheatheEquipmentShell(param1);
            _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
            _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
            initPutInOldEquipmentFrame();
            initShow1();
         }
         _player = param2;
         if(Boolean(_oldEquipment) && _gemEquipment)
         {
            _loc4_ = getCurrentXML(_oldEquipment,_gemEquipment);
            _holeIndex = getHoleIndex(_oldEquipment.equipmentVO as AbleEquipmentVO);
            _currentInsetXML = _loc4_["hole" + (_holeIndex + 1)][0];
            if(_currentInsetXML == null)
            {
               _currentInsetXML = _loc4_["hole1"][0];
            }
            _baseSuccessRate = Number(_currentInsetXML.@successRate);
            _successRate = _baseSuccessRate;
            _needMoney = int(_currentInsetXML.@needMoney);
            _newEquipment = _oldEquipment.clone();
            _newEquipment.addEventListener("rollOver",onOver,false,0,true);
            _newEquipment.addEventListener("rollOut",onOut,false,0,true);
            (_newEquipment.equipmentVO as AbleEquipmentVO).addInsetGem(_gemEquipment.equipmentVO.clone() as InsetGemEquipmentVO);
            _newEquipment.equipmentVO = _newEquipment.equipmentVO;
            (_newEquipment as DisplayObject).scaleY = 1.5;
            (_newEquipment as DisplayObject).scaleX = 1.5;
            initPutInGemFrame();
            initShow2();
            _loc3_ = Math.ceil((1 - _successRate) / 0.1);
            changeSuccessRate2(_loc3_);
         }
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         var _loc4_:EquipmentVO = null;
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(Boolean(param1) && param1 is InsetGemEquipmentVO)
         {
            _loc4_ = !!_gemEquipment ? _gemEquipment.equipmentVO : null;
            initPutInOldEquipmentFrame();
            clearGem();
            clearNewEquipment();
            if(_loc4_)
            {
               _loc2_.push(_loc4_);
            }
            return _loc2_;
         }
         initNotPutInFrame();
         _player = null;
         _loc4_ = !!_oldEquipment ? _oldEquipment.equipmentVO : null;
         var _loc3_:EquipmentVO = !!_gemEquipment ? _gemEquipment.equipmentVO : null;
         clearOldEquipment();
         clearNewEquipment();
         clearGem();
         clearBuyBtn();
         if(_loc4_)
         {
            _loc2_.push(_loc4_);
         }
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      private function getCurrentXML(param1:Equipment, param2:Equipment) : XML
      {
         var _loc10_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:XML = null;
         var _loc3_:* = null;
         var _loc6_:XMLList = _gemInsetXML.equipment;
         _loc4_ = int(_loc6_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc4_)
         {
            _loc5_ = _loc6_[_loc10_];
            _loc7_ = int(_loc5_.@num);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(String(_loc5_["id" + (_loc8_ + 1)]) == param1.equipmentVO.id.toString())
               {
                  _loc3_ = _loc5_;
                  break;
               }
               _loc8_++;
            }
            _loc10_++;
         }
         if(_loc3_ == null)
         {
            _loc3_ = _gemInsetXML.defau[0];
         }
         _loc6_ = _loc3_.gem;
         var _loc9_:* = _loc3_;
         _loc3_ = null;
         _loc4_ = int(_loc6_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc4_)
         {
            _loc5_ = _loc6_[_loc10_];
            _loc7_ = int(_loc5_.@num);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(String(_loc5_["id" + (_loc8_ + 1)]) == param2.equipmentVO.id.toString())
               {
                  _loc3_ = _loc5_;
                  break;
               }
               _loc8_++;
            }
            _loc10_++;
         }
         if(_loc3_ == null)
         {
            _loc3_ = _loc9_.defau[0];
         }
         return _loc3_;
      }
      
      private function getHoleIndex(param1:AbleEquipmentVO) : int
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = param1.getHoleNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1.getInsetGem(_loc4_) == null)
            {
               return _loc4_;
            }
            _loc4_++;
         }
         return -1;
      }
      
      private function getHaveMaterialNum(param1:*, param2:Player) : int
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is Equipment)
         {
            _loc3_ = (param1 as Equipment).equipmentVO.id;
         }
         else
         {
            _loc3_ = param1;
         }
         var _loc5_:Vector.<EquipmentVO> = param2.playerVO.packageEquipmentVOs;
         var _loc4_:int = int(_loc5_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(_loc5_[_loc7_])
            {
               if(_loc5_[_loc7_].id == _loc3_)
               {
                  if(_loc5_[_loc7_] is StackEquipmentVO)
                  {
                     _loc6_ += (_loc5_[_loc7_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc6_++;
                  }
               }
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function initShow1() : void
      {
         _oldContainer.addChild(_oldEquipment as DisplayObject);
      }
      
      private function initShow2() : void
      {
         _gemContainer.addChild(_gemEquipment as DisplayObject);
         _luckStoneContainer.addChild(_luckStoneShow);
         _newContainer.addChild(_newEquipment as DisplayObject);
         _successRateText.text = int(_successRate * 100) + "%";
         clearBuyLuckStoneBtn();
         _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
         _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
         _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
         addChild(_buyLuckStoneGuideBtn);
         refreshMoneyShow();
      }
      
      private function refreshMoneyShow() : void
      {
         if(_moneyText == null)
         {
            return;
         }
         clearBuyGoldPocketBtn();
         var _loc1_:TextFormat = _moneyText.defaultTextFormat;
         if(_player.playerVO.money >= _needMoney)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyGoldPocketBtn = new BuyGoldPocketGuideBtn(11100000,refreshMoneyShow,1);
         }
         _moneyText.defaultTextFormat = _loc1_;
         _moneyText.text = _needMoney.toString();
         _moneyText.width = _moneyText.textWidth + 5;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.x = _moneyText.x + _moneyText.width + 2;
            _buyGoldPocketBtn.y = _moneyText.y;
            addChild(_buyGoldPocketBtn);
         }
      }
      
      private function setNumBtnGroupMaxNum() : void
      {
         if(_numBtnGroup)
         {
            _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         }
      }
      
      private function changeSuccessRate(param1:Number) : void
      {
         _successRate = Math.min(Math.max(0,param1),1);
         _successRateText.text = int(_successRate * 100) + "%";
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGroup.num = param1;
         changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
      }
      
      private function start() : void
      {
         if(_oldEquipment == null)
         {
            return;
         }
         if(_needMoney > _player.playerVO.money)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足！",
               "flag":0
            }));
            return;
         }
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var saveinfo:SaveTaskInfo;
            var saveinfo3:SaveTaskInfo;
            var r:Number = Math.random();
            _player.playerVO.money -= _needMoney;
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_numBtnGroup.num,10500000);
            clearBuyBtn();
            clearBuyLuckStoneBtn();
            clearBuyGoldPocketBtn();
            if(r <= _successRate)
            {
               parent.parent.mouseChildren = false;
               parent.parent.mouseEnabled = false;
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_newEquipment.equipmentVO,_oldEquipment.equipmentVO);
               if(_gemEquipment.equipmentVO is StackEquipmentVO)
               {
                  (_gemEquipment.equipmentVO as StackEquipmentVO).num--;
                  if((_gemEquipment.equipmentVO as StackEquipmentVO).num)
                  {
                     MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_gemEquipment.equipmentVO,_gemEquipment.equipmentVO);
                  }
                  else
                  {
                     MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_gemEquipment.equipmentVO,_gemEquipment.equipmentVO,true);
                  }
               }
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               initSuccessFrame();
               equipmentsAnimation(function():void
               {
                  _successAnimation.gotoAndPlay("start",null,null,function():void
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"镶嵌成功！",
                        "flag":0
                     }));
                     TweenLite.to(_newEquipment,2,{
                        "alpha":0.5,
                        "x":600,
                        "y":100,
                        "ease":Back.easeIn,
                        "onComplete":function():void
                        {
                           _player = null;
                           clearNewEquipment();
                           clearOldEquipment();
                           clearGem();
                           initNotPutInFrame();
                           dispatchEvent(new UIPassiveEvent("refreshAtt",34));
                           parent.parent.mouseChildren = true;
                           parent.parent.mouseEnabled = true;
                        }
                     });
                  },null);
               });
            }
            else
            {
               MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_oldEquipment.equipmentVO,_oldEquipment.equipmentVO);
               saveinfo3 = new SaveTaskInfo();
               saveinfo3.type = "4399";
               saveinfo3.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo3);
               MyFunction2.saveGame2();
               initFailFrame();
               equipmentsAnimation(function():void
               {
                  _failAnimation.gotoAndPlay("start",null,null,function():void
                  {
                     clearOldEquipment();
                     clearNewEquipment();
                     initNotPutInFrame();
                     _player = null;
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"镶嵌失败！",
                        "flag":0
                     }));
                     dispatchEvent(new UIPassiveEvent("refreshAtt",34));
                  });
               });
            }
         },function():void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"网络连接失败！",
               "flag":0
            }));
         },true);
      }
      
      private function initNotPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInOldEquipmentFrame();
         clearPutInGemFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("notPutIn");
      }
      
      private function clearNotPutInFrame() : void
      {
      }
      
      private function initPutInOldEquipmentFrame() : void
      {
         clearNotPutInFrame();
         clearPutInOldEquipmentFrame();
         clearPutInGemFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putInOldEquipment");
         _oldContainer = _show["oldContainer"];
      }
      
      private function clearPutInOldEquipmentFrame() : void
      {
         _oldContainer = null;
      }
      
      private function initPutInGemFrame() : void
      {
         clearNotPutInFrame();
         clearPutInOldEquipmentFrame();
         clearPutInGemFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putInGem");
         _oldContainer = _show["oldContainer"];
         _gemContainer = _show["gemContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _successRateText = _show["successRateText"];
         _moneyText = _show["moneyText"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_successRateText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_moneyText,true);
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.setShow(_show["numBtnGroup"]);
         _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         _startInsetBtn = new ButtonLogicShell2();
         _startInsetBtn.setShow(_show["startInsetBtn"]);
         _startInsetBtn.setTipString("点击开始镶嵌");
      }
      
      private function clearPutInGemFrame() : void
      {
         _oldContainer = null;
         _gemContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startInsetBtn)
         {
            _startInsetBtn.clear();
         }
         _startInsetBtn = null;
      }
      
      private function initSuccessFrame() : void
      {
         clearNotPutInFrame();
         clearPutInOldEquipmentFrame();
         clearPutInGemFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("success");
         _successAnimation = new MovieClipPlayLogicShell();
         _successAnimation.setShow(_show["successAnimation"]);
         _oldContainer = _show["oldContainer"];
         _gemContainer = _show["gemContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearSuccessFrame() : void
      {
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         _successAnimation = null;
         _oldContainer = null;
         _gemContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function initFailFrame() : void
      {
         clearNotPutInFrame();
         clearPutInOldEquipmentFrame();
         clearPutInGemFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("fail");
         _failAnimation = new MovieClipPlayLogicShell();
         _failAnimation.setShow(_show["failAnimation"]);
         _oldContainer = _show["oldContainer"];
         _gemContainer = _show["gemContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearFailFrame() : void
      {
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         _failAnimation = null;
         _oldContainer = null;
         _gemContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _numBtnGroup:
               changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
               break;
            case _startInsetBtn:
               start();
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function equipmentsAnimation(param1:Function) : void
      {
         if(_oldEquipment)
         {
            TweenLite.to(_oldEquipment,2,{
               "alpha":0,
               "x":84,
               "y":58,
               "ease":Back.easeIn,
               "onComplete":param1
            });
         }
         if(_gemEquipment)
         {
            TweenLite.to(_gemEquipment,2,{
               "alpha":0,
               "x":-84,
               "y":58,
               "ease":Back.easeIn
            });
         }
      }
      
      private function clearOldEquipment() : void
      {
         if(_oldEquipment)
         {
            if((_oldEquipment as DisplayObject).parent)
            {
               (_oldEquipment as DisplayObject).parent.removeChild(_oldEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_oldEquipment);
            _oldEquipment.clear();
            _oldEquipment = null;
         }
      }
      
      private function clearNewEquipment() : void
      {
         if(_newEquipment)
         {
            if((_newEquipment as DisplayObject).parent)
            {
               (_newEquipment as DisplayObject).parent.removeChild(_newEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_newEquipment);
            _newEquipment.clear();
            _newEquipment = null;
         }
      }
      
      private function clearGem() : void
      {
         if(_gemEquipment)
         {
            if((_gemEquipment as DisplayObject).parent)
            {
               (_gemEquipment as DisplayObject).parent.removeChild(_gemEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_gemEquipment);
            _gemEquipment.clear();
            _gemEquipment = null;
         }
      }
      
      private function clearBuyLuckStoneBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            if(_buyLuckStoneGuideBtn.parent)
            {
               _buyLuckStoneGuideBtn.parent.removeChild(_buyLuckStoneGuideBtn);
            }
            _buyLuckStoneGuideBtn.clear();
            _buyLuckStoneGuideBtn = null;
         }
      }
      
      private function clearBuyBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
      }
      
      private function clearBuyGoldPocketBtn() : void
      {
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
   }
}

