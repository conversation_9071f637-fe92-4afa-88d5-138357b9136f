package UI.WorldBoss
{
   import UI.MySprite;
   import UI.SoundManager.SoundManager;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AttackAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationQueueData;
   import UI.WorldBoss.FlashView.FlashView;
   import UI.WorldBoss.ShakeView.ShakeView;
   import YJFY.Loader.YJFYLoader;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.INextStopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.ShowLogicShell.PlayEndListener;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class Entity extends MySprite implements IEntity, IFrameLabelListener, INextStopListener
   {
      protected var m_userData:Object;
      
      protected var m_mc:AnimationShowPlayLogicShell;
      
      protected var m_levelShow:MultiPlaceNumLogicShell;
      
      protected var m_position:Position;
      
      protected var m_fightPosition:Sprite;
      
      protected var m_myLoader:YJFYLoader;
      
      protected var m_animationQueueData:AnimationQueueData;
      
      protected var m_isReadyFight:Boolean;
      
      protected var m_isReadyPlay:Boolean;
      
      protected var m_world:StepAttackGameWorld;
      
      protected var m_worldBoss:WorldBoss;
      
      protected var m_fightStage:Sprite;
      
      protected var m_view:View;
      
      protected var m_shakeView_attack:ShakeView;
      
      protected var m_flashView_attack:FlashView;
      
      protected var m_soundManager:SoundManager;
      
      protected var m_extraData:Object;
      
      public function Entity()
      {
         super();
         m_extraData = {};
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullObject(m_userData);
         m_userData = null;
         ClearUtil.nullObject(m_mc);
         m_mc = null;
         m_position = null;
         ClearUtil.clearDisplayObjectInContainer(m_fightPosition,false,false);
         m_fightPosition = null;
         m_myLoader = null;
         m_animationQueueData = null;
         m_world = null;
         m_fightStage = null;
         m_view = null;
         ClearUtil.clearObject(m_shakeView_attack);
         m_shakeView_attack = null;
         ClearUtil.clearObject(m_flashView_attack);
         m_flashView_attack = null;
         m_worldBoss = null;
         m_soundManager = null;
         ClearUtil.clearObject(m_levelShow);
         m_levelShow = null;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function attack(param1:Vector.<IEntity>) : void
      {
      }
      
      public function setUserData(param1:Object) : void
      {
         m_userData = param1;
      }
      
      public function setView(param1:View) : void
      {
         m_view = param1;
      }
      
      public function setSoundManager(param1:SoundManager) : void
      {
         m_soundManager = param1;
      }
      
      public function setWorldBoss(param1:WorldBoss) : void
      {
         m_worldBoss = param1;
      }
      
      public function getWorldBoss() : WorldBoss
      {
         return m_worldBoss;
      }
      
      public function getUserData() : Object
      {
         return m_userData;
      }
      
      public function setPosition(param1:Position) : void
      {
         m_position = param1;
      }
      
      public function setFightPositionSprite(param1:Sprite) : void
      {
         m_fightPosition = param1;
      }
      
      public function getFightPosition() : Sprite
      {
         return m_fightPosition;
      }
      
      public function setAnimationQueueData(param1:AnimationQueueData) : void
      {
         m_animationQueueData = param1;
      }
      
      public function setFightStage(param1:Sprite) : void
      {
         m_fightStage = param1;
      }
      
      public function getX() : int
      {
         return !!m_position ? m_position.positionX : 0;
      }
      
      public function getY() : int
      {
         return !!m_position ? m_position.positionY : 0;
      }
      
      public function getPositionSprite() : Sprite
      {
         return !!m_position ? m_position.container : null;
      }
      
      public function getPositioin() : Position
      {
         return m_position;
      }
      
      public function playAttackAnimation(param1:AttackAnimationData, param2:Function) : void
      {
         var playEndListener2:PlayEndListener;
         var entity:Entity;
         var beEntities:Vector.<IEntity>;
         var i:int;
         var length:int;
         var reachAttackFrameLabelListener:ReachAttackFrameLabelListener;
         var str:String;
         var attackAnimationData:AttackAnimationData = param1;
         var playEndFun:Function = param2;
         var playEndListener:PlayEndListener = new PlayEndListener();
         playEndListener.mc = m_mc;
         playEndListener.playEndFun = playEndFun;
         m_mc.addNextStopListener(playEndListener);
         playEndListener2 = new PlayEndListener();
         playEndListener2.mc = m_mc;
         entity = this;
         playEndListener2.playEndFun = function():void
         {
            if(m_position)
            {
               m_position.container.addChild(entity);
            }
         };
         m_mc.addNextStopListener(playEndListener2);
         beEntities = attackAnimationData.beAttackedEntities;
         length = int(beEntities.length);
         i = 0;
         while(i < length)
         {
            reachAttackFrameLabelListener = new ReachAttackFrameLabelListener();
            reachAttackFrameLabelListener.fightStage = m_fightStage;
            reachAttackFrameLabelListener.attackEntity = this;
            reachAttackFrameLabelListener.beAttackedEnttity = beEntities[i] as Entity;
            reachAttackFrameLabelListener.m_myLoader = m_myLoader;
            reachAttackFrameLabelListener.mc = m_mc;
            reachAttackFrameLabelListener.level = attackAnimationData.beAttackedEntityLevels[i];
            reachAttackFrameLabelListener.hurt = attackAnimationData.hurts[i];
            reachAttackFrameLabelListener.bloodPercent = attackAnimationData.bloodPercents[i];
            reachAttackFrameLabelListener.bloodShowIndex = !!attackAnimationData.bloodShowIndexs ? attackAnimationData.bloodShowIndexs[i] : 0;
            reachAttackFrameLabelListener.currentBlood = attackAnimationData.currentBloods[i];
            reachAttackFrameLabelListener.totalBlood = !!attackAnimationData.totalBloods ? attackAnimationData.totalBloods[i] : 0;
            reachAttackFrameLabelListener.criticalAndDodgeData = attackAnimationData.criticalAndDodgeDatas[i];
            m_mc.addFrameLabelListener(reachAttackFrameLabelListener);
            i++;
         }
         if(m_fightPosition)
         {
            m_fightPosition.addChild(this);
         }
         str = getLevelStr();
         m_mc.gotoAndPlay("attack");
         setLevelStr(str);
      }
      
      public function playIdleAnimation(param1:Function) : void
      {
         if(m_mc == null)
         {
            return;
         }
         var _loc3_:PlayEndListener = new PlayEndListener();
         _loc3_.mc = m_mc;
         _loc3_.playEndFun = param1;
         m_mc.addNextStopListener(_loc3_);
         var _loc2_:String = getLevelStr();
         m_mc.gotoAndPlay("idle");
         setLevelStr(_loc2_);
      }
      
      public function playHurtAnimation(param1:Function) : void
      {
         if(m_mc == null)
         {
            return;
         }
         var _loc3_:PlayEndListener = new PlayEndListener();
         _loc3_.mc = m_mc;
         _loc3_.playEndFun = param1;
         m_mc.addNextStopListener(_loc3_);
         var _loc2_:String = getLevelStr();
         m_mc.gotoAndPlay("hurt");
         setLevelStr(_loc2_);
      }
      
      protected function getLevelStr() : String
      {
         var _loc1_:String = "";
         if(m_levelShow)
         {
            _loc1_ = m_levelShow.getNum().toString();
         }
         return _loc1_;
      }
      
      public function setLevelStr(param1:*) : void
      {
         if(Boolean(m_mc) && m_mc.getShow() && m_mc.getShow()["card"])
         {
            if(m_mc.getShow()["card"]["levelShow"])
            {
               ClearUtil.clearObject(m_levelShow);
               m_levelShow = new MultiPlaceNumLogicShell();
               m_levelShow.setShow(m_mc.getShow()["card"]["levelShow"]);
               m_levelShow.showNum(param1);
            }
         }
      }
      
      public function playDieAnimation(param1:Function) : void
      {
         var _loc3_:PlayEndListener = new PlayEndListener();
         _loc3_.mc = m_mc;
         _loc3_.playEndFun = param1;
         var _loc4_:PlayEndListener = new PlayEndListener();
         _loc4_.mc = m_mc;
         _loc4_.playEndFun = clear;
         m_mc.addNextStopListener(_loc3_);
         m_mc.addNextStopListener(_loc4_);
         var _loc2_:String = getLevelStr();
         m_mc.gotoAndPlay("die");
         setLevelStr(_loc2_);
      }
      
      public function isDie() : Boolean
      {
         return false;
      }
      
      public function Die() : void
      {
      }
      
      public function getIsReadyFight() : Boolean
      {
         return m_isReadyFight;
      }
      
      public function getIsReadyPlay() : Boolean
      {
         return m_isReadyPlay;
      }
      
      public function reachFrameLabel(param1:String) : void
      {
      }
      
      public function reachFrameLabel2(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
      
      public function stop() : void
      {
         if(m_mc)
         {
            m_mc.gotoAndPlay("idle");
         }
      }
      
      public function stop2(param1:AnimationShowPlayLogicShell) : void
      {
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_world = param1;
      }
      
      public function getWorld() : StepAttackGameWorld
      {
         return m_world;
      }
      
      public function getSoundManger() : SoundManager
      {
         return m_soundManager;
      }
      
      public function playAttackViewChange() : void
      {
         if(m_shakeView_attack)
         {
            m_shakeView_attack.shakeView(m_view);
         }
         if(m_flashView_attack)
         {
            m_flashView_attack.flashView(m_view);
         }
      }
      
      public function playBeAttackedSound() : void
      {
         if(m_soundManager)
         {
            m_soundManager.play("BeAttackSound");
         }
      }
      
      public function getExtraData() : Object
      {
         return m_extraData;
      }
   }
}

