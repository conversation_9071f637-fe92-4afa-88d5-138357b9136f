package UI2.SmallAssistant
{
   import UI.EnterFrameTime;
   import UI.ExternalUI.MiddlePanel;
   import UI.ExternalUI.MiddlePanelListener;
   import UI.GamingUI;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   
   public class SmallAssistant
   {
      private const m_const_renderInterval:uint = 1000;
      
      private var m_lastRenderTime:Number;
      
      private var m_smallAssistantEnterShow:SmallAssistantEnterShow;
      
      private var m_smallAssistantPanel:SmallAssistantPanel;
      
      private var m_smallAssistantData:SmallAssistantData;
      
      private var m_middlePanelListener:MiddlePanelListener;
      
      private var m_redNum:uint;
      
      private var m_yellowNum:uint;
      
      private var m_middlePanel:MiddlePanel;
      
      private var m_gamingUI:GamingUI;
      
      public function SmallAssistant()
      {
         super();
         m_middlePanelListener = new MiddlePanelListener();
         m_middlePanelListener.tranToFightStateFun = tranToFightState_middlePanel;
         m_middlePanelListener.tranToNormalStateFun = tranToNormalState_middlePanel;
         m_lastRenderTime = 0;
      }
      
      public function clear() : void
      {
         if(m_middlePanel)
         {
            m_middlePanel.removeMiddlePanelListener(m_middlePanelListener);
            if(m_middlePanel.getShow())
            {
               m_middlePanel.getShow().removeEventListener("clickButton",clickButton,true);
            }
         }
         ClearUtil.clearObject(m_smallAssistantEnterShow);
         m_smallAssistantEnterShow = null;
         ClearUtil.clearObject(m_smallAssistantPanel);
         m_smallAssistantPanel = null;
         ClearUtil.clearObject(m_smallAssistantData);
         m_smallAssistantData = null;
         ClearUtil.clearObject(m_middlePanelListener);
         m_middlePanelListener = null;
         m_middlePanel = null;
         m_gamingUI = null;
      }
      
      public function init(param1:MiddlePanel, param2:SmallAssistantSaveData, param3:XML, param4:GamingUI) : void
      {
         if(m_middlePanel)
         {
            m_middlePanel.removeMiddlePanelListener(m_middlePanelListener);
            m_middlePanel.getShow().removeEventListener("clickButton",clickButton,true);
         }
         m_middlePanel = param1;
         m_gamingUI = param4;
         if(m_middlePanel)
         {
            m_middlePanel.addMiddlePanelListener(m_middlePanelListener);
            m_middlePanel.getShow().addEventListener("clickButton",clickButton,true,0,true);
         }
         ClearUtil.clearObject(m_smallAssistantData);
         m_smallAssistantData = new SmallAssistantData();
         m_smallAssistantData.init(param3,param2);
         initSmallAssistantEnterShow();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_smallAssistantEnterShow)
         {
            if(param1.getOnLineTimeForThisInit() - m_lastRenderTime > 1000)
            {
               m_lastRenderTime = param1.getOnLineTimeForThisInit();
               m_redNum = m_smallAssistantData.getSmallAssistantMaxPriorityRedTipNum();
               if(m_redNum)
               {
                  if(OnLineGiftBagData.getInstance().remainTime == 0)
                  {
                     m_smallAssistantEnterShow.showRedTip(m_redNum + 1);
                  }
                  else
                  {
                     m_smallAssistantEnterShow.showRedTip(m_redNum);
                  }
               }
               else
               {
                  m_yellowNum = m_smallAssistantData.getSmallAssistantMaxPriorityYellowTipNum();
                  if(m_yellowNum)
                  {
                     m_smallAssistantEnterShow.showYellowTip(m_yellowNum);
                  }
                  else
                  {
                     m_smallAssistantEnterShow.showNormalState();
                  }
               }
            }
         }
      }
      
      private function tranToFightState_middlePanel(param1:MiddlePanel) : void
      {
         ClearUtil.clearObject(m_smallAssistantEnterShow);
         m_smallAssistantEnterShow = null;
      }
      
      private function tranToNormalState_middlePanel(param1:MiddlePanel) : void
      {
         initSmallAssistantEnterShow();
      }
      
      private function initSmallAssistantEnterShow() : void
      {
         ClearUtil.clearObject(m_smallAssistantEnterShow);
         m_smallAssistantEnterShow = null;
         if(m_middlePanel && m_middlePanel.getShow().hasOwnProperty("smallAssistantShow"))
         {
            m_smallAssistantEnterShow = new SmallAssistantEnterShow();
            m_smallAssistantEnterShow.setShow(m_middlePanel.getShow()["smallAssistantShow"]);
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if((!!m_smallAssistantEnterShow ? m_smallAssistantEnterShow.getSmallAssistantBtn() : null) === _loc2_)
         {
            openSmallAssistantPanel();
         }
      }
      
      private function openSmallAssistantPanel() : void
      {
         if(m_smallAssistantPanel == null)
         {
            m_smallAssistantPanel = new SmallAssistantPanel();
            m_smallAssistantPanel.init(this,Part1.getInstance().getLoadUI(),m_gamingUI.getVersionControl(),m_smallAssistantData);
         }
         Part1.getInstance().setTipXY(250,10);
         m_gamingUI.addChild(m_smallAssistantPanel);
      }
      
      public function closeSmallAssistantPanel() : void
      {
         Part1.getInstance().setTipXY(250,150);
         ClearUtil.clearObject(m_smallAssistantPanel);
         m_smallAssistantPanel = null;
      }
   }
}

