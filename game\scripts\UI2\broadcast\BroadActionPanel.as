package UI2.broadcast
{
   import UI.EnterFrameTime;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.utils.clearTimeout;
   
   public class BroadActionPanel
   {
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_tippanel:TipPanel;
      
      private var m_txtInfo:TextField;
      
      private var m_moveMc:MovieClip;
      
      private var m_x1:Number = 0;
      
      private var m_y1:Number = 1;
      
      private var m_x2:Number = 0;
      
      private var m_y2:Number = 46;
      
      private var m_avg:Number = 1;
      
      private var m_aValue:Number = 0.1;
      
      private var m_state:int = 0;
      
      private var m_isMove:Boolean = false;
      
      private var m_isTime:Boolean = false;
      
      private var m_isEnd:Boolean = false;
      
      private var m_getTime:int = 60;
      
      private var m_nCdTime:int = 0;
      
      private var m_nCdSec:int = 0;
      
      private var m_data:BroadInfo;
      
      private var m_delayTime:uint;
      
      private var m_hMove:Number = 1;
      
      private var m_hMaxX:Number = -200;
      
      private var m_zanGetTime:Number = 1;
      
      private var m_zanCdTime:Number = 0;
      
      private var m_zanCdSec:Number = 0;
      
      private var m_bZan:Boolean = false;
      
      private var m_showGetTime:Number = 20;
      
      private var m_showCdTime:Number = 0;
      
      private var m_showCdSec:Number = 0;
      
      private var m_bshow:Boolean = false;
      
      public function BroadActionPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
      }
      
      public function init(param1:MovieClip, param2:TipPanel) : void
      {
         m_show = param1;
         m_tippanel = param2;
         initParams();
      }
      
      private function initParams() : void
      {
         m_moveMc = m_show["mcinfomove"] as MovieClip;
         m_txtInfo = m_moveMc["txtinfo"] as TextField;
         m_txtInfo.y = m_y2;
         m_txtInfo.alpha = 0;
         m_txtInfo.mouseEnabled = false;
         m_state = 0;
         setData(m_tippanel.getEvent());
      }
      
      private function calldelay() : void
      {
         setData(m_tippanel.getEvent());
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_isMove && m_isTime == false && m_bZan == false)
         {
            if(m_txtInfo.y >= m_y1 && m_txtInfo.y <= m_y2)
            {
               m_txtInfo.y -= m_avg;
               m_txtInfo.alpha += 1 / (m_y2 - m_y1);
               m_tippanel.hideBtn();
               m_moveMc.x = 0;
            }
            else if(m_txtInfo.y < m_y1)
            {
               m_isTime = true;
               m_isMove = false;
               m_tippanel.showBtn();
            }
         }
         if(m_isTime && m_isMove == false)
         {
            this.m_nCdTime++;
            if(this.m_nCdTime >= 24)
            {
               this.m_nCdTime = 0;
               this.m_nCdSec++;
               if(m_nCdSec >= m_getTime)
               {
                  m_nCdSec = 0;
                  setData(m_tippanel.getEvent());
               }
            }
         }
         if(m_isTime && m_isMove == false && m_moveMc && m_bZan == false)
         {
            if(m_txtInfo.textWidth > 410)
            {
               m_hMaxX = -Math.abs(m_txtInfo.textWidth - 410);
            }
            else
            {
               m_hMaxX = -200;
            }
            if(m_hMaxX >= m_moveMc.x)
            {
               m_bZan = true;
            }
            else
            {
               m_moveMc.x -= m_hMove;
            }
         }
         if(m_bZan)
         {
            m_zanCdTime++;
            if(m_zanCdTime >= 24)
            {
               m_zanCdTime = 0;
               m_zanCdSec++;
               if(m_zanCdSec >= m_zanGetTime)
               {
                  m_zanCdSec = 0;
                  m_moveMc.x = 0;
                  m_bZan = false;
               }
            }
         }
         if(m_bshow)
         {
            m_showCdTime++;
            if(m_showCdTime >= 24)
            {
               m_showCdTime = 0;
               m_showCdSec++;
               if(m_showCdSec >= m_showGetTime)
               {
                  m_showCdSec = 0;
                  m_moveMc.x = 0;
                  m_bshow = false;
                  m_tippanel.hideVisible();
               }
            }
         }
      }
      
      public function setData(param1:String) : void
      {
         m_data = BroadDataManager.getInstance().getBroadData(param1);
         if(m_data)
         {
            m_bshow = true;
            BroadDataManager.getInstance().isShowOld = false;
            loadTxt(m_data);
            m_txtInfo.y = m_y2;
            m_txtInfo.alpha = 0;
            m_isMove = true;
            m_isTime = false;
            m_bZan = false;
            m_tippanel.setData(m_data);
            m_nCdSec = 0;
            m_tippanel.hideBtn();
            m_tippanel.showbg();
         }
         else
         {
            m_tippanel.hide();
            BroadDataManager.getInstance().isShowOld = true;
         }
      }
      
      public function getData() : BroadInfo
      {
         return m_data;
      }
      
      private function loadTxt(param1:BroadInfo) : void
      {
         var _loc3_:Array = param1.value.split("|");
         var _loc2_:Array = param1.txtInfo.split("|");
         if(param1.doId == 1)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "</font>";
         }
         else if(param1.doId == 2)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "</font>";
         }
         else if(param1.doId == 3)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "<font color=\'#00ff33\'>" + String(_loc2_[2]) + "</font>" + String(_loc3_[3]) + "</font>";
         }
         else if(param1.doId == 4)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "</font>";
         }
         else if(param1.doId == 5)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "<font color=\'#00ff33\'>" + String(_loc2_[2]) + "</font>" + String(_loc3_[3]) + "</font>";
         }
         else if(param1.doId == 6)
         {
            m_txtInfo.htmlText = "<font color=\'#FFFF00\' size=\'25\' face=\'方正卡通简体\'><font color=\'#00ff33\'>" + String(_loc2_[0]) + "</font>" + String(_loc3_[1]) + "<font color=\'#00ff33\'>" + "VIP" + String(_loc2_[1]) + "</font>" + String(_loc3_[2]) + "</font>";
         }
      }
   }
}

