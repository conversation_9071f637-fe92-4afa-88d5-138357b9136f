package UI
{
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.CodeMode;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.VersionControl.LineMode;
   
   public class VersionControl implements IVersionControl
   {
      private var m_configXML:XML;
      
      private var m_lineMode:LineMode;
      
      private var m_codeMode:CodeMode;
      
      public function VersionControl()
      {
         super();
         m_configXML = Part1.getInstance().getConfigXML();
         m_lineMode = new LineMode();
         m_lineMode.setLineMode(m_configXML.@lineMode);
         m_codeMode = new CodeMode();
         m_codeMode.setCodeMode(m_configXML.@isCode);
      }
      
      public function clear() : void
      {
         m_configXML = null;
         ClearUtil.clearObject(m_lineMode);
         m_lineMode = null;
         ClearUtil.clearObject(m_codeMode);
         m_codeMode = null;
      }
      
      public function getLineMode() : LineMode
      {
         return m_lineMode;
      }
      
      public function getCodeMode() : CodeMode
      {
         return m_codeMode;
      }
      
      public function getSWFVersionStr(param1:String) : String
      {
         var _loc4_:int = 0;
         var _loc3_:XMLList = m_configXML.swf;
         var _loc2_:int = int(_loc3_.length());
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(String(_loc3_[_loc4_].@swfUrl) == param1)
            {
               return String(_loc3_[_loc4_].@versionNum);
            }
            _loc4_++;
         }
         return "";
      }
      
      public function getImageVersionStr() : String
      {
         return "";
      }
      
      public function getXMLVersionStr() : String
      {
         var _loc1_:String = null;
         if(m_lineMode.getLineMode() == "onLine")
         {
            _loc1_ = String(m_configXML.@xmlVersion);
         }
         else
         {
            _loc1_ = "";
         }
         return _loc1_;
      }
      
      public function getShowVersionStr() : String
      {
         return m_configXML.@showVersion;
      }
   }
}

