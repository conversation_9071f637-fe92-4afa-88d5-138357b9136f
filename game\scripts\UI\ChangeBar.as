package UI
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.UIInterface.IBarMask;
   import flash.display.DisplayObject;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ChangeBar extends MySprite
   {
      private var _bM_x:Number;
      
      private var _isChange:Boolean = false;
      
      protected var _currentVolume:int;
      
      protected var _totalVolume:int;
      
      private var _volumeText:TextField;
      
      private var _isShowText:Boolean = true;
      
      protected var _width:Number;
      
      protected var _heidth:Number;
      
      public function ChangeBar()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _volumeText = null;
      }
      
      protected function changebar(param1:Bar, param2:IBarMask, param3:Number = 0, param4:int = 0, param5:Boolean = true, param6:int = 20, param7:int = 16777215) : void
      {
         var _loc9_:FangZhengKaTongJianTi = null;
         var _loc8_:TextFormat = null;
         if(!_isChange)
         {
            _isChange = true;
            param1.mask = param2 as DisplayObject;
            _bM_x = param2.x;
         }
         if(param3 > 1 || param3 < 0)
         {
            trace("percent value must between 0 and 1.");
            return;
         }
         param2.x = _bM_x - (1 - param3) * param2.width;
         _isShowText = param5;
         _currentVolume = Math.round(param4 * param3);
         _totalVolume = param4;
         if(_isShowText)
         {
            if(!_volumeText)
            {
               _volumeText = new TextField();
               _volumeText.embedFonts = true;
               _volumeText.selectable = false;
            }
            _volumeText.text = "" + _currentVolume + "/" + _totalVolume;
            _loc9_ = new FangZhengKaTongJianTi();
            _loc8_ = new TextFormat(_loc9_.fontName,param6,param7);
            _volumeText.setTextFormat(_loc8_,0,_volumeText.text.length);
            _volumeText.width = _volumeText.textWidth + 5;
            _volumeText.height = _volumeText.textHeight + 5;
            _volumeText.x = param1.x + (param1.width - _volumeText.textWidth) / 2;
            _volumeText.y = -_volumeText.textHeight * 3 / 5;
            if(_volumeText && getChildByName(_volumeText.name) == null)
            {
               addChild(_volumeText);
            }
         }
         else
         {
            if(_volumeText)
            {
               if(getChildByName(_volumeText.name))
               {
                  removeChild(_volumeText);
               }
            }
            _volumeText = null;
         }
         _width = param1.width;
         _heidth = param1.height;
      }
      
      override public function get width() : Number
      {
         return _width * scaleX;
      }
      
      override public function set width(param1:Number) : void
      {
         _width = param1;
      }
      
      override public function get height() : Number
      {
         return _heidth * scaleY;
      }
      
      override public function set height(param1:Number) : void
      {
         _heidth = param1;
      }
   }
}

