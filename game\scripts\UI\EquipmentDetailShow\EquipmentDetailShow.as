package UI.EquipmentDetailShow
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipmentDetailShow extends MySprite
   {
      protected var m_show:MovieClip;
      
      protected var m_showMC:MovieClipPlayLogicShell;
      
      protected var m_ableDragShow:AbleDragSpriteLogicShell;
      
      protected var m_quitBtn:ButtonLogicShell;
      
      protected var m_eqCell:Sprite;
      
      protected var m_eqNameText:TextField;
      
      protected var m_wantLoadSources:Array = ["equipmentDetailShow"];
      
      protected var m_equipmentVO:EquipmentVO;
      
      protected var m_equipment:Equipment;
      
      protected var m_player:Player;
      
      public function EquipmentDetailShow()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         GamingUI.getInstance().loadQueue.unLoad(m_wantLoadSources);
         super.clear();
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_ableDragShow);
         m_ableDragShow = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_eqCell);
         m_eqCell = null;
         m_eqNameText = null;
         ClearUtil.nullArr(m_wantLoadSources);
         m_wantLoadSources = null;
         m_equipmentVO = null;
         ClearUtil.clearObject(m_equipment);
         m_equipment = null;
         m_player = null;
      }
      
      public function setPlayer(param1:Player) : void
      {
         m_player = param1;
      }
      
      public function initDetailShow(param1:EquipmentVO) : void
      {
         var showEquipmentVO:EquipmentVO = param1;
         m_equipmentVO = showEquipmentVO;
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            if(m_show == null)
            {
               m_show = MyFunction2.returnShowByClassName("EquipmentDetailShow") as MovieClip;
               m_showMC = new MovieClipPlayLogicShell();
               m_showMC.setShow(m_show);
               m_ableDragShow = new AbleDragSpriteLogicShell();
               m_ableDragShow.setShow(m_show);
            }
            if(m_show.parent == null)
            {
               addChild(m_show);
            }
            if(stage)
            {
               x = (stage.stageWidth - m_show.width) / 2;
               y = (stage.stageHeight - m_show.height) / 2;
            }
            if(m_quitBtn == null)
            {
               m_quitBtn = new ButtonLogicShell();
               m_quitBtn.setShow(m_show["quitBtn2"]);
               m_quitBtn.setTipString("点击关闭");
            }
            m_eqCell = m_show["eqCell"];
            m_eqNameText = m_show["eqNameText"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_eqNameText);
            initShow();
            m_equipment = MyFunction2.sheatheEquipmentShell(m_equipmentVO);
            m_equipment.addEventListener("rollOver",onOverForNormal,false,0,true);
            m_equipment.addEventListener("rollOut",onOutForNormal,false,0,true);
            m_eqCell.addChild(m_equipment);
            m_eqNameText.text = m_equipmentVO.name;
         },null);
         GamingUI.getInstance().loadQueue.load(m_wantLoadSources,loadFinishListener);
      }
      
      protected function initShow() : void
      {
      }
      
      protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            clear();
         }
      }
      
      protected function onOverForNormal(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      protected function onOutForNormal(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      protected function onOverForShop(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      protected function onOutForShop(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

