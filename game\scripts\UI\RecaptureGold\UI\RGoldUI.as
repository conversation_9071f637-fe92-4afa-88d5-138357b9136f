package UI.RecaptureGold.UI
{
   import UI.GamingUI;
   import UI.MySprite;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import UI.RecaptureGold.UI.Btn.QuitThisLevelBtn;
   import UI.TextShowAnimationNumber.IValueToShowStr;
   import UI.TextShowAnimationNumber.TextShowAnimationNumber;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class RGoldUI extends MySprite implements IValueToShowStr
   {
      public var remainTimeText:TextField;
      
      public var gameLevelText:TextField;
      
      public var currentGoldNumText:TextField;
      
      public var targetGoldNumText:TextField;
      
      public var bombShow:MovieClip;
      
      public var bombNumText:TextField;
      
      public var bookShow:MovieClip;
      
      public var grassShow:MovieClip;
      
      public var potionShow:MovieClip;
      
      public var quitThisLevelBtn:QuitThisLevelBtn;
      
      private var _remainTime:Number;
      
      private var _interval:uint;
      
      private var _oldGoldNum:int;
      
      private var _newTextShowAnimation:TextShowAnimationNumber;
      
      public function RGoldUI()
      {
         super();
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         remainTimeText = null;
         gameLevelText = null;
         currentGoldNumText = null;
         targetGoldNumText = null;
         bombShow = null;
         bombNumText = null;
         bookShow = null;
         grassShow = null;
         potionShow = null;
         if(quitThisLevelBtn)
         {
            quitThisLevelBtn.clear();
         }
         quitThisLevelBtn = null;
         clearInterval(_interval);
         if(_newTextShowAnimation)
         {
            _newTextShowAnimation.clear();
         }
         _newTextShowAnimation = null;
      }
      
      public function initUIData(param1:int, param2:int, param3:Number, param4:Number) : void
      {
         if(GamingUI.getInstance().player2)
         {
            param1 -= 20;
         }
         remainTimeText.text = param1 + "秒";
         _remainTime = param1;
         gameLevelText.text = param2.toString();
         currentGoldNumText.text = param3 + "两";
         targetGoldNumText.text = param4 + "两";
      }
      
      public function setPropState(param1:int, param2:int, param3:int, param4:int) : void
      {
         bombNumText.text = "×" + param1;
         if(param1 == 0)
         {
            bombShow.gotoAndStop(1);
            bombNumText.visible = false;
         }
         else
         {
            bombShow.gotoAndStop(2);
            bombNumText.visible = true;
         }
         if(param2 == 0)
         {
            bookShow.gotoAndStop(1);
         }
         else
         {
            bookShow.gotoAndStop(2);
         }
         if(param3 == 0)
         {
            grassShow.gotoAndStop(1);
         }
         else
         {
            grassShow.gotoAndStop(2);
         }
         if(param4 == 0)
         {
            potionShow.gotoAndStop(1);
         }
         else
         {
            potionShow.gotoAndStop(2);
         }
      }
      
      public function startCountDown() : void
      {
         _interval = setInterval(decTime,1000);
      }
      
      public function stopConutDown() : void
      {
         clearInterval(_interval);
      }
      
      public function changeGoldNum(param1:int) : void
      {
         if(_newTextShowAnimation)
         {
            _newTextShowAnimation.clear();
            _newTextShowAnimation = null;
         }
         _newTextShowAnimation = new TextShowAnimationNumber(currentGoldNumText,_oldGoldNum,param1,this);
         _newTextShowAnimation.play(100,(param1 - _oldGoldNum) / 5);
         _oldGoldNum = param1;
      }
      
      public function valueToShowStrFun(param1:Number) : String
      {
         return int(param1) + "两";
      }
      
      private function decTime() : void
      {
         _remainTime--;
         if(_remainTime <= 0)
         {
            remainTimeText.text = _remainTime + "秒";
            dispatchEvent(new RecaptureGoldEvent("recaptureGoldGameEnd"));
            clearInterval(_interval);
            return;
         }
         remainTimeText.text = _remainTime + "秒";
      }
   }
}

