package UI2.Midautumn
{
   import UI.EnterFrameTime;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class HandLogic
   {
      private var m_handmc:MovieClip;
      
      private var m_show:MovieClip;
      
      private var m_force:MovieClip;
      
      private var m_midautumn:MidautumnView;
      
      private var m_wzwView:WzwView;
      
      private var m_type:int;
      
      public function HandLogic()
      {
         super();
      }
      
      public function init(param1:MovieClip, param2:MidautumnView = null, param3:WzwView = null) : void
      {
         m_show = param1;
         m_midautumn = param2;
         m_wzwView = param3;
         m_handmc = param1["hand"] as MovieClip;
         m_handmc.gotoAndStop("1");
         m_handmc.visible = false;
         m_force = m_handmc["forcemc"] as MovieClip;
         m_force.visible = false;
         m_handmc.addEventListener("mouseDown",MouseDown);
         m_handmc.addEventListener("mouseUp",MouseUp);
         m_handmc.addEventListener("enterFrame",enterframe);
      }
      
      public function clear() : void
      {
         m_show = null;
         m_handmc.removeEventListener("mouseDown",MouseDown);
         m_handmc.removeEventListener("mouseMove",MouseMove);
         m_handmc.removeEventListener("enterFrame",enterframe);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_handmc.currentFrameLabel == "endframe")
         {
            m_handmc.visible = false;
         }
      }
      
      public function enterframe(param1:Event) : void
      {
         m_handmc.x = m_show.mouseX + 100;
         m_handmc.y = m_show.mouseY;
      }
      
      public function MouseMove(param1:MouseEvent) : void
      {
         m_handmc.visible = true;
         m_handmc.gotoAndStop("1");
         m_handmc.x = m_show.mouseX + 100;
         m_handmc.y = m_show.mouseY;
      }
      
      public function MouseDown(param1:MouseEvent) : void
      {
         m_handmc.visible = true;
         m_handmc.gotoAndStop("5");
         if(m_midautumn)
         {
            m_midautumn.hideAllNum();
         }
         else if(m_wzwView)
         {
            m_wzwView.hideAllNum();
         }
         m_force.visible = true;
      }
      
      public function MouseUp(param1:MouseEvent) : void
      {
         m_handmc.visible = true;
         m_force.visible = false;
         m_handmc.gotoAndPlay("8");
         if(m_force.currentFrame >= 1 && m_force.currentFrame <= 5)
         {
            m_type = 1;
         }
         else if(m_force.currentFrame >= 6 && m_force.currentFrame <= 14)
         {
            m_type = 2;
         }
         else if(m_force.currentFrame >= 15)
         {
            m_type = 3;
         }
         if(m_midautumn)
         {
            m_midautumn.startRun();
         }
         else if(m_wzwView)
         {
            m_wzwView.startRun();
         }
      }
      
      public function showDice() : void
      {
         m_handmc.visible = true;
         m_force.visible = false;
         m_handmc.gotoAndPlay("8");
      }
      
      public function getType() : int
      {
         return m_type;
      }
   }
}

