package UI.Farm
{
   import UI.Event.UIPassiveEvent;
   import UI.Farm.CoordGrid.SceneCoordGrid;
   import UI.Farm.CoordGrid.SceneCoordGrids;
   import UI.Farm.FarmBlock.FarmBlockShow;
   import UI.Farm.FarmBlock.FarmBlockVO;
   import UI.Farm.Land.Land;
   import UI.Farm.Land.LandVO;
   import UI.Farm.MouseManager.MouseManager;
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   import flash.utils.getTimer;
   
   public class FarmSceneLayer extends MySprite
   {
      public static const LAND_WIDTH_NUM:int = 3;
      
      public static const LAND_HEIGHT_NUM:int = 3;
      
      private var _lands:Vector.<Land>;
      
      private var _otherShowObect:Vector.<IFarmShowObject>;
      
      private var _environment:Sprite;
      
      private var _landShowlayer:Sprite;
      
      private var _showObjectLayer:Sprite;
      
      private var _gridShape:Shape;
      
      private var _moveLayer:Sprite;
      
      private var _farmBlockLayer:Sprite;
      
      private var _isEnalePutObject:Boolean;
      
      private var _movingShowObject:IFarmShowObject;
      
      private var _weakenShowObjects:Vector.<IFarmShowObject>;
      
      private var _farmBlocks:Vector.<FarmBlockVO>;
      
      private var _originalShowObject_X:int;
      
      private var _originalShowObject_Y:int;
      
      private var _intervalTime:uint;
      
      private var _TIME:int = 200;
      
      public function FarmSceneLayer()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(_environment)
         {
            while(_environment.numChildren > 0)
            {
               _loc2_ = _environment.getChildAt(0);
               _environment.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _environment = null;
         if(_landShowlayer)
         {
            while(_landShowlayer.numChildren > 0)
            {
               _loc2_ = _landShowlayer.getChildAt(0);
               _landShowlayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _landShowlayer = null;
         if(_showObjectLayer)
         {
            while(_showObjectLayer.numChildren > 0)
            {
               _loc2_ = _showObjectLayer.getChildAt(0);
               _showObjectLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _showObjectLayer = null;
         if(_moveLayer)
         {
            while(_moveLayer.numChildren > 0)
            {
               _loc2_ = _moveLayer.getChildAt(0);
               _moveLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _moveLayer = null;
         if(_farmBlockLayer)
         {
            while(_farmBlockLayer.numChildren > 0)
            {
               _loc2_ = _farmBlockLayer.getChildAt(0);
               _farmBlockLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _farmBlockLayer = null;
         var _loc3_:int = 0;
         if(_lands)
         {
            _loc1_ = int(_lands.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _lands[_loc3_].clear();
               _lands[_loc3_] = null;
               _loc3_++;
            }
            _lands = null;
         }
         if(_otherShowObect)
         {
            _loc1_ = int(_otherShowObect.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_otherShowObect[_loc3_])
               {
                  _otherShowObect[_loc3_].clear();
               }
               _otherShowObect[_loc3_] = null;
               _loc3_++;
            }
            _otherShowObect = null;
         }
         if(_gridShape)
         {
            _gridShape.graphics.clear();
         }
         _gridShape = null;
         _movingShowObject = null;
         if(_weakenShowObjects)
         {
            _loc1_ = int(_weakenShowObjects.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _weakenShowObjects[_loc3_] = null;
               _loc3_++;
            }
            _weakenShowObjects = null;
         }
         if(_farmBlocks)
         {
            _loc1_ = int(_farmBlocks.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _farmBlocks[_loc3_] = null;
               _loc3_++;
            }
            _farmBlocks = null;
         }
         SceneCoordGrids.getInstance().clear();
      }
      
      public function get environment() : Sprite
      {
         return _environment;
      }
      
      public function buyNewLand(param1:Land) : void
      {
         var _loc3_:LandVO = new LandVO();
         _loc3_.id = 100100;
         _loc3_.state = 0;
         FarmData.getInstance().ownerLands.push(_loc3_);
         param1.addLandPartShow(_loc3_.id,XMLSingle.getInstance().farmXML);
         param1.landDrive.landVO = _loc3_;
         var _loc2_:int = int(_lands.indexOf(param1));
         if(_loc2_ < _lands.length - 1)
         {
            _lands[_loc2_ + 1].addLandUpPartShow(MyFunction2.returnShowByClassName("DevelopLabel"));
         }
      }
      
      public function openAll() : void
      {
         var _loc4_:* = 0;
         var _loc2_:LandVO = null;
         var _loc3_:int = int(FarmData.getInstance().ownerLands.length);
         var _loc1_:int = int(_lands.length);
         _loc4_ = _loc3_;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = new LandVO();
            _loc2_.id = 100100;
            _loc2_.state = 0;
            FarmData.getInstance().ownerLands.push(_loc2_);
            _lands[_loc4_].addLandPartShow(_loc2_.id,XMLSingle.getInstance().farmXML);
            _lands[_loc4_].landDrive.landVO = _loc2_;
            _loc4_++;
         }
         removeOpenAll();
      }
      
      private function init() : void
      {
         _weakenShowObjects = new Vector.<IFarmShowObject>();
         var farmXML:XML = XMLSingle.getInstance().farmXML;
         _environment = new Sprite();
         addChild(_environment);
         _farmBlockLayer = new Sprite();
         addChild(_farmBlockLayer);
         _landShowlayer = new Sprite();
         addChild(_landShowlayer);
         _showObjectLayer = new Sprite();
         addChild(_showObjectLayer);
         _gridShape = new Shape();
         addChild(_gridShape);
         _moveLayer = new Sprite();
         addChild(_moveLayer);
         initFarmBlocks(farmXML);
         _lands = new Vector.<Land>();
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "处理中...";
         initOtherShowObject(farmXML);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            initLands(param1,farmXML);
            initRecoverLandData(param1);
            refreshPositionFarmShowObject();
            var _loc2_:String = MyFunction.getInstance().splitTimeString(param1);
            if(FarmData.getInstance().recoverLandDate != _loc2_)
            {
               FarmData.getInstance().recoverLandDate = _loc2_;
               FarmData.getInstance().recoverLandNum = 0;
            }
         },null,true);
         initScene(farmXML);
      }
      
      private function initRecoverLandData(param1:String) : void
      {
         var _loc2_:String = MyFunction.getInstance().splitTimeString(param1);
         if(FarmData.getInstance().recoverLandDate != _loc2_)
         {
            FarmData.getInstance().recoverLandDate = _loc2_;
            FarmData.getInstance().recoverLandNum = 0;
         }
      }
      
      private function initFarmBlocks(param1:XML) : void
      {
         var _loc8_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:FarmBlockVO = null;
         var _loc6_:FarmBlockShow = null;
         var _loc3_:XMLList = param1.farmBlock;
         var _loc2_:int = int(_loc3_.length());
         _farmBlocks = new Vector.<FarmBlockVO>();
         _loc4_ = int(FarmData.getInstance().farmBlockIDs.length);
         _loc8_ = 0;
         while(_loc8_ < _loc2_)
         {
            _loc7_ = new FarmBlockVO();
            _loc7_.id = int(_loc3_[_loc8_].@id);
            _loc7_.leftMinCo = int(_loc3_[_loc8_].@leftMinCo);
            _loc7_.rightMaxCo = int(_loc3_[_loc8_].@rightMaxCo);
            _loc7_.upMinCo = int(_loc3_[_loc8_].@upMinCo);
            _loc7_.downMaxCo = int(_loc3_[_loc8_].@downMaxCo);
            _loc7_.isDeveloped = false;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(FarmData.getInstance().farmBlockIDs[_loc5_] == _loc7_.id)
               {
                  _loc7_.isDeveloped = true;
               }
               _loc5_++;
            }
            _farmBlocks.push(_loc7_);
            _loc6_ = new FarmBlockShow(_loc7_);
            _farmBlockLayer.addChild(_loc6_);
            _loc8_++;
         }
      }
      
      private function initOtherShowObject(param1:XML) : void
      {
         var _loc5_:XML = null;
         var _loc7_:String = null;
         var _loc3_:Class = null;
         var _loc9_:IFarmShowObject = null;
         var _loc8_:FarmShowObjectVO = null;
         var _loc2_:SceneCoordGrid = null;
         _otherShowObect = new Vector.<IFarmShowObject>();
         var _loc10_:int = 0;
         var _loc4_:int = int(FarmData.getInstance().otherShowObjects.length);
         _loc10_ = 0;
         for(; _loc10_ < _loc4_; _loc10_++)
         {
            _loc8_ = FarmData.getInstance().otherShowObjects[_loc10_];
            _loc5_ = param1.otherItem.(@id == _loc8_.id)[0];
            _loc7_ = String(_loc5_.@className);
            if(_loc7_ == "UI.Farm.Btn.FarmOpenAll")
            {
               if(FarmData.getInstance().ownerLands.length >= 8)
               {
                  continue;
               }
            }
            _loc3_ = MyFunction2.returnClassByClassName(_loc7_);
            _loc9_ = new _loc3_() as IFarmShowObject;
            _loc9_.farmShowObjectVO = _loc8_;
            _loc2_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc8_.hI,_loc8_.vJ);
            _loc9_.x = _loc2_.x;
            _loc9_.y = _loc2_.y;
            setGridsHaveThing(_loc9_,_loc8_.hI,_loc8_.vJ,_loc8_.H_width,_loc8_.V_height);
            _showObjectLayer.addChild(_loc9_ as DisplayObject);
            _otherShowObect.push(_loc9_);
         }
      }
      
      public function removeOpenAll() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < _otherShowObect.length)
         {
            if(_otherShowObect[_loc1_].farmShowObjectVO.id == 1002)
            {
               _showObjectLayer.removeChild(_otherShowObect[_loc1_] as DisplayObject);
               if(_otherShowObect[_loc1_])
               {
                  _otherShowObect[_loc1_].clear();
               }
               _otherShowObect[_loc1_] = null;
               _otherShowObect.splice(_loc1_,1);
            }
            _loc1_++;
         }
      }
      
      private function initScene(param1:XML) : void
      {
         var _loc2_:* = undefined;
         var _loc5_:XML = param1.scene.(@id == FarmData.getInstance().sceneID)[0];
         _environment.addChild(MyFunction2.returnShowByClassName(String(_loc5_.@className)));
         var _loc3_:Array = String(_loc5_.@coords).split(" ");
         var _loc6_:int = 0;
         var _loc4_:int = int(_loc3_.length);
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc2_ = MyFunction.getInstance().excreteString(_loc3_[_loc6_]);
            SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc2_[0],_loc2_[1]).isHaveThing = new FarmShowObject();
            _loc6_++;
         }
      }
      
      private function initLands(param1:String, param2:XML) : void
      {
         var _loc7_:Land = null;
         var _loc8_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:LandVO = null;
         var _loc6_:SceneCoordGrid = null;
         var _loc9_:int = 0;
         var _loc13_:int = int(FarmData.getInstance().ownerLands.length);
         var _loc4_:Vector.<LandVO> = FarmData.getInstance().ownerLands;
         var _loc5_:XML = XMLSingle.getInstance().equipmentXML;
         _loc9_ = 0;
         while(_loc9_ < 9)
         {
            if(_loc9_ < _loc13_)
            {
               _loc7_ = new Land(_loc4_[_loc9_],param1,_loc5_,param2);
            }
            else
            {
               _loc11_ = new LandVO();
               _loc11_.id = 0;
               _loc7_ = new Land(_loc11_,param1,_loc5_,param2);
               if(_loc9_ == _loc13_)
               {
                  _loc7_.addLandUpPartShow(MyFunction2.returnShowByClassName("DevelopLabel"));
               }
            }
            _loc8_ = -1 + _loc9_ % 3;
            _loc10_ = 1 + int(_loc9_ / 3);
            _loc7_.farmShowObjectVO.hI = int(param2.landData[0].@H_width) * (_loc8_ - 1);
            _loc7_.farmShowObjectVO.vJ = int(param2.landData[0].@V_height) * (_loc10_ - 1);
            _loc6_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc7_.farmShowObjectVO.hI,_loc7_.farmShowObjectVO.vJ);
            _loc7_.x = _loc6_.x;
            _loc7_.y = _loc6_.y;
            _loc7_.landShow.x = _loc6_.x;
            _loc7_.landShow.y = _loc6_.y;
            setGridsHaveThing(_loc7_,_loc7_.farmShowObjectVO.hI,_loc7_.farmShowObjectVO.vJ,int(param2.landData[0].@H_width),int(param2.landData[0].@V_height));
            _showObjectLayer.addChild(_loc7_);
            _lands.push(_loc7_);
            _loc9_++;
         }
         var _loc3_:Vector.<Land> = _lands.concat();
         _loc3_.sort(arrange);
         _loc13_ = int(_loc3_.length);
         _loc9_ = 0;
         while(_loc9_ < _loc13_)
         {
            _landShowlayer.addChild(_loc3_[_loc9_].landShow);
            _loc3_[_loc9_] = null;
            _loc9_++;
         }
         _loc3_ = null;
      }
      
      private function onMouse(param1:Event) : void
      {
         var _loc5_:DisplayObject = null;
         var _loc9_:int = 0;
         var _loc6_:Point = null;
         var _loc11_:Array = null;
         var _loc14_:int = 0;
         var _loc8_:int = 0;
         var _loc3_:IFarmShowObject = null;
         var _loc13_:DisplayObject = null;
         var _loc7_:int = 0;
         var _loc15_:int = 0;
         var _loc2_:int = 0;
         var _loc10_:int = 0;
         var _loc16_:int = 0;
         var _loc4_:int = 0;
         switch(param1.type)
         {
            case "mouseDown":
               _intervalTime = getTimer();
               break;
            case "click":
               _intervalTime = getTimer() - _intervalTime;
               if(_intervalTime < _TIME)
               {
                  _loc5_ = MyFunction2.judgeParentsIsTheClass(param1.target as DisplayObject,IFarmShowObject,this);
                  if(_loc5_)
                  {
                     if(MouseManager.getInstance().state != "move" || !(_loc5_ as IFarmShowObject).farmShowObjectVO.isEnableMove)
                     {
                        return;
                     }
                     if(_movingShowObject)
                     {
                        clearGridShape();
                        _movingShowObject.framShowState = 0;
                        _showObjectLayer.addChild(_movingShowObject as DisplayObject);
                        if(!_isEnalePutObject)
                        {
                           _movingShowObject.sethv(_originalShowObject_X,_originalShowObject_Y);
                        }
                        refreshPositionFarmShowObject();
                        setGridsHaveThing(_movingShowObject,_movingShowObject.hI,_movingShowObject.vJ,_movingShowObject.farmShowObjectVO.H_width,_movingShowObject.farmShowObjectVO.V_height);
                        _isEnalePutObject = false;
                        _movingShowObject = null;
                        break;
                     }
                     _movingShowObject = _loc5_ as IFarmShowObject;
                     _movingShowObject.framShowState = 1;
                     _moveLayer.addChild(_movingShowObject as DisplayObject);
                     _originalShowObject_X = (_loc5_ as IFarmShowObject).hI;
                     _originalShowObject_Y = (_loc5_ as IFarmShowObject).vJ;
                     setGridsNoThing((_loc5_ as IFarmShowObject).hI,(_loc5_ as IFarmShowObject).vJ,(_loc5_ as IFarmShowObject).farmShowObjectVO.H_width,(_loc5_ as IFarmShowObject).farmShowObjectVO.V_height);
                  }
               }
               break;
            case "mouseMove":
               _loc6_ = localToGlobal(new Point(mouseX,mouseY));
               _loc11_ = SceneCoordGrids.getInstance().returnHVByXY(mouseX,mouseY);
               _loc14_ = int(_loc11_[0]);
               _loc8_ = int(_loc11_[1]);
               _loc3_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc14_,_loc8_).isHaveThing;
               _loc9_ = 0;
               while(_loc9_ < _weakenShowObjects.length)
               {
                  if((_weakenShowObjects[_loc9_] as Sprite).hitTestPoint(_loc6_.x,_loc6_.y,true))
                  {
                     if(!Boolean(_loc3_))
                     {
                        (_weakenShowObjects[_loc9_] as Sprite).mouseChildren = true;
                        (_weakenShowObjects[_loc9_] as Sprite).mouseEnabled = true;
                        (_weakenShowObjects[_loc9_] as Sprite).alpha = 1;
                        _weakenShowObjects.splice(_loc9_,1);
                     }
                     else if(_loc3_ == _weakenShowObjects[_loc9_])
                     {
                        (_weakenShowObjects[_loc9_] as Sprite).mouseChildren = true;
                        (_weakenShowObjects[_loc9_] as Sprite).mouseEnabled = true;
                        (_weakenShowObjects[_loc9_] as Sprite).alpha = 1;
                        (_weakenShowObjects[_loc9_] as Sprite).dispatchEvent(new MouseEvent("rollOver"));
                        _weakenShowObjects.splice(_loc9_,1);
                     }
                  }
                  else
                  {
                     (_weakenShowObjects[_loc9_] as Sprite).mouseChildren = true;
                     (_weakenShowObjects[_loc9_] as Sprite).mouseEnabled = true;
                     (_weakenShowObjects[_loc9_] as Sprite).alpha = 1;
                     (_weakenShowObjects[_loc9_] as Sprite).dispatchEvent(new MouseEvent("rollOut"));
                     _weakenShowObjects.splice(_loc9_,1);
                  }
                  _loc9_++;
               }
               _loc13_ = MyFunction2.judgeParentsIsTheClass(param1.target as DisplayObject,IFarmShowObject,this);
               if((Boolean(_loc13_)) && _loc3_)
               {
                  if(_loc13_ != _loc3_ && _loc13_ != _movingShowObject)
                  {
                     (_loc13_ as Sprite).mouseChildren = false;
                     (_loc13_ as Sprite).mouseEnabled = false;
                     (_loc13_ as Sprite).alpha = 0.2;
                     (_loc13_ as Sprite).dispatchEvent(new MouseEvent("rollOut"));
                     (_loc3_ as Sprite).dispatchEvent(new MouseEvent("rollOver"));
                     if(_weakenShowObjects.indexOf(_loc13_ as IFarmShowObject) == -1)
                     {
                        _weakenShowObjects.push(_loc13_);
                     }
                  }
               }
               if(_movingShowObject)
               {
                  _loc7_ = Math.floor((_movingShowObject.farmShowObjectVO.H_width - 1) / 2);
                  _loc15_ = Math.ceil((_movingShowObject.farmShowObjectVO.H_width - 1) / 2);
                  _loc2_ = Math.floor((_movingShowObject.farmShowObjectVO.V_height - 1) / 2);
                  _loc10_ = Math.ceil((_movingShowObject.farmShowObjectVO.V_height - 1) / 2);
                  _loc16_ = Math.max(-30 + _loc7_,Math.min(_loc14_,30 - _loc15_));
                  _loc4_ = Math.max(-30 + _loc2_,Math.min(_loc8_,30 - _loc10_));
                  _movingShowObject.sethv(_loc16_,_loc4_);
                  clearGridShape();
                  _isEnalePutObject = judgeGridAndDrawWarningGrid(_movingShowObject.hI,_movingShowObject.vJ,_movingShowObject.farmShowObjectVO.H_width,_movingShowObject.farmShowObjectVO.V_height);
                  if(_isEnalePutObject)
                  {
                     _movingShowObject.framShowState = 1;
                     break;
                  }
                  _movingShowObject.framShowState = 2;
               }
               break;
            case "changeMouseState":
               if(_movingShowObject)
               {
                  clearGridShape();
                  _movingShowObject.framShowState = 0;
                  _showObjectLayer.addChild(_movingShowObject as DisplayObject);
                  if(!_isEnalePutObject)
                  {
                     _movingShowObject.sethv(_originalShowObject_X,_originalShowObject_Y);
                  }
                  refreshPositionFarmShowObject();
                  setGridsHaveThing(_movingShowObject,_movingShowObject.hI,_movingShowObject.vJ,_movingShowObject.farmShowObjectVO.H_width,_movingShowObject.farmShowObjectVO.V_height);
                  _isEnalePutObject = false;
                  _movingShowObject = null;
               }
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickLand",clickLand,true,0,true);
         addEventListener("mouseMove",onMouse,false,0,true);
         MouseManager.getInstance().addEventListener("changeMouseState",onMouse,false,0,true);
         addEventListener("click",onMouse,false,0,true);
         addEventListener("mouseDown",onMouse,false,0,true);
         addEventListener("mouseUp",onMouse,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("mouseMove",onMouse,false);
         removeEventListener("click",onMouse,false);
         removeEventListener("mouseDown",onMouse,false);
         removeEventListener("mouseUp",onMouse,false);
         MouseManager.getInstance().removeEventListener("changeMouseState",onMouse,false);
         removeEventListener("clickLand",clickLand,true);
      }
      
      private function clickLand(param1:UIPassiveEvent) : void
      {
         var dataObj:Object;
         var prices:Vector.<int>;
         var ticketIds:Vector.<String>;
         var price:int;
         var ticketId:String;
         var farmXML:XML;
         var index:int;
         var equipmentXML:XML;
         var harvestArray:Array;
         var isSuccess:Boolean;
         var harvestNum:int;
         var harvestEquipmentName:String;
         var addSunValue:int;
         var saveinfo:SaveTaskInfo;
         var farmParent:Farm;
         var landVO:LandVO;
         var e:UIPassiveEvent = param1;
         var land:Land = e.target as Land;
         if(!land.landDrive.landVO || !land.landDrive.landVO.id)
         {
            index = int(_lands.indexOf(land));
            if(index == FarmData.getInstance().ownerLands.length)
            {
               farmXML = XMLSingle.getInstance().farmXML;
               prices = MyFunction.getInstance().excreteString(String(farmXML.openLandPrice[0].@ticketPrices));
               ticketIds = MyFunction.getInstance().excreteStringToString(farmXML.openLandPrice[0].@ticketIds);
               price = prices[index];
               ticketId = ticketIds[index];
               dataObj = {};
               dataObj["propId"] = ticketId;
               dataObj["count"] = 1;
               dataObj["price"] = price;
               dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
               dataObj["tag"] = "购买土地";
               (parent as Farm).showWarningBox("是否花费 <font size=\'" + (WarningBoxSingle.getInstance().getTextFontSize() + 9) + "\' color=\'#ff0000\'>" + price + "</font>点券开启该土地！",3,{
                  "type":"buyLand",
                  "buyDataObj":dataObj,
                  "land":land
               });
            }
            else if(index > FarmData.getInstance().ownerLands.length)
            {
               (parent as Farm).showWarningBox("这块土地现在还不能扩建！",0);
            }
            return;
         }
         switch(land.landDrive.landVO.state)
         {
            case 0:
               (parent as Farm).showBuyPlantBox(land);
               break;
            case 1:
               break;
            case 2:
               equipmentXML = XMLSingle.getInstance().equipmentXML;
               harvestArray = FarmFunction.getInstance().getHarvestEquipmentsAndSunValue(land.landDrive.landVO.equipmentIDInLand,land,equipmentXML,XMLSingle.getInstance().farmXML);
               isSuccess = Boolean(harvestArray[0]);
               harvestNum = int(harvestArray[1]);
               harvestEquipmentName = harvestArray[2];
               addSunValue = int(harvestArray[3]);
               if(isSuccess)
               {
                  land.landDrive.changeLandVOState = 3;
                  land.landDrive.changeLandDataPlantState = "witherState";
                  FarmData.getInstance().currentSunValue = FarmData.getInstance().currentSunValue + addSunValue;
                  (parent as Farm).initLevel(XMLSingle.getInstance().farmXML);
                  (parent as Farm).showWarningBox("恭喜收获" + harvestNum + "个" + harvestEquipmentName + "!\n" + "阳光值＋" + addSunValue,0);
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399";
                  saveinfo.isHaveData = false;
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame();
                  break;
               }
               (parent as Farm).showWarningBox("背包空间不足！",0);
               break;
            case 3:
               farmParent = parent as Farm;
               landVO = land.landDrive.landVO;
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  var _loc2_:Object = null;
                  landVO.date = param1;
                  landVO.state = 4;
                  if(Boolean(land) && land.landDrive)
                  {
                     land.landDrive.changeLandVOState = 4;
                     _loc2_ = FarmFunction.getInstance().countLandStateRemainTime(landVO,param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML);
                     land.landDrive.landData.remainTime = _loc2_.remainTime;
                     land.landDrive.landData.vipAccLandRecoverTime = _loc2_.vipAccLandRecoverTime;
                  }
               },!!farmParent ? farmParent.showWarningBox : null,false);
               break;
            case 4:
               prices = MyFunction.getInstance().excreteString(String(XMLSingle.getInstance().farmXML.land.(@id == land.landDrive.landVO.id).@recoverLandTickets));
               ticketIds = MyFunction.getInstance().excreteStringToString(String(XMLSingle.getInstance().farmXML.land.(@id == land.landDrive.landVO.id).@recoverLandTicketIds));
               price = prices[Math.min(FarmData.getInstance().recoverLandNum,prices.length - 1)];
               ticketId = ticketIds[Math.min(FarmData.getInstance().recoverLandNum,ticketIds.length - 1)];
               dataObj = {};
               dataObj["propId"] = ticketId;
               dataObj["count"] = 1;
               dataObj["price"] = price;
               dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
               dataObj["tag"] = "恢复土地";
               (parent as Farm).showWarningBox("是否花费 <font size=\'" + (WarningBoxSingle.getInstance().getTextFontSize() + 9) + "\' color=\'#ff0000\'>" + price + "</font>点券恢复该土地！",3,{
                  "type":"recoverLand",
                  "buyDataObj":dataObj,
                  "land":land
               });
               break;
            default:
               throw new Error("土地状态类型错误！");
         }
      }
      
      private function drawGrids() : void
      {
         var _loc4_:int = 0;
         var _loc2_:SceneCoordGrid = null;
         var _loc1_:TextField = null;
         var _loc6_:Sprite = new Sprite();
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         _loc6_.graphics.lineStyle(2,16711680,1);
         _loc7_ = 0;
         while(_loc7_ < 61)
         {
            _loc4_ = 61;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc2_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc7_ + -30,_loc5_ + -30);
               _loc6_.graphics.moveTo(_loc2_.x - 30,_loc2_.y);
               _loc6_.graphics.lineTo(_loc2_.x,_loc2_.y + 15);
               _loc6_.graphics.lineTo(_loc2_.x + 30,_loc2_.y);
               _loc6_.graphics.lineTo(_loc2_.x,_loc2_.y - 15);
               _loc6_.graphics.lineTo(_loc2_.x - 30,_loc2_.y);
               _loc1_ = new TextField();
               _loc1_.text = "" + (_loc7_ + -30) + "_" + (_loc5_ + -30);
               _loc1_.width = _loc1_.textWidth + 5;
               _loc1_.height = _loc1_.textHeight + 2;
               _loc1_.x = _loc2_.x - _loc1_.width / 2;
               _loc1_.y = _loc2_.y - _loc1_.height / 2;
               _loc6_.addChild(_loc1_);
               _loc5_++;
            }
            _loc7_++;
         }
         addChildAt(_loc6_,2);
      }
      
      private function setGridsHaveThing(param1:IFarmShowObject, param2:int, param3:int, param4:int, param5:int) : void
      {
         var _loc8_:int = 0;
         var _loc14_:int = 0;
         var _loc6_:int = 0;
         var _loc12_:int = 0;
         var _loc11_:int = 0;
         var _loc9_:int = 0;
         var _loc7_:SceneCoordGrid = null;
         _loc8_ = Math.floor((param4 - 1) / 2);
         _loc14_ = Math.ceil((param4 - 1) / 2);
         _loc6_ = Math.floor((param5 - 1) / 2);
         _loc12_ = Math.ceil((param5 - 1) / 2);
         var _loc13_:* = param4;
         var _loc10_:* = param5;
         _loc11_ = 0;
         while(_loc11_ < _loc13_)
         {
            _loc9_ = 0;
            while(_loc9_ < _loc10_)
            {
               _loc7_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(param2 - _loc8_ + _loc11_,param3 - _loc6_ + _loc9_);
               _loc7_.isHaveThing = param1;
               _loc9_++;
            }
            _loc11_++;
         }
      }
      
      private function setGridsNoThing(param1:int, param2:int, param3:int, param4:int) : void
      {
         var _loc7_:int = 0;
         var _loc13_:int = 0;
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         var _loc10_:int = 0;
         var _loc8_:int = 0;
         var _loc6_:SceneCoordGrid = null;
         _loc7_ = Math.floor((param3 - 1) / 2);
         _loc13_ = Math.ceil((param3 - 1) / 2);
         _loc5_ = Math.floor((param4 - 1) / 2);
         _loc11_ = Math.ceil((param4 - 1) / 2);
         var _loc12_:* = param3;
         var _loc9_:* = param4;
         _loc10_ = 0;
         while(_loc10_ < _loc12_)
         {
            _loc8_ = 0;
            while(_loc8_ < _loc9_)
            {
               _loc6_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(param1 - _loc7_ + _loc10_,param2 - _loc5_ + _loc8_);
               _loc6_.isHaveThing = null;
               _loc8_++;
            }
            _loc10_++;
         }
      }
      
      private function judgeGridAndDrawWarningGrid(param1:int, param2:int, param3:int, param4:int) : Boolean
      {
         var _loc8_:int = 0;
         var _loc17_:int = 0;
         var _loc5_:int = 0;
         var _loc13_:int = 0;
         var _loc12_:int = 0;
         var _loc9_:int = 0;
         var _loc11_:int = 0;
         var _loc14_:int = 0;
         var _loc15_:int = 0;
         var _loc18_:Boolean = true;
         var _loc6_:Boolean = false;
         _loc8_ = Math.floor((param3 - 1) / 2);
         _loc17_ = Math.ceil((param3 - 1) / 2);
         _loc5_ = Math.floor((param4 - 1) / 2);
         _loc13_ = Math.ceil((param4 - 1) / 2);
         var _loc16_:* = param3;
         var _loc7_:* = param4;
         var _loc10_:int = int(_farmBlocks.length);
         _loc12_ = 0;
         while(_loc12_ < _loc16_)
         {
            _loc9_ = 0;
            while(_loc9_ < _loc7_)
            {
               _loc6_ = true;
               _loc14_ = param1 - _loc8_ + _loc12_;
               _loc15_ = param2 - _loc5_ + _loc9_;
               _loc11_ = 0;
               while(_loc11_ < _loc10_)
               {
                  if(_farmBlocks[_loc11_].isDeveloped && _loc14_ >= _farmBlocks[_loc11_].leftMinCo && _loc14_ <= _farmBlocks[_loc11_].rightMaxCo && _loc15_ >= _farmBlocks[_loc11_].upMinCo && _loc15_ <= _farmBlocks[_loc11_].downMaxCo)
                  {
                     if(!SceneCoordGrids.getInstance().returnSceneCoordByHV(_loc14_,_loc15_).isHaveThing)
                     {
                        _loc6_ = false;
                     }
                  }
                  _loc11_++;
               }
               if(_loc6_)
               {
                  drawWarningGrid(_loc14_,_loc15_);
               }
               _loc18_ &&= !_loc6_;
               _loc9_++;
            }
            _loc12_++;
         }
         return _loc18_;
      }
      
      private function clearGridShape() : void
      {
         _gridShape.graphics.clear();
      }
      
      private function drawWarningGrid(param1:int, param2:int) : void
      {
         var _loc3_:SceneCoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(param1,param2);
         _gridShape.graphics.lineStyle(1,16724889,0);
         _gridShape.graphics.beginFill(16724889,0.5);
         _gridShape.graphics.moveTo(_loc3_.x - 30,_loc3_.y);
         _gridShape.graphics.lineTo(_loc3_.x,_loc3_.y + 15);
         _gridShape.graphics.lineTo(_loc3_.x + 30,_loc3_.y);
         _gridShape.graphics.lineTo(_loc3_.x,_loc3_.y - 15);
         _gridShape.graphics.lineTo(_loc3_.x - 30,_loc3_.y);
         _gridShape.graphics.endFill();
      }
      
      private function refreshPositionFarmShowObject() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IFarmShowObject> = _otherShowObect.concat(_lands);
         _loc1_.sort(arrange);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _showObjectLayer.addChild(_loc1_[_loc3_] as DisplayObject);
            _loc3_++;
         }
      }
      
      private function arrange(param1:IFarmShowObject, param2:IFarmShowObject) : Number
      {
         if(param2.hI != param1.hI)
         {
            return param2.hI - param1.hI;
         }
         return param1.vJ - param2.vJ;
      }
   }
}

