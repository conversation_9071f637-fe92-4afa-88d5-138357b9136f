package UI.Protect
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class BuyProtectSelectSlot extends SwitchBtn
   {
      public var txt:TextField;
      
      private var _object:Object;
      
      public function BuyProtectSelectSlot()
      {
         super();
         initSlot();
      }
      
      public function set value(param1:Object) : void
      {
         _object = param1;
         txt.text = param1.time / 24 + "天（" + param1.pointTicket + "点券）";
      }
      
      override public function clear() : void
      {
         super.clear();
         txt = null;
         _object = null;
         BuyProtectSlotBorder.getInstance().clear();
      }
      
      private function initSlot() : void
      {
         txt.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,14,16777215);
         txt.filters = [new GlowFilter(0,1,2,2,10,3)];
         txt.embedFonts = true;
         txt.selectable = false;
         txt.wordWrap = true;
         txt.mouseEnabled = false;
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchBuyProtectSelectSlot",_object));
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("rollOut",onRollOut,false,0,true);
         addEventListener("rollOver",onRollOver,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("rollOut",onRollOut,false);
         removeEventListener("rollOver",onRollOver,false);
      }
      
      private function onRollOut(param1:MouseEvent) : void
      {
         if(getChildByName(BuyProtectSlotBorder.getInstance().name))
         {
            removeChild(BuyProtectSlotBorder.getInstance());
         }
      }
      
      private function onRollOver(param1:MouseEvent) : void
      {
         BuyProtectSlotBorder.getInstance().x = 0;
         BuyProtectSlotBorder.getInstance().y = 0;
         addChild(BuyProtectSlotBorder.getInstance());
      }
   }
}

