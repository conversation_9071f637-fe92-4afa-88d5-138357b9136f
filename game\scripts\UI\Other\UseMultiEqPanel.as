package UI.Other
{
   import UI.EquipEquipmentFuns.EquipEqListener;
   import UI.EquipEquipmentFuns.EquipEquipmentFunction;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class UseMultiEqPanel extends MySprite
   {
      private var m_show:Sprite;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_eqContainer:Sprite;
      
      private var m_numBtnGroup:NumberBtnGroupLogicShell;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_dragLG:AbleDragSpriteLogicShell;
      
      private var m_wantLoadSources:Array = ["useEquipmentPanel"];
      
      private var m_useEq:Equipment;
      
      private var m_useEqVO:StackEquipmentVO;
      
      private var m_usePlayer:Player;
      
      private var m_myControlPanel:MyControlPanel;
      
      private var m_equipEquipmentFun:EquipEquipmentFunction;
      
      public function UseMultiEqPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("clickButton",clickButton,true);
         GamingUI.getInstance().loadQueue.unLoad(m_wantLoadSources);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_eqContainer);
         m_eqContainer = null;
         ClearUtil.clearObject(m_numBtnGroup);
         m_numBtnGroup = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         ClearUtil.clearObject(m_dragLG);
         m_dragLG = null;
         ClearUtil.clearObject(m_wantLoadSources);
         m_wantLoadSources = null;
         if(m_useEq)
         {
            m_useEq.equipmentVO = null;
         }
         ClearUtil.clearObject(m_useEq);
         m_useEq = null;
         m_useEqVO = null;
         m_usePlayer = null;
         m_myControlPanel = null;
         m_equipEquipmentFun = null;
      }
      
      public function init(param1:EquipmentVO, param2:Player, param3:MyControlPanel, param4:EquipEquipmentFunction) : void
      {
         var loadFinishListenr:LoadFinishListener1;
         var useEqVO:EquipmentVO = param1;
         var usePlayer:Player = param2;
         var myControlPanel:MyControlPanel = param3;
         var equipEquipmentFun:EquipEquipmentFunction = param4;
         if(!(useEqVO is StackEquipmentVO))
         {
            throw new Error("物品必须是叠加物品");
         }
         if((useEqVO as StackEquipmentVO).num == 0)
         {
            throw new Error("出错了额， 物品的数量为0");
         }
         m_useEqVO = useEqVO as StackEquipmentVO;
         m_usePlayer = usePlayer;
         m_myControlPanel = myControlPanel;
         m_equipEquipmentFun = equipEquipmentFun;
         if(m_show)
         {
            init2();
         }
         else
         {
            loadFinishListenr = new LoadFinishListener1(function():void
            {
               if(m_show == null)
               {
                  m_show = MyFunction2.returnShowByClassName("UseMultiEqPanel") as Sprite;
               }
               if(stage)
               {
                  m_show.x = (stage.stageWidth - m_show.width) / 2;
                  m_show.y = (stage.stageHeight - m_show.height) / 2;
               }
               addChild(m_show);
               m_quitBtn = new ButtonLogicShell2();
               m_quitBtn.setShow(m_show["quitBtn2"]);
               m_quitBtn.setTipString("点击关闭");
               m_eqContainer = m_show["eqContainer"];
               m_numBtnGroup = new NumberBtnGroupLogicShell();
               m_numBtnGroup.setShow(m_show["numBtnGroup"]);
               m_numBtnGroup.minNum = 1;
               m_numBtnGroup.isOpenInput = true;
               m_numBtnGroup.num = 1;
               m_sureBtn = new ButtonLogicShell2();
               m_sureBtn.setShow(m_show["sureBtn"]);
               m_cancelBtn = new ButtonLogicShell2();
               m_cancelBtn.setShow(m_show["cancelBtn"]);
               m_dragLG = new AbleDragSpriteLogicShell();
               m_dragLG.setShow(m_show);
               init2();
            },null);
            GamingUI.getInstance().loadQueue.load(m_wantLoadSources,loadFinishListenr);
         }
      }
      
      private function init2() : void
      {
         ClearUtil.clearObject(m_useEq);
         m_useEq = MyFunction2.sheatheEquipmentShell(m_useEqVO);
         m_eqContainer.addChild(m_useEq);
         m_numBtnGroup.maxNum = m_useEqVO.num;
         m_numBtnGroup.num = 1;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quitBtn:
            case m_cancelBtn:
               m_myControlPanel.closeUseMultiEqPanel();
               break;
            case m_sureBtn:
               useEq();
         }
      }
      
      private function useEq() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            useEq2(m_useEqVO,m_usePlayer,m_myControlPanel,param1,m_numBtnGroup.num);
         },m_myControlPanel.showWarningBox,true);
      }
      
      private function useEq2(param1:EquipmentVO, param2:Player, param3:MyControlPanel, param4:String, param5:int) : void
      {
         var equipEqListener:EquipEqListener;
         var useEquipVO:EquipmentVO = param1;
         var usePlayer:Player = param2;
         var myControlPanel:MyControlPanel = param3;
         var timeStr:String = param4;
         var remaineNum:int = param5;
         if(remaineNum)
         {
            equipEqListener = new EquipEqListener();
            equipEqListener.actionAfterUnableUseFun = myControlPanel.closeUseMultiEqPanel;
            equipEqListener.actionAfterUseFun = function():void
            {
               useEq2(useEquipVO,usePlayer,myControlPanel,timeStr,remaineNum - 1);
            };
            equipEqListener.getWarningTextFontSizeFun = myControlPanel.getWarningTextFontSize;
            equipEqListener.showWarningFun = function(param1:String, param2:int):void
            {
               myControlPanel.showWarning(param1,param2);
            };
            m_equipEquipmentFun.EquipEquipmentVOAction(useEquipVO,usePlayer,equipEqListener,timeStr);
         }
         else
         {
            myControlPanel.closeUseMultiEqPanel();
         }
      }
   }
}

