package UI2.Consumer
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ConsumerView extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_btn1:MySwitchBtnLogicShell;
      
      private var m_btn2:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var btnClose:ButtonLogicShell2;
      
      private var m_tipMC1:MovieClip;
      
      private var m_tipMC2:MovieClip;
      
      private var m_txtTip1:TextField;
      
      private var m_txtTip2:TextField;
      
      private var m_preBtn:ButtonLogicShell2;
      
      private var m_nextBtn:ButtonLogicShell2;
      
      private var m_consumerItem:ConsumerItem;
      
      public function ConsumerView()
      {
         super();
         this.name = "ConsumerView";
         this.mouseChildren = true;
         this.mouseEnabled = true;
         initParams();
         initShow();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(btnClose);
         btnClose = null;
         ClearUtil.clearObject(m_btn1);
         m_btn1 = null;
         ClearUtil.clearObject(m_btn2);
         m_btn2 = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         super.clear();
      }
      
      private function initParams() : void
      {
         ConsumerData.getInstance().requirePaied();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_btn1 = new MySwitchBtnLogicShell();
         m_btn2 = new MySwitchBtnLogicShell();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtnGroup.addSwitchBtn(m_btn1);
         m_switchBtnGroup.addSwitchBtn(m_btn2);
         btnClose = new ButtonLogicShell2();
         m_preBtn = new ButtonLogicShell2();
         m_nextBtn = new ButtonLogicShell2();
         m_consumerItem = new ConsumerItem();
      }
      
      private function initShow() : void
      {
         m_show = MyFunction2.returnShowByClassName("ConsumerView") as MovieClip;
         this.addChild(m_show);
         m_consumerItem.setParams(this,m_show);
         m_btn1.setShow(m_show["daypaybtn"]);
         m_btn2.setShow(m_show["weekpaybtn"]);
         m_switchBtnGroup.addEnd();
         btnClose.setShow(m_show["closebtn"]);
         btnClose.setTipString("关闭");
         m_preBtn.setShow(m_show["prebtn"]);
         m_preBtn.setTipString("上一页");
         m_nextBtn.setShow(m_show["nextbtn"]);
         m_nextBtn.setTipString("下一页");
         m_tipMC1 = m_show["daypaybtn"]["tipmc"] as MovieClip;
         m_tipMC2 = m_show["weekpaybtn"]["tipmc"] as MovieClip;
         m_txtTip1 = m_tipMC1["txtrewardnum"] as TextField;
         m_txtTip2 = m_tipMC2["txtrewardnum"] as TextField;
         m_txtTip1.mouseEnabled = false;
         m_txtTip2.mouseEnabled = false;
         udpateTip(null);
      }
      
      private function udpateTip(param1:Event) : void
      {
         if(ConsumerData.getInstance().getDayNum() > 0)
         {
            m_tipMC1.visible = true;
            m_txtTip1.text = String(ConsumerData.getInstance().getDayNum());
         }
         else
         {
            m_tipMC1.visible = false;
         }
         if(ConsumerData.getInstance().getWeekNum() > 0)
         {
            m_tipMC2.visible = true;
            m_txtTip2.text = String(ConsumerData.getInstance().getWeekNum());
         }
         else
         {
            m_tipMC2.visible = false;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case btnClose:
               clear();
               GamingUI.getInstance().clearConsumer();
               break;
            case m_preBtn:
               if(m_consumerItem)
               {
                  m_consumerItem.prePage();
               }
               break;
            case m_nextBtn:
               if(m_consumerItem)
               {
                  m_consumerItem.nextPage();
               }
               break;
            case m_btn1:
               if(m_consumerItem)
               {
                  m_consumerItem.refreshPage(1,1);
               }
               break;
            case m_btn2:
               if(m_consumerItem)
               {
                  m_consumerItem.refreshPage(2,1);
                  break;
               }
         }
         if(m_consumerItem)
         {
            m_consumerItem.clickButton(param1);
         }
      }
      
      public function refresh() : void
      {
         if(m_consumerItem)
         {
            m_consumerItem.refresh();
         }
         udpateTip(null);
      }
      
      public function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      public function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
   }
}

