package UI.WorldBoss.DaZhao.NotAttackDaZhao
{
   import UI.SoundManager.SoundManager;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Boss.BossFactory;
   import UI.WorldBoss.Boss.CreateOwnImageBoss.CreateOwnImageBoss;
   import UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss;
   import UI.WorldBoss.Entity;
   import UI.WorldBoss.Position;
   import UI.WorldBoss.View;
   import UI.WorldBoss.WorldBoss;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class CreateOwnImageDa<PERSON>hao extends NotAttackDaZhao
   {
      private var m_roundNum:uint;
      
      private var m_createImageNum:uint;
      
      private var m_imageBossClassName:String;
      
      private var m_roundNumOfImageLife:uint;
      
      public function CreateOwnImageDaZhao()
      {
         super();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_roundNum = uint(param1.@roundNum);
         m_createImageNum = uint(param1.@createImageNum);
         m_imageBossClassName = String(param1.@imageBossClassName);
         m_roundNumOfImageLife = uint(param1.@roundNumOfImageLife);
      }
      
      override public function isRunDaZhao() : Boolean
      {
         var _loc1_:int = m_world.getGetNextStepActionEnities().getRoundNum();
         if(_loc1_ % m_roundNum == 0)
         {
            return true;
         }
         return false;
      }
      
      public function createImageBosses(param1:CreateOwnImageBoss, param2:YJFYLoader, param3:WorldBoss, param4:View, param5:Sprite, param6:SoundManager, param7:XML) : Vector.<Boss>
      {
         var _loc12_:int = 0;
         var _loc9_:ImageBoss = null;
         var _loc11_:BossFactory = null;
         var _loc14_:* = undefined;
         var _loc10_:Vector.<Boss> = new Vector.<Boss>();
         var _loc13_:int = m_world.getEnemyNum();
         var _loc8_:int = Math.min(m_world.getExtraData()["bossMaxNum"] - _loc13_,m_createImageNum);
         trace("创建镜像副本boss","数量",_loc8_);
         if(_loc8_)
         {
            _loc11_ = new BossFactory();
            _loc14_ = randomGetNPlace(param3,_loc8_);
         }
         _loc12_ = 0;
         while(_loc12_ < _loc8_)
         {
            _loc9_ = _loc11_.createBossByClassNameAndXML(m_imageBossClassName,param7,param2) as ImageBoss;
            _loc9_.setPosition(_loc14_[_loc12_]);
            _loc9_.setFightPositionSprite(param3.getRightMiddlePosition());
            _loc9_.setAnimationQueueData(param3.getAnimationQueueData());
            _loc9_.setView(param4);
            _loc9_.setFightStage(param5);
            _loc9_.setSoundManager(param6);
            _loc9_.setWorldBoss(param3);
            _loc9_.setRoundNumOfCreate(m_world.getGetNextStepActionEnities().getRoundNum());
            _loc9_.setRoundNumOfLife(m_roundNumOfImageLife);
            _loc9_.cloneRealityAtt(param1.getLevel(),param1.getAttack(),param1.getCriticalRate(),param1.getCriticalMuti(),param1.getHit());
            _loc10_.push(_loc9_);
            m_world.addEnemy(_loc9_);
            _loc12_++;
         }
         return _loc10_;
      }
      
      private function randomGetNPlace(param1:WorldBoss, param2:int) : Vector.<Position>
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc11_:int = 0;
         var _loc12_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:Position = null;
         var _loc13_:Entity = null;
         var _loc4_:Boolean = false;
         var _loc6_:int = 0;
         var _loc14_:Vector.<Position> = new Vector.<Position>();
         _loc12_ = 2;
         _loc8_ = 3;
         _loc9_ = m_world.getEnemyNum();
         _loc10_ = 0;
         while(_loc10_ < _loc12_)
         {
            _loc7_ = 0;
            while(_loc7_ < _loc8_)
            {
               _loc5_ = param1.getRightPosition(_loc10_ + 1,_loc7_ + 1);
               _loc4_ = true;
               _loc11_ = 0;
               while(_loc11_ < _loc9_)
               {
                  _loc13_ = m_world.getEnemyByIndex(_loc11_) as Entity;
                  if(_loc5_ == _loc13_.getPositioin())
                  {
                     _loc4_ = false;
                  }
                  _loc11_++;
               }
               if(_loc4_)
               {
                  _loc14_.push(_loc5_);
               }
               _loc7_++;
            }
            _loc10_++;
         }
         var _loc3_:Vector.<Position> = new Vector.<Position>();
         _loc10_ = 0;
         while(_loc10_ < param2)
         {
            _loc6_ = _loc14_.length * Math.random();
            _loc3_.push(_loc14_[_loc6_]);
            _loc14_.splice(_loc6_,1);
            _loc10_++;
         }
         ClearUtil.nullArr(_loc14_,false,false,false);
         return _loc3_;
      }
   }
}

