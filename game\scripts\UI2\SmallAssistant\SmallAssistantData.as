package UI2.SmallAssistant
{
   import UI.GamingUI;
   import UI2.SmallAssistant.ActiveTask.ActiveTaskRewardData;
   import UI2.SmallAssistant.ActiveTask.ActiveTasksSaveData;
   import UI2.SmallAssistant.ActiveTask.ActiveValueDataOfTask;
   import UI2.SmallAssistant.ActiveTask.HaveActiveValueTaskData;
   import UI2.SmallAssistant.LevelTask.LevelTaskRewardData;
   import UI2.SmallAssistant.LevelTask.LevelTasksSaveData;
   import YJFY.Utils.ClearUtil;
   
   public class SmallAssistantData
   {
      private var m_activeTaskRewardDatas:Vector.<ActiveTaskRewardData>;
      
      private var m_haveActiveValueTaskDatas:Vector.<HaveActiveValueTaskData>;
      
      private var m_levelTaskRewardDatas:Vector.<LevelTaskRewardData>;
      
      private var m_smallAssistantSaveData:SmallAssistantSaveData;
      
      private var m_smallAssistantXML:XML;
      
      public function SmallAssistantData()
      {
         super();
         m_activeTaskRewardDatas = new Vector.<ActiveTaskRewardData>();
         m_levelTaskRewardDatas = new Vector.<LevelTaskRewardData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_activeTaskRewardDatas);
         m_activeTaskRewardDatas = null;
         ClearUtil.clearObject(m_haveActiveValueTaskDatas);
         m_haveActiveValueTaskDatas = null;
         ClearUtil.clearObject(m_levelTaskRewardDatas);
         m_levelTaskRewardDatas = null;
         m_smallAssistantXML = null;
         m_smallAssistantSaveData = null;
      }
      
      public function init(param1:XML, param2:SmallAssistantSaveData) : void
      {
         var _loc6_:XMLList = null;
         var _loc11_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:ActiveTaskRewardData = null;
         var _loc10_:HaveActiveValueTaskData = null;
         var _loc7_:ActiveValueDataOfTask = null;
         var _loc3_:LevelTaskRewardData = null;
         m_smallAssistantXML = param1;
         m_smallAssistantSaveData = param2;
         _loc6_ = param1.activeTaskData[0].rewardDatas[0].rewardData;
         _loc5_ = int(_loc6_.length());
         _loc11_ = 0;
         while(_loc11_ < _loc5_)
         {
            _loc4_ = new ActiveTaskRewardData();
            _loc4_.initByXML(_loc6_[_loc11_]);
            m_activeTaskRewardDatas.push(_loc4_);
            _loc11_++;
         }
         ClearUtil.clearObject(m_haveActiveValueTaskDatas);
         m_haveActiveValueTaskDatas = new Vector.<HaveActiveValueTaskData>(m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVONum());
         _loc6_ = param1.activeTaskData[0].activeValueDatas[0].activeValueData;
         _loc5_ = int(_loc6_.length());
         var _loc8_:uint = m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVONum();
         _loc11_ = 0;
         while(_loc11_ < _loc5_)
         {
            _loc7_ = new ActiveValueDataOfTask();
            _loc7_.initByXML(_loc6_[_loc11_]);
            _loc10_ = null;
            _loc9_ = 0;
            while(_loc9_ < _loc8_)
            {
               if(m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVOByIndex(_loc9_).id == _loc7_.getTaskId())
               {
                  _loc10_ = new HaveActiveValueTaskData(m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVOByIndex(_loc9_),_loc7_);
                  if(m_haveActiveValueTaskDatas[_loc9_])
                  {
                     throw new Error("出错了");
                  }
                  m_haveActiveValueTaskDatas[_loc9_] = _loc10_;
               }
               _loc9_++;
            }
            if(_loc10_ == null)
            {
               throw new Error("任务活跃值没有对应的任务");
            }
            _loc11_++;
         }
         _loc6_ = param1.levelTaskData[0].rewardDatas[0].rewardData;
         _loc5_ = int(_loc6_.length());
         _loc11_ = 0;
         while(_loc11_ < _loc5_)
         {
            _loc3_ = new LevelTaskRewardData();
            _loc3_.initByXML(_loc6_[_loc11_]);
            m_levelTaskRewardDatas.push(_loc3_);
            _loc11_++;
         }
      }
      
      public function getSmallAssistantXML() : XML
      {
         return m_smallAssistantXML;
      }
      
      public function getActiveTaskRewardDataNum() : uint
      {
         return m_activeTaskRewardDatas.length;
      }
      
      public function getActiveTaskRewardDataByIndex(param1:int) : ActiveTaskRewardData
      {
         return m_activeTaskRewardDatas[param1];
      }
      
      public function getHaveActiveValueTaskDataNum() : uint
      {
         return m_haveActiveValueTaskDatas.length;
      }
      
      public function getHaveActiveValueTaskDataByIndex(param1:int) : HaveActiveValueTaskData
      {
         return m_haveActiveValueTaskDatas[param1];
      }
      
      public function getLevelTaskRewardDataNum() : uint
      {
         return m_levelTaskRewardDatas.length;
      }
      
      public function getLevelTaskRewardDataByIndex(param1:int) : LevelTaskRewardData
      {
         return m_levelTaskRewardDatas[param1];
      }
      
      public function getIsGotActiveRewardbyId(param1:String) : Boolean
      {
         if(m_smallAssistantSaveData.getActiveTasksSaveData().getIsGotByRewardId(param1))
         {
            return true;
         }
         return false;
      }
      
      public function getActiveTasksSaveData() : ActiveTasksSaveData
      {
         return m_smallAssistantSaveData.getActiveTasksSaveData();
      }
      
      public function getLevelTasksSaveData() : LevelTasksSaveData
      {
         return m_smallAssistantSaveData.getLevelTasksSaveData();
      }
      
      public function getIsGotLevelTaskRewardById(param1:String) : Boolean
      {
         if(m_smallAssistantSaveData.getLevelTasksSaveData().getIsGotByRewardId(param1))
         {
            return true;
         }
         return false;
      }
      
      public function getCurrentActiveValue() : uint
      {
         var _loc2_:* = 0;
         var _loc3_:int = 0;
         var _loc1_:int = int(m_haveActiveValueTaskDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(m_haveActiveValueTaskDatas[_loc3_].getTaskVO().isFinish)
            {
               _loc2_ += m_haveActiveValueTaskDatas[_loc3_].getActiveValueDataOfTask().getActiveValue();
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getTotalActiveValue() : uint
      {
         var _loc2_:* = 0;
         var _loc3_:int = 0;
         var _loc1_:int = int(m_haveActiveValueTaskDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ += m_haveActiveValueTaskDatas[_loc3_].getActiveValueDataOfTask().getActiveValue();
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getActiveTasksRedTipNum() : uint
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = int(m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVONum());
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(m_smallAssistantSaveData.getActiveTasksSaveData().getTaskVOByIndex(_loc3_).isFinish == false)
            {
               _loc2_++;
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getActiveTaskYellowTipNum() : uint
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:uint = getCurrentActiveValue();
         _loc1_ = int(m_activeTaskRewardDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            if(!m_smallAssistantSaveData.getActiveTasksSaveData().getIsGotByRewardId(m_activeTaskRewardDatas[_loc4_].getId()))
            {
               if(_loc3_ >= m_activeTaskRewardDatas[_loc4_].getNeedMinActiveValue())
               {
                  _loc2_++;
               }
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function getLevelTasksYellowTipNum() : uint
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:uint = Math.max(GamingUI.getInstance().player1.playerVO.level,Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO) ? GamingUI.getInstance().player2.playerVO.level : 0);
         _loc1_ = int(m_levelTaskRewardDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            if(!m_smallAssistantSaveData.getLevelTasksSaveData().getIsGotByRewardId(m_levelTaskRewardDatas[_loc4_].getId()))
            {
               if(_loc3_ >= m_levelTaskRewardDatas[_loc4_].getNeedMinLevel())
               {
                  _loc2_++;
               }
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function getSmallAssistantMaxPriorityRedTipNum() : uint
      {
         var _loc2_:* = 0;
         _loc2_ = getActiveTasksRedTipNum();
         if(_loc2_)
         {
            return _loc2_;
         }
         return _loc2_;
      }
      
      public function getSmallAssistantMaxPriorityYellowTipNum() : uint
      {
         var _loc1_:* = 0;
         _loc1_ = getActiveTaskYellowTipNum();
         if(_loc1_)
         {
            return _loc1_;
         }
         _loc1_ = getLevelTasksYellowTipNum();
         if(_loc1_)
         {
            return _loc1_;
         }
         return _loc1_;
      }
   }
}

