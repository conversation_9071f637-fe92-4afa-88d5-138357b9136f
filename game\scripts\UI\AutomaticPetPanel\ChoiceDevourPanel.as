package UI.AutomaticPetPanel
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ShopWall.ShopWall_BuyPopUpBox;
   import UI.XMLSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class ChoiceDevourPanel extends MySprite
   {
      public static const SHOW_GUIDE_BOX:String = "showGuideBox";
      
      private var m_show:MovieClip;
      
      private var m_choiceAutomaticPetVOs:Vector.<AutomaticPetVO>;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_buyXiaoxia:ButtonLogicShell2;
      
      private var m_chooseDevourCheckBoxs:Vector.<ChoiceDevourCheckBox>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_automaticPetVOs:Vector.<AutomaticPetVO>;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_strengthenPanel:StrengthenPanel;
      
      private var popUpBox:ShopWall_BuyPopUpBox;
      
      public function ChoiceDevourPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_buyXiaoxia = new ButtonLogicShell2();
         m_choiceAutomaticPetVOs = new Vector.<AutomaticPetVO>();
         m_chooseDevourCheckBoxs = new Vector.<ChoiceDevourCheckBox>();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_automaticPetVOs = new Vector.<AutomaticPetVO>();
         m_sureBtn = new ButtonLogicShell2();
         m_cancelBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.nullArr(m_automaticPetVOs,false,false,false);
         m_automaticPetVOs = null;
         ClearUtil.clearObject(popUpBox);
         popUpBox = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.nullArr(m_choiceAutomaticPetVOs,false,false,false);
         m_choiceAutomaticPetVOs = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_buyXiaoxia);
         m_buyXiaoxia = null;
         ClearUtil.clearObject(m_chooseDevourCheckBoxs);
         m_chooseDevourCheckBoxs = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         m_automaticPetVO = null;
         m_automaticPetVOs = null;
         m_strengthenPanel = null;
         super.clear();
      }
      
      public function init(param1:StrengthenPanel) : void
      {
         m_strengthenPanel = param1;
         if(m_show)
         {
            throw new Error("不能重复初始化");
         }
         m_show = MyFunction2.returnShowByClassName("ChoiceDevourPanel") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO, param2:AutomaticPetsData) : void
      {
         var _loc4_:int = 0;
         m_automaticPetVO = param1;
         ClearUtil.nullArr(m_automaticPetVOs,false,false,false);
         m_automaticPetVOs = new Vector.<AutomaticPetVO>();
         var _loc3_:int = int(param2.getAutomaticPetVONum());
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param2.getAutomaticPetVOByIndex(_loc4_) != m_automaticPetVO && param2.getAutomaticPetVOByIndex(_loc4_).getPlayerVO() == null)
            {
               m_automaticPetVOs.push(param2.getAutomaticPetVOByIndex(_loc4_));
            }
            _loc4_++;
         }
         initShow2();
      }
      
      private function initShow() : void
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:DisplayObject = null;
         var _loc1_:ChoiceDevourCheckBox = null;
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_buyXiaoxia.setShow(m_show["btnBuyXiaoxia"]);
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         var _loc2_:uint = uint(m_show.numChildren);
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = m_show.getChildAt(_loc5_);
            if(_loc4_.name.substr(0,10) == "chooseBtn_")
            {
               _loc3_++;
            }
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = new ChoiceDevourCheckBox();
            _loc1_.setShow(m_show["chooseBtn_" + (_loc5_ + 1)]);
            m_chooseDevourCheckBoxs.push(_loc1_);
            _loc5_++;
         }
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO == null)
         {
            return;
         }
         setPageBtn(1);
         arrangePetChooseShow((m_pageBtnGroup.pageNum - 1) * m_chooseDevourCheckBoxs.length);
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_automaticPetVO == null || m_automaticPetVOs.length == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_automaticPetVOs.length % m_chooseDevourCheckBoxs.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_automaticPetVOs.length / m_chooseDevourCheckBoxs.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_automaticPetVOs.length / m_chooseDevourCheckBoxs.length) + 1);
         }
      }
      
      private function arrangePetChooseShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc2_:AutomaticPetVO = null;
         var _loc5_:int = param1 + m_chooseDevourCheckBoxs.length;
         var _loc3_:int = !!m_automaticPetVOs ? m_automaticPetVOs.length : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc3_)
         {
            _loc2_ = m_automaticPetVOs[_loc6_];
            m_chooseDevourCheckBoxs[_loc4_].getShow().visible = true;
            m_chooseDevourCheckBoxs[_loc4_].setData(_loc2_);
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_chooseDevourCheckBoxs.length)
         {
            m_chooseDevourCheckBoxs[_loc4_].getShow().visible = false;
            _loc4_++;
         }
         showChoicedStateInSwitchBtn();
      }
      
      private function showChoicedStateInSwitchBtn() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc1_ = int(m_chooseDevourCheckBoxs.length);
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = int(m_choiceAutomaticPetVOs.length);
            m_chooseDevourCheckBoxs[_loc4_].isCheck = false;
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(m_chooseDevourCheckBoxs[_loc4_].getAutomaticPetVO() == m_choiceAutomaticPetVOs[_loc3_])
               {
                  m_chooseDevourCheckBoxs[_loc4_].isCheck = true;
               }
               _loc3_++;
            }
            _loc4_++;
         }
      }
      
      private function showPop() : void
      {
         var _loc1_:EquipmentVO = null;
         _loc1_ = XMLSingle.getEquipmentVOByID(12000004,XMLSingle.getInstance().equipmentXML);
         if(getChildByName("popUpBox"))
         {
            popUpBox.initBox(_loc1_,"shopWallPrice");
            popUpBox.setNumBtnGroupMaxNum(100,m_strengthenPanel.getPetPanel().showWarningBox,["该物品一次购买最大购买数量为100",0]);
         }
         else
         {
            popUpBox = new ShopWall_BuyPopUpBox(_loc1_,"shopWallPrice");
            popUpBox.setNumBtnGroupMaxNum(100,m_strengthenPanel.getPetPanel().showWarningBox,["该物品一次购买最大购买数量为100",0]);
            popUpBox.x = 350;
            popUpBox.y = 170;
            popUpBox.name = "popUpBox";
            addChild(popUpBox);
         }
         if(!GamingUI.getInstance().player2)
         {
            popUpBox.state = 1;
         }
         else
         {
            popUpBox.state = 2;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         switch(param1.button)
         {
            case m_pageBtnGroup:
               arrangePetChooseShow((m_pageBtnGroup.pageNum - 1) * m_chooseDevourCheckBoxs.length);
               break;
            case m_quitBtn:
            case m_cancelBtn:
            case m_sureBtn:
               if(param1.button == m_sureBtn)
               {
                  m_strengthenPanel.sureChoiceDevourAutomaticPets(m_choiceAutomaticPetVOs);
               }
               m_strengthenPanel.closeChoiceDevourPanel();
               break;
            case m_buyXiaoxia:
               showPop();
         }
         var _loc3_:int = !!m_chooseDevourCheckBoxs ? m_chooseDevourCheckBoxs.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1.button == m_chooseDevourCheckBoxs[_loc4_])
            {
               if(m_chooseDevourCheckBoxs[_loc4_].isCheck)
               {
                  _loc2_ = int(m_choiceAutomaticPetVOs.indexOf(m_chooseDevourCheckBoxs[_loc4_].getAutomaticPetVO()));
                  if(_loc2_ == -1)
                  {
                     m_choiceAutomaticPetVOs.push(m_chooseDevourCheckBoxs[_loc4_].getAutomaticPetVO());
                  }
               }
               else
               {
                  _loc2_ = int(m_choiceAutomaticPetVOs.indexOf(m_chooseDevourCheckBoxs[_loc4_].getAutomaticPetVO()));
                  while(_loc2_ != -1)
                  {
                     m_choiceAutomaticPetVOs.splice(_loc2_,1);
                     _loc2_ = int(m_choiceAutomaticPetVOs.indexOf(m_chooseDevourCheckBoxs[_loc4_].getAutomaticPetVO()));
                  }
               }
            }
            _loc4_++;
         }
      }
   }
}

