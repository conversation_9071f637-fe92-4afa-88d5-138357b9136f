package UI.Task.TaskVO
{
   import UI.MyFunction2;
   import UI.Task.TaskReward.TaskRewardVO;
   import UI.newTask.NewMainTask.NewGotoData;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.Utils.ClearUtil;
   
   public class MTaskVO
   {
      public static const EVERY_DAY_RESET:String = "everyDayReset";
      
      public static const ONCE:String = "once";
      
      public static const DIE_REVIVE:String = "DieRevive";
      
      public static const LIMITING_TIME_TASK:String = "limitingTimeTask";
      
      public static const EVERY_DAY_TASK:String = "everyDayTask";
      
      public static const ACCUMULATED_TASK:String = "accumulatedTask";
      
      public static const LIMITING_TIME_ACCUMULATED_TASK:String = "limitingTimeAccumulatedTask";
      
      public static const UNFINISH:int = 0;
      
      public static const FINISH:int = 1;
      
      public static const DIE:int = 2;
      
      public static const DORMANCY:int = 3;
      
      public static const PART_FINISH:int = 4;
      
      public var name:String;
      
      public var description:String;
      
      public var taskGoalVO_ids:Vector.<int> = new Vector.<int>();
      
      public var taskGoalVO_nums:Vector.<int> = new Vector.<int>();
      
      public var currentTaskGoalVO_nums:Vector.<int> = new Vector.<int>();
      
      public var taskRewardVOs:Vector.<TaskRewardVO> = new Vector.<TaskRewardVO>();
      
      public var resetType:String;
      
      public var type:String;
      
      public var isNew:Boolean;
      
      public var isGotReward:int = 0;
      
      public var locationID:int;
      
      public var isGoto:int = 0;
      
      public var gotoInfo:NewGotoData;
      
      private var _state:int;
      
      private var _id:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function MTaskVO()
      {
         super();
         init();
      }
      
      protected function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.state = _state;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get state() : int
      {
         return _antiwear.state;
      }
      
      public function set state(param1:int) : void
      {
         _antiwear.state = param1;
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         _antiwear = null;
         _binaryEn = null;
         var _loc2_:int = 0;
         if(taskGoalVO_ids)
         {
            _loc1_ = int(taskGoalVO_ids.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               taskGoalVO_ids[_loc2_] = null;
               _loc2_++;
            }
            taskGoalVO_ids = null;
         }
         if(taskGoalVO_nums)
         {
            _loc1_ = int(taskGoalVO_nums.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               taskGoalVO_nums[_loc2_] = null;
               _loc2_++;
            }
            taskGoalVO_nums = null;
         }
         _loc1_ = int(taskRewardVOs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            taskRewardVOs[_loc2_].clear();
            taskRewardVOs[_loc2_] = null;
            _loc2_++;
         }
         taskRewardVOs = null;
         ClearUtil.clearObject(gotoInfo);
         gotoInfo = null;
      }
   }
}

