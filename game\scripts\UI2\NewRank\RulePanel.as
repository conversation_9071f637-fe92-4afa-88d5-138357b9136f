package UI2.NewRank
{
   import UI.GamingUI;
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class RulePanel extends MySprite
   {
      private var m_myloader:YJFYLoader;
      
      private var m_show:Sprite;
      
      public function RulePanel()
      {
         super();
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         init();
      }
      
      override public function clear() : void
      {
      }
      
      public function init() : void
      {
         m_myloader.getClass("rankworld.swf","ruleyj",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         this.addChild(m_show);
         this.initShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function initShow() : void
      {
         this.addEventListener("click",callback);
      }
      
      private function callback(param1:MouseEvent) : void
      {
         if(this.parent)
         {
            this.removeEventListener("click",callback);
            this.parent.removeChild(this);
         }
      }
   }
}

