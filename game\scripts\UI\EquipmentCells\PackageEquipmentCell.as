package UI.EquipmentCells
{
   import UI.Button.ChoiceBtns.EquipmentDestinationBtnListSingle;
   import UI.CellBorderSingle;
   import UI.EquipmentCell;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction2;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class PackageEquipmentCell extends EquipmentCell
   {
      public static var packageBtnSate:int = 0;
      
      public var equipbackground:IEquipmentCellBackground;
      
      private var _equipmentDestinationBtnList:EquipmentDestinationBtnListSingle;
      
      public function PackageEquipmentCell()
      {
         super();
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
         if(_equipmentDestinationBtnList)
         {
            _equipmentDestinationBtnList.clear();
         }
         _equipmentDestinationBtnList = null;
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
         if(getChildByName("equipmentDestinationBtnList"))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
         super.rollOut(param1);
      }
      
      override public function get equipmentCellBackground() : IEquipmentCellBackground
      {
         return equipbackground;
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.parent.visible == true)
         {
            CellBorderSingle.getInstance().x = 0.75;
            CellBorderSingle.getInstance().y = -0.87;
            CellBorderSingle.getInstance().width = 48;
            CellBorderSingle.getInstance().height = 48;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return equipbackground.getRect(param1);
      }
      
      override protected function click(param1:MouseEvent) : void
      {
         super.click(param1);
         if(_intervalTime < 500)
         {
            if(isHaveChild)
            {
               if(!getChildByName("equipmentDestinationBtnList"))
               {
                  EquipmentDestinationBtnListSingle.getInstance().name = "equipmentDestinationBtnList";
                  EquipmentDestinationBtnListSingle.getInstance().x = mouseX;
                  EquipmentDestinationBtnListSingle.getInstance().y = mouseY;
                  EquipmentDestinationBtnListSingle.getInstance().switchToPackageState(MyFunction2.brushBtn(packageBtnSate,child));
                  addChild(EquipmentDestinationBtnListSingle.getInstance());
               }
            }
         }
      }
      
      override protected function mouseDown(param1:MouseEvent) : void
      {
         super.mouseDown(param1);
      }
      
      protected function hideList(param1:UIBtnEvent) : void
      {
         if(getChildByName("equipmentDestinationBtnList"))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickEquipBtn",hideList,true,0,true);
         addEventListener("clickSellBtn",hideList,true,0,true);
         addEventListener("clickSaveEquipmentBtn",hideList,true,0,true);
         addEventListener("clickPutInBtn",hideList,true,0,true);
         addEventListener("clickSendBtn",hideList,true,0,true);
         addEventListener("clickFastUseBtn",hideList,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickEquipBtn",hideList,true);
         removeEventListener("clickSellBtn",hideList,true);
         removeEventListener("clickSaveEquipmentBtn",hideList,true);
         removeEventListener("clickPutInBtn",hideList,true);
         removeEventListener("clickSendBtn",hideList,true);
         removeEventListener("clickFastUseBtn",hideList,true);
      }
   }
}

