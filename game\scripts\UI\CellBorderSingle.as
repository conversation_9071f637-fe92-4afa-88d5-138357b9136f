package UI
{
   public class CellBorderSingle extends CellBorder
   {
      private static var instance:CellBorderSingle = null;
      
      public function CellBorderSingle()
      {
         if(instance == null)
         {
            super();
            instance = this;
            return;
         }
         throw new Error("fuck you!没看见实例已经存在么？！");
      }
      
      public static function getInstance() : CellBorderSingle
      {
         if(instance == null)
         {
            instance = new CellBorderSingle();
         }
         return instance;
      }
      
      override public function clear() : void
      {
         instance = null;
      }
   }
}

