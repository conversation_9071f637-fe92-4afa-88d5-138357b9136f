package UI.SignPanel
{
   import UI.Button.QuitBtn;
   import UI.EquipmentCellBackground;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Number.NumberContainer;
   import UI.SignPanel.Bar.SignProgressBarGroup;
   import UI.SignPanel.Btn.GetSignAwardBtn;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.setTimeout;
   
   public class SignPanel extends MySprite
   {
      private const _ONE_LINE_NUM:int = 8;
      
      public var getSignAwardBtn:GetSignAwardBtn;
      
      public var nowAwardPanel:Sprite;
      
      public var nextAwardPanel:Sprite;
      
      public var quitBtn:QuitBtn;
      
      private var _currentAwards:Vector.<EquipmentVO>;
      
      private var _nextAwards:Vector.<EquipmentVO>;
      
      private var _signProgressBarGroup:SignProgressBarGroup;
      
      private var _newDate:String;
      
      private var _signProgressContainer:NumberContainer;
      
      public function SignPanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         ClearUtil.clearDisplayObjectInContainer(nowAwardPanel);
         ClearUtil.clearDisplayObjectInContainer(nextAwardPanel);
         if(_currentAwards)
         {
            _loc1_ = int(_currentAwards.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_currentAwards[_loc3_])
               {
                  _currentAwards[_loc3_].clear();
               }
               _currentAwards[_loc3_] = null;
               _loc3_++;
            }
            _currentAwards = null;
         }
         if(_nextAwards)
         {
            _loc1_ = int(_nextAwards.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_nextAwards[_loc3_])
               {
                  _nextAwards[_loc3_].clear();
               }
               _nextAwards[_loc3_] = null;
               _loc3_++;
            }
            _nextAwards = null;
         }
         if(getSignAwardBtn)
         {
            getSignAwardBtn.clear();
         }
         getSignAwardBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_signProgressBarGroup)
         {
            _signProgressBarGroup.clear();
         }
         _signProgressBarGroup = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickGetSignAwardBtn",getAwards,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickGetSignAwardBtn",getAwards,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
      }
      
      private function getAwards(param1:UIBtnEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("addGifts",{
            "successFunction":getedAward,
            "successFunParams":[_newDate],
            "failFunction":showWarningBox,
            "failFunParams":["背包空间不足，放不下礼包！",0],
            "giftBags":_currentAwards
         }));
      }
      
      private function getedAward(param1:String) : void
      {
         SignData.getInstance().getSignAwardDate = MyFunction.getInstance().splitTimeString(param1);
         if(getSignAwardBtn)
         {
            getSignAwardBtn.mouseChildren = false;
            getSignAwardBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(getSignAwardBtn,-100);
            showWarningBox("奖励领取成功！",0);
         }
         var _loc2_:SaveTaskInfo = new SaveTaskInfo();
         _loc2_.type = "4399";
         _loc2_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc2_);
         MyFunction2.saveGame2();
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function arrangeAwards(param1:Vector.<EquipmentVO>, param2:Number, param3:Number, param4:Sprite) : void
      {
         var _loc8_:int = 0;
         var _loc7_:Equipment = null;
         var _loc5_:EquipmentCellBackground = null;
         var _loc6_:int = int(param1.length);
         ClearUtil.clearDisplayObjectInContainer(param4);
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            _loc5_ = new EquipmentCellBackground();
            _loc5_.x = param2 + 50 * (_loc8_ % 8);
            _loc5_.y = param3 + 50 * (int(_loc8_ / 8));
            param4.addChild(_loc5_);
            _loc7_ = MyFunction2.sheatheEquipmentShell(param1[_loc8_]);
            _loc7_.x = _loc5_.x;
            _loc7_.y = _loc5_.y;
            _loc7_.addEventListener("rollOver",equipmentInfor,false,0,true);
            _loc7_.addEventListener("rollOut",equipmentInfor,false,0,true);
            param4.addChild(_loc7_);
            _loc8_++;
         }
      }
      
      public function init() : void
      {
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _signProgressContainer = new NumberContainer();
         _signProgressContainer.x = 252;
         _signProgressContainer.y = 120;
         addChild(_signProgressContainer);
         getSignAwardBtn.mouseChildren = false;
         getSignAwardBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(getSignAwardBtn,-100);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var signXML:XML;
            var signDateToZero:String;
            var dTime:Number;
            var buQianBox:BuQianBox;
            var currentSignNum:int;
            var timeStr:String = param1;
            if(quitBtn)
            {
               signXML = XMLSingle.getInstance().signXML;
               _signProgressBarGroup = new SignProgressBarGroup();
               _signProgressBarGroup.init(0,0,750,SignData.getInstance().maxSignNum);
               _signProgressBarGroup.setProgress(0);
               _signProgressBarGroup.x = 85;
               _signProgressBarGroup.y = 250;
               addChild(_signProgressBarGroup);
               _newDate = timeStr;
               signDateToZero = !!SignData.getInstance().currentSignDate ? MyFunction.getInstance().transformTimeToZero(SignData.getInstance().currentSignDate) : "";
               dTime = !!signDateToZero ? new TimeUtil().timeInterval(signDateToZero,_newDate) : 24;
               if(dTime >= 24)
               {
                  if(dTime > 48)
                  {
                     buQianBox = new BuQianBox(signXML,Math.ceil((dTime - 48) / 24),function():void
                     {
                        SignData.getInstance().currentSignDate = _newDate;
                        _signProgressBarGroup.setProgress(SignData.getInstance().currentSignNum);
                        _signProgressContainer.initNumber(SignData.getInstance().currentSignNum,"SignNum",30);
                        GamingUI.getInstance().addMainLineTaskGoalGameEventStr("sign");
                        init2(signXML,_newDate);
                     },null);
                     buQianBox.x = (stage.stageWidth - buQianBox.width) / 2;
                     buQianBox.y = (stage.stageHeight - buQianBox.height) / 2;
                     addChild(buQianBox);
                     return;
                  }
                  currentSignNum = SignData.getInstance().currentSignNum;
                  SignData.getInstance().currentSignNum = currentSignNum + 1 > SignData.getInstance().maxSignNum ? 1 : ++currentSignNum;
                  SignData.getInstance().currentSignDate = _newDate;
                  _signProgressBarGroup.setProgress(SignData.getInstance().currentSignNum - 1);
                  _signProgressContainer.initNumber(SignData.getInstance().currentSignNum - 1,"SignNum",30);
                  setTimeout(function():void
                  {
                     if(_signProgressBarGroup)
                     {
                        _signProgressBarGroup.setProgress(SignData.getInstance().currentSignNum);
                        _signProgressContainer.initNumber(SignData.getInstance().currentSignNum,"SignNum",30);
                        GamingUI.getInstance().addMainLineTaskGoalGameEventStr("sign");
                        showWarningBox("已签到！",0);
                     }
                  },500);
               }
               else
               {
                  _signProgressBarGroup.setProgress(SignData.getInstance().currentSignNum);
                  _signProgressContainer.initNumber(SignData.getInstance().currentSignNum,"SignNum",30);
               }
               init2(signXML,_newDate);
            }
         },showWarningBox,true);
      }
      
      private function init2(param1:XML, param2:String) : void
      {
         if(MyFunction.getInstance().splitTimeString(param2) != SignData.getInstance().getSignAwardDate && Boolean(getSignAwardBtn))
         {
            getSignAwardBtn.mouseChildren = true;
            getSignAwardBtn.mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(getSignAwardBtn,0);
         }
         var _loc4_:XML = param1.Day.(@num == SignData.getInstance().currentSignNum)[0];
         var _loc5_:XML = param1.Day.(@num == (SignData.getInstance().currentSignNum + 1 > SignData.getInstance().maxSignNum ? 1 : SignData.getInstance().currentSignNum + 1))[0];
         var _loc3_:XML = XMLSingle.getInstance().equipmentXML;
         if(_currentAwards)
         {
            ClearUtil.nullArr(_currentAwards);
         }
         if(_nextAwards)
         {
            ClearUtil.nullArr(_nextAwards);
         }
         _currentAwards = XMLSingle.getEquipmentVOs(_loc4_,_loc3_,true);
         _nextAwards = XMLSingle.getEquipmentVOs(_loc5_,_loc3_,true);
         arrangeAwards(_currentAwards,12,50,nowAwardPanel);
         arrangeAwards(_nextAwards,12,50,nextAwardPanel);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

