package UI.WorldBoss.FlashView
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class FlashViewFactory
   {
      private var _flashViews:Array;
      
      public function FlashViewFactory()
      {
         super();
         _flashViews = [FlashView,FlashView1];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_flashViews);
         _flashViews = null;
      }
      
      public function createFlashView(param1:String) : FlashView
      {
         var _loc3_:* = null;
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
      
      public function createFlashViewByXML(param1:XML) : FlashView
      {
         var _loc6_:int = 0;
         var _loc5_:String = String(param1.@className);
         var _loc4_:FlashView = createFlashView(_loc5_);
         var _loc2_:XMLList = param1.att;
         var _loc3_:int = int(!!_loc2_ ? _loc2_.length() : 0);
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc4_[String(_loc2_[_loc6_].@attName)](_loc2_[_loc6_].@attValue);
            _loc6_++;
         }
         return _loc4_;
      }
   }
}

