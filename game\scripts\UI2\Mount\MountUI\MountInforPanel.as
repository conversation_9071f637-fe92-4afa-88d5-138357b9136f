package UI2.Mount.MountUI
{
   import UI.AutomaticPetPanel.SkillShow;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI2.Mount.MountData.MountSkillVO.IMountPassiveSkillVO;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVO;
   import UI2.Mount.MountData.MountSystemData.MountSystemData;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import UI2.Mount.MountLogic.AddStrengthenNumLogic;
   import UI2.Mount.MountLogic.AddStrengthenNumReturnData;
   import UI2.TicketBuyLogic;
   import YJFY.EntityShowContainer;
   import YJFY.Loader.IProgressShow;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class MountInforPanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_showCotainer:Sprite;
      
      private var m_xingShow:XingShow;
      
      private var m_mountNameText:TextField;
      
      private var m_powerChipBar:CMSXChangeBarLogicShell;
      
      private var m_strengthenNumText:TextField;
      
      private var m_totalTicketText:TextField;
      
      private var m_mountIconShow:MovieClipPlayLogicShell;
      
      private var m_skillContainer:Sprite;
      
      private var m_skillShow:SkillShow;
      
      private var m_chuaZhanAndRestBtnShowMC:MovieClipPlayLogicShell;
      
      private var m_choosePetBtns:MovieClipPlayLogicShell;
      
      private var m_choosePetBtn:ButtonLogicShell2;
      
      private var m_choosePet1Btn:ButtonLogicShell2;
      
      private var m_choosePet2Btn:ButtonLogicShell2;
      
      private var m_resetPetBtn:ButtonLogicShell2;
      
      private var m_strengthenLevelText:TextField;
      
      private var m_addAttriShow:MovieClipPlayLogicShell;
      
      private var m_addAttackText:TextField;
      
      private var m_addDefenceText:TextField;
      
      private var m_addHpText:TextField;
      
      private var m_nextAddAttackText:TextField;
      
      private var m_nextAddDefenceText:TextField;
      
      private var m_nextAddHpText:TextField;
      
      private var m_strengthenProText:TextField;
      
      private var m_normalStrengthenText:TextField;
      
      private var m_superStrengthenText:TextField;
      
      private var m_normalStrengthenBtn:ButtonLogicShell2;
      
      private var m_tenStrengthenBtn:ButtonLogicShell2;
      
      private var m_tenHighStrengthenBtn:ButtonLogicShell2;
      
      private var m_superStrengthenBtn:ButtonLogicShell2;
      
      private var m_energyBallShow:EnergyBallShow;
      
      private var m_energyBallListener:EnergyBallShowListener;
      
      private var m_mountShowContainer:EntityShowContainer;
      
      private var m_addStrengthenNumLogic:AddStrengthenNumLogic;
      
      private var m_ticketBuyLogic:TicketBuyLogic;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_closeSubUI:CloseSubUI;
      
      private var m_mountVO:MountVO;
      
      private var m_nextLevel2MountVO:MountVO;
      
      private var m_mountSystemData:MountSystemData;
      
      private var m_mountsVO:MountsVO;
      
      private var m_mountsPanel:MountsPanel;
      
      private var m_versionControl:IVersionControl;
      
      private var m_progressShow:IProgressShow;
      
      public function MountInforPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_xingShow = new XingShow();
         m_powerChipBar = new CMSXChangeBarLogicShell();
         m_normalStrengthenBtn = new ButtonLogicShell2();
         m_tenStrengthenBtn = new ButtonLogicShell2();
         m_tenHighStrengthenBtn = new ButtonLogicShell2();
         m_superStrengthenBtn = new ButtonLogicShell2();
         m_chuaZhanAndRestBtnShowMC = new MovieClipPlayLogicShell();
         m_addAttriShow = new MovieClipPlayLogicShell();
         m_mountShowContainer = new EntityShowContainer();
         m_mountShowContainer.init();
         m_energyBallShow = new EnergyBallShow();
         m_energyBallListener = new EnergyBallShowListener();
         m_energyBallListener.playEndFun = energyBallShowPlayEnd;
         m_energyBallShow.setEnergyBallShowListener(m_energyBallListener);
         m_addStrengthenNumLogic = new AddStrengthenNumLogic();
         m_ticketBuyLogic = new TicketBuyLogic();
         m_skillShow = new SkillShow();
         m_skillShow.addEventListener("rollOver",onOver,false,0,true);
         m_skillShow.addEventListener("rollOut",onOut,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         m_font = new FangZhengKaTongJianTi();
         m_mountIconShow = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         if(m_skillShow)
         {
            m_skillShow.removeEventListener("rollOver",onOver,false);
            m_skillShow.removeEventListener("rollOut",onOut,false);
         }
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_showCotainer);
         m_showCotainer = null;
         ClearUtil.clearObject(m_xingShow);
         m_xingShow = null;
         m_mountNameText = null;
         ClearUtil.clearObject(m_powerChipBar);
         m_powerChipBar = null;
         m_strengthenNumText = null;
         m_totalTicketText = null;
         ClearUtil.clearObject(m_skillContainer);
         m_skillContainer = null;
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         ClearUtil.clearObject(m_chuaZhanAndRestBtnShowMC);
         m_chuaZhanAndRestBtnShowMC = null;
         ClearUtil.clearObject(m_choosePetBtns);
         m_choosePetBtns = null;
         ClearUtil.clearObject(m_choosePetBtn);
         m_choosePetBtn = null;
         ClearUtil.clearObject(m_choosePet1Btn);
         m_choosePet1Btn = null;
         ClearUtil.clearObject(m_choosePet2Btn);
         m_choosePet2Btn = null;
         ClearUtil.clearObject(m_resetPetBtn);
         m_resetPetBtn = null;
         m_strengthenLevelText = null;
         ClearUtil.clearObject(m_addAttriShow);
         m_addAttriShow = null;
         m_addAttackText = null;
         m_addDefenceText = null;
         m_addHpText = null;
         m_nextAddAttackText = null;
         m_nextAddDefenceText = null;
         m_nextAddHpText = null;
         m_strengthenProText = null;
         m_normalStrengthenText = null;
         m_superStrengthenText = null;
         ClearUtil.clearObject(m_normalStrengthenBtn);
         m_normalStrengthenBtn = null;
         ClearUtil.clearObject(m_tenStrengthenBtn);
         m_tenStrengthenBtn = null;
         ClearUtil.clearObject(m_tenHighStrengthenBtn);
         m_tenHighStrengthenBtn = null;
         ClearUtil.clearObject(m_superStrengthenBtn);
         m_superStrengthenBtn = null;
         ClearUtil.clearObject(m_energyBallShow);
         m_energyBallShow = null;
         ClearUtil.clearObject(m_energyBallListener);
         m_energyBallListener = null;
         ClearUtil.clearObject(m_mountShowContainer);
         m_mountShowContainer = null;
         ClearUtil.clearObject(m_addStrengthenNumLogic);
         m_addStrengthenNumLogic = null;
         ClearUtil.clearObject(m_ticketBuyLogic);
         m_ticketBuyLogic = null;
         ClearUtil.clearObject(m_nextLevel2MountVO);
         m_nextLevel2MountVO = null;
         m_font = null;
         ClearUtil.clearObject(m_mountIconShow);
         m_mountIconShow = null;
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_mountVO = null;
         m_mountSystemData = null;
         m_mountsVO = null;
         m_mountsPanel = null;
         m_versionControl = null;
         m_progressShow = null;
         super.clear();
      }
      
      public function init(param1:MountVO, param2:MountSystemData, param3:MountsVO, param4:CloseSubUI, param5:MountsPanel, param6:IVersionControl, param7:IProgressShow) : void
      {
         ClearUtil.clearObject(m_closeSubUI);
         m_closeSubUI = null;
         m_closeSubUI = param4;
         m_mountVO = param1;
         m_mountSystemData = param2;
         m_mountsVO = param3;
         m_mountsPanel = param5;
         m_versionControl = param6;
         m_progressShow = param7;
         m_skillShow.init(m_progressShow,m_versionControl);
         m_show = MyFunction2.returnShowByClassName("MountInforPanel") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function initShow() : void
      {
         var _loc1_:FangZhengKaTongJianTi = m_font;
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_showCotainer = m_show["showContainer"];
         m_xingShow.init(m_show["xingShow"],1);
         m_mountNameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_mountNameText);
         m_skillContainer = m_show["skillContainer"];
         m_chuaZhanAndRestBtnShowMC.setShow(m_show["chuZhanAndRestBtnShow"]);
         m_powerChipBar.setShow(m_show["powerChipBar"]);
         m_strengthenNumText = m_show["strengthenNumText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_strengthenNumText);
         m_totalTicketText = m_show["totalTicketText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_totalTicketText);
         m_strengthenLevelText = m_show["strengthenLevelText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_strengthenLevelText);
         m_addAttriShow.setShow(m_show["attriShow"]);
         m_strengthenProText = m_show["strengthenProText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_strengthenProText);
         m_normalStrengthenText = m_show["normalStrengthenText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_normalStrengthenText);
         m_superStrengthenText = m_show["superStrengthenText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_superStrengthenText);
         m_normalStrengthenBtn.setShow(m_show["normalStrengthenBtn"]);
         m_tenStrengthenBtn.setShow(m_show["tenStrengthenBtn"]);
         m_tenHighStrengthenBtn.setShow(m_show["tenHighStrengthenBtn"]);
         m_superStrengthenBtn.setShow(m_show["superStrengthenBtn"]);
         m_energyBallShow.setShow(m_show["energyBallShow"]);
         m_showCotainer.addChild(m_mountShowContainer.getShow());
         m_mountIconShow.setShow(m_show["mountIconShow"]);
      }
      
      private function initShow2() : void
      {
         m_xingShow.setXingTotalNum(m_mountVO.getMaxLevel1());
         m_xingShow.setXingShineNum(m_mountVO.getLevel1());
         m_mountNameText.text = m_mountVO.getName();
         if(m_mountVO.getPlayerVO())
         {
            initRestBtnShowFrame();
         }
         else
         {
            initChuZhanBtnShowFrame();
         }
         if(m_mountVO.getLevel1() == 0)
         {
            m_chuaZhanAndRestBtnShowMC.getShow().visible = false;
         }
         else
         {
            m_chuaZhanAndRestBtnShowMC.getShow().visible = true;
         }
         if(m_mountVO.getLevel1() < m_mountVO.getMaxLevel1())
         {
            m_powerChipBar.change(m_mountVO.getCurrentPowerChipNum() / m_mountVO.getNeedPowerChipNum());
            m_powerChipBar.setDataShow(m_mountVO.getCurrentPowerChipNum() + "/" + m_mountVO.getNeedPowerChipNum());
         }
         else
         {
            m_powerChipBar.change(1);
            m_powerChipBar.setDataShow("星级已满");
         }
         m_strengthenNumText.text = m_mountsVO.getCurrentStrengthenPointNum().toString();
         m_totalTicketText.text = CurrentTicketPointManager.getInstance().getCurrentTicketPoint().toString();
         updateStrengthShow();
         m_normalStrengthenText.text = m_mountSystemData.getNormalStrengthenNum().toString();
         m_superStrengthenText.text = m_mountSystemData.getSuperStrengthenTicket().toString();
         m_energyBallShow.setPercentOfEnergy(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum());
         m_mountShowContainer.refreshMountShow(m_mountVO);
         var _loc1_:IMountPassiveSkillVO = m_mountVO.getPassiveSkillVOByIndex(0);
         m_skillShow.resetShow(_loc1_.getIconSwfPath(),_loc1_.getIconClassName());
         m_skillShow.setExtra(_loc1_);
         m_skillContainer.addChild(m_skillShow);
         m_mountIconShow.gotoAndStop(m_mountVO.getId());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quitBtn:
               m_closeSubUI.close();
               break;
            case m_choosePetBtn:
            case m_choosePet1Btn:
               GamingUI.getInstance().player1.playerVO.mountVO = m_mountVO;
               initShow2();
               break;
            case m_choosePet2Btn:
               GamingUI.getInstance().player2.playerVO.mountVO = m_mountVO;
               initShow2();
               break;
            case m_resetPetBtn:
               m_mountVO.getPlayerVO().mountVO = null;
               initShow2();
               break;
            case m_normalStrengthenBtn:
               normalStrengthen();
               break;
            case m_tenStrengthenBtn:
               tenStrengthen();
               break;
            case m_tenHighStrengthenBtn:
               tenSuperStrengthen();
               break;
            case m_superStrengthenBtn:
               superStrengthen();
         }
      }
      
      private function clearAddAttriShowFrame() : void
      {
         m_addAttackText = null;
         m_addDefenceText = null;
         m_addHpText = null;
         m_nextAddAttackText = null;
         m_nextAddDefenceText = null;
         m_nextAddHpText = null;
      }
      
      private function initAddAttriShowOneFrame() : void
      {
         clearAddAttriShowFrame();
         m_addAttriShow.gotoAndStop("haveNext");
         m_addAttackText = m_addAttriShow.getShow()["addAttackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addAttackText);
         m_addDefenceText = m_addAttriShow.getShow()["addDefenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDefenceText);
         m_addHpText = m_addAttriShow.getShow()["addHpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHpText);
         m_nextAddAttackText = m_addAttriShow.getShow()["nextAddAttackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_nextAddAttackText);
         m_nextAddDefenceText = m_addAttriShow.getShow()["nextAddDefenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_nextAddDefenceText);
         m_nextAddHpText = m_addAttriShow.getShow()["nextAddHpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_nextAddHpText);
      }
      
      private function initAddAttriShowTwoFrame() : void
      {
         clearAddAttriShowFrame();
         m_addAttriShow.gotoAndStop("noNext");
         m_addAttackText = m_addAttriShow.getShow()["addAttackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addAttackText);
         m_addDefenceText = m_addAttriShow.getShow()["addDefenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDefenceText);
         m_addHpText = m_addAttriShow.getShow()["addHpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHpText);
      }
      
      private function clearChuZhanAndRestBtnShowFrame() : void
      {
         clearChoosePetBtnFrame();
         ClearUtil.clearObject(m_choosePetBtns);
         m_choosePetBtns = null;
         ClearUtil.clearObject(m_resetPetBtn);
         m_resetPetBtn = null;
      }
      
      private function initRestBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("rest");
         m_resetPetBtn = new ButtonLogicShell2();
         m_resetPetBtn.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["restBtn"]);
      }
      
      private function initChuZhanBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("chuZhanBtns");
         m_choosePetBtns = new MovieClipPlayLogicShell();
         m_choosePetBtns.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["chuZhanBtns"]);
         if(GamingUI.getInstance().player2)
         {
            initChoosePetBtnTwoFrame();
         }
         else
         {
            initChoosePetBtnOneFrame();
         }
      }
      
      private function clearChoosePetBtnFrame() : void
      {
         ClearUtil.clearObject(m_choosePetBtn);
         m_choosePetBtn = null;
         ClearUtil.clearObject(m_choosePet1Btn);
         m_choosePet1Btn = null;
         ClearUtil.clearObject(m_choosePet2Btn);
         m_choosePet2Btn = null;
      }
      
      private function initChoosePetBtnOneFrame() : void
      {
         clearChoosePetBtnFrame();
         m_choosePetBtns.gotoAndStop("1");
         m_choosePetBtn = new ButtonLogicShell2();
         m_choosePetBtn.setShow(m_choosePetBtns.getShow()["chuZhanBtn"]);
      }
      
      private function initChoosePetBtnTwoFrame() : void
      {
         clearChoosePetBtnFrame();
         m_choosePetBtns.gotoAndStop("2");
         m_choosePet1Btn = new ButtonLogicShell2();
         m_choosePet1Btn.setShow(m_choosePetBtns.getShow()["chuZhanBtn_1"]);
         m_choosePet2Btn = new ButtonLogicShell2();
         m_choosePet2Btn.setShow(m_choosePetBtns.getShow()["chuZhanBtn_2"]);
      }
      
      private function normalStrengthen() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化已满",0);
            return;
         }
         if(m_mountsVO.getCurrentStrengthenPointNum() < m_mountSystemData.getNormalStrengthenNum())
         {
            m_mountsPanel.showWarningBox("灵兽石不足",0);
            return;
         }
         m_mountsVO.decStrengthenPointNum(m_mountSystemData.getNormalStrengthenNum());
         var _loc1_:AddStrengthenNumReturnData = new AddStrengthenNumReturnData();
         m_addStrengthenNumLogic.addStrengthenNum(m_mountVO,m_mountSystemData.getStrengthenPro(),m_mountSystemData.getNormalAddStren(),m_mountSystemData.getNormalAddStren2(),_loc1_);
         m_strengthenNumText.text = m_mountsVO.getCurrentStrengthenPointNum().toString();
         if(_loc1_.addNum == m_mountSystemData.getNormalAddStren2())
         {
            m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc1_.addNum,false,_loc1_.isUpgrade);
         }
         else
         {
            m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc1_.addNum,true,_loc1_.isUpgrade);
         }
         ClearUtil.clearObject(_loc1_);
         _loc1_ = null;
      }
      
      private function tenStrengthen() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化已满",0);
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() + m_mountSystemData.getNormalAddStren() * 10 >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化过满",0);
            return;
         }
         if(m_mountsVO.getCurrentStrengthenPointNum() < m_mountSystemData.getNormalStrengthenNum() * 10)
         {
            m_mountsPanel.showWarningBox("灵兽石不足",0);
            return;
         }
         m_mountsVO.decStrengthenPointNum(m_mountSystemData.getNormalStrengthenNum() * 10);
         var _loc2_:AddStrengthenNumReturnData = new AddStrengthenNumReturnData();
         m_addStrengthenNumLogic.addStrengthenByNum(m_mountVO,m_mountSystemData.getStrengthenPro(),m_mountSystemData.getNormalAddStren(),m_mountSystemData.getNormalAddStren2(),_loc2_,10,false);
         m_strengthenNumText.text = m_mountsVO.getCurrentStrengthenPointNum().toString();
         m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc2_.addNum,true,_loc2_.isUpgrade);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
      
      private function superStrengthen() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化已满",0);
            return;
         }
         m_mountsPanel.showWarningBox("是否花费" + m_mountSystemData.getSuperStrengthenTicket() + "点券强化。",3,{
            "type":"superStrengthen_1",
            "okFunction":superStrengthen2
         });
      }
      
      private function superStrengthen2() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_ticketBuyLogic == null)
         {
            return;
         }
         m_ticketBuyLogic.buy(m_mountSystemData.getSuperStrengthenTicket(),m_mountSystemData.getSuperStrengthenTicketId(),"高级强化",function():void
         {
            superStrengthen3();
         },m_mountsPanel.showWarningBox);
      }
      
      private function superStrengthen3() : void
      {
         var _loc2_:AddStrengthenNumReturnData = new AddStrengthenNumReturnData();
         m_addStrengthenNumLogic.addStrengthenNum(m_mountVO,m_mountSystemData.getStrengthenPro(),m_mountSystemData.getSuperAddStren(),m_mountSystemData.getSuperAddStren2(),_loc2_);
         if(_loc2_.addNum == m_mountSystemData.getSuperAddStren2())
         {
            m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc2_.addNum,false,_loc2_.isUpgrade);
         }
         else
         {
            m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc2_.addNum,true,_loc2_.isUpgrade);
         }
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
      
      private function tenSuperStrengthen() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化已满",0);
            return;
         }
         if(m_mountVO.getLevel2() >= m_mountVO.getMaxLevel2() && m_mountVO.getCurrentStrengthenNum() + m_mountSystemData.getSuperAddStren() * 10 >= m_mountVO.getNeedStrengthenNum())
         {
            m_mountsPanel.showWarningBox("强化暴满",0);
            return;
         }
         m_mountsPanel.showWarningBox("是否花费" + m_mountSystemData.getSuperStrengthenTicketFor10() + "点券强化。",3,{
            "type":"superStrengthen_1",
            "okFunction":superStrengthen4
         });
      }
      
      private function superStrengthen4() : void
      {
         if(m_energyBallShow == null || m_energyBallShow.getIsPlay())
         {
            return;
         }
         if(m_ticketBuyLogic == null)
         {
            return;
         }
         m_ticketBuyLogic.buy(m_mountSystemData.getSuperStrengthenTicketFor10(),m_mountSystemData.getSuperStrengthenTicketFor10Id(),"高级强化",function():void
         {
            superStrengthen5();
         },m_mountsPanel.showWarningBox);
      }
      
      private function superStrengthen5() : void
      {
         var _loc3_:AddStrengthenNumReturnData = new AddStrengthenNumReturnData();
         m_addStrengthenNumLogic.addStrengthenByNum(m_mountVO,m_mountSystemData.getStrengthenPro(),m_mountSystemData.getSuperAddStren(),m_mountSystemData.getSuperAddStren2(),_loc3_,10,false);
         m_energyBallShow.upgrade(m_mountVO.getCurrentStrengthenNum() / m_mountVO.getNeedStrengthenNum(),_loc3_.addNum,true,_loc3_.isUpgrade);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
      }
      
      private function energyBallShowPlayEnd(param1:EnergyBallShow) : void
      {
         updateStrengthShow();
      }
      
      private function updateStrengthShow() : void
      {
         var _loc1_:* = 0;
         ClearUtil.clearObject(m_nextLevel2MountVO);
         m_nextLevel2MountVO = null;
         if(m_mountVO.getLevel2() < m_mountVO.getMaxLevel2())
         {
            m_nextLevel2MountVO = m_mountVO.clone();
            m_nextLevel2MountVO.setLevel2(m_nextLevel2MountVO.getLevel2() + 1);
         }
         if(m_nextLevel2MountVO)
         {
            initAddAttriShowOneFrame();
         }
         else
         {
            initAddAttriShowTwoFrame();
         }
         m_strengthenProText.text = m_mountVO.getCurrentStrengthenNum() + "/" + m_mountVO.getNeedStrengthenNum();
         m_strengthenLevelText.text = m_mountVO.getLevel2() + "";
         m_addAttackText.text = m_mountVO.getAttackAddToPlayer() + "";
         m_addDefenceText.text = m_mountVO.getDefenceAddToPlayer() + "";
         m_addHpText.text = m_mountVO.getHpAddToPlayer() + "";
         if(m_nextLevel2MountVO)
         {
            _loc1_ = uint(m_nextLevel2MountVO.getAttackAddToPlayer() - m_mountVO.getAttackAddToPlayer());
            m_nextAddAttackText.text = "+" + _loc1_;
            _loc1_ = uint(m_nextLevel2MountVO.getDefenceAddToPlayer() - m_mountVO.getDefenceAddToPlayer());
            m_nextAddDefenceText.text = "+" + _loc1_;
            _loc1_ = uint(m_nextLevel2MountVO.getHpAddToPlayer() - m_mountVO.getHpAddToPlayer());
            m_nextAddHpText.text = "+" + _loc1_;
         }
      }
      
      private function onOver(param1:Event) : void
      {
         var _loc2_:MountSkillVO = (param1.currentTarget as SkillShow).getExtra() as MountSkillVO;
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_loc2_}));
         }
      }
      
      private function onOut(param1:Event) : void
      {
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

