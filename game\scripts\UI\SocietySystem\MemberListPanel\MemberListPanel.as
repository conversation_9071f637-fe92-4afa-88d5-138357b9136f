package UI.SocietySystem.MemberListPanel
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class MemberListPanel
   {
      private var m_onePageColumeNum:int;
      
      private var m_ableDragLS:AbleDragSpriteLogicShell;
      
      private var m_clear:ClearHelper;
      
      private var m_memberColumes:Vector.<MemberColume>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_memberDataList:DOWN_TheSocietyMemberList;
      
      private var m_socitySystemXML:XML;
      
      public function MemberListPanel()
      {
         super();
         m_clear = new ClearHelper();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.nullArr(m_memberColumes);
         m_memberColumes = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_ableDragLS);
         m_ableDragLS = null;
         m_show = null;
         m_memberDataList = null;
         m_socitySystemXML = null;
      }
      
      public function setMemberDataList(param1:DOWN_TheSocietyMemberList) : void
      {
         m_memberDataList = param1;
         if(m_show)
         {
            initShow2();
         }
      }
      
      public function setShow(param1:MovieClip, param2:XML, param3:String = null, param4:Boolean = false) : void
      {
         m_socitySystemXML = param2;
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         initShow(param3,param4);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getOnePageColumeNum() : int
      {
         return m_onePageColumeNum;
      }
      
      private function initShow(param1:String, param2:Boolean) : void
      {
         var _loc6_:int = 0;
         var _loc5_:DisplayObject = null;
         var _loc4_:MemberColume = null;
         m_ableDragLS = new AbleDragSpriteLogicShell();
         m_ableDragLS.setShow(m_show);
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_pageBtnGroup.initPageNumber(1,1);
         if(m_show["quitBtn2"])
         {
            m_quitBtn = new ButtonLogicShell2();
            m_quitBtn.setShow(m_show["quitBtn2"]);
            m_quitBtn.setTipString("点击关闭");
         }
         var _loc3_:int = m_show.numChildren;
         m_onePageColumeNum = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc5_ = m_show.getChildAt(_loc6_);
            if(_loc5_.name.substr(0,6) == "colume")
            {
               m_onePageColumeNum++;
            }
            _loc6_++;
         }
         m_memberColumes = new Vector.<MemberColume>();
         _loc6_ = 0;
         while(_loc6_ < m_onePageColumeNum)
         {
            _loc4_ = new MemberColume();
            _loc4_.setShow(m_show["colume" + (_loc6_ + 1)]);
            if(param1)
            {
               _loc4_.setTipString(param1);
            }
            if(param2 == false)
            {
               (_loc4_.getShow() as MovieClip).buttonMode = false;
            }
            m_memberColumes.push(_loc4_);
            _loc4_.getShow().visible = false;
            _loc6_++;
         }
         if(m_memberDataList)
         {
            initShow2();
         }
      }
      
      private function initShow2() : void
      {
         if(m_memberDataList == null)
         {
            return;
         }
         setPageBtn(m_pageBtnGroup.pageNum,m_memberDataList.getMemberTotalNum());
         arrangeColume(0);
      }
      
      private function setPageBtn(param1:int, param2:int) : void
      {
         if(param2 == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(param2 % m_onePageColumeNum == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,param2 / m_onePageColumeNum);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(param2 / m_onePageColumeNum) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc9_:* = 0;
         var _loc6_:int = 0;
         var _loc3_:MemberColume = null;
         var _loc8_:MemberDataInMemberList = null;
         var _loc2_:TitleData = null;
         var _loc7_:int = param1 + m_onePageColumeNum;
         var _loc5_:int = m_memberDataList.getMemberNum();
         _loc9_ = param1;
         while(_loc9_ < _loc7_ && _loc9_ < _loc5_)
         {
            _loc3_ = m_memberColumes[_loc6_];
            _loc3_.getShow().visible = true;
            _loc8_ = m_memberDataList.getMemberByIndex(_loc9_);
            _loc2_ = new TitleData();
            getTitleName(_loc8_.getRankByPersonalTotalConValue(),_loc2_);
            _loc3_.setMemberDataInList(_loc8_,_loc2_.titleId,_loc2_.titleName);
            _loc6_++;
            _loc9_++;
         }
         while(_loc6_ < m_onePageColumeNum)
         {
            m_memberColumes[_loc6_].getShow().visible = false;
            _loc6_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_pageBtnGroup !== _loc2_)
         {
            return;
         }
         arrangeColume(0);
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      public function getPageBtnGroup() : PageBtnGroupLogicShell
      {
         return m_pageBtnGroup;
      }
      
      public function getColumeNum() : int
      {
         return m_memberColumes.length;
      }
      
      public function getColumeByIndex(param1:int) : MemberColume
      {
         return m_memberColumes[param1];
      }
      
      private function getTitleName(param1:int, param2:TitleData) : void
      {
         param1 -= 1;
         var _loc3_:XML = m_socitySystemXML.societyTitle[0].title.(@minRankByConValue <= param1 && @maxRankByConValue >= param1)[0];
         if(_loc3_)
         {
            param2.titleId = String(_loc3_.@id);
            param2.titleName = String(_loc3_.@name);
         }
      }
   }
}

