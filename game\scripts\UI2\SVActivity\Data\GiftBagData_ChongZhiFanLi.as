package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.IEquipmentVOsData;
   import YJFY.Utils.ClearUtil;
   
   public class GiftBagData_ChongZhiFanLi extends DataManagerParent implements IEquipmentVOsData
   {
      private var m_id:String;
      
      private var m_needTotalTicket:uint;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function GiftBagData_ChongZhiFanLi()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_id = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.id = String(param1.@id);
         this.needTotalTicket = uint(param1.@needTotalTicket);
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
      }
      
      public function getId() : String
      {
         return id;
      }
      
      public function getNeedTotalTicket() : uint
      {
         return needTotalTicket;
      }
      
      public function getEquipmentVONum() : uint
      {
         return m_equipmentVOs.length;
      }
      
      public function getEquipmentVOByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.id = m_id;
         _antiwear.needTotalTicket = m_needTotalTicket;
      }
      
      private function get id() : String
      {
         return _antiwear.id;
      }
      
      private function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      private function get needTotalTicket() : uint
      {
         return _antiwear.needTotalTicket;
      }
      
      private function set needTotalTicket(param1:uint) : void
      {
         _antiwear.needTotalTicket = param1;
      }
   }
}

