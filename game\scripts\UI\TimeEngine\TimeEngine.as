package UI.TimeEngine
{
   import UI.MyFunction2;
   import UI.TextTrace.traceText;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class TimeEngine
   {
      private static const SPACING_INTERVAL1:int = 1000;
      
      private var _timeEngineData:TimeEngineData;
      
      private var _intervalID1:uint;
      
      private var _startTime:Number;
      
      private var _prevTime:Number;
      
      public function TimeEngine()
      {
         super();
         init();
      }
      
      public function clear() : void
      {
         clearInterval(_intervalID1);
         _timeEngineData.clear();
         _timeEngineData = null;
      }
      
      private function init() : void
      {
         _timeEngineData = new TimeEngineData();
      }
      
      public function initFun() : void
      {
         _intervalID1 = setInterval(Process,1000);
         _startTime = new Date().getTime();
         _prevTime = new Date().getTime();
         var _loc2_:int = 0;
         var _loc1_:int = int(_timeEngineData.functionArr_InitArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_timeEngineData.functionArr_InitArr[_loc2_])
            {
               (_timeEngineData.functionArr_InitArr[_loc2_]["fun"] as Function).apply(null,_timeEngineData.functionArr_InitArr[_loc2_]["params"]);
            }
            _loc2_++;
         }
      }
      
      public function start() : void
      {
         _intervalID1 = setInterval(Process,1000);
         _startTime = new Date().getTime();
         _prevTime = new Date().getTime();
         var _loc2_:int = 0;
         var _loc1_:int = int(_timeEngineData.functionArr_StartArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_timeEngineData.functionArr_StartArr[_loc2_])
            {
               (_timeEngineData.functionArr_StartArr[_loc2_]["fun"] as Function).apply(null,_timeEngineData.functionArr_StartArr[_loc2_]["params"]);
            }
            _loc2_++;
         }
      }
      
      public function stop() : void
      {
         clearInterval(_intervalID1);
         var _loc2_:int = 0;
         var _loc1_:int = int(_timeEngineData.functionArr_EndArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_timeEngineData.functionArr_EndArr[_loc2_])
            {
               (_timeEngineData.functionArr_EndArr[_loc2_]["fun"] as Function).apply(null,_timeEngineData.functionArr_EndArr[_loc2_]["params"]);
            }
            _loc2_++;
         }
      }
      
      public function addInitFun(param1:String, param2:Object) : void
      {
         if(_timeEngineData.functionArr_Init[param1])
         {
            traceText("时间引擎中的函数键重复， 已被覆盖！");
            _timeEngineData.functionArr_InitArr.splice(_timeEngineData.functionArr_InitArr.indexOf(_timeEngineData.functionArr_Init[param1]),1);
            _timeEngineData.functionArr_Init[param1] = null;
         }
         _timeEngineData.functionArr_Init[param1] = param2;
         _timeEngineData.functionArr_InitArr.push(param2);
      }
      
      public function addStartFun(param1:String, param2:Object) : void
      {
         if(_timeEngineData.functionArr_Start[param1])
         {
            traceText("时间引擎中的函数键重复， 已被覆盖！");
            _timeEngineData.functionArr_StartArr.splice(_timeEngineData.functionArr_StartArr.indexOf(_timeEngineData.functionArr_Start[param1]),1);
            _timeEngineData.functionArr_Start[param1] = null;
         }
         _timeEngineData.functionArr_Start[param1] = param2;
         _timeEngineData.functionArr_StartArr.push(param2);
      }
      
      public function deleInitFun(param1:String) : void
      {
         _timeEngineData.functionArr_InitArr.splice(_timeEngineData.functionArr_InitArr.indexOf(_timeEngineData.functionArr_Init[param1]),1);
         _timeEngineData.functionArr_Init[param1] = null;
         traceText(param1 + "函数已被删除！");
      }
      
      public function deleStartFun(param1:String) : void
      {
         _timeEngineData.functionArr_StartArr.splice(_timeEngineData.functionArr_StartArr.indexOf(_timeEngineData.functionArr_Start[param1]),1);
         _timeEngineData.functionArr_Start[param1] = null;
         traceText(param1 + "函数已被删除！");
      }
      
      public function addProcessFun(param1:String, param2:Object) : void
      {
         if(_timeEngineData.functionArr_Process[param1])
         {
            traceText("时间引擎中的函数键重复， 已被覆盖！");
            _timeEngineData.functionArr_ProcessArr.splice(_timeEngineData.functionArr_ProcessArr.indexOf(_timeEngineData.functionArr_ProcessArr[param1]),1);
            _timeEngineData.functionArr_Process[param1] = null;
         }
         _timeEngineData.functionArr_Process[param1] = param2;
         _timeEngineData.functionArr_ProcessArr.push(param2);
      }
      
      public function deleProcessFun(param1:String) : void
      {
         _timeEngineData.functionArr_ProcessArr.splice(_timeEngineData.functionArr_ProcessArr.indexOf(_timeEngineData.functionArr_ProcessArr[param1]),1);
         _timeEngineData.functionArr_Process[param1] = null;
         traceText(param1 + "函数已被删除！");
      }
      
      public function addEndFun(param1:String, param2:Object) : void
      {
         if(_timeEngineData.functionArr_End[param1])
         {
            traceText("时间引擎中的函数键重复， 已被覆盖！");
            _timeEngineData.functionArr_EndArr.splice(_timeEngineData.functionArr_EndArr.indexOf(_timeEngineData.functionArr_EndArr[param1]),1);
            _timeEngineData.functionArr_End[param1] = null;
         }
         _timeEngineData.functionArr_End[param1] = param2;
         _timeEngineData.functionArr_EndArr.push(param2);
      }
      
      public function deleEndFun(param1:String) : void
      {
         _timeEngineData.functionArr_EndArr.splice(_timeEngineData.functionArr_EndArr.indexOf(_timeEngineData.functionArr_EndArr[param1]),1);
         _timeEngineData.functionArr_End[param1] = null;
         traceText(param1 + "函数已被删除！");
      }
      
      private function Process() : void
      {
         var _loc4_:Number = NaN;
         var _loc2_:Number = Number(new Date().getTime());
         var _loc1_:Number = _loc2_ - _startTime;
         _loc4_ = _loc2_ - _prevTime;
         if(_loc4_ >= 1000)
         {
            _prevTime = _loc2_;
            var _loc5_:int = 0;
            var _loc3_:int = int(_timeEngineData.functionArr_ProcessArr.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               if(!_timeEngineData)
               {
                  break;
               }
               if(_timeEngineData.functionArr_ProcessArr[_loc5_])
               {
                  if(MyFunction2.judgmentValueInNormal(_loc1_ % (!!_timeEngineData.functionArr_ProcessArr[_loc5_]["spacing"] ? _timeEngineData.functionArr_ProcessArr[_loc5_]["spacing"] * 1000 : 1000),0,1000))
                  {
                     (_timeEngineData.functionArr_ProcessArr[_loc5_]["fun"] as Function).apply(null,_timeEngineData.functionArr_ProcessArr[_loc5_]["params"]);
                  }
               }
               _loc5_++;
            }
            return;
         }
      }
   }
}

