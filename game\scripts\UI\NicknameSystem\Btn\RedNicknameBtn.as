package UI.NicknameSystem.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class RedNicknameBtn extends Btn
   {
      public static const CLICK_RED_NICKNAME_BTN:String = "clickRedNicknameBtn";
      
      public function RedNicknameBtn()
      {
         super();
         setTipString("点击设置个性化红名");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickRedNicknameBtn"));
      }
   }
}

