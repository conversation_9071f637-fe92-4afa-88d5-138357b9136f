package UI.InformationPanel
{
   import UI.Button.Pet.AutoPetBtn;
   import UI.Button.Pet.MountBtn;
   import UI.Button.Pet.PetBtn;
   import UI.Button.Pet.PlayerBtn;
   import UI.Event.UIBtnEvent;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class InformationPanel extends MySprite
   {
      public var playerBtn:PlayerBtn;
      
      public var petBtn:PetBtn;
      
      public var autoPetBtn:AutoPetBtn;
      
      public var mountBtn:MountBtn;
      
      private var _currentState:int = 0;
      
      public function InformationPanel()
      {
         super();
         playerBtn.init(false);
         petBtn.init(true);
         autoPetBtn.init(true);
         mountBtn.init(true);
         _currentState = 0;
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function get currentState() : int
      {
         return _currentState;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickPlayerBtn",switchToPlayer,true,0,true);
         addEventListener("clickPetBtn",switchToPet,true,0,true);
         addEventListener("clickTuDiBtn",switchToTuDi,true,0,true);
         addEventListener("clickAutoPetBtn",switchToAutoPet,true,0,true);
         addEventListener("clickMountBtn",switchToMount,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickPlayerBtn",switchToPlayer,true);
         removeEventListener("clickPetBtn",switchToPet,true);
         removeEventListener("clickTuDiBtn",switchToTuDi,true);
         removeEventListener("clickAutoPetBtn",switchToAutoPet,true);
         removeEventListener("clickMountBtn",switchToMount,true);
      }
      
      public function switchToPlayer(param1:UIBtnEvent) : void
      {
         petBtn.gotoTwoFrame();
         playerBtn.gotoOneFrame();
         autoPetBtn.gotoTwoFrame();
         mountBtn.gotoTwoFrame();
         _currentState = 0;
      }
      
      public function switchToPet(param1:UIBtnEvent) : void
      {
         playerBtn.gotoTwoFrame();
         petBtn.gotoOneFrame();
         autoPetBtn.gotoTwoFrame();
         mountBtn.gotoTwoFrame();
         _currentState = 1;
      }
      
      public function switchToTuDi(param1:UIBtnEvent) : void
      {
         playerBtn.gotoTwoFrame();
         petBtn.gotoTwoFrame();
         autoPetBtn.gotoTwoFrame();
         mountBtn.gotoTwoFrame();
         _currentState = 2;
      }
      
      public function switchToAutoPet(param1:UIBtnEvent) : void
      {
         autoPetBtn.gotoOneFrame();
         playerBtn.gotoTwoFrame();
         petBtn.gotoTwoFrame();
         mountBtn.gotoTwoFrame();
         _currentState = 3;
      }
      
      public function switchToMount(param1:UIBtnEvent) : void
      {
         mountBtn.gotoOneFrame();
         playerBtn.gotoTwoFrame();
         petBtn.gotoTwoFrame();
         autoPetBtn.gotoTwoFrame();
         _currentState = 4;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(playerBtn)
         {
            playerBtn.clear();
         }
         playerBtn = null;
         if(petBtn)
         {
            petBtn.clear();
         }
         petBtn = null;
         if(autoPetBtn)
         {
            autoPetBtn.clear();
         }
         autoPetBtn = null;
         ClearUtil.clearObject(mountBtn);
         mountBtn = null;
      }
   }
}

