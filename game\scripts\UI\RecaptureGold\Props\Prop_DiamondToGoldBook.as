package UI.RecaptureGold.Props
{
   import UI.RecaptureGold.Hook.Hook;
   import UI.RecaptureGold.Parent.Prop;
   import flash.utils.getQualifiedClassName;
   
   public class Prop_DiamondToGoldBook extends Prop implements IOneGameLevelProp
   {
      public function Prop_DiamondToGoldBook()
      {
         super();
      }
      
      public function dealWithRockFromHook(param1:XML, param2:Hook) : Number
      {
         var _loc3_:String = null;
         var _loc5_:XML = null;
         var _loc4_:Number = NaN;
         var _loc6_:String = null;
         if(param2.hookItem)
         {
            _loc3_ = getQualifiedClassName(this);
            _loc5_ = param1.PropData[0].item.(@classNameForShow == _loc3_)[0];
            _loc4_ = 1;
            _loc6_ = getQualifiedClassName(param2.hookItem);
            if(_loc6_.substr(0,7) == "Diamond")
            {
               _loc4_ = Number(_loc5_.@promoteValue);
            }
         }
         return _loc4_;
      }
   }
}

