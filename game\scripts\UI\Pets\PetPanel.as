package UI.Pets
{
   import UI.Button.Pet.Pet_InforBtn;
   import UI.Button.Pet.Pet_SkillBtn;
   import UI.Button.Pet.TakeBackPetBtn;
   import UI.ConsumptionGuide.Button.BuyEssentialPotionGuideBtn;
   import UI.ConsumptionGuide.Button.BuyPetExperiencePillGuideBtn;
   import UI.ConsumptionGuide.Button.BuyPetTrianPotionGuideBtn;
   import UI.ConsumptionGuide.Button.TalentUpGuideBtn;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MySprite;
   import UI.Pets.Bar.PetEssenceBar;
   import UI.Pets.Bar.PetExperienceBar;
   import UI.Pets.Talents.Talent;
   import UI.SkillCells.SkillCell;
   import UI.UIConstant.UIConstantData;
   import UI.UIInterface.OldInterface.ISkillCell;
   import YJFY.EntityShowContainer;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PetPanel extends MySprite
   {
      public static const PASSIVE_SKILL_CELL_MAX_NUM:int = 6;
      
      public static const PASSIVE_SKILL_CELL_NUM:int = 5;
      
      private static const htmlText:String = "<b><font size=\'30\' color=\'#ffffff\'>没有出战宠物</font><br><font size=\'20\' color=\'#ffffff\'><li>点击背包中的宠物选择出战</li><li>宠物会在战斗中随机出现</li><li>也可以通过宠物蛋孵化获得</li></font></b>";
      
      private static const TEXT_FIELD_X:Number = 55;
      
      private static const TEXT_FIELD_Y:Number = 200;
      
      public var petShowPanel:PetShowPanel;
      
      public var petNameText:TextField;
      
      public var inforBtn:Pet_InforBtn;
      
      public var skillBtn:Pet_SkillBtn;
      
      public var takeBackPetBtn:TakeBackPetBtn;
      
      public var isHavePet:Boolean;
      
      private const INFORMATION_STATE:int = 0;
      
      private const SKILL_SATE:int = 1;
      
      private const PASSIVE_SKILL_NUM:int = 5;
      
      private var _currentState:int = 0;
      
      private var _activeSkillText:TextField;
      
      private var _passiveSkillText:TextField;
      
      private var _awakeSkillText:TextField;
      
      private var _talentText:TextField;
      
      private var _talentLayer:TalentLayer;
      
      private var _petLevelText:TextField;
      
      private var _experienceText:TextField;
      
      private var _lifeText:TextField;
      
      private var _experienceBar:PetExperienceBar;
      
      private var _essenceBar:PetEssenceBar;
      
      private var _activeSkillCell:PetSkillCell;
      
      private var _awakeSkillCell:PetSkillCell;
      
      private var _passiveSkillCells:Vector.<ISkillCell>;
      
      private var _petShowContainer:EntityShowContainer;
      
      private var _talentUpGuideBtn:TalentUpGuideBtn;
      
      private var _buyEssentialPotionGuideBtn:BuyEssentialPotionGuideBtn;
      
      private var _buyPetExperiencePillGuideBtn:BuyPetExperiencePillGuideBtn;
      
      private var _buyPetTrianPotionGuideBtn:BuyPetTrianPotionGuideBtn;
      
      private var _pet:Pet;
      
      private var _textField:TextField;
      
      private var _isShowBtn:Boolean;
      
      public function PetPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function refresh(param1:Pet) : void
      {
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc8_:int = 0;
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         if(_isShowBtn)
         {
            _talentUpGuideBtn.visible = true;
            _buyEssentialPotionGuideBtn.visible = true;
            _buyPetExperiencePillGuideBtn.visible = true;
            _buyPetTrianPotionGuideBtn.visible = true;
         }
         _pet = param1;
         if(param1)
         {
            if(param1.petEquipmentVO)
            {
               showAll();
               hideText();
               setPanel(_currentState);
               petNameText.htmlText = "<font face=\'" + _loc2_.fontName + "\' color=\'#ffffff\'>" + param1.petEquipmentVO.name + "</font>";
               _petLevelText.htmlText = "<font face=\'" + _loc2_.fontName + "\'size=\'15\' color=\'#ffffff\'>" + "等级：" + param1.petEquipmentVO.petLevel.toString() + "</font>";
               _experienceBar.changeExpericenceBar(param1.petEquipmentVO.experiencePercent,param1.petEquipmentVO.experienceVolume);
               _essenceBar.changeLifeBar(param1.petEquipmentVO.essentialPercent,param1.petEquipmentVO.essentialVolume);
               _activeSkillCell.addSkillVO(param1.petEquipmentVO.activeSkillVO);
               if(param1.petEquipmentVO is AdvancePetEquipmentVO)
               {
                  _awakeSkillCell.visible = true;
                  _awakeSkillText.visible = true;
                  _awakeSkillCell.addSkillVO((param1.petEquipmentVO as AdvancePetEquipmentVO).petAwakePassiveSkillVOs[0]);
               }
               else
               {
                  _awakeSkillCell.visible = false;
                  _awakeSkillText.visible = false;
               }
               _talentText.htmlText = "<font face=\'" + _loc2_.fontName + "\'size=\'15\' color=\'#ffffff\'>" + "天赋：" + param1.petEquipmentVO.talentVO.name + "</font>";
               addChildToTalentLayer(new Talent(param1.petEquipmentVO.talentVO));
               addChildToTalentLayer(new Talent(param1.petEquipmentVO.talentVO));
               _petShowContainer.refreshPetShow(param1.petEquipmentVO);
               while(petShowPanel.numChildren > 1)
               {
                  petShowPanel.removeChildAt(1);
               }
               _petShowContainer.getShow().x = 180;
               _petShowContainer.getShow().y = 210;
               _petShowContainer.getShow().scaleX = 3;
               _petShowContainer.getShow().scaleY = 3;
               petShowPanel.addChild(_petShowContainer.getShow());
               if(param1.petEquipmentVO.essentialPercent <= 0)
               {
                  MyFunction.getInstance().changeSaturation(petShowPanel,-100);
               }
               else
               {
                  MyFunction.getInstance().changeSaturation(petShowPanel,0);
               }
               _loc4_ = int(_passiveSkillCells.length);
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _passiveSkillCells[_loc5_].removeSkillVO();
                  _loc5_++;
               }
               _loc6_ = int(param1.petEquipmentVO.passiveSkillVOs.length);
               _loc8_ = 0;
               while(_loc8_ < _loc6_ && _loc8_ < _loc4_)
               {
                  _passiveSkillCells[_loc8_].addSkillVO(param1.petEquipmentVO.passiveSkillVOs[_loc8_]);
                  _loc8_++;
               }
               isHavePet = true;
               if(param1.petEquipmentVO.talentVO.level == param1.petEquipmentVO.talentVO.maxLevel)
               {
                  _talentUpGuideBtn.visible = false;
               }
               if(param1.petEquipmentVO.essentialPercent == 1)
               {
                  _buyEssentialPotionGuideBtn.visible = false;
               }
               if(param1.petEquipmentVO.level == param1.petEquipmentVO.maxLevel && param1.petEquipmentVO.petLevel == param1.petEquipmentVO.upgradeLevelNum && param1.petEquipmentVO.experiencePercent == 1)
               {
                  _buyPetExperiencePillGuideBtn.visible = false;
               }
               _loc4_ = int(param1.petEquipmentVO.passiveSkillVOs.length);
               _loc3_ = 0;
               _loc7_ = 0;
               while(_loc7_ < _loc4_)
               {
                  if(param1.petEquipmentVO.passiveSkillVOs[_loc7_])
                  {
                     _loc3_++;
                  }
                  _loc7_++;
               }
               if(_loc3_ == 5 || UIConstantData.ZHULONG_ID_ARRAY.indexOf(param1.petEquipmentVO.id) != -1 || UIConstantData.QIONGQI_ID_ARRAY.indexOf(param1.petEquipmentVO.id) != -1)
               {
                  _buyPetTrianPotionGuideBtn.visible = false;
               }
               if(UIConstantData.ZHULONG_ID_ARRAY.indexOf(param1.petEquipmentVO.id) == -1 && UIConstantData.QIONGQI_ID_ARRAY.indexOf(param1.petEquipmentVO.id) == -1)
               {
                  _passiveSkillCells[5].visible = false;
               }
               setActive(param1.petEquipmentVO.essentialPercent);
               if(param1.petEquipmentVO is AdvancePetEquipmentVO)
               {
                  resetGuidBuyEquipmentId(11000020,11000021);
               }
               else
               {
                  resetGuidBuyEquipmentId(11000011,11000009);
               }
            }
            else
            {
               petNameText.htmlText = "";
               _petLevelText.htmlText = "";
               _talentText.htmlText = "";
               removeChildFromTalentLayer();
               hideAll();
               isHavePet = false;
               showText();
            }
         }
         else
         {
            petNameText.htmlText = "";
            _petLevelText.htmlText = "";
            _talentText.htmlText = "";
            removeChildFromTalentLayer();
            hideAll();
            isHavePet = false;
            showText();
         }
      }
      
      public function changePetVO(param1:PetEquipmentVO) : PetEquipmentVO
      {
         var _loc2_:PetEquipmentVO = null;
         if(_pet)
         {
            _loc2_ = _pet.petEquipmentVO;
            _pet.petEquipmentVO = param1;
            _pet.playerVO.player.changeData();
            return _loc2_;
         }
         trace("petPanel pet 为空");
         return null;
      }
      
      public function addChildToShowPanel(param1:DisplayObject) : void
      {
         while(petShowPanel.numChildren > 1)
         {
            petShowPanel.removeChildAt(1);
         }
         if(param1)
         {
            param1.x = 180;
            param1.y = 210;
            param1.scaleX = 3;
            param1.scaleY = 3;
            petShowPanel.addChild(param1);
         }
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(!(_loc2_ is Pet) && _loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         if(petShowPanel)
         {
            petShowPanel.clear();
         }
         petShowPanel = null;
         petNameText = null;
         if(inforBtn)
         {
            inforBtn.clear();
         }
         inforBtn = null;
         if(skillBtn)
         {
            skillBtn.clear();
         }
         skillBtn = null;
         if(takeBackPetBtn)
         {
            takeBackPetBtn.clear();
         }
         takeBackPetBtn = null;
         _activeSkillText = null;
         _passiveSkillText = null;
         _awakeSkillText = null;
         _talentText = null;
         if(_talentLayer)
         {
            _talentLayer.clear();
         }
         _talentLayer = null;
         _petLevelText = null;
         _experienceText = null;
         _lifeText = null;
         if(_experienceBar)
         {
            _experienceBar.clear();
         }
         _experienceBar = null;
         if(_essenceBar)
         {
            _essenceBar.clear();
         }
         _essenceBar = null;
         if(_activeSkillCell)
         {
            _activeSkillCell.clear();
         }
         _activeSkillCell = null;
         if(_awakeSkillCell)
         {
            _awakeSkillCell.clear();
         }
         _awakeSkillCell = null;
         _textField = null;
         if(_passiveSkillCells)
         {
            _loc1_ = int(_passiveSkillCells.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               (_passiveSkillCells[_loc3_] as SkillCell).clear();
               _passiveSkillCells[_loc3_] = null;
               _loc3_++;
            }
         }
         _passiveSkillCells = null;
         if(_talentUpGuideBtn)
         {
            _talentUpGuideBtn.clear();
         }
         _talentUpGuideBtn = null;
         if(_buyEssentialPotionGuideBtn)
         {
            _buyEssentialPotionGuideBtn.clear();
         }
         _buyEssentialPotionGuideBtn = null;
         if(_buyPetExperiencePillGuideBtn)
         {
            _buyPetExperiencePillGuideBtn.clear();
         }
         _buyPetExperiencePillGuideBtn = null;
         if(_buyPetTrianPotionGuideBtn)
         {
            _buyPetTrianPotionGuideBtn.clear();
         }
         _buyPetTrianPotionGuideBtn = null;
         _pet = null;
      }
      
      public function get pet() : Pet
      {
         return _pet;
      }
      
      public function set isShowPet(param1:Boolean) : void
      {
         if(_isShowBtn != param1)
         {
            _isShowBtn = param1;
            if(!param1)
            {
               if(getChildByName(takeBackPetBtn.name))
               {
                  removeChild(takeBackPetBtn);
               }
            }
            else if(!getChildByName(takeBackPetBtn.name))
            {
               addChild(takeBackPetBtn);
            }
            takeBackPetBtn.visible = param1;
            _buyPetTrianPotionGuideBtn.visible = param1;
            _buyEssentialPotionGuideBtn.visible = param1;
            _buyPetExperiencePillGuideBtn.visible = param1;
            if(!param1)
            {
               if(getChildByName(_talentUpGuideBtn.name))
               {
                  removeChild(_talentUpGuideBtn);
               }
            }
            else if(!getChildByName(_talentUpGuideBtn.name))
            {
               addChild(_talentUpGuideBtn);
            }
         }
      }
      
      public function setPanel(param1:int) : void
      {
         loop2:
         switch(param1)
         {
            case 0:
               if(getChildByName(_activeSkillText.name))
               {
                  removeChild(_activeSkillText);
               }
               if(getChildByName(_passiveSkillText.name))
               {
                  removeChild(_passiveSkillText);
               }
               if(getChildByName(_awakeSkillText.name))
               {
                  removeChild(_awakeSkillText);
               }
               if(getChildByName(_activeSkillCell.name))
               {
                  removeChild(_activeSkillCell);
               }
               if(getChildByName(_awakeSkillCell.name))
               {
                  removeChild(_awakeSkillCell);
               }
               if(getChildByName(_buyPetTrianPotionGuideBtn.name))
               {
                  removeChild(_buyPetTrianPotionGuideBtn);
               }
               for each(var _loc2_ in _passiveSkillCells)
               {
                  if(getChildByName(_loc2_.name))
                  {
                     removeChild(_loc2_);
                  }
               }
               addChild(_petLevelText);
               addChild(_experienceText);
               addChild(_lifeText);
               addChild(_experienceBar);
               addChild(_essenceBar);
               addChild(_buyEssentialPotionGuideBtn);
               addChild(_buyPetExperiencePillGuideBtn);
               break;
            case 1:
               if(getChildByName(_petLevelText.name))
               {
                  removeChild(_petLevelText);
               }
               if(getChildByName(_experienceText.name))
               {
                  removeChild(_experienceText);
               }
               if(getChildByName(_lifeText.name))
               {
                  removeChild(_lifeText);
               }
               if(getChildByName(_experienceBar.name))
               {
                  removeChild(_experienceBar);
               }
               if(getChildByName(_essenceBar.name))
               {
                  removeChild(_essenceBar);
               }
               if(getChildByName(_buyEssentialPotionGuideBtn.name))
               {
                  removeChild(_buyEssentialPotionGuideBtn);
               }
               if(getChildByName(_buyPetExperiencePillGuideBtn.name))
               {
                  removeChild(_buyPetExperiencePillGuideBtn);
               }
               addChild(_activeSkillText);
               addChild(_activeSkillCell);
               addChild(_awakeSkillCell);
               addChild(_passiveSkillText);
               addChild(_awakeSkillText);
               addChild(_buyPetTrianPotionGuideBtn);
               var _loc7_:int = 0;
               var _loc6_:Vector.<ISkillCell> = _passiveSkillCells;
               while(true)
               {
                  for each(var _loc3_ in _loc6_)
                  {
                     addChild(_loc3_);
                  }
                  break loop2;
               }
         }
      }
      
      private function init() : void
      {
         var _loc2_:int = 0;
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         _passiveSkillCells = new Vector.<ISkillCell>();
         _loc2_ = 0;
         while(_loc2_ < 6)
         {
            _passiveSkillCells.push(new PetSkillCell());
            (_passiveSkillCells[_loc2_] as PetSkillCell).scaleX = 0.7;
            (_passiveSkillCells[_loc2_] as PetSkillCell).scaleY = 0.7;
            _passiveSkillCells[_loc2_].x = 150 + _passiveSkillCells[_loc2_].width * _loc2_;
            _passiveSkillCells[_loc2_].y = 435;
            _loc2_++;
         }
         _buyPetTrianPotionGuideBtn = new BuyPetTrianPotionGuideBtn(11000009);
         _buyPetTrianPotionGuideBtn.x = (_passiveSkillCells[4] as SkillCell).x + (_passiveSkillCells[4] as SkillCell).width / 2 + 5;
         _buyPetTrianPotionGuideBtn.y = (_passiveSkillCells[4] as SkillCell).y - (_passiveSkillCells[4] as SkillCell).height / 2;
         _activeSkillCell = new PetSkillCell();
         _activeSkillCell.scaleX = 0.7;
         _activeSkillCell.scaleY = 0.7;
         _activeSkillCell.x = 150;
         _activeSkillCell.y = 395;
         _awakeSkillCell = new PetSkillCell();
         _awakeSkillCell.scaleX = 0.7;
         _awakeSkillCell.scaleY = 0.7;
         _awakeSkillCell.x = 318;
         _awakeSkillCell.y = 395;
         _experienceBar = new PetExperienceBar();
         _experienceBar.scaleY = 0.5;
         _experienceBar.x = 105;
         _experienceBar.y = 412;
         _buyPetExperiencePillGuideBtn = new BuyPetExperiencePillGuideBtn(11000006);
         _buyPetExperiencePillGuideBtn.x = _experienceBar.x + _experienceBar.width + 5;
         _buyPetExperiencePillGuideBtn.y = _experienceBar.y - _experienceBar.height / 2 - 2;
         _essenceBar = new PetEssenceBar();
         _essenceBar.scaleY = 0.5;
         _essenceBar.x = 105;
         _essenceBar.y = 432;
         _buyEssentialPotionGuideBtn = new BuyEssentialPotionGuideBtn(11000003);
         _buyEssentialPotionGuideBtn.x = _essenceBar.x + _essenceBar.width + 5;
         _buyEssentialPotionGuideBtn.y = _essenceBar.y - _essenceBar.height / 2 - 2;
         _talentLayer = new TalentLayer();
         _talentLayer.x = 70;
         _talentLayer.y = 270;
         addChild(_talentLayer);
         _talentUpGuideBtn = new TalentUpGuideBtn(11000011);
         _talentUpGuideBtn.x = _talentLayer.x + _talentLayer.width + 30;
         _talentUpGuideBtn.y = _talentLayer.y + _talentUpGuideBtn.height / 2 - 3;
         addChild(_talentUpGuideBtn);
         _talentText = new TextField();
         _talentText.x = 40;
         _talentText.y = 300;
         _talentText.autoSize = "left";
         _talentText.selectable = false;
         addChild(_talentText);
         _petLevelText = new TextField();
         _petLevelText.x = 40;
         _petLevelText.y = 375;
         _petLevelText.selectable = false;
         _experienceText = new TextField();
         _experienceText.x = 50;
         _experienceText.y = 400;
         _experienceText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         _experienceText.selectable = false;
         _lifeText = new TextField();
         _lifeText.x = 50;
         _lifeText.y = 422;
         _lifeText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         _lifeText.selectable = false;
         _activeSkillText = new TextField();
         _activeSkillText.x = 50;
         _activeSkillText.y = 385;
         _activeSkillText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         _activeSkillText.selectable = false;
         _passiveSkillText = new TextField();
         _passiveSkillText.x = 50;
         _passiveSkillText.y = 423;
         _passiveSkillText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         _passiveSkillText.selectable = false;
         _awakeSkillText = new TextField();
         _awakeSkillText.x = 218;
         _awakeSkillText.y = 383;
         _awakeSkillText.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16777215);
         _awakeSkillText.selectable = false;
         inforBtn.init(false);
         skillBtn.init(true);
         setText();
         petNameText.defaultTextFormat = new TextFormat(_loc1_.fontName,18,16777215);
         petNameText.embedFonts = true;
         _activeSkillText.embedFonts = true;
         _passiveSkillText.embedFonts = true;
         _awakeSkillText.embedFonts = true;
         _talentText.embedFonts = true;
         _petLevelText.embedFonts = true;
         _experienceText.embedFonts = true;
         _lifeText.embedFonts = true;
         _experienceText.text = "经验";
         _passiveSkillText.text = "被动技能";
         _activeSkillText.text = "主动技能";
         _awakeSkillText.text = "觉醒技能";
         _lifeText.text = "精气";
         _isShowBtn = true;
         _petShowContainer = new EntityShowContainer();
         _petShowContainer.init();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchToPetInformation",switchToInfor,true,0,true);
         addEventListener("switchToPetSkill",switchToSkill,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchToPetInformation",switchToInfor,true);
         removeEventListener("switchToPetSkill",switchToSkill,true);
      }
      
      private function setActive(param1:Number) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 == 0)
         {
            MyFunction.getInstance().changeSaturation(_activeSkillCell,-100);
            MyFunction.getInstance().changeSaturation(_awakeSkillCell,-100);
            _loc2_ = int(_passiveSkillCells.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               MyFunction.getInstance().changeSaturation(_passiveSkillCells[_loc3_] as DisplayObject,-100);
               _loc3_++;
            }
         }
         else
         {
            MyFunction.getInstance().changeSaturation(_activeSkillCell,0);
            MyFunction.getInstance().changeSaturation(_awakeSkillCell,0);
            _loc2_ = int(_passiveSkillCells.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               MyFunction.getInstance().changeSaturation(_passiveSkillCells[_loc3_] as DisplayObject,0);
               _loc3_++;
            }
         }
      }
      
      private function switchToInfor(param1:UIBtnEvent) : void
      {
         _currentState = 0;
         setPanel(_currentState);
         skillBtn.gotoTwoFrame();
      }
      
      private function switchToSkill(param1:UIBtnEvent) : void
      {
         _currentState = 1;
         setPanel(_currentState);
         inforBtn.gotoTwoFrame();
      }
      
      private function addChildToTalentLayer(param1:DisplayObject) : void
      {
         while(_talentLayer.numChildren > 0)
         {
            _talentLayer.removeChild(_talentLayer.getChildAt(_talentLayer.numChildren - 1));
         }
         if(!param1)
         {
            return;
         }
         _talentLayer.addChild(param1);
      }
      
      private function removeChildFromTalentLayer() : void
      {
         if(_talentLayer == null)
         {
            return;
         }
         while(_talentLayer.numChildren > 0)
         {
            _talentLayer.removeChild(_talentLayer.getChildAt(_talentLayer.numChildren - 1));
         }
      }
      
      private function hideAll() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = numChildren;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            getChildAt(_loc2_).visible = false;
            _loc2_++;
         }
      }
      
      private function showAll() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = numChildren;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            getChildAt(_loc2_).visible = true;
            _loc2_++;
         }
      }
      
      private function showText() : void
      {
         _textField.visible = true;
      }
      
      private function hideText() : void
      {
         _textField.visible = false;
      }
      
      private function setText() : void
      {
         _textField = new TextField();
         _textField.autoSize = "center";
         _textField.selectable = false;
         _textField.multiline = true;
         _textField.htmlText = "<b><font size=\'30\' color=\'#ffffff\'>没有出战宠物</font><br><font size=\'20\' color=\'#ffffff\'><li>点击背包中的宠物选择出战</li><li>宠物会在战斗中随机出现</li><li>也可以通过宠物蛋孵化获得</li></font></b>";
         _textField.width = _textField.textWidth + 2;
         _textField.height = _textField.textHeight + 2;
         _textField.x = 55;
         _textField.y = 200;
         addChild(_textField);
      }
      
      private function resetGuidBuyEquipmentId(param1:int, param2:int) : void
      {
         _talentUpGuideBtn.setEquipmentID(param1);
         _buyPetTrianPotionGuideBtn.setEquipmentID(param2);
      }
   }
}

