package UI.SocietySystem.MySocietyPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.PlayerDataOfApply;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ColumeForPlayerOfApply extends ButtonLogicShell2
   {
      private var m_clear:ClearHelper;
      
      private var m_playerNameText:TextField;
      
      private var m_playerLevelText:TextField;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_agreeBtn:ButtonLogicShell2;
      
      private var m_refuseBtn:ButtonLogicShell2;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_playerDataOfApply:PlayerDataOfApply;
      
      public function ColumeForPlayerOfApply()
      {
         super();
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         super.clear();
         m_playerNameText = null;
         m_playerLevelText = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_agreeBtn);
         m_agreeBtn = null;
         ClearUtil.clearObject(m_refuseBtn);
         m_refuseBtn = null;
         m_playerDataOfApply = null;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
      }
      
      public function setPlayerDataOfApply(param1:PlayerDataOfApply) : void
      {
         m_playerDataOfApply = param1;
         if(m_show)
         {
            initShow2();
         }
      }
      
      private function initShow() : void
      {
         m_playerNameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerNameText);
         m_playerNameText.mouseEnabled = false;
         m_playerLevelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerLevelText);
         m_playerLevelText.mouseEnabled = false;
         m_lookUpBtn = new ButtonLogicShell2();
         m_lookUpBtn.setShow(m_show["lookUpBtn"]);
         m_lookUpBtn.setTipString("查看玩家装备信息");
         m_agreeBtn = new ButtonLogicShell2();
         m_agreeBtn.setShow(m_show["agreeBtn"]);
         m_agreeBtn.setTipString("同意玩家加入帮会");
         m_refuseBtn = new ButtonLogicShell2();
         m_refuseBtn.setShow(m_show["refuseBtn"]);
         m_refuseBtn.setTipString("拒绝玩家加入帮会");
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_playerDataOfApply == null)
         {
            return;
         }
         m_playerNameText.text = !!m_playerDataOfApply.getName_applicant() ? m_playerDataOfApply.getName_applicant() : m_playerDataOfApply.getUid_applicant().toString();
         m_playerLevelText.text = m_playerDataOfApply.getLevel_applicant().toString();
      }
      
      public function getPlayerDataOfApply() : PlayerDataOfApply
      {
         return m_playerDataOfApply;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      public function getAgreeBtn() : ButtonLogicShell2
      {
         return m_agreeBtn;
      }
      
      public function getRefuseBtn() : ButtonLogicShell2
      {
         return m_refuseBtn;
      }
      
      override protected function onDown(param1:MouseEvent) : void
      {
         super.onDown(param1);
         changeTextField();
      }
      
      override protected function onUp(param1:MouseEvent) : void
      {
         super.onUp(param1);
         changeTextField();
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         super.onOut(param1);
         changeTextField();
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         super.onOver(param1);
         changeTextField();
      }
      
      private function changeTextField() : void
      {
         var _loc1_:String = m_playerNameText.text;
         m_playerNameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerNameText);
         m_playerNameText.mouseEnabled = false;
         m_playerNameText.text = _loc1_;
         _loc1_ = m_playerLevelText.text;
         m_playerLevelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerLevelText);
         m_playerLevelText.mouseEnabled = false;
         m_playerLevelText.text = _loc1_;
      }
   }
}

