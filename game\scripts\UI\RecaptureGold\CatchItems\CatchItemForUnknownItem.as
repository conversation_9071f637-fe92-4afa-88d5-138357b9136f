package UI.RecaptureGold.CatchItems
{
   import UI.MessageTextShow.MessageTextAnimationFunObject;
   import UI.MessageTextShow.MessageTextFilters;
   import UI.MessageTextShow.MessageTextFormat;
   import UI.MessageTextShow.MessageTextShow;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.RecaptureGold.Parent.CatchItem;
   import UI.RecaptureGold.Parent.IProp;
   import UI.RecaptureGold.RecaptureGoldFunction;
   import UI.SoundManager.SoundManager;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import flash.utils.getQualifiedClassName;
   
   public class CatchItemForUnknownItem extends CatchItem
   {
      public function CatchItemForUnknownItem()
      {
         super();
      }
      
      public function getUnknownItem(param1:XML, param2:Sprite, param3:SoundManager, param4:Number) : Object
      {
         var _loc20_:int = 0;
         var _loc15_:XML = null;
         var _loc25_:MessageTextShow = null;
         var _loc19_:Number = NaN;
         var _loc7_:String = null;
         var _loc22_:SoundTransform = null;
         var _loc13_:XMLList = null;
         var _loc8_:Number = NaN;
         var _loc16_:* = undefined;
         var _loc24_:* = undefined;
         var _loc10_:* = undefined;
         var _loc26_:* = undefined;
         var _loc17_:XMLList = null;
         var _loc9_:Number = NaN;
         var _loc5_:* = undefined;
         var _loc6_:Object = {};
         var _loc18_:String = getQualifiedClassName(this);
         var _loc12_:XMLList = param1.TargetData[0].item;
         var _loc11_:int = int(_loc12_.length());
         _loc20_ = 0;
         while(_loc20_ < _loc11_)
         {
            if(_loc12_[_loc20_].@classNameForCatch == _loc18_)
            {
               _loc15_ = _loc12_[_loc20_];
            }
            _loc20_++;
         }
         var _loc14_:Number = Number(_loc15_.@goodProbability) * param4;
         trace("好的可能性的值",_loc14_);
         var _loc23_:Number = Math.random();
         var _loc21_:Point = this.parent.localToGlobal(new Point(this.x,this.y));
         if(_loc23_ <= _loc14_)
         {
            trace("抽到好的");
            _loc19_ = Math.random();
            _loc13_ = _loc15_.Goods[0].good;
            _loc11_ = int(_loc13_.length());
            _loc8_ = 0;
            _loc20_ = 0;
            while(true)
            {
               if(_loc20_ < _loc11_)
               {
                  _loc8_ += Number(_loc13_[_loc20_].@probability);
                  if(_loc19_ > _loc8_)
                  {
                     continue;
                  }
                  _loc6_.content = String(_loc13_[_loc20_].@content);
                  switch(_loc6_.content)
                  {
                     case "goldNum":
                        trace("抽到获取金子！");
                        _loc16_ = MyFunction.getInstance().excreteString(_loc13_[_loc20_].@goldNum);
                        _loc6_.getGoldNum = getGoldNum(_loc16_[1],_loc16_[0]);
                        _loc25_ = new MessageTextShow("金子 +" + _loc6_.getGoldNum,MessageTextFormat.TEXT_FORMAT_1,MessageTextFilters.FILTERS_1,["from","to"],[0.5,2],[MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_3,MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_1]);
                        _loc7_ = String(param1.SoundData[0].@soundForGetGold);
                        _loc22_ = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc21_,param2);
                        param3.play(_loc7_,0,0,_loc22_);
                        break;
                     case "addStrength":
                        trace("抽到增强力量！");
                        _loc24_ = MyFunction.getInstance().excreteStringToNumber(_loc13_[_loc20_].@value);
                        _loc6_.value = getPhysicalStrength(_loc24_[1],_loc24_[0]);
                        _loc25_ = new MessageTextShow("力量 +" + int(_loc6_.value),MessageTextFormat.TEXT_FORMAT_1,MessageTextFilters.FILTERS_1,["from","to"],[0.5,2],[MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_3,MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_1]);
                        _loc7_ = String(param1.SoundData[0].@soundForAddStrength);
                        _loc22_ = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc21_,param2);
                        param3.play(_loc7_,0,0,_loc22_);
                        break;
                     case "getProps":
                        trace("抽到获取道具!");
                        _loc10_ = MyFunction.getInstance().excreteString(_loc13_[_loc20_].@propNum);
                        _loc26_ = getProps(_loc13_[_loc20_].@propClassName,_loc10_[1],_loc10_[0]);
                        _loc6_.props = _loc26_;
                        _loc25_ = new MessageTextShow("获得炸弹",MessageTextFormat.TEXT_FORMAT_1,MessageTextFilters.FILTERS_1,["from","to"],[0.5,2],[MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_3,MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_1]);
                        _loc7_ = String(param1.SoundData[0].@soundForGetProps);
                        _loc22_ = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc21_,param2);
                        param3.play(_loc7_,0,0,_loc22_);
                        break;
                     default:
                        throw new Error("还没为此装备好!");
                  }
               }
               _loc20_++;
            }
         }
         else
         {
            trace("抽到坏的");
            _loc19_ = Math.random();
            _loc17_ = _loc15_.Bads[0].bad;
            _loc11_ = int(_loc17_.length());
            _loc9_ = 0;
            _loc20_ = 0;
            while(_loc20_ < _loc11_)
            {
               _loc9_ += Number(_loc17_[_loc20_].@probability);
               if(_loc19_ <= _loc9_)
               {
                  _loc6_.content = String(_loc17_[_loc20_].@content);
                  var _loc27_:* = _loc6_.content;
                  if("subStrength" !== _loc27_)
                  {
                     throw new Error("还没为此装备好!");
                  }
                  trace("抽到减力量");
                  _loc5_ = MyFunction.getInstance().excreteStringToNumber(_loc17_[_loc20_].@value);
                  _loc6_.value = getPhysicalStrength(_loc5_[1],_loc5_[0]);
                  _loc25_ = new MessageTextShow("力量 " + int(_loc6_.value),MessageTextFormat.TEXT_FORMAT_1,MessageTextFilters.FILTERS_1,["from","to"],[0.5,2],[MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_3,MessageTextAnimationFunObject.ANIMATION_FUN_OBJECT_1]);
                  _loc7_ = String(param1.SoundData[0].@soundForDecStrength);
                  _loc22_ = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc21_,param2);
                  param3.play(_loc7_,0,0,_loc22_);
               }
               _loc20_++;
            }
         }
         _loc25_.x = _loc21_.x;
         _loc25_.y = _loc21_.y;
         param2.addChild(_loc25_);
         _loc25_.play();
         return _loc6_;
      }
      
      private function getGoldNum(param1:Number, param2:Number) : int
      {
         return Math.round(param2 + Math.round(Math.random() * (param1 - param2) * 100) / 100);
      }
      
      private function getPhysicalStrength(param1:Number, param2:Number) : Number
      {
         return param2 + Math.round(Math.random() * (param1 - param2) * 100) / 100;
      }
      
      private function getProps(param1:String, param2:int, param3:int) : Vector.<IProp>
      {
         var _loc7_:int = 0;
         var _loc6_:IProp = null;
         var _loc5_:Vector.<IProp> = new Vector.<IProp>();
         var _loc4_:int = param3 + Math.round(Math.random() * (param2 - param3));
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc6_ = MyFunction2.returnShowByClassName(param1) as IProp;
            _loc5_.push(_loc6_);
            _loc7_++;
         }
         return _loc5_;
      }
   }
}

