package UI.MiragePanel
{
   import UI.Button.NumberBtn.NumberBtnGroup;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MiragePanel.Button.BaojiHBtn;
   import UI.MiragePanel.Button.BaojiLBtn;
   import UI.MiragePanel.Button.GotoProtectBtn;
   import UI.MiragePanel.Button.GotoVipBtn;
   import UI.MiragePanel.Button.StartMirageBtn;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.Pets.Pet;
   import UI.Privilege.PrivilegeVO;
   import UI.Protect.ProtectData;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.Skill;
   import UI.SmallPackage.SmallPackage;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   
   public class MirageDataShow extends MySprite
   {
      private var _dataText1:TextField;
      
      private var _dataText2:TextField;
      
      private var _titleText1:TextField;
      
      private var _titleText2:TextField;
      
      private var _targetNameText:TextField;
      
      private var _currentValueText:TextField;
      
      private var _promoteValueText:TextField;
      
      private var _baojiTxt:TextField;
      
      private var _vipBaoji:TextField;
      
      private var _protectBaoji:TextField;
      
      private var _ruleText:TextField;
      
      private var _targetShow:*;
      
      private var _startMirageBtn:StartMirageBtn;
      
      private var _gotovipBtn:GotoVipBtn;
      
      private var _gotoProtectBtn:GotoProtectBtn;
      
      private var _baojilBtn:BaojiLBtn;
      
      private var _baojihBtn:BaojiHBtn;
      
      private var _luckStone:LuckStoneShow;
      
      private var _font:FangZhengKaTongJianTi;
      
      private var _target:DisplayObject;
      
      private var _mainPet:Pet;
      
      private var _assistantPet:Pet;
      
      public var numBtnGroup:NumberBtnGroup;
      
      public function MirageDataShow()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasEventListener("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         _dataText1 = null;
         _dataText2 = null;
         _titleText1 = null;
         _titleText2 = null;
         _baojiTxt = null;
         _vipBaoji = null;
         _protectBaoji = null;
         _targetNameText = null;
         _currentValueText = null;
         _promoteValueText = null;
         if(_startMirageBtn)
         {
            _startMirageBtn.clear();
         }
         _startMirageBtn = null;
         ClearUtil.clearObject(_gotovipBtn);
         _gotovipBtn = null;
         ClearUtil.clearObject(_gotoProtectBtn);
         _gotoProtectBtn = null;
         ClearUtil.clearObject(_baojilBtn);
         _baojilBtn = null;
         ClearUtil.clearObject(_baojihBtn);
         _baojihBtn = null;
         _luckStone = null;
         if(_targetShow)
         {
            _targetShow.removeEventListener("rollOver",onRoll,false);
            _targetShow.removeEventListener("rollOut",onRoll,false);
         }
         _targetShow = null;
         if(numBtnGroup)
         {
            numBtnGroup.clear();
         }
         numBtnGroup = null;
         _target = null;
         _mainPet = null;
         _assistantPet = null;
      }
      
      public function showData(param1:XML, param2:DisplayObject, param3:Pet, param4:Pet, param5:Function) : void
      {
         var x1:Number;
         var x2:Number;
         var y1:Number;
         var y2:Number;
         var str1:String;
         var successRate:Number;
         var currentHaveMirageNum:int;
         var currentMoney:Number;
         var promoteMoney:Number;
         var petSkillVO:PetPassiveSkillVO;
         var bBaoJi:Boolean;
         var vipBaoji:int;
         var datalist:Vector.<PrivilegeVO>;
         var t:int;
         var protectBaoji:int;
         var multiple:int;
         var rand:int;
         var arr:Array;
         var maxNum:int;
         var promoteXML:XML = param1;
         var target:DisplayObject = param2;
         var mainPet:Pet = param3;
         var assistantPet:Pet = param4;
         var showWarningBoxFun:Function = param5;
         _target = target;
         _mainPet = mainPet;
         _assistantPet = assistantPet;
         MirageFunction.getInstance().initData(target,mainPet,promoteXML);
         x1 = -85;
         x2 = 80;
         y1 = 280;
         y2 = 50;
         str1 = "";
         successRate = MirageFunction.getInstance().getRate(target,numBtnGroup.num,mainPet,assistantPet);
         currentHaveMirageNum = MirageFunction.getInstance().getCurrentHaveMirageNum();
         currentMoney = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1.playerVO.money : GamingUI.getInstance().player2.playerVO.money;
         promoteMoney = MirageFunction.getInstance().getPromoteMoney(target);
         str1 = toHTMLText("幻化成功率",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("     " + successRate.toString() + "%",20,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("幻化费用",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("     " + promoteMoney.toString(),20,_font.fontName,promoteMoney <= currentMoney ? "#00ff00" : "#ff0000") + toHTMLText("\n") + toHTMLText("剩余幻化次数",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("        " + currentHaveMirageNum,20,_font.fontName,!!currentHaveMirageNum ? "#00ff00" : "#ff0000");
         _dataText1.htmlText = str1;
         _dataText1.width = _dataText1.textWidth + 5;
         _dataText1.height = _dataText1.textHeight + 5;
         _dataText1.x = x1;
         _dataText1.y = y1;
         _titleText1.htmlText = toHTMLText("您选择的幻化属性是：",18,_font.fontName,"#ff6600");
         _titleText1.width = _titleText1.textWidth + 5;
         _titleText1.height = _titleText1.textHeight + 5;
         _titleText2.htmlText = toHTMLText("幻化后宠物的属性变化：",18,_font.fontName,"#ff6600");
         _titleText2.width = _titleText2.textWidth + 5;
         _titleText2.height = _titleText2.textHeight + 5;
         if(target is EssenceIcon)
         {
            _targetNameText.htmlText = toHTMLText("精气",15,_font.fontName,"#ffd581");
            _dataText2.htmlText = toHTMLText("精气值（额外增加）:",15,_font.fontName,"#ffd581");
            _currentValueText.htmlText = toHTMLText(Math.round(mainPet.petEquipmentVO.essentialPercent * mainPet.petEquipmentVO.essentialVolume).toString(),15,_font.fontName,"#ffd581");
            _targetShow = (target as EssenceIcon).clone();
            _targetShow.addEventListener("rollOver",onRoll,false,0,true);
            _targetShow.addEventListener("rollOut",onRoll,false,0,true);
         }
         else
         {
            if(!(target is Skill))
            {
               throw new Error();
            }
            petSkillVO = (target as Skill).skillVO as PetPassiveSkillVO;
            _targetNameText.htmlText = toHTMLText(petSkillVO.name,15,_font.fontName,"#ffd581");
            _dataText2.htmlText = toHTMLText(petSkillVO.description + "（额外增加）：",15,_font.fontName,"#ffd581");
            _currentValueText.htmlText = toHTMLText(petSkillVO.value.toString(),15,_font.fontName,"#ffd581");
            _targetShow = (target as Skill).clone();
            _targetShow.addEventListener("rollOver",onRoll,false,0,true);
            _targetShow.addEventListener("rollOut",onRoll,false,0,true);
         }
         _targetNameText.width = _targetNameText.textWidth + 5;
         _targetNameText.height = _targetNameText.textHeight + 5;
         _dataText2.width = _dataText2.textWidth + 5;
         _dataText2.height = _dataText2.textHeight + 5;
         bBaoJi = false;
         vipBaoji = 0;
         datalist = GamingUI.getInstance().player1.vipVO.privilegeVOs;
         t = 0;
         while(t < datalist.length)
         {
            if(datalist[t].id == 177 || datalist[t].id == 178 || datalist[t].id == 179 || datalist[t].id == 180 || datalist[t].id == 181 || datalist[t].id == 182)
            {
               vipBaoji = datalist[t].value;
            }
            t++;
         }
         protectBaoji = ProtectData.getInstance().huanhuaValue;
         multiple = 1;
         rand = 1 + Math.random() * 100;
         if(vipBaoji + protectBaoji > 0 && rand < vipBaoji + protectBaoji)
         {
            multiple = 2;
            bBaoJi = true;
         }
         _baojiTxt.htmlText = toHTMLText("暴击率:" + (vipBaoji + protectBaoji) + "%",15,_font.fontName,"#ffd581");
         _vipBaoji.htmlText = toHTMLText("VIP提升:" + vipBaoji + "%",15,_font.fontName,"#ffd581");
         _protectBaoji.htmlText = toHTMLText("如来保佑提升:" + protectBaoji + "%",15,_font.fontName,"#ffd581");
         _baojiTxt.width = _baojiTxt.textWidth + 5;
         _baojiTxt.height = _baojiTxt.textHeight + 5;
         _vipBaoji.width = _vipBaoji.textWidth + 5;
         _vipBaoji.height = _vipBaoji.textHeight + 5;
         _protectBaoji.width = _protectBaoji.textWidth + 5;
         _protectBaoji.height = _protectBaoji.textHeight + 5;
         _currentValueText.width = _currentValueText.textWidth + 5;
         _currentValueText.height = _currentValueText.textHeight + 5;
         arr = MirageFunction.getInstance().getPromoteValueRange(target,mainPet,assistantPet);
         _promoteValueText.htmlText = toHTMLText("↑" + arr[0] + " - " + arr[1],15,"","#00ff00");
         if(target is EssenceIcon)
         {
            _promoteValueText.htmlText += toHTMLText(" 点",15,_font.fontName,"#ffd581");
         }
         else
         {
            if(!(target is Skill))
            {
               throw new Error();
            }
            _promoteValueText.htmlText += toHTMLText(" " + ((target as Skill).skillVO as PetPassiveSkillVO).unit,15,_font.fontName,"#ffd581");
         }
         _promoteValueText.width = _promoteValueText.textWidth + 5;
         _promoteValueText.height = _promoteValueText.textHeight + 5;
         _titleText1.x = x2;
         _titleText1.y = y2;
         y2 += _titleText1.height + 5;
         _targetShow.x = x2 + _targetShow.width / 2 + 30;
         _targetShow.y = y2 + _targetShow.height / 2;
         addChild(_targetShow);
         _targetNameText.x = _targetShow.x + _targetShow.width / 2 + 5;
         _targetNameText.y = _targetShow.y - _targetNameText.height / 2;
         y2 += _targetShow.height + 5;
         _titleText2.x = x2;
         _titleText2.y = y2;
         y2 += _titleText2.height + 5;
         _dataText2.x = x2;
         _dataText2.y = y2;
         _currentValueText.x = _dataText2.x + _dataText2.width + 5;
         _currentValueText.y = y2;
         if(x + _currentValueText.x + _currentValueText.width + _promoteValueText.width + 50 < stage.stageWidth)
         {
            _promoteValueText.x = _currentValueText.x + _currentValueText.width + 5;
            _promoteValueText.y = y2;
         }
         else
         {
            _promoteValueText.x = x2;
            _promoteValueText.y = y2 + _currentValueText.height + 5;
         }
         maxNum = MirageFunction.getInstance().getLuckStoneMaxNum(target);
         numBtnGroup.setMaxNumAndFun(maxNum,function(param1:String, param2:Function):void
         {
            param2(param1,0);
         },["背包中的幸运石为" + maxNum + "个, 没有更多了！",showWarningBoxFun]);
         _startMirageBtn.visible = true;
         _gotovipBtn.visible = true;
         _gotoProtectBtn.visible = true;
         _baojihBtn.visible = true;
         _baojilBtn.visible = true;
         _luckStone.visible = true;
         numBtnGroup.visible = true;
         hideRuleText();
         if(vipBaoji + protectBaoji > 0)
         {
            _baojihBtn.visible = false;
            _baojilBtn.visible = true;
         }
         else
         {
            _baojihBtn.visible = true;
            _baojilBtn.visible = false;
         }
      }
      
      public function clearData() : void
      {
         if(_targetShow && getChildByName(_targetShow.name))
         {
            removeChild(_targetShow);
         }
         if(_targetShow)
         {
            _targetShow.removeEventListener("rollOver",onRoll,false);
            _targetShow.removeEventListener("rollOut",onRoll,false);
         }
         _targetShow = null;
         _dataText1.htmlText = "";
         _dataText2.htmlText = "";
         _baojiTxt.htmlText = "";
         _vipBaoji.htmlText = "";
         _protectBaoji.htmlText = "";
         _titleText1.htmlText = "";
         _titleText2.htmlText = "";
         _targetNameText.htmlText = "";
         _currentValueText.htmlText = "";
         _promoteValueText.htmlText = "";
         numBtnGroup.restore();
         _startMirageBtn.visible = false;
         _gotovipBtn.visible = false;
         _gotoProtectBtn.visible = false;
         _baojihBtn.visible = false;
         _baojilBtn.visible = false;
         _luckStone.visible = false;
         numBtnGroup.visible = false;
         _target = null;
         _mainPet = null;
         _assistantPet = null;
      }
      
      public function showRuleText() : void
      {
         _ruleText.visible = true;
      }
      
      public function hideRuleText() : void
      {
         _ruleText.visible = false;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("changeNum",changeSuccessRate,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("changeNum",changeSuccessRate,true);
      }
      
      private function changeSuccessRate(param1:UIPassiveEvent) : void
      {
         var _loc6_:int = MirageFunction.getInstance().getRate(_target,numBtnGroup.num,_mainPet,_assistantPet);
         var _loc5_:int = MirageFunction.getInstance().getCurrentHaveMirageNum();
         var _loc2_:Number = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1.playerVO.money : GamingUI.getInstance().player2.playerVO.money;
         var _loc4_:Number = MirageFunction.getInstance().getPromoteMoney(_target);
         var _loc3_:String = toHTMLText("幻化成功率",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("     " + _loc6_.toString() + "%",20,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("幻化费用",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("     " + _loc4_.toString(),20,_font.fontName,_loc4_ <= _loc2_ ? "#00ff00" : "#ff0000") + toHTMLText("\n") + toHTMLText("剩余幻化次数",18,_font.fontName,"#ffd581") + toHTMLText("\n") + toHTMLText("        " + _loc5_.toString(),20,_font.fontName,!!_loc5_ ? "#00ff00" : "#ff0000");
         _dataText1.htmlText = _loc3_;
      }
      
      private function toHTMLText(param1:String, param2:int = 12, param3:String = "", param4:String = "#000000") : String
      {
         var _loc5_:String = null;
         if(param3 == "")
         {
            _loc5_ = "<font  size=\'" + param2 + "\' color=\'" + param4 + "\'>" + param1 + "</font>";
         }
         else
         {
            _loc5_ = "<font face=\'" + param3 + "\' size=\'" + param2 + "\' color=\'" + param4 + "\'>" + param1 + "</font>";
         }
         return _loc5_;
      }
      
      private function init() : void
      {
         _dataText1 = new TextField();
         _dataText2 = new TextField();
         _baojiTxt = new TextField();
         _vipBaoji = new TextField();
         _protectBaoji = new TextField();
         _titleText1 = new TextField();
         _titleText2 = new TextField();
         _targetNameText = new TextField();
         _currentValueText = new TextField();
         _promoteValueText = new TextField();
         _ruleText = new TextField();
         _dataText1.selectable = false;
         _dataText2.selectable = false;
         _baojiTxt.selectable = false;
         _vipBaoji.selectable = false;
         _protectBaoji.selectable = false;
         _titleText1.selectable = false;
         _titleText2.selectable = false;
         _targetNameText.selectable = false;
         _currentValueText.selectable = false;
         _promoteValueText.selectable = false;
         _ruleText.selectable = false;
         _dataText1.embedFonts = true;
         _dataText2.embedFonts = true;
         _baojiTxt.embedFonts = true;
         _vipBaoji.embedFonts = true;
         _protectBaoji.embedFonts = true;
         _titleText1.embedFonts = true;
         _titleText2.embedFonts = true;
         _targetNameText.embedFonts = true;
         _currentValueText.embedFonts = true;
         _ruleText.multiline = true;
         _ruleText.wordWrap = true;
         _dataText1.filters = [new GlowFilter(0,1,2,2,10,3)];
         _dataText2.filters = [new GlowFilter(0,1,2,2,10,3)];
         _baojiTxt.filters = [new GlowFilter(0,1,2,2,10,3)];
         _vipBaoji.filters = [new GlowFilter(0,1,2,2,10,3)];
         _protectBaoji.filters = [new GlowFilter(0,1,2,2,10,3)];
         _titleText1.filters = [new GlowFilter(0,1,2,2,10,3)];
         _titleText2.filters = [new GlowFilter(0,1,2,2,10,3)];
         _targetNameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _currentValueText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _promoteValueText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _ruleText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _ruleText.htmlText = "<b>" + toHTMLText("规则说明：",18,"","#ffff00") + "<br>" + toHTMLText("幻化条件：",16,"","#ffff00") + "<br>" + toHTMLText("1.主宠、副宠必须是相同种类。",15,"","#ffffff") + "<br>" + toHTMLText("2.主宠、副宠必须是第三形态或超进化形态。。",15,"","#ffffff") + "<br>" + toHTMLText("3.每日可以进行宠物幻化5次，VIP玩家可以幻化更多次。",15,"","#ffffff") + "<br>" + toHTMLText("幻化有什么提升？",16,"","#ffff00") + "<br>" + toHTMLText("1.被动技能幻化：当主宠和副宠都拥有一个相同的被动技能时可以选择幻化这个被动技能（激怒、奥义光环无法幻化）。 ",15,"","#ffffff") + "<br>" + toHTMLText("2.精气转移：只要符合幻化条件都可以选择将副宠的精气转移给主宠。",15,"","#ffffff") + "<br>" + toHTMLText("幻化结果：",16,"","#ffff00") + "<br>" + toHTMLText("幻化成功：主宠幻化属性得到提升，消耗副宠和幸运宝石。 ",15,"","#ffffff") + "<br>" + toHTMLText("幻化失败：主宠保持不变，消耗副宠和幸运宝石。",15,"","#ffffff") + "<b>" + toHTMLText("幻化暴击：",16,"","#ffff00") + "<br>" + toHTMLText("幻化成功时有可能暴击，暴击会获取双倍幻化数值，如来保佑和VIP特权会提升暴击几率。",15,"","#ffffff") + "<b>";
         addChild(_dataText1);
         addChild(_dataText2);
         addChild(_baojiTxt);
         addChild(_vipBaoji);
         addChild(_protectBaoji);
         addChild(_titleText1);
         addChild(_titleText2);
         addChild(_targetNameText);
         addChild(_currentValueText);
         addChild(_promoteValueText);
         _ruleText.width = 350;
         _ruleText.height = 380;
         _ruleText.x = 65;
         _ruleText.y = 50;
         addChild(_ruleText);
         _font = new FangZhengKaTongJianTi();
         _startMirageBtn = new StartMirageBtn();
         _startMirageBtn.x = 150;
         _startMirageBtn.y = 350;
         _startMirageBtn.visible = false;
         addChild(_startMirageBtn);
         _gotovipBtn = new GotoVipBtn();
         _gotovipBtn.x = 300;
         _gotovipBtn.y = 270;
         _gotovipBtn.visible = false;
         addChild(_gotovipBtn);
         _gotoProtectBtn = new GotoProtectBtn();
         _gotoProtectBtn.x = 300;
         _gotoProtectBtn.y = 300;
         _gotoProtectBtn.visible = false;
         addChild(_gotoProtectBtn);
         _baojilBtn = new BaojiLBtn();
         _baojilBtn.x = 110;
         _baojilBtn.y = 300;
         _baojilBtn.visible = false;
         addChild(_baojilBtn);
         _baojihBtn = new BaojiHBtn();
         _baojihBtn.x = 110;
         _baojihBtn.y = 300;
         _baojihBtn.visible = false;
         addChild(_baojihBtn);
         _baojiTxt.x = 165;
         _baojiTxt.y = 250;
         _vipBaoji.x = 165;
         _vipBaoji.y = 280;
         _protectBaoji.x = 165;
         _protectBaoji.y = 310;
         _luckStone = new LuckStoneShow();
         _luckStone.x = -40;
         _luckStone.y = 200;
         _luckStone.scaleX = 1.5;
         _luckStone.scaleY = 1.5;
         _luckStone.visible = false;
         addChild(_luckStone);
         numBtnGroup = new NumberBtnGroup();
         numBtnGroup.x = _luckStone.x + (_luckStone.width - numBtnGroup.width) / 2 - 15;
         numBtnGroup.y = _luckStone.y + 30;
         numBtnGroup.visible = false;
         addChild(numBtnGroup);
      }
      
      private function onRoll(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

