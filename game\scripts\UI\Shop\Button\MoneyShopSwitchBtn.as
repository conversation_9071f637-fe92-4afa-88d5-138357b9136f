package UI.Shop.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class MoneyShopSwitchBtn extends SwitchBtn
   {
      public function MoneyShopSwitchBtn()
      {
         super();
         setTipString("金币杂货");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchShop"));
      }
   }
}

