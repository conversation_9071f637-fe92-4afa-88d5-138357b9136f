package UI2.realname
{
   import Json.MyJSON;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.ExchangeGiftBag.ExchangeGiftData;
   import UI.ExchangeGiftBag.GiftMountData;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PlayerDataForPK;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class RealNamePanel extends MySprite
   {
      private var yaYaLeURL:String = "http://my.4399.com/jifen/api-apply";
      
      private var website:String = "http://my.4399.com/forums/thread-59758478";
      
      private var getStatusURL:String = "http://save.api.4399.com/index.php?c=realname&ac=status";
      
      private var submitURL:String = "http://save.api.4399.com/index.php?c=realname&ac=submit";
      
      private var logURL:String = "http://stat.api.4399.com/archive_statistics/log.js?game_id=100021366";
      
      private var exURL:String = "http://save.api.4399.com/index.php?c=realname&ac=suggest";
      
      public var _show:MovieClip;
      
      private var m_txtCode:TextField;
      
      private var m_txtName:TextField;
      
      private var m_txtCodeEx:TextField;
      
      private var m_txtNameEx:TextField;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_toInputViewBtn:ButtonLogicShell2;
      
      private var m_sendRealNameBtn:ButtonLogicShell2;
      
      private var m_cancelRealNameBtn:ButtonLogicShell2;
      
      private var m_minorSureBtn:ButtonLogicShell2;
      
      private var m_minorCancelBtn:ButtonLogicShell2;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var equipments:Vector.<Equipment>;
      
      private var type:String;
      
      private var giftXML:XML;
      
      private var _exchangeGiftBagXML:XML;
      
      private var _currentServeTime:String;
      
      private var _newEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _mountData:GiftMountData;
      
      private var realNameResult:Object;
      
      private var saveNameTxt:String = "";
      
      private var saveCodeTxt:String = "";
      
      private const const_realName:String = "realName2019";
      
      private var canGetReward:Boolean = false;
      
      public function RealNamePanel()
      {
         super();
         name = "RealNamePanel";
         initParams();
         loadXML();
      }
      
      private function getRealNameStatus() : void
      {
         if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
         {
            GamingUI.getInstance().manBan.text.text = "获取实名信息中...";
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderRealNameStatusCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorRealNameStatusHandler);
         _loc1_.addEventListener("securityError",onErrorRealNameStatusHandler);
         var _loc3_:String = this.getStatusURL + "&uid=" + GameData.getInstance().getLoginReturnData().getUid();
         var _loc2_:URLRequest = new URLRequest(_loc3_);
         _loc2_.method = "GET";
         _loc1_.load(_loc2_);
      }
      
      private function logRealNameStatus(param1:int) : void
      {
         var _loc2_:URLLoader = new URLLoader();
         var _loc4_:String = "http://stat.api.4399.com/archive_statistics/log.js?game_id=100021366&uid=" + GameData.getInstance().getLoginReturnData().getUid() + "&index=" + GameData.getInstance().getSaveFileData().index + "&" + "a=" + param1;
         var _loc3_:URLRequest = new URLRequest(_loc4_);
         _loc3_.method = "GET";
         _loc2_.load(_loc3_);
      }
      
      protected function onLoaderRealNameStatusCompleteHandler(param1:Event) : void
      {
         var _loc2_:Object = null;
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderRealNameStatusCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRealNameStatusHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRealNameStatusHandler);
         trace(param1.currentTarget.data);
         try
         {
            _loc2_ = MyJSON.decode(param1.currentTarget.data);
         }
         catch(error:Error)
         {
            showWarningBox(param1.currentTarget.data,0);
            return;
         }
         this.realNameResult = _loc2_;
         init();
      }
      
      protected function onErrorRealNameStatusHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRealNameStatusCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRealNameStatusHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRealNameStatusHandler);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         this.clear();
      }
      
      private function sendRealNameInfo() : void
      {
         if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
         {
            GamingUI.getInstance().manBan.text.text = "提交实名信息中...";
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderSendRealNameInfoCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorSendRealNameInfoHandler);
         _loc1_.addEventListener("securityError",onErrorSendRealNameInfoHandler);
         var _loc4_:String = this.submitURL;
         var _loc2_:URLRequest = new URLRequest();
         _loc2_.method = "POST";
         var _loc3_:* = new URLVariables();
         _loc3_.uid = GameData.getInstance().getLoginReturnData().getUid();
         _loc3_.realname = m_txtName.text;
         _loc3_.idcard = m_txtCode.text;
         saveCodeTxt = m_txtCode.text;
         saveNameTxt = m_txtName.text;
         _loc2_.data = _loc3_;
         _loc2_.url = _loc4_;
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderSendRealNameInfoCompleteHandler(param1:Event) : void
      {
         var _loc2_:Object = null;
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderSendRealNameInfoCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorSendRealNameInfoHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorSendRealNameInfoHandler);
         trace(param1.currentTarget.data);
         try
         {
            _loc2_ = MyJSON.decode(param1.currentTarget.data);
         }
         catch(error:Error)
         {
            showWarningBox(param1.currentTarget.data,0);
            return;
         }
         if(this.canGetReward)
         {
            showWarningBox(_loc2_.desc,0);
            this.showStatusView(1);
            return;
         }
         this.realNameResult = _loc2_;
         if(_loc2_.success)
         {
            this.realNameResult.cert = 1;
            this.canGetReward = true;
            showWarningBox(this.realNameResult.desc,0);
            this.logRealNameStatus(10002);
            if(_loc2_.reconfirm)
            {
               this.showStatusView(3);
            }
            else
            {
               this.showStatusView(1);
            }
         }
         else
         {
            showWarningBox(_loc2_.desc,0);
         }
      }
      
      protected function onErrorSendRealNameInfoHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderSendRealNameInfoCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorSendRealNameInfoHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorSendRealNameInfoHandler);
         showWarningBox("提交失败，请稍后重试",0);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         this.clear();
      }
      
      private function getRealNameEx() : void
      {
         if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
         {
            GamingUI.getInstance().manBan.text.text = "获取信息中...";
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderRealNameExCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorRealNameExHandler);
         _loc1_.addEventListener("securityError",onErrorRealNameExHandler);
         var _loc3_:String = this.exURL + "&uid=" + GameData.getInstance().getLoginReturnData().getUid();
         var _loc2_:URLRequest = new URLRequest(_loc3_);
         _loc2_.method = "GET";
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderRealNameExCompleteHandler(param1:Event) : void
      {
         var _loc2_:Object = null;
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderRealNameExCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRealNameExHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRealNameExHandler);
         trace(param1.currentTarget.data);
         try
         {
            _loc2_ = MyJSON.decode(param1.currentTarget.data);
         }
         catch(error:Error)
         {
            showWarningBox(param1.currentTarget.data,0);
            return;
         }
         if(_loc2_.success)
         {
            if(m_txtNameEx)
            {
               m_txtNameEx.text = "如:" + _loc2_.realname;
            }
            if(m_txtCodeEx)
            {
               m_txtCodeEx.text = "如:" + _loc2_.idcard;
            }
         }
         else
         {
            if(m_txtNameEx)
            {
               m_txtNameEx.text = "";
            }
            if(m_txtCodeEx)
            {
               m_txtCodeEx.text = "";
            }
         }
      }
      
      protected function onErrorRealNameExHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRealNameExCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRealNameExHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorRealNameExHandler);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         this.clear();
      }
      
      private function getGiftTrue() : void
      {
         var _loc1_:SaveTaskInfo = null;
         if(Boolean(_newEquipmentVOs) || _mountData)
         {
            if(_newEquipmentVOs)
            {
               MyFunction2.trueAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,showWarningBox,["礼包领取成功！",0]);
            }
            if(_mountData)
            {
               GamingUI.getInstance().player1.getMountsVO().addGetMaterialPointNum(_mountData.getZhaoHuanNum());
               GamingUI.getInstance().player1.getMountsVO().addStrengthenPointNum(_mountData.getLingShouShiNum());
               GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + _mountData.getYuanbaoNum();
               PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint + _mountData.getPkNum();
            }
            ExchangeGiftData.getInstance().exchangeGiftNames.push("realName2019");
            showWarningBox("礼包领取成功",0);
            this.canGetReward = false;
            _loc1_ = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame();
            this.showStatusView(1);
            GamingUI.getInstance().externalPanel.middlePanel.hideRealNameBtn();
         }
      }
      
      private function initReward() : void
      {
         equipmentVOsData = new EquipmentVOsData();
         equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(giftXML,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         showEq();
      }
      
      private function showEq() : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         var _loc1_:TextFormat = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         equipments = new Vector.<Equipment>();
         _loc3_ = 0;
         while(_loc3_ < equipmentVOsData.getEquipmentVONum())
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData.getEquipmentVOByIndex(_loc3_));
            _show["eqCell_" + _loc3_].addChild(_loc2_);
            _show["eqCellTxt_" + _loc3_].autoSize = "center";
            _show["eqCellTxt_" + _loc3_].text = _loc2_.equipmentVO.name;
            _loc1_ = _show["eqCellTxt_" + _loc3_].getTextFormat();
            _loc1_.size = 20 + 2 * (4 - _loc2_.equipmentVO.name.length);
            _show["eqCellTxt_" + _loc3_].y -= 4 - _loc2_.equipmentVO.name.length;
            _show["eqCellTxt_" + _loc3_].setTextFormat(_loc1_);
            equipments.push(_loc2_);
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            _loc3_++;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function showresult(param1:int) : void
      {
         switch(param1 - 104)
         {
            case 0:
            case 3:
            case 6:
               showWarningBox("此兑换码已被使用！",0);
               break;
            case 1:
               showWarningBox("" + param1,0);
               break;
            case 4:
               showWarningBox("" + param1,0);
               break;
            case 7:
               showWarningBox("" + param1,0);
               break;
            case 8:
               showWarningBox("您已领取过初级礼包！",0);
               break;
            case 9:
               showWarningBox("您已领取过中级礼包！",0);
               break;
            case 10:
               showWarningBox("您已领取过高级礼包！",0);
               break;
            default:
               showWarningBox("" + param1,0);
         }
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(_newEquipmentVOs);
         _newEquipmentVOs = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.clearObject(m_toInputViewBtn);
         m_toInputViewBtn = null;
         ClearUtil.clearObject(m_sendRealNameBtn);
         m_sendRealNameBtn = null;
         ClearUtil.clearObject(m_minorSureBtn);
         m_minorSureBtn = null;
         ClearUtil.clearObject(m_cancelRealNameBtn);
         m_cancelRealNameBtn = null;
         ClearUtil.clearObject(m_minorCancelBtn);
         m_minorCancelBtn = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         GamingUI.getInstance().clearRealNamePanel();
         super.clear();
      }
      
      private function initParams() : void
      {
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setTipString("关闭");
         m_getRewardBtn = new ButtonLogicShell2();
         m_toInputViewBtn = new ButtonLogicShell2();
         m_sendRealNameBtn = new ButtonLogicShell2();
         m_cancelRealNameBtn = new ButtonLogicShell2();
         m_minorSureBtn = new ButtonLogicShell2();
         m_minorCancelBtn = new ButtonLogicShell2();
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("RealNamePanel") as MovieClip;
         _show.visible = false;
         addChild(_show);
         _show.gotoAndStop("1");
         this.initReward();
         addEventListener("clickButton",clickButton,true,0,true);
         m_quitBtn.setShow(_show["quitBtn"]);
         if(this.realNameResult)
         {
            if(this.realNameResult.success)
            {
               if(this.realNameResult.cert)
               {
                  if(this.realNameResult.reconfirm)
                  {
                     this.showStatusView(1);
                  }
                  else
                  {
                     this.showStatusView(1);
                  }
               }
               else
               {
                  this.logRealNameStatus(10001);
                  this.showStatusView(1);
               }
            }
            else
            {
               showWarningBox(this.realNameResult.desc,0);
               this.clear();
            }
         }
      }
      
      private function showStatusView(param1:int) : void
      {
         _show.visible = true;
         _show.gotoAndStop(param1);
         switch(param1 - 1)
         {
            case 0:
               showEq();
               m_toInputViewBtn.setShow(_show["toInputBtn"]);
               m_getRewardBtn.setShow(_show["getRewardBtn"]);
               if(this.realNameResult.cert || this.canGetReward)
               {
                  m_toInputViewBtn.getShow().visible = false;
                  m_getRewardBtn.getShow().visible = true;
                  if(judeIsHaveTheBag("realName2019"))
                  {
                     Sprite(m_getRewardBtn.getShow()).mouseChildren = false;
                     Sprite(m_getRewardBtn.getShow()).mouseEnabled = false;
                     MyFunction.getInstance().changeSaturation(m_getRewardBtn.getShow(),-100);
                  }
                  break;
               }
               m_toInputViewBtn.getShow().visible = true;
               m_getRewardBtn.getShow().visible = false;
               break;
            case 1:
               m_sendRealNameBtn.setShow(_show["sendRealNameBtn"]);
               m_cancelRealNameBtn.setShow(_show["cancelRealNameBtn"]);
               m_txtCode = _show["codetext"] as TextField;
               m_txtCode.type = "input";
               m_txtName = _show["nametext"] as TextField;
               m_txtName.type = "input";
               m_txtCodeEx = _show["codetextex"] as TextField;
               m_txtNameEx = _show["nametextex"] as TextField;
               m_txtCode.text = saveCodeTxt;
               m_txtName.text = saveNameTxt;
               this.getRealNameEx();
               break;
            case 2:
               m_minorCancelBtn.setShow(_show["minorCancelBtn"]);
               m_minorSureBtn.setShow(_show["minorSureBtn"]);
               break;
            case 3:
               m_toInputViewBtn.setShow(_show["toInputBtn"]);
         }
      }
      
      private function judeIsHaveTheBag(param1:String) : Boolean
      {
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = !!ExchangeGiftData.getInstance().exchangeGiftNames ? ExchangeGiftData.getInstance().exchangeGiftNames.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(ExchangeGiftData.getInstance().exchangeGiftNames[_loc4_] == param1)
            {
               _loc3_ = true;
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      private function loadXML() : void
      {
         MyFunction2.loadXMLAndGetServerTimeFunction("ExchangeGiftBag",function(param1:XML, param2:String):void
         {
            _exchangeGiftBagXML = param1;
            _currentServeTime = param2;
            giftXML = _exchangeGiftBagXML.Shimingrenzheng[0];
            getRealNameStatus();
         },showWarningBox,true);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function getGift() : void
      {
         var i:int;
         var length:int;
         var id:int;
         var ids:Vector.<int>;
         var nums:Vector.<int>;
         ClearUtil.clearObject(_newEquipmentVOs);
         _newEquipmentVOs = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         i = 0;
         ids = new Vector.<int>();
         nums = new Vector.<int>();
         if(giftXML)
         {
            length = int(giftXML.children().length());
            i = 0;
            while(i < length)
            {
               id = int(giftXML.children()[i].@id);
               ids.push(id);
               nums.push(int(giftXML.children()[i].@num));
               i++;
            }
            _newEquipmentVOs = XMLSingle.getEquipmentVOsIDs(ids,XMLSingle.getInstance().equipmentXML,nums,true);
            MyFunction2.falseAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,function():void
            {
               showWarningBox("玩家1背包放不下礼包！",0);
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
            },this.getGiftTrue,null,null);
            _mountData = new GiftMountData();
            _mountData.initByXML(giftXML);
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quitBtn:
               clear();
               return;
            case m_getRewardBtn:
               this.getGift();
               break;
            case m_toInputViewBtn:
               this.showStatusView(2);
               break;
            case m_sendRealNameBtn:
               if(m_txtCode.text && m_txtName.text)
               {
                  this.sendRealNameInfo();
                  break;
               }
               showWarningBox("请填写信息",0);
               break;
            case m_cancelRealNameBtn:
               this.showStatusView(1);
               break;
            case m_minorSureBtn:
               this.showStatusView(1);
               break;
            case m_minorCancelBtn:
               this.showStatusView(2);
         }
      }
   }
}

