package UI2.newSign
{
   import UI.DataManagerParent;
   
   public class NewSignData extends DataManagerParent
   {
      private static var _instance:NewSignData;
      
      public var signedArr:Array;
      
      private var _needTimes:Array;
      
      private var _currentGiftType:int = 0;
      
      private var _isGetGiftTime0:Boolean = false;
      
      private var _isGetGiftTime1:Boolean = false;
      
      private var _isGetGiftTime2:<PERSON>olean = false;
      
      private var _isGetGiftTime3:<PERSON>olean = false;
      
      private var _isGetGiftTime4:Boolean = false;
      
      private var _isGetGiftTime5:Boolean = false;
      
      private var _timeStr:String = "";
      
      public function NewSignData()
      {
         super();
      }
      
      public static function getInstance() : NewSignData
      {
         if(!_instance)
         {
            _instance = new NewSignData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         _instance = null;
      }
      
      override protected function init() : void
      {
         super.init();
         signedArr = [];
         needTimes = [1,2,5,10,16,25];
         currentGiftType = _currentGiftType;
         timeStr = _timeStr;
         isGetGiftTime0 = _isGetGiftTime0;
         isGetGiftTime1 = _isGetGiftTime1;
         isGetGiftTime2 = _isGetGiftTime2;
         isGetGiftTime3 = _isGetGiftTime3;
         isGetGiftTime4 = _isGetGiftTime4;
         isGetGiftTime5 = _isGetGiftTime5;
      }
      
      public function initFromSaveXML(param1:XML, param2:String) : void
      {
         var _loc4_:Array = null;
         var _loc7_:int = 0;
         if(param1.hasOwnProperty("newSign") == false)
         {
            return;
         }
         var _loc3_:XML = param1.newSign[0];
         if(_loc3_)
         {
            this.signedArr = [];
            _loc4_ = String(_loc3_.@signedArr).split("|");
            _loc7_ = 0;
            while(_loc7_ < _loc4_.length)
            {
               if(int(_loc4_[_loc7_]))
               {
                  this.signedArr.push(int(_loc4_[_loc7_]));
               }
               _loc7_++;
            }
            this.currentGiftType = int(_loc3_.@currentGiftType);
            this.isGetGiftTime0 = Boolean(int(_loc3_.@isGetGiftTime0));
            this.isGetGiftTime1 = Boolean(int(_loc3_.@isGetGiftTime1));
            this.isGetGiftTime2 = Boolean(int(_loc3_.@isGetGiftTime2));
            this.isGetGiftTime3 = Boolean(int(_loc3_.@isGetGiftTime3));
            this.isGetGiftTime4 = Boolean(int(_loc3_.@isGetGiftTime4));
            this.isGetGiftTime5 = Boolean(int(_loc3_.@isGetGiftTime5));
            this.timeStr = String(_loc3_.@timeStr);
         }
         if(!this.timeStr)
         {
            this.timeStr = param2;
            return;
         }
         var _loc6_:String = param2.split(" ")[0];
         var _loc5_:String = this.timeStr.split(" ")[0];
         if(_loc6_ != _loc5_)
         {
            isGetGiftTime0 = false;
            if(_loc6_.split("-")[1] != _loc5_.split("-")[1])
            {
               if(getIsGetTimeAward(5))
               {
                  if(currentGiftType < 3)
                  {
                     currentGiftType++;
                  }
                  else
                  {
                     currentGiftType = 0;
                  }
               }
               signedArr = [];
               isGetGiftTime0 = false;
               isGetGiftTime1 = false;
               isGetGiftTime2 = false;
               isGetGiftTime3 = false;
               isGetGiftTime4 = false;
               isGetGiftTime5 = false;
            }
            this.timeStr = param2;
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <newSign />;
         _loc1_.@signedArr = signedArr.join("|");
         _loc1_.@currentGiftType = currentGiftType;
         _loc1_.@isGetGiftTime0 = int(isGetGiftTime0);
         _loc1_.@isGetGiftTime1 = int(isGetGiftTime1);
         _loc1_.@isGetGiftTime2 = int(isGetGiftTime2);
         _loc1_.@isGetGiftTime3 = int(isGetGiftTime3);
         _loc1_.@isGetGiftTime4 = int(isGetGiftTime4);
         _loc1_.@isGetGiftTime5 = int(isGetGiftTime5);
         _loc1_.@timeStr = timeStr;
         return _loc1_;
      }
      
      public function getCanGetTime(param1:int) : Boolean
      {
         return signedArr.length >= needTimes[param1];
      }
      
      public function getIsGetTimeAward(param1:int) : Boolean
      {
         return this["isGetGiftTime" + param1];
      }
      
      public function setIsGetTimeAward(param1:int) : void
      {
         this["isGetGiftTime" + param1] = true;
      }
      
      public function get currentGiftType() : int
      {
         return _antiwear._currentGiftType;
      }
      
      public function set currentGiftType(param1:int) : void
      {
         _antiwear._currentGiftType = param1;
      }
      
      public function get isGetGiftTime0() : Boolean
      {
         return _antiwear._isGetGiftTime0;
      }
      
      public function set isGetGiftTime0(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime0 = param1;
      }
      
      public function get isGetGiftTime1() : Boolean
      {
         return _antiwear._isGetGiftTime1;
      }
      
      public function set isGetGiftTime1(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime1 = param1;
      }
      
      public function get isGetGiftTime2() : Boolean
      {
         return _antiwear._isGetGiftTime2;
      }
      
      public function set isGetGiftTime2(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime2 = param1;
      }
      
      public function get isGetGiftTime3() : Boolean
      {
         return _antiwear._isGetGiftTime3;
      }
      
      public function set isGetGiftTime3(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime3 = param1;
      }
      
      public function get isGetGiftTime4() : Boolean
      {
         return _antiwear._isGetGiftTime4;
      }
      
      public function set isGetGiftTime4(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime4 = param1;
      }
      
      public function get isGetGiftTime5() : Boolean
      {
         return _antiwear._isGetGiftTime5;
      }
      
      public function set isGetGiftTime5(param1:Boolean) : void
      {
         _antiwear._isGetGiftTime5 = param1;
      }
      
      public function get timeStr() : String
      {
         return _antiwear._timeStr;
      }
      
      public function set timeStr(param1:String) : void
      {
         _antiwear._timeStr = param1;
      }
      
      public function get needTimes() : Array
      {
         return _antiwear._needTimes;
      }
      
      public function set needTimes(param1:Array) : void
      {
         _antiwear._needTimes = param1;
      }
   }
}

