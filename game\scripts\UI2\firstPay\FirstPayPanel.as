package UI2.firstPay
{
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.BuffData;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.TotalRechargeData;
   import YJFY.API_4399.PayAPI.DateInfor;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.GameEvent;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class FirstPayPanel extends MySprite
   {
      private var firstPayXml:XML;
      
      private var show:MovieClip;
      
      private var btnClose:ButtonLogicShell;
      
      private var btnPay:ButtonLogicShell;
      
      private var btnGetAward:ButtonLogicShell;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var equipments:Vector.<Equipment>;
      
      private var m_totalRechargeData:TotalRechargeData;
      
      private var m_payAPIListener:PayAPIListener;
      
      private var m_payAPI:PayAPI;
      
      private var isCallBack:int = 0;
      
      public var isOnceType:Boolean;
      
      public function FirstPayPanel()
      {
         super();
         this.name = "firstPayPanel";
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_totalRechargeData = new TotalRechargeData();
         m_payAPIListener = new PayAPIListener();
         m_payAPIListener.getRechargedMoneyErrorFun = getRechargedMoneyError;
         m_payAPIListener.getRechargedMoneySuccessFun = getRechargedMoneySuccess;
         m_payAPI = GamingUI.getInstance().getAPI4399().payAPI;
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         GameEvent.eventDispacher.removeEventListener("rechargeCallBack",rechargeCallBackHandler);
         m_payAPI.removePayAPIListener(m_payAPIListener);
         m_payAPI = null;
         ClearUtil.clearObject(m_totalRechargeData);
         m_totalRechargeData = null;
         ClearUtil.clearObject(m_payAPIListener);
         m_payAPIListener = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         btnClose.clear();
         btnClose = null;
         btnPay.clear();
         btnPay = null;
         btnGetAward.clear();
         btnGetAward = null;
      }
      
      private function initShow() : void
      {
         isOnceType = !FirstPayData.getInstance().isGetOnce;
         show = MyFunction2.returnShowByClassName("FirstPayPanel") as MovieClip;
         equipmentVOsData = new EquipmentVOsData();
         if(isOnceType)
         {
            initOnce();
         }
         else
         {
            initEveryDay();
         }
         this.addChild(show);
         btnClose = new ButtonLogicShell();
         btnClose.setShow(show["btnClose"]);
         btnPay = new ButtonLogicShell();
         btnPay.setShow(show["btnPay"]);
         btnGetAward = new ButtonLogicShell();
         btnGetAward.setShow(show["btnGetAward"]);
         resetShow();
         m_payAPI.addPayAPIListener(m_payAPIListener);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         GameEvent.eventDispacher.addEventListener("rechargeCallBack",rechargeCallBackHandler);
         getTotalRecharge();
      }
      
      private function rechargeCallBackHandler(param1:Event) : void
      {
         isCallBack++;
      }
      
      private function getTotalRecharge(param1:Event = null) : void
      {
         var _loc3_:TimeUtil = null;
         var _loc2_:DateInfor = null;
         if(isOnceType)
         {
            m_payAPI.getTotalRechargeFun(null);
         }
         else
         {
            _loc3_ = new TimeUtil();
            _loc2_ = new DateInfor(_loc3_.splitTimeString(TimeUtil.timeStr) + " 00:00:00",_loc3_.splitTimeString(TimeUtil.timeStr) + " 23:59:59");
            m_payAPI.getTotalRechargeFun(_loc2_);
         }
      }
      
      private function initOnce() : void
      {
         show.gotoAndStop("once");
         equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(firstPayXml.firstPayAward[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         showEq();
      }
      
      private function initEveryDay() : void
      {
         show.gotoAndStop("everday");
         equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(firstPayXml.everyDayAward[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         showEq();
      }
      
      private function showEq() : void
      {
         var _loc1_:Equipment = null;
         var _loc2_:int = 0;
         equipments = new Vector.<Equipment>();
         _loc2_ = 0;
         while(_loc2_ < equipmentVOsData.getEquipmentVONum())
         {
            _loc1_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData.getEquipmentVOByIndex(_loc2_));
            show["eqCell_" + _loc2_].addChild(_loc1_);
            equipments.push(_loc1_);
            _loc1_.addEventListener("rollOver",onOver2,false,0,true);
            _loc1_.addEventListener("rollOut",onOut2,false,0,true);
            _loc2_++;
         }
      }
      
      private function init() : void
      {
         MyFunction2.loadXMLFunction("firstPayAward",function(param1:XML):void
         {
            firstPayXml = param1;
            initShow();
         },null,true);
      }
      
      private function resetShow() : void
      {
         if(isOnceType)
         {
            if(FirstPayData.getInstance().isGetOnce)
            {
               btnGetAwardUnable();
            }
            else if(m_totalRechargeData.getTotalRecharge() > 0)
            {
               btnGetAwardAble();
            }
            else
            {
               btnGetAwardUnable();
            }
         }
         else if(FirstPayData.getInstance().isGetAwardToday)
         {
            btnGetAwardUnable();
         }
         else if(m_totalRechargeData.getTotalRecharge() >= int(firstPayXml.everyDayAward[0].@recharge))
         {
            btnGetAwardAble();
         }
         else
         {
            btnGetAwardUnable();
         }
      }
      
      public function btnGetAwardUnable() : void
      {
         MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
         (btnGetAward.getShow() as SimpleButton).mouseEnabled = false;
      }
      
      public function btnGetAwardAble() : void
      {
         MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),0);
         (btnGetAward.getShow() as SimpleButton).mouseEnabled = true;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case btnClose:
               clear();
               GamingUI.getInstance().clearFirstPay();
               break;
            case btnPay:
               AnalogServiceHoldFunction.getInstance().payMoney_As3();
               break;
            case btnGetAward:
               getReward();
         }
      }
      
      private function getReward() : void
      {
         btnGetAwardUnable();
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player1))
            {
               showWarningBox("获取礼包成功",0);
               if(isOnceType)
               {
                  FirstPayData.getInstance().isGetOnce = true;
               }
               else
               {
                  FirstPayData.getInstance().isGetAwardToday = true;
                  getBuffAward(param1);
               }
            }
            else
            {
               showWarningBox("背包已满, 不能获取礼包",0);
            }
            resetShow();
         },showWarningBox);
      }
      
      private function getBuffAward(param1:String) : void
      {
         var _loc3_:AllTimeBuffVO = XMLSingle.getBuff(10000,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc3_.startDate = param1;
         _loc3_.totalTime = AntiwearNumber.nums["consts_24"];
         _loc3_.remainTime = _loc3_.totalTime * AntiwearNumber.nums["consts_100"] * 36;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc3_),BuffData.getInstance().buffDrives);
         var _loc4_:AllTimeBuffVO = XMLSingle.getBuff(10002,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc4_.startDate = param1;
         _loc4_.totalTime = AntiwearNumber.nums["consts_3"];
         _loc4_.remainTime = _loc4_.totalTime * AntiwearNumber.nums["consts_100"] * 36;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc4_),BuffData.getInstance().buffDrives);
         var _loc2_:AllTimeBuffVO = XMLSingle.getBuff(10003,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc2_.startDate = new TimeUtil().transformTimeToZero(param1);
         _loc2_.totalTime = AntiwearNumber.nums["consts_24"];
         _loc2_.remainTime = _loc2_.totalTime * AntiwearNumber.nums["consts_100"] * 36 - new TimeUtil().timeIntervalBySecond(_loc2_.startDate,param1);
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc2_),BuffData.getInstance().buffDrives);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function getRechargedMoneySuccess(param1:int) : void
      {
         if(isCallBack > 0)
         {
            isCallBack--;
            if(isCallBack == 0)
            {
               getTotalRecharge();
            }
            return;
         }
         m_totalRechargeData.setTotalRecharge(param1);
         resetShow();
      }
      
      private function getRechargedMoneyError() : void
      {
         if(isCallBack > 0)
         {
            isCallBack--;
            if(isCallBack == 0)
            {
               getTotalRecharge();
            }
            return;
         }
         showWarningBox("获取累积充值失败",0);
         m_totalRechargeData.setTotalRecharge(0);
         resetShow();
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

