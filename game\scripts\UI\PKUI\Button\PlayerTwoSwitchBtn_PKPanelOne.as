package UI.PKUI.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class PlayerTwoSwitchBtn_PKPanelOne extends SwitchBtn
   {
      public function PlayerTwoSwitchBtn_PKPanelOne()
      {
         super();
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchPlayerInPKPanelOne"));
      }
   }
}

