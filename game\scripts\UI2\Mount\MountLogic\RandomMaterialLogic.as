package UI2.Mount.MountLogic
{
   import UI2.Mount.MountData.MountSystemData.MaterialData;
   import UI2.Mount.MountData.MountSystemData.MountSystemData;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.Lottery.LotteryData;
   import YJFY.Lottery.LotteryDataOne;
   import YJFY.Utils.ClearUtil;
   
   public class RandomMaterialLogic
   {
      private var m_materialFilter:MaterialFilter;
      
      public function RandomMaterialLogic()
      {
         super();
         m_materialFilter = new MaterialFilter();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_materialFilter);
         m_materialFilter = null;
      }
      
      public function randomOneMaterial(param1:MountsVO, param2:MountSystemData, param3:RandomMaterialReturnData) : void
      {
         m_materialFilter.init(param1,param2);
         var _loc6_:LotteryData = param2.getRandomDataByIndex(0);
         var _loc4_:LotteryDataOne = _loc6_.randomOneLotteryDataOne2(m_materialFilter);
         var _loc5_:MaterialData = param2.getMaterialDataById(_loc4_.getEquipmentId());
         addMaterialDataToMonntsVO(param1,_loc5_,_loc4_.getEquipmentNum(),param3);
         var _loc7_:Vector.<MaterialData> = new Vector.<MaterialData>(1);
         var _loc8_:Vector.<uint> = new Vector.<uint>(1);
         _loc7_[0] = _loc5_;
         _loc8_[0] = _loc4_.getEquipmentNum();
         param3.resultMaterialDatas = _loc7_;
         param3.resultMateiralDataNums = _loc8_;
      }
      
      public function randomMultiMaterial(param1:MountsVO, param2:MountSystemData, param3:RandomMaterialReturnData) : void
      {
         var _loc4_:MaterialData = null;
         var _loc10_:* = undefined;
         var _loc9_:* = undefined;
         var _loc11_:int = 0;
         var _loc8_:int = 0;
         m_materialFilter.init(param1,param2);
         var _loc7_:LotteryData = param2.getRandomDataByIndex(1);
         var _loc5_:LotteryDataOne = _loc7_.randomOneLotteryDataOne2(m_materialFilter);
         if(_loc5_)
         {
            _loc4_ = param2.getMaterialDataById(_loc5_.getEquipmentId());
            addMaterialDataToMonntsVO(param1,_loc4_,_loc5_.getEquipmentNum(),param3);
            _loc10_ = new Vector.<MaterialData>(1);
            _loc9_ = new Vector.<uint>(1);
            _loc10_[0] = _loc4_;
            _loc9_[0] = _loc5_.getEquipmentNum();
         }
         else
         {
            _loc10_ = new Vector.<MaterialData>();
            _loc9_ = new Vector.<uint>();
         }
         if(_loc5_)
         {
            _loc8_ = 9;
         }
         else
         {
            _loc8_ = 10;
         }
         var _loc6_:LotteryData = param2.getRandomDataByIndex(0);
         _loc11_ = 0;
         while(_loc11_ < _loc8_)
         {
            _loc5_ = _loc6_.randomOneLotteryDataOne2(m_materialFilter);
            _loc4_ = param2.getMaterialDataById(_loc5_.getEquipmentId());
            _loc10_.push(_loc4_);
            _loc9_.push(_loc5_.getEquipmentNum());
            addMaterialDataToMonntsVO(param1,_loc4_,_loc5_.getEquipmentNum(),param3);
            _loc11_++;
         }
         param3.resultMaterialDatas = _loc10_;
         param3.resultMateiralDataNums = _loc9_;
      }
      
      private function addMaterialDataToMonntsVO(param1:MountsVO, param2:MaterialData, param3:uint, param4:RandomMaterialReturnData) : void
      {
         var _loc7_:MountVO = null;
         var _loc6_:* = 0;
         var _loc5_:* = undefined;
         var _loc8_:* = undefined;
         if(param2.getType() == "powerChip")
         {
            _loc7_ = param1.getMountVOById(param2.getMountId());
            if(param1 == null)
            {
               throw new Error("出现未知的mount id");
            }
            _loc6_ = _loc7_.getLevel1();
            _loc7_.addPowerChipNum(param2.getNum() * param3);
            if(param4.newGetMountVOs != null)
            {
               _loc5_ = param4.newGetMountVOs;
            }
            if(param4.upLevelMountVOs != null)
            {
               _loc8_ = param4.upLevelMountVOs;
            }
            if(_loc7_.getLevel1() > 0 && _loc6_ == 0)
            {
               if(_loc5_ == null)
               {
                  _loc5_ = new Vector.<MountVO>();
               }
               _loc5_.push(_loc7_);
            }
            if(_loc7_.getLevel1() > _loc6_)
            {
               if(_loc8_ == null)
               {
                  _loc8_ = new Vector.<MountVO>();
               }
               _loc8_.push(_loc7_);
            }
            param4.newGetMountVOs = _loc5_;
            param4.upLevelMountVOs = _loc8_;
         }
         else
         {
            if(param2.getType() != "strengthen")
            {
               throw new Error();
            }
            param1.addStrengthenPointNum(param2.getNum() * param3);
         }
      }
   }
}

