package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_AddConValue extends InformationBodyDetail
   {
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_id_conValue:int;
      
      public function UP_AddConValue()
      {
         super();
         m_informationBodyId = 3027;
      }
      
      public function initData(param1:Number, param2:int, param3:int) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_id_conValue = param3;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_id_conValue);
         return _loc1_;
      }
      
      public function getId_conValue() : int
      {
         return m_id_conValue;
      }
   }
}

