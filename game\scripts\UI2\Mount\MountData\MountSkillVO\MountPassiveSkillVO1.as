package UI2.Mount.MountData.MountSkillVO
{
   import UI2.Mount.MountData.MountVO;
   
   public class MountPassiveSkillVO1 extends MountSkillVO implements IMountPassiveSkillVO
   {
      protected var m_mountVO:MountVO;
      
      protected var m_description2:String;
      
      public function MountPassiveSkillVO1()
      {
         super();
      }
      
      override public function clear() : void
      {
         setMountVO(null);
         m_mountVO = null;
         m_description2 = null;
         super.clear();
      }
      
      public function setMountVO(param1:MountVO) : void
      {
         m_mountVO = param1;
      }
      
      public function getDescription2() : String
      {
         return description2;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         super.initSkillLevelData(param1);
         this.description2 = String(param1.data.(@att == "description2")[0].@value);
      }
      
      private function get description2() : String
      {
         return m_description2;
      }
      
      private function set description2(param1:String) : void
      {
         m_description2 = param1;
      }
   }
}

