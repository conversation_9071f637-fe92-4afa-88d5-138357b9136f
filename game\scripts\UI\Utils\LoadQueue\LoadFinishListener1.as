package UI.Utils.LoadQueue
{
   import YJFY.Utils.ClearUtil;
   
   public class LoadFinishListener1 implements ILoadFinishListener
   {
      private var m_loadFinishFun:Function;
      
      private var m_loadFinishFunParams:Array;
      
      public function LoadFinishListener1(param1:Function, param2:Array)
      {
         super();
         m_loadFinishFun = param1;
         m_loadFinishFunParams = param2;
      }
      
      public function clear() : void
      {
         m_loadFinishFun = null;
         ClearUtil.nullArr(m_loadFinishFunParams,false,false,false);
         m_loadFinishFunParams = null;
      }
      
      public function loadFinish() : void
      {
         if(Boolean(m_loadFinishFun))
         {
            m_loadFinishFun.apply(null,m_loadFinishFunParams);
         }
         clear();
      }
   }
}

