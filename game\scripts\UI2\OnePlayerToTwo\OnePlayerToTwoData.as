package UI2.OnePlayerToTwo
{
   import UI.DataManagerParent;
   
   public class OnePlayerToTwoData extends DataManagerParent
   {
      private var m_needTicketPrice:uint;
      
      private var m_needTicketId:String;
      
      private var m_minLevel:uint;
      
      public function OnePlayerToTwoData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.needTicketPrice = param1.@needTicketPrice;
         this.needTicketId = param1.@needTicketId;
         this.minLevel = param1.@minLevel;
      }
      
      public function getNeedTicketPrice() : uint
      {
         return needTicketPrice;
      }
      
      public function getNeedTiccketId() : String
      {
         return needTicketId;
      }
      
      public function getMinLevel() : uint
      {
         return minLevel;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.needTicketPrice = m_needTicketPrice;
         _antiwear.needTicketId = m_needTicketId;
         _antiwear.minLevel = m_minLevel;
      }
      
      private function get needTicketPrice() : uint
      {
         return _antiwear.needTicketPrice;
      }
      
      private function set needTicketPrice(param1:uint) : void
      {
         _antiwear.needTicketPrice = param1;
      }
      
      private function get needTicketId() : String
      {
         return _antiwear.needTicketId;
      }
      
      private function set needTicketId(param1:String) : void
      {
         _antiwear.needTicketId = param1;
      }
      
      private function get minLevel() : uint
      {
         return _antiwear.minLevel;
      }
      
      private function set minLevel(param1:uint) : void
      {
         _antiwear.minLevel = param1;
      }
   }
}

