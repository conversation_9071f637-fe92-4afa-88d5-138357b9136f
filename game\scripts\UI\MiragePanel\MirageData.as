package UI.MiragePanel
{
   import UI.DataManagerParent;
   
   public class MirageD<PERSON> extends DataManagerParent
   {
      public static var _instance:MirageData = null;
      
      private var _currentMirageNum:int;
      
      private var _mirageDate:String;
      
      public function MirageData()
      {
         if(!_instance)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了么？！");
      }
      
      public static function getInstance() : MirageData
      {
         if(!_instance)
         {
            _instance = new MirageData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         _mirageDate = null;
         _instance = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currentMirageNum = _currentMirageNum;
         _antiwear.mirageDate = _mirageDate;
      }
      
      public function get currentMirageNum() : int
      {
         return _antiwear.currentMirageNum;
      }
      
      public function set currentMirageNum(param1:int) : void
      {
         _antiwear.currentMirageNum = param1;
      }
      
      public function get mirageDate() : String
      {
         return _antiwear.mirageDate;
      }
      
      public function set mirageDate(param1:String) : void
      {
         _antiwear.mirageDate = param1;
      }
   }
}

