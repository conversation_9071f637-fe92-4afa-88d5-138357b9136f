package UI.ExchangeGiftBag
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   
   public class ExchangeGiftData extends DataManagerParent
   {
      private static var _instance:ExchangeGiftData = null;
      
      public var exchangeGiftNames:Vector.<String> = new Vector.<String>();
      
      public function ExchangeGiftData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : ExchangeGiftData
      {
         if(!_instance)
         {
            _instance = new ExchangeGiftData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(exchangeGiftNames);
         exchangeGiftNames = null;
         _instance = null;
         super.clear();
      }
   }
}

