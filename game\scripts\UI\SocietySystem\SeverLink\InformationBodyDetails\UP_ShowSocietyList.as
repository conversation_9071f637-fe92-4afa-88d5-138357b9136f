package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class UP_ShowSocietyList extends InformationBodyDetail
   {
      private var m_pageIndex:int;
      
      private var m_num:int;
      
      public function UP_ShowSocietyList()
      {
         super();
         m_informationBodyId = 3004;
      }
      
      public function initData(param1:int, param2:int) : void
      {
         m_pageIndex = param1;
         m_num = param2;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_pageIndex + 1);
         _loc1_.writeInt(m_num);
         return _loc1_;
      }
      
      public function getPageIndex() : int
      {
         return m_pageIndex;
      }
      
      public function getNum() : int
      {
         return m_num;
      }
   }
}

