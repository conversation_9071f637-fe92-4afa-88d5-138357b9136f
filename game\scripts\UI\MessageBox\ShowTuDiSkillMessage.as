package UI.MessageBox
{
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.ShiTu.TuDiSkillVO;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class ShowTuDiSkillMessage
   {
      public function ShowTuDiSkillMessage()
      {
         super();
      }
      
      public function showMessage(param1:TuDiSkillVO, param2:Player, param3:MessageBox) : void
      {
         var _loc8_:String = null;
         var _loc5_:String = null;
         var _loc6_:String = null;
         var _loc4_:MovieClip = null;
         var _loc7_:Sprite = null;
         if(param1)
         {
            _loc8_ = "";
            _loc5_ = "";
            _loc8_ += MessageBoxFunction.getInstance().toHTMLText(param1.name,16);
            _loc6_ = "";
            if(param1.attackMutiple)
            {
               _loc6_ += "" + param1.attackMutiple.toFixed(2) + "×攻击力";
               if(param1.attackAdd)
               {
                  _loc6_ += " + " + param1.attackAdd.toFixed(2);
               }
            }
            else if(param1.attackAdd)
            {
               _loc6_ += "" + param1.attackAdd.toFixed(2);
            }
            else
            {
               _loc6_ += "0";
            }
            _loc5_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(param1.level.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("伤害：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc6_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("所需能量：",16) + MessageBoxFunction.getInstance().toHTMLText(param1.requireEnergy.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(param1.description,14);
            param3.boxWidth = 300;
            _loc4_ = MyFunction2.returnShowByClassName(param1.className) as MovieClip;
            _loc4_.gotoAndStop(0);
            _loc7_ = new Sprite();
            _loc4_.x = -_loc4_.width / 2;
            _loc4_.y = -_loc4_.height / 2;
            _loc7_.addChild(_loc4_);
            param3.addSprite(_loc7_);
            param3.htmlText = _loc8_ + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + _loc5_;
         }
      }
   }
}

