package UI.Button
{
   import UI.Shop.TehuiView;
   import flash.events.MouseEvent;
   
   public class TehuiBtnOkNo extends SureAndCancelBtn
   {
      private var box:TehuiView;
      
      public var type:int;
      
      public function TehuiBtnOkNo()
      {
         super();
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         if(type == 1)
         {
            if(<PERSON><PERSON><PERSON>(box.okFun))
            {
               box.okFun();
            }
         }
         else if(type == 2)
         {
            if(<PERSON><PERSON><PERSON>(box.noFun))
            {
               box.noFun();
            }
         }
      }
      
      public function setBtnControl(param1:TehuiView) : void
      {
         box = param1;
      }
   }
}

