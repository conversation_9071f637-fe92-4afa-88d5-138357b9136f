package UI.Pets
{
   import UI.CellBorderSingle;
   import UI.Event.UIPassiveEvent;
   import UI.SkillCells.SkillCell;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class PetSkillCell extends SkillCell
   {
      public var equipbackground:IEquipmentCellBackground;
      
      public function PetSkillCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
         super.rollOut(param1);
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      override protected function rollOver(param1:MouseEvent) : void
      {
         super.rollOver(param1);
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":child}));
         }
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.visible == true)
         {
            CellBorderSingle.getInstance().x = -0.55;
            CellBorderSingle.getInstance().y = -1.8;
            CellBorderSingle.getInstance().width = 47.25;
            CellBorderSingle.getInstance().height = 46.25;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return equipbackground.getRect(param1);
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("mouseDown",mouseDown,true,0,true);
         addEventListener("click",click,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("mouseDown",mouseDown,true);
         removeEventListener("click",click,false);
      }
      
      protected function mouseDown(param1:MouseEvent) : void
      {
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      protected function click(param1:MouseEvent) : void
      {
         if(isHaveChild)
         {
            dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

