package UI.Task.TaskReward
{
   public class TaskRewardVO_Experience extends TaskRewardVO
   {
      private var _value:int;
      
      public function TaskRewardVO_Experience()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.value = _value;
      }
      
      public function get value() : int
      {
         return _antiwear.value;
      }
      
      public function set value(param1:int) : void
      {
         _antiwear.value = param1;
      }
   }
}

