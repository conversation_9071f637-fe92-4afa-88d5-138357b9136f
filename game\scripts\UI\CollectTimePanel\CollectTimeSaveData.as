package UI.CollectTimePanel
{
   import UI.DataManagerParent;
   
   public class CollectTimeSaveData extends DataManagerParent
   {
      private static var m_instance:CollectTimeSaveData;
      
      private var m_startTime:String;
      
      public function CollectTimeSaveData()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            return;
         }
         throw new Error("实例已经存在");
      }
      
      public static function getInstance() : CollectTimeSaveData
      {
         if(m_instance == null)
         {
            m_instance = new CollectTimeSaveData();
         }
         return m_instance;
      }
      
      override public function clear() : void
      {
         m_startTime = null;
         m_instance = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startTime = m_startTime = "";
      }
      
      public function initFromSaveXML(param1:XML, param2:String) : void
      {
         if(param1.hasOwnProperty("CollectTime"))
         {
            this.startTime = String(param1.CollectTime[0].@startTime);
         }
         else
         {
            this.startTime = "";
         }
      }
      
      public function getStartTime() : String
      {
         return startTime;
      }
      
      public function reset(param1:String) : void
      {
         this.startTime = param1;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
   }
}

