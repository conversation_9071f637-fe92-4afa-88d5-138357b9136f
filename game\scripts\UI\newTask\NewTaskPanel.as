package UI.newTask
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.LimitingTimeTaskVO;
   import UI.Task.TaskVO.LimtingTimeAccumulatedTaskVO;
   import UI.XMLSingle;
   import UI.newGuide.NewGuideData;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import UI.newTask.EveyDayTask.NewEveryDayPanel;
   import UI.newTask.NewActivityTask.MewActivityData;
   import UI.newTask.NewActivityTask.NewActivityPanel;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import UI.newTask.NewMainTask.NewMainTaskPanel;
   import YJFY.GameEvent;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   
   public class NewTaskPanel extends MovieClip
   {
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_nocheck:ButtonLogicShell2;
      
      private var m_check:ButtonLogicShell2;
      
      private var m_mainTask:MySwitchBtnLogicShell;
      
      private var m_dayTask:MySwitchBtnLogicShell;
      
      private var m_activiyTask:MySwitchBtnLogicShell;
      
      private var m_banghuiTask:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_mainNumTxt:TextField;
      
      private var m_dayNumTxt:TextField;
      
      private var m_activityNumTxt:TextField;
      
      private var m_myloader:YJFYLoader;
      
      private var m_show:MovieClip;
      
      private var m_ShowItem:MovieClip;
      
      private var m_bgmc:MovieClip;
      
      public var currentType:int;
      
      public var currentName:String;
      
      private var m_sourceNum:int = 0;
      
      private var m_titlemc:MovieClip;
      
      private var m_time:String;
      
      private var m_btnGroup_1:MovieClip;
      
      private var m_btnGroup_2:MovieClip;
      
      private var m_btnGroup_3:MovieClip;
      
      private var m_newmaintaskpanel:NewMainTaskPanel;
      
      private var m_neweverydaypanel:NewEveryDayPanel;
      
      private var m_newactivitypanel:NewActivityPanel;
      
      public function NewTaskPanel(param1:int, param2:String)
      {
         super();
         runTaskDetectorsInTaskList(null);
         GameEvent.eventDispacher.addEventListener("refreshtip",callrefreshTip);
         currentType = param1;
         currentName = param2;
         Part1.getInstance().showGameWaitShow();
         initParams();
         loadSource();
      }
      
      private function initParams() : void
      {
         m_sourceNum = 0;
         addEventListener("clickButton",clickButton,true,0,true);
         m_nocheck = new ButtonLogicShell2();
         m_check = new ButtonLogicShell2();
         m_quitBtn = new ButtonLogicShell2();
         m_mainTask = new MySwitchBtnLogicShell();
         m_dayTask = new MySwitchBtnLogicShell();
         m_activiyTask = new MySwitchBtnLogicShell();
         m_banghuiTask = new MySwitchBtnLogicShell();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtnGroup.addSwitchBtn(m_mainTask);
         m_switchBtnGroup.addSwitchBtn(m_dayTask);
         m_switchBtnGroup.addSwitchBtn(m_activiyTask);
         m_switchBtnGroup.addSwitchBtn(m_banghuiTask);
         m_banghuiTask.setClickEnble(false);
      }
      
      private function runTaskDetectorsInTaskList(param1:Event) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:MainLineTaskVO = NewMainTaskData.getInstance().currentTask;
         if(Boolean(NewMainTaskData.getInstance().currentTask) && Boolean(NewMainTaskData.getInstance().currentTask.taskDetectors) && NewMainTaskData.getInstance().currentTask.taskDetectors.length)
         {
            _loc3_ = int(NewMainTaskData.getInstance().currentTask.taskDetectors.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               NewMainTaskData.getInstance().currentTask.taskDetectors[_loc4_].detect();
               _loc4_++;
            }
         }
      }
      
      private function loadSource() : void
      {
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myloader.getClass("UISprite2/newTask.swf","taskpanel",getShowSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","taskitem",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","OtherEqArea",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","EqC",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","ChoiceEqCS",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","ChoiceEqArea",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.getClass("UISprite2/newTask.swf","ChoiceGetRewardPlayerPanel",getItemSuccess,getFail,null,null,null,null,false);
         m_myloader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_sourceNum++;
      }
      
      private function getXMLSuccess2(param1:YJFYLoaderData) : void
      {
         var data:XML;
         var loaderData:YJFYLoaderData = param1;
         m_sourceNum++;
         data = XML(loaderData.resultXML);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_time = param1;
            MewActivityData.getInstance().initXML(data,param1);
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      public function CloseUI(param1:MouseEvent = null) : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         GamingUI.getInstance().closeNewTask();
      }
      
      public function clear() : void
      {
         GameEvent.eventDispacher.removeEventListener("refreshtip",callrefreshTip);
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_nocheck);
         m_nocheck = null;
         ClearUtil.clearObject(m_check);
         m_check = null;
         ClearUtil.clearObject(m_ShowItem);
         m_ShowItem = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_mainTask);
         m_mainTask = null;
         ClearUtil.clearObject(m_dayTask);
         m_dayTask = null;
         ClearUtil.clearObject(m_activiyTask);
         m_activiyTask = null;
         ClearUtil.clearObject(m_banghuiTask);
         m_banghuiTask = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         if(currentType == 1)
         {
            ClearUtil.clearObject(m_neweverydaypanel);
            m_neweverydaypanel = null;
            ClearUtil.clearObject(m_newactivitypanel);
            m_newactivitypanel = null;
            ClearUtil.clearObject(m_newmaintaskpanel);
            m_newmaintaskpanel = null;
         }
         else if(currentType == 2)
         {
            ClearUtil.clearObject(m_newmaintaskpanel);
            m_newmaintaskpanel = null;
            ClearUtil.clearObject(m_newactivitypanel);
            m_newactivitypanel = null;
            ClearUtil.clearObject(m_neweverydaypanel);
            m_neweverydaypanel = null;
         }
         else if(currentType == 3)
         {
            ClearUtil.clearObject(m_newmaintaskpanel);
            m_newmaintaskpanel = null;
            ClearUtil.clearObject(m_neweverydaypanel);
            m_neweverydaypanel = null;
            ClearUtil.clearObject(m_newactivitypanel);
            m_newactivitypanel = null;
         }
      }
      
      public function callrefreshTip(param1:Event) : void
      {
         callmain();
         calleveryday();
         callactivity();
      }
      
      private function callmain() : void
      {
         var _loc3_:MainLineTaskVO = null;
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc9_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:Boolean = false;
         _loc9_ = 0;
         while(_loc9_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs.length)
         {
            if(NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].isGotReward == false && NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].isWorking)
            {
               _loc3_ = NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_];
               _loc2_ = 0;
               _loc8_ = 0;
               while(_loc8_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getCurrentTaskGoalNum())
               {
                  _loc2_ += NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getCurrentTaskGoalByIndex(_loc8_).num;
                  _loc8_++;
               }
               _loc1_ = 0;
               _loc6_ = 0;
               while(_loc6_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getNeedTaskGoalNum())
               {
                  _loc1_ += NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getNeedTaskGoalByIndex(_loc6_).num;
                  _loc6_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc4_ = true;
                  _loc5_++;
               }
            }
            _loc9_++;
         }
         if(_loc4_)
         {
            if(m_mainTask && m_mainTask.getShow())
            {
               (m_mainTask.getShow()["mctip"] as MovieClip).visible = true;
               m_mainNumTxt.text = String(_loc5_);
            }
         }
         else if(m_mainTask && m_mainTask.getShow())
         {
            (m_mainTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
      
      private function calleveryday() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         NewEveryDayData.getInstance().refreshData();
         _loc6_ = 0;
         while(_loc6_ < NewEveryDayData.getInstance().everyDayTaskVOs.length)
         {
            if(NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_ids.length)
               {
                  _loc2_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                  _loc1_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_];
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc3_ = true;
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            if(m_dayTask && m_dayTask.getShow())
            {
               (m_dayTask.getShow()["mctip"] as MovieClip).visible = true;
               m_dayNumTxt.text = String(_loc4_);
            }
         }
         else if(m_dayTask && m_dayTask.getShow())
         {
            (m_dayTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
      
      private function callactivity() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         MewActivityData.getInstance().refreshData();
         _loc6_ = 0;
         while(_loc6_ < MewActivityData.getInstance().everyDayTaskVOs.length)
         {
            if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_ids.length)
               {
                  if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is LimitingTimeTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_];
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is AccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc6_] as AccumulatedTaskVO).taskCount;
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is LimtingTimeAccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc6_] as LimtingTimeAccumulatedTaskVO).taskCount;
                  }
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc3_ = true;
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            if(m_activiyTask && m_activiyTask.getShow())
            {
               (m_activiyTask.getShow()["mctip"] as MovieClip).visible = true;
               m_activityNumTxt.text = String(_loc4_);
            }
         }
         else if(m_activiyTask && m_activiyTask.getShow())
         {
            (m_activiyTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
      
      private function getItemSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_ShowItem = new _loc2_();
         m_sourceNum++;
         if(m_sourceNum >= 7)
         {
            initShow();
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var loaderData:YJFYLoaderData = param1;
         var cla:Class = loaderData.resultClass;
         m_show = new cla();
         this.addChild(m_show);
         Part1.getInstance().hideGameWaitShow();
         m_sourceNum++;
         if(m_sourceNum >= 7)
         {
            initShow();
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_time = param1;
            NewEveryDayData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
            MewActivityData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("hidetip"));
         },function():void
         {
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      private function initShow() : void
      {
         var _loc1_:Array = null;
         m_btnGroup_1 = m_show["btnshell_1"];
         m_btnGroup_2 = m_show["btnshell_2"];
         m_btnGroup_3 = m_show["btnshell_3"];
         m_titlemc = m_show["titlemc"] as MovieClip;
         m_titlemc.visible = false;
         m_bgmc = m_show["bgmc"] as MovieClip;
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_nocheck.setShow(m_show["tipframebtn"]["btnnocheck"]);
         m_check.setShow(m_show["tipframebtn"]["btncheck"]);
         m_mainTask.setShow(m_show["maintaskbtn"]);
         m_dayTask.setShow(m_show["daytaskbtn"]);
         m_activiyTask.setShow(m_show["activitytaskbtn"]);
         m_banghuiTask.setShow(m_show["societytaskbtn"]);
         m_mainNumTxt = (m_mainTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         m_dayNumTxt = (m_dayTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         m_activityNumTxt = (m_activiyTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_mainNumTxt,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_dayNumTxt,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_activityNumTxt,true);
         _loc1_ = [new GlowFilter(0,1,2,2,10,1,false,false)];
         m_mainNumTxt.filters = _loc1_;
         m_dayNumTxt.filters = _loc1_;
         m_activityNumTxt.filters = _loc1_;
         if(m_newmaintaskpanel == null)
         {
            m_newmaintaskpanel = new NewMainTaskPanel();
            m_newmaintaskpanel.init(this,m_show);
         }
         if(m_neweverydaypanel == null)
         {
            m_neweverydaypanel = new NewEveryDayPanel();
            m_neweverydaypanel.init(this,m_show);
         }
         if(m_newactivitypanel == null)
         {
            m_newactivitypanel = new NewActivityPanel();
            m_newactivitypanel.init(this,m_show);
         }
         if(currentType == 1)
         {
            m_mainTask.turnActivate();
            showByType(1);
         }
         else if(currentType == 2)
         {
            m_dayTask.turnActivate();
            showByType(2);
         }
         else if(currentType == 3)
         {
            m_activiyTask.turnActivate();
            showByType(3);
         }
         else
         {
            showByType(1);
            m_switchBtnGroup.addEnd();
         }
         openByIndex(currentType,currentName);
         refreshTipFrame();
      }
      
      public function hideAll() : void
      {
         currentName = null;
         m_newmaintaskpanel.hide();
         m_neweverydaypanel.hide();
         m_newactivitypanel.hide();
      }
      
      private function showByType(param1:int) : void
      {
         if(param1 == 1)
         {
            m_btnGroup_1.visible = true;
            m_btnGroup_2.visible = false;
            m_btnGroup_3.visible = false;
         }
         else if(param1 == 2)
         {
            m_btnGroup_1.visible = false;
            m_btnGroup_2.visible = true;
            m_btnGroup_3.visible = false;
         }
         else if(param1 == 3)
         {
            m_btnGroup_1.visible = false;
            m_btnGroup_2.visible = false;
            m_btnGroup_3.visible = true;
         }
         else
         {
            m_btnGroup_1.visible = true;
            m_btnGroup_2.visible = false;
            m_btnGroup_3.visible = false;
         }
      }
      
      public function openByIndex(param1:int, param2:String) : void
      {
         m_titlemc.gotoAndStop(String(param1));
         hideAll();
         if(param1 == 1)
         {
            m_newmaintaskpanel.show(param2);
            m_bgmc.gotoAndStop("2");
            showByType(1);
         }
         else if(param1 == 2)
         {
            m_neweverydaypanel.show(param2);
            m_bgmc.gotoAndStop("1");
            showByType(2);
         }
         else if(param1 == 3)
         {
            m_newactivitypanel.show(param2);
            m_bgmc.gotoAndStop("1");
            showByType(3);
         }
         callrefreshTip(null);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_mainTask:
               if(currentType != 1)
               {
                  currentType = 1;
                  openByIndex(1,null);
               }
               break;
            case m_dayTask:
               if(currentType != 2)
               {
                  currentType = 2;
                  openByIndex(2,null);
               }
               break;
            case m_activiyTask:
               if(currentType != 3)
               {
                  currentType = 3;
                  openByIndex(3,null);
               }
               break;
            case m_banghuiTask:
               break;
            case m_nocheck:
               NewGuideData.getInstance().isShow = true;
               GameEvent.eventDispacher.dispatchEvent(new GameEvent("hidetip"));
               refreshTipFrame();
               break;
            case m_check:
               NewGuideData.getInstance().isShow = false;
               GameEvent.eventDispacher.dispatchEvent(new GameEvent("hidetip"));
               refreshTipFrame();
               break;
            default:
               break;
            case m_quitBtn:
               CloseUI();
               return;
         }
      }
      
      public function refreshTipFrame() : void
      {
         if(NewGuideData.getInstance().isShow)
         {
            m_check.getShow().visible = true;
            m_check.unLock();
            m_nocheck.getShow().visible = false;
            m_nocheck.lock();
         }
         else
         {
            m_check.getShow().visible = false;
            m_check.lock();
            m_nocheck.getShow().visible = true;
            m_nocheck.unLock();
         }
      }
   }
}

