package UI.EquipmentCells
{
   import UI.EquipmentCell;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   
   public class ShopEquipmentCell extends EquipmentCell
   {
      public var equipbackground:IEquipmentCellBackground;
      
      public function ShopEquipmentCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
      }
   }
}

