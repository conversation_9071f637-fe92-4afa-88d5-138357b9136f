package UI.SocietySystem.MySocietyPanel
{
   import UI.DirtyWordFilter.DirtyWordFilterEngine;
   import UI.EnterFrameTime;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.SocietySystem.MemberListPanel.MemberListPanel;
   import UI.SocietySystem.SeverLink.InformationBody;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_AddConValueReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_ChangeAnnouncementReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_DecConValueReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_HaveNewMember;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerListOfApply;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_RemoveMemberReturnToLeader;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import UI.SocietySystem.SeverLink.SocketListener;
   import UI.SocietySystem.SeverLink.SocketManager;
   import UI.SocietySystem.SocialChatPanel.NewGotChatInfor;
   import UI.SocietySystem.SocialChatPanel.SocietyChatPanel;
   import UI.SocietySystem.SocietyContribution.SocietyContributionPanel;
   import UI.SocietySystem.SocietyDataVO;
   import UI.SocietySystem.SocietyShop.BuyPopUpBox;
   import UI.SocietySystem.SocietyShop.BuyingGoodData;
   import UI.SocietySystem.SocietyShop.GoodData;
   import UI.SocietySystem.SocietyShop.OneEqSaveDataInSocietyShop;
   import UI.SocietySystem.SocietyShop.SocietyShop;
   import UI.SocietySystem.SocietySign.SocietySignPanel;
   import UI.SocietySystem.SocietySystem;
   import UI.SocietySystem.SocietyUtil.SocietyLevelData;
   import UI.VersionControl;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class MySocietyPanel extends MySprite
   {
      private const m_const_signAddConValue:String = "signAddConValue";
      
      private const m_const_contriOfExp:String = "contriOfExp";
      
      private const m_const_contriOfMoney:String = "contriOfMoney";
      
      private const m_const_contriOfTicket:String = "contriOfTicket";
      
      private const m_const_buyGood:String = "buyGood";
      
      private var m_clear:ClearHelper;
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_socketListener:SocketListener;
      
      private var m_quiBtn:ButtonLogicShell2;
      
      private var m_playerListOfApplyPanel:PlayerListOfApplayPanel;
      
      private var m_mySocietyNameText:TextField;
      
      private var m_mySocietyLeadernameText:TextField;
      
      private var m_mySocietyLevelText:TextField;
      
      private var m_mySocietyRankText:TextField;
      
      private var m_memberNumText:TextField;
      
      private var m_maxMemberNumText:TextField;
      
      private var m_societyConValueTextField:TextField;
      
      private var m_societyConValuePerLevelTextField:TextField;
      
      private var m_personalReConValueText:TextField;
      
      private var m_personalTotalConValueText:TextField;
      
      private var m_dissolveTimeShow:Sprite;
      
      private var m_dayShow:MultiPlaceNumLogicShell;
      
      private var m_hourShow:MultiPlaceNumLogicShell;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_quitSocietyBtn:ButtonLogicShell2;
      
      private var m_dissolveAndCancelSocietyShow:MovieClipPlayLogicShell;
      
      private var m_cancelDissolveSocietyBtn:ButtonLogicShell2;
      
      private var m_dissolveSocietyBtn:ButtonLogicShell2;
      
      private var m_solveApplyBtn:ButtonLogicShell2;
      
      private var m_changeAnnouncementBtn:ButtonLogicShell2;
      
      private var m_announcementText:TextField;
      
      private var m_mySocietyFunArea:MySocietyFunArea;
      
      private var m_createAnnouncementPanel:CreateAnnouncementPanel;
      
      private var m_otherOperationPanel:OtherOperationPanel;
      
      private var m_societySignPanel:SocietySignPanel;
      
      private var m_societyContriPanel:SocietyContributionPanel;
      
      private var m_societyShopPanel:SocietyShop;
      
      private var m_societyShopBuyPopUpBox:BuyPopUpBox;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_myIsLeader:Boolean;
      
      private var m_currentChangeConValueState:String;
      
      private var m_playerDatas:PlayerDatas;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_dirtyWorldFilterEngine:DirtyWordFilterEngine;
      
      private var m_buyingGoodData:BuyingGoodData;
      
      private var m_newAnnoument:String;
      
      private var m_pageIndex_gettingMemberList:int;
      
      private var m_num_gettingMemberList:int;
      
      private var m_societySystem:SocietySystem;
      
      private var m_socketManager:SocketManager;
      
      private var m_gamingUI:GamingUI;
      
      private var m_societySystemXML:XML;
      
      private var m_versionControl:VersionControl;
      
      private var m_playerListOfApply:DOWN_PlayerListOfApply;
      
      private var m_dissolveTime:Number;
      
      public function MySocietyPanel()
      {
         super();
         m_clear = new ClearHelper();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         if(m_clear)
         {
            m_clear.clear();
         }
         m_clear = null;
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_show);
         if(Boolean(m_show) && m_show.parent)
         {
            m_show.parent.removeChild(m_show);
         }
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_socketListener);
         m_socketListener = null;
         ClearUtil.clearObject(m_quiBtn);
         m_quiBtn = null;
         ClearUtil.clearObject(m_playerListOfApplyPanel);
         m_playerListOfApplyPanel = null;
         m_mySocietyNameText = null;
         m_mySocietyLeadernameText = null;
         m_mySocietyLevelText = null;
         m_mySocietyRankText = null;
         m_memberNumText = null;
         m_maxMemberNumText = null;
         m_societyConValueTextField = null;
         m_societyConValuePerLevelTextField = null;
         m_personalReConValueText = null;
         m_personalTotalConValueText = null;
         ClearUtil.clearDisplayObjectInContainer(m_dissolveTimeShow);
         m_dissolveTimeShow = null;
         ClearUtil.clearObject(m_dayShow);
         m_dayShow = null;
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.clearObject(m_quitSocietyBtn);
         m_quitSocietyBtn = null;
         ClearUtil.clearObject(m_dissolveAndCancelSocietyShow);
         m_dissolveAndCancelSocietyShow = null;
         ClearUtil.clearObject(m_cancelDissolveSocietyBtn);
         m_cancelDissolveSocietyBtn = null;
         ClearUtil.clearObject(m_dissolveSocietyBtn);
         m_dissolveSocietyBtn = null;
         ClearUtil.clearObject(m_solveApplyBtn);
         m_solveApplyBtn = null;
         ClearUtil.clearObject(m_changeAnnouncementBtn);
         m_changeAnnouncementBtn = null;
         m_announcementText = null;
         ClearUtil.clearObject(m_mySocietyFunArea);
         m_mySocietyFunArea = null;
         ClearUtil.clearObject(m_createAnnouncementPanel);
         m_createAnnouncementPanel = null;
         ClearUtil.clearObject(m_otherOperationPanel);
         m_otherOperationPanel = null;
         ClearUtil.clearObject(m_societySignPanel);
         m_societySignPanel = null;
         ClearUtil.clearObject(m_societyContriPanel);
         m_societyContriPanel = null;
         ClearUtil.clearObject(m_societyShopPanel);
         m_societyShopPanel = null;
         ClearUtil.clearObject(m_societyShopBuyPopUpBox);
         m_societyShopBuyPopUpBox = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.clearObject(m_playerDatas);
         m_playerDatas = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.nullArr(m_players);
         m_players = null;
         ClearUtil.clearObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         ClearUtil.clearObject(m_dirtyWorldFilterEngine);
         m_dirtyWorldFilterEngine = null;
         ClearUtil.clearObject(m_buyingGoodData);
         m_buyingGoodData = null;
         m_societySystem = null;
         m_socketManager = null;
         m_gamingUI = null;
         m_societySystemXML = null;
         m_versionControl = null;
         m_playerListOfApply = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setSocketManager(param1:SocketManager) : void
      {
         if(Boolean(m_socketManager) && m_socketListener)
         {
            m_socketManager.removeSocketListener(m_socketListener);
         }
         m_socketManager = param1;
         if(m_socketListener)
         {
            m_socketManager.addSocketListener(m_socketListener);
         }
      }
      
      public function init() : void
      {
         m_myLoader = new YJFYLoader();
         m_loadUI = Part1.getInstance().getLoadUI();
         stage.addChild(m_loadUI);
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         if(m_socketListener == null)
         {
            m_socketListener = new SocketListener();
            m_socketListener.getSocketDataFun = getSocketData;
            m_socketListener.ioErrorFun = ioError;
            m_socketListener.securityErrorFun = securityError;
            m_socketManager.addSocketListener(m_socketListener);
         }
         if(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId() == 0)
         {
            m_gamingUI.closeMySocietyPanel();
            throw new Error("我的帮会Id为0");
         }
         if(m_show == null)
         {
            m_myLoader.getClass("MySocietyPanel.swf","MySocietyPanel",getPanelSuccess,getPanelFail);
            m_myLoader.load();
         }
         else
         {
            initShow2();
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(isNaN(m_dissolveTime) || m_dissolveTime == 0)
         {
            return;
         }
         var _loc4_:int = Math.max(0,m_dissolveTime - param1.getOnLineTimeForThisInit());
         var _loc3_:int = Math.ceil(_loc4_ / 1000);
         var _loc6_:int = _loc3_ / 86400;
         _loc3_ -= _loc6_ * 86400;
         var _loc5_:int = _loc3_ / 3600;
         _loc3_ -= _loc5_ * 3600;
         var _loc2_:int = _loc3_ / 60;
         _loc3_ -= _loc2_ * 60;
         m_dayShow.showNum(_loc6_);
         m_hourShow.showNum(_loc5_);
         m_minuteShow.showNum(_loc2_);
         m_secondShow.showNum(_loc3_);
         if(Boolean(m_mySocietyFunArea) && Boolean(m_mySocietyFunArea.getSocietyChatPanel()))
         {
            m_mySocietyFunArea.getSocietyChatPanel().render(param1);
         }
      }
      
      public function openSocietyShopBuyPopUpBox(param1:GoodData, param2:OneEqSaveDataInSocietyShop, param3:int) : void
      {
         if(m_societyShopBuyPopUpBox == null)
         {
            m_societyShopBuyPopUpBox = new BuyPopUpBox();
            m_societyShopBuyPopUpBox.setSocietySystem(m_societySystem);
            m_societyShopBuyPopUpBox.setMySocietyPanel(this);
            m_myLoader.getClass("SocietyShop.swf","BuyPopUpBox",openSocietyShopBuyPopUpBox2,getPanelFail);
            m_myLoader.load();
         }
         m_societyShopBuyPopUpBox.setData(param1,param2,param3);
      }
      
      public function closeSocietyShopBuyPopUpBox() : void
      {
         ClearUtil.clearObject(m_societyShopBuyPopUpBox);
         m_societyShopBuyPopUpBox = null;
      }
      
      private function getSocketData(param1:InformationBody) : void
      {
         var _loc5_:DOWN_TheSocietyMemberList = null;
         var _loc3_:DOWN_HaveNewMember = null;
         var _loc10_:DOWN_ChangeAnnouncementReturn = null;
         var _loc6_:DOWN_RemoveMemberReturnToLeader = null;
         var _loc2_:int = 0;
         var _loc8_:DOWN_AddConValueReturn = null;
         var _loc12_:DOWN_DecConValueReturn = null;
         var _loc4_:SaveTaskInfo = null;
         var _loc11_:DOWN_GetChatInfor = null;
         var _loc7_:SocietyChatPanel = null;
         var _loc9_:NewGotChatInfor = null;
         if(m_clear == null)
         {
            return;
         }
         m_societySystem.addWasteInforBody(param1);
         loop0:
         switch(param1.id_informationBody)
         {
            case 3007:
               initShow2();
               break;
            case 3039:
               initShow2();
               break;
            case 3009:
               _loc5_ = param1.getDetail() as DOWN_TheSocietyMemberList;
               _loc5_.initOther(m_pageIndex_gettingMemberList,m_num_gettingMemberList);
               m_societySystem.addMemberDataListInMySocietyPanel(_loc5_);
               if(m_mySocietyFunArea)
               {
                  m_mySocietyFunArea.setMemberDataList(param1.getDetail() as DOWN_TheSocietyMemberList);
               }
               if(m_show == null)
               {
                  m_myLoader.getClass("MySocietyPanel.swf","MySocietyPanel",getPanelSuccess,getPanelFail);
                  m_myLoader.load();
                  break;
               }
               initShow2();
               break;
            case 3014:
               m_societySystem.getPlayerListOfApply();
               break;
            case 3020:
               m_playerListOfApply = param1.getDetail() as DOWN_PlayerListOfApply;
               break;
            case 3021:
               _loc3_ = param1.getDetail() as DOWN_HaveNewMember;
               m_societySystem.clearSocietyBufferData();
               if(m_mySocietyFunArea)
               {
                  m_mySocietyFunArea.setMemberDataList(null);
               }
               if(Boolean(m_mySocietyFunArea) && m_mySocietyFunArea.getMemberListPanel())
               {
                  m_pageIndex_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getPageBtnGroup().pageNum - 1;
                  m_num_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getOnePageColumeNum();
                  m_societySystem.getTheSocietyMemberList(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),m_pageIndex_gettingMemberList,m_num_gettingMemberList);
               }
               break;
            case 3032:
               _loc10_ = param1.getDetail() as DOWN_ChangeAnnouncementReturn;
               if(_loc10_.getIsSuccess())
               {
                  m_announcementText.text = !!m_newAnnoument ? m_newAnnoument : "";
                  showWarningBox("修改公告成功",0);
                  break;
               }
               showWarningBox("修改公告失败",0);
               break;
            case 3034:
               _loc6_ = param1.getDetail() as DOWN_RemoveMemberReturnToLeader;
               if(_loc6_.getIsSuccess())
               {
                  m_societySystem.clearSocietyBufferData();
                  if(m_mySocietyFunArea)
                  {
                     m_mySocietyFunArea.setMemberDataList(null);
                  }
                  if(Boolean(m_mySocietyFunArea) && m_mySocietyFunArea.getMemberListPanel())
                  {
                     m_pageIndex_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getPageBtnGroup().pageNum - 1;
                     m_num_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getOnePageColumeNum();
                     m_societySystem.getTheSocietyMemberList(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),m_pageIndex_gettingMemberList,m_num_gettingMemberList);
                  }
                  break;
               }
               _loc2_ = int(m_societySystemXML.removeMember[0].@maxNumOneDay);
               showWarningBox("一天只能踢帮会成员" + _loc2_ + "人, 踢出帮会成员失败。",0);
               break;
            case 3028:
               _loc8_ = param1.getDetail() as DOWN_AddConValueReturn;
               if(_loc8_.getIsSuccess())
               {
                  showWarningBox("帮贡增加成功",0);
                  m_societySystem.getTheSocietyData();
                  m_societySystem.clearSocietyBufferData();
                  if(m_mySocietyFunArea)
                  {
                     m_mySocietyFunArea.setMemberDataList(null);
                  }
                  if(Boolean(m_mySocietyFunArea) && m_mySocietyFunArea.getMemberListPanel())
                  {
                     m_pageIndex_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getPageBtnGroup().pageNum - 1;
                     m_num_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getOnePageColumeNum();
                     m_societySystem.getTheSocietyMemberList(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),m_pageIndex_gettingMemberList,m_num_gettingMemberList);
                  }
                  break;
               }
               showWarningBox("帮贡增加失败",0);
               switch(m_currentChangeConValueState)
               {
                  case "signAddConValue":
                     m_societySignPanel.gotRewardFail();
                     break loop0;
                  case "contriOfExp":
                     m_societyContriPanel.addFailConValueByExp();
                     break loop0;
                  case "contriOfMoney":
                     m_societyContriPanel.addFailConValueByMoney();
                     break loop0;
                  case "buyGood":
               }
               break;
            case 3030:
               _loc12_ = param1.getDetail() as DOWN_DecConValueReturn;
               if(_loc12_.getIsSuccess())
               {
                  if(m_currentChangeConValueState == "buyGood" && m_buyingGoodData)
                  {
                     MyFunction.getInstance().trueAddOneNumEquipmentVO(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs,m_buyingGoodData.getGoodData().getEquipmentVO(),m_buyingGoodData.getBuyNum(),0);
                     GamingUI.getInstance().player1.getSocietyDataVO().getSocietyShopVO().getOneShopDataByShopLevel(m_buyingGoodData.getShopLevel()).addBuyEqData(m_buyingGoodData.getGoodData().getEquipmentVO().id.toString(),m_buyingGoodData.getBuyNum());
                     m_societyShopPanel.refresh();
                     m_societySystem.getTheSocietyData();
                     m_societySystem.clearSocietyBufferData();
                     if(m_mySocietyFunArea)
                     {
                        m_mySocietyFunArea.setMemberDataList(null);
                     }
                     if(Boolean(m_mySocietyFunArea) && m_mySocietyFunArea.getMemberListPanel())
                     {
                        m_pageIndex_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getPageBtnGroup().pageNum - 1;
                        m_num_gettingMemberList = m_mySocietyFunArea.getMemberListPanel().getOnePageColumeNum();
                        m_societySystem.getTheSocietyMemberList(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),m_pageIndex_gettingMemberList,m_num_gettingMemberList);
                     }
                     showWarningBox("购买成功" + m_buyingGoodData.getBuyNum() + "个" + m_buyingGoodData.getGoodData().getEquipmentVO().name,0);
                     ClearUtil.clearObject(m_buyingGoodData);
                     m_buyingGoodData = null;
                     _loc4_ = new SaveTaskInfo();
                     _loc4_.type = "4399";
                     _loc4_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc4_);
                     MyFunction2.saveGame();
                  }
                  break;
               }
               showWarningBox("更改贡献值失败",0);
               break;
            case 3043:
               _loc11_ = param1.getDetail() as DOWN_GetChatInfor;
               _loc7_ = m_mySocietyFunArea.getSocietyChatPanel();
               if(_loc7_)
               {
                  _loc9_ = new NewGotChatInfor();
                  _loc9_.init(_loc11_);
                  _loc7_.getOutputRichText().appendRichText(_loc9_.getChatHeadStr(),_loc9_.getChatHeadFontColor(),_loc9_.getChatHeadFontSize());
                  _loc7_.getOutputRichText().appendRichText(_loc9_.getChatStr(),_loc9_.getChatFontColor(),_loc9_.getChatFontSize());
                  ClearUtil.clearObject(_loc9_);
                  _loc9_ = null;
                  break;
               }
         }
      }
      
      private function openPlayerListOfApplyPanel() : void
      {
         if(m_playerListOfApplyPanel == null)
         {
            m_playerListOfApplyPanel = new PlayerListOfApplayPanel();
            addChild(m_playerListOfApplyPanel);
            m_playerListOfApplyPanel.setSocietySystem(m_societySystem);
            m_playerListOfApplyPanel.setLoader(m_myLoader);
            m_playerListOfApplyPanel.init();
         }
         m_playerListOfApplyPanel.setPlayerListOfApply(m_playerListOfApply);
      }
      
      private function closePlayerListOfApplyPanel() : void
      {
         m_clear.clearObject(m_playerListOfApplyPanel);
         m_playerListOfApplyPanel = null;
      }
      
      private function getPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         initShow();
         m_loadUI.tranToTransparentcy();
      }
      
      private function getPanelFail(param1:YJFYLoaderData) : void
      {
         showWarningBox("加载失败",0);
      }
      
      private function initShow() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_myIsLeader = GamingUI.getInstance().player1.getSocietyDataVO().getUid_leader() == Number(GameData.getInstance().getLoginReturnData().getUid()) && GamingUI.getInstance().player1.getSocietyDataVO().getIdx_leader() == GameData.getInstance().getSaveFileData().index;
         if(m_myIsLeader)
         {
            m_showMC.gotoAndStop("leader");
            m_dissolveAndCancelSocietyShow = new MovieClipPlayLogicShell();
            m_dissolveAndCancelSocietyShow.setShow(m_show["dissolveAndCancelSocietyBtn"]);
            m_dissolveSocietyBtn = new ButtonLogicShell2();
            m_dissolveSocietyBtn.setShow(m_dissolveAndCancelSocietyShow.getShow()["disssolveSocietyBtn"]);
            m_dissolveSocietyBtn.setTipString("点击解散帮会");
            m_changeAnnouncementBtn = new ButtonLogicShell2();
            m_changeAnnouncementBtn.setShow(m_show["changeAnnouncementBtn"]);
            m_changeAnnouncementBtn.setTipString("点击更改帮会公告");
            m_solveApplyBtn = new ButtonLogicShell2();
            m_solveApplyBtn.setShow(m_show["solveApplyBtn"]);
            m_solveApplyBtn.setTipString("点击查看申请加入玩家");
         }
         else
         {
            m_showMC.gotoAndStop("member");
            m_quitSocietyBtn = new ButtonLogicShell2();
            m_quitSocietyBtn.setShow(m_show["quitSocietyBtn"]);
            m_quitSocietyBtn.setTipString("点击退出该帮会");
         }
         m_mySocietyNameText = m_show["mySocietyNameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_mySocietyNameText);
         m_mySocietyLeadernameText = m_show["mySocietyLeaderNameText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_mySocietyLeadernameText);
         m_mySocietyLevelText = m_show["mySocietyLevelText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_mySocietyLevelText);
         m_mySocietyRankText = m_show["mySocietyRankText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_mySocietyRankText);
         m_memberNumText = m_show["memberNumText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_memberNumText);
         m_maxMemberNumText = m_show["maxMemberNumText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_maxMemberNumText);
         m_societyConValueTextField = m_show["societyConValueText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_societyConValueTextField);
         m_societyConValuePerLevelTextField = m_show["societyConValuePerLevelText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_societyConValuePerLevelTextField);
         m_personalReConValueText = m_show["personalReConValueText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_personalReConValueText);
         m_personalTotalConValueText = m_show["personalTotalConValueText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_personalTotalConValueText);
         m_announcementText = m_show["announcementText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_announcementText);
         m_dissolveTimeShow = m_show["dissolveTimeShow"];
         initTimeShow();
         m_quiBtn = new ButtonLogicShell2();
         m_quiBtn.setShow(m_show["quitBtn"]);
         m_quiBtn.setTipString("点击关闭");
         m_mySocietyFunArea = new MySocietyFunArea();
         m_mySocietyFunArea.setMyLoader(m_myLoader);
         m_mySocietyFunArea.setGamingUI(m_gamingUI);
         m_mySocietyFunArea.setMySocietyPanel(this);
         m_mySocietyFunArea.setSocietySystem(m_societySystem);
         m_mySocietyFunArea.setSocietySystemXML(m_societySystemXML);
         m_mySocietyFunArea.setShow(m_show["funArea"]);
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId() == 0)
         {
            m_gamingUI.closeMySocietyPanel();
            return;
         }
         var _loc2_:SocietyLevelData = new SocietyLevelData();
         var _loc1_:SocietyDataVO = GamingUI.getInstance().player1.getSocietyDataVO();
         _loc2_.init(_loc1_.getSocietyTotalConValue(),_loc1_.getSocietyLevel(),m_societySystemXML);
         m_mySocietyNameText.text = !!_loc1_.getSocietyName() ? _loc1_.getSocietyName() : "";
         m_mySocietyLeadernameText.text = !!_loc1_.getName_leader() ? _loc1_.getName_leader() : _loc1_.getUid_leader().toString();
         m_mySocietyLevelText.text = _loc1_.getSocietyLevel().toString();
         m_mySocietyRankText.text = _loc1_.getSocietyRank().toString();
         m_memberNumText.text = _loc1_.getPlayerNumInSociety().toString();
         m_maxMemberNumText.text = _loc1_.getPlayerMaxNumINSociety().toString();
         m_societyConValueTextField.text = _loc1_.getSocietyTotalConValue().toString();
         m_societyConValuePerLevelTextField.text = _loc2_.totalConValueOfNextLevel.toString();
         m_personalReConValueText.text = _loc1_.getPersonalReConValue().toString();
         m_personalTotalConValueText.text = _loc1_.getPersonalTotalConValue().toString();
         m_announcementText.text = !!_loc1_.getAnnouncementOfSociety() ? _loc1_.getAnnouncementOfSociety() : "";
         m_dissolveTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + _loc1_.getDissolveRemainTime();
         if(_loc1_.getDissolveRemainTime())
         {
            m_dissolveTimeShow.visible = true;
            if(m_myIsLeader)
            {
               ClearUtil.clearObject(m_dissolveSocietyBtn);
               m_dissolveSocietyBtn = null;
               m_dissolveAndCancelSocietyShow.gotoAndStop("cancelDissolveBtn");
               m_cancelDissolveSocietyBtn = new ButtonLogicShell2();
               m_cancelDissolveSocietyBtn.setShow(m_dissolveAndCancelSocietyShow.getShow()["cancelDissolveBtn"]);
               m_cancelDissolveSocietyBtn.setTipString("取消解散帮会");
            }
         }
         else
         {
            m_dissolveTimeShow.visible = false;
            if(m_myIsLeader)
            {
               ClearUtil.clearObject(m_cancelDissolveSocietyBtn);
               m_cancelDissolveSocietyBtn = null;
               m_dissolveAndCancelSocietyShow.gotoAndStop("dissolveBtn");
               m_dissolveSocietyBtn = new ButtonLogicShell2();
               m_dissolveSocietyBtn.setShow(m_dissolveAndCancelSocietyShow.getShow()["disssolveSocietyBtn"]);
               m_dissolveSocietyBtn.setTipString("解散帮会");
            }
         }
      }
      
      private function initTimeShow() : void
      {
         m_dayShow = new MultiPlaceNumLogicShell();
         m_dayShow.setShow(m_dissolveTimeShow["timeShow"]["day"]);
         m_hourShow = new MultiPlaceNumLogicShell();
         m_hourShow.setShow(m_dissolveTimeShow["timeShow"]["hour"]);
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_dissolveTimeShow["timeShow"]["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_dissolveTimeShow["timeShow"]["second"]);
         m_dayShow.setIsShowZero(true);
         m_hourShow.setIsShowZero(true);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
      }
      
      private function clearTimeShow() : void
      {
         ClearUtil.clearObject(m_dayShow);
         m_dayShow = null;
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc7_:String = null;
         var _loc4_:int = 0;
         var _loc11_:MemberDataInMemberList = null;
         var _loc10_:MemberDataInMemberList = null;
         var _loc12_:int = 0;
         var _loc8_:Boolean = false;
         var _loc13_:* = param1.button;
         switch(_loc13_)
         {
            case m_quiBtn:
               m_gamingUI.closeMySocietyPanel();
               return;
            case m_solveApplyBtn:
               if(m_playerListOfApply)
               {
                  openPlayerListOfApplyPanel();
               }
               else
               {
                  m_societySystem.getPlayerListOfApply();
               }
               return;
            case m_quitSocietyBtn:
               m_societySystem.leaveSociety();
               return;
            case m_dissolveSocietyBtn:
               m_societySystem.dissolveSociety();
               return;
            case m_cancelDissolveSocietyBtn:
               m_societySystem.cancelDissolveSociety();
               return;
            case m_changeAnnouncementBtn:
               if(m_createAnnouncementPanel)
               {
                  return;
               }
               m_myLoader.getClass("MySocietyPanel.swf","CreateAnnouncementPanel",getCreateAnPanelShowSuccess,getShowFail);
               m_myLoader.load();
               return;
               break;
            default:
               if((!!m_playerListOfApplyPanel ? m_playerListOfApplyPanel.getQuitBtn() : null) !== _loc13_)
               {
                  if((!!m_createAnnouncementPanel ? m_createAnnouncementPanel.getCancelBtn() : null) !== _loc13_)
                  {
                     if((!!m_createAnnouncementPanel ? m_createAnnouncementPanel.getQuitBtn() : null) !== _loc13_)
                     {
                        if((!!m_createAnnouncementPanel ? m_createAnnouncementPanel.getSureBtn() : null) !== _loc13_)
                        {
                           if((!!m_otherOperationPanel ? m_otherOperationPanel.getQuitBtn() : null) !== _loc13_)
                           {
                              if((!!m_otherOperationPanel ? m_otherOperationPanel.getRemoveMemberBtn() : null) !== _loc13_)
                              {
                                 if((!!m_otherOperationPanel ? m_otherOperationPanel.getLookUpBtn() : null) !== _loc13_)
                                 {
                                    _loc12_ = 0;
                                    while(_loc12_ < 0)
                                    {
                                       if(null.getColumeByIndex(_loc12_) != null)
                                       {
                                          if(null.getColumeByIndex(_loc12_).getMemberData() != null)
                                          {
                                             if(param1.button == null.getColumeByIndex(_loc12_))
                                             {
                                                if(null.getColumeByIndex(_loc12_).getMemberData().getUid_member() == Number(GameData.getInstance().getLoginReturnData().getUid()) && null.getColumeByIndex(_loc12_).getMemberData().getIdx_member() == GameData.getInstance().getSaveFileData().index)
                                                {
                                                   _loc8_ = true;
                                                }
                                                m_myLoader.getClass("MySocietyPanel.swf","OtherOperationPanel",getOtherOperationPanelShowSuccess,getShowFail,null,null,[null.getColumeByIndex(_loc12_).getMemberData(),_loc8_]);
                                                m_myLoader.load();
                                                return;
                                             }
                                          }
                                       }
                                       _loc12_++;
                                    }
                                    var _loc3_:SocietyActivityPanel = !!m_mySocietyFunArea ? m_mySocietyFunArea.getSocietyActivityPanel() : null;
                                    if(_loc3_)
                                    {
                                       switch(param1.button)
                                       {
                                          case _loc3_.getSignJoinBtn():
                                             if(m_societySignPanel == null)
                                             {
                                                m_myLoader.getClass("MySocietyPanel.swf","SocietySignPanel",openSocietySignPanel2,getShowFail);
                                                m_myLoader.load();
                                             }
                                             return;
                                          case _loc3_.getContributionBtn():
                                             if(m_societyContriPanel == null)
                                             {
                                                m_myLoader.getClass("MySocietyPanel.swf","SocietyContributionPanel",openSocietyContriPanel2,getShowFail);
                                                m_myLoader.load();
                                             }
                                             return;
                                       }
                                    }
                                    var _loc2_:SocietyWelfarePanel = !!m_mySocietyFunArea ? m_mySocietyFunArea.getSocietyWelfarePanel() : null;
                                    if(_loc2_)
                                    {
                                       _loc13_ = param1.button;
                                       if(_loc2_.getShopJoinBtn() === _loc13_)
                                       {
                                          if(m_societyShopPanel == null)
                                          {
                                             m_myLoader.getClass("SocietyShop.swf","SocietyShop",openSocietyShopPanel2,getShowFail);
                                             m_myLoader.load();
                                          }
                                       }
                                    }
                                    if(!!m_mySocietyFunArea ? m_mySocietyFunArea.getSocietyChatPanel() : null)
                                    {
                                       _loc13_ = param1.button;
                                    }
                                    if(m_societySignPanel)
                                    {
                                       switch(param1.button)
                                       {
                                          case m_societySignPanel.getQuitBtn():
                                             closeSocietySignPanel();
                                             return;
                                          case m_societySignPanel.getRewardBtn():
                                             m_societySignPanel.gotReward();
                                             m_currentChangeConValueState = "signAddConValue";
                                             m_societySystem.addConValue(m_societySignPanel.getAbleGotConValueId());
                                             return;
                                       }
                                    }
                                    if(m_societyContriPanel)
                                    {
                                       switch(param1.button)
                                       {
                                          case m_societyContriPanel.getQuitBtn():
                                             closeSocietyContriPanel();
                                             return;
                                          case m_societyContriPanel.getContriOfExpBtn():
                                             m_currentChangeConValueState = "contriOfExp";
                                             m_societyContriPanel.addConValueByExp();
                                             return;
                                          case m_societyContriPanel.getContriOfMoneyBtn():
                                             m_currentChangeConValueState = "contriOfMoney";
                                             m_societyContriPanel.addConValueByMoney();
                                             return;
                                          case m_societyContriPanel.getContriOfTicketBtn():
                                             m_currentChangeConValueState = "contriOfTicket";
                                             m_societyContriPanel.addConValueByTicket();
                                             return;
                                       }
                                    }
                                    if(m_societyShopPanel)
                                    {
                                       _loc13_ = param1.button;
                                       if(m_societyShopPanel.getQuitBtn() === _loc13_)
                                       {
                                          closeSocietyShopPanel();
                                          return;
                                       }
                                    }
                                    if(m_societyShopBuyPopUpBox)
                                    {
                                       switch(param1.button)
                                       {
                                          case m_societyShopBuyPopUpBox.getCancelBtn():
                                          case m_societyShopBuyPopUpBox.getQuitBtn():
                                          case m_societyShopBuyPopUpBox.getSureBtn():
                                       }
                                       if(param1.button == m_societyShopBuyPopUpBox.getSureBtn())
                                       {
                                          m_currentChangeConValueState = "buyGood";
                                          m_buyingGoodData = m_societyShopBuyPopUpBox.buyGood();
                                       }
                                       closeSocietyShopBuyPopUpBox();
                                       return;
                                    }
                                    return;
                                 }
                                 if(m_playerDatas == null)
                                 {
                                    m_playerDatas = new PlayerDatas();
                                    m_getPlayerDataListener = new GetPlayerDataListener();
                                    m_getPlayerDataListener.getPlayerDataFun = getPlayerData;
                                    m_playerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
                                    m_playerDatas.init();
                                 }
                                 _loc10_ = m_otherOperationPanel.getMemberData();
                                 m_playerDatas.getPlayerData(_loc10_.getUid_member().toString(),_loc10_.getIdx_member());
                                 return;
                              }
                              _loc11_ = m_otherOperationPanel.getMemberData();
                              showWarningBox("确定要逐出该成员吗？",3,{
                                 "type":"removeMember",
                                 "okFunction":removeMember,
                                 "okFunctionParams":[_loc11_]
                              });
                              m_clear.clearObject(m_otherOperationPanel);
                              m_otherOperationPanel = null;
                              return;
                           }
                           m_clear.clearObject(m_otherOperationPanel);
                           m_otherOperationPanel = null;
                           return;
                        }
                        _loc7_ = m_createAnnouncementPanel.getAnnouncementText().text;
                        _loc4_ = int(m_societySystemXML.announcementOfSociety[0].@maxLength);
                        if(_loc7_.length > _loc4_)
                        {
                           _loc7_ = _loc7_.substr(0,_loc4_);
                        }
                        if(m_dirtyWorldFilterEngine == null)
                        {
                           m_dirtyWorldFilterEngine = new DirtyWordFilterEngine(createNewAnnouncement,[_loc7_]);
                        }
                        else
                        {
                           createNewAnnouncement(_loc7_);
                        }
                        return;
                     }
                  }
                  m_clear.clearObject(m_createAnnouncementPanel);
                  m_createAnnouncementPanel = null;
                  return;
               }
               closePlayerListOfApplyPanel();
               return;
         }
      }
      
      public function getMemberListData(param1:int, param2:int) : void
      {
         if(m_societySystem.getMemberDataListInMySocietyPanelByIndex(param1) == null || m_societySystem.getMemberDataListInMySocietyPanelByIndex(param1).getDataDeadTime() < GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit())
         {
            m_societySystem.getTheSocietyMemberList(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),param1,param2);
            m_pageIndex_gettingMemberList = param1;
            m_num_gettingMemberList = param2;
         }
         else if(m_mySocietyFunArea)
         {
            m_mySocietyFunArea.setMemberDataList(m_societySystem.getMemberDataListInMySocietyPanelByIndex(param1));
         }
      }
      
      private function openSocietySignPanel2(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         if(m_societySignPanel == null)
         {
            _loc2_ = param1.resultClass;
            m_societySignPanel = new SocietySignPanel();
            m_societySignPanel.setMySocietyPanel(this);
            m_societySignPanel.setSocietySignVO(GamingUI.getInstance().player1.getSocietyDataVO().getSocietySignVO());
            m_societySignPanel.setSocietySystemXML(m_societySystemXML);
            m_societySignPanel.setShow(new _loc2_());
            addChild(m_societySignPanel.getShow());
            if(stage)
            {
               m_societySignPanel.getShow().x = (stage.stageWidth - m_societySignPanel.getShow().width) / 2;
               m_societySignPanel.getShow().y = (stage.stageHeight - m_societySignPanel.getShow().height) / 2;
            }
         }
      }
      
      private function openSocietyContriPanel2(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         if(m_societyContriPanel == null)
         {
            _loc2_ = param1.resultClass;
            m_societyContriPanel = new SocietyContributionPanel();
            m_societyContriPanel.setShow(new _loc2_(),this,m_societySystem,m_societySystemXML,GamingUI.getInstance().player1.getSocietyDataVO().getSocietyContributionVO());
            addChild(m_societyContriPanel.getShow());
            if(stage)
            {
               m_societyContriPanel.getShow().x = (stage.stageWidth - m_societyContriPanel.getShow().width) / 2;
               m_societyContriPanel.getShow().y = (stage.stageHeight - m_societyContriPanel.getShow().height) / 2;
            }
         }
      }
      
      private function openSocietyShopPanel2(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         if(m_societyShopPanel == null)
         {
            _loc2_ = param1.resultClass;
            m_societyShopPanel = new SocietyShop();
            m_societyShopPanel.setMySocietyPanel(this);
            m_societyShopPanel.setLoader(m_myLoader);
            m_societyShopPanel.setSoceitySystem(m_societySystem);
            m_societyShopPanel.setSocietySystemXML(m_societySystemXML);
            m_societyShopPanel.setSocietyDataVO(GamingUI.getInstance().player1.getSocietyDataVO());
            m_societyShopPanel.setShow(new _loc2_());
            if(stage)
            {
               m_societyShopPanel.getShow().x = (stage.stageWidth - m_societyShopPanel.getShow().width) / 2;
               m_societyShopPanel.getShow().y = (stage.stageHeight - m_societyShopPanel.getShow().height) / 2;
            }
         }
      }
      
      private function openSocietyShopBuyPopUpBox2(param1:YJFYLoaderData) : void
      {
         if(m_societyShopBuyPopUpBox == null)
         {
            return;
         }
         var _loc2_:Class = param1.resultClass;
         m_societyShopBuyPopUpBox.setShow(new _loc2_());
         if(stage)
         {
            m_societyShopBuyPopUpBox.getShow().x = (stage.stageWidth - m_societyShopBuyPopUpBox.getShow().width) / 2;
            m_societyShopBuyPopUpBox.getShow().y = (stage.stageHeight - m_societyShopBuyPopUpBox.getShow().height) / 2;
         }
      }
      
      private function closeSocietySignPanel() : void
      {
         ClearUtil.clearObject(m_societySignPanel);
         m_societySignPanel = null;
      }
      
      private function closeSocietyContriPanel() : void
      {
         ClearUtil.clearObject(m_societyContriPanel);
         m_societyContriPanel = null;
      }
      
      private function closeSocietyShopPanel() : void
      {
         ClearUtil.clearObject(m_societyShopPanel);
         m_societyShopPanel = null;
      }
      
      private function getPlayerData(param1:InitPlayersData) : void
      {
         openPlayerShowPanel(param1.player1,param1.player2,param1.nickNameData);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         addChild(m_otherPlayerShowPanel);
      }
      
      private function getOtherOperationPanelShowSuccess(param1:YJFYLoaderData, param2:MemberDataInMemberList, param3:Boolean) : void
      {
         var _loc4_:Class = null;
         if(m_otherOperationPanel == null)
         {
            _loc4_ = param1.resultClass;
            m_otherOperationPanel = new OtherOperationPanel();
            m_otherOperationPanel.setShow(new _loc4_());
            if(stage)
            {
               m_otherOperationPanel.getShow().x = (stage.stageWidth - m_otherOperationPanel.getShow().width) / 2;
               m_otherOperationPanel.getShow().y = (stage.stageHeight - m_otherOperationPanel.getShow().height) / 2;
            }
         }
         m_otherOperationPanel.setMemberData(param2);
         m_otherOperationPanel.setIsMyPlayer(m_myIsLeader,param3);
         addChild(m_otherOperationPanel.getShow());
      }
      
      private function getCreateAnPanelShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         if(m_createAnnouncementPanel == null)
         {
            _loc2_ = param1.resultClass;
            m_createAnnouncementPanel = new CreateAnnouncementPanel();
            m_createAnnouncementPanel.setAnnouncementMaxLength(int(m_societySystemXML.announcementOfSociety[0].@maxLength));
            m_createAnnouncementPanel.setShow(new _loc2_());
            if(stage)
            {
               m_createAnnouncementPanel.getShow().x = (stage.stageWidth - m_createAnnouncementPanel.getShow().width) / 2;
               m_createAnnouncementPanel.getShow().y = (stage.stageHeight - m_createAnnouncementPanel.getShow().height) / 2;
            }
         }
         addChild(m_createAnnouncementPanel.getShow());
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         showWarningBox("加载失败！",0);
      }
      
      private function createNewAnnouncement(param1:String) : void
      {
         m_dirtyWorldFilterEngine.filterWords(param1,checkBadReturn);
      }
      
      private function checkBadReturn(param1:String) : void
      {
         showWarningBox("新公告为：“" + param1 + "”, 确定要更改公告吗？",3,{
            "type":"changeAnnouncement",
            "okFunction":changeAnnouncement,
            "okFunctionParams":[param1]
         });
         m_clear.clearObject(m_createAnnouncementPanel);
         m_createAnnouncementPanel = null;
      }
      
      private function changeAnnouncement(param1:String) : void
      {
         m_clear.clearObject(m_createAnnouncementPanel);
         m_createAnnouncementPanel = null;
         var _loc2_:int = int(m_societySystemXML.announcementOfSociety[0].@needPointTicket);
         if(CurrentTicketPointManager.getInstance().getCurrentTicketPoint() < _loc2_)
         {
            showWarningBox("点券不足，修改公告失败！",0);
            GamingUI.getInstance().showMessageTip("点券不足，修改公告失败！");
            return;
         }
         m_societySystem.changeAnnouncement(GamingUI.getInstance().player1.getSocietyDataVO().getSocietyId(),param1);
         m_newAnnoument = param1;
      }
      
      private function removeMember(param1:MemberDataInMemberList) : void
      {
         m_societySystem.removeMember(param1.getUid_member(),param1.getIdx_member());
      }
      
      private function ioError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("输入/输出流发生错误，导致加载失败");
      }
      
      private function securityError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("安全错误，导致加载失败");
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,!!m_otherPlayerShowPanel ? m_otherPlayerShowPanel.currentPlayer : null);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

