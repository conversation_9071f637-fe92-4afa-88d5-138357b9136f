package UI2.NewRank
{
   import Json.MyJSON;
   import UI.EnterFrameTime;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitCompleteListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.broadcast.SubmitFunction;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SaveData;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.LoadUI2;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.IPK_test;
   import YJFY.PKMode.LocalPKRankListAPI;
   import YJFY.PKMode.PKData.PKSaveDataOne;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.PKMode.PKLogic.PKWorld;
   import YJFY.PKMode.PKLogic.PKWorldForOneKey;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.utils.MD5;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   
   public class RankPK implements IPK_test
   {
      public static const s_const_OnePK:String = "onePK";
      
      public static const s_const_TwoPK:String = "twoPK";
      
      private var m_strPkUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/pk";
      
      private const m_const_onePKRankListId:uint = 139;
      
      private const m_const_twoPKRankListId:uint = 1580;
      
      private const m_const_MaxDataNumInRankList:uint = 10000;
      
      private const m_const_getRankInforNum:uint = 100;
      
      private const m_const_pkPlayerNum:uint = 8;
      
      private var m_show:Sprite;
      
      private var m_pkPanel:RankWorld;
      
      private var m_myPlayerDatas:PlayerDatas;
      
      private var m_foePalyerDatas:PlayerDatas;
      
      private var m_otherPlayerDatas:PlayerDatas;
      
      private var m_otherPlayerData:InitPlayersData;
      
      private var m_myUiPlayerData:InitPlayersData;
      
      private var m_myUiPlayer1:Player;
      
      private var m_myUiPlayer2:Player;
      
      private var m_foeUiPlayerData:InitPlayersData;
      
      private var m_foeUiPlayer1:Player;
      
      private var m_foeUiPlayer2:Player;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_localRankListAPI:LocalPKRankListAPI;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadIsEx:Boolean;
      
      private var m_myDataInRankList:UserDataInRankList;
      
      private var m_myMonthDataInRankList:UserDataInRankList;
      
      private var m_rankListId:uint;
      
      private var m_panelShowClassName:String;
      
      private var m_pkType:String;
      
      private var m_pkIndex:uint;
      
      private var m_pkMap:PKWorld;
      
      private var m_pkOneKeyMap:PKWorldForOneKey;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_isSigned:Boolean;
      
      private var m_getNeedRank:uint;
      
      private var m_versionControl:IVersionControl;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_loadUI:IProgressShow;
      
      private var m_pkSaveDataOne:PKSaveDataOne;
      
      private var m_pkTargetPlayerData:PKTargetPlayerData;
      
      private var m_userDatas:Vector.<UserDataInRankList>;
      
      private var m_curplayer:Player;
      
      private var m_playerData:Object;
      
      private var m_uid:String;
      
      private var m_idx:int;
      
      private var m_nowFile:int;
      
      private var m_initMode:int;
      
      public function RankPK()
      {
         super();
         GameEvent.eventDispacher.addEventListener("fail",getDataFail);
         m_show = new MySprite();
         m_show.addEventListener("warningBox",sureOrCancel,true,0,true);
         m_show.addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_myPlayerDatas = new PlayerDatas();
         m_foePalyerDatas = new PlayerDatas();
         m_myPlayerDatas.init();
         m_foePalyerDatas.init();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.getSaveDataErrorFun = rankListError1;
         m_rankListAPIListener.getSaveDataSuccessFun = getPlayerRankInfoSuccess;
         m_localRankListAPI = new LocalPKRankListAPI();
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(m_localRankListAPI);
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_playerData = {};
         m_initMode = 2777;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("warningBox",sureOrCancel,true);
            m_show.removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,true);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,false);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,false);
         }
         GameEvent.eventDispacher.removeEventListener("fail",getDataFail);
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(null);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_playerData);
         m_playerData = null;
         ClearUtil.clearObject(m_pkPanel);
         m_pkPanel = null;
         ClearUtil.clearObject(m_myPlayerDatas);
         m_myPlayerDatas = null;
         ClearUtil.clearObject(m_foePalyerDatas);
         m_foePalyerDatas = null;
         ClearUtil.clearObject(m_otherPlayerDatas);
         m_otherPlayerDatas = null;
         ClearUtil.clearObject(m_otherPlayerData);
         m_otherPlayerData = null;
         m_myUiPlayerData = null;
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         m_foeUiPlayerData = null;
         m_foeUiPlayer1 = null;
         m_foeUiPlayer2 = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         ClearUtil.clearObject(m_localRankListAPI);
         m_localRankListAPI = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_myDataInRankList);
         m_myDataInRankList = null;
         if(m_myMonthDataInRankList)
         {
            ClearUtil.clearObject(m_myMonthDataInRankList);
         }
         m_myMonthDataInRankList = null;
         m_panelShowClassName = null;
         m_pkType = null;
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.clearObject(m_players);
         m_players = null;
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         m_versionControl = null;
         m_enterFrameTime = null;
         m_loadUI = null;
         m_pkSaveDataOne = null;
         m_pkTargetPlayerData = null;
         m_curplayer = null;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function setLoadUI(param1:IProgressShow) : void
      {
         m_loadUI = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_pkMap)
         {
            m_pkMap.render(m_enterFrameTime);
            m_pkMap.showRender();
         }
      }
      
      public function openPKWorld() : void
      {
         if(m_pkMap)
         {
            return;
         }
         Part1.getInstance().continueGame();
         RankDataInfo.getInstance().setFightType(1);
         m_pkMap = new PKWorld();
         m_pkMap.setPk(null);
         m_pkMap.setRankWorld(this);
         m_pkMap.setIsTwoMode(false);
         if(m_show.contains(m_pkPanel))
         {
            m_show.removeChild(m_pkPanel);
         }
         m_show.addChild(m_pkMap);
         m_pkMap.setVersionControl(m_versionControl);
         m_pkMap.setLoadUI(m_loadUI as LoadUI2);
         m_pkMap.setMyLoader(m_myLoader);
         (m_loadUI as LoadUI2).tranToOpacity();
         m_pkMap.setEnterFrameTime(m_enterFrameTime);
         m_pkMap.init();
         m_pkMap.initUiPlayerData(m_myUiPlayer1,null,m_foeUiPlayer1,null);
         m_pkMap.initByXMLPath("NewGameFolder/PKMap.xml");
      }
      
      public function closePKWorld() : void
      {
         clearPKDataForNextPK();
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         Part1.getInstance().returnCity();
         Part1.getInstance().openNewRank();
      }
      
      public function addPKDataAndSubmitToRankList(param1:Boolean) : void
      {
         RankDataInfo.getInstance().setReamin();
         RankDataInfo.getInstance().setFightWin(param1);
         var _loc2_:SaveTaskInfo = new SaveTaskInfo();
         _loc2_.type = "4399";
         _loc2_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc2_);
         MyFunction2.saveGame();
         sendFightEnd(param1);
      }
      
      private function sendFightEnd(param1:Boolean) : void
      {
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",onLoaderFightCompleteHandler);
         _loc2_.addEventListener("ioError",onErrorFightHandler);
         _loc2_.addEventListener("securityError",onErrorFightHandler);
         var _loc3_:URLRequest = new URLRequest(this.m_strPkUrl);
         _loc3_.method = "POST";
         var _loc5_:Object = {};
         _loc5_.RankID = RankDataInfo.getInstance().getNowRankId();
         _loc5_.Ver = GameData.getInstance().getLastSaveData().getVersion();
         _loc5_.Win = param1;
         _loc5_.Sid = RankDataInfo.getInstance().getMySid();
         _loc5_.Other = RankDataInfo.getInstance().getFightSid();
         var _loc4_:URLVariables = new URLVariables();
         _loc4_.JSON = MyJSON.encode(_loc5_);
         _loc4_.MD5 = MD5.hash(MyJSON.encode(_loc5_) + XMLSingle.getInstance().dataXML.ServerMD5Key.@key);
         _loc3_.data = _loc4_;
         _loc2_.load(_loc3_);
      }
      
      protected function onLoaderFightCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderFightCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorFightHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorFightHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         if(_loc2_.Result != null && _loc2_.Result == 0)
         {
            GamingUI.getInstance().showMessageTip("数据出错,请清除缓存使用最新版本进行游戏");
            return;
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().initData(_loc2_);
         SubmitFunction.getInstance().setData5(13,RankDataInfo.getInstance().getCurFightRank(),getRankNameById(RankDataInfo.getInstance().getNowRankId()));
      }
      
      private function getRankNameById(param1:int) : String
      {
         if(param1 == 1)
         {
            return "总";
         }
         if(param1 == 2)
         {
            return "孙悟空";
         }
         if(param1 == 3)
         {
            return "白龙马";
         }
         if(param1 == 4)
         {
            return "二郎神";
         }
         if(param1 == 5)
         {
            return "嫦娥";
         }
         if(param1 == 6)
         {
            return "灵狐";
         }
         if(param1 == 7)
         {
            return "铁扇";
         }
         if(param1 == 8)
         {
            return "紫霞";
         }
         return "";
      }
      
      protected function onErrorFightHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderFightCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorFightHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorFightHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      public function init(param1:String) : void
      {
         m_myLoader.getClass("rankworld.swf","RankWorld",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getConfigSuccess(param1:YJFYLoaderData) : void
      {
         RankDataInfo.getInstance().initXML(param1.resultXML);
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
         ClearUtil.clearObject(m_pkPanel);
         var _loc2_:Class = param1.resultClass;
         m_pkPanel = new RankWorld(this);
         m_pkPanel.init2(new _loc2_());
         m_show.addChild(m_pkPanel);
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      public function lookUpMyPlayerInfor() : void
      {
         openPlayerShowPanel(m_myUiPlayer1,null,m_myUiPlayerData.nickNameData);
      }
      
      public function lookUpFoePlayerInfor() : void
      {
         openPlayerShowPanel(m_foeUiPlayer1,null,m_foeUiPlayerData.nickNameData);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         m_show.addChild(m_otherPlayerShowPanel);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
      
      private function getPlayerRankInfoSuccess(param1:SaveData) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         getSaveXMLSuccess(param1.getSaveData());
      }
      
      public function getMySaveData() : void
      {
         m_nowFile = 1;
         m_uid = RankDataInfo.getInstance().getMyUid();
         m_idx = RankDataInfo.getInstance().getMyIdx();
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getSaveData(m_uid,m_idx);
      }
      
      private function getFoeSaveData() : void
      {
         m_nowFile = 2;
         m_uid = RankDataInfo.getInstance().getFoeUid();
         m_idx = RankDataInfo.getInstance().getFoeIdx();
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getSaveData(m_uid,m_idx);
      }
      
      private function getSaveXMLSuccess(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(m_playerData == null)
         {
            return;
         }
         var _loc2_:InitCompleteListener = new InitCompleteListener();
         _loc2_.initCompleteFun = initComplete;
         initPlayerData(m_uid,m_idx,param1,_loc2_);
      }
      
      private function initComplete() : void
      {
         var _loc1_:Number = NaN;
         var _loc2_:InitPlayersData = getOnePlayerData(m_uid,m_idx);
         _loc2_.player1.playerVO.playerUid = m_uid;
         if(_loc2_.player2)
         {
            _loc2_.player2.playerVO.playerUid = m_uid;
         }
         if(m_nowFile == 1)
         {
            m_myUiPlayerData = _loc2_;
            if(RankDataInfo.getInstance().getMySid() % 10 == 1)
            {
               m_myUiPlayer1 = _loc2_.player1;
            }
            if(_loc2_.player2)
            {
               if(RankDataInfo.getInstance().getMySid() % 10 == 2)
               {
                  m_myUiPlayer1 = _loc2_.player2;
               }
               m_myUiPlayer2 = _loc2_.player2;
            }
            getFoeSaveData();
         }
         else if(m_nowFile == 2)
         {
            m_foeUiPlayerData = _loc2_;
            _loc1_ = RankDataInfo.getInstance().getFightSid();
            if(RankDataInfo.getInstance().getFightSid() % 10 == 1)
            {
               m_foeUiPlayer1 = _loc2_.player1;
            }
            if(_loc2_.player2)
            {
               if(RankDataInfo.getInstance().getFightSid() % 10 == 2)
               {
                  m_foeUiPlayer1 = _loc2_.player2;
               }
               m_foeUiPlayer2 = _loc2_.player2;
            }
            if(m_myUiPlayer1 != null && m_foeUiPlayer1 != null)
            {
               openPKWorld();
            }
         }
      }
      
      private function initPlayerData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         if(m_playerData[param1] == null)
         {
            m_playerData[param1] = [];
         }
         var _loc5_:InitPlayersData = new InitPlayersData();
         m_playerData[param1][param2] = _loc5_;
         _loc5_.uid = param1;
         _loc5_.idx = param2;
         param4.addTarget = _loc5_;
         _loc5_.addInitCompleteListener(param4);
         _loc5_.initPlayerData(param3,param2,m_initMode,true);
      }
      
      private function getOnePlayerData(param1:String, param2:int) : InitPlayersData
      {
         if(m_playerData[param1] == null)
         {
            return null;
         }
         return m_playerData[param1][param2];
      }
      
      private function getDataFail(param1:Event) : void
      {
         getFoeSaveData();
      }
      
      private function rankListError1(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
         Part1.getInstance().closeNewRank();
         Part1.getInstance().returnCity();
      }
      
      private function rankListError2(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChildAt(MessageBoxEngine.getInstance(),m_show.numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,!!m_otherPlayerShowPanel ? m_otherPlayerShowPanel.currentPlayer : null);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChildAt(WarningBoxSingle.getInstance(),m_show.numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function clearPKDataForNextPK() : void
      {
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         ClearUtil.clearObject(m_foeUiPlayer1);
         m_foeUiPlayer1 = null;
         ClearUtil.clearObject(m_foeUiPlayer2);
         m_foeUiPlayer2 = null;
         if(m_foeUiPlayerData)
         {
            m_foePalyerDatas.clearOnePlayerData(m_foeUiPlayerData);
         }
         m_foeUiPlayerData = null;
      }
   }
}

