package UI.ShiTu
{
   import UI.DataManagerParent;
   
   public class XiuLianData extends DataManagerParent
   {
      private var m_buyXiuLianNumPointTicket:uint;
      
      private var m_buyXiuLianNumPointTicketId:String;
      
      private var m_needMoney:uint;
      
      private var m_needPKPoint:uint;
      
      private var m_getXiuLianValue:uint;
      
      private var m_xiuLianNumOfOneDay:uint;
      
      public function XiuLianData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         buyXiuLianNumPointTicket = uint(param1.@buyXiuLianNumPointTicket);
         buyXiuLianNumPointTicketId = String(param1.@buyXiuLianNumPointTicketId);
         needMoney = uint(param1.@needMoney);
         needPKPoint = uint(param1.@needPKPoint);
         getXiuLianValue = uint(param1.@getXiuLianValue);
         xiuLianNumOfOneDay = uint(param1.@xiuLianNumOfOneDay);
      }
      
      public function getBuyXiuLianNumPointTicket() : uint
      {
         return buyXiuLianNumPointTicket;
      }
      
      public function getBuyXiuLianNumPointTicketId() : String
      {
         return buyXiuLianNumPointTicketId;
      }
      
      public function getNeedMoney() : uint
      {
         return needMoney;
      }
      
      public function getNeedPKPoint() : uint
      {
         return needPKPoint;
      }
      
      public function getGetXiuLianValue() : uint
      {
         return getXiuLianValue;
      }
      
      public function getXiuLianNumOfOneDay() : uint
      {
         return xiuLianNumOfOneDay;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.buyXiuLianNumPointTicket = m_buyXiuLianNumPointTicket;
         _antiwear.buyXiuLianNumPointTicketId = m_buyXiuLianNumPointTicketId;
         _antiwear.needMoney = m_needMoney;
         _antiwear.needPKPoint = m_needPKPoint;
         _antiwear.getXiuLianValue = m_getXiuLianValue;
         _antiwear.xiuLianNumOfOneDay = m_xiuLianNumOfOneDay;
      }
      
      private function get buyXiuLianNumPointTicket() : uint
      {
         return _antiwear.buyXiuLianNumPointTicket;
      }
      
      private function set buyXiuLianNumPointTicket(param1:uint) : void
      {
         _antiwear.buyXiuLianNumPointTicket = param1;
      }
      
      private function get buyXiuLianNumPointTicketId() : String
      {
         return _antiwear.buyXiuLianNumPointTicketId;
      }
      
      private function set buyXiuLianNumPointTicketId(param1:String) : void
      {
         _antiwear.buyXiuLianNumPointTicketId = param1;
      }
      
      private function get needMoney() : uint
      {
         return _antiwear.needMoney;
      }
      
      private function set needMoney(param1:uint) : void
      {
         _antiwear.needMoney = param1;
      }
      
      private function get needPKPoint() : uint
      {
         return _antiwear.needPKPoint;
      }
      
      private function set needPKPoint(param1:uint) : void
      {
         _antiwear.needPKPoint = param1;
      }
      
      private function get getXiuLianValue() : uint
      {
         return _antiwear.getXiuLianValue;
      }
      
      private function set getXiuLianValue(param1:uint) : void
      {
         _antiwear.getXiuLianValue = param1;
      }
      
      private function get xiuLianNumOfOneDay() : uint
      {
         return _antiwear.xiuLianNumOfOneDay;
      }
      
      private function set xiuLianNumOfOneDay(param1:uint) : void
      {
         _antiwear.xiuLianNumOfOneDay = param1;
      }
   }
}

