package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class HuanLeZhuanPanData extends DataManagerParent
   {
      private static var _instance:HuanLeZhuanPanData;
      
      private var m_ticketPrice:uint;
      
      private var m_ticketPriceId:String;
      
      private var m_startTime:String;
      
      private var m_stopTime:String;
      
      private var m_petGift:Vector.<EquipmentVO>;
      
      private var m_domonGift:Vector.<EquipmentVO>;
      
      private var m_clptheGift:Vector.<EquipmentVO>;
      
      private var m_weaponGift:Vector.<EquipmentVO>;
      
      private var m_preciousGift:Vector.<EquipmentVO>;
      
      private var m_dataXML:XML;
      
      private var _m_turnTimes:Array = [0,0,0,0,0];
      
      private var _curLimitTimes:Array;
      
      private var _curLimitIDs:Array;
      
      public function HuanLeZhuanPanData()
      {
         super();
         m_startTime = null;
         m_stopTime = null;
         m_petGift = null;
         m_domonGift = null;
         m_clptheGift = null;
         m_weaponGift = null;
         m_preciousGift = null;
      }
      
      public static function getInstance() : HuanLeZhuanPanData
      {
         if(_instance == null)
         {
            _instance = new HuanLeZhuanPanData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         m_startTime = null;
         m_stopTime = null;
         m_ticketPriceId = null;
         ClearUtil.clearObject(m_petGift);
         m_petGift = null;
         ClearUtil.clearObject(m_domonGift);
         m_domonGift = null;
         ClearUtil.clearObject(m_clptheGift);
         m_clptheGift = null;
         ClearUtil.clearObject(m_weaponGift);
         m_weaponGift = null;
         ClearUtil.clearObject(m_preciousGift);
         m_preciousGift = null;
         super.clear();
      }
      
      public function initSaveData(param1:XML) : void
      {
         var _loc2_:XML = param1.Data[0];
         if(_loc2_.Times == null)
         {
            return;
         }
         _loc2_ = _loc2_.Times[0];
         if(_loc2_)
         {
            m_turnTimes[0] = _loc2_.@pet;
            m_turnTimes[1] = _loc2_.@domonWill;
            m_turnTimes[2] = _loc2_.@clothe;
            m_turnTimes[3] = _loc2_.@weapon;
            m_turnTimes[4] = _loc2_.@precious;
            return;
         }
      }
      
      public function CheckGiftIndexBuyRand(param1:int, param2:int) : int
      {
         var _loc6_:XML = null;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         if(param2 == 0)
         {
            _loc6_ = m_dataXML.pet[0];
         }
         if(param2 == 1)
         {
            _loc6_ = m_dataXML.domonWill[0];
         }
         if(param2 == 2)
         {
            _loc6_ = m_dataXML.clothe[0];
         }
         if(param2 == 3)
         {
            _loc6_ = m_dataXML.weapon[0];
         }
         if(param2 == 4)
         {
            _loc6_ = m_dataXML.precious[0];
         }
         _curLimitTimes = String(_loc6_.limit[0].@times).split(",");
         _curLimitIDs = String(_loc6_.limit[0].@limitid).split(",");
         var _loc4_:int = int(_loc6_.equipment.length());
         var _loc5_:int = -1;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc5_ = int(_curLimitIDs.indexOf(String(_loc6_.equipment[_loc7_].@id)));
            if(_loc5_ == -1 || m_turnTimes[param2] >= int(_curLimitTimes[_loc5_]))
            {
               _loc3_ += int(_loc6_.equipment[_loc7_].@random);
            }
            if(param1 < _loc3_)
            {
               return _loc7_;
            }
            _loc7_++;
         }
         return 0;
      }
      
      public function getTotalRandomByType(param1:int) : int
      {
         var _loc5_:XML = null;
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         if(param1 == 0)
         {
            _loc5_ = m_dataXML.pet[0];
         }
         if(param1 == 1)
         {
            _loc5_ = m_dataXML.domonWill[0];
         }
         if(param1 == 2)
         {
            _loc5_ = m_dataXML.clothe[0];
         }
         if(param1 == 3)
         {
            _loc5_ = m_dataXML.weapon[0];
         }
         if(param1 == 4)
         {
            _loc5_ = m_dataXML.precious[0];
         }
         _curLimitTimes = String(_loc5_.limit[0].@times).split(",");
         _curLimitIDs = String(_loc5_.limit[0].@limitid).split(",");
         var _loc3_:int = int(_loc5_.equipment.length());
         var _loc4_:int = -1;
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc4_ = int(_curLimitIDs.indexOf(String(_loc5_.equipment[_loc6_].@id)));
            if(_loc4_ == -1 || m_turnTimes[param1] >= int(_curLimitTimes[_loc4_]))
            {
               _loc2_ += int(_loc5_.equipment[_loc6_].@random);
            }
            _loc6_++;
         }
         return _loc2_;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startTime = m_startTime;
         _antiwear.endTime = m_stopTime;
      }
      
      public function initByXML(param1:XML) : void
      {
         m_dataXML = param1;
         this.m_startTime = String(param1.time[0].@startTime);
         this.m_stopTime = String(param1.time[0].@endTime);
         this.ticketPrice = uint(param1.goods[0].@ticketPrice);
         this.ticketPriceId = String(param1.goods[0].@ticketPriceId);
         ClearUtil.clearObject(m_petGift);
         ClearUtil.clearObject(m_domonGift);
         ClearUtil.clearObject(m_clptheGift);
         ClearUtil.clearObject(m_weaponGift);
         ClearUtil.clearObject(m_preciousGift);
         m_petGift = XMLSingle.getEquipmentVOs(m_dataXML.pet[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
         m_domonGift = XMLSingle.getEquipmentVOs(m_dataXML.domonWill[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
         m_clptheGift = XMLSingle.getEquipmentVOs(m_dataXML.clothe[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
         m_weaponGift = XMLSingle.getEquipmentVOs(m_dataXML.weapon[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
         m_preciousGift = XMLSingle.getEquipmentVOs(m_dataXML.precious[0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever());
      }
      
      public function getEquipmentVONum(param1:int) : uint
      {
         if(param1 == 0)
         {
            return m_petGift.length;
         }
         if(param1 == 1)
         {
            return m_domonGift.length;
         }
         if(param1 == 2)
         {
            return m_clptheGift.length;
         }
         if(param1 == 3)
         {
            return m_weaponGift.length;
         }
         if(param1 == 4)
         {
            return m_preciousGift.length;
         }
         return 0;
      }
      
      public function getEquipmentVOByIndex(param1:int, param2:int) : EquipmentVO
      {
         if(param1 == 0)
         {
            return m_petGift[param2];
         }
         if(param1 == 1)
         {
            return m_domonGift[param2];
         }
         if(param1 == 2)
         {
            return m_clptheGift[param2];
         }
         if(param1 == 3)
         {
            return m_weaponGift[param2];
         }
         if(param1 == 4)
         {
            return m_preciousGift[param2];
         }
         return null;
      }
      
      public function getStartTime() : String
      {
         return m_startTime;
      }
      
      public function getEndTime() : String
      {
         return m_stopTime;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
      
      private function get endTime() : String
      {
         return _antiwear.endTime;
      }
      
      private function set endTime(param1:String) : void
      {
         _antiwear.endTime = param1;
      }
      
      public function getTicketPrice() : uint
      {
         return ticketPrice;
      }
      
      public function getTicketPriceId() : String
      {
         return ticketPriceId;
      }
      
      private function get ticketPrice() : uint
      {
         return _antiwear.ticketPrice;
      }
      
      private function set ticketPrice(param1:uint) : void
      {
         _antiwear.ticketPrice = param1;
      }
      
      private function get ticketPriceId() : String
      {
         return _antiwear.ticketPriceId;
      }
      
      private function set ticketPriceId(param1:String) : void
      {
         _antiwear.ticketPriceId = param1;
      }
      
      public function get m_turnTimes() : Array
      {
         return _m_turnTimes;
      }
      
      public function set m_turnTimes(param1:Array) : void
      {
         _m_turnTimes = param1;
      }
   }
}

