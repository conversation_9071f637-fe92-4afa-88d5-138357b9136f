package UI.Buff.Buff
{
   import UI.DataManagerParent;
   import UI.Event.UIPassiveEvent;
   import UI.Players.Player;
   
   public class BuffVO extends DataManagerParent
   {
      public static const ALL_TIME_BUFF:String = "allTimeBuff";
      
      public static const ONLY_ON_LINE_BUFF:String = "onlyOnLineBuff";
      
      public static const NOT_TIME_BUFF:String = "noTimeBuff";
      
      public static const FIELD_PROTECT:String = "protect";
      
      public static const EXPERIENCE_BUFF:String = "experienceBuff";
      
      public static const SUIT_BUFF:String = "suitBuff";
      
      public static const TARGET_TWO_PLAYER:String = "twoPlayer";
      
      public static const TARGET_ONE_PLAYER:String = "onePlayer";
      
      public var className:String;
      
      public var type:String;
      
      public var field:String;
      
      public var player:Player;
      
      public var data:Object;
      
      private var _remainTime:int;
      
      private var _id:int;
      
      private var _xml:XML;
      
      public function BuffVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         player = null;
         data = null;
      }
      
      override protected function init() : void
      {
         super.init();
         data = {};
         _antiwear.id = _id;
         _antiwear.xml = _xml;
         _antiwear.remainTime = _remainTime;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get xml() : XML
      {
         return _antiwear.xml;
      }
      
      public function set xml(param1:XML) : void
      {
         _antiwear.xml = param1;
      }
      
      public function get remainTime() : int
      {
         return _antiwear.remainTime;
      }
      
      public function set remainTime(param1:int) : void
      {
         _antiwear.remainTime = param1;
         dispatchEvent(new UIPassiveEvent("changeBuffRemaineTime"));
      }
   }
}

