package UI2.SVActivity.Show
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.BuffData;
   import UI.CheatData.CheatData;
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFunction2;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.SVActivity.Data.HuanLeZhuanPanData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SVActivity.Data.TotalRechargeData;
   import YJFY.API_4399.PayAPI.DateInfor;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Circ;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   import flash.utils.setTimeout;
   
   public class HuanLeZhuanPanPanel
   {
      private var m_show:MovieClip;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:IVersionControl;
      
      private var m_huanlezhuanpanData:HuanLeZhuanPanData;
      
      private var m_giftType:int = 0;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_giftTypeBtn0:MySwitchBtnLogicShell;
      
      private var m_giftTypeBtn1:MySwitchBtnLogicShell;
      
      private var m_giftTypeBtn2:MySwitchBtnLogicShell;
      
      private var m_giftTypeBtn3:MySwitchBtnLogicShell;
      
      private var m_giftTypeBtn4:MySwitchBtnLogicShell;
      
      private var m_starBtn:ButtonLogicShell2;
      
      private var m_buffIcon:ButtonLogicShell2;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_payAPI:PayAPI;
      
      private var m_totalRechargeData:TotalRechargeData;
      
      private var m_payAPIListener:PayAPIListener;
      
      private var m_dateInforOfGetTotalRecharge:DateInfor;
      
      private var m_startBool:Boolean = false;
      
      private var _currIndex:int = 0;
      
      private var m_freeTimes:int;
      
      private var m_usedTimes:int = 0;
      
      private var _Equipments:Vector.<EquipmentVO>;
      
      private var _oldFrame:int = 0;
      
      private var _rotaSpeed:Number = 0;
      
      private var _toRotation:Number = 0;
      
      private var m_bGO:Boolean;
      
      private var _isDoing:Boolean = false;
      
      private var url:String = "http://a.4399.cn/game-id-49005.html";
      
      private var getFreeUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/lottery/query";
      
      private var useFreeUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/lottery/alter";
      
      private var tweenLite:TweenLite;
      
      public function HuanLeZhuanPanPanel()
      {
         super();
         m_versionControl = null;
         m_giftType = 4;
         m_usedTimes = 0;
         m_starBtn = new ButtonLogicShell2();
         m_buffIcon = new ButtonLogicShell2();
         m_freeTimes = 0;
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_giftTypeBtn0 = new MySwitchBtnLogicShell();
         m_giftTypeBtn1 = new MySwitchBtnLogicShell();
         m_giftTypeBtn2 = new MySwitchBtnLogicShell();
         m_giftTypeBtn3 = new MySwitchBtnLogicShell();
         m_giftTypeBtn4 = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_giftTypeBtn0);
         m_switchBtnGroup.addSwitchBtn(m_giftTypeBtn1);
         m_switchBtnGroup.addSwitchBtn(m_giftTypeBtn2);
         m_switchBtnGroup.addSwitchBtn(m_giftTypeBtn3);
         m_switchBtnGroup.addSwitchBtn(m_giftTypeBtn4);
         m_eqCells = new Vector.<Sprite>();
         m_totalRechargeData = new TotalRechargeData();
         m_payAPIListener = new PayAPIListener();
         m_payAPIListener.getRechargedMoneyErrorFun = getRechargedMoneyError;
         m_payAPIListener.getRechargedMoneySuccessFun = getRechargedMoneySuccess;
         m_payAPI = GamingUI.getInstance().getAPI4399().payAPI;
      }
      
      public function clear() : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         ClearUtil.clearObject(m_payAPIListener);
         m_payAPIListener = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_starBtn);
         m_starBtn = null;
         ClearUtil.clearObject(m_buffIcon);
         m_buffIcon = null;
         ClearUtil.clearObject(m_giftTypeBtn0);
         m_giftTypeBtn0 = null;
         ClearUtil.clearObject(m_giftTypeBtn1);
         m_giftTypeBtn1 = null;
         ClearUtil.clearObject(m_giftTypeBtn2);
         m_giftTypeBtn2 = null;
         ClearUtil.clearObject(m_giftTypeBtn3);
         m_giftTypeBtn3 = null;
         ClearUtil.clearObject(m_giftTypeBtn4);
         m_giftTypeBtn4 = null;
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         if(m_show["perSHowTxt"])
         {
            (m_show["perSHowTxt"] as TextField).removeEventListener("link",textHandler);
         }
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_dateInforOfGetTotalRecharge);
         m_dateInforOfGetTotalRecharge = null;
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(_Equipments);
         _Equipments = null;
         m_show = null;
         m_svActivitySaveData = null;
         m_svActivityPanel = null;
         m_svActivityPanel = null;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         referText();
         if(_rotaSpeed > 0)
         {
            _rotaSpeed -= 0.05;
            this.m_show["rotaMc"].rotation += _rotaSpeed;
            if(_rotaSpeed < 0.5)
            {
               _rotaSpeed = 0.5;
            }
            if(_rotaSpeed == 0.5 && Math.abs(this.m_show["rotaMc"].rotation % 360 - _toRotation) <= 1)
            {
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
               m_svActivityPanel.setButtonEnable(true);
               m_svActivityPanel.showGoodSTip(_Equipments,shareLunTan,1);
               _rotaSpeed = 0;
               _isDoing = false;
            }
         }
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:SVActivityPanel, param4:IVersionControl) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_svActivitySaveData = param2;
         m_svActivityPanel = param3;
         m_versionControl = param4;
         m_giftTypeBtn0.setShow(m_show["typeBtn_0"]);
         m_giftTypeBtn1.setShow(m_show["typeBtn_1"]);
         m_giftTypeBtn2.setShow(m_show["typeBtn_2"]);
         m_giftTypeBtn3.setShow(m_show["typeBtn_3"]);
         m_giftTypeBtn4.setShow(m_show["typeBtn_4"]);
         m_giftTypeBtn4.turnActivate();
         m_starBtn.setShow(m_show["starBtn"]);
         m_starBtn.setTipString("点击抽奖");
         m_buffIcon.setShow(m_show["buffIcon"]);
         m_buffIcon.setTipString("增加人物暴击");
         this.m_show["rotaMc"].rotation = 0;
         (m_show["perSHowTxt"] as TextField).htmlText = "<font><a href=\'event:percent\'><u>概率公示</u></a> </font>";
         (m_show["perSHowTxt"] as TextField).addEventListener("link",textHandler);
         if(m_myLoader)
         {
            throw new Error("出错了");
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/SVActivity/happyActivity.xml",getXMLSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_huanlezhuanpanData = HuanLeZhuanPanData.getInstance();
         m_huanlezhuanpanData.initByXML(param1.resultXML);
         getTotalRechargeForSomeTime();
         initshow();
         referGiftShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         trace("get xml fall");
         m_svActivityPanel.closeCurrPanel();
      }
      
      private function textHandler(param1:TextEvent) : void
      {
         var _loc2_:* = param1.text;
         if("percent" === _loc2_)
         {
            navigateToURL(new URLRequest("http://my.4399.com/forums/thread-59073578"),"_blank");
         }
      }
      
      private function initshow() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 12)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc1_ + 1)]);
            _loc1_++;
         }
      }
      
      private function referTypeBtn() : void
      {
      }
      
      private function referGiftShow() : void
      {
         var _loc1_:Equipment = null;
         var _loc3_:int = 0;
         if(m_equipments)
         {
            ClearUtil.clearObject(m_equipments);
         }
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         var _loc2_:int = int(m_huanlezhuanpanData.getEquipmentVONum(m_giftType));
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ = MyFunction2.sheatheEquipmentShell(m_huanlezhuanpanData.getEquipmentVOByIndex(m_giftType,_loc3_));
            _loc1_.addEventListener("rollOver",onOver2,false,0,true);
            _loc1_.addEventListener("rollOut",onOut2,false,0,true);
            if(_loc3_ < _loc2_)
            {
               m_eqCells[_loc3_].addChild(_loc1_);
            }
            m_equipments.push(_loc1_);
            _loc3_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:String = GamingUI.getInstance().getNewestTimeStrFromSever();
         var _loc3_:String = m_huanlezhuanpanData.getStartTime();
         var _loc2_:String = m_huanlezhuanpanData.getEndTime();
         if(!((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_loc4_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc4_,_loc2_) > 0)))
         {
            m_svActivityPanel.showWarningBox("不在活动时间内!",0);
            return;
         }
         if(m_startBool == false)
         {
            return;
         }
         if(_isDoing)
         {
            return;
         }
         switch(param1.button)
         {
            case m_giftTypeBtn0:
               m_giftType = 0;
               referGiftShow();
               break;
            case m_giftTypeBtn1:
               m_giftType = 1;
               referGiftShow();
               break;
            case m_giftTypeBtn2:
               m_giftType = 2;
               referGiftShow();
               break;
            case m_giftTypeBtn3:
               m_giftType = 3;
               referGiftShow();
               break;
            case m_giftTypeBtn4:
               m_giftType = 4;
               referGiftShow();
               break;
            case m_starBtn:
               sureChoujiang();
         }
      }
      
      private function sureGoods() : void
      {
         var equ:EquipmentVO;
         var rand:int = Math.random() * m_huanlezhuanpanData.getTotalRandomByType(m_giftType);
         _currIndex = m_huanlezhuanpanData.CheckGiftIndexBuyRand(rand,m_giftType);
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         equ = m_huanlezhuanpanData.getEquipmentVOByIndex(m_giftType,_currIndex);
         _Equipments.push(equ);
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame2();
         },null);
         this.m_show["rotaMc"].rotation = 0;
         if(_currIndex < 6)
         {
            _toRotation = 30 * _currIndex + Math.random() * 20 + 5;
         }
         else
         {
            _toRotation = -180 + ((_currIndex - 6) * 30 + Math.random() * 20 + 5);
         }
         _isDoing = true;
         _rotaSpeed = 20;
         m_svActivityPanel.setButtonEnable(false);
      }
      
      private function sureServerGoods() : void
      {
         _isDoing = true;
         m_svActivityPanel.setButtonEnable(false);
         tweenLite = TweenLite.to(this.m_show["rotaMc"],7,{
            "rotation":3600 + 30 * _currIndex + (4 + Math.random() * 27),
            "ease":Circ.easeOut,
            "onComplete":onComplete
         });
      }
      
      private function onComplete() : void
      {
         tweenLite.kill();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         m_svActivityPanel.setButtonEnable(true);
         m_svActivityPanel.showGoodSTip(_Equipments,shareLunTan,1);
         _rotaSpeed = 0;
         _isDoing = false;
         GamingUI.getInstance().refresh(16384);
      }
      
      private function addBuff(param1:String) : void
      {
         var _loc2_:AllTimeBuffVO = XMLSingle.getBuff(13010,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc2_.startDate = param1;
         _loc2_.totalTime = _loc2_.xml.@time;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc2_),BuffData.getInstance().buffDrives);
      }
      
      private function shareLunTan() : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/forums/thread-send-tagid-81260"),"_blank");
      }
      
      private function sureChoujiang() : void
      {
         if(this.m_show["rotaMc"].rotation != 0)
         {
            this.m_show["rotaMc"].rotation = 0;
         }
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
         {
            m_svActivityPanel.showWarningBox("背包空间不足,请先清理背包",0);
         }
         else if(m_freeTimes > 0)
         {
            if(m_svActivitySaveData.getHuanLFreeTimes() + int(m_totalRechargeData.getTotalRecharge() / 500) - m_svActivitySaveData.getHuanLUsedTimes() == m_freeTimes)
            {
               setUseTimeToServer();
            }
         }
         else
         {
            m_svActivityPanel.showWarningBox("是否花费" + m_huanlezhuanpanData.getTicketPrice() + "点券购买并使用暴击祝福",3,{
               "type":"buyjinhousonli1",
               "okFunction":buy
            });
         }
      }
      
      private function buy() : void
      {
         var price:uint = m_huanlezhuanpanData.getTicketPrice();
         var ticketId:String = m_huanlezhuanpanData.getTicketPriceId();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "后年幸运转盘";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            setUseTimeToServer(false);
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":7
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function refreshTotalRechargeShow() : void
      {
         m_startBool = true;
      }
      
      private function referText() : void
      {
         if(m_startBool == false)
         {
            return;
         }
         var _loc1_:TextField = m_show["totalTimes"];
         var _loc2_:TextField = m_show["times"];
         _loc1_.text = BuffFunction.getBuffRemainTimeById(13010,BuffData.getInstance().buffDrives);
         _loc2_.text = String(m_freeTimes);
      }
      
      private function getTotalRechargeForSomeTime() : void
      {
         m_payAPI.addPayAPIListener(m_payAPIListener);
         if(m_dateInforOfGetTotalRecharge == null)
         {
            m_dateInforOfGetTotalRecharge = new DateInfor(m_huanlezhuanpanData.getStartTime(),m_huanlezhuanpanData.getEndTime());
         }
         m_payAPI.getTotalRechargeFun(m_dateInforOfGetTotalRecharge);
      }
      
      private function getRechargedMoneySuccess(param1:int) : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_totalRechargeData.setTotalRecharge(param1);
         getUseFreeNumByServer();
      }
      
      private function getRechargedMoneyError() : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_svActivityPanel.showWarningBox("获取累积充值失败",0);
         m_totalRechargeData.setTotalRecharge(0);
         getUseFreeNumByServer();
      }
      
      private function getUseFreeNumByServer() : void
      {
         if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
         {
            GamingUI.getInstance().manBan.text.text = "获取数据中...";
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderFreeTimeCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorFreeTimeHandler);
         _loc1_.addEventListener("securityError",onErrorFreeTimeHandler);
         var _loc2_:URLRequest = new URLRequest(this.getFreeUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderFreeTimeCompleteHandler(param1:Event) : void
      {
         var _loc3_:Number = NaN;
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderFreeTimeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorFreeTimeHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorFreeTimeHandler);
         var _loc2_:int = int(MyJSON.decode(param1.currentTarget.data).Result);
         m_svActivitySaveData.setHuanLUsedTimes(int(MyJSON.decode(param1.currentTarget.data).LotteryTimes));
         m_freeTimes = m_svActivitySaveData.getHuanLFreeTimes() + int(m_totalRechargeData.getTotalRecharge() / 500) - m_svActivitySaveData.getHuanLUsedTimes();
         if(m_svActivitySaveData.getHuanLFreeTimes() > 0)
         {
            _loc3_ = CurrentTicketPointManager.getInstance().getTotalTicketPoint();
            if(!_loc3_ || _loc3_ < 1000)
            {
               CheatData.getInstance().addCheatDataStr("大转盘次数异常");
               CheatData.getInstance().addCheatDataStr("大转盘次数异常");
               CheatData.getInstance().addCheatDataStr("大转盘次数异常");
            }
         }
         refreshTotalRechargeShow();
      }
      
      protected function onErrorFreeTimeHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderFreeTimeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorFreeTimeHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorFreeTimeHandler);
         m_svActivityPanel.showWarningBox("连接错误",0);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
      
      private function setUseTimeToServer(param1:Boolean = true) : void
      {
         var rand:int;
         var equ:EquipmentVO;
         var eqId:Array;
         var loader:URLLoader;
         var request:URLRequest;
         var info:Object;
         var useTimesBool:Boolean = param1;
         m_bGO = useTimesBool;
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         rand = Math.random() * m_huanlezhuanpanData.getTotalRandomByType(m_giftType);
         _currIndex = m_huanlezhuanpanData.CheckGiftIndexBuyRand(rand,m_giftType);
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         equ = m_huanlezhuanpanData.getEquipmentVOByIndex(m_giftType,_currIndex);
         eqId = [];
         eqId.push(equ.id);
         _Equipments.push(equ);
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            m_huanlezhuanpanData.m_turnTimes[m_giftType]++;
            MyFunction2.getServerTimeFunction(addBuff,null,false);
            setTimeout(MyFunction2.saveGame2,1000);
         },null);
         this.m_show["rotaMc"].rotation = 0;
         if(_currIndex < 6)
         {
            _toRotation = 30 * _currIndex + Math.random() * 20 + 5;
         }
         else
         {
            _toRotation = -180 + ((_currIndex - 6) * 30 + Math.random() * 20 + 5);
         }
         loader = new URLLoader();
         loader.addEventListener("complete",setUseTimeToServerCompleteHandler);
         loader.addEventListener("ioError",onErrorsetUseTimeToServerr);
         loader.addEventListener("securityError",onErrorsetUseTimeToServerr);
         request = new URLRequest(this.useFreeUrl);
         request.method = "POST";
         request.contentType = "application/json";
         info = {};
         info.MD5 = "2B01530154A2C991";
         info.UID = GameData.getInstance().getLoginReturnData().getUid();
         info.Items = eqId;
         info.IsAdd = useTimesBool;
         request.data = MyJSON.encode(info);
         loader.load(request);
      }
      
      private function setUseTimeToServerCompleteHandler(param1:Event) : void
      {
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         param1.currentTarget.removeEventListener("complete",onLoaderFreeTimeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorFreeTimeHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorFreeTimeHandler);
         var _loc2_:int = int(MyJSON.decode(param1.currentTarget.data).Result);
         if(_loc2_ == 1)
         {
            if(m_bGO)
            {
               m_freeTimes--;
               m_svActivitySaveData.setHuanLUsedTimes(m_svActivitySaveData.getHuanLUsedTimes() + 1);
            }
            sureServerGoods();
         }
         else
         {
            m_svActivityPanel.showWarningBox("连接错误",0);
         }
      }
      
      private function onErrorsetUseTimeToServerr(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",setUseTimeToServerCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorsetUseTimeToServerr);
         param1.currentTarget.removeEventListener("securityError",onErrorsetUseTimeToServerr);
         m_svActivityPanel.showWarningBox("连接错误",0);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
   }
}

