package UI2.SVActivity.Data
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   
   public class Ji<PERSON><PERSON>HaoLiData extends DataManagerParent
   {
      private var m_startTime:String;
      
      private var m_endTime:String;
      
      private var m_giftBags:Vector.<GiftBagData_ChongZhiFanLi>;
      
      public function JiLeiHaoLiData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_startTime = null;
         m_endTime = null;
         ClearUtil.clearObject(m_giftBags);
         m_giftBags = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc2_:GiftBagData_ChongZhiFanLi = null;
         this.startTime = param1.@startTime;
         this.endTime = param1.@endTime;
         ClearUtil.clearObject(m_giftBags);
         m_giftBags = new Vector.<GiftBagData_ChongZhiFanLi>();
         var _loc3_:XMLList = param1.giftBag;
         var _loc4_:int = int(!!_loc3_ ? _loc3_.length() : 0);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = new GiftBagData_ChongZhiFanLi();
            _loc2_.initByXML(_loc3_[_loc5_]);
            m_giftBags.push(_loc2_);
            _loc5_++;
         }
      }
      
      public function getStartTime() : String
      {
         return startTime;
      }
      
      public function getEndTime() : String
      {
         return endTime;
      }
      
      public function getGiftBagNum() : uint
      {
         return m_giftBags.length;
      }
      
      public function getGiftBagByIndex(param1:int) : GiftBagData_ChongZhiFanLi
      {
         return m_giftBags[param1];
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startTime = m_startTime;
         _antiwear.endTime = m_endTime;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
      
      private function get endTime() : String
      {
         return _antiwear.endTime;
      }
      
      private function set endTime(param1:String) : void
      {
         _antiwear.endTime = param1;
      }
   }
}

