package UI.WorldBoss
{
   import flash.display.Sprite;
   
   public class View
   {
      private var m_show:Sprite;
      
      private var m_isShake:Boolean;
      
      private var m_isFlash:Boolean;
      
      public function View()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
      }
      
      public function setShow(param1:Sprite) : void
      {
         m_show = param1;
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function getIsShake() : Boolean
      {
         return m_isShake;
      }
      
      public function getIsFlash() : Boolean
      {
         return m_isFlash;
      }
      
      public function setIsShake(param1:Boolean) : void
      {
         m_isShake = param1;
      }
      
      public function setIsFlash(param1:Boolean) : void
      {
         m_isFlash = param1;
      }
   }
}

