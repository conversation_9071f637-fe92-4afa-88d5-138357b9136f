package UI.Button.SwitchBtn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SwitchBtn extends Btn
   {
      private var _isClickAble:Boolean;
      
      private var _lockSprite:Sprite;
      
      private var _isLock:Boolean;
      
      protected var _lockExtra:Object;
      
      public function SwitchBtn()
      {
         super();
         addFrameScript(0,addScript);
         addFrameScript(1,addScript);
         setIsLock(false,null);
      }
      
      public function init(param1:Boolean) : void
      {
         _isClickAble = param1;
         if(_isClickAble)
         {
            gotoTwoFrame();
         }
         else
         {
            gotoOneFrame();
         }
      }
      
      public function get isLock() : Boolean
      {
         return _isLock;
      }
      
      public function setIsLock(param1:Boolean, param2:Class = null, param3:Object = null) : void
      {
         _isLock = param1;
         _lockExtra = param3;
         if(!param1)
         {
            if(_lockSprite)
            {
               if(getChildByName(_lockSprite.name))
               {
                  removeChild(_lockSprite);
                  _lockSprite = null;
               }
               else
               {
                  _lockSprite = null;
               }
            }
         }
         else if(!_lockSprite)
         {
            _lockSprite = new Sprite();
            initLock(param2);
            _lockSprite.x = 0;
            _lockSprite.y = 0;
            addChild(_lockSprite);
         }
         else if(!getChildByName(_lockSprite.name))
         {
            initLock(param2);
            _lockSprite.x = 0;
            _lockSprite.y = 0;
            addChild(_lockSprite);
         }
      }
      
      protected function initLock(param1:Class) : void
      {
         var _loc3_:BitmapData = null;
         var _loc2_:Bitmap = null;
         if(param1)
         {
            _loc3_ = new param1() as BitmapData;
            _loc2_ = new Bitmap(_loc3_);
            _lockSprite.addChild(_loc2_);
         }
      }
      
      protected function addScript() : void
      {
         stop();
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         if(!isLock)
         {
            if(_isClickAble)
            {
               dispatchUIBtnEvent();
               gotoOneFrame();
            }
         }
         else
         {
            dispatchTipEvent();
         }
      }
      
      public function gotoTwoFrame() : void
      {
         gotoAndStop(2);
         _isClickAble = true;
      }
      
      public function gotoOneFrame() : void
      {
         gotoAndStop(1);
         _isClickAble = false;
      }
      
      public function get isClickAble() : Boolean
      {
         return _isClickAble;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         if(_lockSprite)
         {
            while(_lockSprite.numChildren > 0)
            {
               _loc1_ = _lockSprite.getChildAt(0);
               if(_loc1_ is Bitmap)
               {
                  if((_loc1_ as Bitmap).bitmapData)
                  {
                     (_loc1_ as Bitmap).bitmapData.dispose();
                  }
                  (_loc1_ as Bitmap).bitmapData = null;
               }
               _lockSprite.removeChildAt(0);
            }
         }
         _lockSprite = null;
      }
      
      protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchPanel"));
      }
      
      protected function dispatchTipEvent() : void
      {
      }
      
      public function set lockExtra(param1:Object) : void
      {
         _lockExtra = param1;
      }
   }
}

