package UI2.Midautumn
{
   import UI.EnterFrameTime;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.utils.clearTimeout;
   
   public class DiceItem extends MySprite
   {
      private var m_id:int;
      
      private var m_show:MovieClip;
      
      private var m_result:MovieClip;
      
      private var m_change:MovieClip;
      
      private var m_x:Number = 100;
      
      private var m_y:Number = 100;
      
      private var m_CrashNum:int;
      
      private var m_nX:Number;
      
      private var m_nY:Number;
      
      private var m_nTagX:Number;
      
      private var m_nTagY:Number;
      
      private var m_nTotalFrame:Number;
      
      private var m_nangle:Number;
      
      private var m_nDistance:Number;
      
      private var m_nAvgX:Number;
      
      private var m_nAvgY:Number;
      
      private var m_bGo:Boolean = false;
      
      private var m_bGo2:Boolean = false;
      
      private var m_bGo3:Boolean = false;
      
      private var m_bGo4:Boolean = false;
      
      private var m_bThree:Boolean = false;
      
      private var m_nValue:int;
      
      private var m_timeId:int;
      
      private var m_midautumn:MidautumnView;
      
      private var m_wzwView:WzwView;
      
      private var m_type:int;
      
      private var m_numX:Number;
      
      private var m_numY:Number;
      
      private var m_bEnd:Boolean = false;
      
      private var m_xList:Vector.<Number>;
      
      private var m_yList:Vector.<Number>;
      
      private var m_timeNum:int;
      
      private var m_timeAll:int;
      
      private var m_indexTime:int;
      
      private var m_bMove:int = 1;
      
      private var m_bStart:Boolean = false;
      
      private var m_bEndShow:Boolean = false;
      
      private var m_bShowMoveResult:Boolean = false;
      
      public function DiceItem()
      {
         super();
         m_xList = new Vector.<Number>();
         m_yList = new Vector.<Number>();
      }
      
      public function init(param1:int, param2:MovieClip, param3:MidautumnView = null, param4:WzwView = null) : void
      {
         m_id = param1;
         m_show = param2;
         m_midautumn = param3;
         m_wzwView = param4;
         m_result = param2["result_" + param1] as MovieClip;
         m_change = param2["change_" + param1] as MovieClip;
         m_result.gotoAndStop(String(param1));
         m_result.visible = true;
         m_change.visible = false;
         m_result.x = m_result.y = 0;
         m_change.x = m_change.y = 0;
         initPos();
      }
      
      public function setEndXY(param1:Number, param2:Number) : void
      {
         m_numX = param1;
         m_numY = param2;
      }
      
      override public function clear() : void
      {
         m_midautumn = null;
         clearTimeout(m_timeId);
         m_timeId = 0;
         ClearUtil.clearObject(m_xList);
         m_xList = null;
         ClearUtil.clearObject(m_yList);
         m_yList = null;
         super.clear();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_bStart && m_timeNum <= m_timeAll && m_bMove == 1)
         {
            m_bMove = 2;
            m_timeNum++;
            m_nTagX = 386 + 168 * Math.random();
            m_nTagY = 280 + 100 * Math.random();
            loadposlist();
         }
         else if(m_bStart && m_timeNum <= m_timeAll && m_bMove == 2 && m_nDistance > 4)
         {
            loadposlist();
         }
         else if(m_bStart && m_timeNum <= m_timeAll && m_bMove == 2 && m_nDistance <= 4)
         {
            m_bMove = 1;
         }
         else if(m_bStart && m_timeNum > m_timeAll && m_bShowMoveResult == false)
         {
            m_bStart = false;
            m_bEndShow = true;
            m_nTagX = m_numX;
            m_nTagY = m_numY;
            m_bShowMoveResult = true;
            loadposlist();
         }
         if(m_bEndShow && m_bShowMoveResult && m_nDistance > 4)
         {
            m_nTotalFrame = 1;
            loadposlist();
         }
         else if(m_bEndShow && m_bShowMoveResult && m_nDistance <= 4)
         {
            showResult();
         }
      }
      
      public function getEndShow() : Boolean
      {
         return m_bEndShow && m_result.visible;
      }
      
      private function initPos() : void
      {
         m_x = 415 + 100 * Math.random();
         m_y = 300 + 100 * Math.random();
         setPos(m_x,m_y);
      }
      
      public function setPos(param1:Number, param2:Number) : void
      {
         m_x = param1;
         m_y = param2;
         m_result.x = m_change.x = m_x;
         m_result.y = m_change.y = m_y;
      }
      
      public function hideItem() : void
      {
         m_result.visible = false;
         m_change.visible = false;
      }
      
      public function showItem() : void
      {
         m_result.visible = false;
         m_change.visible = true;
      }
      
      public function showResult() : void
      {
         m_result.visible = true;
         m_change.visible = false;
      }
      
      private function loadResult() : void
      {
         m_result.gotoAndStop(String(m_nValue));
      }
      
      public function setResult(param1:int) : void
      {
         m_nValue = param1;
      }
      
      public function getResult() : int
      {
         return m_nValue;
      }
      
      public function Calculate_BeginPos() : void
      {
         if(m_type == 1)
         {
            m_nTotalFrame = 2;
         }
         else if(m_type == 2)
         {
            m_nTotalFrame = 4;
         }
         else if(m_type == 3)
         {
            m_nTotalFrame = 6;
         }
         loadResult();
         m_nX = 366 + 48 * Math.random();
         m_nY = 354 + 37 * Math.random();
         m_nTagX = 444 + 150 * Math.random();
         m_nTagY = 231 + 134 * Math.random();
         setPos(m_nX,m_nY);
         loadposlist();
         m_bStart = true;
         m_xList.length = 0;
         m_yList.length = 0;
         m_timeNum = 0;
         m_indexTime = 0;
         m_bMove = 2;
         m_bEndShow = false;
         m_bShowMoveResult = false;
         m_timeAll = 1 + 4 * Math.random();
      }
      
      private function loadposlist() : void
      {
         m_nangle = Math.atan(Math.abs(m_nY - m_nTagY) / Math.abs(m_nX - m_nTagX));
         m_nDistance = Math.sqrt(Math.abs(m_nY - m_nTagY) * Math.abs(m_nY - m_nTagY) + Math.abs(m_nX - m_nTagX) * Math.abs(m_nX - m_nTagX));
         var _loc1_:Number = 0;
         var _loc2_:Number = 0;
         if(m_nTagX >= m_nX && m_nTagY == m_nY)
         {
            m_nAvgX = m_nTotalFrame;
            m_nAvgY = 0;
         }
         else if(m_nTagY >= m_nY && m_nTagX == m_nX)
         {
            m_nAvgX = 0;
            m_nAvgY = m_nTotalFrame;
         }
         else if(m_nTagX < m_nX && m_nTagY == m_nY)
         {
            m_nAvgX = -m_nTotalFrame;
            m_nAvgY = 0;
         }
         else if(m_nTagY < m_nY && m_nTagX == m_nX)
         {
            m_nAvgX = 0;
            m_nAvgY = -m_nTotalFrame;
         }
         else if(m_nTagX > m_nX && m_nTagY > m_nY)
         {
            _loc1_ = Math.sin(m_nangle) * m_nTotalFrame;
            _loc2_ = Math.cos(m_nangle) * m_nTotalFrame;
            m_nAvgY = _loc1_;
            m_nAvgX = _loc2_;
         }
         else if(m_nTagX < m_nX && m_nTagY > m_nY)
         {
            _loc1_ = Math.sin(m_nangle) * m_nTotalFrame;
            _loc2_ = Math.cos(m_nangle) * m_nTotalFrame;
            m_nAvgY = _loc1_;
            m_nAvgX = -_loc2_;
         }
         else if(m_nTagX > m_nX && m_nTagY < m_nY)
         {
            _loc1_ = Math.sin(m_nangle) * m_nTotalFrame;
            _loc2_ = Math.cos(m_nangle) * m_nTotalFrame;
            m_nAvgY = -_loc1_;
            m_nAvgX = _loc2_;
         }
         else if(m_nTagX < m_nX && m_nTagY < m_nY)
         {
            _loc1_ = Math.sin(m_nangle) * m_nTotalFrame;
            _loc2_ = Math.cos(m_nangle) * m_nTotalFrame;
            m_nAvgY = -_loc1_;
            m_nAvgX = -_loc2_;
         }
         m_nX += m_nAvgX;
         m_nY += m_nAvgY;
         setPos(m_nX,m_nY);
      }
      
      public function setType(param1:int) : void
      {
         m_type = param1;
      }
   }
}

