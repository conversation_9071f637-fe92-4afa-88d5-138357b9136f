package UI.MessageBox
{
   import UI.Buff.Buff.BuffVO;
   
   public class ShowBuffVOMessage
   {
      public function ShowBuffVOMessage()
      {
         super();
      }
      
      public function showMessage(param1:BuffVO, param2:MessageBox) : void
      {
         var _loc8_:String = null;
         var _loc4_:String = null;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         if(param1)
         {
            _loc8_ = "剩余时间：";
            _loc4_ = String(param1.xml.@description);
            _loc7_ = param1.remainTime;
            _loc10_ = _loc7_ / 86400;
            _loc5_ = _loc7_ - _loc10_ * 86400;
            _loc9_ = _loc5_ / 3600;
            _loc5_ -= _loc9_ * 3600;
            _loc3_ = _loc5_ / 60;
            if(_loc7_ < 0)
            {
               _loc8_ = "";
            }
            else if(!_loc10_ && !_loc9_ && !_loc3_)
            {
               _loc6_ = _loc5_ - _loc3_ * 60;
               _loc8_ += "" + _loc6_ + "秒";
            }
            else
            {
               _loc8_ += "" + (!!_loc10_ ? _loc10_ + "天" : "") + (!!_loc9_ ? _loc9_ + "小时" : "") + (!!_loc3_ ? _loc3_ + "分钟" : "");
            }
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText(_loc4_,15) + MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().toHTMLText(_loc8_,12);
         }
      }
   }
}

