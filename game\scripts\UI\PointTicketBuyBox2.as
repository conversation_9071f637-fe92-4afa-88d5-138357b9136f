package UI
{
   import UI.Button.NumberBtn.NumberBtnGroup;
   import UI.Event.UIPassiveEvent;
   import flash.events.Event;
   
   public class PointTicketBuyBox2 extends PointTicketBuyBox
   {
      protected var _numBtnGroup:NumberBtnGroup;
      
      public function PointTicketBuyBox2()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _numBtnGroup = new NumberBtnGroup();
         _numBtnGroup.x = 235;
         _numBtnGroup.y = 12;
         _numBtnGroup.isOpenInput = false;
         addChild(_numBtnGroup);
         _numBtnGroup.num = 1;
         _numBtnGroup.minNum = 1;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("changeNum",changeNum,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("changeNum",changeNum,true);
      }
      
      protected function changeNum(param1:UIPassiveEvent) : void
      {
      }
   }
}

