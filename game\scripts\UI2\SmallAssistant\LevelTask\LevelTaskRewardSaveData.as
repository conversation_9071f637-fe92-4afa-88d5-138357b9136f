package UI2.SmallAssistant.LevelTask
{
   import UI.DataManagerParent;
   
   public class LevelTaskRewardSaveData extends DataManagerParent
   {
      private var m_rewardId:String;
      
      public function LevelTaskRewardSaveData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_rewardId = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.rewardId = m_rewardId;
      }
      
      public function getRewardId() : String
      {
         return rewardId;
      }
      
      public function setRewardId(param1:String) : void
      {
         rewardId = param1;
      }
      
      private function get rewardId() : String
      {
         return _antiwear.rewardId;
      }
      
      private function set rewardId(param1:String) : void
      {
         _antiwear.rewardId = param1;
      }
   }
}

