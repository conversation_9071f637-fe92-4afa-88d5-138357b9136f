package UI
{
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.ShowLogicShell.ChangeBarLogicShell.IElapsedTimeData;
   
   public class EnterFrameTime implements IElapsedTimeData
   {
      private var _computerTime:Number = 0;
      
      private var _onLineTimeForThisInit:Number = 0;
      
      private var _gameTimeForThisInit:Number = 0;
      
      private var _thisInitTime:Number;
      
      private var _gameDelayTimeForThisInit:Number = 0;
      
      private var _addTimeOneFrame:uint;
      
      private var _addGameTimeOneFrame:uint;
      
      private var _isStop:Boolean;
      
      public function EnterFrameTime()
      {
         super();
      }
      
      public function init(param1:Number) : void
      {
         _thisInitTime = param1;
         _computerTime = param1;
      }
      
      public function getComputerTime() : Number
      {
         return _computerTime;
      }
      
      public function getOnLineTimeForThisInit() : Number
      {
         return _onLineTimeForThisInit;
      }
      
      public function getGameTimeForThisInit() : Number
      {
         return _gameTimeForThisInit;
      }
      
      public function getThisInitTime() : Number
      {
         return _thisInitTime;
      }
      
      public function getGameDelayTimeForThisInit() : Number
      {
         return _gameDelayTimeForThisInit;
      }
      
      public function getAddTimeOneFrame() : uint
      {
         return _addTimeOneFrame;
      }
      
      public function getAddGameTimeOneFrame() : uint
      {
         return _addGameTimeOneFrame;
      }
      
      public function render(param1:Number) : void
      {
         if(isNaN(_thisInitTime))
         {
            return;
         }
         _addTimeOneFrame = param1 - _computerTime;
         if(_addTimeOneFrame > ProgramStartData.getInstance().getOneThousand())
         {
            _addTimeOneFrame = ProgramStartData.getInstance().getOneThousand();
         }
         else if(_addTimeOneFrame < ProgramStartData.getInstance().get_zero())
         {
            _addTimeOneFrame = 42;
         }
         _onLineTimeForThisInit += _addTimeOneFrame;
         _computerTime = param1;
         if(_isStop == false)
         {
            _gameTimeForThisInit = _onLineTimeForThisInit - _gameDelayTimeForThisInit;
            _addGameTimeOneFrame = _addTimeOneFrame;
         }
         else
         {
            _gameDelayTimeForThisInit += _addTimeOneFrame;
            _addGameTimeOneFrame = 0;
         }
      }
      
      public function continueGame(param1:Number) : void
      {
         _isStop = false;
      }
      
      public function stopGame(param1:Number) : void
      {
         _isStop = true;
      }
      
      public function getElapsedTime() : Number
      {
         return getGameTimeForThisInit();
      }
   }
}

