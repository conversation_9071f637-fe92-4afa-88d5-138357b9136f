package UI.Buff
{
   import UI.Buff.Buff.BuffDrive;
   import UI.DataManagerParent;
   
   public class BuffData extends DataManagerParent
   {
      private static var _instance:BuffData = null;
      
      public var buffDrives:Vector.<BuffDrive>;
      
      public var buffDrives_playerOne:Vector.<BuffDrive>;
      
      public var buffDrives_playerTwo:Vector.<BuffDrive>;
      
      public function BuffData()
      {
         if(!_instance)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在了！");
      }
      
      public static function getInstance() : BuffData
      {
         if(!_instance)
         {
            _instance = new BuffData();
         }
         return _instance;
      }
      
      public function isHaveTheFieldBuff(param1:String, param2:String) : Boolean
      {
         var _loc5_:int = 0;
         var _loc3_:Boolean = false;
         var _loc4_:int = int(!!this[param2] ? this[param2].length : 0);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if((this[param2][_loc5_] as BuffDrive).buffVO.field == param1)
            {
               _loc3_ = true;
               break;
            }
            _loc5_++;
         }
         return _loc3_;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         var _loc2_:int = 0;
         if(buffDrives)
         {
            _loc1_ = int(buffDrives.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               buffDrives[_loc2_].clear();
               buffDrives[_loc2_] = null;
               _loc2_++;
            }
            buffDrives = null;
         }
         if(buffDrives_playerOne)
         {
            _loc1_ = int(buffDrives_playerOne.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               buffDrives_playerOne[_loc2_].clear();
               buffDrives_playerOne[_loc2_] = null;
               _loc2_++;
            }
         }
         buffDrives_playerOne = null;
         if(buffDrives_playerTwo)
         {
            _loc1_ = int(buffDrives_playerTwo.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               buffDrives_playerTwo[_loc2_].clear();
               buffDrives_playerTwo[_loc2_] = null;
               _loc2_++;
            }
         }
         buffDrives_playerTwo = null;
         _instance = null;
      }
      
      override protected function init() : void
      {
         super.init();
      }
   }
}

