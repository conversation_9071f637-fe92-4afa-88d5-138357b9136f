package UI.SocietySystem.SocialChatPanel
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.SocietySystem.SocietySystem;
   import YJFY.API_4399.API_4399;
   import YJFY.API_4399.Open4399ToolsAPI.ErrorData;
   import YJFY.API_4399.Open4399ToolsAPI.Open4399ToolsAPIListener;
   import YJFY.API_4399.Open4399ToolsAPI.SuccessData;
   import YJFY.KeyManager.KeyManager;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.RichTextArea.IRichTextAreaRefreshListener;
   import YJFY.RichTextArea.IconData.IconData;
   import YJFY.RichTextArea.IconData.IconData_Gif;
   import YJFY.RichTextArea.IconData.IconData_Jpg;
   import YJFY.RichTextArea.IconData.IconData_MovieClip;
   import YJFY.RichTextArea.IconData.IconType;
   import YJFY.RichTextArea.RichTextAreaLS;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteDirection;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.Font;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.clearInterval;
   import flash.utils.setTimeout;
   
   public class SocietyChatPanel implements IRichTextAreaRefreshListener
   {
      public static const const_in_societyChatPanel:String = "in";
      
      public static const const_out_societyChatPanel:String = "out";
      
      private const m_const_in_faceIconsPanel_swf:String = "MySocietyPanel.swf";
      
      private const m_const_in_faceIconsPanel_className:String = "FaceIconsPanel";
      
      private const m_const_out_faceIconsPanel_swf:String = "ExSocietyPanel.swf";
      
      private const m_const_out_faceIconsPanel_className:String = "FaceIconsPanel";
      
      private var m_societyChatType:String;
      
      private var m_outputScrollArea:ScrollSpriteLogicShell;
      
      private var m_sendBtn:ButtonLogicShell2;
      
      private var m_outputRichText:RichTextAreaLS;
      
      private var m_inputRichText:RichTextAreaLS;
      
      private var m_openFaceIconsPanelBtn:ButtonLogicShell2;
      
      private var m_faceIconsPanel:FaceIconsPanel;
      
      private var m_remainTimeOfUnableShow:MultiPlaceNumLogicShell;
      
      private var m_openFaceIconsPanelTimeOut:uint;
      
      private var m_unaleSendChatInterval:int;
      
      private var m_isUnableSendChat:Boolean;
      
      private var m_iconDatas:Vector.<IconData>;
      
      private var m_keyManager:KeyManager;
      
      private var m_sendFont:Font;
      
      private var m_open4399ToolsAPIListener:Open4399ToolsAPIListener;
      
      private var m_sendStr:String;
      
      private var m_show:MovieClip;
      
      private var m_outputText:TextField;
      
      private var m_intputText:TextField;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_societySystem:SocietySystem;
      
      private var m_societySystemXML:XML;
      
      private var m_4399API:API_4399;
      
      public function SocietyChatPanel()
      {
         super();
         m_iconDatas = new Vector.<IconData>();
         m_open4399ToolsAPIListener = new Open4399ToolsAPIListener();
         m_open4399ToolsAPIListener.checkBadWordsErrorFun = checkBadWordsError;
         m_open4399ToolsAPIListener.checkBadWordsSuccessFun = checkBadWordsSuccess;
         m_open4399ToolsAPIListener.serviceInitCompleteFun = null;
      }
      
      public function clear() : void
      {
         if(m_4399API)
         {
            m_4399API.open4399ToolsAPI.removeOpen4399ToolsAPIListener(m_open4399ToolsAPIListener);
         }
         clearInterval(m_openFaceIconsPanelTimeOut);
         ClearUtil.clearObject(m_outputScrollArea);
         m_outputScrollArea = null;
         ClearUtil.clearObject(m_sendBtn);
         m_sendBtn = null;
         ClearUtil.clearObject(m_outputRichText);
         m_outputRichText = null;
         ClearUtil.clearObject(m_inputRichText);
         m_inputRichText = null;
         ClearUtil.clearObject(m_remainTimeOfUnableShow);
         m_remainTimeOfUnableShow = null;
         ClearUtil.clearObject(m_openFaceIconsPanelBtn);
         m_openFaceIconsPanelBtn = null;
         ClearUtil.clearObject(m_faceIconsPanel);
         m_faceIconsPanel = null;
         ClearUtil.clearObject(m_iconDatas);
         m_iconDatas = null;
         ClearUtil.clearObject(m_keyManager);
         m_keyManager = null;
         m_sendFont = null;
         ClearUtil.clearObject(m_open4399ToolsAPIListener);
         m_open4399ToolsAPIListener = null;
         m_sendStr = null;
         m_show = null;
         m_outputText = null;
         m_intputText = null;
         m_myLoader = null;
         m_societySystem = null;
         m_societySystemXML = null;
         m_4399API = null;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function set4399API(param1:API_4399) : void
      {
         if(m_4399API)
         {
            m_4399API.open4399ToolsAPI.removeOpen4399ToolsAPIListener(m_open4399ToolsAPIListener);
         }
         m_4399API = param1;
         if(m_4399API)
         {
            m_4399API.open4399ToolsAPI.addOpen4399ToolsAPIListener(m_open4399ToolsAPIListener);
         }
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
         m_unaleSendChatInterval = int(m_societySystemXML.chatData[0].@unaleSendChatInterval);
      }
      
      public function setShow(param1:MovieClip, param2:String) : void
      {
         m_societyChatType = param2;
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         initShow();
      }
      
      public function refresh() : void
      {
         m_outputScrollArea.refresh();
      }
      
      public function getSendBtn() : ButtonLogicShell2
      {
         return m_sendBtn;
      }
      
      public function insertIconData(param1:IconData) : void
      {
         m_inputRichText.insertRichText(param1.getIconStr());
      }
      
      public function sendchat() : void
      {
         m_sendStr = getSendStrAndClearInputText();
         if(m_sendStr)
         {
            m_4399API.open4399ToolsAPI.checkBadWords(m_sendStr);
         }
      }
      
      public function getOutputRichText() : RichTextAreaLS
      {
         return m_outputRichText;
      }
      
      public function closeFaceIconsPanel() : void
      {
         ClearUtil.clearObject(m_faceIconsPanel);
         m_faceIconsPanel = null;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc2_:int = 0;
         if(m_isUnableSendChat)
         {
            _loc2_ = caculateRemainTime(param1);
            if(_loc2_ <= 0)
            {
               m_isUnableSendChat = false;
               m_remainTimeOfUnableShow.getShow().visible = false;
               return;
            }
            m_remainTimeOfUnableShow.showNum(_loc2_);
         }
      }
      
      private function caculateRemainTime(param1:EnterFrameTime) : int
      {
         return m_unaleSendChatInterval - (param1.getOnLineTimeForThisInit() - m_societySystem.getUnAbleSendChatTime()) / 1000;
      }
      
      private function initShow() : void
      {
         m_sendBtn = new ButtonLogicShell2();
         m_sendBtn.setShow(m_show["sendBtn"]);
         m_sendBtn.setTipString("点击发送");
         m_openFaceIconsPanelBtn = new ButtonLogicShell2();
         m_openFaceIconsPanelBtn.setShow(m_show["openFaceIconsPanelBtn"]);
         m_openFaceIconsPanelBtn.setTipString("增加表情");
         m_remainTimeOfUnableShow = new MultiPlaceNumLogicShell();
         m_remainTimeOfUnableShow.setShow(m_show["remainTimeOfUnableShow"]);
         m_remainTimeOfUnableShow.setIsShowZero(false);
         var _loc1_:int = caculateRemainTime(GamingUI.getInstance().getEnterFrameTime());
         if(_loc1_ > 0)
         {
            m_remainTimeOfUnableShow.getShow().visible = true;
            m_remainTimeOfUnableShow.showNum(_loc1_);
            m_isUnableSendChat = true;
         }
         else
         {
            m_remainTimeOfUnableShow.getShow().visible = false;
            m_isUnableSendChat = false;
         }
         var _loc2_:ScrollSpriteDirection = new ScrollSpriteDirection();
         _loc2_.setDirection("L");
         m_outputScrollArea = new ScrollSpriteLogicShell();
         m_outputScrollArea.setShow(m_show["outputArea"],_loc2_);
         m_outputScrollArea.closeMouseWheel();
         m_outputText = m_outputScrollArea.getDataLayer()["text"];
         m_outputText.embedFonts = true;
         m_outputText.selectable = false;
         m_outputText.mouseEnabled = false;
         m_intputText = m_show["inputText"];
         m_intputText.embedFonts = true;
         m_intputText.type = "input";
         m_intputText.selectable = true;
         m_outputRichText = new RichTextAreaLS();
         m_outputRichText.setMyLoader(m_myLoader);
         m_outputRichText.setShow(m_outputText);
         m_outputRichText.addRefreshListener(this);
         m_inputRichText = new RichTextAreaLS();
         m_inputRichText.setMyLoader(m_myLoader);
         m_inputRichText.setShow(m_intputText);
         m_inputRichText.setMaxChars(int(m_societySystemXML.chatData[0].@inputMaxCharNum));
         m_inputRichText.addRefreshListener(this);
         addIconData();
         addOtherIconData();
         m_keyManager = new KeyManager();
         m_keyManager.addKeyEventListener(Part1.getInstance().stage);
         m_keyManager.createKeyCombo("enter","",enterSend,null,null,null,null,null,["ENTER"]);
         m_myLoader.getClass("SecondFont.swf","SecondFont",getFontSuccess,getFontFail);
         m_myLoader.load();
      }
      
      private function getFontSuccess(param1:YJFYLoaderData) : void
      {
         Font.registerFont(param1.resultClass);
         m_sendFont = new param1.resultClass();
         var _loc2_:TextFormat = m_outputRichText.getDefaultTextformat();
         _loc2_.font = m_sendFont.fontName;
         m_outputRichText.setDefaultTextformat(_loc2_);
         _loc2_ = m_inputRichText.getDefaultTextformat();
         _loc2_.font = m_sendFont.fontName;
         m_inputRichText.setDefaultTextformat(_loc2_);
         recoverHistoryChatInfor();
         m_outputRichText.refresh();
         m_inputRichText.refresh();
      }
      
      private function getFontFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function enterSend() : void
      {
         sendchat();
      }
      
      private function getSendStrAndClearInputText() : String
      {
         var _loc1_:int = caculateRemainTime(GamingUI.getInstance().getEnterFrameTime());
         if(_loc1_ > 0)
         {
            return null;
         }
         var _loc2_:String = m_inputRichText.getRichText();
         if(_loc2_)
         {
            m_inputRichText.setRichText("");
            m_isUnableSendChat = true;
            m_remainTimeOfUnableShow.getShow().visible = true;
            m_societySystem.setUnAbleSendChatTime(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit());
         }
         return _loc2_;
      }
      
      private function addIconData() : void
      {
         var _loc6_:int = 0;
         var _loc1_:IconData = null;
         var _loc4_:String = null;
         var _loc3_:XMLList = m_societySystemXML.faceIcons[0].faceIcon;
         var _loc2_:int = int(_loc3_.length());
         var _loc5_:IconType = new IconType();
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            switch(_loc4_ = String(_loc3_[_loc6_].@iconType))
            {
               case "movieClip":
                  _loc1_ = new IconData_MovieClip(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@swfPath,_loc3_[_loc6_].@className);
                  break;
               case "jpg":
                  _loc1_ = new IconData_Jpg(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@iconUrl);
                  break;
               case "gif":
                  _loc1_ = new IconData_Gif(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@iconUrl);
                  break;
               default:
                  throw new Error("图标类型出错了！");
            }
            m_outputRichText.addIconData(_loc1_);
            m_inputRichText.addIconData(_loc1_);
            m_iconDatas.push(_loc1_);
            _loc6_++;
         }
      }
      
      private function addOtherIconData() : void
      {
         var _loc6_:int = 0;
         var _loc1_:IconData = null;
         var _loc4_:String = null;
         var _loc3_:XMLList = m_societySystemXML.otherFaceIcons[0].faceIcon;
         var _loc2_:int = int(_loc3_.length());
         var _loc5_:IconType = new IconType();
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            switch(_loc4_ = String(_loc3_[_loc6_].@iconType))
            {
               case "movieClip":
                  _loc1_ = new IconData_MovieClip(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@swfPath,_loc3_[_loc6_].@className);
                  break;
               case "jpg":
                  _loc1_ = new IconData_Jpg(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@iconUrl);
                  break;
               case "gif":
                  _loc1_ = new IconData_Gif(_loc3_[_loc6_].@iconStr,_loc3_[_loc6_].@iconName,_loc3_[_loc6_].@iconUrl);
                  break;
               default:
                  throw new Error("图标类型出错了！");
            }
            m_outputRichText.addIconData(_loc1_);
            _loc6_++;
         }
      }
      
      private function recoverHistoryChatInfor() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<NewGotChatInfor> = m_societySystem.getChatHistoryStr();
         var _loc1_:int = int(_loc2_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_outputRichText.appendRichText(_loc2_[_loc3_].getChatHeadStr(),_loc2_[_loc3_].getChatHeadFontColor(),_loc2_[_loc3_].getChatHeadFontSize());
            m_outputRichText.appendRichText(_loc2_[_loc3_].getChatStr(),_loc2_[_loc3_].getChatFontColor(),_loc2_[_loc3_].getChatFontSize());
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_);
         _loc2_ = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_openFaceIconsPanelBtn:
               m_openFaceIconsPanelTimeOut = setTimeout(openFaceIconsPanel,100);
               break;
            case m_sendBtn:
               sendchat();
         }
      }
      
      private function openFaceIconsPanel() : void
      {
         if(m_faceIconsPanel == null)
         {
            m_faceIconsPanel = new FaceIconsPanel();
            m_faceIconsPanel.setIconDatas(m_iconDatas);
            m_faceIconsPanel.setMyLoader(m_myLoader);
            m_faceIconsPanel.setSocietyChatPanel(this);
            GamingUI.getInstance().addChild(m_faceIconsPanel);
            if(m_societyChatType == "in")
            {
               m_faceIconsPanel.initShow("MySocietyPanel.swf","FaceIconsPanel");
            }
            else
            {
               if(m_societyChatType != "out")
               {
                  throw new Error("帮会聊天面板类型出错。");
               }
               m_faceIconsPanel.initShow("ExSocietyPanel.swf","FaceIconsPanel");
            }
         }
      }
      
      private function checkBadWordsError(param1:ErrorData) : void
      {
         trace("check bad words Error");
      }
      
      private function checkBadWordsSuccess(param1:SuccessData) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         if(param1.getIsHaveBadWord())
         {
            _loc2_ = int(param1.getBadWordsNum());
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               m_sendStr = m_sendStr.replace(param1.getBadWordsByIndex(_loc4_),"***");
               _loc4_++;
            }
         }
         var _loc3_:NewGotChatInfor = new NewGotChatInfor();
         m_societySystem.upChatInfor(m_sendStr,_loc3_.getUpChatTypeDataByPlayerType());
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
      }
   }
}

