package UI.AutomaticPetPanel
{
   public class SkillShowListener implements ISkillShowListener
   {
      public var showSkillCompleteFun:Function;
      
      public function SkillShowListener()
      {
         super();
      }
      
      public function clear() : void
      {
         showSkillCompleteFun = null;
      }
      
      public function showSkillComplete(param1:SkillShow) : void
      {
         if(<PERSON><PERSON>an(showSkillCompleteFun))
         {
            showSkillCompleteFun(param1);
         }
      }
   }
}

