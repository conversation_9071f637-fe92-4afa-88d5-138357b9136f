package UI.WorldBoss
{
   import UI.GamingUI;
   import UI.RankListFunction;
   import YJFY.GameData;
   import YJFY.Part1;
   import flash.utils.setTimeout;
   import unit4399.events.RankListEvent;
   
   public class WorldBossRankListFun
   {
      public function WorldBossRankListFun()
      {
         super();
      }
      
      public function clear() : void
      {
         if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
         {
            Part1.getInstance().stage.removeEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
         }
         if(!Part1.getInstance().stage.hasEventListener("rankListError"))
         {
            Part1.getInstance().stage.removeEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
         }
      }
      
      public function getUserData(param1:String, param2:uint, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         RankListFunction.getInstance().getUserData(param1,param2,param3,param4,param5,param6,param7,param8);
      }
      
      public function getOneRankListDataByUserName(param1:int, param2:String, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         var rankId:int = param1;
         var userName:String = param2;
         var getByNameBeforeFun:Function = param3;
         var getByNameAfterFun:Function = param4;
         var getByNameBeforeFunParams:Array = param5;
         var getByNameAfterFunParams:Array = param6;
         var getByNameFailFun:Function = param7;
         var getByNameFailFunParams:Array = param8;
         RankListFunction.getInstance().getOneRankListDataByUserName(rankId,userName,getByNameBeforeFun,function(param1:Array):void
         {
            var _loc4_:int = 0;
            if(param1 == null || param1.length == 0)
            {
               if(Boolean(getByNameAfterFun))
               {
                  getByNameAfterFun.apply(null,!!getByNameAfterFunParams ? getByNameAfterFunParams.push(null) : [null]);
               }
               return;
            }
            var _loc3_:int = int(param1.length);
            var _loc2_:Boolean = false;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(param1[_loc4_])
               {
                  if(param1[_loc4_].index == GameData.getInstance().getSaveFileData().index)
                  {
                     if(Boolean(getByNameAfterFun))
                     {
                        getByNameAfterFun.apply(null,!!getByNameAfterFunParams ? getByNameAfterFunParams.push(param1[_loc4_]) : [param1[_loc4_]]);
                     }
                     _loc2_ = true;
                     break;
                  }
               }
               _loc4_++;
            }
            if(!_loc2_)
            {
               if(Boolean(getByNameAfterFun))
               {
                  getByNameAfterFun.apply(null,!!getByNameAfterFunParams ? getByNameAfterFunParams.push(null) : [null]);
               }
            }
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               param1[_loc4_] = null;
               _loc4_++;
            }
            param1 = null;
         },getByNameBeforeFunParams,null,getByNameFailFun,getByNameFailFunParams);
         if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
            {
               Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
            }
            if(!Part1.getInstance().stage.hasEventListener("rankListError"))
            {
               Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
            }
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                  "data":[{
                     "userName":GameData.getInstance().getLoginReturnData().getName(),
                     "score":1,
                     "rank":1,
                     "index":GameData.getInstance().getSaveFileData().index,
                     "extra":"ABC"
                  }],
                  "apiName":"1"
               }));
            },100);
         }
      }
      
      public function getRankListData(param1:int, param2:int, param3:int, param4:Function, param5:Function, param6:Array, param7:Array, param8:Function, param9:Array) : void
      {
         RankListFunction.getInstance().getRankListData(param1,param2,param3,param4,param5,param6,param7,param8,param9);
      }
      
      public function submitScoreToRankList(param1:uint, param2:Array, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         RankListFunction.getInstance().submitScoreToRankLists(param1,param2,param3,param4,param5,param6,param7,param8);
         if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
            {
               Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
            }
            if(!Part1.getInstance().stage.hasEventListener("rankListError"))
            {
               Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
            }
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "data":[{
                  "code":"10000",
                  "curRank":1
               }],
               "apiName":"3"
            }));
         }
      }
   }
}

