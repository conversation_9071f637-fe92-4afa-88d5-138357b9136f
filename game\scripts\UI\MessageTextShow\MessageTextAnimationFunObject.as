package UI.MessageTextShow
{
   import com.greensock.easing.Circ;
   import com.greensock.easing.Elastic;
   import com.greensock.easing.Linear;
   
   public class MessageTextAnimationFunObject
   {
      public function MessageTextAnimationFunObject()
      {
         super();
      }
      
      public static function get ANIMATION_FUN_OBJECT_1() : Object
      {
         return {
            "alpha":0,
            "ease":Circ.easeOut
         };
      }
      
      public static function get ANIMATION_FUN_OBJECT_2() : Object
      {
         return {
            "scaleX":"+2",
            "scaleY":"+2",
            "ease":Elastic.easeOut
         };
      }
      
      public static function get ANIMATION_FUN_OBJECT_3() : Object
      {
         return {
            "alpha":0,
            "scaleX":"+0.5",
            "scaleY":"+0.5",
            "ease":Linear.easeNone
         };
      }
      
      public static function get ANIMATION_FUN_OBJECT_4() : Object
      {
         return {
            "alpha":0,
            "y":"+10",
            "ease":Linear.easeNone
         };
      }
      
      public static function get ANIMATION_FUN_OBJECT_5() : Object
      {
         return {
            "alpha":0,
            "y":"-10",
            "ease":Linear.easeNone
         };
      }
   }
}

