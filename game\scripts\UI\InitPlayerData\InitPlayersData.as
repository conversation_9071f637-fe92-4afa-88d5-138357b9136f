package UI.InitPlayerData
{
   import UI.Buff.Buff.BuffDrive;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MyFunction2;
   import UI.Players.PKVO;
   import UI.Players.Player;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.API_4399.SaveAPI.SaveAPI;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.GameEvent;
   import YJFY.Other.PlayerTypeDataInSaveXML;
   import YJFY.Other.SaveXMLFunction;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.Utils.ClearUtil;
   import flash.system.System;
   
   public class InitPlayersData
   {
      public var uid:String;
      
      public var idx:uint;
      
      public var player1:Player;
      
      public var player2:Player;
      
      public var player1BuffDrives:Vector.<BuffDrive>;
      
      public var player2BuffDrives:Vector.<BuffDrive>;
      
      public var nickNameData:Object;
      
      public var automaticPetsData:AutomaticPetsData;
      
      private var m_initCompleteListeners:Vector.<IInitCompleteListener>;
      
      private var m_xml:XML;
      
      private var m_idx:int;
      
      private var m_initMode:int;
      
      private var m_isInitNickName:Boolean;
      
      public function InitPlayersData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(player1);
         player1 = null;
         ClearUtil.clearObject(player2);
         player2 = null;
         ClearUtil.nullArr(player1BuffDrives);
         player1BuffDrives = null;
         ClearUtil.nullArr(player2BuffDrives);
         player2BuffDrives = null;
         ClearUtil.nullObject(nickNameData);
         nickNameData = null;
         ClearUtil.clearObject(automaticPetsData);
         automaticPetsData = null;
         ClearUtil.nullArr(m_initCompleteListeners,false,false,false);
         m_initCompleteListeners = null;
         System.disposeXML(m_xml);
         m_xml = null;
      }
      
      public function addInitCompleteListener(param1:IInitCompleteListener) : void
      {
         if(m_initCompleteListeners == null)
         {
            m_initCompleteListeners = new Vector.<IInitCompleteListener>();
         }
         m_initCompleteListeners.push(param1);
      }
      
      public function removeInitCompleteListener(param1:IInitCompleteListener) : void
      {
         if(m_initCompleteListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_initCompleteListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_initCompleteListeners.splice(_loc2_,1);
            _loc2_ = int(m_initCompleteListeners.indexOf(param1));
         }
      }
      
      public function initPlayerData(param1:XML, param2:int, param3:int, param4:Boolean) : void
      {
         m_xml = param1;
         m_idx = param2;
         m_initMode = param3;
         m_isInitNickName = param4;
         GamingUI.getInstance().lockGamingUI("加载数据中...");
         if(!GamingUI.getInstance().getNewestTimeStrFromSever())
         {
            MyFunction2.getServerTimeFunction(initPlayerData2,null,false);
         }
         else
         {
            initPlayerData2(GamingUI.getInstance().getNewestTimeStrFromSever());
         }
      }
      
      public function getSaveXML() : XML
      {
         return m_xml;
      }
      
      public function setSaveXML(param1:XML) : void
      {
         m_xml = param1;
      }
      
      private function initPlayerData2(param1:String) : void
      {
         var _loc14_:String = null;
         var _loc5_:String = null;
         var _loc16_:String = null;
         var _loc11_:* = 0;
         var _loc2_:String = null;
         var _loc21_:Object = null;
         var _loc10_:XML = m_xml;
         var _loc4_:int = m_idx;
         var _loc8_:int = m_initMode;
         var _loc6_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc3_:XML = XMLSingle.getInstance().skillXML;
         var _loc17_:XML = XMLSingle.getInstance().talentXML;
         var _loc12_:XML = XMLSingle.getInstance().privilegeXML;
         var _loc9_:XML = XMLSingle.getInstance().taskXML;
         var _loc15_:XML = XMLSingle.getInstance().buffXML;
         var _loc19_:XML = XMLSingle.getInstance().dataXML;
         _loc14_ = "";
         _loc5_ = "";
         var _loc18_:PlayerTypeDataInSaveXML = SaveXMLFunction.getPlayerTypeDataFromSaveXML(_loc10_);
         _loc14_ = _loc18_.getPlayer1Type();
         _loc5_ = _loc18_.getPlayer2Type();
         ClearUtil.clearObject(_loc18_);
         _loc18_ = null;
         var _loc22_:PKMode2VO = new PKMode2VO();
         var _loc7_:MountsVO = new MountsVO();
         var _loc20_:PKVO = new PKVO();
         switch(_loc14_)
         {
            case "SunWuKong":
               player1 = InitUI.getInstance().initMonkey(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "BaiLongMa":
               player1 = InitUI.getInstance().initDragon(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "ErLangShen":
               player1 = InitUI.getInstance().initErLangShen(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "ChangE":
               player1 = InitUI.getInstance().initChangE(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "Fox":
               player1 = InitUI.getInstance().initFox(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "TieShan":
               player1 = InitUI.getInstance().initTieShan(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "Houyi":
               player1 = InitUI.getInstance().initHouYi(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            case "ZiXia":
               player1 = InitUI.getInstance().initZiXia(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerOne");
               break;
            default:
               GameEvent.dispatchEventWith("fail");
               return;
         }
         player1.setPKMode2VO1(_loc22_);
         player1.setMountsVO(_loc7_);
         player1.setPKVO(_loc20_);
         player1.playerVO.playerUid = String(_loc10_.Data[0].@uid);
         player1BuffDrives = new Vector.<BuffDrive>();
         if(_loc10_.Data[0].hasOwnProperty("Buff") && _loc10_.Data[0].Buff[0].hasOwnProperty("one"))
         {
            InitUI.getInstance().initBuffDrives(_loc10_.Data[0].Buff[0].one[0].item,player1BuffDrives,player1,_loc15_,param1);
         }
         if(_loc5_)
         {
            switch(_loc5_)
            {
               case "SunWuKong":
                  player2 = InitUI.getInstance().initMonkey(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "BaiLongMa":
                  player2 = InitUI.getInstance().initDragon(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "ErLangShen":
                  player2 = InitUI.getInstance().initErLangShen(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "ChangE":
                  player2 = InitUI.getInstance().initChangE(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "Fox":
                  player2 = InitUI.getInstance().initFox(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "TieShan":
                  player2 = InitUI.getInstance().initTieShan(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "Houyi":
                  player2 = InitUI.getInstance().initHouYi(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               case "ZiXia":
                  player2 = InitUI.getInstance().initZiXia(_loc10_.Data[0],_loc6_,_loc3_,_loc17_,_loc19_,param1,_loc8_,"playerTwo");
                  break;
               default:
                  GameEvent.dispatchEventWith("fail");
                  return;
            }
            player2.setPKMode2VO1(_loc22_);
            player2.setMountsVO(_loc7_);
            player2.setPKVO(_loc20_);
            player2.playerVO.playerUid = String(_loc10_.Data[0].@uid);
            player2BuffDrives = new Vector.<BuffDrive>();
            if(_loc10_.Data[0].hasOwnProperty("Buff") && _loc10_.Data[0].Buff[0].hasOwnProperty("two"))
            {
               InitUI.getInstance().initBuffDrives(_loc10_.Data[0].Buff[0].two[0].item,player2BuffDrives,player2,_loc15_,param1);
            }
         }
         _loc20_.initByXML(_loc10_.Data[0].PK[0]);
         _loc7_.initFromSaveXML(_loc10_.Data[0],param1,player1,player2);
         var _loc13_:AutomaticPetsData = new AutomaticPetsData();
         _loc13_.initFromSaveXML(_loc10_.Data[0],param1,player1,player2);
         this.automaticPetsData = _loc13_;
         if(_loc10_.Data[0].hasOwnProperty("PK21"))
         {
            _loc22_.initFromSaveXML(_loc10_.Data[0].PK21[0]);
         }
         if(m_isInitNickName)
         {
            _loc16_ = decodeURIComponent(String(_loc10_.Data[0].@nickName));
            _loc11_ = uint(_loc10_.Data[0].@vL);
            _loc2_ = "whiteNickname";
            if(_loc11_)
            {
               _loc2_ = "redNickname";
            }
            if(_loc16_)
            {
               _loc21_ = {};
               _loc21_.extra = _loc16_;
               _loc21_.nicknameType = _loc2_;
               nickNameData = _loc21_;
            }
         }
         SaveAPI.saveFileDataBackup = null;
         initComplete();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
      
      private function initComplete() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<IInitCompleteListener> = !!m_initCompleteListeners ? m_initCompleteListeners.slice(0) : null;
         var _loc1_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].initComplete();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
   }
}

