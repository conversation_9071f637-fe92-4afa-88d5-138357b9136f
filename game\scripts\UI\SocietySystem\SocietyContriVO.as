package UI.SocietySystem
{
   import UI.DataManagerParent;
   
   public class SocietyContriVO extends DataManagerParent
   {
      private var m_num_contriOfExp:int;
      
      private var m_num_contriOfMoney:int;
      
      private var m_num_contriOfTicket:int;
      
      private var m_time:String;
      
      public function SocietyContriVO()
      {
         super();
         time = "";
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         num_contriOfExp = int(param1.@eN);
         num_contriOfMoney = int(param1.@mN);
         num_contriOfTicket = int(param1.@tN);
         time = String(param1.@time);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:String = null;
         var _loc2_:XML = <contri />;
         _loc1_ = !!_loc1_ ? _loc1_ : "";
         _loc2_.@eN = num_contriOfExp;
         _loc2_.@mN = num_contriOfMoney;
         _loc2_.@tN = num_contriOfTicket;
         _loc2_.@time = _loc1_;
         return _loc2_;
      }
      
      public function reSetData(param1:String) : void
      {
         num_contriOfExp = 0;
         num_contriOfMoney = 0;
         num_contriOfTicket = 0;
         time = param1;
      }
      
      public function addOneNum_contriOfExp() : void
      {
         num_contriOfExp += 1;
      }
      
      public function addOneNum_contriOfMoney() : void
      {
         num_contriOfMoney += 1;
      }
      
      public function addOneNum_contriOfTicket() : void
      {
         num_contriOfTicket += 1;
      }
      
      public function decOneNum_contriOfExp() : void
      {
         num_contriOfExp -= 1;
      }
      
      public function decOneNum_contriOfMoney() : void
      {
         num_contriOfMoney -= 1;
      }
      
      public function decOneNum_contriOfTicket() : void
      {
         num_contriOfTicket -= 1;
      }
      
      public function getNum_contriOfExp() : int
      {
         return num_contriOfExp;
      }
      
      public function getNum_contriOfMoney() : int
      {
         return num_contriOfMoney;
      }
      
      public function getNum_contriOfTicket() : int
      {
         return num_contriOfTicket;
      }
      
      public function getTime() : String
      {
         return time;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.num_contriOfExp = m_num_contriOfExp;
         _antiwear.num_contriOfMoney = m_num_contriOfMoney;
         _antiwear.num_contriOfTicket = m_num_contriOfTicket;
      }
      
      private function get num_contriOfExp() : int
      {
         return _antiwear.num_contriOfExp;
      }
      
      private function set num_contriOfExp(param1:int) : void
      {
         _antiwear.num_contriOfExp = param1;
      }
      
      private function get num_contriOfMoney() : int
      {
         return _antiwear.num_contriOfMoney;
      }
      
      private function set num_contriOfMoney(param1:int) : void
      {
         _antiwear.num_contriOfMoney = param1;
      }
      
      private function get num_contriOfTicket() : int
      {
         return _antiwear.num_contriOfTicket;
      }
      
      private function set num_contriOfTicket(param1:int) : void
      {
         _antiwear.num_contriOfTicket = param1;
      }
      
      private function get time() : String
      {
         return _antiwear.time;
      }
      
      private function set time(param1:String) : void
      {
         _antiwear.time = param1;
      }
   }
}

