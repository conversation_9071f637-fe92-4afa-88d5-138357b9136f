package UI2.Mount.MountUI
{
   public class EnergyBallShowListener
   {
      public var playEndFun:Function;
      
      public function EnergyBallShowListener()
      {
         super();
      }
      
      public function clear() : void
      {
         playEndFun = null;
      }
      
      public function playEnd(param1:EnergyBallShow) : void
      {
         if(Bo<PERSON>an(playEndFun))
         {
            playEndFun(param1);
         }
      }
   }
}

