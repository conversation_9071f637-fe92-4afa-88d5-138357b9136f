package UI2.medalPanel
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class MedalIPanelItem
   {
      private var show:MovieClip;
      
      private var panel:MedalPanel;
      
      private var medal:EquipmentVO;
      
      public function MedalIPanelItem(param1:MovieClip, param2:MedalPanel)
      {
         super();
         show = param1;
         panel = param2;
         show["btnUnequip"].addEventListener("click",unequipHandler);
         show["btnEquip"].addEventListener("click",equipHandler);
      }
      
      public function clear() : void
      {
         if(show.hasEventListener("rollOver"))
         {
            show["icon"].removeEventListener("rollOver",equipmentInfor,false);
            show["icon"].removeEventListener("rollOut",equipmentInfor,false);
         }
         show["btnUnequip"].removeEventListener("click",unequipHandler);
         show["btnEquip"].removeEventListener("click",equipHandler);
         show = null;
         panel = null;
         ClearUtil.clearObject(medal);
         medal = null;
      }
      
      public function setInfo(param1:Array, param2:Boolean) : void
      {
         if(show.hasEventListener("rollOver"))
         {
            show["icon"].removeEventListener("rollOver",equipmentInfor,false);
            show["icon"].removeEventListener("rollOut",equipmentInfor,false);
         }
         ClearUtil.clearObject(medal);
         medal = null;
         if(!param1)
         {
            show.visible = false;
            return;
         }
         show.visible = true;
         show.gotoAndStop("id_" + param1[0]);
         if(param1[1])
         {
            MyFunction.getInstance().changeSaturation(show,0);
            medal = param1[1].clone();
            if(param2)
            {
               show["btnUnequip"].visible = true;
               show["btnEquip"].visible = false;
            }
            else
            {
               show["btnUnequip"].visible = false;
               show["btnEquip"].visible = true;
            }
         }
         else
         {
            MyFunction.getInstance().changeSaturation(show,-100);
            medal = XMLSingle.getEquipmentVOByID(param1[0],XMLSingle.getInstance().equipmentXML,TimeUtil.timeStr);
            show["btnUnequip"].visible = false;
            show["btnEquip"].visible = false;
         }
         if(panel.getPlayer() != GamingUI.getInstance().player1 && panel.getPlayer() != GamingUI.getInstance().player2)
         {
            show["btnUnequip"].visible = false;
            show["btnEquip"].visible = false;
         }
         show["icon"].addEventListener("rollOver",equipmentInfor,false);
         show["icon"].addEventListener("rollOut",equipmentInfor,false);
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         if(!show)
         {
            return;
         }
         switch(param1.type)
         {
            case "rollOver":
               show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":medal}));
               break;
            case "rollOut":
               show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function unequipHandler(param1:MouseEvent) : void
      {
         panel.equipMedal(0);
      }
      
      private function equipHandler(param1:MouseEvent) : void
      {
         panel.equipMedal(medal.id);
      }
   }
}

