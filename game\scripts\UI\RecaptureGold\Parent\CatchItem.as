package UI.RecaptureGold.Parent
{
   import UI.MyFunction2;
   import UI.MyMovieClip;
   import UI.RecaptureGold.Hook.Hook;
   import flash.display.MovieClip;
   import flash.utils.getQualifiedClassName;
   
   public class CatchItem extends MyMovieClip implements ICatchItem
   {
      public function CatchItem()
      {
         super();
      }
      
      override public function clear() : void
      {
         stop();
         super.clear();
      }
      
      public function playExplodedAnimtion(param1:XML, param2:Hook) : void
      {
         var _loc3_:XML = param1.TargetData[0].item.(@classNameForCatch == getQualifiedClassName(this))[0];
         var _loc5_:Class = MyFunction2.returnClassByClassName(_loc3_.@explodedAnimation);
         var _loc4_:MovieClip = new _loc5_();
         _loc4_.x = this.x;
         _loc4_.y = this.y;
         param2.addChild(_loc4_);
      }
   }
}

