package UI
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.PKUI.PlayerDataForPK;
   import UI.Pets.Pet;
   import UI.Players.ImplicitProPlayer.ImplicitProPlayer;
   import UI.Players.ImplicitProPlayer.ImplicitProPlayerFactory;
   import UI.Players.Player;
   import UI.ShiTu.IXiuLianTargetVO;
   import UI.ShiTu.PromoteValueObject;
   import UI.ShiTu.XiuLianContent;
   import UI.Shop.ShopData;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.UIInterface.ILimitEquipmentVO;
   import UI.UIInterface.ISegmentedBar;
   import UI2.broadcast.SubmitFunction;
   import UI2.medalPanel.MedalFunction;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.filters.ColorMatrixFilter;
   import flash.text.Font;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.Dictionary;
   import flash.utils.getQualifiedClassName;
   
   public class MyFunction
   {
      public static const SIGN_BIT:int = 1;
      
      public static const CLASS_BIT:int = 2;
      
      public static const OWNER_BIT:int = 2;
      
      public static const LEVEL_BIT:int = 1;
      
      public static const OTHER_BIT:int = 2;
      
      private static var _instance:MyFunction = null;
      
      public var registerFont:Boolean;
      
      public function MyFunction()
      {
         var _loc1_:String = null;
         if(!_instance)
         {
            super();
            try
            {
               Font.registerFont(FangZhengKaTongJianTi);
               registerFont = true;
               _loc1_ = new FangZhengKaTongJianTi().fontName;
            }
            catch(error:Error)
            {
               trace("MyFunction Font error:",error.message);
            }
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在么？！");
      }
      
      public static function getInstance() : MyFunction
      {
         if(_instance == null)
         {
            _instance = new MyFunction();
         }
         return _instance;
      }
      
      public function initMyFont() : void
      {
      }
      
      public function getIDOne(param1:int = 0) : int
      {
         return param1 / Math.pow(10,7);
      }
      
      public function getIDTwo(param1:int = 0) : int
      {
         return param1 / Math.pow(10,5) % Math.pow(10,2);
      }
      
      public function getIDThree(param1:int = 0) : int
      {
         return param1 / Math.pow(10,3) % Math.pow(10,2);
      }
      
      public function getIDFour(param1:int = 0) : int
      {
         return param1 / Math.pow(10,2) % Math.pow(10,1);
      }
      
      public function getIDFive(param1:int = 0) : int
      {
         return param1 % Math.pow(10,2);
      }
      
      public function getIDFiveString(param1:int = 0) : String
      {
         return String(param1).substr(-2,2);
      }
      
      public function getTaskTypeFromID(param1:int = 0) : int
      {
         return param1 / Math.pow(10,3) % Math.pow(10,1);
      }
      
      public function calculateNextLevelID(param1:* = 0) : int
      {
         return int((int(param1 / Math.pow(10,3))).toString() + (getIDFour(param1) + 1) + getIDFiveString(param1));
      }
      
      public function calculateUpLevelID(param1:int = 0) : int
      {
         return int((int(param1 / Math.pow(10,3))).toString() + (getIDFour(param1) - 1) + getIDFiveString(param1));
      }
      
      public function calculateCurrentLevelID(param1:* = 0, param2:int = 0) : int
      {
         return int((int(param1 / Math.pow(10,3))).toString() + param2 + getIDFiveString(param1));
      }
      
      public function calculateInitLevelID(param1:int = 0) : int
      {
         return int((int(param1 / Math.pow(10,3))).toString() + 1 + getIDFiveString(param1));
      }
      
      public function calculateZeroLevelID(param1:int = 0) : int
      {
         return int((int(param1 / Math.pow(10,3))).toString() + 0 + getIDFiveString(param1));
      }
      
      public function excreteString(param1:String) : Vector.<int>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1) || param1 == "")
         {
            return new Vector.<int>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc3_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_] = int(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function excreteStringToNumber(param1:String) : Vector.<Number>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1) || param1 == "")
         {
            return new Vector.<Number>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc2_:Vector.<Number> = new Vector.<Number>();
         var _loc3_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_] = Number(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function excreteStringToString(param1:String) : Vector.<String>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1))
         {
            return new Vector.<String>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc3_:Vector.<String> = new Vector.<String>();
         var _loc2_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_.push(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function combineIdArray(param1:Vector.<int>) : String
      {
         var _loc4_:int = 0;
         var _loc2_:String = "";
         var _loc3_:int = int(param1.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc4_ == _loc3_ - 1)
            {
               _loc2_ += param1[_loc4_];
            }
            else
            {
               _loc2_ += param1[_loc4_];
               _loc2_ += "_";
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function combineStringsToArr(param1:Vector.<String>) : String
      {
         var _loc4_:int = 0;
         var _loc2_:String = "";
         var _loc3_:int = !!param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc4_ == _loc3_ - 1)
            {
               _loc2_ += param1[_loc4_];
            }
            else
            {
               _loc2_ += param1[_loc4_];
               _loc2_ += "_";
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function combineArrOrObjectToString(param1:*) : String
      {
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:String = getQualifiedClassName(param1);
         var _loc3_:String = "";
         if(_loc5_ == "Array" || _loc5_.substr(0,20) == "__AS3__.vec::Vector.")
         {
            _loc4_ = int(!!param1 ? param1.length : 0);
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               if(_loc6_ == _loc4_ - 1)
               {
                  _loc3_ += String(param1[_loc6_]);
               }
               else
               {
                  _loc3_ += String(param1[_loc6_]);
                  _loc3_ += "_";
               }
               _loc6_++;
            }
         }
         else if(_loc5_ == "Object" || _loc5_ == "flash.utils::Dictionary")
         {
            if(param1)
            {
               for(var _loc2_ in param1)
               {
                  _loc3_ += String(param1[_loc2_]);
                  _loc3_ += "_";
               }
               if(_loc3_)
               {
                  if(_loc3_.length == 1)
                  {
                     _loc3_ = "";
                  }
                  else
                  {
                     _loc3_ = _loc3_.slice(0,_loc3_.length - 1);
                  }
               }
            }
         }
         return _loc3_;
      }
      
      public function excreteStrToArrOrObj(param1:String, param2:Class) : *
      {
         var _loc5_:int = 0;
         if(!Boolean(param1))
         {
            return new param2();
         }
         var _loc6_:Array = param1.split("_");
         var _loc4_:* = new param2();
         var _loc3_:int = int(_loc6_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_.push(_loc6_[_loc5_]);
            _loc5_++;
         }
         return _loc4_;
      }
      
      public function changeNum(param1:int) : String
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:String = null;
         if(param1 < 0)
         {
            throw new Error("提供的数字小于零，不符合要求");
         }
         if(param1 < 10)
         {
            return changeSingleNum(param1);
         }
         if(param1 < 100)
         {
            _loc2_ = param1 / 10;
            _loc3_ = param1 % 10;
            if(_loc2_ == 1)
            {
               _loc4_ = "";
            }
            else
            {
               _loc4_ = changeSingleNum(_loc2_);
            }
            if(_loc3_ == 0)
            {
               _loc5_ = "";
            }
            else
            {
               _loc5_ = changeSingleNum(_loc3_);
            }
            return "" + _loc4_ + "十" + _loc5_;
         }
         throw new Error("提供的数字超过了99");
      }
      
      public function setTextFieldFormat(param1:TextField, param2:String) : void
      {
         param1.defaultTextFormat = new TextFormat(param2);
         param1.embedFonts = true;
      }
      
      private function changeSingleNum(param1:int) : String
      {
         if(param1 > 9 || param1 < 0)
         {
            throw new Error("你妹，数字大于9了或小于0了");
         }
         switch(param1)
         {
            case 0:
               return "零";
            case 1:
               return "一";
            case 2:
               return "二";
            case 3:
               return "三";
            case 4:
               return "四";
            case 5:
               return "五";
            case 6:
               return "六";
            case 7:
               return "七";
            case 8:
               return "八";
            case 9:
               return "九";
            default:
               throw new Error("没有符合的");
         }
      }
      
      public function unweaveClassNameString(param1:String) : String
      {
         var _loc2_:Array = param1.split(".");
         if(_loc2_[_loc2_.length - 1].substr(_loc2_[_loc2_.length - 1].length - 1,1) == "S")
         {
            return (_loc2_[_loc2_.length - 1] as String).slice(0,-2);
         }
         return _loc2_[_loc2_.length - 1];
      }
      
      public function changeSaturation(param1:DisplayObject, param2:Number) : void
      {
         var _loc6_:int = 0;
         var _loc4_:ColorMatrix = new ColorMatrix();
         _loc4_.adjustSaturation(param2);
         var _loc3_:Array = param1.filters;
         var _loc5_:int = !!_loc3_ ? _loc3_.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            if(_loc3_[_loc6_] is ColorMatrixFilter)
            {
               _loc3_.splice(_loc6_,1);
               _loc6_--;
               _loc5_--;
            }
            _loc6_++;
         }
         _loc3_.push(new ColorMatrixFilter(_loc4_));
         param1.filters = _loc3_;
      }
      
      public function changeSaturation2(param1:DisplayObject, param2:int, param3:int, param4:int) : void
      {
         var _loc8_:int = 0;
         var _loc6_:ColorMatrix = new ColorMatrix();
         _loc6_.adjustBrightness(param2);
         _loc6_.adjustContrast(param3);
         _loc6_.adjustSaturation(param4);
         var _loc5_:Array = param1.filters;
         var _loc7_:int = !!_loc5_ ? _loc5_.length : 0;
         _loc8_ = 0;
         while(_loc8_ < _loc7_)
         {
            if(_loc5_[_loc8_] is ColorMatrixFilter)
            {
               _loc5_.splice(_loc8_,1);
               _loc8_--;
               _loc7_--;
            }
            _loc8_++;
         }
         _loc5_.push(new ColorMatrixFilter(_loc6_));
         param1.filters = _loc5_;
      }
      
      public function combineTheEquipmentVO(param1:EquipmentVO, param2:Vector.<EquipmentVO>) : Boolean
      {
         var eqs:Vector.<EquipmentVO>;
         var t:EquipmentVO;
         var equip:EquipmentVO;
         var tdNum:int;
         var remainNum:int;
         var equipmentVO:EquipmentVO = param1;
         var equipmentArray:Vector.<EquipmentVO> = param2;
         var sortFun:* = function(param1:EquipmentVO, param2:EquipmentVO):Number
         {
            return (param2 as StackEquipmentVO).num - (param1 as StackEquipmentVO).num;
         };
         if(equipmentVO)
         {
            if(equipmentVO is StackEquipmentVO)
            {
               eqs = new Vector.<EquipmentVO>();
               for each(t in equipmentArray)
               {
                  if(t)
                  {
                     if(t.className == equipmentVO.className && t.getIsBinding() == equipmentVO.getIsBinding())
                     {
                        eqs.push(t);
                     }
                  }
               }
               if(!eqs.length)
               {
                  return false;
               }
               eqs = eqs.sort(sortFun);
               for each(equip in eqs)
               {
                  tdNum = (equip as StackEquipmentVO).maxSuperposition - (equip as StackEquipmentVO).num;
                  remainNum = (equipmentVO as StackEquipmentVO).num - tdNum;
                  if(remainNum <= 0)
                  {
                     (equip as StackEquipmentVO).num += (equipmentVO as StackEquipmentVO).num;
                     (equipmentVO as StackEquipmentVO).num = 0;
                     return true;
                  }
                  (equip as StackEquipmentVO).num = (equip as StackEquipmentVO).maxSuperposition;
                  (equipmentVO as StackEquipmentVO).num = remainNum;
               }
            }
         }
         return false;
      }
      
      public function putInEquipmentVOToEquipmentVOVector(param1:Vector.<EquipmentVO>, param2:EquipmentVO, param3:int = 0, param4:int = 0) : Boolean
      {
         var _loc12_:int = 0;
         var _loc11_:int = 0;
         var _loc13_:int = 0;
         var _loc5_:* = 0;
         var _loc10_:EquipmentVO = null;
         var _loc7_:Boolean = false;
         var _loc8_:int = 0;
         var _loc15_:Boolean = false;
         var _loc14_:int = 0;
         var _loc9_:int = 0;
         if(param2)
         {
            if(param2 is StackEquipmentVO)
            {
               if(!(param2 as StackEquipmentVO).num)
               {
                  return true;
               }
               if((param2 as StackEquipmentVO).num > (param2 as StackEquipmentVO).maxSuperposition)
               {
                  _loc12_ = (param2 as StackEquipmentVO).num;
                  _loc11_ = (param2 as StackEquipmentVO).maxSuperposition;
                  _loc13_ = Math.ceil(_loc12_ / _loc11_);
                  _loc9_ = 0;
                  while(_loc9_ < _loc13_)
                  {
                     if(_loc9_ == _loc13_ - 1)
                     {
                        _loc5_ = _loc12_ - _loc11_ * _loc9_;
                        (param2 as StackEquipmentVO).num = _loc5_;
                     }
                     else
                     {
                        _loc5_ = _loc11_;
                        _loc10_ = param2.clone();
                        (_loc10_ as StackEquipmentVO).num = _loc5_;
                        if(putInEquipmentVOToEquipmentVOVector(param1,_loc10_,param3,param4))
                        {
                        }
                     }
                     _loc9_++;
                  }
               }
            }
            _loc7_ = false;
            _loc8_ = 0;
            if(!MyFunction.getInstance().combineTheEquipmentVO(param2,param1))
            {
               _loc15_ = false;
               _loc14_ = int(param1.length);
               _loc9_ = 0;
               while(_loc9_ < _loc14_)
               {
                  if(param1[_loc9_] == null)
                  {
                     _loc8_++;
                     if(_loc8_ > param4)
                     {
                        _loc15_ = true;
                        if(!param3)
                        {
                           param1[_loc9_] = param2.clone();
                        }
                        else
                        {
                           param1[_loc9_] = param2;
                        }
                        _loc7_ = true;
                        break;
                     }
                  }
                  _loc9_++;
               }
               if(!_loc15_)
               {
                  _loc7_ = false;
               }
            }
            else
            {
               _loc7_ = true;
            }
         }
         else
         {
            _loc7_ = false;
         }
         return _loc7_;
      }
      
      public function refreshEquipmentVOToEquipmentVOVector(param1:Vector.<EquipmentVO>, param2:EquipmentVO, param3:EquipmentVO, param4:Boolean = false) : Boolean
      {
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:Boolean = false;
         if(param2 && param3)
         {
            _loc5_ = int(param1.length);
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               if(param1[_loc7_] && param1[_loc7_].isPutInOperate && getQualifiedClassName(param1[_loc7_]) == getQualifiedClassName(param3))
               {
                  param1[_loc7_] = null;
                  if(!param4)
                  {
                     param1[_loc7_] = param2;
                     param1[_loc7_].isPutInOperate = false;
                  }
                  _loc6_ = true;
                  break;
               }
               _loc7_++;
            }
         }
         else
         {
            _loc6_ = false;
         }
         return _loc6_;
      }
      
      public function resethEquipmentVOVectorToPutOut(param1:Vector.<EquipmentVO>, param2:EquipmentVO = null) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(param1.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1[_loc4_] && param1[_loc4_].isPutInOperate && (!param2 || getQualifiedClassName(param1[_loc4_]) == getQualifiedClassName(param2)))
            {
               param1[_loc4_].isPutInOperate = false;
            }
            _loc4_++;
         }
      }
      
      public function refreshXiuLianTargetVO(param1:IXiuLianTargetVO) : void
      {
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc8_:int = 0;
         var _loc7_:XiuLianContent = null;
         var _loc6_:String = null;
         var _loc4_:PromoteValueObject = null;
         var _loc2_:PromoteValueObject = null;
         var _loc3_:Dictionary = new Dictionary();
         _loc5_ = !!param1.xiuLianContents ? param1.xiuLianContents.length : 0;
         _loc10_ = 0;
         while(_loc10_ < _loc5_)
         {
            _loc7_ = param1.xiuLianContents[_loc10_];
            _loc8_ = int(_loc7_.addAttributes.length);
            _loc9_ = 0;
            while(_loc9_ < _loc8_)
            {
               _loc6_ = _loc7_.addAttributes[_loc9_];
               _loc4_ = _loc7_.addAttributeValueObjects[_loc9_];
               if(!Boolean(_loc3_[_loc6_]))
               {
                  _loc2_ = _loc4_.getOriginalValueObject();
                  if(param1[_loc6_] is Function)
                  {
                     param1[_loc6_](_loc2_);
                  }
                  else
                  {
                     param1[_loc6_] = _loc2_.value;
                  }
                  _loc2_.clear();
                  _loc3_[_loc6_] = 1;
               }
               if(param1[_loc6_] is Function)
               {
                  param1[_loc6_](_loc4_);
               }
               else
               {
                  param1[_loc6_] = _loc4_.value;
               }
               _loc9_++;
            }
            _loc10_++;
         }
      }
      
      public function refreshPlayer(param1:Player, param2:int) : void
      {
         var _loc26_:int = 0;
         var _loc14_:int = 0;
         var _loc19_:int = 0;
         var _loc5_:int = 0;
         var _loc28_:WeaponEquipmentVO = null;
         var _loc13_:NecklaceEquipmentVO = null;
         var _loc17_:GourdEquipmentVO = null;
         var _loc9_:ClothesEquipmentVO = null;
         var _loc6_:ForeverFashionEquipmentVO = null;
         var _loc32_:FashionEquipmentVO = null;
         var _loc31_:MedalEquipmentVO = null;
         var _loc33_:Object = null;
         var _loc24_:PreciousEquipmentVO = null;
         var _loc34_:ImplicitProPlayer = null;
         var _loc25_:XML = null;
         var _loc7_:int = 0;
         var _loc22_:int = 0;
         var _loc35_:XML = null;
         var _loc27_:int = 0;
         var _loc36_:PreciousEquipmentVO = null;
         if(!param1)
         {
            return;
         }
         if(!param1.playerVO)
         {
            return;
         }
         param1.playerVO.weaponAttack = 0;
         param1.playerVO.petAddAttack = 0;
         param1.playerVO.suitAttack = 0;
         param1.playerVO.otherAttack = 0;
         param1.playerVO.clothesDefence = 0;
         param1.playerVO.suitDefence = 0;
         param1.playerVO.petAddDefence = 0;
         param1.playerVO.otherDefence = 0;
         param1.playerVO.addBloodRate = 0;
         param1.playerVO.addMagicRate = 0;
         param1.playerVO.addRenPinRate = 0;
         param1.playerVO.addDoubleExp = 0;
         param1.playerVO.addAvgMagic = 0;
         param1.playerVO.addAvgShanbi = 0;
         param1.playerVO.addAvgShanbi2 = 0;
         param1.playerVO.addDownHp = 0;
         param1.playerVO.otherRiotValue = 0;
         param1.playerVO.otherMaxMagic = 0;
         param1.playerVO.otherRegHp = 0;
         param1.playerVO.otherRegMp = 0;
         param1.playerVO.otherRenPin = 0;
         param1.playerVO.otherOffensiveValue = 0;
         param1.playerVO.otherCriticalRate = 0;
         param1.playerVO.otherHit = 0;
         param1.playerVO.otherDodge = 0;
         param1.playerVO.petLowHp = 0;
         param1.playerVO.PetPressSiveRegHp = 0;
         param1.playerVO.PetPressSiveKuangBao = 0;
         param1.playerVO.PetPressSiveXueNu = 0;
         param1.playerVO.petLowXueNu = 0;
         param1.playerVO.addHitPet = 0;
         param1.playerVO.addFbRate = 0;
         param1.playerVO.addBjRate = 0;
         param1.playerVO.addMzRate = 0;
         param1.playerVO.addMingzhongPet = 0;
         param1.playerVO.addSbRate = 0;
         param1.playerVO.addGjRate = 0;
         param1.playerVO.addDownDef = 0;
         param1.playerVO.addChangeRp = 0;
         param1.playerVO.addAvgBaoji = 0;
         param1.playerVO.addAvgBaoji2 = 0;
         param1.playerVO.petAddBloodVolume = 0;
         param1.playerVO.otherBloodVolume = 0;
         param1.playerVO.renPin1 = 0;
         param1.playerVO.renPin_foreverFashionAdd = 0;
         param1.playerVO.eqScore = 0;
         param1.playerVO.clear_FashionAdd();
         param1.playerVO.clear_MagicAdd();
         for each(var _loc3_ in param1.playerVO.inforEquipmentVOs)
         {
            if(!_loc3_)
            {
               continue;
            }
            switch(_loc3_.equipmentType)
            {
               case "precious":
                  break;
               case "weapon":
                  _loc28_ = _loc3_ as WeaponEquipmentVO;
                  param1.playerVO.weaponAttack += _loc28_.attack;
                  param1.playerVO.renPin1 += _loc28_.rengPin;
                  param1.playerVO.eqScore += _loc28_.attack * 0.2 * 5 / (_loc28_.maxAttack + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(_loc28_));
                  _loc14_ = int(_loc28_.addPlayerSaveAttr.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     var _loc37_:* = _loc28_.addPlayerSaveAttr[_loc26_];
                     var _loc38_:* = param1.playerVO[_loc37_] + _loc28_.addPlayerSaveAttrVals[_loc26_];
                     param1.playerVO[_loc37_] = _loc38_;
                     _loc26_++;
                  }
                  break;
               case "necklace":
                  _loc13_ = _loc3_ as NecklaceEquipmentVO;
                  param1.playerVO.otherCriticalRate += _loc13_.criticalRate;
                  param1.playerVO.renPin1 += _loc13_.rengPin;
                  param1.playerVO.eqScore += _loc13_.criticalRate * 0.2 * 5 / (_loc13_.maxCriticalRate + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(_loc13_));
                  _loc14_ = int(_loc13_.addPlayerSaveAttr.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     _loc38_ = _loc13_.addPlayerSaveAttr[_loc26_];
                     _loc37_ = param1.playerVO[_loc38_] + _loc13_.addPlayerSaveAttrVals[_loc26_];
                     param1.playerVO[_loc38_] = _loc37_;
                     _loc26_++;
                  }
                  break;
               case "gourd":
                  _loc17_ = _loc3_ as GourdEquipmentVO;
                  param1.playerVO.otherMaxMagic += _loc17_.maxMagic;
                  param1.playerVO.renPin1 += _loc17_.rengPin;
                  param1.playerVO.eqScore += _loc17_.maxMagic * 0.2 * 5 / (_loc17_.maxMaxMagic + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(_loc17_));
                  _loc14_ = int(_loc17_.addPlayerSaveAttr.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     _loc37_ = _loc17_.addPlayerSaveAttr[_loc26_];
                     _loc38_ = param1.playerVO[_loc37_] + _loc17_.addPlayerSaveAttrVals[_loc26_];
                     param1.playerVO[_loc37_] = _loc38_;
                     _loc26_++;
                  }
                  break;
               case "clothes":
                  _loc9_ = _loc3_ as ClothesEquipmentVO;
                  param1.playerVO.clothesDefence += _loc9_.defence;
                  param1.playerVO.otherRiotValue += _loc9_.riot;
                  param1.playerVO.renPin1 += _loc9_.rengPin;
                  param1.playerVO.eqScore += _loc9_.defence * 0.2 * 5 / (_loc9_.maxDefence + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(_loc9_,"upgradeValue"));
                  param1.playerVO.eqScore += !!_loc9_.maxRiot ? _loc9_.riot * 0.2 * 5 / (_loc9_.maxRiot + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(_loc9_,"upgradeValue2")) : 0;
                  _loc14_ = int(_loc9_.addPlayerSaveAttr.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     _loc38_ = _loc9_.addPlayerSaveAttr[_loc26_];
                     _loc37_ = param1.playerVO[_loc38_] + _loc9_.addPlayerSaveAttrVals[_loc26_];
                     param1.playerVO[_loc38_] = _loc37_;
                     _loc26_++;
                  }
                  break;
               case "forever_fashion":
                  _loc6_ = _loc3_ as ForeverFashionEquipmentVO;
                  _loc14_ = int(_loc6_.addPlayerAttributes.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     _loc37_ = _loc6_.addPlayerAttributes[_loc26_];
                     _loc38_ = param1.playerVO[_loc37_] + _loc6_.addPlayerAttributeValues[_loc26_];
                     param1.playerVO[_loc37_] = _loc38_;
                     _loc26_++;
                  }
                  break;
               case "fashion":
                  _loc32_ = _loc3_ as FashionEquipmentVO;
                  _loc14_ = int(_loc32_.addPlayerAttributes.length);
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     _loc38_ = _loc32_.addPlayerAttributes[_loc26_];
                     _loc37_ = param1.playerVO[_loc38_] + _loc32_.addPlayerAttributeValues[_loc26_];
                     param1.playerVO[_loc38_] = _loc37_;
                     _loc26_++;
                  }
                  break;
               case "medal":
                  _loc31_ = _loc3_ as MedalEquipmentVO;
                  _loc14_ = int(_loc31_.addPlayerAttributes.length);
                  _loc33_ = {};
                  _loc26_ = 0;
                  while(_loc26_ < _loc14_)
                  {
                     if(!_loc33_[_loc31_.addPlayerAttributes[_loc26_]])
                     {
                        _loc33_[_loc31_.addPlayerAttributes[_loc26_]] = 0;
                     }
                     _loc33_[_loc31_.addPlayerAttributes[_loc26_]] = Math.max(_loc31_.addPlayerAttributeValues[_loc26_],_loc33_[_loc31_.addPlayerAttributes[_loc26_]]);
                     _loc26_++;
                  }
                  for each(var _loc29_ in _loc33_)
                  {
                     param1.playerVO[_loc29_] += _loc33_[_loc29_];
                  }
                  break;
            }
         }
         for each(var _loc20_ in param1.playerVO.inforEquipmentVOs)
         {
            if(_loc20_ && _loc20_.equipmentType == "precious")
            {
               _loc24_ = _loc20_ as PreciousEquipmentVO;
               _loc14_ = int(_loc24_.addPlayerSaveAttr.length);
               _loc26_ = 0;
               while(_loc26_ < _loc14_)
               {
                  _loc37_ = _loc24_.addPlayerSaveAttr[_loc26_];
                  var _loc40_:* = param1.playerVO[_loc37_] + _loc24_.addPlayerSaveAttrVals[_loc26_];
                  param1.playerVO[_loc37_] = _loc40_;
                  _loc26_++;
               }
               _loc14_ = int(_loc24_.basisAttr.length);
               _loc26_ = 0;
               while(_loc26_ < _loc14_)
               {
                  if(_loc24_.basisAttr[_loc26_] == "addrenpin")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addRenPinRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addRenPinRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addmagic")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addMagicRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addMagicRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addhp")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addBloodRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addBloodRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addfangbao")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addFbRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addFbRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addshanbi")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addSbRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                        if(param1.playerVO.addSbRate > 0.1)
                        {
                           param1.playerVO.addSbRate = 0.1;
                        }
                     }
                     else
                     {
                        param1.playerVO.addSbRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addbaoji")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addBjRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addBjRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addmingzhong")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addMzRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addMzRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addgongji")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.addGjRate = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.addGjRate = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  else if(_loc24_.basisAttr[_loc26_] == "addshanghai")
                  {
                     if(_loc24_.level > 0)
                     {
                        param1.playerVO.otherAttack = _loc24_.basisAttrValue[_loc26_] * 0.01 + _loc24_.basisUpValue[_loc26_] * 0.01;
                     }
                     else
                     {
                        param1.playerVO.otherAttack = _loc24_.basisAttrValue[_loc26_] * 0.01;
                     }
                  }
                  _loc26_++;
               }
               _loc14_ = int(_loc24_.sAttrName.length);
               _loc26_ = 0;
               while(_loc26_ < _loc14_)
               {
                  if(_loc24_.sAttrName[_loc26_] == "doubleexpgold")
                  {
                     param1.playerVO.addDoubleExp = _loc24_.sAttrValue[_loc26_];
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "increaseAttack")
                  {
                     param1.playerVO.addAvgMagic = param1.playerVO.maxMagic / _loc24_.sAvgValue[_loc26_] * _loc24_.sAttrValue[_loc26_];
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "increaseShanbi")
                  {
                     param1.playerVO.addAvgShanbi = _loc24_.sAttrValue[_loc26_];
                     param1.playerVO.addAvgShanbi2 = _loc24_.sAvgValue[_loc26_];
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "dincreasehp")
                  {
                     param1.playerVO.addDownHp = _loc24_.sAttrValue[_loc26_] * 0.01;
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "dincreaseDef")
                  {
                     param1.playerVO.addDownDef = _loc24_.sAttrValue[_loc26_] * 0.01;
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "increaseBaoji")
                  {
                     param1.playerVO.addAvgBaoji = _loc24_.sAttrValue[_loc26_];
                     param1.playerVO.addAvgBaoji2 = _loc24_.sAvgValue[_loc26_];
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "changerenpin")
                  {
                     param1.playerVO.addChangeRp = _loc24_.sAttrValue[_loc26_];
                  }
                  else if(_loc24_.sAttrName[_loc26_] == "increaseMingzhong")
                  {
                     param1.playerVO.increaseMingzhong = _loc24_.sAvgValue[_loc26_];
                     param1.playerVO.increaseMingzhong2 = _loc24_.sAttrValue[_loc26_] * 0.01;
                  }
                  _loc26_++;
               }
            }
         }
         var _loc10_:Object = MedalFunction.getInstance().getALlMedalAttribute(param1.playerVO.medalEquipmentVOs);
         for(var _loc16_ in _loc10_)
         {
            param1.playerVO[_loc16_] += _loc10_[_loc16_];
         }
         param1.playerVO.clearImplicitProPlayer();
         var _loc30_:ImplicitProPlayerFactory = new ImplicitProPlayerFactory();
         var _loc21_:Vector.<String> = MyFunction.getInstance().excreteStringToString(XMLSingle.getInstance().buffXML.implicitProPlayers[0].@suitIds);
         var _loc4_:int = int(_loc21_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc25_ = XMLSingle.getInstance().buffXML.implicitProPlayers[0].implicitProPlayer.(@id == _loc21_[_loc7_])[0];
            if(_loc25_ != null)
            {
               if(!param1.playerVO.getImplicitProPlayer(_loc21_[_loc7_]))
               {
                  _loc34_ = _loc30_.getAndInitOneImplicitProPlayer(_loc25_);
                  param1.playerVO.addImplicitProPlayer(_loc34_);
               }
            }
            _loc7_++;
         }
         ClearUtil.clearObject(_loc21_);
         _loc21_ = null;
         for each(var _loc8_ in param1.playerVO.inforEquipmentVOs)
         {
            if(_loc8_ && _loc8_ is AbleEquipmentVO && Boolean((_loc8_ as AbleEquipmentVO).implictProPlayerId))
            {
               _loc25_ = XMLSingle.getInstance().buffXML.implicitProPlayers[0].implicitProPlayer.(@id == (_loc8_ as AbleEquipmentVO).implictProPlayerId)[0];
               if(_loc25_ != null)
               {
                  if(!param1.playerVO.getImplicitProPlayer((_loc8_ as AbleEquipmentVO).implictProPlayerId))
                  {
                     _loc34_ = _loc30_.getAndInitOneImplicitProPlayer(_loc25_);
                     param1.playerVO.addImplicitProPlayer(_loc34_);
                  }
               }
            }
         }
         ClearUtil.clearObject(_loc30_);
         _loc30_ = null;
         for each(var _loc11_ in param1.playerVO.skillVOs)
         {
            if(_loc11_)
            {
               if(_loc11_.type == "playerActive")
               {
                  (_loc11_ as PlayerActiveSkillVO).coolDown = (_loc11_ as PlayerActiveSkillVO).originalCoolDown;
                  (_loc11_ as PlayerActiveSkillVO).nextCoolDown = (_loc11_ as PlayerActiveSkillVO).originalNextCoolDown;
               }
            }
         }
         var _loc18_:Boolean = false;
         if(!param1.playerVO.pet || !param1.playerVO.pet.petEquipmentVO || !param1.playerVO.pet.petEquipmentVO.essentialPercent || !param1.playerVO.pet.petEquipmentVO.passiveSkillVOs)
         {
            _loc18_ = true;
         }
         if(!_loc18_)
         {
            _loc22_ = param1.playerVO.pet.petEquipmentVO.id;
            if(_loc22_ == 10400120 || _loc22_ == 10400220 || _loc22_ == 10400320 || _loc22_ == 10400421)
            {
               _loc35_ = XMLSingle.getInstance().equipmentXML.item.(@id == _loc22_)[0];
               if(_loc35_)
               {
                  param1.playerVO.addHitPet = Number(_loc35_.@addHitPet);
               }
            }
            for each(var _loc15_ in param1.playerVO.pet.petEquipmentVO.passiveSkillVOs)
            {
               if(!_loc15_)
               {
                  continue;
               }
               if(_loc15_.type != "petPassive")
               {
                  throw new Error("宠物被动技能中错误的存在着其他技能");
               }
               switch(_loc15_.className)
               {
                  case "PetSkill_GuWu":
                     param1.playerVO.petAddAttack = (_loc15_ as PetPassiveSkillVO).value;
                     break;
                  case "PetSkill_JiNu":
                     param1.playerVO.otherCriticalRate += (_loc15_ as PetPassiveSkillVO).value;
                     break;
                  case "PetSkill_ShouHu":
                     param1.playerVO.petAddDefence = (_loc15_ as PetPassiveSkillVO).value;
                     break;
                  case "PetSkill_JianZhuang":
                     _loc27_ = param1.playerVO.bloodVolume;
                     param1.playerVO.petAddBloodVolume = 0;
                     param1.playerVO.petAddBloodVolume = Math.ceil(param1.playerVO.getPetAddTargetBloodVolume() * (_loc15_ as PetPassiveSkillVO).value / 100);
                     break;
                  case "PetSkill_MiShuZhangWo":
                     param1.playerVO.otherMaxMagic += Math.ceil(param1.playerVO.getPetAddTargetMaxMagic() * ((_loc15_ as PetPassiveSkillVO).value / 100));
                     break;
                  case "PetSkill_AoYiGuangHuang":
                     for each(var _loc12_ in param1.playerVO.skillVOs)
                     {
                        if(_loc12_)
                        {
                           if(_loc12_.type == "playerActive")
                           {
                              if((_loc12_ as PlayerActiveSkillVO).originalCoolDown != 0)
                              {
                                 (_loc12_ as PlayerActiveSkillVO).coolDown -= (_loc15_ as PetPassiveSkillVO).value;
                                 if((_loc12_ as PlayerActiveSkillVO).coolDown < 2)
                                 {
                                    (_loc12_ as PlayerActiveSkillVO).coolDown = 2;
                                 }
                              }
                              if((_loc12_ as PlayerActiveSkillVO).originalNextCoolDown != 0)
                              {
                                 (_loc12_ as PlayerActiveSkillVO).nextCoolDown -= (_loc15_ as PetPassiveSkillVO).value;
                                 if((_loc12_ as PlayerActiveSkillVO).nextCoolDown < 2)
                                 {
                                    (_loc12_ as PlayerActiveSkillVO).nextCoolDown = 2;
                                 }
                              }
                           }
                        }
                     }
                     break;
                  case "PetSkill_GuBen":
                     param1.playerVO.otherRiotValue += (_loc15_ as PetPassiveSkillVO).value / 100;
                     break;
                  case "PetSkill_ShanBi":
                     param1.playerVO.dodge_PetPassiveAdd += (_loc15_ as PetPassiveSkillVO).value / 100;
                     break;
                  case "PetSkill_YiShuJingTong":
                     break;
                  case "PetSkill_ZhiShang":
                     break;
                  case "PetSkill_Mingzhong":
                     param1.playerVO.addMingzhongPet += (_loc15_ as PetPassiveSkillVO).value / 100;
                     break;
                  case "PetSkill_Xuenu":
                     param1.playerVO.petLowXueNu = (_loc15_ as PetPassiveSkillVO).value;
                     param1.playerVO.PetPressSiveXueNu = (_loc15_ as PetPassiveSkillVO).bloodPerLow;
                     break;
                  case "PetSkill_Kuangbao":
                     param1.playerVO.PetPressSiveKuangBao = (_loc15_ as PetPassiveSkillVO).value;
                     break;
                  case "PetSkill_Shixue":
                     param1.playerVO.PetPressSiveRegHp = (_loc15_ as PetPassiveSkillVO).bloodPerLow;
                     param1.playerVO.petLowHp = (_loc15_ as PetPassiveSkillVO).value;
                     break;
               }
            }
         }
         for each(var _loc23_ in param1.playerVO.inforEquipmentVOs)
         {
            if(_loc23_ && _loc23_.equipmentType == "precious")
            {
               _loc36_ = _loc23_ as PreciousEquipmentVO;
               _loc14_ = int(_loc36_.sAttrName.length);
               _loc26_ = 0;
               while(_loc26_ < _loc14_)
               {
                  if(_loc36_.sAttrName[_loc26_] == "increaseAttack")
                  {
                     param1.playerVO.addAvgMagic = param1.playerVO.maxMagic / _loc36_.sAvgValue[_loc26_] * _loc36_.sAttrValue[_loc26_];
                  }
                  _loc26_++;
               }
            }
         }
         if(param2)
         {
            _loc19_ = Math.round(param1.playerVO.magicPercent * param1.playerVO.maxMagic);
         }
         if(param2)
         {
            _loc5_ = param1.playerVO.bloodPercent * param1.playerVO.bloodVolume;
         }
         if(param2)
         {
            param1.playerVO.magicPercent = _loc19_ / param1.playerVO.maxMagic;
            param1.playerVO.bloodPercent = _loc5_ / param1.playerVO.bloodVolume;
         }
      }
      
      public function refreshPet(param1:PetEquipmentVO) : void
      {
         if(!param1)
         {
            return;
         }
         if(!param1.passiveSkillVOs)
         {
            return;
         }
         var _loc2_:int = int(param1.passiveSkillVOs.length);
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.passiveSkillVOs[_loc3_])
            {
               if(!param1.passiveSkillVOs[_loc3_] is PetPassiveSkillVO)
               {
                  throw new Error("宠物被动技能中加入了不应该有的技能.");
               }
               (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).originalValue + (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).promoteValue;
            }
            _loc3_++;
         }
         (param1.activeSkillVO as PetActiveSkillVO).passiveAddHurt = 0;
         (param1.activeSkillVO as PetActiveSkillVO).passiveCoolDown = 0;
         (param1.activeSkillVO as PetActiveSkillVO).passiveSkillChangeEssenseCost = 0;
         (param1.activeSkillVO as PetActiveSkillVO).passiveYiShuJinTong = 0;
         (param1.activeSkillVO as PetActiveSkillVO).diePrecentHuanhua = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.passiveSkillVOs[_loc3_])
            {
               if(!param1.passiveSkillVOs[_loc3_] is PetPassiveSkillVO)
               {
                  throw new Error("宠物被动技能中加入了不应该有的技能.");
               }
               if((param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).passiveType == param1.talentVO.talentType)
               {
                  (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value = Math.ceil((1 + param1.talentVO.promoteValue / 100) * (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value);
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_FaShuZengQiang" && param1.essentialPercent)
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveAddHurt = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_JiNengAoYi" && param1.essentialPercent)
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveCoolDown = -(param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_JiNengLingWu" && param1.essentialPercent)
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveSkillChangeEssenseCost = -(param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_YiShuJingTong")
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveYiShuJinTong = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_YiShuJingTong2")
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveYiShuJinTong2 = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_YiShuJingTong3")
               {
                  (param1.activeSkillVO as PetActiveSkillVO).diePrecentHuanhua = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
               if(param1.passiveSkillVOs[_loc3_].className == "PetSkill_YiShuJingTong4")
               {
                  (param1.activeSkillVO as PetActiveSkillVO).passiveYiShuJinTong = (param1.passiveSkillVOs[_loc3_] as PetPassiveSkillVO).value;
               }
            }
            _loc3_++;
         }
      }
      
      public function setSegmentedBar(param1:Number, param2:Vector.<ISegmentedBar>) : void
      {
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:int = int(param2.length);
         if(param1 < 0 || param1 > _loc3_)
         {
            throw new Error("数过大或过小");
         }
         var _loc6_:int = param1;
         _loc7_ = 0;
         while(_loc7_ < _loc3_ + 1)
         {
            if(_loc6_ == _loc7_)
            {
               _loc4_ = 0;
               while(_loc4_ < _loc7_)
               {
                  param2[_loc4_].change(1);
                  _loc4_++;
               }
               if(_loc7_ < _loc3_)
               {
                  param2[_loc7_].change(param1 - _loc7_);
               }
               _loc5_ = _loc7_ + 1;
               while(_loc5_ < _loc3_)
               {
                  param2[_loc5_].change(0);
                  _loc5_++;
               }
               break;
            }
            _loc7_++;
         }
      }
      
      public function splitTimeString(param1:String) : String
      {
         return param1.split(" ")[0];
      }
      
      public function transformTimeToZero(param1:String) : String
      {
         return splitTimeString(param1) + " 00:00:00";
      }
      
      public function newDateIsNewDay(param1:String, param2:String) : Boolean
      {
         if(!Boolean(param1))
         {
            return true;
         }
         var _loc3_:Number = new TimeUtil().timeInterval(transformTimeToZero(param1),param2);
         if(_loc3_ > 24)
         {
            return true;
         }
         return false;
      }
      
      public function splitDateString(param1:String) : int
      {
         return int(param1.split("-")[2]);
      }
      
      public function minusEquipmentVOs(param1:Vector.<EquipmentVO>, param2:int, param3:int) : int
      {
         var length:int;
         var equipmentVO:EquipmentVO;
         var eqsLength:int;
         var equipmentVOs:Vector.<EquipmentVO> = param1;
         var num:int = param2;
         var id:int = param3;
         var sortFun:* = function(param1:EquipmentVO, param2:EquipmentVO):Number
         {
            return (param1 as StackEquipmentVO).num - (param2 as StackEquipmentVO).num;
         };
         var eqs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var isHave:Boolean = false;
         var isStack:Boolean = true;
         var i:int = 0;
         for each(equipmentVO in equipmentVOs)
         {
            if(equipmentVO)
            {
               if(equipmentVO.id == id && !equipmentVO.isPutInOperate)
               {
                  eqs.push(equipmentVO);
                  isHave = true;
                  if(equipmentVO is StackEquipmentVO)
                  {
                     isStack = true;
                  }
                  else
                  {
                     isStack = false;
                  }
               }
            }
         }
         if(!isHave)
         {
            eqs = null;
            return num;
         }
         if(isStack)
         {
            eqs = eqs.sort(sortFun);
            length = int(eqs.length);
            i = 0;
            while(true)
            {
               if(i < length)
               {
                  if((eqs[i] as StackEquipmentVO).num < num)
                  {
                     continue;
                  }
                  (eqs[i] as StackEquipmentVO).num -= num;
                  num = 0;
                  if((eqs[i] as StackEquipmentVO).num == 0)
                  {
                     equipmentVOs[equipmentVOs.indexOf(eqs[i])] = null;
                  }
               }
               num -= (eqs[i] as StackEquipmentVO).num;
               equipmentVOs[equipmentVOs.indexOf(eqs[i])] = null;
               i++;
            }
         }
         else
         {
            length = num;
            eqsLength = int(eqs.length);
            i = 0;
            while(i < length && i < eqsLength)
            {
               num -= 1;
               equipmentVOs[equipmentVOs.indexOf(eqs[eqsLength - i - 1])] = null;
               i++;
            }
         }
         ClearUtil.nullArr(eqs,false,false,false);
         eqs = null;
         return num;
      }
      
      public function minusOneEquipmentVOFromEquipmentVOs(param1:Vector.<EquipmentVO>, param2:EquipmentVO) : void
      {
         var _loc5_:int = 0;
         var _loc3_:Boolean = false;
         var _loc4_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(Boolean(param1[_loc5_]) && param1[_loc5_] == param2)
            {
               param1[_loc5_] = null;
               _loc3_ = true;
            }
            _loc5_++;
         }
         if(_loc3_ == false)
         {
            throw new Error("删除装备出错了");
         }
      }
      
      public function buyEquipmentVO(param1:Player, param2:EquipmentVO, param3:int, param4:Function, param5:String, param6:Function = null, param7:int = 0, ... rest) : void
      {
         var payAmount:int;
         var realityBuyNum:int;
         var player:Player = param1;
         var buyEquipmentVO:EquipmentVO = param2;
         var num:int = param3;
         var fun:Function = param4;
         var buyMode:String = param5;
         var decFun:Function = param6;
         var reservePositionNum:int = param7;
         var args:Array = rest;
         var decMoneyOrPointTicket:* = function(param1:String, param2:Player, param3:int, param4:EquipmentVO, param5:int, param6:int):void
         {
            var obj:Object;
            var buyMode1:String = param1;
            var player1:Player = param2;
            var price1:int = param3;
            var equipVO:EquipmentVO = param4;
            var realityNum:int = param5;
            var num:int = param6;
            switch(buyMode1)
            {
               case "moneyBuy":
                  player1.playerVO.money -= price1;
                  MyFunction.getInstance().trueAddOneNumEquipmentVO(player.playerVO.packageEquipmentVOs,equipVO,num,reservePositionNum);
                  break;
               case "pkPointBuy":
                  PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint - price1;
                  MyFunction.getInstance().trueAddOneNumEquipmentVO(player.playerVO.packageEquipmentVOs,equipVO,num,reservePositionNum);
                  break;
               default:
                  if(buyMode == "poincketTicket")
                  {
                     obj = price1;
                  }
                  else
                  {
                     if(buyMode != "ticketObj")
                     {
                        throw new Error();
                     }
                     obj = {};
                     obj["propId"] = equipVO.ticketId;
                     obj["count"] = realityNum;
                     obj["price"] = equipVO.ticketPrice;
                     obj["idx"] = GameData.getInstance().getSaveFileData().index;
                  }
                  decFun(obj,function(param1:Object = null):void
                  {
                     if(param1)
                     {
                        if(param1["propId"] != equipVO.ticketId)
                        {
                           throw new Error("购买物品的id与实际购买到的物品的id不相同");
                        }
                        num = param1["count"];
                     }
                     MyFunction.getInstance().trueAddOneNumEquipmentVO(player.playerVO.packageEquipmentVOs,equipVO,num,reservePositionNum);
                     GamingUI.getInstance().refresh(2);
                     var _loc4_:int = 0;
                     var _loc3_:int = int(args.length);
                     _loc4_ = 0;
                     while(_loc4_ < _loc3_)
                     {
                        if(args[_loc4_])
                        {
                           args[_loc4_]();
                        }
                        _loc4_++;
                     }
                     GamingUI.getInstance().addMainLineTaskGoalGameEventStr("ticketPointBuyQuipment");
                     var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame2();
                     if(realityBuyNum == num)
                     {
                        fun("购买成功！",0);
                     }
                     else
                     {
                        fun("背包已满！已成功购买部分物品！",0);
                     }
                  },Part1.getInstance().stage,fun);
            }
         };
         if(buyMode == "moneyBuy")
         {
            payAmount = buyEquipmentVO.price * num;
            if(player.playerVO.money < payAmount)
            {
               fun("元宝不足",0);
               return;
            }
         }
         else if(buyMode == "pkPointBuy")
         {
            payAmount = buyEquipmentVO.pkPrice * num;
            if(PlayerDataForPK.getInstance().pkPoint < payAmount)
            {
               fun("PK点不足， 无法购买",0);
               return;
            }
         }
         realityBuyNum = MyFunction.getInstance().faseAddOneNumEquipmentVO(player.playerVO.packageEquipmentVOs,buyEquipmentVO,num,reservePositionNum);
         if(realityBuyNum)
         {
            if(buyMode == "moneyBuy")
            {
               decMoneyOrPointTicket(buyMode,player,buyEquipmentVO.price * realityBuyNum,buyEquipmentVO,realityBuyNum,num);
            }
            else if(buyMode == "pkPointBuy")
            {
               ShopData.getInstance().currentBuyNumInPKShop++;
               decMoneyOrPointTicket(buyMode,player,buyEquipmentVO.pkPrice * realityBuyNum,buyEquipmentVO,realityBuyNum,num);
            }
            else
            {
               decMoneyOrPointTicket(buyMode,player,buyEquipmentVO.ticketPrice * realityBuyNum,buyEquipmentVO,realityBuyNum,num);
            }
         }
         if(!realityBuyNum && num)
         {
            fun("背包已满！",0);
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
      
      public function faseAddOneNumEquipmentVO(param1:Vector.<EquipmentVO>, param2:EquipmentVO, param3:int, param4:int) : int
      {
         var _loc12_:int = 0;
         var _loc9_:int = 0;
         var _loc8_:EquipmentVO = null;
         var _loc10_:* = undefined;
         var _loc6_:StackEquipmentVO = null;
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         var _loc7_:int = 0;
         if(param2)
         {
            if(param2 is StackEquipmentVO)
            {
               _loc6_ = param2 as StackEquipmentVO;
               _loc9_ = Math.ceil(param3 / _loc6_.maxSuperposition);
               _loc10_ = cloneEquipmentVOArray(param1);
               _loc12_ = 0;
               while(_loc12_ < _loc9_)
               {
                  _loc8_ = param2.clone();
                  if(_loc12_ == _loc9_ - 1)
                  {
                     _loc5_ = param3 - _loc6_.maxSuperposition * _loc12_;
                  }
                  else
                  {
                     _loc5_ = _loc6_.maxSuperposition;
                  }
                  (_loc8_ as StackEquipmentVO).num = _loc5_;
                  if(MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_loc10_,_loc8_,0,param4))
                  {
                     _loc7_ += _loc5_;
                  }
                  else
                  {
                     _loc7_ += _loc5_ - (_loc8_ as StackEquipmentVO).num;
                  }
                  _loc12_++;
               }
            }
            else
            {
               if(!param3)
               {
                  return param3;
               }
               _loc9_ = int(param1.length);
               _loc12_ = 0;
               while(_loc12_ < _loc9_)
               {
                  if(param1[_loc12_] == null)
                  {
                     _loc11_++;
                  }
                  if(_loc11_ - param4 == param3)
                  {
                     break;
                  }
                  _loc12_++;
               }
               _loc7_ = _loc11_ - param4 >= 0 ? _loc11_ - param4 : 0;
            }
         }
         return _loc7_;
      }
      
      public function trueAddOneNumEquipmentVO(param1:Vector.<EquipmentVO>, param2:EquipmentVO, param3:int, param4:int) : void
      {
         var _loc9_:int = 0;
         var _loc7_:* = 0;
         var _loc8_:EquipmentVO = null;
         var _loc6_:StackEquipmentVO = null;
         var _loc5_:int = 0;
         if(param2 is StackEquipmentVO)
         {
            _loc6_ = param2 as StackEquipmentVO;
            _loc7_ = Math.ceil(param3 / _loc6_.maxSuperposition);
            _loc9_ = 0;
            while(_loc9_ < _loc7_)
            {
               _loc8_ = param2.clone();
               if(_loc9_ == _loc7_ - 1)
               {
                  _loc5_ = param3 - _loc6_.maxSuperposition * _loc9_;
               }
               else
               {
                  _loc5_ = _loc6_.maxSuperposition;
               }
               (_loc8_ as StackEquipmentVO).num = _loc5_;
               MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param1,_loc8_,0,param4);
               _loc9_++;
            }
         }
         else
         {
            _loc7_ = param3;
            _loc9_ = 0;
            while(_loc9_ < _loc7_)
            {
               _loc8_ = XMLSingle.getBuyEquipmentVO(param2);
               if(_loc8_.equipmentType == "pet")
               {
                  (_loc8_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(0);
                  (_loc8_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomOnePassiveSkillVOInCreatePet(_loc8_ as PetEquipmentVO);
                  MyFunction.getInstance().refreshPet(_loc8_ as PetEquipmentVO);
               }
               if(_loc8_.equipmentType == "precious")
               {
                  _loc8_ = XMLSingle.getEquipmentVOByID(_loc8_.id,XMLSingle.getInstance().equipmentXML);
               }
               MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(param1,_loc8_,0,param4);
               _loc9_++;
            }
         }
      }
      
      public function addXiuLianValueToXiuLianContent(param1:XiuLianContent, param2:int, param3:Function, param4:Array, param5:Function, param6:Array) : void
      {
         var _loc8_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:* = undefined;
         var _loc7_:String = null;
         if(!param1)
         {
            return;
         }
         var _loc9_:int = param1.needXiuLianValue - param1.currentXiuLianValue;
         if(param1.level >= param1.maxLevel)
         {
            param1.currentXiuLianValue = 0;
            if(Boolean(param5))
            {
               param5.apply(null,param6);
            }
            return;
         }
         if(param2 < _loc9_)
         {
            param1.currentXiuLianValue += param2;
            if(Boolean(param5))
            {
               param5.apply(null,param6);
            }
         }
         else if(param1.level < param1.maxLevel)
         {
            _loc11_ = excreteStringToString(param1.id);
            _loc7_ = _loc11_[0] + "_" + _loc11_[1] + "_" + (param1.level + 1);
            param1.id = _loc7_;
            XMLSingle.getInstance().setXiuLianContent(param1,XMLSingle.getInstance().xiuLianContentXML,param1.owner);
            param1.currentXiuLianValue = 0;
            _loc8_ = param1.needXiuLianValue;
            _loc10_ = param1.level;
            addXiuLianValueToXiuLianContent(param1,param2 - _loc9_,param3,param4,param5,param6);
            if(param2 - _loc9_ < _loc8_ || _loc10_ == param1.maxLevel)
            {
               if(Boolean(param3))
               {
                  param3.apply(null,param4);
               }
            }
         }
         else
         {
            param1.currentXiuLianValue = param1.needXiuLianValue;
            if(Boolean(param3))
            {
               param5.apply(null,param6);
            }
         }
      }
      
      public function addPlayerExperience(param1:Player, param2:int) : void
      {
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc7_:SkillVO = null;
         var _loc4_:int = 0;
         if(!param1)
         {
            return;
         }
         if(!param1.playerVO)
         {
            return;
         }
         if(param1.playerVO.bloodPercent)
         {
            _loc8_ = (1 - param1.playerVO.experiencePercent) * param1.playerVO.experienceVolume;
            if(param2 < _loc8_)
            {
               param1.playerVO.experiencePercent += param2 / param1.playerVO.experienceVolume;
               GamingUI.getInstance().refresh(128);
            }
            else if(param1.playerVO.level < XMLSingle.getInstance().getPlayerMaxLevel(param1))
            {
               param1.playerVO.level += 1;
               XMLSingle.setPlayerVO(param1.playerVO,1);
               param1.playerVO.experiencePercent = 0;
               param1.playerVO.bloodPercent = 1;
               param1.playerVO.magicPercent = 1;
               SubmitFunction.getInstance().setData5(3,param1.playerVO.level,getRoleName(param1.playerVO.playerType));
               _loc5_ = int(param1.playerVO.skillVOs.length);
               _loc9_ = 0;
               while(_loc9_ < _loc5_)
               {
                  _loc7_ = param1.playerVO.skillVOs[_loc9_];
                  if(_loc7_.level == 0 && (_loc7_ as PlayerActiveSkillVO).initLevel <= param1.playerVO.level)
                  {
                     _loc4_ = MyFunction.getInstance().calculateNextLevelID(_loc7_.id);
                     _loc7_ = XMLSingle.getSkill(_loc4_,XMLSingle.getInstance().skillXML);
                     param1.playerVO.skillVOs[_loc9_] = _loc7_;
                  }
                  _loc9_++;
               }
               _loc3_ = param1.playerVO.experienceVolume;
               _loc6_ = param1.playerVO.level;
               addPlayerExperience(param1,param2 - _loc8_);
               if(param2 - _loc8_ < _loc3_ || _loc6_ == XMLSingle.getInstance().getPlayerMaxLevel(param1))
               {
                  refreshPlayer(param1,2);
                  param1.playerVO.bloodPercent = 1;
                  param1.playerVO.magicPercent = 1;
                  GamingUI.getInstance().refresh(201);
                  if(GamingUI.getInstance().getSocietySystem())
                  {
                     GamingUI.getInstance().getSocietySystem().updatePlayerData(1);
                  }
               }
            }
            else
            {
               param1.playerVO.experiencePercent = 1;
               GamingUI.getInstance().refresh(128);
            }
         }
      }
      
      private function getRoleName(param1:String) : String
      {
         if(param1 == "SunWuKong")
         {
            return "孙悟空";
         }
         if(param1 == "BaiLongMa")
         {
            return "白龙马";
         }
         if(param1 == "ErLangShen")
         {
            return "二郎神";
         }
         if(param1 == "ChangE")
         {
            return "嫦娥";
         }
         if(param1 == "Fox")
         {
            return "灵狐";
         }
         if(param1 == "TieShan")
         {
            return "铁扇公主";
         }
         if(param1 == "ZiXia")
         {
            return "紫霞仙子";
         }
         return "";
      }
      
      public function addPlayerToLevel(param1:Player, param2:int) : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:SkillVO = null;
         var _loc3_:int = 0;
         if(!param1)
         {
            return;
         }
         if(!param1.playerVO)
         {
            return;
         }
         if(param1.playerVO.bloodPercent)
         {
            param1.playerVO.level = param2;
            XMLSingle.setPlayerVO(param1.playerVO,1);
            param1.playerVO.experiencePercent = 0;
            param1.playerVO.bloodPercent = 1;
            param1.playerVO.magicPercent = 1;
            _loc4_ = int(param1.playerVO.skillVOs.length);
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               _loc5_ = param1.playerVO.skillVOs[_loc6_];
               if(_loc5_.level == 0 && (_loc5_ as PlayerActiveSkillVO).initLevel <= param1.playerVO.level)
               {
                  _loc3_ = MyFunction.getInstance().calculateNextLevelID(_loc5_.id);
                  _loc5_ = XMLSingle.getSkill(_loc3_,XMLSingle.getInstance().skillXML);
                  param1.playerVO.skillVOs[_loc6_] = _loc5_;
               }
               _loc6_++;
            }
            if(param1.playerVO.level == XMLSingle.getInstance().getPlayerMaxLevel(param1))
            {
               refreshPlayer(param1,2);
               param1.playerVO.bloodPercent = 1;
               param1.playerVO.magicPercent = 1;
               GamingUI.getInstance().refresh(201);
               if(GamingUI.getInstance().getSocietySystem())
               {
                  GamingUI.getInstance().getSocietySystem().updatePlayerData(1);
               }
            }
         }
      }
      
      public function addPetExperience(param1:Player, param2:int) : void
      {
         var _loc6_:int = 0;
         var _loc11_:int = 0;
         var _loc3_:int = 0;
         var _loc12_:int = 0;
         var _loc8_:PetEquipmentVO = null;
         var _loc14_:int = 0;
         var _loc16_:PetEquipmentVO = null;
         var _loc5_:PetActiveSkillVO = null;
         var _loc7_:int = 0;
         var _loc4_:* = undefined;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc15_:PetActiveSkillVO = null;
         if(!param1)
         {
            return;
         }
         if(!param1.playerVO)
         {
            return;
         }
         if(param1.playerVO.bloodPercent)
         {
            if(param1.playerVO.pet)
            {
               if(param1.playerVO.pet.petEquipmentVO)
               {
                  _loc8_ = param1.playerVO.pet.petEquipmentVO;
                  _loc14_ = (1 - _loc8_.experiencePercent) * _loc8_.experienceVolume;
                  if(param2 < _loc14_)
                  {
                     _loc8_.experiencePercent += param2 / _loc8_.experienceVolume;
                     GamingUI.getInstance().refresh(16);
                  }
                  else
                  {
                     _loc12_ = _loc8_.petLevel + 1;
                     if(_loc12_ > _loc8_.upgradeLevelNum)
                     {
                        if(_loc8_.level < _loc8_.maxLevel)
                        {
                           _loc16_ = XMLSingle.getEquipment(MyFunction.getInstance().calculateNextLevelID(_loc8_.id),XMLSingle.getInstance().equipmentXML) as PetEquipmentVO;
                           _loc16_.petLevel = 1;
                           XMLSingle.getInstance().setPetData(_loc16_.id,_loc16_.petLevel,_loc16_);
                           _loc5_ = _loc16_.activeSkillVO as PetActiveSkillVO;
                           _loc5_.originalHurt = _loc16_.petLevel * _loc5_.hurCoefficient + _loc5_.additionHurt;
                           _loc5_.originalPkHurt = _loc16_.petLevel * _loc5_.pkHurtCoefficient + _loc5_.additionPkHurt;
                           XMLSingle.getInstance().randomGetOnePassiveSkillVO(_loc8_,1);
                           _loc4_ = new Vector.<SkillVO>();
                           for each(var _loc13_ in _loc8_.passiveSkillVOs)
                           {
                              if(_loc13_)
                              {
                                 _loc7_ = (_loc13_ as PetPassiveSkillVO).promoteValue;
                                 _loc13_ = XMLSingle.getSkill(MyFunction.getInstance().calculateCurrentLevelID(_loc13_.id,_loc16_.level),XMLSingle.getInstance().skillXML);
                                 (_loc13_ as PetPassiveSkillVO).promoteValue = _loc7_;
                                 (_loc13_ as PetPassiveSkillVO).value = (_loc13_ as PetPassiveSkillVO).originalValue + (_loc13_ as PetPassiveSkillVO).promoteValue;
                                 _loc4_.push(_loc13_);
                              }
                           }
                           _loc16_.passiveSkillVOs = _loc4_;
                           _loc9_ = _loc8_.essentialPercent * _loc8_.essentialVolume;
                           _loc16_.essentialPercent = _loc9_ / _loc16_.essentialVolume;
                           _loc16_.experiencePercent = 0;
                           ClearUtil.nullArr(_loc8_.passiveSkillVOs);
                           _loc8_.passiveSkillVOs = null;
                           XMLSingle.getInstance().randomUpgradeTalent(_loc8_,1);
                           _loc16_.talentVO = _loc8_.talentVO;
                           _loc8_.talentVO = null;
                           MyFunction.getInstance().refreshPet(_loc16_);
                           param1.playerVO.pet.petEquipmentVO = _loc16_;
                           _loc6_ = _loc16_.experienceVolume;
                           _loc6_ = _loc16_.level;
                           addPetExperience(param1,param2 - _loc14_);
                           if(param2 - _loc14_ < _loc6_)
                           {
                              GamingUI.getInstance().refresh(729);
                           }
                        }
                        else
                        {
                           if(_loc8_.upgradeLevelNum)
                           {
                              _loc8_.petLevel = _loc8_.upgradeLevelNum;
                           }
                           _loc8_.experiencePercent = 1;
                           GamingUI.getInstance().refresh(16);
                        }
                     }
                     else
                     {
                        _loc8_.petLevel = _loc12_;
                        _loc10_ = _loc8_.essentialPercent * _loc8_.essentialVolume;
                        XMLSingle.getInstance().setPetData(_loc8_.id,_loc8_.petLevel,_loc8_);
                        _loc15_ = _loc8_.activeSkillVO as PetActiveSkillVO;
                        _loc15_.originalHurt = _loc8_.petLevel * _loc15_.hurCoefficient + _loc15_.additionHurt;
                        _loc15_.originalPkHurt = _loc8_.petLevel * _loc15_.pkHurtCoefficient + _loc15_.additionPkHurt;
                        _loc8_.essentialPercent = _loc10_ / _loc8_.essentialVolume;
                        XMLSingle.getInstance().randomUpgradeTalent(_loc8_,0);
                        XMLSingle.getInstance().randomGetOnePassiveSkillVO(_loc8_,0);
                        MyFunction.getInstance().refreshPet(_loc8_);
                        _loc8_.experiencePercent = 0;
                        _loc6_ = _loc8_.experienceVolume;
                        _loc11_ = _loc8_.petLevel;
                        _loc3_ = _loc8_.level;
                        addPetExperience(param1,param2 - _loc14_);
                        if(param2 - _loc14_ < _loc6_ || _loc11_ == _loc8_.upgradeLevelNum && _loc3_ == _loc8_.maxLevel)
                        {
                           GamingUI.getInstance().refresh(729);
                        }
                     }
                  }
               }
            }
         }
      }
      
      public function cloneEquipmentVOArray(param1:Vector.<EquipmentVO>) : Vector.<EquipmentVO>
      {
         var _loc3_:Pet = null;
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>(param1.length);
         var _loc5_:int = 0;
         var _loc4_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(param1[_loc5_])
            {
               if(param1[_loc5_] is PetEquipmentVO)
               {
                  if((param1[_loc5_] as PetEquipmentVO).pet)
                  {
                     _loc3_ = (param1[_loc5_] as PetEquipmentVO).pet;
                     _loc3_.petEquipmentVO = null;
                  }
               }
               _loc2_[_loc5_] = param1[_loc5_].clone();
               if(_loc3_)
               {
                  _loc3_.petEquipmentVO = param1[_loc5_] as PetEquipmentVO;
               }
               _loc3_ = null;
            }
            else
            {
               _loc2_[_loc5_] = null;
            }
            _loc5_++;
         }
         return _loc2_;
      }
      
      public function dealWithDataEquipmentVOFromEquipmentVOs(param1:Vector.<EquipmentVO>, param2:String) : void
      {
         var _loc4_:Number = NaN;
         var _loc5_:int = 0;
         var _loc3_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(param1[_loc5_])
            {
               if(param1[_loc5_] is ILimitEquipmentVO)
               {
                  if((param1[_loc5_] as ILimitEquipmentVO).TimeLimit == -1)
                  {
                     _loc4_ = 2147483647;
                  }
                  else
                  {
                     _loc4_ = (param1[_loc5_] as ILimitEquipmentVO).TimeLimit - new TimeUtil().timeInterval((param1[_loc5_] as ILimitEquipmentVO).initTime,param2);
                     if(_loc4_ <= 0 || _loc4_ > (param1[_loc5_] as ILimitEquipmentVO).TimeLimit)
                     {
                        param1[_loc5_] = null;
                     }
                     else
                     {
                        (param1[_loc5_] as ILimitEquipmentVO).remainingTime = Math.ceil(_loc4_ / 24);
                     }
                  }
               }
            }
            _loc5_++;
         }
      }
   }
}

