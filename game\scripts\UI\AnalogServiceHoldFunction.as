package UI
{
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.ShopWall.MaskingAfterRecharge;
   import UI2.Consumer.ConsumerData;
   import UI2.broadcast.BroadDataManager;
   import YJFY.API_4399.PayAPI.BuyPropData;
   import YJFY.API_4399.PayAPI.GetRechargeManager;
   import YJFY.API_4399.PayAPI.RequireInfo;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import flash.display.Stage;
   import flash.utils.setTimeout;
   import unit4399.events.PayEvent;
   import unit4399.events.ShopEvent;
   
   public class AnalogServiceHoldFunction
   {
      private static var _instance:AnalogServiceHoldFunction = null;
      
      private var _currrentPointTicket:Number = 0;
      
      private var _allPointTicket:Number = 0;
      
      private var _funAfterDec:Function;
      
      private var _showWarningBox:Function;
      
      private var _funBeforeDec:Function;
      
      private var _warningTextFontSize:int = 16;
      
      private var _getTotalRechargedSuccessFunDatas:Array;
      
      private var _getTotalRechargedFailFunDatas:Array;
      
      private var _isGettingTotalRecharged:Boolean;
      
      public function AnalogServiceHoldFunction()
      {
         super();
         if(!_instance)
         {
            Part1.getInstance().stage.dispatchEvent(new PayEvent("usePayApi",null));
            _instance = this;
            return;
         }
         throw new Error("fuck you! don\'t you see example!");
      }
      
      public static function getInstance() : AnalogServiceHoldFunction
      {
         if(_instance == null)
         {
            _instance = new AnalogServiceHoldFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         _funAfterDec = null;
         _showWarningBox = null;
         _funBeforeDec = null;
         ClearUtil.clearObject(_getTotalRechargedFailFunDatas);
         _getTotalRechargedFailFunDatas = null;
         ClearUtil.clearObject(_getTotalRechargedSuccessFunDatas);
         _getTotalRechargedSuccessFunDatas = null;
         _instance = null;
      }
      
      public function buyByPointTicketFun(param1:int, param2:Function, param3:Function, param4:int) : void
      {
         var price:int = param1;
         var decAfterFun:Function = param2;
         var showWarningFun:Function = param3;
         var warningTextFontSize:int = param4;
         _warningTextFontSize = warningTextFontSize;
         AnalogServiceHoldFunction.getInstance().buyByPointTicket(price,function(param1:Function):void
         {
            param1(price,decAfterFun,Part1.getInstance().stage,showWarningFun);
         },Part1.getInstance().stage,showWarningFun,warningTextFontSize);
      }
      
      public function buyByPayDataFun(param1:Object, param2:Function, param3:Function, param4:int) : void
      {
         var dataObject:Object = param1;
         var decAfterFun:Function = param2;
         var showWarningFun:Function = param3;
         var warningTextFontSize:int = param4;
         _warningTextFontSize = warningTextFontSize;
         AnalogServiceHoldFunction.getInstance().buyByPayData(dataObject,function(param1:Function):void
         {
            param1(dataObject,decAfterFun,Part1.getInstance().stage,showWarningFun);
         },Part1.getInstance().stage,showWarningFun,warningTextFontSize);
      }
      
      public function payMoney_As3() : void
      {
         BroadDataManager.getInstance().nowVip = GamingUI.getInstance().player1.vipVO.vipLevel;
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            _currrentPointTicket += Number(XMLSingle.getInstance().dataXML.testPayMoney.@value);
            _allPointTicket += Number(XMLSingle.getInstance().dataXML.testPayMoney.@value);
            Part1.getInstance().stage.addChild(new MaskingAfterRecharge());
         }
         else
         {
            GamingUI.getInstance().getAPI4399().payAPI.payMoney(PayMoneyVar.getInstance().money);
            Part1.getInstance().stage.addChild(new MaskingAfterRecharge());
         }
      }
      
      public function decMoney_As3(param1:Number) : void
      {
         var pointTicket:Number = param1;
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            _currrentPointTicket -= pointTicket;
            _allPointTicket -= pointTicket;
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new PayEvent("decMoney",{"balance":_currrentPointTicket}));
            },500);
         }
         else
         {
            PayMoneyVar.getInstance().money = pointTicket;
            if(GamingUI.getInstance().getAPI4399().getServiceHold())
            {
               GamingUI.getInstance().getAPI4399().getServiceHold().decMoney_As3(PayMoneyVar.getInstance());
            }
         }
      }
      
      public function buyPropNd(param1:Object) : void
      {
         var buyProData:BuyPropData;
         var dataObj:Object = param1;
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            _currrentPointTicket -= dataObj["price"] * dataObj["count"];
            _allPointTicket -= dataObj["price"] * dataObj["count"];
            dataObj.balance = _currrentPointTicket;
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new ShopEvent("shopBuyNd",dataObj));
            },500);
         }
         else
         {
            buyProData = new BuyPropData();
            buyProData.init(dataObj["propId"],dataObj["count"],dataObj["price"],dataObj["idx"],dataObj["tag"]);
            GamingUI.getInstance().getAPI4399().payAPI.buyProp(buyProData);
         }
      }
      
      public function getBalance() : void
      {
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            Part1.getInstance().stage.dispatchEvent(new PayEvent("getMoney",{"balance":_currrentPointTicket}));
         }
         else
         {
            trace("向服务器获取剩余游戏币, 发送");
            GamingUI.getInstance().getAPI4399().payAPI.getBalance();
         }
      }
      
      public function getTotalRechargedFun(param1:Function, param2:Array, param3:Function, param4:Array) : void
      {
         var getTotalRechargedAfterFun:Function = param1;
         var getTotalRechargedAfterFunParams:Array = param2;
         var getTotalRechargedFailFun:Function = param3;
         var getTotalRechargedFailFunParams:Array = param4;
         if(!_getTotalRechargedSuccessFunDatas)
         {
            _getTotalRechargedSuccessFunDatas = [];
         }
         _getTotalRechargedSuccessFunDatas.push([getTotalRechargedAfterFun,getTotalRechargedAfterFunParams]);
         if(!_getTotalRechargedFailFunDatas)
         {
            _getTotalRechargedFailFunDatas = [];
         }
         _getTotalRechargedFailFunDatas.push([getTotalRechargedFailFun,getTotalRechargedFailFunParams]);
         _isGettingTotalRecharged = true;
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new PayEvent("rechargedMoney",{"balance":_allPointTicket}));
            },500);
         }
         else
         {
            GamingUI.getInstance().getAPI4399().payAPI.getTotalRechargeFun();
         }
      }
      
      public function getTotalRechargedFun2(param1:RequireInfo, param2:Function, param3:Array, param4:Function, param5:Array) : void
      {
         var requirinfo:RequireInfo = param1;
         var getTotalRechargedAfterFun:Function = param2;
         var getTotalRechargedAfterFunParams:Array = param3;
         var getTotalRechargedFailFun:Function = param4;
         var getTotalRechargedFailFunParams:Array = param5;
         if(!_getTotalRechargedSuccessFunDatas)
         {
            _getTotalRechargedSuccessFunDatas = [];
         }
         _getTotalRechargedSuccessFunDatas.push([getTotalRechargedAfterFun,getTotalRechargedAfterFunParams]);
         if(!_getTotalRechargedFailFunDatas)
         {
            _getTotalRechargedFailFunDatas = [];
         }
         _getTotalRechargedFailFunDatas.push([getTotalRechargedFailFun,getTotalRechargedFailFunParams]);
         _isGettingTotalRecharged = true;
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new PayEvent("rechargedMoney",{"balance":_allPointTicket}));
            },500);
         }
         else
         {
            GetRechargeManager.getInstance().addRequir(requirinfo);
            ConsumerData.getInstance().requirePaied();
         }
      }
      
      public function getIsGettingTotalRecharged() : Boolean
      {
         return _isGettingTotalRecharged;
      }
      
      public function getStoreState() : void
      {
         GamingUI.getInstance().getAPI4399().saveAPI.getStoreState();
      }
      
      public function buyByPointTicket(param1:int, param2:Function, param3:Stage, param4:Function, param5:int) : void
      {
         _warningTextFontSize = param5;
         if(param1 <= CurrentTicketPointManager.getInstance().getCurrentTicketPoint())
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "购买处理中， 请勿关闭游戏！";
            GamingUI.getInstance().manBan.wheel.visible = true;
            _funBeforeDec = param2;
            this.getStoreState();
         }
         else
         {
            if(Boolean(param4))
            {
               setTimeout(param4,100,"点券不足， 无法购买！",0);
            }
            GamingUI.getInstance().openRechargePanel();
         }
      }
      
      public function buyByPayData(param1:Object, param2:Function, param3:Stage, param4:Function, param5:int) : void
      {
         _warningTextFontSize = param5;
         if(param1["price"] * param1["count"] <= CurrentTicketPointManager.getInstance().getCurrentTicketPoint())
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "购买处理中， 请勿关闭游戏！";
            GamingUI.getInstance().manBan.wheel.visible = true;
            _funBeforeDec = param2;
            this.getStoreState();
         }
         else
         {
            if(Boolean(param4))
            {
               setTimeout(param4,100,"点券不足， 无法购买！",0);
            }
            GamingUI.getInstance().openRechargePanel();
         }
      }
      
      private function buy(param1:*, param2:Function, param3:Stage, param4:Function) : void
      {
         _funAfterDec = param2;
         _showWarningBox = param4;
         if(param1 is int)
         {
            this.decMoney_As3(param1);
         }
         else
         {
            this.buyPropNd(param1);
         }
      }
      
      public function dealStoreState() : void
      {
         if(Boolean(_funBeforeDec))
         {
            _funBeforeDec(buy);
         }
         _funBeforeDec = null;
      }
      
      public function decMoney(param1:Number) : void
      {
         _funAfterDec();
         if(Boolean(_showWarningBox))
         {
            _showWarningBox("<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>购买成功</font>，消耗<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>" + (CurrentTicketPointManager.getInstance().getCurrentTicketPoint() - param1) + "</font>点券， 余额为<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>" + param1 + "</font>点券！",1);
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         _funAfterDec = null;
         _showWarningBox = null;
      }
      
      public function buySuccessByData(param1:Object, param2:Number) : void
      {
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         _funAfterDec(param1);
         if(Boolean(_showWarningBox))
         {
            _showWarningBox("<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>购买成功</font>，消耗<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>" + (CurrentTicketPointManager.getInstance().getCurrentTicketPoint() - param2) + "</font>点券， 余额为<font size=\'" + (_warningTextFontSize + 9) + "\' color=\'#fcca00\'>" + param2 + "</font>点券！",1);
         }
         _funAfterDec = null;
         _showWarningBox = null;
      }
      
      public function decMoneyError() : void
      {
         if(Boolean(_showWarningBox))
         {
            _showWarningBox("服务器连接失败！请重试！",0);
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         _funAfterDec = null;
         _showWarningBox = null;
      }
      
      public function buyErrorByData(param1:Object) : void
      {
         var data:Object = param1;
         MyFunction2.loadXMLFunction("buyErrorData",function(param1:XML):void
         {
            var _loc4_:int = 0;
            var _loc3_:XMLList = param1.item;
            var _loc2_:int = int(!!_loc3_ ? _loc3_.length() : 0);
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               if(String(_loc3_[_loc4_].@eId) == data.eId)
               {
                  if(Boolean(_showWarningBox))
                  {
                     _showWarningBox(String(_loc3_[_loc4_].@message),0);
                  }
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
                  _funAfterDec = null;
                  _showWarningBox = null;
                  break;
               }
               _loc4_++;
            }
         },function():void
         {
            if(Boolean(_showWarningBox))
            {
               _showWarningBox("网络连接失败！",0);
            }
         },true);
      }
      
      public function getRechargedSuceessFun() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _isGettingTotalRecharged = false;
         if(_getTotalRechargedSuccessFunDatas)
         {
            _loc1_ = int(_getTotalRechargedSuccessFunDatas.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_getTotalRechargedSuccessFunDatas[_loc4_])
               {
                  if(_getTotalRechargedSuccessFunDatas[_loc4_][0])
                  {
                     _getTotalRechargedSuccessFunDatas[_loc4_][0].apply(null,_getTotalRechargedSuccessFunDatas[_loc4_][1]);
                  }
                  _getTotalRechargedSuccessFunDatas[_loc4_][0] = null;
                  if(_getTotalRechargedSuccessFunDatas[_loc4_][1])
                  {
                     _loc2_ = int(_getTotalRechargedSuccessFunDatas[_loc4_][1].length);
                     _loc3_ = 0;
                     while(_loc3_ < _loc2_)
                     {
                        _getTotalRechargedSuccessFunDatas[_loc4_][1][_loc3_] = null;
                        _loc3_++;
                     }
                     _getTotalRechargedSuccessFunDatas[_loc4_][1] = null;
                  }
                  _getTotalRechargedSuccessFunDatas[_loc4_] = null;
               }
               _loc4_++;
            }
            _getTotalRechargedSuccessFunDatas = null;
         }
         if(_getTotalRechargedFailFunDatas)
         {
            _loc1_ = int(_getTotalRechargedFailFunDatas.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_getTotalRechargedFailFunDatas[_loc4_])
               {
                  _getTotalRechargedFailFunDatas[_loc4_][0] = null;
                  if(_getTotalRechargedFailFunDatas[_loc4_][1])
                  {
                     _loc2_ = int(_getTotalRechargedFailFunDatas[_loc4_][1].length);
                     _loc3_ = 0;
                     while(_loc3_ < _loc2_)
                     {
                        _getTotalRechargedFailFunDatas[_loc4_][1][_loc3_] = null;
                        _loc3_++;
                     }
                     _getTotalRechargedFailFunDatas[_loc4_][1] = null;
                  }
                  _getTotalRechargedFailFunDatas[_loc4_] = null;
               }
               _loc4_++;
            }
            _getTotalRechargedFailFunDatas = null;
         }
      }
      
      public function getRechargedErrorFun() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _isGettingTotalRecharged = false;
         if(_getTotalRechargedSuccessFunDatas)
         {
            _loc1_ = int(_getTotalRechargedSuccessFunDatas.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_getTotalRechargedSuccessFunDatas[_loc4_])
               {
                  _getTotalRechargedSuccessFunDatas[_loc4_][0] = null;
                  if(_getTotalRechargedSuccessFunDatas[_loc4_][1])
                  {
                     _loc2_ = int(_getTotalRechargedSuccessFunDatas[_loc4_][1].length);
                     _loc3_ = 0;
                     while(_loc3_ < _loc2_)
                     {
                        _getTotalRechargedSuccessFunDatas[_loc4_][1][_loc3_] = null;
                        _loc3_++;
                     }
                     _getTotalRechargedSuccessFunDatas[_loc4_][1] = null;
                  }
                  _getTotalRechargedSuccessFunDatas[_loc4_] = null;
               }
               _loc4_++;
            }
            _getTotalRechargedSuccessFunDatas = null;
         }
         if(_getTotalRechargedFailFunDatas)
         {
            _loc1_ = int(_getTotalRechargedFailFunDatas.length);
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               if(_getTotalRechargedFailFunDatas[_loc4_])
               {
                  _getTotalRechargedFailFunDatas[_loc4_][0].apply(null,_getTotalRechargedFailFunDatas[_loc4_][1]);
                  _getTotalRechargedFailFunDatas[_loc4_][0] = null;
                  if(_getTotalRechargedFailFunDatas[_loc4_][1])
                  {
                     _loc2_ = int(_getTotalRechargedFailFunDatas[_loc4_][1].length);
                     _loc3_ = 0;
                     while(_loc3_ < _loc2_)
                     {
                        _getTotalRechargedFailFunDatas[_loc4_][1][_loc3_] = null;
                        _loc3_++;
                     }
                     _getTotalRechargedFailFunDatas[_loc4_][1] = null;
                  }
                  _getTotalRechargedFailFunDatas[_loc4_] = null;
               }
               _loc4_++;
            }
            _getTotalRechargedFailFunDatas = null;
         }
      }
   }
}

