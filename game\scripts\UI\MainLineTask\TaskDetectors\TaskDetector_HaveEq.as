package UI.MainLineTask.TaskDetectors
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.Players.Player;
   import YJFY.Utils.ClearUtil;
   
   public class TaskDetector_HaveEq extends TaskDetector
   {
      private var m_detectXML:XML;
      
      public function TaskDetector_HaveEq()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_detectXML = param1;
      }
      
      override public function detect() : void
      {
         // 装备检测已禁用 - Equipment detection disabled
         return;

      }
      
      private function createAllEquipmentVOs() : Vector.<EquipmentVO>
      {
         var _loc4_:* = undefined;
         var _loc1_:* = undefined;
         var _loc2_:Player = GamingUI.getInstance().player1;
         var _loc3_:Player = GamingUI.getInstance().player2;
         _loc4_ = _loc2_.playerVO.inforEquipmentVOs.slice(0);
         _loc1_ = _loc4_.concat(_loc2_.playerVO.packageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         _loc4_ = _loc1_;
         _loc1_ = _loc4_.concat(_loc2_.playerVO.storageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         _loc4_ = _loc1_;
         if(Boolean(_loc2_.playerVO.pet) && _loc2_.playerVO.pet.petEquipmentVO)
         {
            _loc4_.push(_loc2_.playerVO.pet.petEquipmentVO);
         }
         if(_loc3_)
         {
            _loc1_ = _loc4_.concat(_loc3_.playerVO.inforEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            _loc1_ = _loc4_.concat(_loc3_.playerVO.packageEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            _loc1_ = _loc4_.concat(_loc3_.playerVO.storageEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            if(_loc3_.playerVO.pet != null && _loc3_.playerVO.pet.petEquipmentVO)
            {
               _loc4_.push(_loc3_.playerVO.pet.petEquipmentVO);
            }
         }
         _loc1_ = _loc4_.concat(GamingUI.getInstance().publicStorageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         return _loc1_;
      }
   }
}

