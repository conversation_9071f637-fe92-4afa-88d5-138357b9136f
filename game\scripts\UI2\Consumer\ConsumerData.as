package UI2.Consumer
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.API_4399.PayAPI.DateInfor;
   import YJFY.API_4399.PayAPI.GetPaiedManager;
   import YJFY.API_4399.PayAPI.RequireInfo;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class ConsumerData
   {
      private static var _instance:ConsumerData;
      
      private var datalist:Vector.<ConsumerOne>;
      
      private var dataTimeDay:String;
      
      private var dataTimeWeek:String;
      
      private var requirinfo1:RequireInfo;
      
      private var requirinfo2:RequireInfo;
      
      private var m_dayPaied:Number = -1;
      
      private var m_weekPaied:Number = -1;
      
      private var m_ndayGetNum:int = -1;
      
      private var m_nweekGetNum:int = -1;
      
      private var m_bGetSavedata:Boolean = false;
      
      private var m_nGetPaiedNum:int = 0;
      
      private var m_bXMLLoad:Boolean = false;
      
      private var m_dataXML:Vector.<PayXMLData>;
      
      public function ConsumerData()
      {
         super();
         datalist = new Vector.<ConsumerOne>();
         m_dataXML = new Vector.<PayXMLData>();
         requirinfo1 = new RequireInfo();
         requirinfo2 = new RequireInfo();
      }
      
      public static function getInstance() : ConsumerData
      {
         if(_instance == null)
         {
            _instance = new ConsumerData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(datalist);
         datalist = null;
         ClearUtil.clearObject(m_dataXML);
         m_dataXML = null;
         ClearUtil.clearObject(requirinfo1);
         requirinfo1 = null;
         ClearUtil.clearObject(requirinfo2);
         requirinfo2 = null;
      }
      
      public function checkNewWeek(param1:String) : void
      {
         var _loc2_:int = 0;
         if(TimeUtil.getTimeUtil().newTimeIsNewWeek(dataTimeWeek,param1))
         {
            _loc2_ = 0;
            while(_loc2_ < datalist.length)
            {
               if(datalist[_loc2_].type == 2)
               {
                  datalist[_loc2_].nget = 0;
               }
               _loc2_++;
            }
         }
         dataTimeWeek = param1;
      }
      
      public function checkNewDay(param1:String) : void
      {
         var _loc2_:int = 0;
         if(TimeUtil.getTimeUtil().newDateIsNewDay(dataTimeDay,param1))
         {
            _loc2_ = 0;
            while(_loc2_ < datalist.length)
            {
               if(datalist[_loc2_].type == 1)
               {
                  datalist[_loc2_].nget = 0;
               }
               _loc2_++;
            }
         }
         dataTimeDay = param1;
      }
      
      private function getPaiedMoney1() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            checkNewDay(param1);
            var _loc2_:String = String(param1.split(" ")[0]) + " 00:00:01";
            var _loc3_:String = String(param1.split(" ")[0]) + " 23:59:59";
            requirinfo1.dateinfo = new DateInfor(_loc2_,_loc3_);
            requirinfo1.typeid = "dayPiedData";
            requirinfo1.failFun = requirFail;
            requirinfo1.successFun = requirSuccess;
            GetPaiedManager.getInstance().addRequir(requirinfo1);
         },null,true);
      }
      
      private function getPaiedMoney2() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            checkNewWeek(param1);
            var _loc2_:String = TimeUtil.getTimeUtil().getFirstWeekDay(param1);
            if(TimeUtil.getTimeUtil().getInTime("2017-03-13 00:00:01","2017-03-19 23:59:59",param1))
            {
               _loc2_ = "2017-03-16 00:00:01";
            }
            var _loc3_:String = String(param1.split(" ")[0]) + " 23:59:59";
            requirinfo2.dateinfo = new DateInfor(_loc2_,_loc3_);
            requirinfo2.typeid = "weekPiedData";
            requirinfo2.failFun = requirFail;
            requirinfo2.successFun = requirSuccess;
            GetPaiedManager.getInstance().addRequir(requirinfo2);
         },null,true);
      }
      
      public function requirSuccess(param1:String, param2:int) : void
      {
         if(param1 == "dayPiedData")
         {
            m_dayPaied = param2;
            m_nGetPaiedNum++;
         }
         else if(param1 == "weekPiedData")
         {
            m_weekPaied = param2;
            m_nGetPaiedNum++;
         }
         refreshData();
         if(GamingUI.getInstance().getConsumerView())
         {
            GamingUI.getInstance().getConsumerView().refresh();
         }
      }
      
      public function requirFail(param1:String) : void
      {
         if(param1 == "dayPiedData")
         {
            GamingUI.getInstance().showMessageTip("获取日消费失败");
            m_nGetPaiedNum++;
         }
         else if(param1 == "weekPiedData")
         {
            GamingUI.getInstance().showMessageTip("获取周消费失败");
            m_nGetPaiedNum++;
         }
         refreshData();
      }
      
      public function refreshData() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(m_nGetPaiedNum >= 2 && m_bGetSavedata && m_dayPaied >= 0 && m_weekPaied >= 0 && m_bXMLLoad)
         {
            m_ndayGetNum = 0;
            m_nweekGetNum = 0;
            _loc2_ = 0;
            while(_loc2_ < m_dataXML.length)
            {
               _loc1_ = 0;
               if(m_dataXML[_loc2_].id == "1001")
               {
                  _loc1_ = 0;
                  while(_loc1_ < m_dataXML[_loc2_].infoList.length)
                  {
                     if(m_dataXML[_loc2_].infoList[_loc1_].pay <= m_dayPaied)
                     {
                        if(getBGetById(m_dataXML[_loc2_].infoList[_loc1_].id) == false)
                        {
                           m_ndayGetNum++;
                        }
                     }
                     _loc1_++;
                  }
               }
               if(m_dataXML[_loc2_].id == "1002")
               {
                  _loc1_ = 0;
                  while(_loc1_ < m_dataXML[_loc2_].infoList.length)
                  {
                     if(m_dataXML[_loc2_].infoList[_loc1_].pay <= m_weekPaied)
                     {
                        if(getBGetById(m_dataXML[_loc2_].infoList[_loc1_].id) == false)
                        {
                           m_nweekGetNum++;
                        }
                     }
                     _loc1_++;
                  }
               }
               _loc2_++;
            }
         }
      }
      
      public function getBGetById(param1:String) : Boolean
      {
         var _loc2_:int = 0;
         var _loc3_:Boolean = false;
         _loc2_ = 0;
         while(_loc2_ < datalist.length)
         {
            if(param1 == datalist[_loc2_].id && datalist[_loc2_].nget == 1)
            {
               _loc3_ = true;
            }
            _loc2_++;
         }
         return _loc3_;
      }
      
      public function getDayPaied() : Number
      {
         return m_dayPaied;
      }
      
      public function getWeekPaied() : Number
      {
         return m_weekPaied;
      }
      
      public function getDayNum() : int
      {
         refreshData();
         return m_ndayGetNum;
      }
      
      public function getWeekNum() : int
      {
         refreshData();
         return m_nweekGetNum;
      }
      
      public function getAllNum() : int
      {
         refreshData();
         return m_ndayGetNum + m_nweekGetNum;
      }
      
      public function resetConsumerData() : void
      {
         m_nGetPaiedNum = 0;
         m_bGetSavedata = false;
      }
      
      public function requirePaied() : void
      {
         getPaiedMoney1();
         getPaiedMoney2();
      }
      
      public function addData(param1:PayInfoItem) : void
      {
         var _loc5_:int = 0;
         var _loc4_:String = String(param1.id.split("_")[0]);
         var _loc3_:ConsumerOne = new ConsumerOne();
         var _loc2_:Boolean = false;
         if(_loc4_ == "1001")
         {
            _loc2_ = false;
            _loc5_ = 0;
            while(_loc5_ < datalist.length)
            {
               if(datalist[_loc5_].id == param1.id)
               {
                  _loc2_ = true;
                  datalist[_loc5_].nget = 1;
                  break;
               }
               _loc5_++;
            }
            if(_loc2_ == false)
            {
               _loc3_.id = param1.id;
               _loc3_.nget = 1;
               _loc3_.type = 1;
               datalist.push(_loc3_);
            }
         }
         else if(_loc4_ == "1002")
         {
            _loc2_ = false;
            _loc5_ = 0;
            while(_loc5_ < datalist.length)
            {
               if(datalist[_loc5_].id == param1.id)
               {
                  _loc2_ = true;
                  datalist[_loc5_].nget = 1;
                  break;
               }
               _loc5_++;
            }
            if(_loc2_ == false)
            {
               _loc3_.id = param1.id;
               _loc3_.nget = 1;
               _loc3_.type = 2;
               datalist.push(_loc3_);
            }
         }
      }
      
      public function initSaveData(param1:XML) : void
      {
         var _loc4_:XML = null;
         var _loc8_:int = 0;
         var _loc7_:ConsumerOne = null;
         var _loc6_:XML = null;
         var _loc5_:XMLList = null;
         var _loc2_:XML = null;
         var _loc3_:XMLList = null;
         datalist.length = 0;
         if(param1.hasOwnProperty("ConsumerActivity"))
         {
            _loc4_ = param1.ConsumerActivity[0];
            _loc8_ = 0;
            if(_loc4_.hasOwnProperty("dayConsumer"))
            {
               _loc6_ = _loc4_.dayConsumer[0];
               dataTimeDay = String(_loc6_.@time);
               if(_loc6_.hasOwnProperty("data"))
               {
                  _loc5_ = _loc6_.data;
                  _loc8_ = 0;
                  while(_loc8_ < _loc5_.length())
                  {
                     _loc7_ = new ConsumerOne();
                     _loc7_.id = String(_loc5_[_loc8_].@id);
                     _loc7_.nget = int(_loc5_[_loc8_].@bGet);
                     _loc7_.type = 1;
                     datalist.push(_loc7_);
                     _loc8_++;
                  }
               }
            }
            else
            {
               dataTimeDay = TimeUtil.getTimeUtil().getTimeStr();
            }
            if(_loc4_.hasOwnProperty("weekConsumer"))
            {
               _loc2_ = _loc4_.weekConsumer[0];
               dataTimeWeek = String(_loc2_.@time);
               if(_loc2_.hasOwnProperty("data"))
               {
                  _loc3_ = _loc2_.data;
                  _loc8_ = 0;
                  while(_loc8_ < _loc3_.length())
                  {
                     _loc7_ = new ConsumerOne();
                     _loc7_.id = String(_loc3_[_loc8_].@id);
                     _loc7_.nget = int(_loc3_[_loc8_].@bGet);
                     _loc7_.type = 2;
                     datalist.push(_loc7_);
                     _loc8_++;
                  }
               }
            }
            else
            {
               dataTimeWeek = TimeUtil.getTimeUtil().getTimeStr();
            }
         }
         else
         {
            dataTimeDay = TimeUtil.getTimeUtil().getTimeStr();
            dataTimeWeek = TimeUtil.getTimeUtil().getTimeStr();
         }
         m_bGetSavedata = true;
         loadXMLData();
         refreshData();
      }
      
      public function exploreData() : XML
      {
         var _loc2_:XML = null;
         var _loc3_:XML = <ConsumerActivity />;
         var _loc5_:int = 0;
         var _loc4_:XML = <dayConsumer />;
         if(dataTimeDay)
         {
            _loc4_.@time = dataTimeDay;
         }
         _loc5_ = 0;
         while(_loc5_ < datalist.length)
         {
            if(datalist[_loc5_].type == 1)
            {
               _loc2_ = <data />;
               _loc2_.@id = datalist[_loc5_].id;
               _loc2_.@bGet = String(datalist[_loc5_].nget);
               _loc4_.appendChild(_loc2_);
            }
            _loc5_++;
         }
         _loc3_.appendChild(_loc4_);
         var _loc1_:XML = <weekConsumer />;
         if(dataTimeWeek)
         {
            _loc1_.@time = dataTimeWeek;
         }
         _loc5_ = 0;
         while(_loc5_ < datalist.length)
         {
            if(datalist[_loc5_].type == 2)
            {
               _loc2_ = <data />;
               _loc2_.@id = datalist[_loc5_].id;
               _loc2_.@bGet = String(datalist[_loc5_].nget);
               _loc1_.appendChild(_loc2_);
            }
            _loc5_++;
         }
         _loc3_.appendChild(_loc1_);
         return _loc3_;
      }
      
      private function loadXMLData() : void
      {
         var _loc3_:PayXMLData = null;
         var _loc2_:PayInfoItem = null;
         var _loc1_:XMLList = null;
         var _loc5_:int = 0;
         m_dataXML.length = 0;
         var _loc6_:int = 0;
         var _loc4_:XMLList = XMLSingle.getInstance().consumer.consumer;
         _loc6_ = 0;
         while(_loc6_ < _loc4_.length())
         {
            _loc3_ = new PayXMLData();
            _loc3_.id = String(_loc4_[_loc6_].@id);
            _loc3_.name = String(_loc4_[_loc6_].@name);
            _loc1_ = _loc4_[_loc6_].payitem;
            _loc5_ = 0;
            while(_loc5_ < _loc1_.length())
            {
               _loc2_ = new PayInfoItem();
               _loc2_.id = String(_loc1_[_loc5_].@id);
               _loc2_.pay = int(_loc1_[_loc5_].@pay);
               _loc2_.rewardXML = _loc1_[_loc5_];
               _loc3_.infoList.push(_loc2_);
               _loc5_++;
            }
            m_dataXML.push(_loc3_);
            _loc6_++;
         }
         m_bXMLLoad = true;
         refreshData();
      }
      
      public function getDataById(param1:String, param2:int) : PayInfoItem
      {
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_dataXML.length)
         {
            if(m_dataXML[_loc3_].id == param1)
            {
               if(param2 >= 0 && param2 <= m_dataXML[_loc3_].infoList.length - 1)
               {
                  return m_dataXML[_loc3_].infoList[param2];
               }
            }
            _loc3_++;
         }
         return null;
      }
      
      public function getLenById(param1:String) : int
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_dataXML.length)
         {
            if(m_dataXML[_loc2_].id == param1)
            {
               return m_dataXML[_loc2_].infoList.length;
            }
            _loc2_++;
         }
         return 0;
      }
   }
}

