package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class InstructionBtn extends Btn
   {
      public function InstructionBtn()
      {
         super();
         setTipString("操作指南");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("showInstruction"));
      }
   }
}

