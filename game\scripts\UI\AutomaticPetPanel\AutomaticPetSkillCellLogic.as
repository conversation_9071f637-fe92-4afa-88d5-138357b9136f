package UI.AutomaticPetPanel
{
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.MyFunction;
   import UI.Other.CDAnimationShape;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.Loader.IProgressShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class AutomaticPetSkillCellLogic
   {
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_blackLayer:Shape;
      
      private var m_timeShowMC:MultiPlaceNumLogicShell;
      
      private var m_cdAnimationShape:CDAnimationShape;
      
      private var m_skillShow:SkillShow;
      
      private var m_show:MovieClip;
      
      private var m_skillLayer:Sprite;
      
      private var m_timeShow:MovieClip;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetSkillVO:AutomaticPetSkillVO;
      
      private var m_automaticPetActiveSkillVO:AutomaticPetActiveSkillVO;
      
      private var m_isShowHaveEnoughMp:Boolean;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      public function AutomaticPetSkillCellLogic()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         m_timeShowMC = new MultiPlaceNumLogicShell();
         m_cdAnimationShape = new CDAnimationShape();
         m_skillShow = new SkillShow();
         m_skillShow.addEventListener("rollOver",onOver,false,0,true);
         m_skillShow.addEventListener("rollOut",onOut,false,0,true);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         m_blackLayer = null;
         ClearUtil.clearObject(m_timeShowMC);
         m_timeShowMC = null;
         ClearUtil.clearObject(m_cdAnimationShape);
         m_cdAnimationShape = null;
         if(m_skillShow)
         {
            m_skillShow.removeEventListener("rollOver",onOver,false);
            m_skillShow.removeEventListener("rollOut",onOut,false);
         }
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         m_show = null;
         m_skillLayer = null;
         m_timeShow = null;
         m_automaticPetVO = null;
         m_automaticPetSkillVO = null;
         m_automaticPetActiveSkillVO = null;
         m_versionControl = null;
         m_loadUI = null;
      }
      
      public function setLoadUI(param1:IProgressShow) : void
      {
         m_loadUI = param1;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setShow(param1:MovieClip, param2:MovieClip) : void
      {
         m_show = param1;
         m_timeShow = param2;
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO, param2:AutomaticPetSkillVO) : void
      {
         m_automaticPetVO = param1;
         m_automaticPetSkillVO = param2;
         initShow2();
      }
      
      public function getAutomaticPetSkillVO() : AutomaticPetSkillVO
      {
         return m_automaticPetSkillVO;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_automaticPetVO == null || m_automaticPetSkillVO == null)
         {
            return;
         }
         if(m_automaticPetActiveSkillVO)
         {
            m_cdAnimationShape.drawShape(6.283185307179586 * (m_automaticPetActiveSkillVO.getSkillCdTime() - m_automaticPetActiveSkillVO.getSkillCdRemainTime()) / m_automaticPetActiveSkillVO.getSkillCdTime());
            m_timeShowMC.showNum(Math.round(m_automaticPetActiveSkillVO.getSkillCdRemainTime() / 1000));
            if(m_automaticPetActiveSkillVO.getSkillCdRemainTime() <= 0)
            {
               m_timeShowMC.getShow().visible = false;
               m_blackLayer.visible = false;
            }
            else
            {
               m_timeShowMC.getShow().visible = true;
               m_blackLayer.visible = true;
            }
            if(m_automaticPetVO.getCurrentMp() < m_automaticPetActiveSkillVO.getSkillCostMp() && m_isShowHaveEnoughMp)
            {
               MyFunction.getInstance().changeSaturation2(m_show,-37,-46,0);
               m_isShowHaveEnoughMp = false;
            }
            else if(m_automaticPetVO.getCurrentMp() >= m_automaticPetActiveSkillVO.getSkillCostMp() && m_isShowHaveEnoughMp == false)
            {
               m_isShowHaveEnoughMp = true;
               MyFunction.getInstance().changeSaturation2(m_show,0,0,0);
            }
         }
      }
      
      protected function initShow() : void
      {
         m_skillLayer = m_show["skillLayer"];
         if(m_timeShow)
         {
            m_timeShowMC.setShow(m_timeShow);
            m_timeShow.mouseChildren = false;
            m_timeShow.mouseEnabled = false;
         }
         m_blackLayer = new Shape();
         m_show.addChild(m_blackLayer);
         m_cdAnimationShape.y = 0;
         m_cdAnimationShape.x = 0;
         m_cdAnimationShape.init(m_show.width / m_show.scaleX,m_show.height / m_show.scaleY,6736930,0.8);
         m_cdAnimationShape.drawShape(6.283185307179586);
         m_show.addChild(m_cdAnimationShape);
         if(m_timeShow)
         {
            m_timeShowMC.getShow().visible = false;
         }
         m_blackLayer.visible = false;
         drawBlackLayer();
         m_skillLayer.mask = m_cdAnimationShape;
      }
      
      protected function initShow2() : void
      {
         if(m_automaticPetSkillVO == null || m_automaticPetVO == null)
         {
            m_skillShow.init(m_loadUI,m_versionControl,null,null);
            return;
         }
         if(m_automaticPetSkillVO is AutomaticPetActiveSkillVO)
         {
            m_automaticPetActiveSkillVO = m_automaticPetSkillVO as AutomaticPetActiveSkillVO;
         }
         else
         {
            if(m_timeShow)
            {
               m_timeShowMC.getShow().visible = false;
            }
            m_blackLayer.visible = false;
            MyFunction.getInstance().changeSaturation2(m_show,0,0,0);
         }
         if(m_versionControl == null || m_loadUI == null)
         {
            return;
         }
         m_skillShow.init(m_loadUI,m_versionControl,m_automaticPetSkillVO.getIconSwfPath(),m_automaticPetSkillVO.getIconClassName());
         m_skillShow.setExtra(m_automaticPetSkillVO);
         m_skillLayer.addChild(m_skillShow);
      }
      
      protected function drawBlackLayer() : void
      {
         m_blackLayer.graphics.lineStyle();
         m_blackLayer.graphics.beginFill(0,0.3);
         m_blackLayer.graphics.drawRect(-(m_show.width / m_show.scaleX) / 2,-(m_show.height / m_show.scaleY) / 2,m_show.width / m_show.scaleX,m_show.height / m_show.scaleY);
         m_blackLayer.graphics.endFill();
      }
      
      private function onOver(param1:Event) : void
      {
         var _loc2_:AutomaticPetSkillVO = (param1.currentTarget as SkillShow).getExtra() as AutomaticPetSkillVO;
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_loc2_}));
         }
      }
      
      private function onOut(param1:Event) : void
      {
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

