package UI.EquipmentMakeAndUpgrade
{
   import UI.Animation.AnimationObject;
   import UI.Animation.M2B;
   import UI.Button.NumberBtn.NumberBtnGroup;
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Effects.Effects_Fail_Upgrade;
   import UI.Effects.Effects_Success_Upgrade;
   import UI.EquipmentCells.NpcEquipmentCell;
   import UI.EquipmentMakeAndUpgrade.Button.UpgradeEQBtn;
   import UI.Equipments.AbleEquipments.AbleEquipment;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MessageBox.MessageBoxFunction;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Protect.ProtectData;
   import UI.XMLSingle;
   import UI2.broadcast.SubmitFunction;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.GameEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipmentUpgradePanel extends MySprite implements IEquipmentProcessPanel
   {
      public var cell:NpcEquipmentCell;
      
      public var sayText:TextField;
      
      public var sayText2:TextField;
      
      public var sayText3:TextField;
      
      public var requiredMoneyText:TextField;
      
      public var upgradeBtn:UpgradeEQBtn;
      
      public var successRateText:TextField;
      
      public var upgradeFurnace:Sprite;
      
      private const GEM_X:Number = 362;
      
      private const GEM_Y:Number = 66;
      
      private const WARNING_BOX_X:Number = 120;
      
      private const WARNING_BOX_Y:Number = 70;
      
      private const FAIL_TEXT:String = "升级失败！";
      
      private const FAIL_TEXT_VIP:String = "升级失败！VIP特权生效,升级装备不降级。";
      
      private const LACK_FAIL_TEXT:String = "升级的宝石、金钱以及材料可能不足， 无法升级！";
      
      private const SUCCESS_UPGRADE:String = "升级成功！";
      
      private var ADD_EXTATTR:String = "";
      
      private const UPGRADE_EQUIPMENT_X:Number = 274;
      
      private const UPGRADE_EQUIPMENT_Y:Number = 235;
      
      private const LUCK_STONE_X:Number = 65;
      
      private const LUCK_STONE_Y:Number = 166;
      
      private const EQ_SCALE_X:Number = 1.5;
      
      private const EQ_SCALE_Y:Number = 1.5;
      
      private var _isAbleUpgrade:Boolean = false;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _player:Player;
      
      private var _successRate:Number;
      
      private var _baseSuccessRate:Number;
      
      private var _luckStone:LuckStoneShow;
      
      private var _upgradeGem:Equipment;
      
      private var _upgradeEquipmentVO:EquipmentVO;
      
      private var _upgradeEquipment:Equipment;
      
      private var _numBtnGrounp:NumberBtnGroup;
      
      public function EquipmentUpgradePanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function init() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         sayText.defaultTextFormat = new TextFormat(_loc1_.fontName,15,16776960);
         sayText2.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16776960);
         sayText3.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16776960);
         requiredMoneyText.defaultTextFormat = new TextFormat(_loc1_.fontName,15,16776960);
         successRateText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16777215);
         sayText.embedFonts = true;
         sayText2.embedFonts = true;
         sayText3.embedFonts = true;
         requiredMoneyText.embedFonts = true;
         successRateText.embedFonts = true;
         _luckStone = new LuckStoneShow();
         _luckStone.x = 65;
         _luckStone.y = 166;
         addChild(_luckStone);
         _luckStone.visible = false;
         sayText.text = "请点击装备放入";
         _isAbleUpgrade = false;
         _numBtnGrounp = new NumberBtnGroup();
         _numBtnGrounp.x = 30;
         _numBtnGrounp.y = 190;
         addChild(_numBtnGrounp);
         switchToNothing();
      }
      
      public function putInEquipmentVO(param1:EquipmentVO, param2:Player) : void
      {
         _equipmentVO = param1;
         _player = param2;
         switchToHave();
         cell.addEquipmentVO(_equipmentVO);
         cell.addEventListener("rollOver",equipmentInfor,false,0,true);
         cell.addEventListener("rollOut",equipmentInfor,false,0,true);
         _baseSuccessRate = (_equipmentVO as AbleEquipmentVO).upgradeSuccessRate;
         _successRate = _baseSuccessRate;
         successRateText.text = "成功率\n" + Math.ceil(_successRate * 100).toString() + "%";
         requiredMoneyText.text = (_equipmentVO as AbleEquipmentVO).upgradePrice.toString();
         arrangeUpgrageGem(_equipmentVO,param2);
         var _loc4_:int = MyFunction.getInstance().calculateNextLevelID(_equipmentVO.id);
         var _loc6_:AbleEquipmentVO = XMLSingle.getEquipment(_loc4_,XMLSingle.getInstance().equipmentXML) as AbleEquipmentVO;
         if(_equipmentVO.equipmentType == "precious")
         {
            addBfbAttribute(_loc6_,_equipmentVO);
         }
         else
         {
            addAttribute(_loc6_,_equipmentVO as AbleEquipmentVO);
         }
         _upgradeEquipment = new AbleEquipment(_loc6_);
         _upgradeEquipment.x = 274;
         _upgradeEquipment.y = 235;
         (_upgradeEquipment as Sprite).scaleX = 1.5;
         (_upgradeEquipment as Sprite).scaleY = 1.5;
         addChildAt(_upgradeEquipment as DisplayObject,numChildren);
         setMaxNum(_player);
         _upgradeEquipment.addEventListener("rollOver",equipmentInfor,false,0,true);
         _upgradeEquipment.addEventListener("rollOut",equipmentInfor,false,0,true);
         var _loc3_:BuyMaterialGuideBtn = new BuyMaterialGuideBtn(10500000,refreshPanel,1);
         _loc3_.x = 38;
         _loc3_.y = 225;
         addChild(_loc3_);
         var _loc5_:uint = Math.ceil((1 - _successRate) / 0.1);
         changeSuccessRate2(_loc5_);
      }
      
      public function refreshPanel() : void
      {
         var _loc3_:DisplayObject = null;
         if(_equipmentVO == null)
         {
            return;
         }
         while(numChildren > 12)
         {
            _loc3_ = getChildAt(numChildren - 1);
            removeChildAt(numChildren - 1);
            if(_loc3_ != _upgradeEquipment && _loc3_.hasOwnProperty("clear"))
            {
               _loc3_["clear"]();
            }
         }
         upgradeFurnace.visible = true;
         _successRate = 0;
         _numBtnGrounp.restore();
         switchToHave();
         _baseSuccessRate = (_equipmentVO as AbleEquipmentVO).upgradeSuccessRate;
         _successRate = _baseSuccessRate;
         successRateText.text = "成功率\n" + Math.ceil(_successRate * 100).toString() + "%";
         arrangeUpgrageGem(_equipmentVO,_player);
         _upgradeEquipment.x = 274;
         _upgradeEquipment.y = 235;
         (_upgradeEquipment as Sprite).scaleX = 1.5;
         (_upgradeEquipment as Sprite).scaleY = 1.5;
         addChildAt(_upgradeEquipment as DisplayObject,numChildren);
         setMaxNum(_player);
         var _loc1_:BuyMaterialGuideBtn = new BuyMaterialGuideBtn(10500000,refreshPanel,1);
         _loc1_.x = 38;
         _loc1_.y = 225;
         addChildAt(_loc1_,numChildren);
         var _loc2_:uint = Math.ceil((1 - _successRate) / 0.1);
         changeSuccessRate2(_loc2_);
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         var _loc4_:DisplayObject = null;
         while(numChildren > 12)
         {
            _loc4_ = getChildAt(numChildren - 1);
            removeChildAt(numChildren - 1);
            if(_loc4_.hasOwnProperty("clear"))
            {
               _loc4_["clear"]();
            }
         }
         _player = null;
         upgradeFurnace.visible = true;
         _upgradeEquipment = null;
         var _loc3_:EquipmentVO = !!_equipmentVO ? _equipmentVO : null;
         _equipmentVO = null;
         _successRate = 0;
         _numBtnGrounp.restore();
         switchToNothing();
         cell.removeEquipmentVO();
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(cell)
         {
            cell.clear();
         }
         cell = null;
         sayText = null;
         sayText2 = null;
         sayText3 = null;
         requiredMoneyText = null;
         if(upgradeBtn)
         {
            upgradeBtn.clear();
         }
         upgradeBtn = null;
         successRateText = null;
         upgradeFurnace = null;
         _equipmentVO = null;
         _player = null;
         _luckStone = null;
         _upgradeGem = null;
         _upgradeEquipment = null;
         if(_numBtnGrounp)
         {
            _numBtnGrounp.clear();
         }
         _numBtnGrounp = null;
      }
      
      private function switchToNothing() : void
      {
         sayText.visible = true;
         sayText2.visible = false;
         sayText3.visible = false;
         if(_numBtnGrounp)
         {
            _numBtnGrounp.visible = false;
         }
         requiredMoneyText.text = "";
         successRateText.visible = false;
         _luckStone.visible = false;
      }
      
      private function switchToHave() : void
      {
         sayText.visible = false;
         sayText2.visible = true;
         sayText3.visible = true;
         _numBtnGrounp.visible = true;
         successRateText.visible = true;
         _luckStone.visible = true;
      }
      
      private function addAttribute(param1:AbleEquipmentVO, param2:AbleEquipmentVO) : void
      {
         switch(param1.equipmentType)
         {
            case "precious":
               (param1 as PreciousEquipmentVO).addPlayerSaveAttr = (param2 as PreciousEquipmentVO).addPlayerSaveAttr;
               (param1 as PreciousEquipmentVO).addPlayerSaveAttrVals = (param2 as PreciousEquipmentVO).addPlayerSaveAttrVals;
               break;
            case "weapon":
               (param1 as WeaponEquipmentVO).addPlayerSaveAttr = (param2 as WeaponEquipmentVO).addPlayerSaveAttr;
               (param1 as WeaponEquipmentVO).addPlayerSaveAttrVals = (param2 as WeaponEquipmentVO).addPlayerSaveAttrVals;
               (param1 as WeaponEquipmentVO).attack = (param2 as WeaponEquipmentVO).attack + (param2 as WeaponEquipmentVO).upgradeValue;
               break;
            case "clothes":
               (param1 as ClothesEquipmentVO).addPlayerSaveAttr = (param2 as ClothesEquipmentVO).addPlayerSaveAttr;
               (param1 as ClothesEquipmentVO).addPlayerSaveAttrVals = (param2 as ClothesEquipmentVO).addPlayerSaveAttrVals;
               (param1 as ClothesEquipmentVO).defence = (param2 as ClothesEquipmentVO).defence + (param2 as ClothesEquipmentVO).upgradeValue;
               (param1 as ClothesEquipmentVO).riot = (param2 as ClothesEquipmentVO).riot + (param2 as ClothesEquipmentVO).upgradeValue2;
               break;
            case "gourd":
               (param1 as GourdEquipmentVO).addPlayerSaveAttr = (param2 as GourdEquipmentVO).addPlayerSaveAttr;
               (param1 as GourdEquipmentVO).addPlayerSaveAttrVals = (param2 as GourdEquipmentVO).addPlayerSaveAttrVals;
               (param1 as GourdEquipmentVO).maxMagic = (param2 as GourdEquipmentVO).maxMagic + (param2 as GourdEquipmentVO).upgradeValue;
               break;
            case "necklace":
               (param1 as NecklaceEquipmentVO).addPlayerSaveAttr = (param2 as NecklaceEquipmentVO).addPlayerSaveAttr;
               (param1 as NecklaceEquipmentVO).addPlayerSaveAttrVals = (param2 as NecklaceEquipmentVO).addPlayerSaveAttrVals;
               (param1 as NecklaceEquipmentVO).criticalRate = (param2 as NecklaceEquipmentVO).criticalRate + (param2 as NecklaceEquipmentVO).upgradeValue;
               break;
            case "forever_fashion":
               break;
            default:
               throw new Error("装备类型错误！");
         }
         param1.addInsetGems(param2.cloneHoleAndInsetGems());
      }
      
      private function addBfbAttribute(param1:EquipmentVO, param2:EquipmentVO) : void
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:PreciousEquipmentVO = param1 as PreciousEquipmentVO;
         var _loc3_:PreciousEquipmentVO = param2 as PreciousEquipmentVO;
         if(param1.equipmentType == "precious" && param1.level > 0)
         {
            _loc6_.setData(_loc3_);
            _loc4_ = int(_loc6_.basisUpValue.length);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(_loc3_.level == 0)
               {
                  _loc6_.basisUpValue[_loc5_] = _loc3_.basisUpValue[_loc5_];
               }
               else
               {
                  _loc6_.basisUpValue[_loc5_] = _loc3_.basisUpValue[_loc5_] / _loc3_.level * _loc6_.level;
                  if(_loc6_.basisUpValue[_loc5_] + _loc3_.basisAttrValue[_loc5_] > 10 && _loc6_.basisAttr[_loc5_] == "addshanbi")
                  {
                     _loc6_.basisUpValue[_loc5_] = 10 - _loc3_.basisAttrValue[_loc5_];
                  }
               }
               _loc5_++;
            }
         }
         _loc6_.addInsetGems(_loc3_.cloneHoleAndInsetGems());
      }
      
      private function addForeverFashionAttribute(param1:EquipmentVO) : void
      {
         var _loc9_:ForeverFashionEquipmentVO = null;
         var _loc4_:* = undefined;
         var _loc14_:* = undefined;
         var _loc5_:int = 0;
         var _loc3_:Boolean = false;
         var _loc8_:int = 0;
         var _loc13_:XMLList = null;
         var _loc15_:XMLList = null;
         var _loc16_:int = 0;
         var _loc2_:* = undefined;
         var _loc7_:* = undefined;
         var _loc10_:int = 0;
         var _loc12_:* = undefined;
         var _loc17_:int = 0;
         var _loc6_:int = 0;
         var _loc11_:Array = null;
         if(param1.equipmentType == "forever_fashion")
         {
            if(param1.level == 9)
            {
               _loc9_ = param1 as ForeverFashionEquipmentVO;
               _loc4_ = new Vector.<String>();
               _loc14_ = new Vector.<Number>();
               _loc5_ = int(_loc9_.addPlayerAttributes.length);
               _loc3_ = false;
               _loc8_ = 0;
               for(; _loc8_ < _loc5_; _loc8_++)
               {
                  if(_loc3_ == false)
                  {
                     if(_loc9_.addPlayerAttributes[_loc8_] == _loc9_.addExtraAttrEx && _loc9_.addPlayerAttributeValues[_loc8_] == _loc9_.addExtraAttrValue)
                     {
                        _loc3_ = true;
                        continue;
                     }
                  }
                  _loc4_.push(_loc9_.addPlayerAttributes[_loc8_]);
                  _loc14_.push(_loc9_.addPlayerAttributeValues[_loc8_]);
               }
               _loc13_ = XMLSingle.getInstance().dataXML.ForeverFashionAddAttr;
               _loc15_ = _loc13_.value;
               _loc16_ = int(_loc15_.length());
               _loc2_ = new Vector.<String>();
               _loc7_ = new Vector.<Number>();
               _loc10_ = 0;
               while(_loc10_ < _loc16_)
               {
                  _loc2_.push(String(_loc15_[_loc10_].@addPlayerAttribute));
                  _loc12_ = MyFunction.getInstance().excreteStringToNumber(String(_loc15_[_loc10_].@addPlayerAttributeValue));
                  _loc17_ = Math.floor(Math.random() * _loc12_.length);
                  _loc7_.push(_loc12_[_loc17_]);
                  _loc10_++;
               }
               _loc6_ = Math.floor(Math.random() * _loc16_);
               _loc9_.addExtraAttrEx = _loc2_[_loc6_];
               _loc9_.addExtraAttrValue = _loc7_[_loc6_];
               _loc4_.push(_loc9_.addExtraAttrEx);
               _loc14_.push(_loc9_.addExtraAttrValue);
               _loc9_.addPlayerAttributes = _loc4_;
               _loc9_.addPlayerAttributeValues = _loc14_;
               GamingUI.getInstance().refresh(515);
               _loc11_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc9_.addExtraAttrEx,_loc9_.addExtraAttrValue);
               ADD_EXTATTR += "恭喜获得了额外属性<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc11_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc11_[1],16);
            }
         }
      }
      
      private function decAttribute(param1:AbleEquipmentVO, param2:AbleEquipmentVO) : void
      {
         switch(param1.equipmentType)
         {
            case "weapon":
               (param1 as WeaponEquipmentVO).attack = (param2 as WeaponEquipmentVO).attack - (param1 as WeaponEquipmentVO).upgradeValue;
               break;
            case "clothes":
               (param1 as ClothesEquipmentVO).defence = (param2 as ClothesEquipmentVO).defence - (param1 as ClothesEquipmentVO).upgradeValue;
               (param1 as ClothesEquipmentVO).riot = (param2 as ClothesEquipmentVO).riot - (param1 as ClothesEquipmentVO).upgradeValue2;
               break;
            case "gourd":
               (param1 as GourdEquipmentVO).maxMagic = (param2 as GourdEquipmentVO).maxMagic - (param1 as GourdEquipmentVO).upgradeValue;
               break;
            case "necklace":
               (param1 as NecklaceEquipmentVO).criticalRate = (param2 as NecklaceEquipmentVO).criticalRate - (param1 as NecklaceEquipmentVO).upgradeValue;
               break;
            case "forever_fashion":
            case "precious":
               break;
            default:
               throw new Error("装备类型错误！");
         }
      }
      
      private function arrangeUpgrageGem(param1:EquipmentVO, param2:Player) : void
      {
         var _loc11_:int = 0;
         var _loc4_:BuyMaterialGuideBtn = null;
         var _loc8_:TextFormat = null;
         var _loc6_:TextFormat = null;
         var _loc3_:BuyGoldPocketGuideBtn = null;
         var _loc7_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         var _loc5_:TextField = new TextField();
         _loc5_.embedFonts = true;
         _loc5_.selectable = false;
         _loc5_.mouseEnabled = false;
         _loc5_.autoSize = "center";
         if(param1.equipmentType == "forever_fashion")
         {
            _upgradeGem = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(returnfashionIDForForeverFashion(param1.id),XMLSingle.getInstance().equipmentXML));
         }
         else if(param1.equipmentType == "precious")
         {
            _upgradeGem = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID((param1 as PreciousEquipmentVO).materialid,XMLSingle.getInstance().equipmentXML));
         }
         else
         {
            _upgradeGem = MyFunction2.sheatheEquipmentShell(getUpgradeGem(param1));
         }
         _upgradeGem.addEventListener("rollOver",equipmentInfor,false,0,true);
         _upgradeGem.addEventListener("rollOut",equipmentInfor,false,0,true);
         _upgradeGem.x = 362;
         _upgradeGem.y = 66;
         addChild(_upgradeGem as DisplayObject);
         for each(var _loc10_ in param2.playerVO.packageEquipmentVOs)
         {
            if(_loc10_)
            {
               if(_loc10_.id == _upgradeGem.equipmentVO.id && !_loc10_.isPutInOperate)
               {
                  if(_loc10_ is StackEquipmentVO)
                  {
                     _loc11_ += (_loc10_ as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc11_++;
                  }
               }
            }
         }
         if(_loc11_ >= (param1 as AbleEquipmentVO).upgradeGemNum)
         {
            _loc5_.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#00ff33\'>" + _loc11_.toString() + "/" + (param1 as AbleEquipmentVO).upgradeGemNum.toString() + "</font>";
            _isAbleUpgrade = true;
         }
         else
         {
            _loc5_.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#00ff33\'>" + "<font color=\'#ff0000\'>" + _loc11_.toString() + "</font>" + "/" + (param1 as AbleEquipmentVO).upgradeGemNum.toString() + "</font>";
            _isAbleUpgrade = false;
            if(param1.equipmentType != "precious")
            {
               _loc4_ = new BuyMaterialGuideBtn(_upgradeGem.equipmentVO.id,refreshPanel,1);
               _loc4_.x = 338;
               _loc4_.y = 100;
               addChildAt(_loc4_,numChildren);
            }
         }
         _loc5_.width = _loc5_.textWidth + 5;
         _loc5_.height = _loc5_.textHeight + 5;
         _loc5_.x = _upgradeGem.x + _upgradeGem.width / 2 - _loc5_.textWidth;
         _loc5_.y = _upgradeGem.y + _upgradeGem.height / 2 - _loc5_.textHeight;
         addChildAt(_loc5_,numChildren);
         _loc5_.filters = [filter()];
         var _loc9_:* = (param1 as AbleEquipmentVO).upgradePrice <= _player.playerVO.money;
         if(_loc9_)
         {
            _loc8_ = new TextFormat(_loc7_.fontName,15,16776960);
            requiredMoneyText.setTextFormat(_loc8_);
            requiredMoneyText.width = requiredMoneyText.textWidth + 15;
         }
         else
         {
            _loc6_ = new TextFormat(_loc7_.fontName,15,16711680);
            requiredMoneyText.setTextFormat(_loc6_);
            requiredMoneyText.width = requiredMoneyText.textWidth + 15;
            _loc3_ = new BuyGoldPocketGuideBtn(11100000,refreshPanel,1);
            _loc3_.x = requiredMoneyText.x + requiredMoneyText.textWidth + 5;
            _loc3_.y = 286;
            addChildAt(_loc3_,numChildren);
         }
         _isAbleUpgrade &&= _loc9_;
      }
      
      private function getUpgradeGem(param1:EquipmentVO) : EquipmentVO
      {
         var _loc12_:int = 0;
         var _loc4_:String = null;
         var _loc9_:String = null;
         var _loc7_:int = 0;
         var _loc10_:String = null;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:Array = null;
         var _loc2_:XML = XMLSingle.getInstance().dataXML.EquipmentUpgradeMaterial[0];
         var _loc11_:XMLList = _loc2_.item;
         var _loc8_:int = int(!!_loc11_ ? _loc11_.length() : 0);
         _loc12_ = 0;
         while(_loc12_ < _loc8_)
         {
            _loc4_ = String(_loc11_[_loc12_].@equipmentType);
            _loc9_ = String(_loc11_[_loc12_].@implictProPlayerId);
            _loc10_ = String(_loc11_[_loc12_].@levelRange);
            if(Boolean(_loc4_) == false || param1.equipmentType == _loc4_)
            {
               if(Boolean(_loc9_) == false || param1 is AbleEquipmentVO && (param1 as AbleEquipmentVO).implictProPlayerId == _loc9_)
               {
                  if(_loc10_)
                  {
                     _loc5_ = _loc10_.split("_");
                     _loc6_ = int(_loc5_[0]);
                     _loc3_ = int(_loc5_[1]);
                     ClearUtil.clearObject(_loc5_);
                     _loc5_ = null;
                  }
                  if(Boolean(_loc10_) == false || param1.level >= _loc6_ && param1.level <= _loc3_)
                  {
                     _loc7_ = int(_loc11_[_loc12_].@materialId);
                     break;
                  }
               }
            }
            _loc12_++;
         }
         if(_loc7_ == 0)
         {
            _loc7_ = 10500008;
         }
         return XMLSingle.getEquipment(_loc7_,XMLSingle.getInstance().equipmentXML);
      }
      
      private function deleEquipmentVOs(param1:EquipmentVO, param2:Player) : void
      {
         MyFunction.getInstance().minusEquipmentVOs(param2.playerVO.packageEquipmentVOs,(param1 as AbleEquipmentVO).upgradeGemNum,_upgradeGem.equipmentVO.id);
         MyFunction.getInstance().minusEquipmentVOs(param2.playerVO.packageEquipmentVOs,_numBtnGrounp.num,10500000);
      }
      
      public function setMaxNum(param1:Player) : void
      {
         var equipVO:EquipmentVO;
         var player:Player = param1;
         var maxNum:int = 0;
         _numBtnGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":param1,
               "flag":0
            }));
         },["背包中的幸运石为" + maxNum + "个, 没有更多了！"]);
         for each(equipVO in player.playerVO.packageEquipmentVOs)
         {
            if(equipVO)
            {
               if(equipVO.className == "LuckStone")
               {
                  maxNum += (equipVO as StackEquipmentVO).num;
                  _numBtnGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":param1,
                        "flag":0
                     }));
                  },["背包中的幸运石为" + maxNum + "个, 没有更多了！"]);
               }
            }
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         upgradeBtn.addEventListener("clickUpgradeEquipmentBtn",upgrade,false,0,true);
         addEventListener("changeNum",changeSuccessRate,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         upgradeBtn.removeEventListener("clickUpgradeEquipmentBtn",upgrade,false);
         removeEventListener("changeNum",changeSuccessRate,true);
      }
      
      private function upgrade(param1:UIBtnEvent) : void
      {
         var n:Number;
         var e:UIBtnEvent = param1;
         if(cell.isHaveChild)
         {
            n = Math.random();
            if(_isAbleUpgrade)
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"升级过程中请勿刷新！",
                  "flag":3,
                  "task":{
                     "type":"EquipmentUprade",
                     "okFunction":function():void
                     {
                        MyFunction2.getUserStateIsRightFunction(function():void
                        {
                           var _loc1_:SaveTaskInfo = null;
                           var _loc2_:SaveTaskInfo = null;
                           cell.removeEquipmentVO();
                           _baseSuccessRate = (_equipmentVO as AbleEquipmentVO).upgradeSuccessRate;
                           _successRate = _baseSuccessRate + _numBtnGrounp.num * (AntiwearNumber.nums["consts_10"] / AntiwearNumber.nums["consts_100"]);
                           if(n <= _successRate)
                           {
                              addForeverFashionAttribute(_upgradeEquipment.equipmentVO);
                              if(MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_upgradeEquipment.equipmentVO,_equipmentVO))
                              {
                                 if(_upgradeEquipment.equipmentVO.equipmentType == "precious")
                                 {
                                    SubmitFunction.getInstance().setData3(9,(_upgradeEquipment.equipmentVO as PreciousEquipmentVO).implictProPlayerId,_upgradeEquipment.equipmentVO.name,_upgradeEquipment.equipmentVO.level);
                                 }
                                 else if(_upgradeEquipment.equipmentVO is AbleEquipmentVO)
                                 {
                                    SubmitFunction.getInstance().setData3(11,(_upgradeEquipment.equipmentVO as AbleEquipmentVO).implictProPlayerId,_upgradeEquipment.equipmentVO.name,_upgradeEquipment.equipmentVO.level);
                                 }
                              }
                              _player.playerVO.money -= (_equipmentVO as AbleEquipmentVO).upgradePrice;
                              deleEquipmentVOs(_equipmentVO,_player);
                              _equipmentVO = null;
                              GamingUI.getInstance().addMainLineTaskGoalGameEventStr("EquipmentUpgrade");
                              GamingUI.getInstance().addMainLineTaskGoalGameEventStr("EquipmentUpgradeTo" + _upgradeEquipment.equipmentVO.level);
                              GameEvent.eventDispacher.dispatchEvent(new GameEvent("rundetector"));
                              _loc1_ = new SaveTaskInfo();
                              _loc1_.type = "4399";
                              _loc1_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc1_);
                              MyFunction2.saveGame2();
                              resultSuccessAnimation();
                           }
                           else
                           {
                              _player.playerVO.money -= (_equipmentVO as AbleEquipmentVO).upgradePrice;
                              deleEquipmentVOs(_equipmentVO,_player);
                              if(_player.vipVO.isLostEquipmentInUpgrade && ProtectData.getInstance().isLostEquipmentInUpgrade)
                              {
                                 if(_equipmentVO.level)
                                 {
                                    DemotionEquipmentVO(_equipmentVO);
                                 }
                              }
                              if(_equipmentVO)
                              {
                                 MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_equipmentVO,_equipmentVO);
                              }
                              _loc2_ = new SaveTaskInfo();
                              _loc2_.type = "4399";
                              _loc2_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc2_);
                              MyFunction2.saveGame2();
                              resultFailAnimation();
                           }
                        },function(param1:String, param2:int):void
                        {
                           dispatchEvent(new UIPassiveEvent("showWarningBox",{
                              "text":param1,
                              "flag":param2
                           }));
                        },true);
                     }
                  }
               }));
            }
            else
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"升级的宝石、金钱以及材料可能不足， 无法升级！",
                  "flag":1
               }));
            }
         }
      }
      
      private function changeSuccessRate(param1:UIPassiveEvent) : void
      {
         _successRate = _baseSuccessRate + _numBtnGrounp.num * (AntiwearNumber.nums["consts_10"] / AntiwearNumber.nums["consts_100"]);
         if(_successRate > 1)
         {
            _successRate = 1;
         }
         if(_successRate < 0)
         {
            _successRate = 0;
         }
         successRateText.text = "成功率\n " + Math.ceil(_successRate * 100).toString() + "%";
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGrounp.num = param1;
         changeSuccessRate(null);
      }
      
      private function resultSuccessAnimation() : void
      {
         parent.parent.mouseChildren = false;
         parent.parent.mouseEnabled = false;
         upgradeFurnace.visible = false;
         var _loc1_:Effects_Success_Upgrade = new Effects_Success_Upgrade();
         var _loc2_:AnimationObject = new AnimationObject();
         _loc2_.x = upgradeFurnace.x;
         _loc2_.y = upgradeFurnace.y;
         addChild(_loc2_);
         _loc2_.imgList = M2B.transformM2B(_loc1_,false);
         _loc2_.repeatCount = 1;
         _loc2_.delay = 60;
         _loc2_.play();
         _loc2_.addEventListener("play over event",playerOver,false,0,true);
      }
      
      private function resultFailAnimation() : void
      {
         parent.parent.mouseChildren = false;
         parent.parent.mouseEnabled = false;
         upgradeFurnace.visible = false;
         var _loc1_:Effects_Fail_Upgrade = new Effects_Fail_Upgrade();
         var _loc2_:AnimationObject = new AnimationObject();
         _loc2_.x = upgradeFurnace.x;
         _loc2_.y = upgradeFurnace.y;
         addChild(_loc2_);
         _loc2_.imgList = M2B.transformM2B(_loc1_,false);
         _loc2_.repeatCount = 1;
         _loc2_.delay = 60;
         _loc2_.play();
         _loc2_.addEventListener("play over event",playerOver2,false,0,true);
      }
      
      private function resultAnimation() : void
      {
         upgradeFurnace.visible = true;
         if(_upgradeEquipment)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"升级成功！" + ADD_EXTATTR,
               "flag":1
            }));
            TweenLite.to(_upgradeEquipment,2,{
               "alpha":0.5,
               "x":600,
               "y":100,
               "ease":Back.easeIn,
               "onComplete":onFinishTween
            });
            ADD_EXTATTR = "";
         }
      }
      
      private function playerOver(param1:Event) : void
      {
         resultAnimation();
      }
      
      private function playerOver2(param1:Event) : void
      {
         var _loc2_:String = null;
         if(_player.vipVO.isLostEquipmentInUpgrade)
         {
            _loc2_ = "升级失败！";
         }
         else
         {
            _loc2_ = "升级失败！VIP特权生效,升级装备不降级。";
         }
         putOutEquipmentVO();
         _player = null;
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":_loc2_,
            "flag":1
         }));
         dispatchEvent(new UIPassiveEvent("refreshAtt",34));
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
      }
      
      private function onFinishTween() : void
      {
         try
         {
            putOutEquipmentVO();
            _player = null;
            dispatchEvent(new UIPassiveEvent("refreshAtt",34));
         }
         catch(error:ArgumentError)
         {
            trace(error.message);
         }
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               if(param1.target == _upgradeEquipment)
               {
                  dispatchEvent(new UIPassiveEvent("showMessageBox",{
                     "equipment":_upgradeEquipment,
                     "messageBoxMode":2
                  }));
                  break;
               }
               if(param1.target is NpcEquipmentCell)
               {
                  dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":(param1.target as NpcEquipmentCell).equipment}));
                  break;
               }
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.target}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function filter() : GlowFilter
      {
         return new GlowFilter(0,1,2,2,10,3);
      }
      
      private function DemotionEquipmentVO(param1:EquipmentVO) : void
      {
         var _loc2_:int = MyFunction.getInstance().calculateUpLevelID(param1.id);
         var _loc3_:AbleEquipmentVO = XMLSingle.getEquipment(_loc2_,XMLSingle.getInstance().equipmentXML) as AbleEquipmentVO;
         decAttribute(_loc3_,param1 as AbleEquipmentVO);
         param1 = _loc3_;
      }
      
      private function returnfashionIDForForeverFashion(param1:int) : int
      {
         return int("109000" + MyFunction.getInstance().getIDFiveString(param1));
      }
   }
}

