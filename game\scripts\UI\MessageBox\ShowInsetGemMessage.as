package UI.MessageBox
{
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   
   public class ShowInsetGemMessage
   {
      public function ShowInsetGemMessage()
      {
         super();
      }
      
      public function addMessage(param1:InsetGemEquipmentVO) : String
      {
         var _loc2_:String = "";
         if(param1)
         {
            _loc2_ += MessageBoxFunction.getInstance().toHTMLText(param1.description,14);
         }
         return _loc2_;
      }
   }
}

