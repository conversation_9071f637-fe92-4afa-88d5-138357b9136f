package UI.PKUI
{
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.EntityShowContainer;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class RankListPanel extends MySprite
   {
      protected const NUM_ONE_PAGE:int = 7;
      
      protected var m_show:MovieClip;
      
      protected var m_showStageMC:MovieClipPlayLogicShell;
      
      protected var m_quitBtn:ButtonLogicShell;
      
      protected var m_playerNameText:TextField;
      
      protected var m_lookUpBtn:ButtonLogicShell;
      
      protected var m_myRankText:TextField;
      
      protected var m_myScoreText:TextField;
      
      protected var m_columes:Vector.<PKRankListColume>;
      
      protected var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      protected var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      protected var _isTwoMode:Boolean;
      
      protected var _otherPanel:PKPanelOne;
      
      protected var _rankObject:Object;
      
      protected var _rankId:int;
      
      protected var _player1ShowContainer:EntityShowContainer;
      
      protected var _player2ShowContainer:EntityShowContainer;
      
      protected var _gamingUI:GamingUI;
      
      protected var _isShowAutomatic:Boolean;
      
      protected var m_curRank:int = 0;
      
      public var putBtnForName:String = "";
      
      public function RankListPanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         PlayerDataForPK.getInstance().clearRankListTsData();
         m_show = null;
         ClearUtil.clearObject(m_showStageMC);
         m_showStageMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_playerNameText = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         m_myRankText = null;
         m_myScoreText = null;
         ClearUtil.nullArr(m_columes);
         m_columes = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         if(_otherPanel)
         {
            _otherPanel.clear();
         }
         _otherPanel = null;
         _rankObject = null;
         ClearUtil.clearObject(_player1ShowContainer);
         _player1ShowContainer = null;
         ClearUtil.clearObject(_player2ShowContainer);
         _player2ShowContainer = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         _gamingUI = param1;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("clickQuitBtn",quit,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("clickLookUpBtn",lookUpData,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("clickQuitBtn",quit,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("clickLookUpBtn",lookUpData,true);
      }
      
      protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         switch(param1.button)
         {
            case m_pageBtnGroup:
               quit();
               getRankListData(null,arrangeRankList);
               return;
            case m_quitBtn:
               quit();
               _gamingUI.closeRankList();
               return;
            case m_lookUpBtn:
               lookUpData();
         }
         var _loc2_:int = int(m_columes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_columes[_loc3_])
            {
               refreshColume(m_columes[_loc3_].rankObject);
               return;
            }
            _loc3_++;
         }
      }
      
      public function lookUpData() : void
      {
         if(!_otherPanel)
         {
            _otherPanel = new PKPanelOne();
            _otherPanel.isTwoMode = _isTwoMode;
            _otherPanel.initPKPanel(PlayerDataForPK.getInstance().playersInRankList,PlayerDataForPK.getInstance().playersInRankListNicknameData);
         }
         if(!getChildByName(_otherPanel.name))
         {
            addChild(_otherPanel);
         }
      }
      
      public function quit(param1:UIBtnEvent = null) : void
      {
         if(!Boolean(_otherPanel) || Boolean(param1) && param1.target.parent != _otherPanel)
         {
            return;
         }
         if(Boolean(_otherPanel) && getChildByName(_otherPanel.name))
         {
            removeChild(_otherPanel);
         }
         if(_otherPanel)
         {
            _otherPanel.clear();
            _otherPanel = null;
         }
      }
      
      protected function refreshColume(param1:Object = null) : void
      {
         quit();
         var _loc2_:int = numChildren;
         _rankObject = param1;
         if(_rankObject)
         {
            GamingUI.getInstance().lockGamingUI("加载中...");
            PKFunction.getInstance().getUserData(_rankObject.uId,_rankObject.index,null,getUserAfterFun);
         }
      }
      
      private function getUserAfterFun(param1:Object) : void
      {
         PKFunction.getInstance().initPlayerInRankList(param1.data,param1.index,funAfterInitPlayer);
      }
      
      private function funAfterInitPlayer() : void
      {
         var _loc2_:AutomaticPetVO = null;
         var _loc1_:Sprite = new Sprite();
         if(_isShowAutomatic == false)
         {
            if(!_isTwoMode && putBtnForName != "")
            {
               if(PlayerDataForPK.getInstance().playersInRankList[1])
               {
                  if(putBtnForName == PlayerDataForPK.getInstance().playersInRankList[0].playerVO.name)
                  {
                     _player1ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[0].playerVO);
                  }
                  else if(putBtnForName == PlayerDataForPK.getInstance().playersInRankList[1].playerVO.name)
                  {
                     _player1ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[1].playerVO);
                  }
                  else
                  {
                     _player1ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[0].playerVO);
                  }
               }
               else
               {
                  _player1ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[0].playerVO);
               }
            }
            else
            {
               _player1ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[0].playerVO);
            }
            if(_isTwoMode && PlayerDataForPK.getInstance().playersInRankList[1])
            {
               _player2ShowContainer.refreshPlayerShow(PlayerDataForPK.getInstance().playersInRankList[1].playerVO);
               _player1ShowContainer.getShow().x = -35;
               _player2ShowContainer.getShow().x = 35;
               _loc1_.addChildAt(_player1ShowContainer.getShow(),0);
               _loc1_.addChildAt(_player2ShowContainer.getShow(),0);
            }
            else
            {
               _player1ShowContainer.getShow().x = 0;
               if(_player2ShowContainer)
               {
                  _player2ShowContainer.getShow().x = 0;
               }
               _loc1_.addChild(_player1ShowContainer.getShow());
            }
            _loc1_.scaleX = _loc1_.scaleY = 1.5;
         }
         else if(Boolean(_rankObject.extra) && (_rankObject.extra as Object).hasOwnProperty("autoPet"))
         {
            _loc2_ = new AutomaticPetVO();
            _loc2_.initFromSaveML(_rankObject.extra.autoPet[0],XMLSingle.getInstance().automaticPetsXML);
            _player1ShowContainer.refreshAutomaticPetShow(_loc2_);
            _player1ShowContainer.getShow().x = 0;
            if(_player2ShowContainer)
            {
               _player2ShowContainer.getShow().x = 0;
            }
            _loc1_.addChild(_player1ShowContainer.getShow());
            _loc1_.scaleX = _loc1_.scaleY = 1;
         }
         if(_rankObject.rank <= 3)
         {
            m_showStageMC.gotoAndStop("" + _rankObject.rank);
         }
         else
         {
            m_showStageMC.gotoAndStop("4");
         }
         ClearUtil.clearDisplayObjectInContainer(m_showStageMC.getShow()["playerShowContainer"]);
         m_showStageMC.getShow()["playerShowContainer"].addChild(_loc1_);
         PlayerDataForPK.getInstance().playersInRankList[0].playerVO.playerUid = !!_rankObject.uId ? _rankObject.uId : "";
         if(PlayerDataForPK.getInstance().playersInRankList[1])
         {
            PlayerDataForPK.getInstance().playersInRankList[1].playerVO.playerUid = !!_rankObject.uId ? _rankObject.uId : "";
         }
         m_playerNameText.text = !!_rankObject.uId ? _rankObject.uId : "";
         _rankObject = null;
         GamingUI.getInstance().unLockGamingUI();
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,!!_otherPanel ? _otherPanel.currentPlayer : null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function getRankListData(param1:Function, param2:Function) : void
      {
         PKFunction.getInstance().getRankListData(_rankId,param1,param2,m_pageBtnGroup.pageNum,7);
      }
      
      protected function getMyDataByUserName(param1:Function, param2:Function) : void
      {
         PKFunction.getInstance().getOneRankListDataByUserName(_rankId,GameData.getInstance().getLoginReturnData().getName(),param1,param2);
      }
      
      protected function arrangeRankList(param1:Array) : void
      {
         var _loc3_:PKRankListColume = null;
         var _loc4_:int = 0;
         var _loc2_:int = !!param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = m_columes[_loc4_];
            _loc3_.m_rId = _rankId;
            _loc3_.rankObject = param1[_loc4_];
            _loc3_.getShow().visible = true;
            _loc4_++;
         }
         _loc2_ = !!m_columes ? m_columes.length : 0;
         while(_loc4_ < _loc2_)
         {
            m_columes[_loc4_].getShow().visible = false;
            _loc4_++;
         }
         if(param1.length > 0)
         {
            m_columes[0].turnActiveAndDispatchEvent();
         }
      }
      
      protected function initRankListData(param1:Number = 0) : void
      {
         var score:Number = param1;
         var PkRankListPanel:RankListPanel = this;
         getMyDataByUserName(null,function(param1:Array):void
         {
            var i:int;
            var length:int;
            var isHave:Boolean;
            var arr:Array = param1;
            if(!arr || arr.length == 0)
            {
               m_myRankText.text = "暂无排名";
               m_myScoreText.text = score.toString();
            }
            else
            {
               length = int(arr.length);
               isHave = false;
               i = 0;
               while(i < length)
               {
                  if(arr[i].index == GameData.getInstance().getSaveFileData().index)
                  {
                     m_myRankText.text = arr[i].rank.toString();
                     m_curRank = arr[i].rank;
                     m_myScoreText.text = score.toString();
                     isHave = true;
                     break;
                  }
                  i++;
               }
               if(!isHave)
               {
                  m_myRankText.text = "暂无排名";
                  m_myScoreText.text = score.toString();
               }
               i = 0;
               while(i < length)
               {
                  arr[i] = null;
                  i++;
               }
               arr = null;
            }
            getRankListData(null,function(param1:Array):void
            {
               arrangeRankList(param1);
               length = !!param1 ? param1.length : 0;
               i = 0;
               while(i < length)
               {
                  param1[i] = null;
                  i++;
               }
               param1 = null;
            });
         });
      }
      
      public function init(param1:int, param2:Number) : void
      {
         var _loc5_:int = 0;
         var _loc3_:PKRankListColume = null;
         var _loc4_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_showStageMC = new MovieClipPlayLogicShell();
         m_showStageMC.setShow(m_show["showAnimation"]);
         m_quitBtn = new ButtonLogicShell();
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击关闭");
         m_playerNameText = m_show["otherPlayerNameText"];
         MyFunction2.changeTextFieldFont(_loc4_.fontName,m_playerNameText);
         m_lookUpBtn = new ButtonLogicShell();
         m_lookUpBtn.setShow(m_show["lookUpBtn"]);
         m_lookUpBtn.setTipString("点击查看详细装备信息");
         m_myRankText = m_show["myRankText"];
         MyFunction2.changeTextFieldFont(_loc4_.fontName,m_myRankText);
         m_myScoreText = m_show["myScoreText"];
         MyFunction2.changeTextFieldFont(_loc4_.fontName,m_myScoreText);
         m_columes = new Vector.<PKRankListColume>();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         _loc5_ = 0;
         while(_loc5_ < 7)
         {
            _loc3_ = new PKRankListColume();
            _loc3_.setShow(m_show["colume" + (_loc5_ + 1)]);
            _loc3_.setTipString("点击查看该玩家");
            _loc3_.getShow().visible = false;
            m_columes.push(_loc3_);
            m_switchBtnGroup.addSwitchBtn(_loc3_);
            _loc5_++;
         }
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtn"]);
         m_pageBtnGroup.initPageNumber(1,7);
         _player1ShowContainer = new EntityShowContainer();
         _player1ShowContainer.init();
         if(_isTwoMode)
         {
            _player2ShowContainer = new EntityShowContainer();
            _player2ShowContainer.init();
         }
         _rankId = param1;
         initRankListData(param2);
      }
   }
}

