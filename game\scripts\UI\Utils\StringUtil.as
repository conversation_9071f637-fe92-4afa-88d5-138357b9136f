package UI.Utils
{
   public class StringUtil
   {
      public function StringUtil()
      {
         super();
      }
      
      public function cloneStrings(param1:Array) : Array
      {
         var _loc4_:int = 0;
         var _loc2_:Array = null;
         var _loc3_:int = !!param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_ == null)
            {
               _loc2_ = [];
            }
            _loc2_.push(param1[_loc4_]);
            _loc4_++;
         }
         return _loc2_;
      }
   }
}

