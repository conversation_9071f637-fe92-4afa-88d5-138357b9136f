package UI.MainLineTask
{
   public class TaskGoalVOFactory
   {
      public function TaskGoalVOFactory()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function createOneTaskGoalByGameEventStr(param1:String, param2:XML) : TaskGoalVO_MainTask
      {
         var _loc3_:XML = param2.taskGoal.(@gameEventStr == param1)[0];
         if(_loc3_ == null)
         {
            return null;
         }
         var _loc4_:TaskGoalVO_MainTask = new TaskGoalVO_MainTask();
         _loc4_.id = String(_loc3_.@id);
         _loc4_.gameEventStr = param1;
         _loc4_.name = String(_loc3_.@name);
         _loc4_.num = 1;
         return _loc4_;
      }
      
      public function createTaskGoalsByXML(param1:XML, param2:XML) : Vector.<TaskGoalVO_MainTask>
      {
         var _loc6_:TaskGoalVO_MainTask = null;
         var _loc7_:int = 0;
         var _loc5_:XMLList = param1.children();
         var _loc4_:int = int(_loc5_.length());
         var _loc3_:Vector.<TaskGoalVO_MainTask> = new Vector.<TaskGoalVO_MainTask>();
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc6_ = createOneTaskGoalByIdAndNum(_loc5_[_loc7_].@id,_loc5_[_loc7_].@num,param2);
            _loc3_.push(_loc6_);
            _loc7_++;
         }
         return _loc3_;
      }
      
      public function createOneTaskGoalByIdAndNum(param1:String, param2:int, param3:XML) : TaskGoalVO_MainTask
      {
         var _loc4_:TaskGoalVO_MainTask = new TaskGoalVO_MainTask();
         _loc4_.id = param1;
         _loc4_.num = param2;
         _loc4_.gameEventStr = String(param3.taskGoal.(@id == param1)[0].@gameEventStr);
         return _loc4_;
      }
   }
}

