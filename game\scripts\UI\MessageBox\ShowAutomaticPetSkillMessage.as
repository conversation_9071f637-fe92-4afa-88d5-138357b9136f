package UI.MessageBox
{
   import UI.AutomaticPetPanel.SkillShow;
   import UI.AutomaticPetPanel.SkillShowListener;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAuxiliarySkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetPassiveSkillVO1;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class ShowAutomaticPetSkillMessage
   {
      private var m_skillShow:SkillShow;
      
      private var m_skillShowListener:SkillShowListener;
      
      private var m_description:String;
      
      private var m_box:MessageBox;
      
      public function ShowAutomaticPetSkillMessage()
      {
         super();
         m_skillShow = new SkillShow();
         m_skillShow.init(Part1.getInstance().getLoadUI(),Part1.getInstance().getVersionControl(),null,null);
         m_skillShowListener = new SkillShowListener();
         m_skillShowListener.showSkillCompleteFun = showSkillComplete;
         m_skillShow.addSkillShowListener(m_skillShowListener);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         ClearUtil.clearObject(m_skillShowListener);
         m_skillShowListener = null;
         m_box = null;
      }
      
      public function showMessage(param1:AutomaticPetSkillVO, param2:MessageBox) : void
      {
         var _loc7_:AutomaticPetActiveSkillVO = null;
         var _loc9_:AutomaticPetAttackSkillVO = null;
         var _loc8_:AutomaticPetPassiveSkillVO1 = null;
         var _loc10_:AutomaticPetAuxiliarySkillVO = null;
         var _loc11_:String = null;
         var _loc5_:String = null;
         var _loc6_:String = null;
         var _loc4_:String = null;
         var _loc3_:String = null;
         m_box = param2;
         if(param1)
         {
            _loc7_ = param1 as AutomaticPetActiveSkillVO;
            _loc9_ = param1 as AutomaticPetAttackSkillVO;
            _loc8_ = param1 as AutomaticPetPassiveSkillVO1;
            _loc10_ = param1 as AutomaticPetAuxiliarySkillVO;
            _loc11_ = "";
            _loc5_ = "";
            _loc6_ = "";
            _loc4_ = "";
            _loc3_ = "";
            _loc11_ += MessageBoxFunction.getInstance().toHTMLText(param1.getName(),20);
            if(_loc9_)
            {
               _loc6_ += MessageBoxFunction.getInstance().toHTMLText("伤害：",16) + MessageBoxFunction.getInstance().toHTMLText(int((1 + _loc9_.getSkillHurtMulti()) * 100) + "%攻击力+" + _loc9_.getSkillHurtAdd(),16);
            }
            if(_loc7_)
            {
               if(_loc7_.getSkillCdTime())
               {
                  _loc4_ += MessageBoxFunction.getInstance().toHTMLText("CD：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc7_.getSkillCdTime() / 1000 + "秒",16);
               }
               if(_loc7_.getSkillCostMp())
               {
                  _loc3_ += MessageBoxFunction.getInstance().toHTMLText("耗魔：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc7_.getSkillCostMp() + "点",16);
               }
            }
            _loc5_ += _loc11_ + "<br>" + "<br>";
            if(_loc6_)
            {
               _loc5_ += _loc6_ + "<br>";
            }
            if(_loc4_)
            {
               _loc5_ += _loc4_ + "<br>";
            }
            if(_loc3_)
            {
               _loc5_ += _loc3_ + "<br>";
            }
            if(_loc8_)
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText(_loc8_.getDescription2(),16) + "<br>";
            }
            if(_loc10_)
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText(_loc10_.getDescription2(),16) + "<br>";
            }
            m_description = _loc5_ += MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(param1.getDescription(),14);
            m_skillShow.resetShow(param1.getIconSwfPath(),param1.getIconClassName());
         }
      }
      
      private function showSkillComplete(param1:SkillShow) : void
      {
         m_box.boxWidth = 250;
         m_box.addSprite(m_skillShow);
         m_box.htmlText = m_description;
      }
   }
}

