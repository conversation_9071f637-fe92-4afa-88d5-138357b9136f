package UI.Button.SwitchBtn.ShopSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Shop_OtherBtn extends SwitchBtn
   {
      public var btnName:String;
      
      public function Shop_OtherBtn()
      {
         super();
         setTipString("其他");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopSwitchBtn"));
      }
   }
}

