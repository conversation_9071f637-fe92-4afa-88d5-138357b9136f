package UI.MessageBox
{
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.ShiTu.PromoteValueObject;
   import UI.Skills.PetSkills.PetAwakeSkillVO;
   import flash.display.MovieClip;
   
   public class ShowPetAwakePassiveSkillMessage
   {
      public function ShowPetAwakePassiveSkillMessage()
      {
         super();
      }
      
      public function showMessage(param1:PetAwakeSkillVO, param2:Player, param3:MessageBox) : void
      {
         var _loc9_:String = null;
         var _loc5_:String = null;
         var _loc7_:String = null;
         var _loc8_:* = undefined;
         var _loc6_:String = null;
         var _loc10_:int = 0;
         var _loc4_:MovieClip = null;
         if(param1)
         {
            _loc9_ = "";
            _loc5_ = "";
            _loc9_ += MessageBoxFunction.getInstance().toHTMLText(param1.name,16);
            _loc7_ = "";
            _loc8_ = PromoteValueObject.getDescriptions(param1.attributes,param1.attributeValues,param1.descriptions);
            _loc10_ = 0;
            while(_loc10_ < _loc8_.length)
            {
               _loc6_ += "<br>";
               _loc6_ = _loc6_ + MessageBoxFunction.getInstance().toHTMLText(_loc8_[_loc10_],16);
               _loc10_++;
            }
            _loc5_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(param1.level.toString(),16) + _loc6_ + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(param1.description,14);
            param3.boxWidth = 300;
            _loc4_ = MyFunction2.returnShowByClassName("UI.Skills." + param1.className) as MovieClip;
            if(_loc4_)
            {
               param3.addSprite(_loc4_);
            }
            param3.htmlText = _loc9_ + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + _loc5_;
         }
      }
   }
}

