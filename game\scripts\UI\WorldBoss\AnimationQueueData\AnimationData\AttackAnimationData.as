package UI.WorldBoss.AnimationQueueData.AnimationData
{
   import UI.WorldBoss.Entity;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.Utils.ClearUtil;
   
   public class AttackAnimationData extends AnimationData
   {
      public var attackEntity:IEntity;
      
      public var attackEntityLevel:int;
      
      public var roundNum:int;
      
      public var beAttackedEntities:Vector.<IEntity>;
      
      public var beAttackedEntityLevels:Vector.<int>;
      
      public var hurts:Vector.<int>;
      
      public var criticalAndDodgeDatas:Vector.<CriticalAndDodgeData>;
      
      public var currentBloods:Vector.<int>;
      
      public var totalBloods:Vector.<int>;
      
      public var bloodPercents:Vector.<Number>;
      
      public var bloodShowIndexs:Vector.<int>;
      
      public function AttackAnimationData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         attackEntity = null;
         ClearUtil.nullArr(beAttackedEntities,false,false,false);
         beAttackedEntities = null;
         attackEntity = null;
         ClearUtil.nullArr(beAttackedEntityLevels);
         beAttackedEntityLevels = null;
         ClearUtil.nullArr(hurts);
         hurts = null;
         ClearUtil.nullArr(criticalAndDodgeDatas);
         criticalAndDodgeDatas = null;
         ClearUtil.nullArr(currentBloods);
         currentBloods = null;
         ClearUtil.nullArr(totalBloods);
         totalBloods = null;
         ClearUtil.nullArr(bloodPercents);
         bloodPercents = null;
         ClearUtil.nullArr(bloodShowIndexs);
         bloodShowIndexs = null;
      }
      
      override public function playAnimation() : void
      {
         super.playAnimation();
         (attackEntity as Entity).playAttackAnimation(this,playEnd);
      }
   }
}

