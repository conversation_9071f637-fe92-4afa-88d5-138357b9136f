package UI.newGuide.GuideEveryDay
{
   import UI.MyFunction2;
   import UI.Task.TaskVO.MTaskVO;
   import UI.newGuide.NewGuidePanel;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class GuideEveryList
   {
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_guideeverypanel:GuideEveryPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_dataLayer:MovieClip;
      
      private var m_spritelist:Vector.<Sprite>;
      
      private var m_slider:MovieClip;
      
      private var m_sliderbg:MovieClip;
      
      private var m_upbtn:MovieClip;
      
      private var m_downbtn:MovieClip;
      
      private var m_bDown:Boolean = false;
      
      private var m_bIn:Boolean = false;
      
      private var m_itemHeigh:int = 60;
      
      private var m_rotio:Number;
      
      private var m_itemlist:Vector.<GuideEveryItem>;
      
      private var m_bFirst:Boolean = false;
      
      private var m_minX:Number = 185;
      
      private var m_maxX:Number = 210;
      
      private var m_minY:Number = 68;
      
      private var m_maxY:Number = 173;
      
      private var m_yMove:Number = 5;
      
      public function GuideEveryList()
      {
         super();
      }
      
      public function init(param1:NewGuidePanel, param2:GuideEveryPanel, param3:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_guideeverypanel = param2;
         m_show = param3;
         m_mc = m_show["listpanel"];
         initShow();
      }
      
      public function clear() : void
      {
         closeEvent();
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
      }
      
      public function initShow() : void
      {
         m_dataLayer = m_mc["container"];
         m_slider = m_mc["slider_L"];
         m_upbtn = m_mc["upbtn"];
         m_downbtn = m_mc["downbtn"];
      }
      
      public function show() : void
      {
         registerEvent();
         refreshlist();
      }
      
      public function hide() : void
      {
         closeEvent();
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
      }
      
      private function registerEvent() : void
      {
         m_mc.addEventListener("enterFrame",render);
         m_slider.addEventListener("mouseDown",calldown);
         m_show.addEventListener("mouseUp",calldown);
         m_upbtn.addEventListener("mouseUp",callup);
         m_downbtn.addEventListener("mouseUp",callnext);
      }
      
      private function callup(param1:MouseEvent) : void
      {
         if(m_slider.y - m_yMove <= m_maxY && m_slider.y - m_yMove >= m_minY)
         {
            m_slider.y -= m_yMove;
         }
         else if(m_slider.y - m_yMove < m_minY)
         {
            m_slider.y = m_minY;
         }
         else if(m_slider.y - m_yMove > m_maxY)
         {
            m_slider.y = m_maxY;
         }
      }
      
      private function callnext(param1:MouseEvent) : void
      {
         if(m_slider.y + m_yMove <= m_maxY && m_slider.y + m_yMove >= m_minY)
         {
            m_slider.y += m_yMove;
         }
      }
      
      private function closeEvent() : void
      {
         m_mc.removeEventListener("enterFrame",render);
         m_slider.removeEventListener("mouseDown",calldown);
         m_show.removeEventListener("mouseUp",calldown);
         m_upbtn.removeEventListener("mouseUp",callup);
         m_downbtn.removeEventListener("mouseUp",callnext);
      }
      
      private function calldown(param1:MouseEvent) : void
      {
         if(param1.type == "mouseDown")
         {
            m_bDown = true;
         }
         else
         {
            m_bDown = false;
         }
      }
      
      private function render(param1:Event) : void
      {
         checkIN();
         moveMouse();
         moveInfo();
      }
      
      private function moveMouse() : void
      {
         true;
         if(m_bDown)
         {
            if(m_mc.mouseY - 27 <= m_maxY && m_mc.mouseY - 27 >= m_minY)
            {
               m_slider.y = m_mc.mouseY - 27;
            }
            else if(m_mc.mouseY - 27 < m_minY)
            {
               m_slider.y = m_minY;
            }
            else if(m_mc.mouseY - 27 > m_maxY)
            {
               m_slider.y = m_maxY;
            }
         }
      }
      
      private function moveInfo() : void
      {
         m_rotio = (m_slider.y - m_minY) / (m_maxY - m_minY);
         m_dataLayer.y = -m_rotio * (m_dataLayer.numChildren * m_itemHeigh) + 47;
      }
      
      private function checkIN() : void
      {
         if(m_mc.mouseX <= m_maxX && m_mc.mouseX >= m_minX && m_mc.mouseY >= m_minY && m_mc.mouseY <= m_maxY)
         {
            m_bIn = true;
         }
         else
         {
            m_bIn = false;
         }
      }
      
      public function refreshlist() : void
      {
         var _loc2_:GuideEveryItem = null;
         var _loc3_:Sprite = null;
         var _loc6_:MTaskVO = null;
         var _loc5_:int = 0;
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         m_itemlist = new Vector.<GuideEveryItem>();
         ClearUtil.clearObject(m_spritelist);
         m_spritelist = null;
         m_spritelist = new Vector.<Sprite>();
         m_bFirst = false;
         var _loc1_:int = 0;
         var _loc4_:Vector.<MTaskVO> = NewEveryDayData.getInstance().everyDayTaskVOs;
         _loc5_ = 0;
         while(_loc5_ < _loc4_.length)
         {
            _loc6_ = _loc4_[_loc5_];
            if(_loc6_ && _loc6_.isGotReward == 0)
            {
               _loc3_ = MyFunction2.returnShowByClassName("listitem") as Sprite;
               m_spritelist.push(_loc3_);
               _loc2_ = new GuideEveryItem();
               _loc2_.init(m_newguidepanel,m_guideeverypanel,this,m_show,_loc6_,_loc3_,String(_loc6_.id));
               _loc2_.show();
               m_itemlist.push(_loc2_);
               _loc3_.x = 0;
               _loc3_.y = _loc1_ * m_itemHeigh;
               m_dataLayer.addChild(_loc3_);
               _loc1_++;
            }
            _loc5_++;
         }
         arrangeList();
      }
      
      public function setFirst(param1:Boolean) : void
      {
         m_bFirst = param1;
      }
      
      public function refreshById(param1:String) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_itemlist.length)
         {
            m_itemlist[_loc2_].checkCurrent(param1);
            _loc2_++;
         }
      }
      
      private function getIsGet(param1:MTaskVO) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < param1.currentTaskGoalVO_nums.length)
         {
            _loc3_ += param1.currentTaskGoalVO_nums[_loc4_];
            _loc4_++;
         }
         _loc4_ = 0;
         while(_loc4_ < param1.taskGoalVO_nums.length)
         {
            _loc2_ += param1.taskGoalVO_nums[_loc4_];
            _loc4_++;
         }
         if(_loc3_ >= _loc2_)
         {
            return true;
         }
         return false;
      }
      
      public function arrangeList() : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:String = "-1";
         if(m_itemlist && m_itemlist.length > 0)
         {
            _loc3_ = 0;
            while(_loc3_ < m_itemlist.length)
            {
               if(m_itemlist[_loc3_].getData().isGotReward == false && getIsGet(m_itemlist[_loc3_].getData()))
               {
                  m_itemlist[_loc3_].getShow().y = _loc2_ * m_itemHeigh;
                  m_itemlist[_loc3_].refreshInfo();
                  _loc2_++;
                  if(m_bFirst == false)
                  {
                     m_bFirst = true;
                     m_itemlist[_loc3_].refreshScript();
                  }
                  if(_loc1_ == "-1")
                  {
                     _loc1_ = m_itemlist[_loc3_].getID();
                  }
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < m_itemlist.length)
            {
               if(m_itemlist[_loc3_].getData().isGotReward == false && getIsGet(m_itemlist[_loc3_].getData()) == false)
               {
                  m_itemlist[_loc3_].getShow().y = _loc2_ * m_itemHeigh;
                  m_itemlist[_loc3_].refreshInfo();
                  _loc2_++;
                  if(m_bFirst == false)
                  {
                     m_bFirst = true;
                     m_itemlist[_loc3_].refreshScript();
                  }
                  if(_loc1_ == "-1")
                  {
                     _loc1_ = m_itemlist[_loc3_].getID();
                  }
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < m_itemlist.length)
            {
               if(m_itemlist[_loc3_].getData().isGotReward == true)
               {
                  m_itemlist[_loc3_].getShow().y = _loc2_ * m_itemHeigh;
                  m_itemlist[_loc3_].refreshInfo();
                  _loc2_++;
                  if(m_bFirst == false)
                  {
                     m_bFirst = true;
                     m_itemlist[_loc3_].refreshScript();
                  }
                  if(_loc1_ == "-1")
                  {
                     _loc1_ = m_itemlist[_loc3_].getID();
                  }
               }
               _loc3_++;
            }
            if(_loc1_ != "-1")
            {
               refreshById(_loc1_);
            }
         }
      }
   }
}

