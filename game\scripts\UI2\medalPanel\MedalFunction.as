package UI2.medalPanel
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Players.PlayerVO;
   
   public class MedalFunction extends DataManagerParent
   {
      private static var _instance:MedalFunction;
      
      private var _allMedalsXml:XML;
      
      public function MedalFunction()
      {
         super();
      }
      
      public static function getInstance() : MedalFunction
      {
         if(!_instance)
         {
            _instance = new MedalFunction();
         }
         return _instance;
      }
      
      override protected function init() : void
      {
         super.init();
         allMedalsXml = null;
      }
      
      public function addMedalToPlayer(param1:MedalEquipmentVO, param2:PlayerVO) : Array
      {
         if(!param1)
         {
            throw new Error();
         }
         for each(var _loc3_ in param2.medalEquipmentVOs)
         {
            if(_loc3_.id == param1.id)
            {
               return [false,"激活失败！此勋章已激活"];
            }
         }
         param2.medalEquipmentVOs.push(param1);
         return [true,"激活勋章成功！"];
      }
      
      public function getALlMedalAttribute(param1:Vector.<EquipmentVO>) : Object
      {
         var _loc2_:Object = {};
         var _loc4_:int = 0;
         for each(var _loc3_ in param1)
         {
            _loc4_ = 0;
            while(_loc4_ < (_loc3_ as MedalEquipmentVO).addPlayerAttributes.length)
            {
               if(!_loc2_[(_loc3_ as MedalEquipmentVO).addPlayerAttributes[_loc4_]])
               {
                  _loc2_[(_loc3_ as MedalEquipmentVO).addPlayerAttributes[_loc4_]] = 0;
               }
               _loc2_[(_loc3_ as MedalEquipmentVO).addPlayerAttributes[_loc4_]] = Math.max((_loc3_ as MedalEquipmentVO).addPlayerAttributeValues[_loc4_],_loc2_[(_loc3_ as MedalEquipmentVO).addPlayerAttributes[_loc4_]]);
               _loc4_++;
            }
         }
         return _loc2_;
      }
      
      public function get allMedalsXml() : XML
      {
         return _antiwear._allMedalsXml;
      }
      
      public function set allMedalsXml(param1:XML) : void
      {
         _antiwear._allMedalsXml = param1;
      }
   }
}

