package UI.Event
{
   import flash.events.Event;
   
   public class UIPassiveEvent extends Event
   {
      public static const SHOW_MESSAGEBOX:String = "showMessageBox";
      
      public static const HIDE_MESSAGEBOX:String = "hideMessageBox";
      
      public static const CHANGE_NUM:String = "changeNum";
      
      public static const CHANGE_NUMUP:String = "changeNumUp";
      
      public static const CHANGE_NUMDOWN:String = "changeNumDown";
      
      public static const WARNING_BOX:String = "warningBox";
      
      public static const SHOW_WARNING_BOX:String = "showWarningBox";
      
      public static const A_KEY_SOLD:String = "aKeySold";
      
      public static const REFRESH_ATT:String = "refreshAtt";
      
      public static const SHOW_PLAYER:String = "showPlayer";
      
      public static const STOP_GAME:String = "stopGame";
      
      public static const CONTINUE_GAME:String = "continueGame";
      
      public static const RECEIVE_ITEM:String = "receiveItem";
      
      public static const SEND_PANEL:String = "sendPanel";
      
      public static const SUCCESS_COMPOSE:String = "successCompose";
      
      public static const DELE_ITEM:String = "deleItem";
      
      public static const GET_EVENT:String = "getEvent";
      
      public static const UPGRADE_WAVE_NUM:String = "upgradeWaveNum";
      
      public static const OPEN_PANEL:String = "openPanel";
      
      public static const SAVE_UI:String = "saveUI";
      
      public static const ADD_GIFTS:String = "addGifts";
      
      public static const GET_MINHUNTANG_NUM:String = "getMinHunTangNum";
      
      public static const CUT_ONE_MINHUNTANG:String = "cutOneMinHunTang";
      
      public static const SHOW_QUICKEN_CHARGE_BOX:String = "showQuickenChargeBox";
      
      public static const SHOW_LAND_PROGRESS_BAR:String = "showLandProgressBar";
      
      public static const HIDE_LAND_PROGRESS_BAR:String = "hideLandProgressBar";
      
      public static const CLICK_LAND:String = "clickLand";
      
      public static const CLICK_LIANDANFURNACE:String = "clickLianDanFurnace";
      
      public static const CHANGE_BUFF_REMAINE_TIME:String = "changeBuffRemaineTime";
      
      public static const SHOW_PLAYER_ATTRIBUTE_PANEL:String = "showPlayerAttributePanel";
      
      public static const CHANGE_FARM_SHOW_OBJECT_PLACE:String = "changeFarmShowObjectPlace";
      
      public static const CHANGE_CHECK_BOX_STATE:String = "changeCheckBoxState";
      
      public var data:*;
      
      public function UIPassiveEvent(param1:String, param2:* = null, param3:Boolean = false, param4:Boolean = false)
      {
         this.data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new UIPassiveEvent(type,data,bubbles,cancelable);
      }
   }
}

