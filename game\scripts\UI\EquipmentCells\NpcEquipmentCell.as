package UI.EquipmentCells
{
   import UI.EquipmentCell;
   import flash.events.MouseEvent;
   
   public class NpcEquipmentCell extends EquipmentCell
   {
      public function NpcEquipmentCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function rollOver(param1:MouseEvent) : void
      {
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
      }
      
      override protected function mouseDown(param1:MouseEvent) : void
      {
      }
      
      override protected function click(param1:MouseEvent) : void
      {
      }
   }
}

