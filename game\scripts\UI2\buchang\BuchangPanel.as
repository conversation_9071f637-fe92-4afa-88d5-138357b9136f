package UI2.buchang
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class BuchangPanel extends MySprite
   {
      private var show:MovieClip;
      
      private var btnClose:ButtonLogicShell;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var equipReward:EquipmentVOsData;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipments:Vector.<Equipment>;
      
      private var btnGetAward:ButtonLogicShell;
      
      private var m_index:int = -1;
      
      public function BuchangPanel()
      {
         super();
         this.name = "BuchangPanel";
         this.mouseChildren = true;
         this.mouseEnabled = true;
         initParams();
         initShow();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(btnClose);
         btnClose = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         super.clear();
      }
      
      private function initParams() : void
      {
         equipments = new Vector.<Equipment>();
         equipmentVOsData = new EquipmentVOsData();
         equipReward = new EquipmentVOsData();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function initShow() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            if(new TimeUtil().timeInterval(param1,"2017-05-01 23:59:59") < 0)
            {
               clear();
               GamingUI.getInstance().clearBuchang();
            }
            else
            {
               show = MyFunction2.returnShowByClassName("BuchangPanel") as MovieClip;
               addChild(show);
               btnClose = new ButtonLogicShell();
               btnClose.setShow(show["btnClose"]);
               btnGetAward = new ButtonLogicShell();
               btnGetAward.setShow(show["btnGetAward"]);
               initEquipment();
               showEq();
            }
         },null,true);
      }
      
      private function initEquipment() : void
      {
         var _loc2_:XML = null;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:XMLList = XMLSingle.getInstance().buchang.item;
         if(GamingUI.getInstance().player2)
         {
            if(GamingUI.getInstance().player1.playerVO.level > GamingUI.getInstance().player2.playerVO.level)
            {
               _loc1_ = GamingUI.getInstance().player1.playerVO.level;
            }
            else
            {
               _loc1_ = GamingUI.getInstance().player2.playerVO.level;
            }
         }
         else
         {
            _loc1_ = GamingUI.getInstance().player1.playerVO.level;
         }
         _loc4_ = 0;
         while(_loc4_ < _loc3_.length())
         {
            if(_loc1_ >= int(_loc3_[_loc4_].@start) && _loc1_ <= int(_loc3_[_loc4_].@end))
            {
               _loc2_ = _loc3_[_loc4_];
               break;
            }
            _loc4_++;
         }
         if(_loc2_)
         {
            equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(_loc2_,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         }
      }
      
      private function showEq() : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         ClearUtil.clearObject(equipments);
         equipments = null;
         equipments = new Vector.<Equipment>();
         _loc3_ = 0;
         while(_loc3_ < equipmentVOsData.getEquipmentVONum() && _loc3_ < 7)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData.getEquipmentVOByIndex(_loc3_).clone());
            show["eqCell_" + _loc3_].addChild(_loc2_);
            equipments.push(_loc2_);
            _loc2_.mouseEnabled = true;
            _loc2_.name = "equip_" + String(_loc3_);
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            _loc2_.addEventListener("mouseDown",clickcall,false,0,true);
            _loc3_++;
         }
         BuchangData.getInstance().setBShow(1);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
         hideALL();
         btnGetAward.getShow().mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
      }
      
      private function clickcall(param1:MouseEvent) : void
      {
         m_index = int(String(param1.currentTarget.name).split("_")[1]);
         hideALL();
         ((show["eqCell_" + m_index] as MovieClip)["checkMC"] as MovieClip).visible = true;
         btnGetAward.getShow().mouseEnabled = true;
         MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),0);
      }
      
      private function hideALL() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 7)
         {
            ((show["eqCell_" + _loc1_] as MovieClip)["checkMC"] as MovieClip).visible = false;
            _loc1_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = undefined;
         switch(param1.button)
         {
            case btnClose:
               clear();
               GamingUI.getInstance().clearBuchang();
               break;
            case btnGetAward:
               if(m_index >= 0 && m_index < equipmentVOsData.getEquipmentVONum())
               {
                  btnGetAward.getShow().mouseEnabled = false;
                  MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
                  _loc2_ = new Vector.<EquipmentVO>();
                  _loc2_.push(equipmentVOsData.getEquipmentVOByIndex(m_index));
                  equipReward.setEquipmentVOs(_loc2_);
                  getReward();
                  break;
               }
               GamingUI.getInstance().showMessageTip("请选择奖品");
               break;
         }
      }
      
      private function getReward() : void
      {
         var _loc3_:EquipmentVOsData = null;
         var _loc1_:SaveTaskInfo = null;
         var _loc2_:int = BuchangData.getInstance().getBGet();
         _loc3_ = equipReward;
         if(_loc2_ == 1)
         {
            showWarningBox("已经领取，不能重复领取",0);
            return;
         }
         if(m_getEquipmentVOsLogic.getEquipmentVOs(_loc3_,GamingUI.getInstance().player1))
         {
            showWarningBox("获取礼包成功",0);
            BuchangData.getInstance().setBGet(1);
            _loc1_ = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame();
         }
         else
         {
            showWarningBox("背包已满, 不能获取礼包",0);
            btnGetAward.getShow().mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),0);
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

