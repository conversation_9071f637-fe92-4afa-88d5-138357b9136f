package UI2.Mount.MountUI
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.ShowWarningBoxLogic;
   import UI2.Mount.MountData.MountSystemData.MountSystemData;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.Util.InitTipShowsInShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.CloseSubUI;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class MountsPanel extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_mountInforCards:Vector.<MountInforCardShow>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_collectMaterialBtn:ButtonLogicShell2;
      
      private var m_introduceBtn:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_mountSystemData:MountSystemData;
      
      private var m_showWarningBoxLogic:ShowWarningBoxLogic;
      
      private var m_randomMaterialPanel:RandomMaterialPanel;
      
      private var m_mountInforPanel:MountInforPanel;
      
      private var m_introducePanel:IntroducePanel;
      
      private var m_totalAddAttackText:TextField;
      
      private var m_totalAddDefenceText:TextField;
      
      private var m_totalAddHpText:TextField;
      
      private var m_strengthenNumText:TextField;
      
      private var m_currentPointNumText:TextField;
      
      private var m_initTipShows:InitTipShowsInShow;
      
      private var m_mountsVO:MountsVO;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_gamingUI:GamingUI;
      
      public function MountsPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_mountInforCards = new Vector.<MountInforCardShow>();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_collectMaterialBtn = new ButtonLogicShell2();
         m_introduceBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_showWarningBoxLogic = new ShowWarningBoxLogic();
         m_showWarningBoxLogic.init(this);
         m_initTipShows = new InitTipShowsInShow();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_mountInforCards);
         m_mountInforCards = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_collectMaterialBtn);
         m_collectMaterialBtn = null;
         ClearUtil.clearObject(m_introduceBtn);
         m_introduceBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_mountSystemData);
         m_mountSystemData = null;
         ClearUtil.clearObject(m_showWarningBoxLogic);
         m_showWarningBoxLogic = null;
         ClearUtil.clearObject(m_randomMaterialPanel);
         m_randomMaterialPanel = null;
         ClearUtil.clearObject(m_mountInforPanel);
         m_mountInforPanel = null;
         ClearUtil.clearObject(m_introducePanel);
         m_introducePanel = null;
         m_totalAddAttackText = null;
         m_totalAddDefenceText = null;
         m_totalAddHpText = null;
         m_strengthenNumText = null;
         m_currentPointNumText = null;
         ClearUtil.clearObject(m_initTipShows);
         m_initTipShows = null;
         m_mountsVO = null;
         m_versionControl = null;
         m_loadUI = null;
         m_gamingUI = null;
         super.clear();
      }
      
      public function init(param1:MountsVO, param2:GamingUI, param3:IVersionControl, param4:IProgressShow) : void
      {
         m_mountsVO = param1;
         m_gamingUI = param2;
         m_versionControl = param3;
         m_loadUI = param4;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/mount/mountData.xml",getXMLSuccess,getFail);
         m_myLoader.getClass("UISprite2/MountPanel.swf","MountPanel",getShowSuceess,getFail);
         m_myLoader.load();
      }
      
      private function initShow() : void
      {
         var _loc6_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc5_:int = 0;
         var _loc4_:MountInforCardShow = null;
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_collectMaterialBtn.setShow(m_show["collectMaterialBtn"]);
         m_introduceBtn.setShow(m_show["introduceBtn"]);
         m_totalAddAttackText = m_show["totalAddDefenceText"];
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_totalAddAttackText);
         m_totalAddDefenceText = m_show["totalAddAttackText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_totalAddDefenceText);
         m_totalAddHpText = m_show["totalAddHpText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_totalAddHpText);
         m_strengthenNumText = m_show["strengthenNumText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_strengthenNumText);
         m_currentPointNumText = m_show["currentPointNumText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_currentPointNumText);
         ClearUtil.clearObject(m_mountInforCards);
         m_mountInforCards.length = 0;
         var _loc3_:int = m_show.numChildren;
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc1_ = m_show.getChildAt(_loc6_);
            if(_loc1_.name.substr(0,10) == "mountCard_")
            {
               _loc5_++;
            }
            _loc6_++;
         }
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc4_ = new MountInforCardShow();
            _loc4_.setShow(m_show["mountCard_" + (_loc6_ + 1)]);
            _loc4_.setVersionControl(m_versionControl);
            _loc4_.setProgressShow(m_loadUI);
            m_mountInforCards.push(_loc4_);
            _loc6_++;
         }
         m_initTipShows.init(m_show,this);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_mountSystemData == null || m_mountsVO == null)
         {
            return;
         }
         setPageBtn(1);
         arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_mountInforCards.length);
         m_totalAddAttackText.text = m_mountsVO.getTotalAddAttackOfMountVOs().toString();
         m_totalAddDefenceText.text = m_mountsVO.getTotalAddDefenceOfMountVOs().toString();
         m_totalAddHpText.text = m_mountsVO.getTotalAddHpOfMountVOs().toString();
         m_strengthenNumText.text = m_mountsVO.getCurrentStrengthenPointNum().toString();
         m_currentPointNumText.text = m_mountsVO.getGetMaterialPointNum().toString();
      }
      
      public function updateShow() : void
      {
         setPageBtn(m_pageBtnGroup.pageNum);
         arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_mountInforCards.length);
         m_totalAddAttackText.text = m_mountsVO.getTotalAddAttackOfMountVOs().toString();
         m_totalAddDefenceText.text = m_mountsVO.getTotalAddDefenceOfMountVOs().toString();
         m_totalAddHpText.text = m_mountsVO.getTotalAddHpOfMountVOs().toString();
         m_strengthenNumText.text = m_mountsVO.getCurrentStrengthenPointNum().toString();
         m_currentPointNumText.text = m_mountsVO.getGetMaterialPointNum().toString();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         switch(param1.button)
         {
            case m_pageBtnGroup:
               arrangeActiveTaskLineShow((m_pageBtnGroup.pageNum - 1) * m_mountInforCards.length);
               break;
            case m_quitBtn:
               m_gamingUI.closeMountPanel();
               break;
            case m_collectMaterialBtn:
               openRandomMaterialPanel();
               break;
            case m_introduceBtn:
               openIntroducePanel();
         }
         var _loc2_:int = !!m_mountInforCards ? m_mountInforCards.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_mountInforCards[_loc3_].getMountShowBtn())
            {
               if(m_mountInforCards[_loc3_].getMountVO().getLevel1())
               {
                  openMountInforPanel(m_mountInforCards[_loc3_].getMountVO());
               }
               else
               {
                  showWarningBox("该坐骑星级为0，还未召唤成功。",0);
               }
               return;
            }
            _loc3_++;
         }
      }
      
      private function openIntroducePanel() : void
      {
         var _loc1_:CloseSubUI = null;
         if(m_introducePanel == null)
         {
            _loc1_ = new CloseSubUI();
            _loc1_.init(closeIntroducePanel);
            m_introducePanel = new IntroducePanel();
            m_introducePanel.init(_loc1_);
            addChild(m_introducePanel);
         }
      }
      
      private function closeIntroducePanel() : void
      {
         ClearUtil.clearObject(m_introducePanel);
         m_introducePanel = null;
      }
      
      private function openMountInforPanel(param1:MountVO) : void
      {
         var _loc2_:CloseSubUI = null;
         if(m_mountInforPanel == null)
         {
            _loc2_ = new CloseSubUI();
            _loc2_.init(closeMountInforPanel);
            m_mountInforPanel = new MountInforPanel();
            m_mountInforPanel.init(param1,m_mountSystemData,m_mountsVO,_loc2_,this,m_versionControl,m_loadUI);
            addChild(m_mountInforPanel);
         }
      }
      
      private function closeMountInforPanel() : void
      {
         ClearUtil.clearObject(m_mountInforPanel);
         m_mountInforPanel = null;
         updateShow();
      }
      
      private function openRandomMaterialPanel() : void
      {
         var _loc1_:CloseSubUI = null;
         if(m_randomMaterialPanel == null)
         {
            _loc1_ = new CloseSubUI();
            _loc1_.init(closeRandomMaterialPanel);
            m_randomMaterialPanel = new RandomMaterialPanel();
            m_randomMaterialPanel.init(m_mountSystemData,m_mountsVO,this,_loc1_);
            addChild(m_randomMaterialPanel);
         }
      }
      
      private function closeRandomMaterialPanel() : void
      {
         ClearUtil.clearObject(m_randomMaterialPanel);
         m_randomMaterialPanel = null;
         updateShow();
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_mountsVO == null || m_mountsVO.getMountVONum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_mountsVO.getMountVONum() % m_mountInforCards.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_mountsVO.getMountVONum() / m_mountInforCards.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_mountsVO.getMountVONum() / m_mountInforCards.length) + 1);
         }
      }
      
      private function arrangeActiveTaskLineShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc4_:MountVO = null;
         var _loc5_:int = param1 + m_mountInforCards.length;
         var _loc2_:int = !!m_mountsVO ? m_mountsVO.getMountVONum() : 0;
         var _loc3_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc2_)
         {
            _loc4_ = m_mountsVO.getMountVOByIndex(_loc6_);
            m_mountInforCards[_loc3_].setMountVO(_loc4_);
            m_mountInforCards[_loc3_].getShow().visible = true;
            _loc3_++;
            _loc6_++;
         }
         while(_loc3_ < m_mountInforCards.length)
         {
            m_mountInforCards[_loc3_].getShow().visible = false;
            _loc3_++;
         }
      }
      
      private function getShowSuceess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         ClearUtil.clearObject(m_mountSystemData);
         m_mountSystemData = null;
         m_mountSystemData = new MountSystemData();
         m_mountSystemData.initByXML(param1.resultXML);
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeMountPanel();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         m_showWarningBoxLogic.showWarningBox(param1,param2,param3);
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

