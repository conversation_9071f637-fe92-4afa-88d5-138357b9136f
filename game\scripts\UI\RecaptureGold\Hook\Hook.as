package UI.RecaptureGold.Hook
{
   import UI.MyMovieClip;
   import UI.RecaptureGold.HitPoint;
   import UI.RecaptureGold.Parent.ICatchItem;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class Hook extends MyMovieClip
   {
      public var putItemLayer:Sprite;
      
      private var _hookItem:ICatchItem;
      
      public function Hook()
      {
         super();
         stop();
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(putItemLayer)
         {
            while(putItemLayer.numChildren > 0)
            {
               _loc1_ = putItemLayer.getChildAt(0);
               putItemLayer.removeChildAt(0);
               if(_loc1_.hasOwnProperty("clear"))
               {
                  _loc1_["clear"]();
               }
            }
         }
         putItemLayer = null;
         if(_hookItem)
         {
            _hookItem.clear();
         }
         _hookItem = null;
      }
      
      public function getAllHitPoint() : Vector.<HitPoint>
      {
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:DisplayObject = null;
         _loc1_ = numChildren;
         var _loc2_:Vector.<HitPoint> = new Vector.<HitPoint>();
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = getChildAt(_loc4_);
            if(_loc3_ is HitPoint)
            {
               _loc2_.push(_loc3_);
            }
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function get hookItem() : ICatchItem
      {
         return _hookItem;
      }
      
      public function set hookItem(param1:ICatchItem) : void
      {
         if(param1)
         {
            putItemLayer.addChild(param1 as DisplayObject);
            _hookItem = param1;
            showCatchState();
         }
         else
         {
            while(putItemLayer.numChildren > 0)
            {
               putItemLayer.removeChildAt(0);
            }
            _hookItem = param1;
            showReleaseState();
         }
      }
      
      private function showReleaseState() : void
      {
         gotoAndStop(1);
      }
      
      private function showCatchState() : void
      {
         gotoAndStop(2);
      }
   }
}

