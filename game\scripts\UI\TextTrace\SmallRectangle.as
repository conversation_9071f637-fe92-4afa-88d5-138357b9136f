package UI.TextTrace
{
   import UI.GamingUI;
   import UI.MySprite;
   import flash.events.MouseEvent;
   
   public class SmallRectangle extends MySprite
   {
      public function SmallRectangle()
      {
         super();
         addEventListener("click",showTextTrace,false,0,true);
      }
      
      private function showTextTrace(param1:MouseEvent) : void
      {
         GamingUI.getInstance().textTrace.visible = true;
         visible = false;
      }
   }
}

