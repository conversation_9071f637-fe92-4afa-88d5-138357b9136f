package UI2.Consumer
{
   import YJFY.Utils.ClearUtil;
   
   public class PayXMLData
   {
      public var id:String;
      
      public var name:String;
      
      public var infoList:Vector.<PayInfoItem>;
      
      public function PayXMLData()
      {
         super();
         infoList = new Vector.<PayInfoItem>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(infoList);
         infoList = null;
      }
   }
}

