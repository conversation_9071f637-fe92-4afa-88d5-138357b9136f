package UI2.SVActivity.Show
{
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.ChengZhangJiHuaData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class XinJueSeLiBaoPanel
   {
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_buyBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_currentServeTime:String;
      
      private var m_show:MovieClip;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_xinJueSeLiBaoData:ChengZhangJiHuaData;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      public function XinJueSeLiBaoPanel()
      {
         super();
         m_eqCells = new Vector.<Sprite>();
         m_buyBtn = new ButtonLogicShell2();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(m_buyBtn);
         m_buyBtn = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         m_show = null;
         m_svActivitySaveData = null;
         m_svActivityPanel = null;
         m_xinJueSeLiBaoData = null;
         m_svActivityPanel = null;
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:ChengZhangJiHuaData, param4:SVActivityPanel, param5:String) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         setServerTime(param5);
         m_svActivitySaveData = param2;
         m_xinJueSeLiBaoData = param3;
         m_svActivityPanel = param4;
         initShow();
         initShow2();
         initShow3();
      }
      
      private function initShow() : void
      {
         var _loc3_:int = 0;
         m_buyBtn.setShow(m_show["buyBtn"]);
         var _loc1_:int = m_show.numChildren;
         var _loc2_:Number = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(m_show.getChildAt(_loc3_).name.substr(0,6) == "eqCell")
            {
               _loc2_++;
            }
            _loc3_++;
         }
         m_eqCells.length = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc3_ + 1)]);
            _loc3_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc2_:Equipment = null;
         var _loc4_:int = 0;
         if(m_show == null || m_svActivitySaveData == null || m_xinJueSeLiBaoData == null)
         {
            return;
         }
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         var _loc1_:int = int(m_xinJueSeLiBaoData.getEquipmentVONum());
         var _loc3_:uint = m_eqCells.length;
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(m_xinJueSeLiBaoData.getEquipmentVOByIndex(_loc4_));
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            if(_loc4_ < _loc3_)
            {
               m_eqCells[_loc4_].addChild(_loc2_);
            }
            m_equipments.push(_loc2_);
            _loc4_++;
         }
      }
      
      private function initShow3() : void
      {
         if(m_show == null || m_svActivitySaveData == null || m_xinJueSeLiBaoData == null)
         {
            return;
         }
         if(m_svActivitySaveData.getIsBuy_xinJueSeLiBao() == false)
         {
            initShow_notBuy();
         }
         else
         {
            initShow_buy();
         }
         if(isInActivityTime() == false)
         {
            MyFunction.getInstance().changeSaturation(m_buyBtn.getShow(),-100);
            m_buyBtn.getShow().mouseEnabled = false;
            (m_buyBtn.getShow() as MovieClip).mouseChildren = false;
         }
      }
      
      private function initShow_buy() : void
      {
         MyFunction.getInstance().changeSaturation(m_buyBtn.getShow(),-100);
         m_buyBtn.getShow().mouseEnabled = false;
         (m_buyBtn.getShow() as MovieClip).mouseChildren = false;
      }
      
      private function initShow_notBuy() : void
      {
         MyFunction.getInstance().changeSaturation(m_buyBtn.getShow(),0);
         m_buyBtn.getShow().mouseEnabled = true;
         (m_buyBtn.getShow() as MovieClip).mouseChildren = true;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_buyBtn === _loc2_)
         {
            buy();
         }
      }
      
      private function buy() : void
      {
         if(isInActivityTime() == false)
         {
            m_svActivityPanel.showWarningBox("购买时间不对，欢迎下次再来！",0);
            return;
         }
         if(m_getEquipmentVOsLogic.getIsAbleAddEquipmentVOs(m_xinJueSeLiBaoData,GamingUI.getInstance().player1))
         {
            m_svActivityPanel.showWarningBox("是否花费" + m_xinJueSeLiBaoData.getTicketPrice() + "点券购买暑期超值礼包",3,{
               "type":"buyXinJueSeLiBao",
               "okFunction":buy2
            });
         }
         else
         {
            m_svActivityPanel.showWarningBox("背包已满, 不能购买礼包",0);
         }
      }
      
      public function isInActivityTime() : Boolean
      {
         if(m_xinJueSeLiBaoData == null)
         {
            return false;
         }
         if(m_currentServeTime == null)
         {
            return false;
         }
         var _loc1_:String = m_xinJueSeLiBaoData.getStartTime();
         var _loc2_:String = m_xinJueSeLiBaoData.getEndTime();
         if((!Boolean(_loc1_) || new TimeUtil().timeInterval(_loc1_,m_currentServeTime) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(m_currentServeTime,_loc2_) > 0))
         {
            return true;
         }
         return false;
      }
      
      public function setServerTime(param1:String) : void
      {
         m_currentServeTime = param1;
      }
      
      private function buy2() : void
      {
         var price:uint;
         var ticketId:String;
         var dataObj:Object;
         if(m_xinJueSeLiBaoData == null)
         {
            return;
         }
         price = m_xinJueSeLiBaoData.getTicketPrice();
         ticketId = m_xinJueSeLiBaoData.getTicketPriceId();
         dataObj = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买新角色礼包";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_svActivitySaveData.buy_xinJueSeLiBao();
            m_getEquipmentVOsLogic.getEquipmentVOs(m_xinJueSeLiBaoData,GamingUI.getInstance().player1);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            initShow3();
         },m_svActivityPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":7
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

