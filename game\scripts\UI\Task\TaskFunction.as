package UI.Task
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.Task.TaskReward.TaskRewardVO;
   import UI.Task.TaskReward.TaskRewardVO_Equipment;
   import UI.Task.TaskReward.TaskRewardVO_Experience;
   import UI.Task.TaskReward.TaskRewardVO_LSHSHI;
   import UI.Task.TaskReward.TaskRewardVO_Money;
   import UI.Task.TaskReward.TaskRewardVO_ZHHJZH;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.EveryDayTaskVO;
   import UI.Task.TaskVO.LimitingTimeTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.XMLSingle;
   import UI.newTask.EveyDayTask.NewEveryDataItem;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import UI.newTask.NewActivityTask.MewActivityData;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.events.Event;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   
   public class TaskFunction
   {
      private static var _instance:TaskFunction = null;
      
      public function TaskFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在么？！");
      }
      
      public static function getInstance() : TaskFunction
      {
         if(_instance == null)
         {
            _instance = new TaskFunction();
         }
         return _instance;
      }
      
      public function dealWithTaskReward(param1:MTaskVO, param2:Player) : Boolean
      {
         var i:int;
         var length:int;
         var taskRewardVO:TaskRewardVO;
         var isSuccessAdd:Boolean;
         var rewardEquipmentVOs:Vector.<EquipmentVO>;
         var rewardEquipmentVOs2:Vector.<EquipmentVO>;
         var taskVO:MTaskVO = param1;
         var player:Player = param2;
         if(taskVO.state == 1)
         {
            length = int(taskVO.taskRewardVOs.length);
            i = 0;
            while(i < length)
            {
               taskRewardVO = taskVO.taskRewardVOs[i];
               if(taskRewardVO is TaskRewardVO_Equipment)
               {
                  rewardEquipmentVOs = XMLSingle.getEquipmentVOsIDs((taskRewardVO as TaskRewardVO_Equipment).equipments_IDs,XMLSingle.getInstance().equipmentXML,(taskRewardVO as TaskRewardVO_Equipment).equipments_Nums,(taskRewardVO as TaskRewardVO_Equipment).isBinding);
                  MyFunction2.falseAddEquipmentVOs(rewardEquipmentVOs,player,function():void
                  {
                     isSuccessAdd = false;
                  },function():void
                  {
                     isSuccessAdd = true;
                  },null,null);
                  GamingUI.getInstance().refresh(2);
               }
               i++;
            }
            if(isSuccessAdd == false)
            {
               return isSuccessAdd;
            }
            i = 0;
            while(i < length)
            {
               taskRewardVO = taskVO.taskRewardVOs[i];
               if(taskRewardVO is TaskRewardVO_Equipment)
               {
                  rewardEquipmentVOs2 = XMLSingle.getEquipmentVOsIDs((taskRewardVO as TaskRewardVO_Equipment).equipments_IDs,XMLSingle.getInstance().equipmentXML,(taskRewardVO as TaskRewardVO_Equipment).equipments_Nums,(taskRewardVO as TaskRewardVO_Equipment).isBinding);
                  MyFunction2.trueAddEquipmentVOs(rewardEquipmentVOs2,player,null,null);
               }
               else if(taskRewardVO is TaskRewardVO_Experience)
               {
                  MyFunction.getInstance().addPlayerExperience(player,(taskRewardVO as TaskRewardVO_Experience).value);
                  MyFunction.getInstance().addPetExperience(player,(taskRewardVO as TaskRewardVO_Experience).value);
               }
               else if(taskRewardVO is TaskRewardVO_Money)
               {
                  player.playerVO.money += (taskRewardVO as TaskRewardVO_Money).value;
               }
               else if(taskRewardVO is TaskRewardVO_ZHHJZH)
               {
                  player.getMountsVO().addGetMaterialPointNum((taskRewardVO as TaskRewardVO_ZHHJZH).value);
               }
               else
               {
                  if(!(taskRewardVO is TaskRewardVO_LSHSHI))
                  {
                     throw new Error("任务奖励类型错误！");
                  }
                  player.getMountsVO().addStrengthenPointNum((taskRewardVO as TaskRewardVO_LSHSHI).value);
               }
               i++;
            }
            return true;
         }
         throw new Error();
      }
      
      public function judgeFinishTask(param1:MTaskVO) : void
      {
         var _loc4_:Boolean = false;
         var _loc3_:int = 0;
         var _loc2_:Boolean = false;
         var _loc5_:int = 0;
         if(param1 is EveryDayTaskVO)
         {
            _loc4_ = true;
            _loc3_ = int(param1.taskGoalVO_nums.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc4_ &&= param1.taskGoalVO_nums[_loc5_] == param1.currentTaskGoalVO_nums[_loc5_];
               _loc5_++;
            }
            if(_loc4_)
            {
               param1.state = 1;
            }
            else if(param1.state != 2)
            {
               param1.state = 0;
            }
         }
         else if(param1 is LimitingTimeTaskVO)
         {
            _loc4_ = true;
            _loc3_ = int(param1.taskGoalVO_nums.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc4_ &&= param1.taskGoalVO_nums[_loc5_] == param1.currentTaskGoalVO_nums[_loc5_];
               _loc5_++;
            }
            if(_loc4_)
            {
               param1.state = 1;
            }
            else if(param1.state != 2)
            {
               param1.state = 0;
            }
         }
         else if(param1 is AccumulatedTaskVO)
         {
            _loc2_ = true;
            _loc3_ = int(param1.taskGoalVO_nums.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc2_ &&= param1.taskGoalVO_nums[_loc5_] == param1.currentTaskGoalVO_nums[_loc5_];
               _loc5_++;
            }
            if(_loc2_)
            {
               param1.state = 4;
               if((param1 as AccumulatedTaskVO).currentTaskCount == (param1 as AccumulatedTaskVO).taskCount - 1)
               {
                  param1.state = 1;
               }
               else if((param1 as AccumulatedTaskVO).currentTaskCount > (param1 as AccumulatedTaskVO).taskCount)
               {
                  throw new Error();
               }
            }
            else if(param1.state != 2 && param1.state != 3)
            {
               param1.state = 0;
            }
         }
      }
      
      public function dealWithTaskGoalsAndTaskVOs(param1:MTaskVO) : void
      {
         var length:int;
         var length2:int;
         var taskVO:MTaskVO = param1;
         var tt:* = function(param1:Vector.<int>):void
         {
            var _loc6_:int = 0;
            var _loc3_:int = 0;
            var _loc2_:Boolean = false;
            if(param1.length <= 1)
            {
               return;
            }
            var _loc4_:int = int(param1.length);
            var _loc5_:int = int(changeTaskVOIDs.length);
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               if(param1[_loc6_] != taskVO.id)
               {
                  _loc2_ = false;
                  _loc3_ = 0;
                  while(_loc3_ < _loc5_)
                  {
                     if(changeTaskVOIDs[_loc3_] == param1[_loc6_])
                     {
                        _loc2_ = true;
                        break;
                     }
                     _loc3_++;
                  }
                  if(!_loc2_)
                  {
                     changeTaskVOIDs.push(param1[_loc6_]);
                  }
               }
               _loc6_++;
            }
         };
         var judgeCompleteFromTaskVOsByID:* = function(param1:int):void
         {
            var _loc4_:int = 0;
            var _loc2_:Vector.<MTaskVO> = TasksManager.getInstance().acceptedEveryDayTaskVOs.concat(TasksManager.getInstance().acceptedActivityTaskVOs);
            var _loc3_:int = int(_loc2_.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(_loc2_[_loc4_].id == param1)
               {
                  statisticalTaskGoalVONum(_loc2_[_loc4_]);
               }
               _loc4_++;
            }
         };
         var i:int = 0;
         var j:int = 0;
         var changeTaskVOIDs:Vector.<int> = new Vector.<int>();
         i = 0;
         while(i < TaskGoalManager.getInstance().taskGoalVOs.length)
         {
            length2 = int(TaskGoalManager.getInstance().taskGoalVOs[i].owerTaskIDs.length);
            j = 0;
            while(j < length2)
            {
               if(TaskGoalManager.getInstance().taskGoalVOs[i].owerTaskIDs[j] == taskVO.id)
               {
                  tt(TaskGoalManager.getInstance().taskGoalVOs[i].owerTaskIDs);
                  TaskGoalManager.getInstance().taskGoalVOs.splice(i,1);
                  i--;
                  break;
               }
               j++;
            }
            i++;
         }
         length = int(changeTaskVOIDs.length);
         i = 0;
         while(i < length)
         {
            judgeCompleteFromTaskVOsByID(changeTaskVOIDs[i]);
            i++;
         }
      }
      
      public function statisticalTaskGoalVONum(param1:MTaskVO) : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:int = int(TaskGoalManager.getInstance().taskGoalVOs.length);
         var _loc2_:int = int(param1.taskGoalVO_ids.length);
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            param1.currentTaskGoalVO_nums[_loc6_] = 0;
            _loc6_++;
         }
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            for each(var _loc4_ in TaskGoalManager.getInstance().taskGoalVOs[_loc6_].owerTaskIDs)
            {
               if(_loc4_ == param1.id)
               {
                  _loc5_ = 0;
                  while(_loc5_ < _loc2_)
                  {
                     if(TaskGoalManager.getInstance().taskGoalVOs[_loc6_].id == param1.taskGoalVO_ids[_loc5_])
                     {
                        var _loc7_:* = _loc5_;
                        var _loc8_:* = param1.currentTaskGoalVO_nums[_loc7_] + TaskGoalManager.getInstance().taskGoalVOs[_loc6_].num;
                        param1.currentTaskGoalVO_nums[_loc7_] = _loc8_;
                     }
                     _loc5_++;
                  }
               }
            }
            _loc6_++;
         }
         judgeFinishTask(param1);
      }
      
      public function addNewTaskGoalVOByString(param1:String) : void
      {
         var _loc7_:int = 0;
         var _loc3_:Boolean = false;
         var _loc8_:int = 0;
         var _loc5_:NewEveryDataItem = null;
         var _loc4_:* = undefined;
         var _loc2_:int = -1;
         var _loc6_:XMLList = XMLSingle.getInstance().taskXML.TaskGoal[0].item;
         _loc7_ = 0;
         while(_loc7_ < _loc6_.length())
         {
            if(param1 == String(_loc6_[_loc7_].@str))
            {
               _loc2_ = int(_loc6_[_loc7_].@id);
               break;
            }
            _loc7_++;
         }
         if(_loc2_ != -1)
         {
            _loc3_ = false;
            _loc8_ = 0;
            _loc4_ = NewEveryDayData.getInstance().everyAddNums;
            _loc8_ = 0;
            while(_loc8_ < _loc4_.length)
            {
               if(_loc4_[_loc8_].id == _loc2_)
               {
                  _loc4_[_loc8_].num++;
                  _loc3_ = true;
               }
               _loc8_++;
            }
            if(_loc3_ == false)
            {
               _loc5_ = new NewEveryDataItem();
               _loc5_.id = _loc2_;
               _loc5_.num = 1;
               NewEveryDayData.getInstance().everyAddNums.push(_loc5_);
            }
         }
         _loc3_ = false;
         _loc4_ = MewActivityData.getInstance().everyAddNums;
         _loc8_ = 0;
         while(_loc8_ < _loc4_.length)
         {
            if(_loc4_[_loc8_].id == _loc2_)
            {
               _loc4_[_loc8_].num++;
               _loc3_ = true;
            }
            _loc8_++;
         }
         if(_loc3_ == false)
         {
            _loc5_ = new NewEveryDataItem();
            _loc5_.id = _loc2_;
            _loc5_.num = 1;
            MewActivityData.getInstance().everyAddNums.push(_loc5_);
         }
      }
      
      public function addTaskGoalVOByString(param1:String) : void
      {
         var i:int;
         var j:int;
         var taskVOs:Vector.<MTaskVO>;
         var length:int;
         var length2:int;
         var isSame:Boolean;
         var str:String = param1;
         var sortFun:* = function(param1:MTaskVO, param2:MTaskVO):Number
         {
            return param1.id - param2.id;
         };
         var taskGoalVO:TaskGoalVO = XMLSingle.getInstance().createTaskGoalByStr(str);
         if(taskGoalVO)
         {
            i = 0;
            j = 0;
            taskVOs = TasksManager.getInstance().acceptedEveryDayTaskVOs.concat(TasksManager.getInstance().acceptedActivityTaskVOs).sort(sortFun);
            length = int(taskVOs.length);
            i = 0;
            while(i < length)
            {
               if(!(taskVOs[i].state == 1 || taskVOs[i].state == 2 || taskVOs[i].state == 4 || taskVOs[i].state == 3))
               {
                  length2 = int(taskVOs[i].taskGoalVO_ids.length);
                  j = 0;
                  while(j < length2)
                  {
                     if(taskVOs[i].taskGoalVO_ids[j] == taskGoalVO.id && taskVOs[i].taskGoalVO_nums[j] > taskVOs[i].currentTaskGoalVO_nums[j])
                     {
                        taskGoalVO.owerTaskIDs.push(taskVOs[i].id);
                        taskVOs[i].currentTaskGoalVO_nums[j]++;
                        judgeFinishTask(taskVOs[i]);
                     }
                     j++;
                  }
               }
               i++;
            }
            length = int(TaskGoalManager.getInstance().taskGoalVOs.length);
            i = 0;
            while(i < length)
            {
               if(TaskGoalManager.getInstance().taskGoalVOs[i].id == taskGoalVO.id)
               {
                  if(TaskGoalManager.getInstance().taskGoalVOs[i].owerTaskIDs.length == taskGoalVO.owerTaskIDs.length)
                  {
                     length2 = int(taskGoalVO.owerTaskIDs.length);
                     isSame = true;
                     j = 0;
                     while(j < length2)
                     {
                        isSame = isSame && taskGoalVO.owerTaskIDs[j] == TaskGoalManager.getInstance().taskGoalVOs[i].owerTaskIDs[j];
                        j++;
                     }
                     if(isSame)
                     {
                        TaskGoalManager.getInstance().taskGoalVOs[i].num++;
                        break;
                     }
                  }
               }
               i++;
            }
            if(i == length)
            {
               if(taskGoalVO.owerTaskIDs && taskGoalVO.owerTaskIDs.length)
               {
                  TaskGoalManager.getInstance().taskGoalVOs.push(taskGoalVO);
               }
            }
         }
      }
      
      public function initExTask(param1:XML, param2:XML, param3:String) : void
      {
         var _loc14_:MTaskVO = null;
         var _loc15_:int = 0;
         var _loc6_:Boolean = false;
         var _loc5_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc12_:XMLList = param1.item;
         var _loc13_:int = 0;
         var _loc8_:int = int(_loc12_.length());
         var _loc7_:Vector.<MTaskVO> = TasksManager.getInstance().acceptedEveryDayTaskVOs.concat(TasksManager.getInstance().acceptedActivityTaskVOs);
         var _loc9_:Vector.<MTaskVO> = _loc7_.concat(TasksManager.getInstance().wasteTaskVOs);
         var _loc16_:int = int(_loc7_.length);
         _loc13_ = 0;
         while(_loc13_ < _loc16_)
         {
            _loc7_[_loc13_] = null;
            _loc13_++;
         }
         _loc7_ = null;
         var _loc10_:int = 0;
         var _loc11_:int = int(_loc9_.length);
         _loc13_ = 0;
         for(; _loc13_ < _loc8_; _loc13_++)
         {
            _loc6_ = false;
            _loc15_ = int(_loc12_[_loc13_].@id);
            _loc10_ = 0;
            while(_loc10_ < _loc11_)
            {
               if(_loc15_ == _loc9_[_loc10_].id)
               {
                  _loc6_ = true;
                  break;
               }
               _loc10_++;
            }
            if(!_loc6_)
            {
               if(String(param2.Task.item.(@id == _loc15_)[0].@formerData) && String(param2.Task.item.(@id == _loc15_)[0].@latterData))
               {
                  _loc5_ = new TimeUtil().timeInterval(param2.Task.item.(@id == _loc15_)[0].@formerData,param3);
                  _loc4_ = new TimeUtil().timeInterval(param3,param2.Task.item.(@id == _loc15_)[0].@latterData);
                  if(_loc5_ < 0 || _loc4_ < 0)
                  {
                     continue;
                  }
               }
               _loc14_ = XMLSingle.getTask(int(_loc12_[_loc13_].@id),param2);
               _loc14_.state = 0;
               if(MyFunction.getInstance().getTaskTypeFromID(_loc12_[_loc13_].@id))
               {
                  TasksManager.getInstance().activityTaskVOs.push(_loc14_);
               }
               else
               {
                  TasksManager.getInstance().taskVOs.push(_loc14_);
               }
            }
         }
      }
      
      public function getTaskIsReceive(param1:int) : Boolean
      {
         var _loc2_:Boolean = false;
         var _loc4_:int = 0;
         var _loc3_:int = int(TasksManager.getInstance().acceptedActivityTaskVOs.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1 == TasksManager.getInstance().acceptedActivityTaskVOs[_loc4_].id)
            {
               _loc2_ = true;
               break;
            }
            _loc4_++;
         }
         if(!_loc2_)
         {
            _loc3_ = int(TasksManager.getInstance().acceptedEveryDayTaskVOs.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(param1 == TasksManager.getInstance().acceptedEveryDayTaskVOs[_loc4_].id)
               {
                  _loc2_ = true;
                  break;
               }
               _loc4_++;
            }
         }
         return _loc2_;
      }
      
      public function resetCompletedTask() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "重置进行中...";
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < TasksManager.getInstance().wasteTaskVOs.length)
         {
            if(TasksManager.getInstance().wasteTaskVOs[_loc2_].resetType == "DieRevive" || TasksManager.getInstance().wasteTaskVOs[_loc2_].resetType == "everyDayReset")
            {
               TasksManager.getInstance().wasteTaskVOs.splice(_loc2_,1);
               _loc2_--;
            }
            _loc2_++;
         }
         var _loc1_:int = int(TasksManager.getInstance().acceptedActivityTaskVOs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(TasksManager.getInstance().acceptedActivityTaskVOs[_loc2_] is AccumulatedTaskVO && TasksManager.getInstance().acceptedActivityTaskVOs[_loc2_].state != 1 && TasksManager.getInstance().acceptedActivityTaskVOs[_loc2_].state != 4)
            {
               TasksManager.getInstance().acceptedActivityTaskVOs[_loc2_].state = 0;
            }
            _loc2_++;
         }
         reLoadAndInitExTask();
      }
      
      private function onEveryDayTaskComplete(param1:Event) : void
      {
         var strInfo:String;
         var everyDayTaskXML:XML;
         var e:Event = param1;
         var dealWithDate:* = function(param1:String):void
         {
            TasksManager.getInstance().activityTaskVOs = new Vector.<MTaskVO>();
            TasksManager.getInstance().taskVOs = new Vector.<MTaskVO>();
            TaskFunction.getInstance().initExTask(everyDayTaskXML,XMLSingle.getInstance().taskXML,param1);
            if(GamingUI.getInstance().exTaskPanel)
            {
               GamingUI.getInstance().exTaskPanel.currentState = 0;
               GamingUI.getInstance().exTaskPanel.refreshTaskList();
            }
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         };
         e.currentTarget.removeEventListener("complete",onEveryDayTaskComplete,false);
         strInfo = (e.currentTarget as URLLoader).data;
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            try
            {
               strInfo = MyBase64.getReallyDate(strInfo);
            }
            catch(e:Error)
            {
               throw new Error("任务配置表解密错误",43999);
            }
         }
         everyDayTaskXML = new XML(strInfo);
         MyFunction2.getServerTimeFunction(dealWithDate,null,false);
      }
      
      public function checkAutomaticPetTask() : void
      {
         var _loc2_:int = 0;
         var _loc1_:AutomaticPetsData = GamingUI.getInstance().getAutomaticPetsData();
         _loc2_ = 0;
         while(_loc2_ < _loc1_.getAutomaticPetVONum())
         {
            TaskFunction.getInstance().addTaskGoalVOByString("autoPet_" + _loc1_.getAutomaticPetVOByIndex(_loc2_).getRealID());
            _loc2_++;
         }
      }
      
      public function reLoadAndInitExTask() : void
      {
         var _loc1_:String = GamingUI.getInstance().getVersionControl().getXMLVersionStr();
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",onEveryDayTaskComplete,false,0,true);
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            _loc2_.load(new URLRequest("CodeXML" + _loc1_ + "/UIData/EveryDayTask.xml" + "?v=" + Math.random() * 99999));
         }
         else
         {
            _loc2_.load(new URLRequest("XML" + _loc1_ + "/UIData/EveryDayTask.xml" + "?v=" + Math.random() * 99999));
         }
      }
   }
}

