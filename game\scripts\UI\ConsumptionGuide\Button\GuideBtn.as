package UI.ConsumptionGuide.Button
{
   import UI.Button.Btn;
   import UI.ConsumptionGuide.GuideUseEquipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.Timer;
   
   public class GuideBtn extends Btn
   {
      public static const SHOW_GUIDE_BOX:String = "showGuideBox";
      
      private var _equipmentID:int;
      
      private var _fun:Function;
      
      private var _reservePositionNum:int;
      
      protected var _guideUseEquipment:GuideUseEquipment;
      
      protected var _showWarningFun:Function;
      
      public var IsLaterOn:Boolean;
      
      private var m_renderTimer:Timer;
      
      public function GuideBtn(param1:int, param2:Function = null, param3:int = 0)
      {
         var equipmentID:int = param1;
         var fun:Function = param2;
         var reservePositionNum:int = param3;
         super();
         _showWarningFun = function(param1:String, param2:int):void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":param1,
               "flag":param2
            }));
         };
         _equipmentID = equipmentID;
         _fun = fun;
         _reservePositionNum = reservePositionNum;
         IsLaterOn = false;
         m_renderTimer = new Timer(1000);
         m_renderTimer.addEventListener("timer",timerRender,false,0,true);
      }
      
      public function setEquipmentID(param1:int) : void
      {
         _equipmentID = param1;
      }
      
      public function setGuideEquipment(param1:GuideUseEquipment) : void
      {
         _guideUseEquipment = param1;
         _guideUseEquipment.setWarningTextFontSizse(WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      override public function clear() : void
      {
         super.clear();
         _fun = null;
         if(_guideUseEquipment)
         {
            _guideUseEquipment.clear();
         }
         _guideUseEquipment = null;
         if(m_renderTimer)
         {
            m_renderTimer.stop();
         }
         m_renderTimer = null;
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         var _loc3_:Player = null;
         var _loc2_:EquipmentVO = null;
         if(GamingUI.getInstance().internalPanel && GamingUI.getInstance().internalPanel.currentPlayer)
         {
            _loc3_ = GamingUI.getInstance().internalPanel.currentPlayer;
         }
         else
         {
            _loc3_ = GamingUI.getInstance().player1;
         }
         if(IsLaterOn == true)
         {
            m_renderTimer.start();
            SetMouseClickNotAble();
         }
         if(_guideUseEquipment)
         {
            _guideUseEquipment.setPlayer(_loc3_);
         }
         if(_guideUseEquipment == null || _guideUseEquipment.useEq() == false)
         {
            _loc2_ = XMLSingle.getEquipmentVOByID(_equipmentID,XMLSingle.getInstance().equipmentXML);
            dispatchEvent(new UIDataEvent("showGuideBox",{
               "equipmentVO":_loc2_,
               "fun":_fun,
               "reservePositionNum":_reservePositionNum
            }));
         }
      }
      
      private function timerRender(param1:Event) : void
      {
         SetMouseClickAble();
         m_renderTimer.stop();
      }
      
      public function SetMouseClickNotAble() : void
      {
         removeEventListener("click",clickBtn,false);
      }
      
      public function SetMouseClickAble() : void
      {
         addEventListener("click",clickBtn,false,0,true);
      }
   }
}

