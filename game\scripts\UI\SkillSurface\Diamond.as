package UI.SkillSurface
{
   import UI.MyMovieClip;
   
   public class Diamond extends MyMovieClip
   {
      public static const FIRE_STATE:int = 0;
      
      public static const ICE_STATE:int = 1;
      
      public static const GAS_STATE:int = 2;
      
      public static const LIGHT_STATE:int = 3;
      
      public static const SOUL_STATE:int = 4;
      
      public static const GREEN_STATE:int = 5;
      
      public static const FIRE_NEW_STATE:int = 6;
      
      public static const PURPLE_STATE:int = 7;
      
      private var state:int = 0;
      
      public function Diamond()
      {
         super();
         addFrameScript(0,addScript);
         addFrameScript(1,addScript);
      }
      
      public function setState(param1:int) : void
      {
         state = param1;
      }
      
      public function gotoOneFrame() : void
      {
         gotoAndStop(1);
      }
      
      public function gotoTwoFrame() : void
      {
         switch(state)
         {
            case 0:
               gotoAndStop(2);
               break;
            case 1:
               gotoAndStop(3);
               break;
            case 2:
               gotoAndStop(4);
               break;
            case 3:
               gotoAndStop(5);
               break;
            case 4:
               gotoAndStop(6);
               break;
            case 5:
               gotoAndStop(7);
               break;
            case 6:
               gotoAndStop(8);
               break;
            case 7:
               gotoAndStop(8);
         }
      }
      
      protected function addScript() : void
      {
         stop();
      }
   }
}

