package UI2.ProgramStartData
{
   import UI.DataManagerParent;
   import UI.PKUI.PlayerDataForPK;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class ProgramStartData extends DataManagerParent
   {
      private static var m_instance:ProgramStartData;
      
      private var m_pSProMaxValueDatas:Array = [{
         "className":"PetSkill_GuWu",
         "value":40
      },{
         "className":"PetSkill_FaShuZengQiang",
         "value":1000
      },{
         "className":"PetSkill_ShouHu",
         "value":40
      },{
         "className":"PetSkill_JianZhuang",
         "value":40
      },{
         "className":"PetSkill_MiShuZhangWo",
         "value":2
      },{
         "className":"PetSkill_JiNengAoYi",
         "value":10
      },{
         "className":"PetSkill_JiNengLingWu",
         "value":10
      }];
      
      private var m_version:uint;
      
      private var m_discount:Number;
      
      private var m_normalCriticalMulti:uint;
      
      private var m_percent_oneHundred:uint;
      
      private var m_superAttackMulti:uint;
      
      private var m_one:uint;
      
      private var m_zero:uint;
      
      private var m_winAddPKPoint:uint = 10;
      
      private var m_failAddPKPoint:uint = 3;
      
      private var m_oneThousand:uint = 1000;
      
      private var m_sixty:uint = 60;
      
      private var m_fifty:uint = 50;
      
      private var m_petPassiveSkillProMaxValueDatas:Vector.<PetSkillProMaxValueData>;
      
      public function ProgramStartData()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            return;
         }
         throw new Error("fuck you, 实例已经存在");
      }
      
      public static function getInstance() : ProgramStartData
      {
         if(m_instance == null)
         {
            m_instance = new ProgramStartData();
         }
         return m_instance;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pSProMaxValueDatas);
         m_pSProMaxValueDatas = null;
         ClearUtil.clearObject(m_petPassiveSkillProMaxValueDatas);
         m_petPassiveSkillProMaxValueDatas = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:PetSkillProMaxValueData = null;
         super.init();
         _antiwear.version = m_version = 0;
         _antiwear.discount = m_discount = 0.1;
         _antiwear.normalCriticalMulti = m_normalCriticalMulti = 2;
         _antiwear.percent_oneHundred = m_percent_oneHundred = 100;
         _antiwear.superAttackMulti = m_superAttackMulti = 3;
         _antiwear.one = m_one = 1;
         _antiwear.zero = m_zero = 0;
         _antiwear.winAddPKPoint = m_winAddPKPoint = 10;
         _antiwear.failAddPKPoint = m_failAddPKPoint = 3;
         _antiwear.oneThousand = m_oneThousand = 1000;
         _antiwear.sixty = m_sixty = 60;
         _antiwear.fifty = m_fifty = 50;
         version = Part1.getInstance().getVersion();
         discount = 0.1;
         normalCriticalMulti = 2;
         percent_oneHundred = 100;
         superAttackMulti = 3;
         one = 1;
         zero = 0;
         winAddPKPoint = 10;
         failAddPKPoint = 3;
         oneThousand = 1000;
         sixty = 60;
         fifty = 50;
         ClearUtil.clearObject(m_petPassiveSkillProMaxValueDatas);
         m_petPassiveSkillProMaxValueDatas = new Vector.<PetSkillProMaxValueData>();
         _loc1_ = int(m_pSProMaxValueDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = new PetSkillProMaxValueData();
            _loc2_.initData(m_pSProMaxValueDatas[_loc3_]["className"],m_pSProMaxValueDatas[_loc3_]["value"]);
            m_petPassiveSkillProMaxValueDatas.push(_loc2_);
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < 11)
         {
            _antiwear["num" + _loc3_] = _loc3_;
            _loc3_++;
         }
      }
      
      public function getVersion() : uint
      {
         return version;
      }
      
      public function getDiscount() : Number
      {
         return this.discount;
      }
      
      public function getNormalCriticalMulti() : uint
      {
         return normalCriticalMulti;
      }
      
      public function getPercent_oneHundred() : uint
      {
         return percent_oneHundred;
      }
      
      public function getSuperAttackMulti() : uint
      {
         return superAttackMulti;
      }
      
      public function getOne() : uint
      {
         return one;
      }
      
      public function getZero() : uint
      {
         return zero;
      }
      
      public function getWinAddPKPoint() : uint
      {
         trace("aaaA:" + winAddPKPoint * (1 + PlayerDataForPK.getInstance().extraAddPkPointRate / 100));
         return winAddPKPoint * (1 + PlayerDataForPK.getInstance().extraAddPkPointRate / 100);
      }
      
      public function getFailAddPKPoint() : uint
      {
         return failAddPKPoint * (1 + PlayerDataForPK.getInstance().extraAddPkPointRate / 100);
      }
      
      public function getPetPassiveSkillProMaxValueDataNum() : uint
      {
         return m_petPassiveSkillProMaxValueDatas.length;
      }
      
      public function getPetPassiveSkillProMaxValueDataByIndex(param1:int) : PetSkillProMaxValueData
      {
         return m_petPassiveSkillProMaxValueDatas[param1];
      }
      
      public function getOneThousand() : uint
      {
         return oneThousand;
      }
      
      public function getSixty() : uint
      {
         return sixty;
      }
      
      public function getFifty() : uint
      {
         return fifty;
      }
      
      public function get_zero() : uint
      {
         return _antiwear["num0"];
      }
      
      public function get_one() : uint
      {
         return _antiwear["num1"];
      }
      
      public function get_two() : uint
      {
         return _antiwear["num2"];
      }
      
      public function get_three() : uint
      {
         return _antiwear["num3"];
      }
      
      public function get_four() : uint
      {
         return _antiwear["num4"];
      }
      
      public function get_five() : uint
      {
         return _antiwear["num5"];
      }
      
      public function get_six() : uint
      {
         return _antiwear["num6"];
      }
      
      public function get_seven() : uint
      {
         return _antiwear["num7"];
      }
      
      public function get_eight() : uint
      {
         return _antiwear["num8"];
      }
      
      public function get_nine() : uint
      {
         return _antiwear["num9"];
      }
      
      public function get_ten() : uint
      {
         return _antiwear["num10"];
      }
      
      private function get version() : uint
      {
         return _antiwear.version;
      }
      
      private function set version(param1:uint) : void
      {
         _antiwear.version = param1;
      }
      
      private function get discount() : Number
      {
         return _antiwear.discount;
      }
      
      private function set discount(param1:Number) : void
      {
         _antiwear.discount = param1;
      }
      
      private function get normalCriticalMulti() : uint
      {
         return _antiwear.normalCriticalMulti;
      }
      
      private function set normalCriticalMulti(param1:uint) : void
      {
         _antiwear.normalCriticalMulti = param1;
      }
      
      private function get percent_oneHundred() : uint
      {
         return _antiwear.percent_oneHundred;
      }
      
      private function set percent_oneHundred(param1:uint) : void
      {
         _antiwear.percent_oneHundred = param1;
      }
      
      private function get superAttackMulti() : uint
      {
         return _antiwear.superAttackMulti;
      }
      
      private function set superAttackMulti(param1:uint) : void
      {
         _antiwear.superAttackMulti = param1;
      }
      
      private function get one() : uint
      {
         return _antiwear.one;
      }
      
      private function set one(param1:uint) : void
      {
         _antiwear.one = param1;
      }
      
      private function get zero() : uint
      {
         return _antiwear.zero;
      }
      
      private function set zero(param1:uint) : void
      {
         _antiwear.zero = param1;
      }
      
      private function get winAddPKPoint() : uint
      {
         return _antiwear.winAddPKPoint;
      }
      
      private function set winAddPKPoint(param1:uint) : void
      {
         _antiwear.winAddPKPoint = param1;
      }
      
      private function get failAddPKPoint() : uint
      {
         return _antiwear.failAddPKPoint;
      }
      
      private function set failAddPKPoint(param1:uint) : void
      {
         _antiwear.failAddPKPoint = param1;
      }
      
      private function get oneThousand() : uint
      {
         return _antiwear.oneThousand;
      }
      
      private function set oneThousand(param1:uint) : void
      {
         _antiwear.oneThousand = param1;
      }
      
      private function get sixty() : uint
      {
         return _antiwear.sixty;
      }
      
      private function set sixty(param1:uint) : void
      {
         _antiwear.sixty = param1;
      }
      
      private function get fifty() : uint
      {
         return _antiwear.fifty;
      }
      
      private function set fifty(param1:uint) : void
      {
         _antiwear.fifty = param1;
      }
   }
}

