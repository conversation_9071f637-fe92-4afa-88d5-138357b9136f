package UI2.SmallAssistant.ActiveTask
{
   import UI.MainLineTask.MainLineTaskVO;
   import YJFY.Utils.ClearUtil;
   
   public class HaveActiveValueTaskData
   {
      private var m_activeValueDataOfTask:ActiveValueDataOfTask;
      
      private var m_taskVO:MainLineTaskVO;
      
      public function HaveActiveValueTaskData(param1:MainLineTaskVO, param2:ActiveValueDataOfTask)
      {
         super();
         m_taskVO = param1;
         m_activeValueDataOfTask = param2;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_activeValueDataOfTask);
         m_activeValueDataOfTask = null;
         m_taskVO = null;
      }
      
      public function getTaskVO() : MainLineTaskVO
      {
         return m_taskVO;
      }
      
      public function getActiveValueDataOfTask() : ActiveValueDataOfTask
      {
         return m_activeValueDataOfTask;
      }
   }
}

