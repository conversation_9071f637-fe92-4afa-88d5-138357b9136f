package UI.Shop
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class ShopData
   {
      public static var _instance:ShopData = null;
      
      private var _currentBuyNumInPKShop:int;
      
      private var _binaryEn:binaryEncrypt;
      
      private var _antiwear:Antiwear;
      
      public function ShopData()
      {
         super();
         if(!_instance)
         {
            init();
            _instance = this;
            return;
         }
         throw new Error("fuck you!没看见实例已经存在了么？！");
      }
      
      public static function getInstance() : ShopData
      {
         if(!_instance)
         {
            _instance = new ShopData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
         _instance = null;
      }
      
      private function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.currentBuyNumInPKShop = _currentBuyNumInPKShop = 0;
      }
      
      public function get currentBuyNumInPKShop() : int
      {
         return _antiwear.currentBuyNumInPKShop;
      }
      
      public function set currentBuyNumInPKShop(param1:int) : void
      {
         _antiwear.currentBuyNumInPKShop = param1;
      }
   }
}

