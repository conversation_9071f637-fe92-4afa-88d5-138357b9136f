package UI.RefineFactory.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class PutAllBtn extends Btn
   {
      public function PutAllBtn()
      {
         super();
         setTipString("放入全部草药");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickPutAllBtn"));
      }
   }
}

