package UI.newTask.NewMainTask
{
   import UI.Event.UIPassiveEvent;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MainLineTask.RewardShow_TaskPanel;
   import UI.MessageBox.MessageBoxEngine;
   import UI.newTask.NewTaskPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class NewMainReward
   {
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_newmaintaskpanel:NewMainTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_rewardShow:RewardShow_TaskPanel;
      
      public function NewMainReward()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_rewardShow);
         m_rewardShow = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewMainTaskPanel) : void
      {
         m_newtaskpanel = param1;
         m_newmaintaskpanel = param3;
         m_show = param2;
         m_mc = param2["taskRewardShow"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_rewardShow = new RewardShow_TaskPanel();
         m_rewardShow.setShow(m_mc);
      }
      
      public function showReward(param1:MainLineTaskVO) : void
      {
         m_rewardShow.showReward(param1);
      }
      
      public function show() : void
      {
         registerEvent();
         m_mc.visible = true;
         m_mc.x = 343.1;
         m_mc.y = 328.35;
      }
      
      public function hide() : void
      {
         closeEvent();
         m_mc.visible = false;
         m_mc.x = 10000;
         m_mc.y = 10000;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         param1.button;
      }
      
      private function registerEvent() : void
      {
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function closeEvent() : void
      {
         m_mc.removeEventListener("clickButton",clickButton,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,true);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,false);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,false);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

