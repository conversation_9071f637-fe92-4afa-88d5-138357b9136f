package UI2.SmallAssistant.ActiveTask
{
   import UI.DataManagerParent;
   
   public class ActiveValueDataOfTask extends DataManagerParent
   {
      private var m_taskId:String;
      
      private var m_activeValue:uint;
      
      private var m_skipStr:String;
      
      public function ActiveValueDataOfTask()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_taskId = null;
         m_skipStr = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         taskId = String(param1.@taskId);
         activeValue = uint(param1.@value);
         skipStr = String(param1.@skipStr);
      }
      
      public function getTaskId() : String
      {
         return taskId;
      }
      
      public function getActiveValue() : uint
      {
         return activeValue;
      }
      
      public function getSkipStr() : String
      {
         return skipStr;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.taskId = m_taskId;
         _antiwear.activeValue = m_activeValue;
      }
      
      private function get taskId() : String
      {
         return _antiwear.taskId;
      }
      
      private function set taskId(param1:String) : void
      {
         _antiwear.taskId = param1;
      }
      
      private function get activeValue() : uint
      {
         return _antiwear.activeValue;
      }
      
      private function set activeValue(param1:uint) : void
      {
         _antiwear.activeValue = param1;
      }
      
      private function get skipStr() : String
      {
         return m_skipStr;
      }
      
      private function set skipStr(param1:String) : void
      {
         m_skipStr = param1;
      }
   }
}

