package UI.Skills.PetSkills
{
   import UI.ShiTu.PromoteValueObject;
   import UI.Skills.SkillVO;
   import YJFY.Utils.ClearUtil;
   
   public class PetAwakeSkillVO extends SkillVO
   {
      public static const PLAYER:String = "player";
      
      public static const PET:String = "pet";
      
      public var attributes:Vector.<String>;
      
      public var attributeValues:Vector.<PromoteValueObject>;
      
      public var descriptions:Vector.<String>;
      
      public function PetAwakeSkillVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(attributes);
         ClearUtil.nullArr(attributeValues);
         ClearUtil.nullArr(descriptions);
         attributes = null;
         attributeValues = null;
         descriptions = null;
      }
      
      override public function clone() : SkillVO
      {
         var _loc1_:PetAwakeSkillVO = new PetAwakeSkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribue(param1:SkillVO) : void
      {
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribue(param1);
         var _loc3_:Vector.<String> = new Vector.<String>();
         _loc2_ = !!attributes ? attributes.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc3_.push(attributes[_loc6_]);
            _loc6_++;
         }
         (param1 as PetAwakeSkillVO).attributes = _loc3_;
         var _loc5_:Vector.<PromoteValueObject> = new Vector.<PromoteValueObject>();
         _loc2_ = !!attributeValues ? attributeValues.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_.push(attributeValues[_loc6_].clone());
            _loc6_++;
         }
         (param1 as PetAwakeSkillVO).attributeValues = _loc5_;
         var _loc4_:Vector.<String> = new Vector.<String>();
         _loc2_ = !!descriptions.length ? descriptions.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc4_.push(descriptions[_loc6_]);
            _loc6_++;
         }
         (param1 as PetAwakeSkillVO).descriptions = _loc4_;
      }
   }
}

