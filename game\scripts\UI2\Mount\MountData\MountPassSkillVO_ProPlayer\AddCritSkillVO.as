package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddCritSkillVO extends MountPassSkillVO_ProPlayer
   {
      private var m_addCrit:uint;
      
      private var m_addHit:Number;
      
      private var m_addValueForHit:Number;
      
      public function AddCritSkillVO()
      {
         super();
         m_addValueForHit = 0;
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") - m_addValue);
         m_targetPlayer.playerVO.set2("hit_mountAdd1",m_targetPlayer.playerVO.get2("hit_mountAdd1") - m_addValueForHit);
         m_addValue = 0;
         m_addValueForHit = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addCrit;
         m_addValueForHit = addHit;
         m_targetPlayer.playerVO.set2("criticalRate_mountAdd1",m_targetPlayer.playerVO.get2("criticalRate_mountAdd1") + m_addValue);
         m_targetPlayer.playerVO.set2("hit_mountAdd1",m_targetPlayer.playerVO.get2("hit_mountAdd1") + m_addValueForHit);
      }
      
      public function getAddCrit() : uint
      {
         return m_addCrit;
      }
      
      public function getAddHit() : uint
      {
         return m_addHit;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addCrit = uint(param1.data.(@att == "addCrit")[0].@value);
         this.addHit = Number(param1.data.(@att == "addHit")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addCrit = m_addCrit;
         _antiwear.addHit = m_addHit;
      }
      
      private function get addCrit() : uint
      {
         return _antiwear.addCrit;
      }
      
      private function set addCrit(param1:uint) : void
      {
         _antiwear.addCrit = param1;
      }
      
      private function get addHit() : Number
      {
         return _antiwear.addHit;
      }
      
      private function set addHit(param1:Number) : void
      {
         _antiwear.addHit = param1;
      }
   }
}

