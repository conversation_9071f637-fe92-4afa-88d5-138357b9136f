package UI2.NewRank
{
   import UI.GamingUI;
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class NewRankRecord extends MySprite
   {
      private var m_rank:RankWorld;
      
      private var m_show:Sprite;
      
      private var m_close:MovieClip;
      
      private var m_list:Vector.<MovieClip>;
      
      private var m_myloader:YJFYLoader;
      
      public function NewRankRecord(param1:RankWorld)
      {
         super();
         m_rank = param1;
         m_list = new Vector.<MovieClip>();
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_list);
         m_list = null;
      }
      
      public function init() : void
      {
         m_myloader.getClass("rankworld.swf","recordpanel",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         this.addChild(m_show);
         this.initShow();
         registerBtn();
         refreshList();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function initShow() : void
      {
         var _loc1_:MovieClip = null;
         var _loc2_:int = 0;
         m_close = m_show["closebtn"] as MovieClip;
         m_close.gotoAndStop(1);
         _loc2_ = 0;
         while(_loc2_ < 7)
         {
            _loc1_ = m_show["recorditem_" + (_loc2_ + 1)] as MovieClip;
            m_list.push(_loc1_);
            _loc1_.visible = false;
            _loc2_++;
         }
      }
      
      private function registerBtn() : void
      {
         m_close.addEventListener("click",clickcall);
      }
      
      private function clickcall(param1:MouseEvent) : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      private function refreshList() : void
      {
         var _loc3_:RecordInfo = null;
         var _loc2_:TextField = null;
         var _loc1_:MovieClip = null;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < 7)
         {
            _loc3_ = RankDataInfo.getInstance().getRecordByIndex(_loc4_);
            if(_loc3_)
            {
               m_list[_loc4_].visible = true;
               _loc2_ = m_list[_loc4_]["datetext"] as TextField;
               _loc2_.text = String(_loc3_.m_year) + "." + String(_loc3_.m_month) + "." + String(_loc3_.m_day);
               _loc2_ = m_list[_loc4_]["fighttext"] as TextField;
               if(_loc3_.m_challenge == true)
               {
                  _loc2_.text = _loc3_.m_myname + "挑战" + _loc3_.m_foename;
               }
               else
               {
                  _loc2_.text = _loc3_.m_foename + "挑战" + _loc3_.m_myname;
               }
               _loc2_ = m_list[_loc4_]["ranktext"] as TextField;
               _loc2_.text = "排名:" + String(_loc3_.m_oldrank) + "-" + String(_loc3_.m_newrank);
               _loc1_ = m_list[_loc4_]["winimg"] as MovieClip;
               _loc1_.gotoAndStop(_loc3_.m_win);
            }
            else
            {
               m_list[_loc4_].visible = false;
            }
            _loc4_++;
         }
      }
   }
}

