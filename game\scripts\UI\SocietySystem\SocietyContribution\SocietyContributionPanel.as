package UI.SocietySystem.SocietyContribution
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.SocietySystem.MySocietyPanel.MySocietyPanel;
   import UI.SocietySystem.SocietyContriData;
   import UI.SocietySystem.SocietyContriVO;
   import UI.SocietySystem.SocietySystem;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class SocietyContributionPanel
   {
      private var m_societyContriData:SocietyContriData;
      
      private var m_ableDragLS:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_rNumOfExpText:TextField;
      
      private var m_rNumOfMoneyText:TextField;
      
      private var m_rNumOfTicketText:TextField;
      
      private var m_contriOfExpBtn:ButtonLogicShell2;
      
      private var m_contriOfMoneyBtn:ButtonLogicShell2;
      
      private var m_contriOfTicketBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_mySocietyPanel:MySocietyPanel;
      
      private var m_societySystemXML:XML;
      
      private var m_societySystem:SocietySystem;
      
      public function SocietyContributionPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            ClearUtil.clearDisplayObjectInContainer(m_show);
            if(m_show.parent)
            {
               m_show.parent.removeChild(m_show);
            }
            m_show = null;
         }
         ClearUtil.clearObject(m_societyContriData);
         m_societyContriData = null;
         ClearUtil.clearObject(m_ableDragLS);
         m_ableDragLS = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_rNumOfExpText = null;
         m_rNumOfMoneyText = null;
         m_rNumOfTicketText = null;
         ClearUtil.clearObject(m_contriOfExpBtn);
         m_contriOfExpBtn = null;
         ClearUtil.clearObject(m_contriOfMoneyBtn);
         m_contriOfMoneyBtn = null;
         ClearUtil.clearObject(m_contriOfTicketBtn);
         m_contriOfTicketBtn = null;
         m_mySocietyPanel = null;
         m_societySystemXML = null;
         m_societySystem = null;
      }
      
      public function setShow(param1:MovieClip, param2:MySocietyPanel, param3:SocietySystem, param4:XML, param5:SocietyContriVO) : void
      {
         var show:MovieClip = param1;
         var mySocietyPanel:MySocietyPanel = param2;
         var societySystem:SocietySystem = param3;
         var societySystemXML:XML = param4;
         var societyContriVO:SocietyContriVO = param5;
         m_show = show;
         m_mySocietyPanel = mySocietyPanel;
         m_societySystem = societySystem;
         m_societySystemXML = societySystemXML;
         ClearUtil.clearObject(m_societyContriData);
         m_societyContriData = null;
         initShow();
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_societyContriData = new SocietyContriData();
            m_societyContriData.initData(societyContriVO,param1,societySystemXML);
            initShow2();
         },m_mySocietyPanel.showWarningBox,true);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function addConValueByExp() : void
      {
         if(m_societyContriData.getrNum_contriOfExp() <= 0)
         {
            m_mySocietyPanel.showWarningBox("经验捐献剩余次数为0, 不能捐献。",0);
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc2_:SaveTaskInfo = null;
            var _loc4_:int = GamingUI.getInstance().player1.playerVO.experiencePercent * GamingUI.getInstance().player1.playerVO.experienceVolume;
            var _loc3_:int = int(m_societySystemXML.Contribution[0].contriOfExp[0].@needExp);
            if(_loc4_ >= _loc3_)
            {
               GamingUI.getInstance().player1.playerVO.experiencePercent = (GamingUI.getInstance().player1.playerVO.experiencePercent * GamingUI.getInstance().player1.playerVO.experienceVolume - _loc3_) / GamingUI.getInstance().player1.playerVO.experienceVolume;
               m_societySystem.addConValue(m_societySystemXML.Contribution[0].contriOfExp[0].@idOfConValue);
               m_societyContriData.addOneNum_contriOfExp();
               initShow2();
               _loc2_ = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
            }
            else
            {
               m_mySocietyPanel.showWarningBox("人物1经验不足，无法进行经验捐献。",0);
            }
         },m_mySocietyPanel.showWarningBox,true);
      }
      
      public function addFailConValueByExp() : void
      {
         var _loc2_:int = int(m_societySystemXML.Contribution[0].contriOfExp[0].@needExp);
         GamingUI.getInstance().player1.playerVO.experiencePercent = (GamingUI.getInstance().player1.playerVO.experiencePercent * GamingUI.getInstance().player1.playerVO.experienceVolume + _loc2_) / GamingUI.getInstance().player1.playerVO.experienceVolume;
         m_societyContriData.decOneNum_contriOfExp();
         initShow2();
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
      }
      
      public function addConValueByMoney() : void
      {
         if(m_societyContriData.getrNum_contriOfMoney() <= 0)
         {
            m_mySocietyPanel.showWarningBox("元宝捐献剩余次数为0, 不能捐献。",0);
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc3_:SaveTaskInfo = null;
            var _loc4_:int = GamingUI.getInstance().player1.playerVO.money;
            var _loc2_:int = int(m_societySystemXML.Contribution[0].contriOfMoney[0].@needMoney);
            if(_loc4_ >= _loc2_)
            {
               GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money - _loc2_;
               m_societySystem.addConValue(m_societySystemXML.Contribution[0].contriOfMoney[0].@idOfConValue);
               m_societyContriData.addOneNum_contriOfMoney();
               initShow2();
               _loc3_ = new SaveTaskInfo();
               _loc3_.type = "4399";
               _loc3_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc3_);
               MyFunction2.saveGame();
            }
            else
            {
               m_mySocietyPanel.showWarningBox("人物1元宝不足，无法进行元宝捐献。",0);
            }
         },m_mySocietyPanel.showWarningBox,true);
      }
      
      public function addFailConValueByMoney() : void
      {
         var _loc1_:int = int(m_societySystemXML.Contribution[0].contriOfMoney[0].@needMoney);
         GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + _loc1_;
         m_societyContriData.decOneNum_contriOfMoney();
         initShow2();
         var _loc2_:SaveTaskInfo = new SaveTaskInfo();
         _loc2_.type = "4399";
         _loc2_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc2_);
         MyFunction2.saveGame();
      }
      
      public function addConValueByTicket() : void
      {
         var price:int;
         var ticketId:String;
         var dataObj:Object;
         if(m_societyContriData.getrNum_contriOfTicket() <= 0)
         {
            m_mySocietyPanel.showWarningBox("点券捐献剩余次数为0, 不能捐献。",0);
            return;
         }
         price = int(m_societySystemXML.Contribution[0].contriOfTicket[0].@needTicket);
         ticketId = String(m_societySystemXML.Contribution[0].contriOfTicket[0].@needticketId);
         dataObj = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "帮会点券捐献";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_mySocietyPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_societyContriData.addOneNum_contriOfTicket();
            m_societySystem.addConValue(m_societySystemXML.Contribution[0].contriOfTicket[0].@idOfConValue);
            initShow2();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         },m_mySocietyPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      public function getContriOfExpBtn() : ButtonLogicShell2
      {
         return m_contriOfExpBtn;
      }
      
      public function getContriOfMoneyBtn() : ButtonLogicShell2
      {
         return m_contriOfMoneyBtn;
      }
      
      public function getContriOfTicketBtn() : ButtonLogicShell2
      {
         return m_contriOfTicketBtn;
      }
      
      private function initShow() : void
      {
         m_ableDragLS = new AbleDragSpriteLogicShell();
         m_ableDragLS.setShow(m_show);
         m_quitBtn = new ButtonLogicShell2();
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_rNumOfExpText = m_show["rNumOfExpText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_rNumOfExpText);
         m_rNumOfMoneyText = m_show["rNOfMoneyText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_rNumOfMoneyText);
         m_rNumOfTicketText = m_show["rNumOfTicketText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_rNumOfTicketText);
         m_contriOfExpBtn = new ButtonLogicShell2();
         m_contriOfExpBtn.setShow(m_show["contriOfExpBtn"]);
         m_contriOfMoneyBtn = new ButtonLogicShell2();
         m_contriOfMoneyBtn.setShow(m_show["contriOfMoneyBtn"]);
         m_contriOfTicketBtn = new ButtonLogicShell2();
         m_contriOfTicketBtn.setShow(m_show["contriOfTicketBtn"]);
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_societyContriData == null)
         {
            return;
         }
         m_rNumOfExpText.text = m_societyContriData.getrNum_contriOfExp().toString();
         m_rNumOfMoneyText.text = m_societyContriData.getrNum_contriOfMoney().toString();
         m_rNumOfTicketText.text = m_societyContriData.getrNum_contriOfTicket().toString();
      }
   }
}

