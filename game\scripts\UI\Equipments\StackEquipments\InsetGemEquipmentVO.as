package UI.Equipments.StackEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.ShiTu.PromoteValueObject;
   import YJFY.Utils.ClearUtil;
   
   public class InsetGemEquipmentVO extends StackEquipmentVO
   {
      public var showColor:uint;
      
      public var attributes:Vector.<String>;
      
      public var attributeValues:Vector.<PromoteValueObject>;
      
      public var descriptions:Vector.<String>;
      
      public var canInsetPos:String;
      
      public function InsetGemEquipmentVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(attributes);
         ClearUtil.nullArr(attributeValues);
         ClearUtil.nullArr(descriptions);
         attributes = null;
         attributeValues = null;
         descriptions = null;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:InsetGemEquipmentVO = new InsetGemEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as InsetGemEquipmentVO).showColor = this.showColor;
         (param1 as InsetGemEquipmentVO).canInsetPos = this.canInsetPos;
         var _loc3_:Vector.<String> = new Vector.<String>();
         _loc2_ = !!attributes ? attributes.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc3_.push(attributes[_loc6_]);
            _loc6_++;
         }
         (param1 as InsetGemEquipmentVO).attributes = _loc3_;
         var _loc5_:Vector.<PromoteValueObject> = new Vector.<PromoteValueObject>();
         _loc2_ = !!attributeValues ? attributeValues.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_.push(attributeValues[_loc6_].clone());
            _loc6_++;
         }
         (param1 as InsetGemEquipmentVO).attributeValues = _loc5_;
         var _loc4_:Vector.<String> = new Vector.<String>();
         _loc2_ = !!descriptions.length ? descriptions.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc4_.push(descriptions[_loc6_]);
            _loc6_++;
         }
         (param1 as InsetGemEquipmentVO).descriptions = _loc4_;
      }
   }
}

