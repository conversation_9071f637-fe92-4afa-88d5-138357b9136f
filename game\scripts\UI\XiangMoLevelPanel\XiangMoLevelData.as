package UI.XiangMoLevelPanel
{
   import YJFY.Utils.ClearUtil;
   
   public class XiangMoLevelData
   {
      private var m_fuBenDatas:Vector.<FuBenData>;
      
      public function XiangMoLevelData()
      {
         super();
         m_fuBenDatas = new Vector.<FuBenData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_fuBenDatas);
         m_fuBenDatas = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc3_:FuBenData = null;
         var _loc4_:XMLList = param1.fuBen;
         var _loc2_:int = int(!!_loc4_ ? _loc4_.length() : 0);
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = new FuBenData();
            _loc3_.initByXML(_loc4_[_loc5_]);
            m_fuBenDatas.push(_loc3_);
            _loc5_++;
         }
      }
      
      public function getFuBenDataNum() : uint
      {
         return m_fuBenDatas.length;
      }
      
      public function getFuBenDataByIndex(param1:int) : FuBenData
      {
         return m_fuBenDatas[param1];
      }
   }
}

