package UI.Farm.Bar
{
   import UI.MySprite;
   import UI.UIInterface.IBarMask;
   
   public class FarmLevelProgressBarMask extends MySprite implements IBarMask
   {
      public function FarmLevelProgressBarMask()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
      }
   }
}

