package UI.XiangMoLevelPanel
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class NanDuData extends DataManagerParent
   {
      private var m_levelXMLPath:String;
      
      private var m_description:String;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function NanDuData()
      {
         super();
         m_equipmentVOs = new Vector.<EquipmentVO>();
      }
      
      override public function clear() : void
      {
         m_levelXMLPath = null;
         m_description = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc4_:int = 0;
         levelXMLPath = String(param1.@levelXMLPath);
         description = String(param1.@description);
         var _loc3_:XMLList = param1.showEq;
         var _loc2_:int = int(!!_loc3_ ? _loc3_.length() : 0);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            m_equipmentVOs.push(XMLSingle.getEquipmentVOByID(_loc3_[_loc4_].@id,XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever(),false));
            _loc4_++;
         }
      }
      
      public function getEquipmentVONum() : uint
      {
         return m_equipmentVOs.length;
      }
      
      public function getEquipmentVOByIndex(param1:uint) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
      
      public function getDescription() : String
      {
         return description;
      }
      
      public function getLevelXMLPath() : String
      {
         return levelXMLPath;
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      private function set levelXMLPath(param1:String) : void
      {
         m_levelXMLPath = param1;
      }
      
      private function get levelXMLPath() : String
      {
         return m_levelXMLPath;
      }
      
      private function get description() : String
      {
         return m_description;
      }
      
      private function set description(param1:String) : void
      {
         m_description = param1;
      }
   }
}

