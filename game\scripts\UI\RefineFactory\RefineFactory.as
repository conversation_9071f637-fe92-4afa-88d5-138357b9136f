package UI.RefineFactory
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.QuitBtn;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipment;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RefineFactory.Btn.CompleteRefineAtOnceBtn;
   import UI.RefineFactory.Btn.RefineAttackDanSwitchBtn;
   import UI.RefineFactory.Btn.RefineDefenceDanSwitchBtn;
   import UI.RefineFactory.Btn.StartRefineBtn;
   import UI.RefineFactory.LianDanFurnace.LianDanFurnace;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.clearInterval;
   import flash.utils.setTimeout;
   
   public class RefineFactory extends MySprite
   {
      public static const NUM_ONE_PAGE:int = 5;
      
      public static const START_NUM:int = 1;
      
      public static const ATTACK_DAN_STATE:int = 0;
      
      public static const DEFENCE_DAN_STATE:int = 1;
      
      public var switchAttackDanBtn:RefineAttackDanSwitchBtn;
      
      public var switchDefenceDanBtn:RefineDefenceDanSwitchBtn;
      
      public var refineDataArea:RefineDataArea;
      
      public var messageText:TextField;
      
      public var quitBtn:QuitBtn;
      
      private const COLOUME_START_X:Number = 43;
      
      private const COLOUME_START_Y:Number = 153;
      
      private var _currentSelectedIndex:int;
      
      private var _pageBtnGroup:PageBtnGroup;
      
      private var _currentState:int;
      
      private var _currentSelectedEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _attackEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _defenceEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _intervalID:uint = 0;
      
      private var _startRefineBtn:StartRefineBtn;
      
      private var _completeRefineBtnAtOnce:CompleteRefineAtOnceBtn;
      
      private var _lianDanFurnace:LianDanFurnace;
      
      public function RefineFactory()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(switchAttackDanBtn)
         {
            switchAttackDanBtn.clear();
         }
         switchAttackDanBtn = null;
         if(switchDefenceDanBtn)
         {
            switchDefenceDanBtn.clear();
         }
         switchDefenceDanBtn = null;
         if(refineDataArea)
         {
            refineDataArea.clear();
         }
         refineDataArea = null;
         messageText = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_pageBtnGroup)
         {
            _pageBtnGroup.clear();
         }
         _pageBtnGroup = null;
         _currentSelectedEquipmentVOs = null;
         if(_attackEquipmentVOs)
         {
            _loc1_ = int(_attackEquipmentVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_attackEquipmentVOs[_loc3_])
               {
                  _attackEquipmentVOs[_loc3_].clear();
               }
               _attackEquipmentVOs[_loc3_] = null;
               _loc3_++;
            }
            _attackEquipmentVOs = null;
         }
         if(_defenceEquipmentVOs)
         {
            _loc1_ = int(_defenceEquipmentVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_defenceEquipmentVOs[_loc3_])
               {
                  _defenceEquipmentVOs[_loc3_].clear();
               }
               _defenceEquipmentVOs[_loc3_] = null;
               _loc3_++;
            }
            _defenceEquipmentVOs = null;
         }
         if(_startRefineBtn)
         {
            _startRefineBtn.clear();
         }
         _startRefineBtn = null;
         if(_completeRefineBtnAtOnce)
         {
            _completeRefineBtnAtOnce.clear();
         }
         _completeRefineBtnAtOnce = null;
         if(_lianDanFurnace)
         {
            _lianDanFurnace.clear();
         }
         _lianDanFurnace = null;
         clearInterval(_intervalID);
         super.clear();
      }
      
      public function set currentState(param1:int) : void
      {
         switch(param1)
         {
            case 0:
               _currentState = 0;
               _currentSelectedEquipmentVOs = _attackEquipmentVOs;
               break;
            default:
               _currentState = 1;
               _currentSelectedEquipmentVOs = _defenceEquipmentVOs;
         }
      }
      
      public function showWarningBox(param1:String, param2:int = 0, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().x = (stage.stageWidth - WarningBoxSingle.getInstance().width) / 2;
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function setPanelPage(param1:int) : void
      {
         var _loc2_:int = 0;
         _loc2_ = int(_currentSelectedEquipmentVOs.length);
         if(!_loc2_)
         {
            _pageBtnGroup.initPageNumber(1,1);
            return;
         }
         if(_loc2_ % 5)
         {
            _pageBtnGroup.initPageNumber(param1,int(_loc2_ / 5) + 1);
         }
         else
         {
            _pageBtnGroup.initPageNumber(param1,_loc2_ / 5);
         }
      }
      
      private function arrange(param1:int) : void
      {
         var _loc5_:int = 0;
         var _loc9_:* = 0;
         var _loc8_:RefineFactoryColume = null;
         var _loc3_:UnOpenRefineFactoryColume = null;
         var _loc7_:DisplayObject = null;
         var _loc2_:int = param1 + 5;
         _loc9_ = 0;
         while(_loc9_ < numChildren)
         {
            _loc7_ = getChildAt(_loc9_);
            if(_loc7_ is RefineFactoryColume || _loc7_ is UnOpenRefineFactoryColume)
            {
               removeChildAt(_loc9_);
               _loc7_["clear"]();
               _loc9_--;
            }
            _loc9_++;
         }
         _loc5_ = int(_currentSelectedEquipmentVOs.length);
         var _loc4_:XML = XMLSingle.getInstance().equipmentXML;
         _loc9_ = param1;
         while(_loc9_ < _loc2_ && _loc9_ < _loc5_)
         {
            if(_currentSelectedEquipmentVOs[_loc9_])
            {
               _loc8_ = new RefineFactoryColume();
               _loc8_.x = 43;
               _loc8_.y = 153 + 60 * (_loc9_ - param1);
               _loc8_.equipmentVO = _currentSelectedEquipmentVOs[_loc9_].clone();
               addChild(_loc8_);
            }
            else
            {
               _loc3_ = new UnOpenRefineFactoryColume();
               _loc3_.x = 43;
               _loc3_.y = 153 + 60 * (_loc9_ - param1);
               addChild(_loc3_);
            }
            _loc9_++;
         }
         var _loc6_:int = 0;
         _loc5_ = numChildren;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc7_ = getChildAt(_loc9_);
            if(_loc7_ is RefineFactoryColume)
            {
               if(!_loc6_)
               {
                  (_loc7_ as RefineFactoryColume).init(false);
               }
               else
               {
                  (_loc7_ as RefineFactoryColume).init(true);
               }
               _loc6_++;
            }
            _loc9_++;
         }
         refreshData();
      }
      
      private function init() : void
      {
         _pageBtnGroup = new PageBtnGroup();
         _pageBtnGroup.x = 150;
         _pageBtnGroup.y = 475;
         addChild(_pageBtnGroup);
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "refineFactoryQuitBtn";
         switchAttackDanBtn.init(false);
         switchDefenceDanBtn.init(true);
         MyFunction2.loadXMLAndGetServerTimeFunction("RefineFactory",onComplete,showWarningBox,true);
         messageText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16777215);
         messageText.selectable = false;
         messageText.multiline = true;
      }
      
      private function onComplete(param1:XML, param2:String) : void
      {
         var _loc6_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc3_:XML = XMLSingle.getInstance().skillXML;
         var _loc5_:XML = XMLSingle.getInstance().talentXML;
         var _loc7_:XML = XMLSingle.getInstance().farmXML;
         _attackEquipmentVOs = InitUI.getInstance().getEquipmentVOs(param1.AttackDan[0],_loc6_,_loc3_,_loc5_,"");
         _defenceEquipmentVOs = InitUI.getInstance().getEquipmentVOs(param1.DefenceDan[0],_loc6_,_loc3_,_loc5_,"");
         currentState = 0;
         setPanelPage(1);
         arrange((_pageBtnGroup.pageNum - 1) * 5);
         _lianDanFurnace = new LianDanFurnace(RefineFactoryData.getInstance().lianDanFurnace,param2,_loc6_,_loc7_);
         _lianDanFurnace.x = 508;
         _lianDanFurnace.y = 375;
         addChild(_lianDanFurnace);
         _lianDanFurnace.lianDanFurnaceDrive.addEventListener("changeRemainTime",changeRemainTime,false,0,true);
         _lianDanFurnace.lianDanFurnaceDrive.addEventListener("changeState",changeState,false,0,true);
         switch(RefineFactoryData.getInstance().lianDanFurnace.state)
         {
            case 0:
               showMiddlePartWhenIdleState();
               break;
            case 1:
               initRefineDataArea(false);
               showMiddlePartWhenRefining("炼制中...   " + MyFunction2.transformTimeToString(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.remainTime));
               break;
            case 2:
               initRefineDataArea(false);
               showMiddlePartWhenComplete("已炼制完成！\n点击炼丹炉获得丹药");
               break;
            default:
               throw new Error("状态类型错误！");
         }
         var _loc4_:String = MyFunction.getInstance().splitTimeString(param2);
         if(RefineFactoryData.getInstance().atOnceCompleteDate != _loc4_)
         {
            RefineFactoryData.getInstance().atOnceCompleteDate = _loc4_;
            RefineFactoryData.getInstance().atOnceCompleteNum = 0;
         }
      }
      
      private function clearMiddlePart() : void
      {
         if(Boolean(_startRefineBtn) && getChildByName(_startRefineBtn.name))
         {
            removeChild(_startRefineBtn);
         }
         if(_startRefineBtn)
         {
            _startRefineBtn = null;
         }
         if(Boolean(_completeRefineBtnAtOnce) && getChildByName(_completeRefineBtnAtOnce.name))
         {
            removeChild(_completeRefineBtnAtOnce);
         }
         if(_completeRefineBtnAtOnce)
         {
            _completeRefineBtnAtOnce = null;
         }
      }
      
      private function showMiddlePartWhenRefining(param1:String) : void
      {
         clearMiddlePart();
         if(!_completeRefineBtnAtOnce)
         {
            _completeRefineBtnAtOnce = new CompleteRefineAtOnceBtn();
         }
         _completeRefineBtnAtOnce.x = 407;
         _completeRefineBtnAtOnce.y = 450;
         if(!getChildByName(_completeRefineBtnAtOnce.name))
         {
            addChild(_completeRefineBtnAtOnce);
         }
         messageText.text = param1;
      }
      
      private function showMiddlePartWhenIdleState() : void
      {
         clearMiddlePart();
         messageText.text = "";
         if(!_startRefineBtn)
         {
            _startRefineBtn = new StartRefineBtn();
         }
         _startRefineBtn.x = 407;
         _startRefineBtn.y = 450;
         if(!getChildByName(_startRefineBtn.name))
         {
            addChild(_startRefineBtn);
         }
      }
      
      private function showMiddlePartWhenComplete(param1:String) : void
      {
         clearMiddlePart();
         messageText.text = param1;
      }
      
      private function initRefineDataArea(param1:Boolean) : void
      {
         var _loc2_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc4_:Equipment = new StackEquipment(XMLSingle.getEquipment(RefineFactoryData.getInstance().materialID,_loc2_) as StackEquipmentVO);
         (_loc4_.equipmentVO as StackEquipmentVO).num = 1;
         _loc4_.equipmentVO = _loc4_.equipmentVO;
         var _loc3_:Equipment = new Equipment(XMLSingle.getEquipment(RefineFactoryData.getInstance().refineTargetID,_loc2_));
         setRefineDataArea(_loc4_,RefineFactoryData.getInstance().materialNum,RefineFactoryData.getInstance().materialNum,RefineFactoryData.getInstance().materialNum,_loc3_,param1,_loc2_);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchToRefineAttackDan",switchToAttackDan,true,0,true);
         addEventListener("switchToRefineDefenceDan",switchToDefenceDan,true,0,true);
         addEventListener("clickStartRefineBtn",startRefine,true,0,true);
         addEventListener("clickCompleteRefineAtOnceBtn",atOnceCompleteRefine,true,0,true);
         addEventListener("clickLianDanFurnace",clickLianDanFurnace,true,0,true);
         addEventListener("switchRefineFactoryColume",switchColume,true,0,true);
         addEventListener("switchRefineFactoryColume",refreshData,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchToRefineAttackDan",switchToAttackDan,true);
         removeEventListener("switchToRefineDefenceDan",switchToDefenceDan,true);
         removeEventListener("clickStartRefineBtn",startRefine,true);
         removeEventListener("clickCompleteRefineAtOnceBtn",atOnceCompleteRefine,true);
         removeEventListener("clickLianDanFurnace",clickLianDanFurnace,true);
         removeEventListener("switchRefineFactoryColume",switchColume,true);
         removeEventListener("switchRefineFactoryColume",refreshData,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("warningBox",sureOrCancel,true);
         if(Boolean(_lianDanFurnace) && _lianDanFurnace.lianDanFurnaceDrive)
         {
            _lianDanFurnace.lianDanFurnaceDrive.removeEventListener("changeRemainTime",changeRemainTime,false);
         }
         if(Boolean(_lianDanFurnace) && _lianDanFurnace.lianDanFurnaceDrive)
         {
            _lianDanFurnace.lianDanFurnaceDrive.removeEventListener("changeState",changeState,false);
         }
      }
      
      private function atOnceCompleteRefine(param1:UIBtnEvent) : void
      {
         var _loc4_:Vector.<int> = MyFunction.getInstance().excreteString(String(XMLSingle.getInstance().equipmentXML.item.(@id == RefineFactoryData.getInstance().refineTargetID)[0].refineData.(@material == RefineFactoryData.getInstance().materialID)[0].@atOnceCompleteRefinePrice));
         var _loc3_:Vector.<int> = MyFunction.getInstance().excreteString(String(XMLSingle.getInstance().farmXML.lianDanFurnace.(@id == RefineFactoryData.getInstance().lianDanFurnace.id)[0].@atOnceCompleteRefinePriceValue));
         var _loc2_:Number = _loc4_[Math.min(RefineFactoryData.getInstance().atOnceCompleteNum,_loc4_.length - 1)] * _loc3_[Math.min(RefineFactoryData.getInstance().atOnceCompleteNum,_loc3_.length - 1)];
         showWarningBox("是否花费<font size=\'25\' color=\'#ff0000\'>" + _loc2_ + "</font>点券立即完成炼制！",3,{
            "type":"atOnceCompleteRefine",
            "buyPrice":_loc2_
         });
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var price:Number;
         var e:UIPassiveEvent = param1;
         if(e.data.detail == 1 && e.data.task.type == "startRefine")
         {
            if(!RefineFactoryData.getInstance().materialNum)
            {
               showWarningBox("材料数量为0， 不能炼制！");
               return;
            }
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               var _loc6_:int = 0;
               var _loc4_:XML = XMLSingle.getInstance().equipmentXML;
               var _loc5_:Number = RefineFactoryFunction.getRefineRate(_loc4_,XMLSingle.getInstance().farmXML,RefineFactoryData.getInstance().lianDanFurnace.id,RefineFactoryData.getInstance().refineTargetID,RefineFactoryData.getInstance().materialID,RefineFactoryData.getInstance().materialNum);
               var _loc3_:Number = Math.random();
               if(_loc3_ < _loc5_ - int(_loc5_))
               {
                  _loc6_ = Math.ceil(_loc5_);
               }
               else
               {
                  _loc6_ = Math.floor(_loc5_);
               }
               if(_lianDanFurnace)
               {
                  _lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.date = param1;
                  _lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.state = 1;
                  _lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.remainTime = RefineFactoryFunction.setRefineStateByTimeStr(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO,param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML)[0];
                  initRefineDataArea(false);
                  showMiddlePartWhenRefining("炼制中...   " + MyFunction2.transformTimeToString(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.remainTime));
                  _lianDanFurnace.showOrdinayToRefineAnimation(funAfterAnimation);
               }
               else
               {
                  RefineFactoryData.getInstance().lianDanFurnace.date = param1;
                  RefineFactoryData.getInstance().lianDanFurnace.state = 1;
               }
               RefineFactoryData.getInstance().refineTargetNum = _loc6_;
               MyFunction.getInstance().minusEquipmentVOs(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs,RefineFactoryData.getInstance().materialNum,XMLSingle.getEquipment(RefineFactoryData.getInstance().materialID,XMLSingle.getInstance().equipmentXML).id);
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },null,false);
         }
         if(e.data.detail == 1 && e.data.task.type == "atOnceCompleteRefine")
         {
            price = Number(e.data.task.buyPrice);
            AnalogServiceHoldFunction.getInstance().buyByPointTicketFun(price,function():void
            {
               _lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.date = "";
               _lianDanFurnace.lianDanFurnaceDrive.changeLianDanFurnaceVOState(2);
               RefineFactoryData.getInstance().atOnceCompleteNum++;
               initRefineDataArea(false);
               showMiddlePartWhenComplete("已炼制完成！\n点击炼丹炉获得丹药");
               var _loc1_:SaveTaskInfo = new SaveTaskInfo();
               _loc1_.type = "4399";
               _loc1_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc1_);
               MyFunction2.saveGame();
            },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         else if(e.data.detail == 1)
         {
            if(e.data.task.hasOwnProperty("fun") && Boolean(e.data.task.fun))
            {
               e.data.task.fun.apply(null,e.data.task.funParams);
            }
         }
      }
      
      private function changeState(param1:Event) : void
      {
         switch(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.state)
         {
            case 0:
               initRefineDataArea(true);
               showMiddlePartWhenIdleState();
               break;
            case 1:
               initRefineDataArea(false);
               showMiddlePartWhenRefining("炼制中...   " + MyFunction2.transformTimeToString(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.remainTime));
               break;
            case 2:
               initRefineDataArea(false);
               showMiddlePartWhenComplete("已炼制完成！\n点击炼丹炉获得丹药");
               break;
            default:
               throw new Error("状态类型错误！");
         }
      }
      
      private function changeRemainTime(param1:Event) : void
      {
         messageText.text = "炼制中...   " + MyFunction2.transformTimeToString(_lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.remainTime);
      }
      
      private function clickLianDanFurnace(param1:UIPassiveEvent) : void
      {
         var eqXML:XML;
         var getNum:int;
         var i:int;
         var newEquipmentVOs:Vector.<EquipmentVO>;
         var e:UIPassiveEvent = param1;
         if(RefineFactoryData.getInstance().lianDanFurnace.state == 2)
         {
            eqXML = XMLSingle.getInstance().equipmentXML;
            getNum = RefineFactoryData.getInstance().refineTargetNum;
            i = 0;
            newEquipmentVOs = new Vector.<EquipmentVO>();
            i = 0;
            while(i < getNum)
            {
               newEquipmentVOs.push(XMLSingle.getEquipment(RefineFactoryData.getInstance().refineTargetID,eqXML));
               i++;
            }
            MyFunction2.addEquipmentVOs(newEquipmentVOs,GamingUI.getInstance().player1,!!_lianDanFurnace ? showWarningBox : null,function(param1:Function, param2:String):void
            {
               if(_lianDanFurnace)
               {
                  _lianDanFurnace.lianDanFurnaceDrive.lianDanFurnaceVO.date = "";
                  _lianDanFurnace.lianDanFurnaceDrive.changeLianDanFurnaceVOState(0);
                  showMiddlePartWhenIdleState();
                  refreshData();
                  param1(param2,0);
               }
               else
               {
                  RefineFactoryData.getInstance().lianDanFurnace.date = "";
                  RefineFactoryData.getInstance().lianDanFurnace.state = 0;
               }
               var _loc3_:SaveTaskInfo = new SaveTaskInfo();
               _loc3_.type = "4399";
               _loc3_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc3_);
               MyFunction2.saveGame();
            },["玩家1背包已满！"],[showWarningBox,!!newEquipmentVOs.length ? "炼制成功！， 您获得了" + getNum + "个" + newEquipmentVOs[0].name + "!" : "炼制失败！"]);
         }
      }
      
      private function startRefine(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         if(!RefineFactoryData.getInstance().materialNum)
         {
            showWarningBox("材料数量为0， 不能炼制！");
            return;
         }
         if(!Math.floor(RefineFactoryFunction.getRefineRate(XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML,RefineFactoryData.getInstance().lianDanFurnace.id,RefineFactoryData.getInstance().refineTargetID,RefineFactoryData.getInstance().materialID,RefineFactoryData.getInstance().materialNum)))
         {
            showWarningBox("现在炼制结果为“0-1“，炼制可能失败，是否继续！",3,{
               "type":"startRefine2",
               "fun":function():void
               {
                  setTimeout(showWarningBox,100,"炼制需要<font size=\'24\' color=\'#ffff00\'>" + MyFunction2.transformTimeToString(RefineFactoryFunction.getRefineTime(XMLSingle.getInstance().farmXML,XMLSingle.getInstance().equipmentXML)) + "</font>, 是否开始炼制！",3,{"type":"startRefine"});
               },
               "funParams":[]
            });
            return;
         }
         showWarningBox("炼制需要<font size=\'24\' color=\'#ffff00\'>" + MyFunction2.transformTimeToString(RefineFactoryFunction.getRefineTime(XMLSingle.getInstance().farmXML,XMLSingle.getInstance().equipmentXML)) + "</font>, 是否开始炼制！",3,{"type":"startRefine"});
      }
      
      private function funAfterAnimation(param1:Event) : void
      {
         if(_lianDanFurnace && _lianDanFurnace.lianDanFurnaceDrive)
         {
            _lianDanFurnace.lianDanFurnaceDrive.changeLianDanFurnaceVOState(1);
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function switchToAttackDan(param1:UIBtnEvent) : void
      {
         switchDefenceDanBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(switchAttackDanBtn),getChildIndex(switchDefenceDanBtn));
         setChildIndex(switchDefenceDanBtn,_loc2_);
         currentState = 0;
         setPanelPage(1);
         arrange((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function switchToDefenceDan(param1:UIBtnEvent) : void
      {
         switchAttackDanBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(switchAttackDanBtn),getChildIndex(switchDefenceDanBtn));
         setChildIndex(switchAttackDanBtn,_loc2_);
         currentState = 1;
         setPanelPage(1);
         arrange((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function switchColume(param1:UIBtnEvent) : void
      {
         var _loc3_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc2_:int = numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = getChildAt(_loc4_);
            if(_loc3_ is RefineFactoryColume && _loc3_ != param1.target)
            {
               (_loc3_ as RefineFactoryColume).gotoTwoFrame();
            }
            _loc4_++;
         }
      }
      
      private function pageUp(param1:UIBtnEvent) : void
      {
         arrange((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function pageDown(param1:UIBtnEvent) : void
      {
         arrange((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function refreshData(param1:UIBtnEvent = null) : void
      {
         var _loc2_:EquipmentVO = null;
         var _loc4_:Equipment = null;
         var _loc3_:XML = null;
         if(RefineFactoryData.getInstance().lianDanFurnace.state == 0)
         {
            if(param1)
            {
               _loc2_ = param1.data.clone();
            }
            else if(_currentSelectedEquipmentVOs.length && _currentSelectedEquipmentVOs[(_pageBtnGroup.pageNum - 1) * 5])
            {
               _loc2_ = _currentSelectedEquipmentVOs[(_pageBtnGroup.pageNum - 1) * 5].clone();
            }
            if(_loc2_)
            {
               _loc3_ = XMLSingle.getInstance().equipmentXML;
               _loc4_ = MyFunction2.sheatheEquipmentShell(RefineFactoryFunction.getMaterialIDByRefineEquipmentID(_loc2_.id,_loc3_));
               setRefineDataArea(_loc4_,0,MyFunction2.getEquimentVOMaxNumInEquipmentVOs(_loc4_.equipmentVO,GamingUI.getInstance().player1.playerVO.packageEquipmentVOs),RefineFactoryFunction.getCanPutMaxMaterialNum(_loc3_,XMLSingle.getInstance().farmXML,RefineFactoryData.getInstance().lianDanFurnace.id,_loc2_.id,_loc4_.equipmentVO.id,MyFunction2.getEquipmentVOsEnbleContainEquipmenVOtMaxNum(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs,_loc2_)),MyFunction2.sheatheEquipmentShell(_loc2_),true,_loc3_);
            }
         }
      }
      
      private function setRefineDataArea(param1:Equipment, param2:int, param3:int, param4:int, param5:Equipment, param6:Boolean, param7:XML) : void
      {
         refineDataArea.drawRefineDataArea(param1,param2,param3,param4,param5,param6,param7,showWarningBox);
      }
   }
}

