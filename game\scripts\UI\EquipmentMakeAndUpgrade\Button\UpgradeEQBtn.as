package UI.EquipmentMakeAndUpgrade.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class UpgradeEQBtn extends Btn
   {
      public function UpgradeEQBtn()
      {
         super();
         setTipString("开始装备升级");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickUpgradeEquipmentBtn"));
      }
   }
}

