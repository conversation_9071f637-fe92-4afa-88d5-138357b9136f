package UI.PetAdvancePanel
{
   import UI.AbleDragSprite;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.Skills.Skill;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class UsePetSkillBookPanel extends AbleDragSprite
   {
      private var _show:MovieClip;
      
      private var _oldSkillContainer:Sprite;
      
      private var _newSkillContainer:Sprite;
      
      private var _changeBtn:ButtonLogicShell2;
      
      private var _quitBtn:ButtonLogicShell2;
      
      private var _oldSkill:Skill;
      
      private var _newSkill:Skill;
      
      private var _funAfterChange:Function;
      
      private var _funParamsAfterChange:Array;
      
      private var _funAfterQuit:Function;
      
      private var _funParamsAfterQuit:Array;
      
      private var _wantLoadSources:Array = ["usePetSkillBookPanel"];
      
      public function UsePetSkillBookPanel()
      {
         super();
      }
      
      override public function clear() : void
      {
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         super.clear();
         _show = null;
         _oldSkillContainer = null;
         _newSkillContainer = null;
         if(_changeBtn)
         {
            _changeBtn.clear();
         }
         _changeBtn = null;
         if(_quitBtn)
         {
            _quitBtn.clear();
         }
         _quitBtn = null;
         if(_oldSkill)
         {
            _oldSkill.clear();
         }
         _oldSkill = null;
         if(_newSkill)
         {
            _newSkill.clear();
         }
         _newSkill = null;
         _funAfterChange = null;
         ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
         _funParamsAfterChange = null;
         _funAfterQuit = null;
         ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
         _funParamsAfterQuit = null;
      }
      
      public function init2(param1:Skill, param2:Skill, param3:Function, param4:Array, param5:Function, param6:Array) : void
      {
         var loadFinishListenr:LoadFinishListener1;
         var oldSkill:Skill = param1;
         var newSkill:Skill = param2;
         var funAfterChange:Function = param3;
         var funParamsAfterChange:Array = param4;
         var funAfterQuit:Function = param5;
         var funParamsAfterQuit:Array = param6;
         if(Boolean(_oldSkill) && (_oldSkill as DisplayObject).parent)
         {
            (_oldSkill as DisplayObject).parent.removeChild(_oldSkill as DisplayObject);
         }
         if(Boolean(_newSkill) && (_newSkill as DisplayObject).parent)
         {
            (_newSkill as DisplayObject).parent.removeChild(_newSkill as DisplayObject);
         }
         if(_oldSkill)
         {
            _oldSkill.clear();
         }
         if(_newSkill)
         {
            _newSkill.clear();
         }
         _funAfterChange = null;
         ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
         _funAfterQuit = null;
         ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
         _oldSkill = oldSkill;
         _newSkill = newSkill;
         _oldSkill.addEventListener("mouseOver",onOver,false,0,true);
         _oldSkill.addEventListener("mouseOut",onOut,false,0,true);
         _newSkill.addEventListener("mouseOver",onOver,false,0,true);
         _newSkill.addEventListener("mouseOut",onOut,false,0,true);
         _funAfterChange = funAfterChange;
         _funParamsAfterChange = funParamsAfterChange;
         _funAfterQuit = funAfterQuit;
         _funParamsAfterQuit = funParamsAfterQuit;
         loadFinishListenr = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("UsePetSkillBookPanel") as MovieClip;
            }
            if(_show.parent == null)
            {
               if(stage)
               {
                  _show.x = (stage.stageWidth - _show.width) / 2;
                  _show.y = (stage.stageHeight - _show.height) / 2;
               }
               addChild(_show);
            }
            _oldSkillContainer = _show["oldSkillContainer"];
            _newSkillContainer = _show["newSkillContainer"];
            if(_changeBtn == null)
            {
               _changeBtn = new ButtonLogicShell2();
               _changeBtn.setShow(_show["changeBtn"]);
               _changeBtn.setTipString("点击使用宠物技能书");
            }
            if(_quitBtn == null)
            {
               _quitBtn = new ButtonLogicShell2();
               _quitBtn.setShow(_show["quitBtn3"]);
               _quitBtn.setTipString("点击关闭");
            }
            _oldSkillContainer.addChild(_oldSkill as DisplayObject);
            _newSkillContainer.addChild(_newSkill as DisplayObject);
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListenr);
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _changeBtn:
            case _quitBtn:
               switch(param1.button)
               {
                  case _changeBtn:
                     if(Boolean(_funAfterChange))
                     {
                        _funAfterChange.apply(null,_funParamsAfterChange);
                     }
                     _funAfterChange = null;
                     ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
                     break;
                  case _quitBtn:
                     if(Boolean(_funAfterQuit))
                     {
                        _funAfterQuit.apply(null,_funParamsAfterQuit);
                     }
                     _funAfterQuit = null;
                     ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
               }
               if(parent)
               {
                  parent.removeChild(this);
               }
               clear();
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

