package UI.RecaptureGold
{
   public class RGPlayerVO
   {
      private var _physicalStrength:int;
      
      public var GoldNum:uint;
      
      public function RGPlayerVO()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function set physicalStrength(param1:int) : void
      {
         if(param1 < 0)
         {
            param1 = 1;
         }
         _physicalStrength = param1;
      }
      
      public function get physicalStrength() : int
      {
         return _physicalStrength;
      }
   }
}

