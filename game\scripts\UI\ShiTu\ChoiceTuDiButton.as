package UI.ShiTu
{
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import flash.display.InteractiveObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class ChoiceTuDiButton implements IButton
   {
      public var show:InteractiveObject;
      
      private var _isLock:Boolean;
      
      private var _isPlaying:Boolean;
      
      private var _tartgetMovieClip:MovieClipPlayLogicShell;
      
      public function ChoiceTuDiButton(param1:InteractiveObject)
      {
         super();
         MovieClip(param1).gotoAndStop(1);
         this.show = param1;
         (this.show as MovieClip).buttonMode = true;
         this.show.addEventListener("click",onClick,false,0,true);
         this.show.addEventListener("click",onClick,true,0,true);
         this.show.addEventListener("rollOver",onOver,false,0,true);
         this.show.addEventListener("rollOut",onOut,false,0,true);
      }
      
      public function clear() : void
      {
         if(_tartgetMovieClip)
         {
            _tartgetMovieClip.clear();
         }
         _tartgetMovieClip = null;
         show.removeEventListener("click",onClick,false);
         show.removeEventListener("click",onClick,true);
         show.removeEventListener("rollOut",onOut,false);
         show.removeEventListener("rollOver",onOver,false);
         show = null;
      }
      
      public function lock(param1:int) : void
      {
         _isLock = true;
         try
         {
            MovieClip(show).gotoAndStop("lock" + (param1 + 1));
         }
         catch(error:ArgumentError)
         {
            MovieClip(show).gotoAndStop(4 + param1);
         }
         clearOldTarget();
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         if(_isLock)
         {
            return;
         }
         show.dispatchEvent(new ButtonEvent("clickButton",this));
         clearOldTarget();
      }
      
      private function createNewTarget(param1:Function = null, param2:Array = null, param3:Function = null, param4:Array = null) : void
      {
         clearOldTarget();
         _tartgetMovieClip = new MovieClipPlayLogicShell();
         _tartgetMovieClip.setShow(show["target"]);
         _tartgetMovieClip.gotoAndPlay("playStart",param1,param2,param3,param4);
      }
      
      private function clearOldTarget() : void
      {
         if(_tartgetMovieClip)
         {
            _tartgetMovieClip.clear();
         }
         _isPlaying = false;
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         if(_isLock)
         {
            return;
         }
         if(_isPlaying)
         {
            return;
         }
         try
         {
            MovieClip(show).gotoAndStop("out");
         }
         catch(error:Error)
         {
            MovieClip(show).gotoAndStop(1);
         }
         clearOldTarget();
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var e:MouseEvent = param1;
         if(_isLock)
         {
            return;
         }
         if(_isPlaying)
         {
            return;
         }
         try
         {
            MovieClip(show).gotoAndStop("over" + Math.ceil(2 * Math.random()));
         }
         catch(error:Error)
         {
            MovieClip(show).gotoAndStop(2);
         }
         createNewTarget(function():void
         {
            _isPlaying = true;
         },null,function():void
         {
            _isPlaying = false;
            onOver(null);
         },null);
      }
   }
}

