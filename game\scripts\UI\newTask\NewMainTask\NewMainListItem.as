package UI.newTask.NewMainTask
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.newTask.NewTaskPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class NewMainListItem
   {
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_newmaintaskpanel:NewMainTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_newmainlist:NewMainList;
      
      private var m_data:MainLineTaskVO;
      
      private var m_item:Sprite;
      
      private var m_index:int;
      
      private var m_level:int;
      
      private var m_id:String;
      
      private var m_workcheck:ButtonLogicShell2;
      
      private var m_worknocheck:ButtonLogicShell2;
      
      private var m_donebtn:ButtonLogicShell2;
      
      private var m_noopenbtn:ButtonLogicShell2;
      
      private var m_txtName1:TextField;
      
      private var m_txtName2:TextField;
      
      private var m_txtName3:TextField;
      
      private var m_txtName4:TextField;
      
      private var m_checked:MovieClip;
      
      private var m_lingqutexiao:MovieClip;
      
      public function NewMainListItem()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_workcheck);
         m_workcheck = null;
         ClearUtil.clearObject(m_worknocheck);
         m_worknocheck = null;
         ClearUtil.clearObject(m_donebtn);
         m_donebtn = null;
         ClearUtil.clearObject(m_noopenbtn);
         m_noopenbtn = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewMainTaskPanel, param4:NewMainList, param5:MainLineTaskVO, param6:Sprite, param7:int, param8:int, param9:String) : void
      {
         m_newtaskpanel = param1;
         m_show = param2;
         m_newmaintaskpanel = param3;
         m_newmainlist = param4;
         m_data = param5;
         m_item = param6;
         m_index = param7 + 1;
         m_level = param8;
         m_id = param9;
         initParams();
         initShow();
         refreshInfo();
      }
      
      public function getShow() : Sprite
      {
         return m_item;
      }
      
      public function getData() : MainLineTaskVO
      {
         return m_data;
      }
      
      public function getID() : String
      {
         return m_id;
      }
      
      private function initParams() : void
      {
         m_workcheck = new ButtonLogicShell2();
         m_worknocheck = new ButtonLogicShell2();
         m_donebtn = new ButtonLogicShell2();
         m_noopenbtn = new ButtonLogicShell2();
      }
      
      public function initShow() : void
      {
         m_lingqutexiao = m_item["lingqutexiao"];
         m_checked = m_item["btnchecked"];
         m_workcheck.setShow(m_item["btnworkingchecked"]);
         m_worknocheck.setShow(m_item["btnnoworkingnochecked"]);
         m_donebtn.setShow(m_item["btndone"]);
         m_noopenbtn.setShow(m_item["btnnoopen"]);
         m_workcheck.setTipString("点击查看详情");
         m_worknocheck.setTipString("点击查看详情");
         m_donebtn.setTipString("点击查看详情");
         m_noopenbtn.setTipString("点击查看详情");
         m_txtName1 = m_workcheck.getShow()["txtname"];
         m_txtName2 = m_worknocheck.getShow()["txtname"];
         m_txtName3 = m_donebtn.getShow()["txtname"];
         m_txtName4 = m_noopenbtn.getShow()["txtname"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName1,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName2,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName3,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName4,true);
      }
      
      public function refreshInfo() : void
      {
         m_txtName1.text = "任务" + String(m_index) + "(等级" + m_level + "以上)";
         m_txtName2.text = "任务" + String(m_index) + "(等级" + m_level + "以上)";
         m_txtName3.text = "任务" + String(m_index) + "(等级" + m_level + "以上)";
         m_txtName4.text = "任务" + String(m_index) + "(等级" + m_level + "以上)";
         m_txtName1.mouseEnabled = false;
         m_txtName2.mouseEnabled = false;
         m_txtName3.mouseEnabled = false;
         m_txtName4.mouseEnabled = false;
         if(m_data.isGotReward)
         {
            m_lingqutexiao.visible = false;
         }
         else if(m_data.isWorking)
         {
            if(m_data.isFinish)
            {
               m_lingqutexiao.visible = true;
            }
            else
            {
               m_lingqutexiao.visible = false;
            }
         }
         else
         {
            m_lingqutexiao.visible = false;
         }
      }
      
      public function checkCurrent(param1:String) : void
      {
         if(param1 == m_id)
         {
            m_checked.visible = false;
            if(m_data.isGotReward)
            {
               m_workcheck.getShow().visible = false;
               m_worknocheck.getShow().visible = false;
               m_donebtn.getShow().visible = true;
               m_noopenbtn.getShow().visible = false;
            }
            else if(m_data.isWorking)
            {
               m_workcheck.getShow().visible = true;
               m_worknocheck.getShow().visible = false;
               m_donebtn.getShow().visible = false;
               m_noopenbtn.getShow().visible = false;
            }
            else
            {
               m_workcheck.getShow().visible = false;
               m_worknocheck.getShow().visible = false;
               m_donebtn.getShow().visible = false;
               m_noopenbtn.getShow().visible = true;
            }
         }
         else
         {
            m_checked.visible = false;
            if(m_data.isGotReward)
            {
               m_workcheck.getShow().visible = false;
               m_worknocheck.getShow().visible = false;
               m_donebtn.getShow().visible = true;
               m_noopenbtn.getShow().visible = false;
            }
            else if(m_data.isWorking)
            {
               m_workcheck.getShow().visible = false;
               m_worknocheck.getShow().visible = true;
               m_donebtn.getShow().visible = false;
               m_noopenbtn.getShow().visible = false;
            }
            else
            {
               m_workcheck.getShow().visible = false;
               m_worknocheck.getShow().visible = false;
               m_donebtn.getShow().visible = false;
               m_noopenbtn.getShow().visible = true;
            }
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_workcheck:
            case m_worknocheck:
            case m_donebtn:
            case m_noopenbtn:
               m_newmaintaskpanel.refreshScript(this);
               m_newmainlist.refreshById(m_id);
         }
      }
      
      public function refreshScript() : void
      {
         m_newmaintaskpanel.refreshScript(this);
      }
      
      public function show() : void
      {
         m_item.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function hide() : void
      {
         m_item.removeEventListener("clickButton",clickButton,true);
      }
   }
}

