package UI2.broadcast
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class BroadDataManager
   {
      private static var _instance:BroadDataManager;
      
      private var m_datalist:Vector.<BroadInfo>;
      
      private var m_savelist:Vector.<BroadSaveOne>;
      
      private var m_xml:XML;
      
      public var isLoaded:Boolean = false;
      
      private var m_time:uint;
      
      private var m_addTime:uint;
      
      public var nowEvent:String = "alltip";
      
      public var nowVip:int;
      
      public var upNum:int;
      
      public var isShowOld:Boolean = true;
      
      public var maxNum:int = 2;
      
      public function BroadDataManager()
      {
         super();
         m_datalist = new Vector.<BroadInfo>();
         m_savelist = new Vector.<BroadSaveOne>();
         m_xml = XMLSingle.getInstance().broadcast;
      }
      
      public static function getInstance() : BroadDataManager
      {
         if(_instance == null)
         {
            _instance = new BroadDataManager();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_datalist);
         m_datalist = null;
         ClearUtil.clearObject(m_savelist);
         m_savelist = null;
      }
      
      public function initData() : void
      {
         isLoaded = false;
      }
      
      public function getBroadList() : Vector.<BroadInfo>
      {
         return m_datalist;
      }
      
      public function refreshNum() : void
      {
         var _loc1_:int = GamingUI.getInstance().player1.vipVO.vipLevel;
         if(_loc1_ > 0)
         {
            if(_loc1_ == 1)
            {
               maxNum = 3;
            }
            else if(_loc1_ == 2)
            {
               maxNum = 3;
            }
            else if(_loc1_ == 3)
            {
               maxNum = 4;
            }
            else if(_loc1_ == 4)
            {
               maxNum = 4;
            }
            else if(_loc1_ == 5)
            {
               maxNum = 5;
            }
            else if(_loc1_ == 6)
            {
               maxNum = 5;
            }
            else if(_loc1_ == 7)
            {
               maxNum = 6;
            }
            else if(_loc1_ == 8)
            {
               maxNum = 6;
            }
         }
         else
         {
            maxNum = 2;
         }
      }
      
      public function getBroadData(param1:String) : BroadInfo
      {
         var _loc2_:BroadSaveOne = null;
         var _loc3_:* = 0;
         var _loc4_:int = 0;
         if(m_datalist && m_datalist.length > 0)
         {
            delOther();
            if(m_datalist.length == 1)
            {
               _loc2_ = new BroadSaveOne();
               _loc2_.uid = m_datalist[0].uid;
               _loc2_.score = m_datalist[0].score;
               m_savelist.push(_loc2_);
               return m_datalist[0];
            }
            if(m_datalist.length >= 2)
            {
               _loc3_ = 0;
               _loc4_ = 1;
               while(_loc4_ < m_datalist.length)
               {
                  if(TimeUtil.getTimeUtil().stringToDate(m_datalist[_loc3_].time).time < TimeUtil.getTimeUtil().stringToDate(m_datalist[_loc4_].time).time)
                  {
                     _loc3_ = _loc4_;
                  }
                  _loc4_++;
               }
               _loc2_ = new BroadSaveOne();
               _loc2_.uid = m_datalist[_loc3_].uid;
               _loc2_.score = m_datalist[_loc3_].score;
               m_savelist.push(_loc2_);
               return m_datalist[_loc3_];
            }
         }
         return null;
      }
      
      private function delOther() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_datalist.length)
         {
            if(checkShowed(m_datalist[_loc1_].uid,m_datalist[_loc1_].score))
            {
               m_datalist.splice(_loc1_,1);
            }
            _loc1_++;
         }
      }
      
      private function sortVect(param1:BroadInfo, param2:BroadInfo) : int
      {
         if(TimeUtil.getTimeUtil().stringToDate(param1.time).time > TimeUtil.getTimeUtil().stringToDate(param2.time).time)
         {
            return 1;
         }
         return -1;
      }
      
      public function addData(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc3_:BroadInfo = null;
         var _loc2_:String = null;
         var _loc7_:Array = null;
         var _loc4_:XMLList = null;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         if(param1 == null)
         {
            return;
         }
         _loc6_ = 0;
         while(param1 && _loc6_ < param1.length)
         {
            _loc3_ = new BroadInfo();
            _loc2_ = String(param1[_loc6_].getExtra());
            _loc3_.score = param1[_loc6_].getScore();
            _loc7_ = _loc2_.split("&");
            _loc3_.type = int(_loc7_[0]);
            _loc3_.value = String(_loc7_[1]);
            _loc3_.name = String(_loc7_[2]);
            _loc3_.uid = uint(_loc7_[3]);
            _loc3_.time = String(_loc7_[4]);
            _loc3_.txtInfo = String(_loc7_[5]);
            _loc3_.doId = int(_loc7_[6]);
            if(_loc3_.time != "undefined" && checkTime(_loc3_.time) && checkShowed(_loc3_.uid,_loc3_.score) == false)
            {
               if(m_xml == null)
               {
                  m_xml = XMLSingle.getInstance().broadcast;
               }
               _loc4_ = m_xml.tipinfo;
               _loc5_ = 0;
               while(_loc5_ < _loc4_.length())
               {
                  if(String(_loc3_.type) == String(_loc4_[_loc5_].@type))
                  {
                     loadDataItem(_loc3_,_loc4_[_loc5_]);
                     loadRewardData(_loc3_);
                     m_datalist.push(_loc3_);
                     break;
                  }
                  _loc5_++;
               }
            }
            _loc6_++;
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
      }
      
      private function checkTime(param1:String) : Boolean
      {
         if(BroadcastFunction.getInstance().m_time == null)
         {
            return false;
         }
         var _loc3_:Date = TimeUtil.getTimeUtil().stringToDate(BroadcastFunction.getInstance().m_time);
         var _loc2_:Date = TimeUtil.getTimeUtil().stringToDate(param1);
         if(_loc3_.getFullYear() == _loc2_.getFullYear() && _loc3_.getMonth() == _loc2_.getMonth() && _loc3_.getDay() == _loc2_.getDay() && _loc3_.getHours() == _loc2_.getHours() && Math.abs(_loc3_.getMinutes() - _loc2_.getMinutes()) <= BroadcastFunction.getInstance().m_maxTime)
         {
            return true;
         }
         return false;
      }
      
      private function checkShowed(param1:uint, param2:uint) : Boolean
      {
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_savelist.length)
         {
            if(param1 == m_savelist[_loc3_].uid && param2 == m_savelist[_loc3_].score)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc2_:XML = null;
         GetDataFunction.getInstance().initParams();
         m_savelist.length = 0;
         if(param1.hasOwnProperty("BroadData"))
         {
            _loc2_ = param1.BroadData[0];
            upNum = int(_loc2_.@upNum);
            BroadcastFunction.getInstance().setTime(String(_loc2_.@time));
         }
         else
         {
            upNum = 0;
            BroadcastFunction.getInstance().setFirst();
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <BroadData />;
         _loc1_.@time = BroadcastFunction.getInstance().m_time;
         _loc1_.@upNum = upNum;
         return _loc1_;
      }
      
      private function loadRewardData(param1:BroadInfo) : void
      {
         var _loc6_:XML = null;
         var _loc8_:int = 0;
         var _loc16_:XMLList = null;
         var _loc12_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:RewardInfo = null;
         var _loc9_:int = 0;
         var _loc17_:XMLList = null;
         var _loc15_:int = 0;
         var _loc3_:Number = NaN;
         var _loc11_:int = 0;
         var _loc13_:Number = NaN;
         var _loc14_:* = NaN;
         var _loc10_:Number = NaN;
         var _loc5_:XMLList = m_xml.rewards;
         _loc9_ = 0;
         while(_loc9_ < _loc5_.length())
         {
            if(param1.type == int(_loc5_[_loc9_].@type))
            {
               _loc6_ = _loc5_[_loc9_];
               _loc16_ = _loc6_.addWeight;
               _loc12_ = int(_loc16_[0].@totalweight);
               _loc4_ = 1 + Math.random() * (_loc12_ - 1 + 1);
               _loc8_ = 0;
               while(_loc8_ < _loc16_.length())
               {
                  if(_loc4_ >= int(_loc16_[_loc8_].@weightmin) && _loc4_ <= int(_loc16_[_loc8_].@weightmax))
                  {
                     _loc7_ = int(_loc16_[_loc8_].@value);
                     break;
                  }
                  _loc8_++;
               }
               _loc17_ = _loc6_.item;
               _loc15_ = int(!!_loc17_ ? _loc17_.length() : 0);
               _loc3_ = 0;
               _loc8_ = 0;
               while(_loc8_ < _loc15_)
               {
                  _loc3_ += Number(_loc17_[_loc8_].@proWeight);
                  _loc8_++;
               }
               _loc11_ = 0;
               while(_loc11_ < _loc7_)
               {
                  _loc13_ = Math.random();
                  _loc14_ = 0;
                  _loc10_ = 0;
                  _loc8_ = 0;
                  while(_loc8_ < _loc15_)
                  {
                     _loc10_ = _loc14_ + Number(_loc17_[_loc8_].@proWeight) / _loc3_;
                     if(_loc13_ >= _loc14_ && _loc13_ < _loc10_)
                     {
                        _loc2_ = new RewardInfo();
                        _loc2_.id = int(_loc17_[_loc8_].@id);
                        _loc2_.num = 1;
                        param1.equipResult.push(_loc2_);
                        break;
                     }
                     _loc14_ = _loc10_;
                     _loc8_++;
                  }
                  _loc11_++;
               }
               break;
            }
            _loc9_++;
         }
      }
      
      private function loadDataItem(param1:BroadInfo, param2:XML) : void
      {
         var _loc4_:String = null;
         var _loc3_:String = null;
         var _loc6_:Array = null;
         var _loc5_:int = 0;
         param1.isShowReward = int(param2.@isShowReward);
         if(param1.type == 1)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 2)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 3)
         {
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 4)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.idsstr.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.idsstr.push(String(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 5)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.idsstr.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.idsstr.push(String(_loc6_[_loc5_]));
               _loc5_++;
            }
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 6)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.idsstr.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.idsstr.push(String(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 7)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.idsstr.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.idsstr.push(String(_loc6_[_loc5_]));
               _loc5_++;
            }
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 8)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 9)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 10)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 11)
         {
            _loc4_ = String(param2.@ids);
            _loc6_ = _loc4_.split(",");
            param1.ids.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.ids.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 12)
         {
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 13)
         {
            _loc3_ = String(param2.@ids);
            _loc6_ = _loc3_.split(",");
            param1.levels.length = 0;
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               param1.levels.push(int(_loc6_[_loc5_]));
               _loc5_++;
            }
         }
         else if(param1.type == 13)
         {
            param1.hp = uint(param2.@hp);
         }
      }
   }
}

