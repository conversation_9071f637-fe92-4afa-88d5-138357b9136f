package UI.XiangMoLevelPanel
{
   import UI.DataManagerParent;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class XiangMoLevelSaveData extends DataManagerParent
   {
      private static var m_instance:XiangMoLevelSaveData;
      
      private var m_dataTime:String;
      
      private var m_xiangMoLevelSaveOneDatas:Vector.<XiangMoLevelSaveOneData>;
      
      public function XiangMoLevelSaveData()
      {
         super();
         if(m_instance == null)
         {
            m_xiangMoLevelSaveOneDatas = new Vector.<XiangMoLevelSaveOneData>();
            m_instance = this;
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : XiangMoLevelSaveData
      {
         if(m_instance == null)
         {
            m_instance = new XiangMoLevelSaveData();
         }
         return m_instance;
      }
      
      override public function clear() : void
      {
         m_instance = null;
         m_dataTime = null;
         ClearUtil.clearObject(m_xiangMoLevelSaveOneDatas);
         m_xiangMoLevelSaveOneDatas = null;
         super.clear();
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc7_:XMLList = null;
         var _loc10_:XML = null;
         var _loc12_:Array = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:Boolean = false;
         var _loc9_:int = 0;
         var _loc11_:int = 0;
         var _loc8_:XMLList = null;
         var _loc5_:int = 0;
         var _loc6_:XiangMoLevelSaveOneData = null;
         if(param1.hasOwnProperty("XM"))
         {
            _loc7_ = param1.XM[0].one;
            _loc10_ = XML(param1.XM[0]).copy();
            delete _loc10_.one;
            _loc12_ = [];
            _loc3_ = 0;
            _loc4_ = 0;
            _loc2_ = false;
            _loc9_ = int(_loc7_.length());
            _loc3_ = 0;
            while(_loc3_ < _loc9_)
            {
               _loc2_ = false;
               _loc4_ = 0;
               while(_loc4_ < _loc12_.length)
               {
                  if(String(_loc12_[_loc4_]) == String(_loc7_[_loc3_].@id))
                  {
                     _loc2_ = true;
                  }
                  _loc4_++;
               }
               if(_loc2_ == false)
               {
                  _loc12_.push(String(_loc7_[_loc3_].@id));
                  _loc10_.appendChild(_loc7_[_loc3_]);
               }
               _loc3_++;
            }
            ClearUtil.clearObject(_loc12_);
            _loc12_ = null;
            m_xiangMoLevelSaveOneDatas.length = 0;
            dataTime = MyFunction2.resetErrorTime(String(_loc10_.@t));
            _loc8_ = _loc10_.one;
            _loc5_ = int(!!_loc8_ ? _loc8_.length() : 0);
            _loc11_ = 0;
            while(_loc11_ < _loc5_)
            {
               _loc6_ = new XiangMoLevelSaveOneData();
               _loc6_.initFromSaveXML(_loc8_[_loc11_]);
               m_xiangMoLevelSaveOneDatas.push(_loc6_);
               _loc11_++;
            }
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc3_:int = 0;
         var _loc2_:XML = <XM />;
         if(dataTime)
         {
            _loc2_.@t = dataTime;
         }
         var _loc1_:int = int(m_xiangMoLevelSaveOneDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_.appendChild(m_xiangMoLevelSaveOneDatas[_loc3_].exportSaveXML());
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getSaveOneDataByFuBenId(param1:String) : XiangMoLevelSaveOneData
      {
         var _loc4_:int = 0;
         var _loc2_:int = int(m_xiangMoLevelSaveOneDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_xiangMoLevelSaveOneDatas[_loc4_].getFuBenId() == param1)
            {
               return m_xiangMoLevelSaveOneDatas[_loc4_];
            }
            _loc4_++;
         }
         var _loc3_:XiangMoLevelSaveOneData = new XiangMoLevelSaveOneData();
         _loc3_.initByData(param1,0);
         m_xiangMoLevelSaveOneDatas.push(_loc3_);
         return _loc3_;
      }
      
      public function isResetData(param1:String) : Boolean
      {
         if(new TimeUtil().newDateIsNewDay(dataTime,param1))
         {
            return true;
         }
         return false;
      }
      
      public function resetData(param1:String) : void
      {
         var _loc3_:int = 0;
         this.dataTime = param1;
         var _loc2_:int = int(m_xiangMoLevelSaveOneDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_xiangMoLevelSaveOneDatas[_loc3_].resetData();
            _loc3_++;
         }
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.dataTime = m_dataTime;
      }
      
      private function get dataTime() : String
      {
         return _antiwear.dataTime;
      }
      
      private function set dataTime(param1:String) : void
      {
         _antiwear.dataTime = param1;
      }
   }
}

