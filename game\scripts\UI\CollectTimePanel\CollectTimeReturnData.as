package UI.CollectTimePanel
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class CollectTimeReturnData
   {
      private var _m_isStartCollect:Boolean;
      
      private var _m_ableTransformExp:uint;
      
      private var _m_ableTransformExpByTicket:uint;
      
      private var _m_isLessMinTime:Boolean;
      
      private var _m_isMoreMaxTime:Boolean;
      
      private var _m_collectTime:uint;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function CollectTimeReturnData()
      {
         super();
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.m_isStartCollect = _m_isStartCollect;
         _antiwear.m_ableTransformExp = _m_ableTransformExp;
         _antiwear.m_ableTransformExpByTicket = _m_ableTransformExpByTicket;
         _antiwear.m_isLessMinTime = _m_isLessMinTime;
         _antiwear.m_isMoreMaxTime = _m_isMoreMaxTime;
         _antiwear.m_collectTime = _m_collectTime;
      }
      
      public function get m_isStartCollect() : Boolean
      {
         return _antiwear.m_isStartCollect;
      }
      
      public function set m_isStartCollect(param1:Boolean) : void
      {
         _antiwear.m_isStartCollect = param1;
      }
      
      public function get m_ableTransformExp() : uint
      {
         return _antiwear.m_ableTransformExp;
      }
      
      public function set m_ableTransformExp(param1:uint) : void
      {
         _antiwear.m_ableTransformExp = param1;
      }
      
      public function get m_ableTransformExpByTicket() : uint
      {
         return _antiwear.m_ableTransformExpByTicket;
      }
      
      public function set m_ableTransformExpByTicket(param1:uint) : void
      {
         _antiwear.m_ableTransformExpByTicket = param1;
      }
      
      public function get m_isLessMinTime() : Boolean
      {
         return _antiwear.m_isLessMinTime;
      }
      
      public function set m_isLessMinTime(param1:Boolean) : void
      {
         _antiwear.m_isLessMinTime = param1;
      }
      
      public function get m_isMoreMaxTime() : Boolean
      {
         return _antiwear.m_isMoreMaxTime;
      }
      
      public function set m_isMoreMaxTime(param1:Boolean) : void
      {
         _antiwear.m_isMoreMaxTime = param1;
      }
      
      public function get m_collectTime() : uint
      {
         return _antiwear.m_collectTime;
      }
      
      public function set m_collectTime(param1:uint) : void
      {
         _antiwear.m_collectTime = param1;
      }
      
      public function setIsStartCollet(param1:Boolean) : void
      {
         m_isStartCollect = param1;
      }
      
      public function getIsStartCollect() : Boolean
      {
         return m_isStartCollect;
      }
      
      public function setAbleTransformExp(param1:uint) : void
      {
         m_ableTransformExp = param1;
      }
      
      public function getAbleTransformExp() : uint
      {
         return m_ableTransformExp;
      }
      
      public function setAbleTransformExpByTicket(param1:uint) : void
      {
         m_ableTransformExpByTicket = param1;
      }
      
      public function getAbleTransformExpByTicket() : uint
      {
         return m_ableTransformExpByTicket;
      }
      
      public function setIsLessMinTime(param1:Boolean) : void
      {
         m_isLessMinTime = param1;
      }
      
      public function getIsLessMinTime() : Boolean
      {
         return m_isLessMinTime;
      }
      
      public function setIsMoreMaxTime(param1:Boolean) : void
      {
         m_isMoreMaxTime = param1;
      }
      
      public function getIsMoreMaxTime() : Boolean
      {
         return m_isMoreMaxTime;
      }
      
      public function setCollectTime(param1:uint) : void
      {
         m_collectTime = param1;
      }
      
      public function getCollectTime() : uint
      {
         return m_collectTime;
      }
   }
}

