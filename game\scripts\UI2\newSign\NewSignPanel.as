package UI2.newSign
{
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.ExchangeGiftBag.GiftMountData;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class NewSignPanel extends MySprite
   {
      private var newSignXml:XML;
      
      private var show:MovieClip;
      
      private var btnClose:ButtonLogicShell2;
      
      private var btnSign:ButtonLogicShell2;
      
      private var btnGetAward:ButtonLogicShell2;
      
      private var btnRetroactive:ButtonLogicShell2;
      
      private var btnRetroactiveCancel:ButtonLogicShell2;
      
      private var btnRetroactiveSure:ButtonLogicShell2;
      
      private var giftTypeBtnGroup:SwitchBtnGroupLogicShell;
      
      private var giftTimeBtnGroup:SwitchBtnGroupLogicShell;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var equipments:Vector.<Equipment>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var _mountData:GiftMountData;
      
      private var monthArr:Array = [31,28,31,30,31,30,31,31,30,31,30,31];
      
      private var items:Vector.<NewSignItem>;
      
      public function NewSignPanel()
      {
         super();
         this.name = "newSign";
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         if(!NewSignData.getInstance().timeStr)
         {
            NewSignData.getInstance().timeStr = TimeUtil.timeStr;
         }
         initUI();
         loadXml();
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(btnClose);
         btnClose = null;
         ClearUtil.clearObject(btnSign);
         btnSign = null;
         ClearUtil.clearObject(btnGetAward);
         btnGetAward = null;
         ClearUtil.clearObject(btnRetroactive);
         btnRetroactive = null;
         ClearUtil.clearObject(btnRetroactiveCancel);
         btnRetroactiveCancel = null;
         ClearUtil.clearObject(btnRetroactiveSure);
         btnRetroactiveSure = null;
         ClearUtil.clearObject(items);
         items = null;
         ClearUtil.clearObject(giftTypeBtnGroup);
         giftTypeBtnGroup = null;
         ClearUtil.clearObject(giftTimeBtnGroup);
         giftTimeBtnGroup = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
      }
      
      private function initUI() : void
      {
         var _loc1_:MySwitchBtnLogicShell = null;
         show = MyFunction2.returnShowByClassName("NewSign") as MovieClip;
         this.addChild(show);
         btnClose = new ButtonLogicShell2();
         btnClose.setShow(show["btnClose"]);
         btnGetAward = new ButtonLogicShell2();
         btnGetAward.setShow(show["btnGetAward"]);
         btnSign = new ButtonLogicShell2();
         btnSign.setShow(show["btnSign"]);
         btnRetroactive = new ButtonLogicShell2();
         btnRetroactive.setShow(show["btnRetroactive"]);
         btnRetroactiveCancel = new ButtonLogicShell2();
         btnRetroactiveCancel.setShow(show["btnRetroactiveCancel"]);
         btnRetroactiveSure = new ButtonLogicShell2();
         btnRetroactiveSure.setShow(show["btnRetroactiveSure"]);
         var _loc2_:int = 0;
         giftTypeBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         _loc2_ = 0;
         while(show.getChildByName("btnGiftType" + _loc2_) != null)
         {
            _loc1_ = new MySwitchBtnLogicShell();
            _loc1_.setShow(show.getChildByName("btnGiftType" + _loc2_) as Sprite);
            giftTypeBtnGroup.addSwitchBtn(_loc1_);
            _loc2_++;
         }
         giftTypeBtnGroup.addEnd();
         giftTimeBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         _loc2_ = 0;
         while(show.getChildByName("btnGiftTime" + _loc2_) != null)
         {
            _loc1_ = new MySwitchBtnLogicShell();
            _loc1_.setShow(show.getChildByName("btnGiftTime" + _loc2_) as Sprite);
            giftTimeBtnGroup.addSwitchBtn(_loc1_);
            _loc2_++;
         }
         giftTimeBtnGroup.addEnd();
         items = new Vector.<NewSignItem>();
         _loc2_ = 0;
         while(_loc2_ < 42)
         {
            items.push(new NewSignItem(show["item" + _loc2_]));
            _loc2_++;
         }
         _loc2_ = 0;
         while(_loc2_ < 3)
         {
            if(_loc2_ < NewSignData.getInstance().currentGiftType)
            {
               show["mcArrow" + _loc2_].gotoAndStop(1);
            }
            else
            {
               show["mcArrow" + _loc2_].gotoAndStop(2);
            }
            _loc2_++;
         }
         resetSignShow();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      private function resetShow() : void
      {
         resetSignShow();
         resetGiftShow();
      }
      
      private function resetSignShow() : void
      {
         var _loc6_:int = 0;
         var _loc5_:TimeUtil = new TimeUtil();
         var _loc4_:Date = _loc5_.stringToDate(NewSignData.getInstance().timeStr);
         var _loc2_:int = int(monthArr[_loc4_.getMonth()]);
         var _loc3_:int = int(_loc4_.getDate());
         if(Number(_loc4_.getFullYear().toString().substr(2,3)) % 4 == 0)
         {
            if(_loc4_.getMonth() == 1)
            {
               _loc2_ = 29;
            }
         }
         _loc4_.date = 1;
         var _loc1_:int = int(_loc4_.getDay());
         _loc6_ = 0;
         while(_loc6_ < 42)
         {
            if(_loc6_ < _loc1_ || _loc6_ >= _loc1_ + _loc2_)
            {
               items[_loc6_].turnUnActivate();
            }
            else
            {
               items[_loc6_].setDay(_loc6_ - _loc1_ + 1);
               if(NewSignData.getInstance().signedArr.indexOf(_loc6_ - _loc1_ + 1) != -1)
               {
                  if(_loc6_ == _loc3_ + _loc1_ - 1)
                  {
                     items[_loc6_].turnTodaySigned();
                  }
                  else
                  {
                     items[_loc6_].turnSigned();
                  }
               }
               else if(_loc6_ == _loc3_ + _loc1_ - 1)
               {
                  items[_loc6_].turnTodayNormal();
               }
               else
               {
                  items[_loc6_].turnNormal();
               }
            }
            _loc6_++;
         }
         if(NewSignData.getInstance().signedArr.indexOf(_loc3_) == -1)
         {
            btnSign.unLock();
         }
         else
         {
            btnSign.lock();
         }
         show["txtAllSignedNum"].text = String(NewSignData.getInstance().signedArr.length);
         show["txtMonth"].text = String(_loc4_.getMonth() + 1);
         btnRetroactive.getShow().visible = true;
         btnRetroactiveCancel.getShow().visible = false;
         btnRetroactiveSure.getShow().visible = false;
      }
      
      private function resetGiftShow() : void
      {
         var _loc1_:Equipment = null;
         if(!giftTypeBtnGroup.currentActivateBtn().getShow())
         {
            return;
         }
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         equipmentVOsData = new EquipmentVOsData();
         equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(newSignXml[giftTypeBtnGroup.currentActivateBtn().getShow().name][giftTimeBtnGroup.currentActivateBtn().getShow().name][0],XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         equipments = new Vector.<Equipment>();
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < equipmentVOsData.getEquipmentVONum())
         {
            _loc1_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData.getEquipmentVOByIndex(_loc2_));
            show["eqCell_" + _loc2_].addChild(_loc1_);
            equipments.push(_loc1_);
            _loc1_.addEventListener("rollOver",onOver2,false,0,true);
            _loc1_.addEventListener("rollOut",onOut2,false,0,true);
            _loc2_++;
         }
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         _mountData = new GiftMountData();
         _mountData.initByXML(newSignXml[giftTypeBtnGroup.currentActivateBtn().getShow().name][giftTimeBtnGroup.currentActivateBtn().getShow().name][0]);
         _loc2_ = 0;
         if(_mountData.getExpNum())
         {
            show["mcOtherItem" + _loc2_].visible = true;
            show["mcOtherItem" + _loc2_].gotoAndStop("expNum");
            show["mcOtherItem" + _loc2_].txt.text = MyFunction2.transformMoneyConcise1(_mountData.getExpNum());
            _loc2_++;
         }
         if(_mountData.getYuanbaoNum())
         {
            show["mcOtherItem" + _loc2_].visible = true;
            show["mcOtherItem" + _loc2_].gotoAndStop("yuanbaoNum");
            show["mcOtherItem" + _loc2_].txt.text = MyFunction2.transformMoneyConcise1(_mountData.getYuanbaoNum());
            _loc2_++;
         }
         if(_mountData.getZhaoHuanNum())
         {
            show["mcOtherItem" + _loc2_].visible = true;
            show["mcOtherItem" + _loc2_].gotoAndStop("zhaoHuanNum");
            show["mcOtherItem" + _loc2_].txt.text = MyFunction2.transformMoneyConcise1(_mountData.getZhaoHuanNum());
            _loc2_++;
         }
         if(_mountData.getLingShouShiNum())
         {
            show["mcOtherItem" + _loc2_].visible = true;
            show["mcOtherItem" + _loc2_].gotoAndStop("lingShouShiNum");
            show["mcOtherItem" + _loc2_].txt.text = MyFunction2.transformMoneyConcise1(_mountData.getLingShouShiNum());
            _loc2_++;
         }
         _loc2_;
         while(_loc2_ < 4)
         {
            show["mcOtherItem" + _loc2_].visible = false;
            _loc2_++;
         }
         if(giftTypeBtnGroup.currentActivateBtnIndex() == NewSignData.getInstance().currentGiftType)
         {
            if(giftTimeBtnGroup.currentActivateBtnIndex() == 0)
            {
               if(btnSign.isLock)
               {
                  MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),0);
                  btnGetAward.getShow().mouseEnabled = true;
                  if(NewSignData.getInstance().isGetGiftTime0)
                  {
                     btnGetAward.lock();
                  }
                  else
                  {
                     btnGetAward.unLock();
                  }
               }
               else
               {
                  btnGetAward.unLock();
                  MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
                  btnGetAward.getShow().mouseEnabled = false;
               }
            }
            else if(NewSignData.getInstance().getCanGetTime(giftTimeBtnGroup.currentActivateBtnIndex()))
            {
               MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),0);
               btnGetAward.getShow().mouseEnabled = true;
               if(NewSignData.getInstance().getIsGetTimeAward(giftTimeBtnGroup.currentActivateBtnIndex()))
               {
                  btnGetAward.lock();
               }
               else
               {
                  btnGetAward.unLock();
               }
            }
            else
            {
               btnGetAward.unLock();
               MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
               btnGetAward.getShow().mouseEnabled = false;
            }
         }
         else
         {
            btnGetAward.unLock();
            MyFunction.getInstance().changeSaturation(btnGetAward.getShow(),-100);
            btnGetAward.getShow().mouseEnabled = false;
         }
      }
      
      private function getAward() : void
      {
         btnGetAward.getShow().mouseEnabled = false;
         if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player1))
         {
            GamingUI.getInstance().player1.getMountsVO().addGetMaterialPointNum(_mountData.getZhaoHuanNum());
            GamingUI.getInstance().player1.getMountsVO().addStrengthenPointNum(_mountData.getLingShouShiNum());
            GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + _mountData.getYuanbaoNum();
            MyFunction.getInstance().addPlayerExperience(GamingUI.getInstance().player1,_mountData.getExpNum());
            showWarningBox("获取礼包成功",0);
            NewSignData.getInstance().setIsGetTimeAward(giftTimeBtnGroup.currentActivateBtnIndex());
         }
         else
         {
            showWarningBox("背包已满, 不能获取礼包",0);
         }
         resetGiftShow();
      }
      
      private function doSign() : void
      {
         var _loc3_:TimeUtil = new TimeUtil();
         var _loc2_:Date = _loc3_.stringToDate(NewSignData.getInstance().timeStr);
         var _loc1_:int = int(_loc2_.getDate());
         NewSignData.getInstance().signedArr.push(_loc1_);
         resetShow();
         GamingUI.getInstance().addMainLineTaskGoalGameEventStr("sign");
      }
      
      private function turnRetroactiveState() : void
      {
         var _loc4_:int = 0;
         btnRetroactive.getShow().visible = false;
         btnRetroactiveCancel.getShow().visible = true;
         btnRetroactiveSure.getShow().visible = true;
         var _loc3_:TimeUtil = new TimeUtil();
         var _loc2_:Date = _loc3_.stringToDate(NewSignData.getInstance().timeStr);
         var _loc1_:int = int(_loc2_.getDate());
         _loc4_ = 0;
         while(_loc4_ < 42)
         {
            if(items[_loc4_].getDay() > 0 && items[_loc4_].getDay() < _loc1_ && !items[_loc4_].getIsSigned())
            {
               items[_loc4_].turnUnselect();
            }
            _loc4_++;
         }
      }
      
      private function onClickGiftTypeBtn() : void
      {
         giftTimeBtnGroup.getSwitchBtnByIndex(0).turnActiveAndDispatchEvent();
      }
      
      private function onClickGiftTimeBtn() : void
      {
         resetGiftShow();
      }
      
      private function doRetroactiveSure() : void
      {
         var _loc2_:int = 0;
         var _loc1_:Array = [];
         _loc2_ = 0;
         while(_loc2_ < 42)
         {
            if(items[_loc2_].getIsSelecting())
            {
               _loc1_.push(items[_loc2_].getDay());
            }
            _loc2_++;
         }
         if(_loc1_.length == 0)
         {
            resetSignShow();
            return;
         }
         showWarningBox("是否花费" + newSignXml.Retroactive.@ticketPrice * _loc1_.length + "点券补签" + _loc1_.length + "天",3,{
            "type":"buyRetroactive",
            "okFunction":buy2
         });
      }
      
      private function buy2() : void
      {
         var price:uint;
         var ticketId:String;
         var dataObj:Object;
         var arr:Array = [];
         var i:int = 0;
         while(i < 42)
         {
            if(items[i].getIsSelecting())
            {
               arr.push(items[i].getDay());
            }
            i++;
         }
         if(arr.length == 0)
         {
            resetSignShow();
            return;
         }
         price = uint(newSignXml.Retroactive.@ticketPrice);
         ticketId = newSignXml.Retroactive.@ticketId;
         dataObj = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = arr.length;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = arr.join("|");
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            var _loc4_:int = 0;
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不想同！",0);
               throw new Error("购买物品id前后端不想同！");
            }
            var _loc3_:Array = String(param1["tag"]).split("|");
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               NewSignData.getInstance().signedArr.push(int(_loc3_[_loc4_]));
               _loc4_++;
            }
            NewSignData.getInstance().signedArr = NewSignData.getInstance().signedArr.sort(16);
            resetShow();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(String(param1.button["getShow"]().name).substr(0,11) == "btnGiftType")
         {
            onClickGiftTypeBtn();
            return;
         }
         if(String(param1.button["getShow"]().name).substr(0,11) == "btnGiftTime")
         {
            onClickGiftTimeBtn();
            return;
         }
         switch(param1.button)
         {
            case btnClose:
               clear();
               GamingUI.getInstance().clearNewSign();
               break;
            case btnSign:
               doSign();
               break;
            case btnGetAward:
               getAward();
               break;
            case btnRetroactive:
               turnRetroactiveState();
               break;
            case btnRetroactiveCancel:
               resetSignShow();
               break;
            case btnRetroactiveSure:
               doRetroactiveSure();
         }
      }
      
      private function loadXml() : void
      {
         MyFunction2.loadXMLFunction("NewSign",loadXmlComplete,showWarningBox,true);
      }
      
      private function loadXmlComplete(param1:XML) : void
      {
         newSignXml = param1;
         this.mouseChildren = true;
         this.mouseEnabled = true;
         giftTypeBtnGroup.getSwitchBtnByIndex(NewSignData.getInstance().currentGiftType).turnActiveAndDispatchEvent();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChild(WarningBoxSingle.getInstance());
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

