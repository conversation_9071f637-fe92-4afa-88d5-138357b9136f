package UI
{
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.OnlyOnLineBuff.OnlyOnLineBuffVO;
   import UI.Buff.BuffData;
   import UI.CheatData.CheatData;
   import UI.CollectTimePanel.CollectTimeSaveData;
   import UI.DetectionClass.DetectionClass;
   import UI.DetectionClass.DetectionClass2;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.ExchangeEquipment.ExEPlayerData;
   import UI.ExchangeGiftBag.ExchangeGiftData;
   import UI.Farm.FarmData;
   import UI.MainLineTask.MainLineTaskData;
   import UI.MiragePanel.MirageData;
   import UI.NicknameSystem.NicknameData;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import UI.PKUI.PlayerDataForPK;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Players.VipVO;
   import UI.Protect.ProtectData;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RefineFactory.RefineFactoryData;
   import UI.ShiTu.TuDiSkillVO;
   import UI.ShiTu.TuDiVO;
   import UI.ShiTu.XiuLianContent;
   import UI.SignPanel.SignData;
   import UI.Skills.PetSkills.PetAwakeSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.Task.TaskGoalManager;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.EveryDayTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.UIInterface.IActiveSkillVO;
   import UI.UIInterface.ILimitEquipmentVO;
   import UI.WorldBoss.WorldBossSaveData;
   import UI.XiangMoLevelPanel.XiangMoLevelSaveData;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import UI.newTask.NewActivityTask.MewActivityData;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.Consumer.ConsumerData;
   import UI2.NewPrecious.PreDataInfo;
   import UI2.NewRank.RankDataInfo;
   import UI2.ProgramStartData.ProgramStartData;
   import UI2.SVActivity.Data.HuanLeZhuanPanData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SmallAssistant.SmallAssistantSaveData;
   import UI2.broadcast.BroadDataManager;
   import UI2.buchang.BuchangData;
   import UI2.firstPay.FirstPayData;
   import UI2.newSign.NewSignData;
   import UI2.tehui.TeHuiData;
   import UI2.weekpay.WeekPayData;
   import UI2.wuyi.WuyiData;
   import YJFY.ActivityMode.ActivityManager;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.BossMode.BossUIAndData.BossSaveData;
   import YJFY.BossMode.PetBossUIAndData.PetBossSaveData;
   import YJFY.EndlessMode.EndlessLevelData;
   import YJFY.GameData;
   import YJFY.LevelMode1.DreamLand.DreamLandSaveData;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.LevelMode4.AnyeLevelData;
   import YJFY.PKMode.PKData.MyPKSaveData;
   import YJFY.Part1;
   import YJFY.optimize.SaveFileInfo;
   import YJFY.optimize.SaveNewFileData;
   
   public class ExportUI
   {
      public static const MAGIC_PERCENT:String = "magicPercent";
      
      private var m_mainLineTaskData:MainLineTaskData;
      
      private var player1:Player;
      
      private var player2:Player;
      
      private var publicStorageEquipmentVOs:Vector.<EquipmentVO>;
      
      private var automaicPetsData:AutomaticPetsData;
      
      private var smallAssistantSaveData:SmallAssistantSaveData;
      
      private var svActivitySaveData:SVActivitySaveData;
      
      private var vipVO:VipVO;
      
      private var m_item:SaveFileInfo;
      
      public function ExportUI()
      {
         super();
      }
      
      public function setMainLineTaskData(param1:MainLineTaskData) : void
      {
         m_mainLineTaskData = param1;
      }
      
      public function saveItem_1() : void
      {
         if(GameData.getInstance().getLoginReturnData().getUid())
         {
            Part1.getInstance().xmlData.@uid = GameData.getInstance().getLoginReturnData().getUid();
         }
      }
      
      public function saveItem_2() : void
      {
         if(GameData.getInstance().getLoginReturnData().getName())
         {
            Part1.getInstance().xmlData.@name = GameData.getInstance().getLoginReturnData().getName();
         }
         if(GameData.getInstance().getLoginReturnData().getNickname())
         {
            Part1.getInstance().xmlData.@nickName = GameData.getInstance().getLoginReturnData().getNickname_uri();
         }
      }
      
      public function saveItem_3() : void
      {
         if(GameData.getInstance().getSaveFileData().index)
         {
            Part1.getInstance().xmlData.@idx = GameData.getInstance().getSaveFileData().index;
         }
         if(ProgramStartData.getInstance().getVersion())
         {
            Part1.getInstance().xmlData.@ver = ProgramStartData.getInstance().getVersion();
         }
         if(Boolean(ProgramStartData.getInstance().getVersion()) && Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine")
         {
            DetectionClass.getInstance().detectionVersionMatchTime();
         }
      }
      
      public function saveItem_4() : void
      {
         if(player1.vipVO.vipLevel)
         {
            Part1.getInstance().xmlData.@vL = player1.vipVO.vipLevel;
         }
      }
      
      public function saveItem_5() : void
      {
         switch(player1.playerVO.playerType)
         {
            case "SunWuKong":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("Monkey",player1));
               break;
            case "BaiLongMa":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("Dragon",player1));
               break;
            case "ErLangShen":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("ErLangShen",player1));
               break;
            case "ChangE":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("ChangE",player1));
               break;
            case "Fox":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("Fox",player1));
               break;
            case "TieShan":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("TieShan",player1));
               break;
            case "Houyi":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("Houyi",player1));
               break;
            case "ZiXia":
               Part1.getInstance().xmlData.appendChild(createPlayerXML("ZiXia",player1));
               break;
            default:
               throw new Error("不存在的人物类型");
         }
         if(player2)
         {
            switch(player2.playerVO.playerType)
            {
               case "SunWuKong":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("Monkey",player2));
                  break;
               case "BaiLongMa":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("Dragon",player2));
                  break;
               case "ErLangShen":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("ErLangShen",player2));
                  break;
               case "ChangE":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("ChangE",player2));
                  break;
               case "Fox":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("Fox",player2));
                  break;
               case "TieShan":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("TieShan",player2));
                  break;
               case "Houyi":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("Houyi",player2));
                  break;
               case "ZiXia":
                  Part1.getInstance().xmlData.appendChild(createPlayerXML("ZiXia",player2));
                  break;
               default:
                  throw new Error("不存在的人物类型");
            }
         }
      }
      
      public function saveItem_6() : void
      {
         Part1.getInstance().xmlData.appendChild(createStorageXML("PublicStorage",publicStorageEquipmentVOs));
         Part1.getInstance().xmlData.appendChild(createTaskGoalsXML("TaskGoals"));
      }
      
      public function saveItem_7() : void
      {
         Part1.getInstance().xmlData.appendChild(createFarmXML("Farm"));
         Part1.getInstance().xmlData.appendChild(createLDFXML("LDF"));
      }
      
      public function saveItem_8() : void
      {
         Part1.getInstance().xmlData.appendChild(createBuffXML("Buff"));
         Part1.getInstance().xmlData.appendChild(createProtectXML("Protect"));
      }
      
      public function saveItem_9() : void
      {
         Part1.getInstance().xmlData.appendChild(createVIPXML("VIP",vipVO));
         Part1.getInstance().xmlData.appendChild(createEGDXML("EGD"));
      }
      
      public function saveItem_10() : void
      {
         Part1.getInstance().xmlData.appendChild(createMirageXML("Mirage"));
         Part1.getInstance().xmlData.appendChild(createPKXML("PK"));
      }
      
      public function saveItem_11() : void
      {
         Part1.getInstance().xmlData.appendChild(createSignXML("Sign"));
         Part1.getInstance().xmlData.appendChild(createRGXML("RG"));
      }
      
      public function saveItem_12() : void
      {
         Part1.getInstance().xmlData.appendChild(createEXEXML("ExE"));
         Part1.getInstance().xmlData.appendChild(createNicknameDataXML("NNameData"));
      }
      
      public function saveItem_13() : void
      {
         var _loc1_:XML = createNicknameErrorXML("NNameError");
         if(_loc1_)
         {
            Part1.getInstance().xmlData.appendChild(_loc1_);
         }
         Part1.getInstance().xmlData.appendChild(createOnLineGiftBag("OnLineGiftBag"));
      }
      
      public function saveItem_14() : void
      {
         Part1.getInstance().xmlData.appendChild(NewMainTaskData.getInstance().exportToSaveXML());
         Part1.getInstance().xmlData.appendChild(NewEveryDayData.getInstance().exportToSaveXML());
      }
      
      public function saveItem_15() : void
      {
         Part1.getInstance().xmlData.appendChild(MewActivityData.getInstance().exportToSaveXML());
         Part1.getInstance().xmlData.appendChild(WorldBossSaveData.getInstance().exportSaveXML());
      }
      
      public function saveItem_16() : void
      {
         Part1.getInstance().xmlData.appendChild(player1.getSocietyDataVO().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(BossSaveData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(PetBossSaveData.getInstance().exportSaveXML());
      }
      
      public function saveItem_17() : void
      {
         Part1.getInstance().xmlData.appendChild(MyPKSaveData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(LevelModeSaveData.getInstance().exportSaveXML());
      }
      
      public function saveItem_18() : void
      {
         Part1.getInstance().xmlData.appendChild(XiangMoLevelSaveData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(PreDataInfo.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(WuyiData.getInstance().exploreData());
      }
      
      public function saveItem_19() : void
      {
         Part1.getInstance().xmlData.appendChild(EndlessLevelData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(FirstPayData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(BuchangData.getInstance().exploreData());
      }
      
      public function saveItem_20() : void
      {
         Part1.getInstance().xmlData.appendChild(NewSignData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(AnyeLevelData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(TeHuiData.getInstance().exploreData());
      }
      
      public function saveItem_21() : void
      {
         Part1.getInstance().xmlData.appendChild(ConsumerData.getInstance().exploreData());
         Part1.getInstance().xmlData.appendChild(WeekPayData.getInstance().exploreData());
         Part1.getInstance().xmlData.appendChild(automaicPetsData.exportSaveXML());
      }
      
      public function saveItem_22() : void
      {
         Part1.getInstance().xmlData.appendChild(createCollectTime("CollectTime"));
         Part1.getInstance().xmlData.appendChild(smallAssistantSaveData.exportSaveXML());
         Part1.getInstance().xmlData.appendChild(player1.getPKMode2VO1().exportSaveXML("PK21"));
      }
      
      public function saveItem_23() : void
      {
         Part1.getInstance().xmlData.appendChild(svActivitySaveData.exportSaveXML());
         Part1.getInstance().xmlData.appendChild(RankDataInfo.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(BroadDataManager.getInstance().exportSaveXML());
      }
      
      public function saveItem_24() : void
      {
         Part1.getInstance().xmlData.appendChild(APIDataAnalyze.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(DreamLandSaveData.getInstance().exportSaveXML());
         Part1.getInstance().xmlData.appendChild(player1.getMountsVO().exportSaveXML());
      }
      
      public function saveItem_25() : void
      {
         Part1.getInstance().xmlData.appendChild(createOtherXML("Other"));
         Part1.getInstance().xmlData.appendChild(createActivityKillNum());
      }
      
      public function saveItem_26() : void
      {
         Part1.getInstance().xmlData.appendChild(createOtherXML("HuanLeZhuan"));
         Part1.getInstance().xmlData.appendChild(createHuanLeTurnTimes());
         Part1.getInstance().xmlData.appendChild(createActivityDoubleEggNum());
      }
      
      public function Export(param1:Player, param2:Player, param3:Vector.<EquipmentVO>, param4:AutomaticPetsData, param5:SmallAssistantSaveData, param6:SVActivitySaveData, param7:VipVO) : XML
      {
         player1 = param1;
         player2 = param2;
         publicStorageEquipmentVOs = param3;
         automaicPetsData = param4;
         smallAssistantSaveData = param5;
         svActivitySaveData = param6;
         vipVO = param7;
         SaveNewFileData.getInstance().getList().length = 0;
         m_item = new SaveFileInfo();
         m_item.typeid = "1001";
         m_item.saveFunc = saveItem_1;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1002";
         m_item.saveFunc = saveItem_2;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1003";
         m_item.saveFunc = saveItem_3;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1004";
         m_item.saveFunc = saveItem_4;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1005";
         m_item.saveFunc = saveItem_5;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1006";
         m_item.saveFunc = saveItem_6;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1007";
         m_item.saveFunc = saveItem_7;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1008";
         m_item.saveFunc = saveItem_8;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1009";
         m_item.saveFunc = saveItem_9;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1010";
         m_item.saveFunc = saveItem_10;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1011";
         m_item.saveFunc = saveItem_11;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1012";
         m_item.saveFunc = saveItem_12;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1013";
         m_item.saveFunc = saveItem_13;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1014";
         m_item.saveFunc = saveItem_14;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1015";
         m_item.saveFunc = saveItem_15;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1016";
         m_item.saveFunc = saveItem_16;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1017";
         m_item.saveFunc = saveItem_17;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1018";
         m_item.saveFunc = saveItem_18;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1019";
         m_item.saveFunc = saveItem_19;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1020";
         m_item.saveFunc = saveItem_20;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1021";
         m_item.saveFunc = saveItem_21;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1022";
         m_item.saveFunc = saveItem_22;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1023";
         m_item.saveFunc = saveItem_23;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1024";
         m_item.saveFunc = saveItem_24;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1025";
         m_item.saveFunc = saveItem_25;
         SaveNewFileData.getInstance().getList().push(m_item);
         m_item = new SaveFileInfo();
         m_item.typeid = "1026";
         m_item.saveFunc = saveItem_26;
         SaveNewFileData.getInstance().getList().push(m_item);
         return Part1.getInstance().xmlData;
      }
      
      public function createPlayerPartXML(param1:Player, param2:Player) : Array
      {
         var _loc4_:XML = null;
         var _loc3_:Array = [];
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               _loc4_ = createPlayerXML("Monkey",param1);
               break;
            case "BaiLongMa":
               _loc4_ = createPlayerXML("Dragon",param1);
               break;
            case "ErLangShen":
               _loc4_ = createPlayerXML("ErLangShen",param1);
               break;
            case "ChangE":
               _loc4_ = createPlayerXML("ChangE",param1);
               break;
            case "Fox":
               _loc4_ = createPlayerXML("Fox",param1);
               break;
            case "TieShan":
               _loc4_ = createPlayerXML("TieShan",param1);
               break;
            case "Houyi":
               _loc4_ = createPlayerXML("Houyi",param1);
               break;
            case "ZiXia":
               _loc4_ = createPlayerXML("ZiXia",param1);
               break;
            default:
               throw new Error("不存在的人物类型");
         }
         _loc3_.push(_loc4_);
         if(param2)
         {
            switch(param2.playerVO.playerType)
            {
               case "SunWuKong":
                  _loc4_ = createPlayerXML("Monkey",param2);
                  break;
               case "BaiLongMa":
                  _loc4_ = createPlayerXML("Dragon",param2);
                  break;
               case "ErLangShen":
                  _loc4_ = createPlayerXML("ErLangShen",param2);
                  break;
               case "ChangE":
                  _loc4_ = createPlayerXML("ChangE",param2);
                  break;
               case "Fox":
                  _loc4_ = createPlayerXML("Fox",param2);
                  break;
               case "TieShan":
                  _loc4_ = createPlayerXML("TieShan",param2);
                  break;
               case "Houyi":
                  _loc4_ = createPlayerXML("Houyi",param2);
                  break;
               case "ZiXia":
                  _loc4_ = createPlayerXML("ZiXia",param2);
                  break;
               default:
                  throw new Error("不存在的人物类型");
            }
            _loc3_.push(_loc4_);
         }
         return _loc3_;
      }
      
      private function createPlayerXML(param1:String, param2:Player) : XML
      {
         DetectionClass.getInstance().detectionPlayerVO(param2.playerVO);
         var _loc3_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc3_.@name = param2.playerVO.name;
         _loc3_.@playerId = param2.playerVO.playerID;
         trace("playerID:",param2.playerVO.playerID);
         _loc3_.@experiencePercent = param2.playerVO.experiencePercent.toFixed(4);
         if(param2.playerVO.bloodPercent)
         {
            _loc3_.@bloodPercent = param2.playerVO.bloodPercent.toFixed(4);
         }
         else
         {
            _loc3_.@bloodPercent = 1;
         }
         _loc3_["magicPercent"] = param2.playerVO.magicPercent.toFixed(4);
         _loc3_.@level = param2.playerVO.level;
         _loc3_.@money = param2.playerVO.money;
         _loc3_.@sF = int(param2.playerVO.isShowFashionShow);
         _loc3_.@medal = !!param2.playerVO.medal ? param2.playerVO.medal.id : 0;
         _loc3_.appendChild(createStorageXML("Package",param2.playerVO.packageEquipmentVOs));
         if(param2.playerVO.storageEquipmentXml)
         {
            _loc3_.appendChild(param2.playerVO.storageEquipmentXml);
         }
         else
         {
            _loc3_.appendChild(createStorageXML("Storage",param2.playerVO.storageEquipmentVOs));
         }
         _loc3_.appendChild(createStorageXML("Medals",param2.playerVO.medalEquipmentVOs));
         _loc3_.appendChild(createStorageXML("InformationPanel",param2.playerVO.inforEquipmentVOs));
         _loc3_.appendChild(createPetPanelXML("PetPanel",param2.playerVO.pet));
         _loc3_.appendChild(createDMPanelXML("DMPanel",param2));
         _loc3_.appendChild(createShiTuXML("ShiTu",param2));
         _loc3_.appendChild(param2.playerVO.hatchVO.exportSaveXML());
         _loc3_.appendChild(createSkillVOXML("Skill",param2.playerVO.skillVOs));
         _loc3_.appendChild(param2.playerVO.eqMagicVO.exportSaveXML());
         return _loc3_;
      }
      
      private function createStorageXML(param1:String, param2:Vector.<EquipmentVO>) : XML
      {
         var _loc5_:int = 0;
         var _loc4_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         var _loc3_:int = int(param2.length);
         DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param2);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_.appendChild(createEquipmentXML("item",!!param2[_loc5_] ? param2[_loc5_] : null));
            _loc5_++;
         }
         return _loc4_;
      }
      
      private function createPetPanelXML(param1:String, param2:Pet) : XML
      {
         var _loc3_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         if(!param2)
         {
            return _loc3_;
         }
         if(!param2.petEquipmentVO)
         {
            return _loc3_;
         }
         _loc3_.appendChild(createEquipmentXML("item",param2.petEquipmentVO));
         return _loc3_;
      }
      
      private function createEquipmentXML(param1:String, param2:EquipmentVO) : XML
      {
         var _loc3_:int = 0;
         var _loc5_:PreciousEquipmentVO = null;
         var _loc10_:WeaponEquipmentVO = null;
         var _loc12_:NecklaceEquipmentVO = null;
         var _loc15_:GourdEquipmentVO = null;
         var _loc7_:ClothesEquipmentVO = null;
         var _loc8_:AbleEquipmentVO = null;
         var _loc9_:int = 0;
         var _loc11_:int = 0;
         var _loc14_:InsetGemEquipmentVO = null;
         var _loc6_:int = 0;
         var _loc13_:int = 0;
         var _loc4_:XML = new XML("<" + (param1 + " ") + " />");
         if(param2)
         {
            DetectionClass.getInstance().addEquipmentVOFix(param2);
            _loc4_.@id = param2.id;
            if(param2.isBinding)
            {
               _loc4_.@isB = int(param2.isBinding);
            }
            _loc4_.@pf = !!param2.prefix ? MyBase64.encode(param2.prefix) : param2.prefix;
            _loc4_.@sf = !!param2.suffix ? MyBase64.encode(param2.suffix) : param2.suffix;
            switch(param2.equipmentType)
            {
               case "precious":
               case "weapon":
               case "necklace":
               case "clothes":
               case "gourd":
               case "forever_fashion":
                  switch(param2.equipmentType)
                  {
                     case "precious":
                        _loc3_ = 0;
                        _loc5_ = param2 as PreciousEquipmentVO;
                        _loc13_ = int(_loc5_.addPlayerSaveAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<eqmagic />);
                           _loc4_.children()[_loc3_].@addPlayerSaveAttr = _loc5_.addPlayerSaveAttr[_loc6_];
                           _loc4_.children()[_loc3_].@addPlayerSaveAttrVals = _loc5_.addPlayerSaveAttrVals[_loc6_];
                           _loc3_++;
                           _loc6_++;
                        }
                        _loc13_ = int(_loc5_.basisAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<addAttr />);
                           _loc4_.children()[_loc3_].@addAttName = _loc5_.basisAttr[_loc6_];
                           _loc4_.children()[_loc3_].@addAttValue = _loc5_.basisAttrValue[_loc6_];
                           _loc4_.children()[_loc3_].@basisUpValue = _loc5_.basisUpValue[_loc6_];
                           _loc4_.children()[_loc3_].@minupgrade = _loc5_.basisMinUp[_loc6_];
                           _loc4_.children()[_loc3_].@maxupgrade = _loc5_.basisMaxUp[_loc6_];
                           _loc4_.children()[_loc3_].@weight = _loc5_.basisWeight[_loc6_];
                           _loc4_.children()[_loc3_].@totalweight = _loc5_.basisTotalWeight[_loc6_];
                           _loc3_++;
                           _loc6_++;
                        }
                        _loc13_ = int(_loc5_.sAttrName.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<sAddAttr />);
                           _loc4_.children()[_loc3_].@addAttName = _loc5_.sAttrName[_loc6_];
                           _loc4_.children()[_loc3_].@addAttValue = _loc5_.sAttrValue[_loc6_];
                           _loc4_.children()[_loc3_].@avgValue = _loc5_.sAvgValue[_loc6_];
                           _loc4_.children()[_loc3_].@weight = _loc5_.sWeight[_loc6_];
                           _loc4_.children()[_loc3_].@totalweight = _loc5_.sTotalWeight[_loc6_];
                           _loc4_.children()[_loc3_].@minvalue = _loc5_.sMinValue[_loc6_];
                           _loc4_.children()[_loc3_].@maxvalue = _loc5_.sMaxValue[_loc6_];
                           _loc3_++;
                           _loc6_++;
                        }
                        _loc4_.appendChild(<materialid />);
                        _loc4_.children()[_loc3_].@value = _loc5_.materialid;
                        break;
                     case "weapon":
                        _loc4_.@attack = (param2 as WeaponEquipmentVO).attack;
                        _loc4_.@renPin = (param2 as AbleEquipmentVO).rengPin;
                        _loc10_ = param2 as WeaponEquipmentVO;
                        _loc13_ = int(_loc10_.addPlayerSaveAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<eqmagic />);
                           _loc4_.children()[_loc6_].@addPlayerSaveAttr = _loc10_.addPlayerSaveAttr[_loc6_];
                           _loc4_.children()[_loc6_].@addPlayerSaveAttrVals = _loc10_.addPlayerSaveAttrVals[_loc6_];
                           _loc6_++;
                        }
                        break;
                     case "necklace":
                        _loc4_.@criticalRate = (param2 as NecklaceEquipmentVO).criticalRate;
                        _loc4_.@renPin = (param2 as AbleEquipmentVO).rengPin;
                        _loc12_ = param2 as NecklaceEquipmentVO;
                        _loc13_ = int(_loc12_.addPlayerSaveAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<eqmagic />);
                           _loc4_.children()[_loc6_].@addPlayerSaveAttr = _loc12_.addPlayerSaveAttr[_loc6_];
                           _loc4_.children()[_loc6_].@addPlayerSaveAttrVals = _loc12_.addPlayerSaveAttrVals[_loc6_];
                           _loc6_++;
                        }
                        break;
                     case "gourd":
                        _loc4_.@maxMagic = (param2 as GourdEquipmentVO).maxMagic;
                        _loc4_.@renPin = (param2 as AbleEquipmentVO).rengPin;
                        _loc15_ = param2 as GourdEquipmentVO;
                        _loc13_ = int(_loc15_.addPlayerSaveAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<eqmagic />);
                           _loc4_.children()[_loc6_].@addPlayerSaveAttr = _loc15_.addPlayerSaveAttr[_loc6_];
                           _loc4_.children()[_loc6_].@addPlayerSaveAttrVals = _loc15_.addPlayerSaveAttrVals[_loc6_];
                           _loc6_++;
                        }
                        break;
                     case "clothes":
                        _loc4_.@defence = (param2 as ClothesEquipmentVO).defence;
                        _loc4_.@riot = (param2 as ClothesEquipmentVO).riot.toFixed(4);
                        _loc4_.@renPin = (param2 as AbleEquipmentVO).rengPin;
                        _loc7_ = param2 as ClothesEquipmentVO;
                        _loc13_ = int(_loc7_.addPlayerSaveAttr.length);
                        _loc6_ = 0;
                        while(_loc6_ < _loc13_)
                        {
                           _loc4_.appendChild(<eqmagic />);
                           _loc4_.children()[_loc6_].@addPlayerSaveAttr = _loc7_.addPlayerSaveAttr[_loc6_];
                           _loc4_.children()[_loc6_].@addPlayerSaveAttrVals = _loc7_.addPlayerSaveAttrVals[_loc6_];
                           _loc6_++;
                        }
                        break;
                     case "forever_fashion":
                        _loc4_.@addExtraAttrEx = (param2 as ForeverFashionEquipmentVO).addExtraAttrEx;
                        _loc4_.@addExtraAttrValue = (param2 as ForeverFashionEquipmentVO).addExtraAttrValue;
                  }
                  _loc8_ = param2 as AbleEquipmentVO;
                  _loc11_ = _loc8_.getHoleNum();
                  _loc9_ = 0;
                  while(_loc9_ < _loc11_)
                  {
                     _loc14_ = _loc8_.getInsetGem(_loc9_);
                     _loc4_.appendChild(createEquipmentXML("hole",_loc14_));
                     _loc9_++;
                  }
                  break;
               case "fashion":
               case "medal":
                  _loc4_.@initTime = (param2 as ILimitEquipmentVO).initTime;
                  break;
               case "pet":
                  _loc4_.@petLevel = (param2 as PetEquipmentVO).petLevel;
                  _loc4_.@experiencePercent = (param2 as PetEquipmentVO).experiencePercent.toFixed(4);
                  _loc4_.@essentialPercent = (param2 as PetEquipmentVO).essentialPercent.toFixed(4);
                  _loc4_.@talent = (param2 as PetEquipmentVO).talentVO.id;
                  _loc4_.appendChild(tranSkillVOsToXMLList((param2 as PetEquipmentVO).passiveSkillVOs));
                  _loc4_.appendChild(<activeSkill />);
                  _loc4_.activeSkill.@id = (param2 as PetEquipmentVO).activeSkillVO.id;
                  _loc4_.activeSkill.@currentCDTime = ((param2 as PetEquipmentVO).activeSkillVO as IActiveSkillVO).currentCDTime;
                  if(param2 is AdvancePetEquipmentVO)
                  {
                     _loc4_.appendChild(tranAwakeSkillVOsToXMLList((param2 as AdvancePetEquipmentVO).petAwakePassiveSkillVOs));
                  }
                  break;
               case "material":
               case "potion":
               case "pocket":
               case "grass":
               case "buffEquipment":
               case "forceDan":
               case "insetGem":
                  _loc4_.@num = (param2 as StackEquipmentVO).num;
                  break;
               case "undeterminded":
               case "egg":
               case "scroll":
               case "danMedicine":
               case "skillEquipment":
               case "petSkillBook":
               case "contract":
                  break;
               default:
                  throw new Error("出现未知类型装备!");
            }
         }
         else
         {
            _loc4_.@id = 0;
         }
         return _loc4_;
      }
      
      private function createSkillVOXML(param1:String, param2:Vector.<SkillVO>) : XML
      {
         var _loc5_:int = 0;
         var _loc4_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         var _loc3_:int = int(param2.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_.appendChild(<item />);
            if(param2[_loc5_])
            {
               _loc4_.children()[_loc5_].@id = param2[_loc5_].id;
               _loc4_.children()[_loc5_].@currentCDTime = (param2[_loc5_] as IActiveSkillVO).currentCDTime;
            }
            else
            {
               _loc4_.children()[_loc5_].@id = 0;
            }
            _loc5_++;
         }
         return _loc4_;
      }
      
      private function createDMPanelXML(param1:String, param2:Player) : XML
      {
         var _loc3_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc3_.appendChild(createDMPanelXMLOne("attack",param2.playerVO.attackDanMedicineEquipmentVOGrid));
         _loc3_.appendChild(createDMPanelXMLOne("defence",param2.playerVO.defenceDanMedicineEquipmentVOGrid));
         return _loc3_;
      }
      
      private function createDMPanelXMLOne(param1:String, param2:Vector.<Vector.<EquipmentVO>>) : XML
      {
         var _loc11_:int = 0;
         var _loc9_:int = 0;
         var _loc4_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:int = 0;
         var _loc5_:* = undefined;
         var _loc3_:String = null;
         var _loc7_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc11_ = 0;
         while(_loc11_ < 7)
         {
            _loc5_ = new Vector.<int>();
            _loc8_ = 2 + _loc11_ * 1;
            _loc9_ = 0;
            while(_loc9_ < _loc8_)
            {
               if(param2[_loc11_][_loc9_])
               {
                  _loc5_.push(param2[_loc11_][_loc9_].id);
               }
               _loc9_++;
            }
            if(_loc5_.length)
            {
               _loc3_ = MyFunction.getInstance().combineIdArray(_loc5_);
               _loc7_["l" + (_loc11_ + 1)] = _loc3_;
            }
            _loc10_ = int(_loc5_.length);
            _loc4_ = 0;
            while(_loc4_ < _loc10_)
            {
               _loc5_[_loc4_] = null;
               _loc4_++;
            }
            _loc5_ = null;
            _loc11_++;
         }
         return _loc7_;
      }
      
      private function createShiTuXML(param1:String, param2:Player) : XML
      {
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:XML = null;
         var _loc6_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         param2.playerVO.shiFuVO.exportSaveXML(_loc6_);
         var _loc5_:Vector.<XiuLianContent> = param2.playerVO.xiuLianContents;
         _loc4_ = !!_loc5_ ? _loc5_.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc3_ = createXiuLianXML(_loc5_[_loc7_]);
            _loc6_.appendChild(_loc3_);
            _loc7_++;
         }
         if(param2.playerVO.tuDiVO)
         {
            _loc6_.appendChild(createTuDiXML(param2.playerVO.tuDiVO));
         }
         return _loc6_;
      }
      
      private function createTuDiXML(param1:TuDiVO) : XML
      {
         var _loc7_:int = 0;
         var _loc2_:XML = null;
         var _loc6_:XML = <TuDi />;
         _loc6_.@type = param1.type;
         _loc6_.@bloodPercent = param1.bloodPercent.toFixed(4);
         _loc6_.@energyPercent = param1.energyPercent.toFixed(4);
         _loc6_.@xLValue = param1.currentXiuLianValue;
         _loc6_.@xLUsedNum = param1.currentUsedXianLianNum;
         _loc6_.@xLBuyNum = param1.buyXiuLianNum;
         _loc6_.@xLDate = !!param1.xiuLianDate ? param1.xiuLianDate : "";
         var _loc5_:Vector.<XiuLianContent> = param1.xiuLianContents;
         var _loc4_:int = !!_loc5_ ? _loc5_.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc6_.appendChild(createXiuLianXML(_loc5_[_loc7_]));
            _loc7_++;
         }
         var _loc3_:Vector.<TuDiSkillVO> = param1.skillVOs;
         _loc4_ = !!_loc3_ ? _loc3_.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc2_ = <skill />;
            _loc2_.@id = _loc3_[_loc7_].id;
            _loc6_.appendChild(_loc2_);
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function createXiuLianXML(param1:XiuLianContent) : XML
      {
         var _loc2_:XML = <xiuLian />;
         _loc2_.@id = param1.id;
         _loc2_.@xLValue = param1.currentXiuLianValue;
         return _loc2_;
      }
      
      private function createTaskGoalsXML(param1:String) : XML
      {
         var _loc6_:XMLList = null;
         var _loc8_:* = null;
         var _loc3_:Boolean = false;
         var _loc5_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = int(TaskGoalManager.getInstance().taskGoalVOs.length);
         _loc9_ = 0;
         while(_loc9_ < _loc4_)
         {
            _loc2_ = TaskGoalManager.getInstance().taskGoalVOs[_loc9_].id;
            _loc6_ = _loc5_.item;
            _loc3_ = false;
            for each(_loc8_ in _loc6_)
            {
               if(int(_loc8_.@id) == _loc2_)
               {
                  _loc3_ = true;
                  _loc8_.appendChild(<item />);
                  _loc8_.item[_loc8_.item.length() - 1].@ownerTaskID = MyFunction.getInstance().combineIdArray(TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs);
                  _loc8_.item[_loc8_.item.length() - 1].@num = TaskGoalManager.getInstance().taskGoalVOs[_loc9_].num;
                  break;
               }
            }
            if(!_loc3_)
            {
               _loc5_.appendChild(<item />);
               _loc5_.children()[_loc7_].@id = _loc2_;
               _loc5_.children()[_loc7_].appendChild(<item />);
               _loc5_.children()[_loc7_].item[0].@ownerTaskID = MyFunction.getInstance().combineIdArray(TaskGoalManager.getInstance().taskGoalVOs[_loc9_].owerTaskIDs);
               _loc5_.children()[_loc7_].item[0].@num = TaskGoalManager.getInstance().taskGoalVOs[_loc9_].num;
               _loc7_++;
            }
            _loc9_++;
         }
         return _loc5_;
      }
      
      private function createTasksXML(param1:String) : XML
      {
         var _loc4_:Vector.<MTaskVO> = TasksManager.getInstance().acceptedEveryDayTaskVOs.concat(TasksManager.getInstance().acceptedActivityTaskVOs);
         _loc4_ = _loc4_.concat(TasksManager.getInstance().wasteTaskVOs);
         var _loc3_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc3_.@reCount = TasksManager.getInstance().resetCount;
         _loc3_.@reDate = TasksManager.getInstance().resetDate;
         var _loc5_:int = 0;
         var _loc2_:int = int(_loc4_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_.appendChild(<item />);
            _loc3_.children()[_loc5_].@id = _loc4_[_loc5_].id;
            _loc3_.children()[_loc5_].@state = _loc4_[_loc5_].state;
            switch(_loc4_[_loc5_].type)
            {
               case "everyDayTask":
                  _loc3_.children()[_loc5_].@rDate = (_loc4_[_loc5_] as EveryDayTaskVO).receiveTaskDate;
                  break;
               case "limitingTimeTask":
                  break;
               case "accumulatedTask":
               case "limitingTimeAccumulatedTask":
                  _loc3_.children()[_loc5_].@currentTaskCount = (_loc4_[_loc5_] as AccumulatedTaskVO).currentTaskCount;
                  break;
               default:
                  throw new Error();
            }
            _loc3_.children()[_loc5_].@dDate = (_loc4_[_loc5_] as AccumulatedTaskVO).deteDate;
            _loc5_++;
         }
         return _loc3_;
      }
      
      private function createFarmXML(param1:String) : XML
      {
         var _loc3_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc3_.@sunValue = FarmData.getInstance().currentSunValue;
         if(FarmData.getInstance().sceneID != 1000)
         {
            _loc3_.@sceneID = FarmData.getInstance().sceneID;
         }
         if(FarmData.getInstance().farmBlockIDs.length > 1)
         {
            _loc3_.@bk = MyFunction.getInstance().combineIdArray(FarmData.getInstance().farmBlockIDs);
         }
         if(FarmData.getInstance().recoverLandNum)
         {
            _loc3_.@rLD = FarmData.getInstance().recoverLandDate;
            _loc3_.@rLN = FarmData.getInstance().recoverLandNum;
         }
         var _loc4_:int = 0;
         var _loc2_:int = int(FarmData.getInstance().ownerLands.length);
         _loc4_ = 0;
         for(; _loc4_ < _loc2_; _loc4_++)
         {
            _loc3_.appendChild(<land />);
            _loc3_.children()[_loc4_].@id = FarmData.getInstance().ownerLands[_loc4_].id;
            _loc3_.children()[_loc4_].@state = FarmData.getInstance().ownerLands[_loc4_].state;
            switch(FarmData.getInstance().ownerLands[_loc4_].state)
            {
               case 1:
               case 2:
               case 3:
               case 4:
                  _loc3_.children()[_loc4_].@eqID = FarmData.getInstance().ownerLands[_loc4_].equipmentIDInLand;
                  switch(FarmData.getInstance().ownerLands[_loc4_].state)
                  {
                     case 1:
                     case 4:
                        break;
                     default:
                        continue;
                  }
                  break;
            }
            _loc3_.children()[_loc4_].@date = FarmData.getInstance().ownerLands[_loc4_].date;
         }
         _loc2_ = int(FarmData.getInstance().otherShowObjects.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_.appendChild(<other />);
            _loc3_.other[_loc4_].@id = FarmData.getInstance().otherShowObjects[_loc4_].id;
            _loc3_.other[_loc4_].@hv = FarmData.getInstance().otherShowObjects[_loc4_].hI + "_" + FarmData.getInstance().otherShowObjects[_loc4_].vJ;
            _loc4_++;
         }
         return _loc3_;
      }
      
      private function createLDFXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc2_.@id = RefineFactoryData.getInstance().lianDanFurnace.id;
         _loc2_.@state = RefineFactoryData.getInstance().lianDanFurnace.state;
         switch(RefineFactoryData.getInstance().lianDanFurnace.state)
         {
            case 0:
               break;
            case 1:
               _loc2_.@date = RefineFactoryData.getInstance().lianDanFurnace.date;
               _loc2_.appendChild(<material />);
               _loc2_.material.@id = RefineFactoryData.getInstance().materialID;
               _loc2_.material.@num = RefineFactoryData.getInstance().materialNum;
               _loc2_.appendChild(<target />);
               _loc2_.target.@id = RefineFactoryData.getInstance().refineTargetID;
               _loc2_.target.@num = RefineFactoryData.getInstance().refineTargetNum;
               break;
            case 2:
               _loc2_.appendChild(<material />);
               _loc2_.material.@id = RefineFactoryData.getInstance().materialID;
               _loc2_.material.@num = RefineFactoryData.getInstance().materialNum;
               _loc2_.appendChild(<target />);
               _loc2_.target.@id = RefineFactoryData.getInstance().refineTargetID;
               _loc2_.target.@num = RefineFactoryData.getInstance().refineTargetNum;
               break;
            default:
               throw new Error();
         }
         if(RefineFactoryData.getInstance().atOnceCompleteNum)
         {
            _loc2_.@cRD = RefineFactoryData.getInstance().atOnceCompleteDate;
            _loc2_.@cRN = RefineFactoryData.getInstance().atOnceCompleteNum;
         }
         return _loc2_;
      }
      
      private function createBuffXML(param1:String) : XML
      {
         var child:String = param1;
         var createBuffOneXML:* = function(param1:String, param2:Vector.<BuffDrive>):XML
         {
            var _loc5_:int = 0;
            var _loc4_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
            if(!param2)
            {
               return _loc4_;
            }
            var _loc3_:int = int(param2.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc4_.appendChild(<item />);
               _loc4_.children()[_loc5_].@id = param2[_loc5_].buffVO.id;
               switch(param2[_loc5_].buffVO.type)
               {
                  case "allTimeBuff":
                     _loc4_.children()[_loc5_].@time = (param2[_loc5_].buffVO as AllTimeBuffVO).totalTime;
                     _loc4_.children()[_loc5_].@date = (param2[_loc5_].buffVO as AllTimeBuffVO).startDate;
                     break;
                  case "onlyOnLineBuff":
                     _loc4_.children()[_loc5_].@remTim = (param2[_loc5_].buffVO as OnlyOnLineBuffVO).saveRemainTime;
                     break;
                  case "noTimeBuff":
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               _loc5_++;
            }
            return _loc4_;
         };
         var xml:XML = new XML("<" + (child + " ") + "></" + (child + " ") + ">");
         if(BuffData.getInstance().buffDrives.length)
         {
            xml.appendChild(createBuffOneXML("pu",BuffData.getInstance().buffDrives));
         }
         if(BuffData.getInstance().buffDrives_playerOne.length)
         {
            xml.appendChild(createBuffOneXML("one",BuffData.getInstance().buffDrives_playerOne));
         }
         if(GamingUI.getInstance().player2 && BuffData.getInstance().buffDrives_playerTwo.length)
         {
            xml.appendChild(createBuffOneXML("two",BuffData.getInstance().buffDrives_playerTwo));
         }
         return xml;
      }
      
      private function createProtectXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc2_.@gD = ProtectData.getInstance().getGiftDate;
         return _loc2_;
      }
      
      private function createSignXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "></" + (param1 + " ") + ">");
         _loc2_.@num = SignData.getInstance().currentSignNum;
         _loc2_.@signD = SignData.getInstance().currentSignDate;
         _loc2_.@gAD = SignData.getInstance().getSignAwardDate;
         return _loc2_;
      }
      
      private function createOtherXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "/>");
         _loc2_.@unAbleBuyEqs = UnAbleBuyEquipmentData.getInstance().getUnAbleBuyEquipmentIdStrs();
         _loc2_.appendChild(<cheatData />);
         if(CheatData.getInstance().changeNumData)
         {
            _loc2_.cheatData[0].appendChild(<changeNumData />);
            _loc2_.cheatData[0].changeNumData[0].@changeNum = CheatData.getInstance().changeNumData.changeNum;
            _loc2_.cheatData[0].changeNumData[0].@originalNum = CheatData.getInstance().changeNumData.originalNum;
         }
         return _loc2_;
      }
      
      private function createVIPXML(param1:String, param2:VipVO) : XML
      {
         var _loc3_:XML = new XML("<" + (param1 + " ") + "/>");
         _loc3_.@oldDateGetGift = param2.oldDateGetGift;
         return _loc3_;
      }
      
      private function createMirageXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + " />");
         _loc2_.@mirageDate = MirageData.getInstance().mirageDate;
         _loc2_.@mirageNum = MirageData.getInstance().currentMirageNum;
         return _loc2_;
      }
      
      private function createPKXML(param1:String) : XML
      {
         DetectionClass.getInstance().detectionPKData();
         var _loc2_:XML = new XML("<" + (param1 + " ") + " />");
         _loc2_.@allMatch = PlayerDataForPK.getInstance().allMatch;
         _loc2_.@winMatch = PlayerDataForPK.getInstance().winMatch;
         _loc2_.@failMatch = PlayerDataForPK.getInstance().failMatch;
         _loc2_.@pkPoint = PlayerDataForPK.getInstance().pkPoint;
         _loc2_.@allMatch2 = PlayerDataForPK.getInstance().allMatchForTwoPlayer;
         _loc2_.@winMatch2 = PlayerDataForPK.getInstance().winMatchForTwoPlayer;
         _loc2_.@failMatch2 = PlayerDataForPK.getInstance().failMatchForTwoPlayer;
         _loc2_.@matchD2 = PlayerDataForPK.getInstance().matchDateForTwoPlayer;
         _loc2_.@isReseted = PlayerDataForPK.getInstance().isReseted;
         _loc2_.@winMonth = PlayerDataForPK.getInstance().winMonthMatch;
         _loc2_.@isSignUp2 = int(PlayerDataForPK.getInstance().isSignUpForDouble);
         _loc2_.@isSignUp = int(PlayerDataForPK.getInstance().isSignUpForSingle);
         _loc2_.@monthTime = PlayerDataForPK.getInstance().monthResetTime;
         _loc2_.@mingrenTwoPlayer = PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer;
         return _loc2_;
      }
      
      private function createEGDXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + " />");
         _loc2_.@gGN = !!ExchangeGiftData.getInstance().exchangeGiftNames ? MyFunction.getInstance().combineStringsToArr(ExchangeGiftData.getInstance().exchangeGiftNames) : "";
         return _loc2_;
      }
      
      private function createNicknameDataXML(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "/>");
         if(NicknameData.getInstance().myNicknameRankListId)
         {
            _loc2_.@rId = NicknameData.getInstance().myNicknameRankListId;
         }
         return _loc2_;
      }
      
      private function createNicknameErrorXML(param1:String) : XML
      {
         var _loc3_:XML = null;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         if(Boolean(NicknameData.getInstance().nicknameSaveData) && NicknameData.getInstance().nicknameSaveData.length)
         {
            _loc3_ = new XML("<" + (param1 + " ") + " />");
            _loc2_ = int(NicknameData.getInstance().nicknameSaveData.length);
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc3_.appendChild(<NNameData />);
               _loc3_.children()[_loc4_].@rankId = NicknameData.getInstance().nicknameSaveData[_loc4_].currentRankId;
               _loc3_.children()[_loc4_].@cN = NicknameData.getInstance().nicknameSaveData[_loc4_].currentChangeNicknameNum;
               _loc3_.children()[_loc4_].@cNN = NicknameData.getInstance().nicknameSaveData[_loc4_].currentNickname;
               _loc3_.children()[_loc4_].@error = NicknameData.getInstance().nicknameSaveData[_loc4_].errorString;
               _loc4_++;
            }
         }
         return _loc3_;
      }
      
      private function createRGXML(param1:String) : XML
      {
         var _loc2_:XML = null;
         _loc2_ = new XML("<" + (param1 + " ") + "/>");
         _loc2_.@rGNum = RecaptureGoldData.getInstance().rGNum;
         _loc2_.@rGDate = RecaptureGoldData.getInstance().rGDate;
         _loc2_.@buyNum = RecaptureGoldData.getInstance().buyNum;
         return _loc2_;
      }
      
      private function createEXEXML(param1:String) : XML
      {
         var _loc2_:XML = null;
         _loc2_ = new XML("<" + (param1 + " ") + "/>");
         _loc2_.@haveNum = ExEPlayerData.getInstance().haveExeNum;
         _loc2_.@exeNum = ExEPlayerData.getInstance().exeNum;
         return _loc2_;
      }
      
      private function createOnLineGiftBag(param1:String) : XML
      {
         var _loc2_:XML = null;
         _loc2_ = new XML("<" + (param1 + " ") + "/>");
         _loc2_.@remainTime = OnLineGiftBagData.getInstance().exportSaveRemainTime() / ProgramStartData.getInstance().getOneThousand();
         _loc2_.@id = OnLineGiftBagData.getInstance().currentGiftBagId;
         _loc2_.@dayTime = OnLineGiftBagData.getInstance().getDayTimeStr();
         if(OnLineGiftBagData.getInstance().getRecyleNum)
         {
            _loc2_.@rN = OnLineGiftBagData.getInstance().getRecyleNum;
         }
         return _loc2_;
      }
      
      private function createCollectTime(param1:String) : XML
      {
         var _loc2_:XML = new XML("<" + (param1 + " ") + "/>\r\n\t\t\t");
         if(CollectTimeSaveData.getInstance().getStartTime())
         {
            _loc2_.@startTime = CollectTimeSaveData.getInstance().getStartTime();
         }
         return _loc2_;
      }
      
      private function tranSkillVOsToXMLList(param1:Vector.<SkillVO>) : XMLList
      {
         var _loc2_:PetPassiveSkillVO = null;
         var _loc4_:XML = <root></root>;
         var _loc5_:int = 0;
         var _loc3_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(param1[_loc5_])
            {
               _loc2_ = param1[_loc5_] as PetPassiveSkillVO;
               _loc4_.appendChild(<passiveSkill />);
               _loc4_.passiveSkill[_loc5_].@id = _loc2_.id;
               _loc4_.passiveSkill[_loc5_].@promoteValue = _loc2_.promoteValue;
            }
            _loc5_++;
         }
         return _loc4_.children();
      }
      
      private function tranAwakeSkillVOsToXMLList(param1:Vector.<SkillVO>) : XMLList
      {
         var _loc2_:PetAwakeSkillVO = null;
         var _loc4_:XML = <root></root>;
         var _loc5_:int = 0;
         var _loc3_:int = !!param1 ? param1.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(param1[_loc5_])
            {
               _loc2_ = param1[_loc5_] as PetAwakeSkillVO;
               _loc4_.appendChild(<awakeSkill/>);
               _loc4_.awakeSkill[_loc5_].@id = _loc2_.id;
            }
            _loc5_++;
         }
         return _loc4_.children();
      }
      
      private function createActivityKillNum() : XML
      {
         var _loc1_:XML = <SantaClaus />;
         _loc1_.@killNum = ActivityManager.getInstance().getKillNum();
         return _loc1_;
      }
      
      private function createHuanLeTurnTimes() : XML
      {
         var _loc1_:XML = <Times />;
         _loc1_.@pet = HuanLeZhuanPanData.getInstance().m_turnTimes[0];
         _loc1_.@domonWill = HuanLeZhuanPanData.getInstance().m_turnTimes[1];
         _loc1_.@clothe = HuanLeZhuanPanData.getInstance().m_turnTimes[2];
         _loc1_.@weapon = HuanLeZhuanPanData.getInstance().m_turnTimes[3];
         _loc1_.@precious = HuanLeZhuanPanData.getInstance().m_turnTimes[4];
         return _loc1_;
      }
      
      private function createActivityDoubleEggNum() : XML
      {
         var _loc1_:XML = <DoubleEggNum />;
         _loc1_.@doubleEggNum = ActivityManager.getInstance().getDoubleEggNum();
         _loc1_.@doubleEggGetTime = ActivityManager.getInstance().getDoubleEggTime;
         return _loc1_;
      }
   }
}

