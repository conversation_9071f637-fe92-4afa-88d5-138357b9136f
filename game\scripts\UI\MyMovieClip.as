package UI
{
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MyMovieClip extends MovieClip
   {
      public function MyMovieClip()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearDisplayObjectInContainer(this);
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

