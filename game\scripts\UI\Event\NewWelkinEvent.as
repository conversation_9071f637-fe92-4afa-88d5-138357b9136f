package UI.Event
{
   import flash.events.Event;
   
   public class NewWelkinEvent extends Event
   {
      public static const HIDE:String = "hide";
      
      public static const SHOW:String = "show";
      
      public var data:*;
      
      public function NewWelkinEvent(param1:String, param2:* = null, param3:Boolean = false, param4:<PERSON>olean = false)
      {
         this.data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new NewWelkinEvent(type,data,bubbles,cancelable);
      }
   }
}

