package UI.SkillCells
{
   import UI.Cell;
   import UI.Skills.Skill;
   import UI.Skills.SkillVO;
   import UI.UIInterface.OldInterface.ISkillCell;
   import UI.UIInterface.OldInterface.ISkillCellBackground;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class SkillCell extends Cell implements ISkillCell
   {
      protected var _child:SkillVO;
      
      protected var _skill:Skill;
      
      public function SkillCell()
      {
         super();
      }
      
      public function get skillCellBackground() : ISkillCellBackground
      {
         return null;
      }
      
      public function addSkillVO(param1:SkillVO) : void
      {
         removeSkillVO();
         if(!isHaveChild && param1 != null)
         {
            if(_skill)
            {
               _skill.clear();
            }
            _skill = null;
            _child = param1;
            _skill = new Skill(_child);
            _skill.x = 0;
            _skill.y = 0;
            addChild(_skill);
            isHaveChild = true;
         }
      }
      
      override public function clear() : void
      {
         if(_skill)
         {
            _skill.clear();
         }
         _skill = null;
         _child = null;
         super.clear();
      }
      
      public function removeSkillVO(param1:SkillVO = null) : void
      {
         if(isHaveChild)
         {
            if(param1 == _child || param1 == null)
            {
               if(_skill)
               {
                  _skill.clear();
               }
               _skill = null;
               _child = null;
               isHaveChild = false;
            }
         }
      }
      
      public function get child() : SkillVO
      {
         return _child;
      }
      
      public function get skill() : Skill
      {
         return _skill;
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      protected function rollOver(param1:MouseEvent) : void
      {
         if(parent)
         {
            parent.setChildIndex(this,parent.numChildren - 1);
         }
      }
      
      protected function rollOut(param1:MouseEvent) : void
      {
      }
      
      override protected function addToStage(param1:Event) : void
      {
         addEventListener("rollOver",rollOver,false,0,true);
         addEventListener("rollOut",rollOut,false,0,true);
         addEventListener("removedFromStage",removeFromStage,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("rollOver",rollOver);
         removeEventListener("rollOut",rollOut);
         removeEventListener("removedFromStage",removeFromStage);
      }
   }
}

