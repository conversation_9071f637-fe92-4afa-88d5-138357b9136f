package UI.WorldBoss.AnimationQueueData
{
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.PlayEndListener;
   import YJFY.Utils.ClearUtil;
   
   public class AnimationQueueData
   {
      private var m_animationDatas:Vector.<AnimationData>;
      
      public var playEndFun:Function;
      
      private var m_isPlay:Boolean;
      
      private var m_playAnimationDataListeners:Vector.<IPlayAnimationDataListener>;
      
      public function AnimationQueueData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_animationDatas);
         m_animationDatas = null;
         playEndFun = null;
         ClearUtil.nullArr(m_playAnimationDataListeners,false,false,false);
         m_playAnimationDataListeners = null;
      }
      
      public function addAnimationData(param1:AnimationData) : void
      {
         if(m_animationDatas == null)
         {
            m_animationDatas = new Vector.<AnimationData>();
         }
         m_animationDatas.push(param1);
      }
      
      public function playAnimation() : void
      {
         if(m_animationDatas == null || m_animationDatas.length == 0)
         {
            end();
            return;
         }
         if(m_animationDatas[0] == null)
         {
            m_animationDatas.splice(0,1);
            playAnimation();
            return;
         }
         var _loc1_:PlayEndListener = new PlayEndListener();
         _loc1_.playEndFun = playAnimationDataEnd;
         _loc1_.animationData = m_animationDatas[0];
         m_animationDatas[0].addPlayEndListener(_loc1_);
         m_animationDatas[0].playAnimation();
         playAnimationData(m_animationDatas[0]);
         m_isPlay = true;
      }
      
      private function playAnimationDataEnd() : void
      {
         if(m_animationDatas == null || m_animationDatas.length == 0)
         {
            return;
         }
         m_animationDatas[0].clear();
         m_animationDatas.splice(0,1);
         if(m_animationDatas.length == 0)
         {
            end();
            return;
         }
         playAnimation();
      }
      
      public function getIsPlay() : Boolean
      {
         return m_isPlay;
      }
      
      private function end() : void
      {
         m_isPlay = false;
         if(Boolean(playEndFun))
         {
            playEndFun();
         }
      }
      
      public function addPlayAnimationDataListener(param1:IPlayAnimationDataListener) : void
      {
         if(m_playAnimationDataListeners == null)
         {
            m_playAnimationDataListeners = new Vector.<IPlayAnimationDataListener>();
         }
         m_playAnimationDataListeners.push(param1);
      }
      
      public function removePlayAnimationDataListener(param1:IPlayAnimationDataListener) : void
      {
         if(m_playAnimationDataListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_playAnimationDataListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_playAnimationDataListeners.splice(_loc2_,1);
            _loc2_ = int(m_playAnimationDataListeners.indexOf(param1));
         }
      }
      
      private function playAnimationData(param1:AnimationData) : void
      {
         var _loc4_:int = 0;
         var _loc2_:Vector.<IPlayAnimationDataListener> = !!m_playAnimationDataListeners ? m_playAnimationDataListeners.slice(0) : null;
         var _loc3_:int = !!_loc2_ ? _loc2_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].playAnimationData(param1);
            }
            _loc4_++;
         }
      }
   }
}

