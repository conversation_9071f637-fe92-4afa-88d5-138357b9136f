package UI.WorldBoss
{
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class GetWeekNum
   {
      public function GetWeekNum()
      {
         super();
      }
      
      public function getWeekNum(param1:String, param2:String, param3:Number, param4:WeekData) : void
      {
         var _loc5_:Number = new TimeUtil().timeInterval(param1,param2);
         param4.cycleLenght = param3;
         param4.cycleNum = _loc5_ / param3;
         param4.timeInCycle = _loc5_ - param4.cycleNum * param4.cycleLenght;
      }
   }
}

