package UI.TaskPanel
{
   import UI.MyFunction2;
   import UI.TaskPanel.Bar.HuanYingXingQiuBar;
   import UI.TaskPanel.Bar.ITaskBar;
   import UI.TaskPanel.Bar.LiuErMiHouBar;
   import UI.TaskPanel.Bar.NewYearTaskBar;
   import UI.TaskPanel.Bar.NuiBoWanJinDuBar;
   import UI.TaskPanel.Bar.SignTaskBar;
   import YJFY.Utils.ClearUtil;
   
   public class TaskProgressBarFactory
   {
      private var _allProgressBarClass:Array;
      
      public function TaskProgressBarFactory()
      {
         super();
         init();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_allProgressBarClass);
         _allProgressBarClass = null;
      }
      
      private function init() : void
      {
         _allProgressBarClass = [NuiBoWanJinDuBar,NewYearTaskBar,SignTaskBar,LiuErMiHouBar,HuanYingXingQiuBar];
      }
      
      public function createTaskBar(param1:String) : ITaskBar
      {
         var _loc2_:ITaskBar = null;
         var _loc3_:Class = MyFunction2.returnClassByClassName(param1);
         _loc2_ = new _loc3_();
         if(_loc2_ == null)
         {
            throw new Error();
         }
         return _loc2_;
      }
   }
}

