package UI.Players
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.SocietySystem.SocietyDataVO;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.EndlessMode.EndlessManage;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   
   public class Player extends MySprite
   {
      private static const path:String = "UI.Player";
      
      private var _playerVO:PlayerVO;
      
      private var _vipVO:VipVO;
      
      private var _societyDataVO:SocietyDataVO;
      
      private var _PKMode2VO1:PKMode2VO;
      
      private var _mountsVO:MountsVO;
      
      private var _pkVO:PKVO;
      
      private var listenerList:Array = [];
      
      private var _lastTimeCode:Number;
      
      private var _isStop:Boolean;
      
      private var _decValue:int;
      
      private var _addValue:int;
      
      private var _currentChangeValue:int;
      
      private var _nextStopAddTime:Number = 0;
      
      private var _addDelay:Number;
      
      private var automaticInvincibleTimeCd:Number = 0;
      
      private var automaticVampireTimeCd:Number = 0;
      
      protected var _binaryEn:binaryEncrypt = new binaryEncrypt();
      
      protected var _antiwear:Antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
      
      private var m_playerListeners:Vector.<IPlayerListener>;
      
      public function Player(param1:PlayerVO)
      {
         _antiwear._lastTime = _lastTimeCode;
         super();
         _playerVO = param1;
         _playerVO.player = this;
         m_playerListeners = new Vector.<IPlayerListener>();
         _decValue = int(XMLSingle.getInstance().dataXML.NuQi[0].@decValuePerS);
         _addValue = int(XMLSingle.getInstance().dataXML.NuQi[0].@addValuePerS);
         _addDelay = int(XMLSingle.getInstance().dataXML.NuQi[0].@addDelay) * 1000;
      }
      
      public function get _lastTime() : Number
      {
         return _antiwear._lastTime;
      }
      
      public function set _lastTime(param1:Number) : void
      {
         _antiwear._lastTime = param1;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(_pkVO);
         _pkVO = null;
         ClearUtil.clearObject(_mountsVO);
         _mountsVO = null;
         ClearUtil.clearObject(_societyDataVO);
         _societyDataVO = null;
         ClearUtil.clearObject(_PKMode2VO1);
         _PKMode2VO1 = null;
         ClearUtil.clearObject(_vipVO);
         _vipVO = null;
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         destory();
         _playerVO.clear();
         ClearUtil.nullArr(m_playerListeners,false,false,false);
         m_playerListeners = null;
      }
      
      public function getPKVO() : PKVO
      {
         return _pkVO;
      }
      
      public function setPKVO(param1:PKVO) : void
      {
         _pkVO = param1;
      }
      
      public function getMountsVO() : MountsVO
      {
         return _mountsVO;
      }
      
      public function setMountsVO(param1:MountsVO) : void
      {
         _mountsVO = param1;
         if(_playerVO)
         {
            _playerVO.setMountsVO(_mountsVO);
         }
      }
      
      public function getSocietyDataVO() : SocietyDataVO
      {
         return _societyDataVO;
      }
      
      public function setsocietyDataVO(param1:SocietyDataVO) : void
      {
         _societyDataVO = param1;
      }
      
      public function getPKMode2VO1() : PKMode2VO
      {
         return _PKMode2VO1;
      }
      
      public function setPKMode2VO1(param1:PKMode2VO) : void
      {
         _PKMode2VO1 = param1;
      }
      
      public function addNuQiValue() : void
      {
         _nextStopAddTime = Part1.getInstance().getEnterFrameTime().getGameTimeForThisInit() + _addDelay;
      }
      
      public function useNuQi() : Boolean
      {
         if(_playerVO.nuQiCurrentValue < _playerVO.nuQiAllValue)
         {
            return false;
         }
         _playerVO.nuQiCurrentValue = 0;
         return true;
      }
      
      public function get playerVO() : PlayerVO
      {
         return _playerVO;
      }
      
      public function set playerVO(param1:PlayerVO) : void
      {
         _playerVO = param1;
         _playerVO.player = this;
      }
      
      public function get vipVO() : VipVO
      {
         return _vipVO;
      }
      
      public function set vipVO(param1:VipVO) : void
      {
         _vipVO = param1;
      }
      
      public function stopGame() : void
      {
         _lastTime = NaN;
         _isStop = true;
      }
      
      public function continueGame() : void
      {
         _isStop = false;
         _lastTime = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc4_:int = 0;
         var _loc2_:Boolean = false;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         if(_isStop)
         {
            return;
         }
         if(isNaN(_lastTime))
         {
            _lastTime = param1.getGameTimeForThisInit();
         }
         if(param1.getGameTimeForThisInit() - _lastTime >= 1000)
         {
            _loc4_ = (param1.getGameTimeForThisInit() - _lastTime) / 1000;
            _lastTime = param1.getGameTimeForThisInit();
            if(playerVO.regHp && playerVO.bloodPercent < 1 && playerVO.bloodPercent > 0 && !EndlessManage.getInstance().stopRehpAndmp)
            {
               playerVO.bloodPercent = (playerVO.bloodPercent * playerVO.bloodVolume + playerVO.regHp * _loc4_) / playerVO.bloodVolume;
               _loc2_ = true;
            }
            if(playerVO.regMp && playerVO.magicPercent < 1 && playerVO.bloodPercent > 0 && !EndlessManage.getInstance().stopRehpAndmp)
            {
               playerVO.magicPercent = (playerVO.magicPercent * playerVO.maxMagic + playerVO.regMp * _loc4_) / playerVO.maxMagic;
               _loc2_ = true;
            }
            _loc3_ = int(_playerVO.skillVOs.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               if(_playerVO.skillVOs[_loc5_] is PlayerActiveSkillVO)
               {
                  (_playerVO.skillVOs[_loc5_] as PlayerActiveSkillVO).currentCDTime -= _loc4_;
               }
               _loc5_++;
            }
            if(param1.getGameTimeForThisInit() > _nextStopAddTime)
            {
               _currentChangeValue = _decValue;
            }
            else
            {
               _currentChangeValue = _addValue;
            }
            if(_playerVO.nuQiCurrentValue < _playerVO.nuQiAllValue)
            {
               _playerVO.nuQiCurrentValue += _currentChangeValue * _loc4_;
               _loc2_ = true;
            }
            if(playerVO.pet)
            {
               playerVO.pet.render(_loc4_);
            }
            if(_loc2_)
            {
               GamingUI.getInstance().refresh(128);
            }
         }
         if(_playerVO.isInvincible && _playerVO.endInvincibleTime < param1.getGameTimeForThisInit())
         {
            _playerVO.isInvincible = false;
         }
         updateAutomaciInvincible();
         if(_playerVO.isVampire && _playerVO.endVampireTime < param1.getGameTimeForThisInit())
         {
            _playerVO.isVampire = false;
         }
         updateAutomaticVampireBuff();
      }
      
      private function updateAutomaciInvincible() : void
      {
         if(_playerVO.invincibleCD > 0 && _playerVO.bloodPercent <= 0.25 && _playerVO.bloodPercent > 0)
         {
            if(!_playerVO.isInvincible && automaticInvincibleTimeCd < GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit())
            {
               _playerVO.isInvincible = true;
               _playerVO.endInvincibleTime = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit() + _playerVO.invincibleCD * AntiwearNumber.nums["consts_100"] * AntiwearNumber.nums["consts_10"];
               automaticInvincibleTimeCd = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit() + AntiwearNumber.nums["consts_60"] * 1000;
            }
         }
      }
      
      private function updateAutomaticVampireBuff() : void
      {
         if(_playerVO.vampireCD > 0 && _playerVO.bloodPercent <= _playerVO.vampireBloodLimit && _playerVO.bloodPercent > 0)
         {
            if(!_playerVO.isVampire && automaticVampireTimeCd < GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit())
            {
               _playerVO.isVampire = true;
               _playerVO.endVampireTime = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit() + _playerVO.vampireDuration * AntiwearNumber.nums["consts_100"] * AntiwearNumber.nums["consts_10"];
               automaticVampireTimeCd = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit() + _playerVO.vampireCD * AntiwearNumber.nums["consts_100"] * AntiwearNumber.nums["consts_10"];
            }
         }
      }
      
      public function addPlayerListener(param1:IPlayerListener) : void
      {
         ListenerUtil.addListener(m_playerListeners,param1);
      }
      
      public function removePlayerListener(param1:IPlayerListener) : void
      {
         ListenerUtil.removeListener(m_playerListeners,param1);
      }
      
      public function changeData() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IPlayerListener> = m_playerListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].changeData(this);
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
   }
}

