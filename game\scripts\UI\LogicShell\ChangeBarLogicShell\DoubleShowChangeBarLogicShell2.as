package UI.LogicShell.ChangeBarLogicShell
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import YJFY.ShowLogicShell.ChangeBarLogicShell.DoubleShowChangeBarLogicShell2;
   
   public class DoubleShowChangeBarLogicShell2 extends YJFY.ShowLogicShell.ChangeBarLogicShell.DoubleShowChangeBarLogicShell2
   {
      public function DoubleShowChangeBarLogicShell2()
      {
         super();
         setFont(new FangZhengKaTongJianTi());
      }
   }
}

