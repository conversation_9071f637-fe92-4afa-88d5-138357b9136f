package UI.Farm
{
   import UI.DataManagerParent;
   import UI.Farm.Land.LandVO;
   
   public class FarmData extends DataManagerParent
   {
      private static var _instance:FarmData = null;
      
      public var ownerLands:Vector.<LandVO>;
      
      public var otherShowObjects:Vector.<FarmShowObjectVO>;
      
      public var farmBlockIDs:Vector.<int>;
      
      private var _sceneID:int;
      
      private var _currentSunValue:int;
      
      private var _recoverLandDate:String;
      
      private var _recoverLandNum:int;
      
      public function FarmData()
      {
         if(!_instance)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : FarmData
      {
         if(!_instance)
         {
            _instance = new FarmData();
         }
         return _instance;
      }
      
      public function get sceneID() : int
      {
         return _antiwear.sceneID;
      }
      
      public function set sceneID(param1:int) : void
      {
         _antiwear.sceneID = param1;
      }
      
      public function get currentSunValue() : int
      {
         return _antiwear.currentSunValue;
      }
      
      public function set currentSunValue(param1:int) : void
      {
         _antiwear.currentSunValue = param1;
      }
      
      public function get recoverLandDate() : String
      {
         return _antiwear.recoverLandDate;
      }
      
      public function set recoverLandDate(param1:String) : void
      {
         _antiwear.recoverLandDate = param1;
      }
      
      public function get recoverLandNum() : int
      {
         return _antiwear.recoverLandNum;
      }
      
      public function set recoverLandNum(param1:int) : void
      {
         _antiwear.recoverLandNum = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.sceneID = _sceneID;
         _antiwear.currentSunValue = _currentSunValue;
         _antiwear.recoverLandDate = _recoverLandDate;
         _antiwear.recoverLandNum = _recoverLandNum;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         if(ownerLands)
         {
            _loc1_ = int(ownerLands.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               ownerLands[_loc2_] = null;
               _loc2_++;
            }
            ownerLands = null;
         }
         if(otherShowObjects)
         {
            _loc1_ = int(otherShowObjects.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               otherShowObjects[_loc2_] = null;
               _loc2_++;
            }
            otherShowObjects = null;
         }
         if(farmBlockIDs)
         {
            _loc1_ = int(farmBlockIDs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               farmBlockIDs[_loc2_] = null;
               _loc2_++;
            }
            farmBlockIDs = null;
         }
         _instance = null;
      }
   }
}

