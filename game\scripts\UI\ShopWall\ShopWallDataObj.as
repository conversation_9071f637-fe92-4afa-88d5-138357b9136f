package UI.ShopWall
{
   import YJFY.GameData;
   
   public class ShopWallDataObj
   {
      public static const PROP_ID:String = "propId";
      
      public static const COUNT:String = "count";
      
      public static const PRICE:String = "price";
      
      public static const IDX:String = "idx";
      
      public static const TAG:String = "tag";
      
      public static const E_ID:String = "eId";
      
      public static const MSG:String = "msg";
      
      public static const PROP_TYPE:String = "propType";
      
      public static const BALANCE:String = "balance";
      
      public var propId:String;
      
      public var count:int;
      
      public var price:int;
      
      public var idx:int;
      
      public var tag:String;
      
      public var eId:String;
      
      public var msg:String;
      
      public var propType:String;
      
      public var balance:String;
      
      public function ShopWallDataObj()
      {
         super();
      }
      
      public function setBuyPropAttri(param1:String, param2:int, param3:int, param4:String) : void
      {
         this.propId = param1;
         this.count = param2;
         this.price = param3;
         this.idx = GameData.getInstance().getSaveFileData().index;
         this.tag = param4;
      }
   }
}

