package UI.WorldBoss
{
   import UI.DataManagerParent;
   import UI.MyFunction2;
   
   public class BossHurtData extends DataManagerParent
   {
      private var m_bossId:String;
      
      private var m_hurt:uint;
      
      private var m_hurtNum:uint;
      
      private var m_fightNum:uint;
      
      private var m_rewardDate:String = "";
      
      public function BossHurtData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.bossId = m_bossId;
         _antiwear.hurt = m_hurt;
         _antiwear.hurtNum = m_hurtNum;
         _antiwear.fightNum = m_fightNum;
         _antiwear.rewardDate = m_rewardDate;
      }
      
      private function get bossId() : String
      {
         return _antiwear.bossId;
      }
      
      private function set bossId(param1:String) : void
      {
         _antiwear.bossId = param1;
      }
      
      private function get hurt() : uint
      {
         return _antiwear.hurt;
      }
      
      private function set hurt(param1:uint) : void
      {
         _antiwear.hurt = param1;
      }
      
      private function get hurtNum() : uint
      {
         return _antiwear.hurtNum;
      }
      
      private function set hurtNum(param1:uint) : void
      {
         _antiwear.hurtNum = param1;
      }
      
      private function get fightNum() : uint
      {
         return _antiwear.fightNum;
      }
      
      private function set fightNum(param1:uint) : void
      {
         _antiwear.fightNum = param1;
      }
      
      private function get rewardDate() : String
      {
         return _antiwear.rewardDate;
      }
      
      private function set rewardDate(param1:String) : void
      {
         _antiwear.rewardDate = param1;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         bossId = String(param1.@id);
         hurt = uint(param1.@hurt);
         hurtNum = uint(param1.@hurtNum);
         fightNum = uint(param1.@fightNum);
         rewardDate = MyFunction2.resetErrorTime(String(param1.@rDate));
      }
      
      public function exportXML() : XML
      {
         var _loc1_:XML = <boss />;
         _loc1_.@id = bossId;
         _loc1_.@hurt = hurt;
         _loc1_.@hurtNum = hurtNum;
         _loc1_.@fightNum = fightNum;
         _loc1_.@rDate = rewardDate;
         return _loc1_;
      }
      
      public function getBossId() : String
      {
         return bossId;
      }
      
      public function setBossId(param1:String) : void
      {
         bossId = param1;
      }
      
      public function getHurt() : uint
      {
         return hurt;
      }
      
      public function getHurtNum() : uint
      {
         return hurtNum;
      }
      
      public function getFigthNum() : uint
      {
         return fightNum;
      }
      
      public function addHurt(param1:uint, param2:uint) : void
      {
         hurt += param1;
         hurtNum += param2;
         fightNum++;
      }
      
      public function getRewardDate() : String
      {
         return rewardDate;
      }
      
      public function setRewardDate(param1:String) : void
      {
         rewardDate = param1;
      }
   }
}

