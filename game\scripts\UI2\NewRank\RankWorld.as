package UI2.NewRank
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.InitCompleteListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SaveData;
   import YJFY.GameData;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.LocalPKRankListAPI;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.utils.MD5;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.text.Font;
   import flash.text.TextField;
   
   public class RankWorld extends MySprite
   {
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/openrank";
      
      private var m_strRecordUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/pkrecord";
      
      private var m_strPkUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/pk";
      
      private var m_strPrizeUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/pkprize";
      
      private var m_strNoMyUrl:String = "http://sgxz.aiwan4399.com:8601/record/web/getrank";
      
      private var m_verification:String = "http://save.api.4399.com/?ac=user_auth";
      
      private var m_show:MovieClip;
      
      private var m_btnClose:ButtonLogicShell2;
      
      private var m_switchBtnGroupPlayer:SwitchBtnGroupLogicShell;
      
      private var m_btnplayer1:MySwitchBtnLogicShell;
      
      private var m_btnplayer2:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroupRole:SwitchBtnGroupLogicShell;
      
      private var m_role_1:MySwitchBtnLogicShell;
      
      private var m_role_2:MySwitchBtnLogicShell;
      
      private var m_role_3:MySwitchBtnLogicShell;
      
      private var m_role_4:MySwitchBtnLogicShell;
      
      private var m_role_5:MySwitchBtnLogicShell;
      
      private var m_role_6:MySwitchBtnLogicShell;
      
      private var m_role_7:MySwitchBtnLogicShell;
      
      private var m_role_8:MySwitchBtnLogicShell;
      
      private var m_role_9:MySwitchBtnLogicShell;
      
      private var m_textYestRank:TextField;
      
      private var m_btnGetReward:MovieClip;
      
      private var m_imghead:MovieClip;
      
      private var m_btnUp:ButtonLogicShell2;
      
      private var m_btnDown:ButtonLogicShell2;
      
      private var m_btnScroll:MovieClip;
      
      private var m_list:Vector.<MovieClip>;
      
      private var m_rewardmc:Vector.<MovieClip>;
      
      private var m_equiplist:Vector.<Equipment>;
      
      private var m_playerlist:Vector.<Player>;
      
      private var m_textNowRank:TextField;
      
      private var m_textMaxRank:TextField;
      
      private var m_textFightNum:TextField;
      
      private var m_grademc:MovieClip;
      
      private var m_rulemc:ButtonLogicShell2;
      
      private var m_allrank:ButtonLogicShell2;
      
      private var m_equipBtn:Vector.<ButtonLogicShell2>;
      
      private var m_fightbtn:Vector.<ButtonLogicShell2>;
      
      private var m_record:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_RankWorldXML:XML;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_curplayer:Player;
      
      private var m_rankListEquipListener:RankListAPIListener;
      
      private var m_localRankListAPI:LocalPKRankListAPI;
      
      private var m_playerData:Object;
      
      private var m_uid:String;
      
      private var m_idx:int;
      
      private var m_equipUid:String;
      
      private var m_equipIdx:int;
      
      private var m_equipitem:ListItem;
      
      private var m_isMyRead:Boolean;
      
      private var m_isFoeRead:Boolean;
      
      private var m_nowFile:int;
      
      private var m_initMode:int;
      
      private var m_isMyWin:Boolean;
      
      private var m_shownum:int = 7;
      
      private var m_bDown:Boolean = false;
      
      private var m_maxy:int = 464;
      
      private var m_miny:int = 177;
      
      private var m_distance:int = 287;
      
      private var m_ndiv:int;
      
      private var m_ntotalpage:int;
      
      private var m_navg:int;
      
      private var m_curpage:int;
      
      private var m_curfightuid:Number;
      
      private var m_curfightidx:int;
      
      private var m_curFightSid:Number;
      
      private var m_otherPanel:PKPanelOne;
      
      private var m_lookPlayer:InitPlayersData;
      
      private var m_layer:Sprite;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_rankpk:RankPK;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var m_itemx:Number = 587.1;
      
      private var m_itemy:Number = 147;
      
      private var m_itemheigh:Number = 60;
      
      private var m_blank:Number = 410.95;
      
      private var m_heightotal:Number;
      
      private var m_dvlist:Number;
      
      private var m_moveAvg:Number = 60;
      
      private var m_textLoad:TextField;
      
      private var font:Font;
      
      private var m_itemlist:Vector.<EquipmentVO>;
      
      private var currentRole:String = "";
      
      private var UserId:String = "";
      
      private var UserName:String = "";
      
      private var NickName:String = "";
      
      private var Time:String = "";
      
      private var Token:String = "";
      
      public function RankWorld(param1:RankPK)
      {
         super();
         m_rankpk = param1;
         font = new FangZhengKaTongJianTi();
         m_playerData = {};
         m_layer = new Sprite();
         addChild(m_layer);
         m_equipBtn = new Vector.<ButtonLogicShell2>();
         m_fightbtn = new Vector.<ButtonLogicShell2>();
         m_btnClose = new ButtonLogicShell2();
         m_btnUp = new ButtonLogicShell2();
         m_btnDown = new ButtonLogicShell2();
         m_allrank = new ButtonLogicShell2();
         m_rulemc = new ButtonLogicShell2();
         m_itemlist = new Vector.<EquipmentVO>();
         m_switchBtnGroupPlayer = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_btnplayer1 = new MySwitchBtnLogicShell();
         m_btnplayer2 = new MySwitchBtnLogicShell();
         m_switchBtnGroupRole = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_role_1 = new MySwitchBtnLogicShell();
         m_role_2 = new MySwitchBtnLogicShell();
         m_role_3 = new MySwitchBtnLogicShell();
         m_role_4 = new MySwitchBtnLogicShell();
         m_role_5 = new MySwitchBtnLogicShell();
         m_role_6 = new MySwitchBtnLogicShell();
         m_role_7 = new MySwitchBtnLogicShell();
         m_role_8 = new MySwitchBtnLogicShell();
         m_role_9 = new MySwitchBtnLogicShell();
         m_list = new Vector.<MovieClip>();
         m_rewardmc = new Vector.<MovieClip>();
         m_playerlist = new Vector.<Player>();
         m_equiplist = new Vector.<Equipment>();
         equipmentVOsData = new EquipmentVOsData();
         m_enterFrameTime = new EnterFrameTime();
         m_enterFrameTime.init(new Date().getTime());
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_record = new ButtonLogicShell2();
         m_rankListEquipListener = new RankListAPIListener();
         m_rankListEquipListener.getSaveDataSuccessFun = getSaveEquipSuccess;
         m_rankListEquipListener.getSaveDataErrorFun = getSaveDataError;
         addEventListener("clickButton",clickButton,true,0,true);
         m_localRankListAPI = new LocalPKRankListAPI();
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(m_localRankListAPI);
         m_initMode = 2777;
      }
      
      override public function clear() : void
      {
         m_btnScroll.removeEventListener("mouseDown",callMoveDown);
         m_btnScroll.removeEventListener("enterFrame",UpdateMove);
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_itemlist);
         m_itemlist = null;
         ClearUtil.clearObject(font);
         font = null;
         ClearUtil.clearObject(m_rulemc);
         m_rulemc = null;
         ClearUtil.clearObject(m_allrank);
         m_allrank = null;
         ClearUtil.clearObject(m_btnUp);
         m_btnUp = null;
         ClearUtil.clearObject(m_btnDown);
         m_btnDown = null;
         ClearUtil.clearObject(m_switchBtnGroupRole);
         m_switchBtnGroupRole = null;
         ClearUtil.clearObject(m_switchBtnGroupPlayer);
         m_switchBtnGroupPlayer = null;
         ClearUtil.clearObject(m_btnplayer1);
         m_btnplayer1 = null;
         ClearUtil.clearObject(m_btnplayer2);
         m_btnplayer2 = null;
         ClearUtil.clearObject(m_equipBtn);
         m_equipBtn = null;
         ClearUtil.clearObject(m_fightbtn);
         m_fightbtn = null;
         ClearUtil.clearObject(m_record);
         m_record = null;
         ClearUtil.clearObject(m_btnClose);
         m_btnClose = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_rankListEquipListener);
         m_rankListEquipListener = null;
         ClearUtil.clearObject(m_RankWorldXML);
         m_RankWorldXML = null;
         m_curplayer = null;
         m_equipitem = null;
         ClearUtil.clearObject(m_otherPanel);
         m_otherPanel = null;
         ClearUtil.clearObject(m_lookPlayer);
         m_lookPlayer = null;
         ClearUtil.clearObject(m_layer);
         m_layer = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         m_myLoader = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_list);
         m_list = null;
         ClearUtil.clearObject(m_playerlist);
         m_playerlist = null;
         ClearUtil.clearObject(m_localRankListAPI);
         m_localRankListAPI = null;
         ClearUtil.nullObject(m_playerData);
         m_playerData = null;
         m_loadUI = null;
         m_versionControl = null;
         m_rankpk = null;
         super.clear();
      }
      
      private function getSaveDataError(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("获取存档失败");
         trace("获取存档失败, errorMessage:",param1);
      }
      
      private function initPlayerData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         if(m_playerData[param1] == null)
         {
            m_playerData[param1] = [];
         }
         var _loc5_:InitPlayersData = new InitPlayersData();
         m_playerData[param1][param2] = _loc5_;
         _loc5_.uid = param1;
         _loc5_.idx = param2;
         param4.addTarget = _loc5_;
         _loc5_.addInitCompleteListener(param4);
         _loc5_.initPlayerData(param3,param2,m_initMode,true);
      }
      
      private function getOnePlayerData(param1:String, param2:int) : InitPlayersData
      {
         if(m_playerData[param1] == null)
         {
            return null;
         }
         return m_playerData[param1][param2];
      }
      
      public function clearOnePlayerData(param1:InitPlayersData) : void
      {
         if(m_playerData[param1.uid] == null)
         {
            return;
         }
         if(m_playerData[param1.uid][param1.idx] == null)
         {
            return;
         }
         m_playerData[param1.uid][param1.idx] = null;
         ClearUtil.clearObject(param1);
         param1 = null;
      }
      
      private function fight() : void
      {
         if(RankDataInfo.getInstance().getReamin() > 0)
         {
            m_textLoad.visible = true;
            m_layer.mouseChildren = false;
            m_layer.mouseEnabled = false;
            m_rankpk.getMySaveData();
         }
         else
         {
            showWarningBox("次数不足，是否花费" + String(RankDataInfo.getInstance().getprice()) + "点卷进行挑战",3,{
               "type":"buydianjuan",
               "okFunction":buy2
            });
         }
      }
      
      private function buy2() : void
      {
         var price:uint = uint(RankDataInfo.getInstance().getprice());
         var ticketId:String = String(RankDataInfo.getInstance().getpriceid());
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买点卷";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            RankDataInfo.getInstance().setAddRemain();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            m_textFightNum.text = String(RankDataInfo.getInstance().getReamin());
            m_rankpk.getMySaveData();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      public function init2(param1:MovieClip) : void
      {
         m_show = param1;
         m_layer.addChild(param1);
         this.initShow();
         this.registerBtn();
         RankDataInfo.getInstance().setNowPlayerType(GamingUI.getInstance().player1.playerVO.playerID);
         this.changePlayer(RankDataInfo.getInstance().getNowPlayerType());
         RankDataInfo.getInstance().setNowRankId(1);
         Verification();
      }
      
      public function getShow() : Sprite
      {
         return m_layer;
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_RankWorldXML = param1.resultXML;
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function Verification() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "正在验证信息， 请勿关闭游戏！";
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderVerificationCompleteHandler);
         _loc1_.addEventListener("ioError",onVerificationErrorHandler);
         _loc1_.addEventListener("securityError",securityVerificationErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_verification);
         _loc2_.method = "POST";
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.gameId = "100021366";
         _loc3_.userId = GameData.getInstance().getLoginReturnData().getUid();
         _loc3_.userName = GameData.getInstance().getLoginReturnData().getName();
         _loc2_.data = _loc3_;
         _loc1_.load(_loc2_);
      }
      
      private function onLoaderVerificationCompleteHandler(param1:Event) : void
      {
         var _loc3_:Array = null;
         param1.currentTarget.removeEventListener("complete",onLoaderVerificationCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onVerificationErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityVerificationErrorHandler);
         var _loc2_:String = String(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         if("Error: auth error." == _loc2_)
         {
            GamingUI.getInstance().showMessageTip("用户信息校验失败");
            closecallback();
         }
         else if("Error: user id error." == _loc2_)
         {
            GamingUI.getInstance().showMessageTip("用户ID错误");
            closecallback();
         }
         else if("Error: game id error." == _loc2_)
         {
            GamingUI.getInstance().showMessageTip("游戏ID错误");
            closecallback();
         }
         else
         {
            _loc3_ = _loc2_.split("|");
            UserId = String(_loc3_[0]);
            UserName = String(_loc3_[1]);
            NickName = String(_loc3_[2]);
            Time = String(_loc3_[3]);
            Token = String(_loc3_[4]);
            sendInfo();
         }
      }
      
      private function onVerificationErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderVerificationCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onVerificationErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityVerificationErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function securityVerificationErrorHandler(param1:SecurityErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderVerificationCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onVerificationErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityVerificationErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function sendInfo() : void
      {
         var _loc3_:URLLoader = null;
         var _loc4_:URLRequest = null;
         var _loc6_:Object = null;
         var _loc2_:Object = null;
         var _loc1_:Number = NaN;
         var _loc5_:URLVariables = null;
         var _loc7_:int = 0;
         if(RankDataInfo.getInstance().getNowPlayerType() == "playerOne")
         {
            m_curplayer = GamingUI.getInstance().player1;
            _loc7_ = 1;
         }
         else if(RankDataInfo.getInstance().getNowPlayerType() == "playerTwo")
         {
            m_curplayer = GamingUI.getInstance().player2;
            _loc7_ = 2;
         }
         if(m_curplayer == null)
         {
            return;
         }
         if(RankDataInfo.getInstance().getNowRankId() == 1 || RankDataInfo.getInstance().getNowRankId() == getRoleId(m_curplayer.playerVO.playerType))
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            GamingUI.getInstance().manBan.text.text = "信息加载中， 请勿关闭游戏！";
            _loc3_ = new URLLoader();
            _loc3_.addEventListener("complete",onLoaderCompleteHandler);
            _loc3_.addEventListener("ioError",onErrorHandler);
            _loc3_.addEventListener("securityError",securityErrorHandler);
            _loc4_ = new URLRequest(this.m_strOpenUrl);
            _loc4_.method = "POST";
            _loc6_ = {};
            _loc6_.RankID = RankDataInfo.getInstance().getNowRankId();
            _loc6_.UserId = UserId;
            _loc6_.UserName = UserName;
            _loc6_.NickName = NickName;
            _loc6_.Time = Time;
            _loc6_.Token = Token;
            _loc2_ = {};
            _loc1_ = Number(GameData.getInstance().getLoginReturnData().getUid()) * 100 + GameData.getInstance().getSaveFileData().index * 10 + _loc7_;
            _loc2_.Sid = _loc1_;
            _loc2_.RankID = getRoleId(m_curplayer.playerVO.playerType);
            _loc2_.Role = m_curplayer.playerVO.playerType;
            _loc2_.Name = GameData.getInstance().getLoginReturnData().getNickname();
            _loc6_.P1 = _loc2_;
            RankDataInfo.getInstance().setMySid(_loc1_);
            _loc5_ = new URLVariables();
            _loc5_.JSON = MyJSON.encode(_loc6_);
            _loc5_.MD5 = MD5.hash(MyJSON.encode(_loc6_) + "dd9c8b2f13cadff77e0984e863470cd3");
            _loc4_.data = _loc5_;
            _loc3_.load(_loc4_);
         }
         else
         {
            nomyrefresh();
            sendNoMy();
         }
      }
      
      private function nomyrefresh() : void
      {
         m_grademc.visible = true;
         m_grademc.gotoAndStop(9);
         ClearUtil.clearObject(m_equiplist);
         m_equiplist = null;
         m_btnGetReward.gotoAndStop(3);
         m_btnGetReward.mouseEnabled = false;
         m_textNowRank.text = "无";
         m_textMaxRank.text = "无";
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 7)
         {
            m_list[_loc1_].visible = false;
            _loc1_++;
         }
      }
      
      private function sendNoMy() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderNoMyCompleteHandler);
         _loc1_.addEventListener("ioError",onErroNoMyrHandler);
         _loc1_.addEventListener("securityError",securityNoMyErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strNoMyUrl);
         _loc2_.method = "POST";
         var _loc4_:Object = {};
         _loc4_.RankID = RankDataInfo.getInstance().getNowRankId();
         _loc4_.Begin = 1;
         _loc4_.End = 100;
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.JSON = MyJSON.encode(_loc4_);
         _loc3_.MD5 = MD5.hash(MyJSON.encode(_loc4_) + "dd9c8b2f13cadff77e0984e863470cd3");
         _loc2_.data = _loc3_;
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderNoMyCompleteHandler(param1:Event) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",onLoaderNoMyCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErroNoMyrHandler);
         param1.currentTarget.removeEventListener("securityError",securityNoMyErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().initNoMy(_loc2_);
         m_btnScroll.y = m_miny;
         m_list[0].y = m_itemy;
         _loc3_ = 1;
         while(_loc3_ < m_list.length)
         {
            m_list[_loc3_].y = m_list[0].y + m_itemheigh * _loc3_;
            _loc3_++;
         }
         refreshlist();
         refreshreward();
      }
      
      protected function onErroNoMyrHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderNoMyCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErroNoMyrHandler);
         param1.currentTarget.removeEventListener("securityError",securityNoMyErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function securityNoMyErrorHandler(param1:SecurityErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderNoMyCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErroNoMyrHandler);
         param1.currentTarget.removeEventListener("securityError",securityNoMyErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler);
         Part1.getInstance().hideGameWaitShow();
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().initData(_loc2_);
         m_curpage = 0;
         if(RankDataInfo.getInstance().getLength() % 7 == 0)
         {
            this.m_ntotalpage = RankDataInfo.getInstance().getLength() / 7;
         }
         else
         {
            this.m_ntotalpage = RankDataInfo.getInstance().getLength() / 7 + 1;
         }
         this.m_ndiv = this.m_ntotalpage;
         this.m_navg = (m_maxy - m_miny) / m_ndiv;
         m_btnScroll.y = m_miny;
         if(RankDataInfo.getInstance().getYestGrade() != null)
         {
            m_textYestRank.text = RankDataInfo.getInstance().getYestGrade() + "(第" + RankDataInfo.getInstance().getYestRank() + "名)";
         }
         else
         {
            m_textYestRank.text = "无";
         }
         m_list[0].y = m_itemy;
         _loc3_ = 1;
         while(_loc3_ < m_list.length)
         {
            m_list[_loc3_].y = m_list[0].y + m_itemheigh * _loc3_;
            _loc3_++;
         }
         m_textMaxRank.text = String(RankDataInfo.getInstance().getMaxRank());
         m_textNowRank.text = String(RankDataInfo.getInstance().getCurRank());
         m_textFightNum.text = String(RankDataInfo.getInstance().getReamin());
         m_grademc.visible = true;
         m_grademc.gotoAndStop(RankDataInfo.getInstance().getGrade());
         m_btnGetReward.mouseEnabled = true;
         m_btnGetReward.gotoAndStop(1);
         refreshlist();
         refreshreward();
      }
      
      private function refreshreward() : void
      {
         var _loc5_:Equipment = null;
         var _loc1_:EquipmentVO = null;
         var _loc4_:NewRankItem = null;
         var _loc3_:NewRankItem = null;
         var _loc6_:int = 0;
         var _loc2_:Boolean = false;
         ClearUtil.clearObject(m_equiplist);
         m_equiplist = null;
         m_equiplist = new Vector.<Equipment>();
         if(RankDataInfo.getInstance().getNowRankId() == 1)
         {
            _loc4_ = RankDataInfo.getInstance().getRewardList();
            if(_loc4_ != null)
            {
               if(RankDataInfo.getInstance().getNowRankId() == 1)
               {
                  _loc2_ = false;
                  _loc6_ = 0;
                  while(_loc6_ < _loc4_.itemlist.length)
                  {
                     if(_loc4_.itemlist[_loc6_] != null)
                     {
                        _loc1_ = _loc4_.itemlist[_loc6_].clone();
                        _loc5_ = MyFunction2.sheatheEquipmentShell(_loc1_);
                        m_rewardmc[_loc6_].addChild(_loc5_);
                        m_equiplist.push(_loc5_);
                        _loc2_ = true;
                     }
                     _loc6_++;
                  }
                  if(_loc2_ == true && RankDataInfo.getInstance().getIsGet() == false)
                  {
                     m_btnGetReward.gotoAndStop(1);
                     m_btnGetReward.mouseEnabled = true;
                  }
                  else
                  {
                     m_btnGetReward.gotoAndStop(3);
                     m_btnGetReward.mouseEnabled = false;
                  }
               }
               else
               {
                  m_btnGetReward.gotoAndStop(3);
                  m_btnGetReward.mouseEnabled = false;
               }
            }
            else
            {
               m_btnGetReward.gotoAndStop(3);
               m_btnGetReward.mouseEnabled = false;
            }
         }
         else
         {
            _loc3_ = RankDataInfo.getInstance().getRewarRoleList(currentRole);
            if(_loc3_ != null && m_curplayer != null && this.getRoleId(m_curplayer.playerVO.playerType) == RankDataInfo.getInstance().getNowRankId())
            {
               _loc2_ = false;
               _loc6_ = 0;
               while(_loc6_ < _loc3_.itemlist.length)
               {
                  if(_loc3_.itemlist[_loc6_] != null)
                  {
                     _loc1_ = _loc3_.itemlist[_loc6_].clone();
                     _loc5_ = MyFunction2.sheatheEquipmentShell(_loc1_);
                     m_rewardmc[_loc6_].addChild(_loc5_);
                     m_equiplist.push(_loc5_);
                     _loc2_ = true;
                  }
                  _loc6_++;
               }
               if(_loc2_ == true && RankDataInfo.getInstance().getIsGet() == false)
               {
                  m_btnGetReward.gotoAndStop(1);
                  m_btnGetReward.mouseEnabled = true;
               }
               else
               {
                  m_btnGetReward.gotoAndStop(3);
                  m_btnGetReward.mouseEnabled = false;
               }
            }
            else
            {
               m_btnGetReward.gotoAndStop(3);
               m_btnGetReward.mouseEnabled = false;
            }
         }
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler);
         trace("错误:",param1.toString());
         Part1.getInstance().hideGameWaitShow();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function securityErrorHandler(param1:SecurityErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler);
         trace("错误:",param1.toString());
         Part1.getInstance().hideGameWaitShow();
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function securityRecordErrorHandler(param1:SecurityErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRecordCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRecordHandler);
         param1.currentTarget.removeEventListener("securityError",securityRecordErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function initShow() : void
      {
         var _loc1_:MovieClip = null;
         var _loc2_:ButtonLogicShell2 = null;
         m_btnClose.setShow(m_show["closebtn"] as MovieClip);
         m_btnClose.setTipString("关闭");
         m_grademc = m_show["greademc"] as MovieClip;
         m_grademc.gotoAndStop(1);
         m_textLoad = m_show["loading"] as TextField;
         m_textLoad.visible = false;
         m_rulemc.setShow(m_show["rulemc"] as MovieClip);
         m_rulemc.setTipString("查看规则");
         m_allrank.setShow(m_show["mcrankall"] as MovieClip);
         m_allrank.setTipString("查看总排行");
         m_btnplayer1.setShow(m_show["btnplayer_1"] as MovieClip);
         m_btnplayer2.setShow(m_show["btnplayer_2"] as MovieClip);
         m_btnplayer1.setTipString("玩家一");
         m_btnplayer2.setTipString("玩家二");
         m_btnplayer1.turnActivate();
         m_btnplayer2.turnUnActivate();
         m_switchBtnGroupPlayer.addSwitchBtn(m_btnplayer1);
         m_switchBtnGroupPlayer.addSwitchBtn(m_btnplayer2);
         m_role_1.setShow(m_show["btnrole_1"] as MovieClip);
         m_role_2.setShow(m_show["btnrole_2"] as MovieClip);
         m_role_3.setShow(m_show["btnrole_3"] as MovieClip);
         m_role_4.setShow(m_show["btnrole_4"] as MovieClip);
         m_role_5.setShow(m_show["btnrole_5"] as MovieClip);
         m_role_6.setShow(m_show["btnrole_6"] as MovieClip);
         m_role_7.setShow(m_show["btnrole_7"] as MovieClip);
         m_role_8.setShow(m_show["btnrole_8"] as MovieClip);
         m_role_9.setShow(m_show["btnrole_9"] as MovieClip);
         m_role_1.setTipString("孙悟空");
         m_role_2.setTipString("白龙马");
         m_role_3.setTipString("二郎神");
         m_role_4.setTipString("嫦娥");
         m_role_5.setTipString("灵狐");
         m_role_6.setTipString("铁扇");
         m_role_7.setTipString("后羿");
         m_role_8.setTipString("总榜");
         m_role_9.setTipString("紫霞仙子");
         m_role_1.data = "SunWuKong";
         m_role_2.data = "BaiLongMa";
         m_role_3.data = "ErLangShen";
         m_role_4.data = "ChangE";
         m_role_5.data = "Fox";
         m_role_6.data = "TieShan";
         m_role_7.data = "Houyi";
         m_role_8.data = "";
         m_role_9.data = "ZiXia";
         m_role_1.turnUnActivate();
         m_role_2.turnUnActivate();
         m_role_3.turnUnActivate();
         m_role_4.turnUnActivate();
         m_role_5.turnUnActivate();
         m_role_6.turnUnActivate();
         m_role_7.turnUnActivate();
         m_role_8.turnActivate();
         m_role_9.turnUnActivate();
         m_switchBtnGroupRole.addSwitchBtn(m_role_1);
         m_switchBtnGroupRole.addSwitchBtn(m_role_2);
         m_switchBtnGroupRole.addSwitchBtn(m_role_3);
         m_switchBtnGroupRole.addSwitchBtn(m_role_4);
         m_switchBtnGroupRole.addSwitchBtn(m_role_5);
         m_switchBtnGroupRole.addSwitchBtn(m_role_6);
         m_switchBtnGroupRole.addSwitchBtn(m_role_7);
         m_switchBtnGroupRole.addSwitchBtn(m_role_8);
         m_switchBtnGroupRole.addSwitchBtn(m_role_9);
         m_textYestRank = m_show["yestrank"] as TextField;
         MyFunction2.changeTextFieldFont(font.fontName,m_textYestRank);
         m_btnGetReward = m_show["btnreward"] as MovieClip;
         m_btnGetReward.gotoAndStop(1);
         m_imghead = m_show["imgheadplayer"] as MovieClip;
         m_btnUp.setShow(m_show["upbtn"] as MovieClip);
         m_btnDown.setShow(m_show["downbtn"] as MovieClip);
         m_btnUp.setTipString("点击向上翻页");
         m_btnDown.setTipString("点击向下翻页");
         m_btnScroll = m_show["updownbtn"] as MovieClip;
         m_btnScroll.gotoAndStop(1);
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < 16)
         {
            _loc1_ = m_show["rankitem_" + (_loc3_ + 1)] as MovieClip;
            _loc1_.x = m_itemx;
            _loc1_.y = m_itemheigh * _loc3_ + m_itemy;
            m_list.push(_loc1_);
            _loc1_.visible = false;
            _loc1_.gotoAndStop(1);
            (_loc1_["equipbtn"] as MovieClip).gotoAndStop(1);
            (_loc1_["fightbtn"] as MovieClip).gotoAndStop(1);
            _loc2_ = new ButtonLogicShell2();
            m_equipBtn.push(_loc2_);
            _loc2_ = new ButtonLogicShell2();
            m_fightbtn.push(_loc2_);
            MyFunction2.changeTextFieldFont(font.fontName,_loc1_["nametext"] as TextField);
            MyFunction2.changeTextFieldFont(font.fontName,_loc1_["myranktext"] as TextField);
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < 5)
         {
            _loc1_ = m_show["rankreward_" + (_loc3_ + 1)] as MovieClip;
            m_rewardmc.push(_loc1_);
            _loc3_++;
         }
         m_textNowRank = m_show["ranknow"] as TextField;
         m_textMaxRank = m_show["rankgood"] as TextField;
         m_textFightNum = m_show["fightnum"] as TextField;
         MyFunction2.changeTextFieldFont(font.fontName,m_textNowRank);
         MyFunction2.changeTextFieldFont(font.fontName,m_textMaxRank);
         MyFunction2.changeTextFieldFont(font.fontName,m_textFightNum);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < m_equipBtn.length)
         {
            if(param1.button == m_equipBtn[_loc2_])
            {
               newShowEquip(param1);
               break;
            }
            _loc2_++;
         }
         _loc2_ = 0;
         while(_loc2_ < m_fightbtn.length)
         {
            if(param1.button == m_fightbtn[_loc2_])
            {
               newFight(param1);
               break;
            }
            _loc2_++;
         }
         if(m_record == param1.button)
         {
            sendRecordInfo();
         }
         if(m_btnClose == param1.button)
         {
            closecallback();
         }
         if(m_btnplayer1 == param1.button)
         {
            clickone();
         }
         if(m_btnplayer2 == param1.button)
         {
            clicktwo();
         }
         if(m_role_1 == param1.button)
         {
            currentRole = String(m_role_1.data);
            clickRole(2);
         }
         if(m_role_2 == param1.button)
         {
            currentRole = String(m_role_2.data);
            clickRole(3);
         }
         if(m_role_3 == param1.button)
         {
            currentRole = String(m_role_3.data);
            clickRole(4);
         }
         if(m_role_4 == param1.button)
         {
            currentRole = String(m_role_4.data);
            clickRole(5);
         }
         if(m_role_5 == param1.button)
         {
            currentRole = String(m_role_5.data);
            clickRole(6);
         }
         if(m_role_6 == param1.button)
         {
            currentRole = String(m_role_6.data);
            clickRole(7);
         }
         if(m_role_7 == param1.button)
         {
            currentRole = String(m_role_7.data);
            clickRole(8);
         }
         if(m_role_8 == param1.button)
         {
            currentRole = String(m_role_8.data);
            clickRole(1);
         }
         if(m_role_9 == param1.button)
         {
            currentRole = String(m_role_9.data);
            clickRole(9);
         }
         if(m_btnUp == param1.button)
         {
            clickup();
         }
         if(m_btnDown == param1.button)
         {
            clickdown();
         }
         if(m_allrank == param1.button)
         {
            m_layer.addChild(new NewAllRank());
            RankDataInfo.getInstance().setInType(2);
         }
         if(m_rulemc == param1.button)
         {
            m_layer.addChild(new RulePanel());
         }
      }
      
      private function registerBtn() : void
      {
         m_layer.addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         m_btnGetReward.addEventListener("click",callBackClick);
         m_btnScroll.addEventListener("mouseDown",callMoveDown);
         m_btnScroll.addEventListener("enterFrame",UpdateMove);
      }
      
      private function callMoveOut(param1:MouseEvent) : void
      {
         m_bDown = false;
         if(m_layer)
         {
            m_layer.removeEventListener("mouseUp",callMoveUp);
         }
      }
      
      private function callMoveDown(param1:MouseEvent) : void
      {
         m_bDown = true;
         if(m_layer)
         {
            m_layer.addEventListener("mouseUp",callMoveUp);
         }
      }
      
      private function callMoveUp(param1:MouseEvent) : void
      {
         m_bDown = false;
         if(m_layer)
         {
            m_layer.removeEventListener("mouseUp",callMoveUp);
         }
      }
      
      private function UpdateMove(param1:Event) : void
      {
         var _loc2_:int = 0;
         if(m_bDown)
         {
            if((mouseX < m_btnScroll.x - 21 || mouseX > m_btnScroll.x + 21) && (mouseY > m_miny && mouseY < m_maxy))
            {
               m_btnScroll.y = mouseY;
            }
            else if(mouseY < m_miny)
            {
               m_btnScroll.y = m_miny;
            }
            else if(mouseY > m_maxy)
            {
               m_btnScroll.y = m_maxy;
            }
            else
            {
               m_btnScroll.y = mouseY;
            }
            m_heightotal = m_itemheigh * RankDataInfo.getInstance().getLength();
            if(m_heightotal < m_blank)
            {
               m_dvlist = 0;
            }
            else
            {
               m_dvlist = (m_heightotal - m_blank) / m_distance * (m_btnScroll.y - m_miny);
            }
            m_list[0].y = m_itemy - m_dvlist;
            _loc2_ = 1;
            while(_loc2_ < m_list.length)
            {
               m_list[_loc2_].y = m_list[0].y + m_itemheigh * _loc2_;
               _loc2_++;
            }
         }
      }
      
      private function closecallback() : void
      {
         m_layer.removeEventListener("mouseUp",callMoveUp);
         m_layer.removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
         Part1.getInstance().closeNewRank();
         Part1.getInstance().returnCity();
      }
      
      private function clickone() : void
      {
         if(RankDataInfo.getInstance().getNowPlayerType() != "playerOne")
         {
            RankDataInfo.getInstance().setNowPlayerType("playerOne");
            RankDataInfo.getInstance().setPlayerType(1);
            RankDataInfo.getInstance().setNowRankId(1);
            this.changePlayer(RankDataInfo.getInstance().getNowPlayerType());
            this.sendInfo();
         }
      }
      
      private function clicktwo() : void
      {
         if(GamingUI.getInstance().player2 != null)
         {
            if(RankDataInfo.getInstance().getNowPlayerType() != "playerTwo")
            {
               RankDataInfo.getInstance().setNowPlayerType("playerTwo");
               RankDataInfo.getInstance().setPlayerType(2);
               RankDataInfo.getInstance().setNowRankId(1);
               this.changePlayer(RankDataInfo.getInstance().getNowPlayerType());
               this.sendInfo();
            }
         }
         else
         {
            trace("当前为单人模式");
            GamingUI.getInstance().showMessageTip("当前为单人模式");
         }
      }
      
      private function clickRole(param1:int) : void
      {
         if(RankDataInfo.getInstance().getNowRankId() != param1)
         {
            RankDataInfo.getInstance().setNowRankId(param1);
            this.sendInfo();
         }
      }
      
      private function clickup() : void
      {
         var _loc1_:int = 0;
         m_heightotal = m_itemheigh * RankDataInfo.getInstance().getLength();
         if(m_heightotal > m_blank)
         {
            if(m_list[0].y + m_moveAvg > m_itemy)
            {
               m_list[0].y = m_itemy;
            }
            else
            {
               m_list[0].y += m_moveAvg;
            }
            _loc1_ = 1;
            while(_loc1_ < m_list.length)
            {
               m_list[_loc1_].y = m_list[0].y + m_itemheigh * _loc1_;
               _loc1_++;
            }
         }
      }
      
      private function clickdown() : void
      {
         var _loc1_:int = 0;
         m_heightotal = m_itemheigh * RankDataInfo.getInstance().getLength();
         if(m_heightotal > m_blank)
         {
            if(m_list[0].y - m_moveAvg < m_itemy - (m_heightotal - m_blank))
            {
               m_list[0].y = m_itemy - (m_heightotal - m_blank);
            }
            else
            {
               m_list[0].y -= m_moveAvg;
            }
            _loc1_ = 1;
            while(_loc1_ < m_list.length)
            {
               m_list[_loc1_].y = m_list[0].y + m_itemheigh * _loc1_;
               _loc1_++;
            }
         }
      }
      
      private function callBackClick(param1:MouseEvent) : void
      {
         var _loc3_:NewRankItem = null;
         var _loc4_:int = 0;
         var _loc2_:Player = null;
         if(String(param1.currentTarget.name) == "btnreward")
         {
            if(RankDataInfo.getInstance().getIsGet() == false)
            {
               if(RankDataInfo.getInstance().getNowRankId() == 1)
               {
                  _loc3_ = RankDataInfo.getInstance().getRewardList();
               }
               else
               {
                  _loc3_ = RankDataInfo.getInstance().getRewarRoleList(currentRole);
               }
               ClearUtil.clearObject(m_itemlist);
               m_itemlist = null;
               m_itemlist = new Vector.<EquipmentVO>();
               _loc4_ = 0;
               while(_loc4_ < _loc3_.itemlist.length)
               {
                  m_itemlist.push(_loc3_.itemlist[_loc4_].clone());
                  _loc4_++;
               }
               equipmentVOsData.setEquipmentVOs(m_itemlist);
               if(RankDataInfo.getInstance().getNowPlayerType() == "playerOne")
               {
                  _loc2_ = GamingUI.getInstance().player1;
               }
               else if(RankDataInfo.getInstance().getNowPlayerType() == "playerTwo")
               {
                  _loc2_ = GamingUI.getInstance().player2;
               }
               if(m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,_loc2_))
               {
                  m_btnGetReward.mouseEnabled = false;
                  m_btnGetReward.gotoAndStop(3);
                  GamingUI.getInstance().showMessageTip("领取成功");
                  sendPrizeInfo();
               }
               else
               {
                  GamingUI.getInstance().showMessageTip("背包已满");
               }
            }
            else
            {
               GamingUI.getInstance().showMessageTip("已领取");
            }
         }
      }
      
      private function sendPrizeInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderPrizeCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorPrizeHandler);
         _loc1_.addEventListener("securityError",securityPrizeErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strPrizeUrl);
         _loc2_.method = "POST";
         var _loc4_:Object = {};
         _loc4_.RankID = RankDataInfo.getInstance().getNowRankId();
         _loc4_.Sid = RankDataInfo.getInstance().getMySid();
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.JSON = MyJSON.encode(_loc4_);
         _loc3_.MD5 = MD5.hash(MyJSON.encode(_loc4_) + "dd9c8b2f13cadff77e0984e863470cd3");
         _loc2_.data = _loc3_;
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderPrizeCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderPrizeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorPrizeHandler);
         param1.currentTarget.removeEventListener("securityError",securityPrizeErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().setIsGet(true);
      }
      
      protected function onErrorPrizeHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderPrizeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorPrizeHandler);
         param1.currentTarget.removeEventListener("securityError",securityPrizeErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
         m_btnGetReward.mouseEnabled = true;
      }
      
      private function securityPrizeErrorHandler(param1:SecurityErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderPrizeCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorPrizeHandler);
         param1.currentTarget.removeEventListener("securityError",securityPrizeErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function getRoleId(param1:String) : int
      {
         var _loc2_:int = 0;
         if(param1 == "SunWuKong")
         {
            _loc2_ = 2;
         }
         else if(param1 == "BaiLongMa")
         {
            _loc2_ = 3;
         }
         else if(param1 == "ErLangShen")
         {
            _loc2_ = 4;
         }
         else if(param1 == "ChangE")
         {
            _loc2_ = 5;
         }
         else if(param1 == "Fox")
         {
            _loc2_ = 6;
         }
         else if(param1 == "TieShan")
         {
            _loc2_ = 7;
         }
         else if(param1 == "Houyi")
         {
            _loc2_ = 8;
         }
         else if(param1 == "ZiXia")
         {
            _loc2_ = 9;
         }
         return _loc2_;
      }
      
      private function getRoleByIndex(param1:int) : String
      {
         var _loc2_:String = null;
         if(param1 == 1)
         {
            _loc2_ = "SunWuKong";
         }
         else if(param1 == 2)
         {
            _loc2_ = "BaiLongMa";
         }
         else if(param1 == 3)
         {
            _loc2_ = "ErLangShen";
         }
         else if(param1 == 4)
         {
            _loc2_ = "ChangE";
         }
         else if(param1 == 5)
         {
            _loc2_ = "Fox";
         }
         else if(param1 == 6)
         {
            _loc2_ = "TieShan";
         }
         else if(param1 == 7)
         {
            _loc2_ = "Houyi";
         }
         return _loc2_;
      }
      
      private function changePlayer(param1:String) : void
      {
         if(param1 == "playerOne")
         {
            m_imghead.gotoAndStop(this.getRoleId(GamingUI.getInstance().player1.playerVO.playerType) - 1);
         }
         else if(param1 == "playerTwo")
         {
            m_imghead.gotoAndStop(this.getRoleId(GamingUI.getInstance().player2.playerVO.playerType) - 1);
         }
         m_role_1.turnUnActivate();
         m_role_2.turnUnActivate();
         m_role_3.turnUnActivate();
         m_role_4.turnUnActivate();
         m_role_5.turnUnActivate();
         m_role_6.turnUnActivate();
         m_role_7.turnUnActivate();
         m_role_8.turnActivate();
      }
      
      public function refreshlist() : void
      {
         var _loc3_:ListItem = null;
         var _loc2_:MovieClip = null;
         var _loc1_:TextField = null;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < 16)
         {
            m_list[_loc4_].visible = false;
            _loc4_++;
         }
         _loc4_ = 0;
         while(_loc4_ < 16)
         {
            _loc3_ = RankDataInfo.getInstance().getItemByIndex(_loc4_);
            if(_loc3_)
            {
               if(_loc3_.m_isMy == 2)
               {
                  m_list[_loc4_].visible = true;
                  m_list[_loc4_].gotoAndStop(1);
                  m_equipBtn[_loc4_].setShow(m_list[_loc4_]["equipbtn"] as MovieClip);
                  m_equipBtn[_loc4_].setTipString("查看详情");
                  _loc2_ = m_list[_loc4_]["fightbtn"] as MovieClip;
                  m_fightbtn[_loc4_].setShow(_loc2_);
                  m_fightbtn[_loc4_].setTipString("点击进入战斗");
                  if(_loc3_.m_isFight == 1)
                  {
                     _loc2_.visible = true;
                  }
                  else
                  {
                     _loc2_.visible = false;
                  }
                  if(_loc3_.m_rank >= 4)
                  {
                     _loc2_ = m_list[_loc4_]["rankmc"] as MovieClip;
                     _loc2_.visible = false;
                     _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                     _loc1_.text = String(_loc3_.m_rank);
                     _loc1_.visible = true;
                  }
                  else
                  {
                     _loc2_ = m_list[_loc4_]["rankmc"] as MovieClip;
                     _loc2_.gotoAndStop(_loc3_.m_rank);
                     _loc2_.visible = true;
                     _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                     _loc1_.visible = false;
                  }
                  _loc1_ = m_list[_loc4_]["nametext"] as TextField;
                  _loc1_.text = _loc3_.m_name;
                  _loc2_ = m_list[_loc4_]["imgheadplayer"] as MovieClip;
                  _loc2_.gotoAndStop(this.getRoleId(_loc3_.m_role) - 1);
               }
               else if(_loc3_.m_isMy == 1)
               {
                  if(this.getRoleId(_loc3_.m_role) == RankDataInfo.getInstance().getNowRankId() || RankDataInfo.getInstance().getNowRankId() == 1)
                  {
                     m_list[_loc4_].visible = true;
                     m_list[_loc4_].gotoAndStop(2);
                     m_equipBtn[_loc4_].setShow(m_list[_loc4_]["myequipbtn"] as MovieClip);
                     m_equipBtn[_loc4_].setTipString("查看详情");
                     _loc2_ = m_list[_loc4_]["recordfightbtn"] as MovieClip;
                     m_record.setShow(_loc2_);
                     m_record.setTipString("查看挑战记录");
                     _loc2_.visible = true;
                     _loc2_ = m_list[_loc4_]["imgheadplayer"] as MovieClip;
                     _loc2_.gotoAndStop(this.getRoleId(_loc3_.m_role) - 1);
                     if(_loc3_.m_rank >= 4)
                     {
                        _loc2_ = m_list[_loc4_]["myrankmc"] as MovieClip;
                        _loc2_.gotoAndStop(4);
                        _loc2_.visible = false;
                        _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                        _loc1_.text = String(_loc3_.m_rank);
                        _loc1_.visible = true;
                     }
                     else
                     {
                        _loc2_ = m_list[_loc4_]["myrankmc"] as MovieClip;
                        _loc2_.gotoAndStop(_loc3_.m_rank);
                        _loc2_.visible = true;
                        _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                        _loc1_.visible = false;
                     }
                     _loc1_ = m_list[_loc4_]["nametext"] as TextField;
                     _loc1_.text = _loc3_.m_name;
                  }
               }
               else if(_loc3_.m_isMy == 3)
               {
                  m_list[_loc4_].visible = true;
                  m_list[_loc4_].gotoAndStop(2);
                  m_equipBtn[_loc4_].setShow(m_list[_loc4_]["myequipbtn"] as MovieClip);
                  m_equipBtn[_loc4_].setTipString("查看详情");
                  _loc2_ = m_list[_loc4_]["recordfightbtn"] as MovieClip;
                  m_record.setShow(_loc2_);
                  m_record.setTipString("查看挑战记录");
                  _loc2_.visible = false;
                  _loc2_ = m_list[_loc4_]["imgheadplayer"] as MovieClip;
                  _loc2_.gotoAndStop(this.getRoleId(_loc3_.m_role) - 1);
                  if(_loc3_.m_rank >= 4)
                  {
                     _loc2_ = m_list[_loc4_]["myrankmc"] as MovieClip;
                     _loc2_.gotoAndStop(4);
                     _loc2_.visible = false;
                     _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                     _loc1_.text = String(_loc3_.m_rank);
                     _loc1_.visible = true;
                  }
                  else
                  {
                     _loc2_ = m_list[_loc4_]["myrankmc"] as MovieClip;
                     _loc2_.gotoAndStop(_loc3_.m_rank);
                     _loc2_.visible = true;
                     _loc1_ = m_list[_loc4_]["myranktext"] as TextField;
                     _loc1_.visible = false;
                  }
                  _loc1_ = m_list[_loc4_]["nametext"] as TextField;
                  _loc1_.text = _loc3_.m_name;
               }
            }
            _loc4_++;
         }
      }
      
      private function newShowEquip(param1:ButtonEvent) : void
      {
         var _loc2_:String = ((param1.target as MovieClip).parent as MovieClip).name;
         var _loc4_:int = int(_loc2_.split("_")[1]) - 1;
         var _loc3_:ListItem = RankDataInfo.getInstance().getItemByIndex(_loc4_);
         if(_loc3_ != null)
         {
            m_equipitem = _loc3_;
            m_equipUid = String(uint(_loc3_.m_sid / 100));
            m_equipIdx = _loc3_.m_sid / 10 % 10;
            getEquipSaveData(m_equipUid,m_equipIdx);
         }
      }
      
      private function getEquipSaveData(param1:String, param2:int) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListEquipListener);
         Part1.getInstance().getApi4399().rankListAPI.getSaveData(param1,param2);
      }
      
      private function getSaveEquipSuccess(param1:SaveData) : void
      {
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListEquipListener);
         getSaveXMLEquipSuccess(param1.getSaveData());
      }
      
      private function getSaveXMLEquipSuccess(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(m_playerData == null)
         {
            return;
         }
         var _loc2_:InitCompleteListener = new InitCompleteListener();
         _loc2_.initCompleteFun = initCompleteEquip;
         initPlayerEquipData(m_equipUid,m_equipIdx,param1,_loc2_);
      }
      
      private function initPlayerEquipData(param1:String, param2:int, param3:XML, param4:InitCompleteListener) : void
      {
         ClearUtil.clearObject(m_lookPlayer);
         m_lookPlayer = null;
         m_lookPlayer = new InitPlayersData();
         m_lookPlayer.uid = param1;
         m_lookPlayer.idx = param2;
         param4.addTarget = m_lookPlayer;
         m_lookPlayer.addInitCompleteListener(param4);
         m_lookPlayer.initPlayerData(param3,param2,m_initMode,true);
      }
      
      private function initCompleteEquip() : void
      {
         if(!m_otherPanel)
         {
            ClearUtil.clearObject(m_playerlist);
            m_playerlist.length = 0;
            if(m_equipitem.m_sid % 10 == 1)
            {
               m_lookPlayer.player1.playerVO.playerUid = m_lookPlayer.uid;
               m_playerlist.push(m_lookPlayer.player1);
               m_otherPanel = new PKPanelOne();
               m_otherPanel.isTwoMode = false;
               m_otherPanel.initPKPanel(m_playerlist,m_lookPlayer.nickNameData);
            }
            else if(m_lookPlayer.player2 != null)
            {
               if(m_equipitem.m_sid % 10 == 2)
               {
                  m_lookPlayer.player2.playerVO.playerUid = m_lookPlayer.uid;
                  m_playerlist.push(m_lookPlayer.player2);
                  m_otherPanel = new PKPanelOne();
                  m_otherPanel.isTwoMode = false;
                  m_otherPanel.initPKPanel(m_playerlist,m_lookPlayer.nickNameData);
               }
            }
         }
         if(m_otherPanel != null && !getChildByName(m_otherPanel.name))
         {
            m_layer.addChild(m_otherPanel);
         }
      }
      
      private function newFight(param1:ButtonEvent) : void
      {
         var _loc5_:String = ((param1.target as MovieClip).parent as MovieClip).name;
         var _loc6_:int = int(_loc5_.split("_")[1]);
         var _loc3_:ListItem = RankDataInfo.getInstance().getItemByIndex(_loc6_ - 1);
         var _loc2_:Number = _loc3_.m_sid;
         RankDataInfo.getInstance().setPreRank(RankDataInfo.getInstance().getCurRank());
         RankDataInfo.getInstance().setCurFightRank(_loc3_.m_rank);
         RankDataInfo.getInstance().setFightSid(_loc2_);
         m_curFightSid = _loc2_;
         _loc2_ /= 10;
         var _loc4_:int = _loc2_ % 10;
         _loc2_ /= 10;
         m_curfightuid = _loc2_;
         m_curfightidx = _loc4_;
         this.fight();
      }
      
      private function showrecord(param1:MouseEvent) : void
      {
         sendRecordInfo();
      }
      
      private function sendRecordInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderRecordCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorRecordHandler);
         _loc1_.addEventListener("securityError",securityRecordErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strRecordUrl);
         _loc2_.method = "POST";
         var _loc4_:Object = {};
         _loc4_.RankID = RankDataInfo.getInstance().getNowRankId();
         _loc4_.Sid = RankDataInfo.getInstance().getMySid();
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.JSON = MyJSON.encode(_loc4_);
         _loc3_.MD5 = MD5.hash(MyJSON.encode(_loc4_) + "dd9c8b2f13cadff77e0984e863470cd3");
         _loc2_.data = _loc3_;
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderRecordCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRecordCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRecordHandler);
         param1.currentTarget.removeEventListener("securityError",securityRecordErrorHandler);
         var _loc3_:Object = MyJSON.decode(param1.currentTarget.data);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         RankDataInfo.getInstance().initRecord(_loc3_);
         m_textMaxRank.text = String(RankDataInfo.getInstance().getMaxRank());
         var _loc2_:NewRankRecord = new NewRankRecord(this);
         _loc2_.init();
         m_layer.addChild(_loc2_);
      }
      
      protected function onErrorRecordHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderRecordCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorRecordHandler);
         param1.currentTarget.removeEventListener("securityError",securityRecordErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         GamingUI.getInstance().showMessageTip("接收数据错误");
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.clearObject(m_playerlist);
         m_playerlist.length = 0;
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_playerlist.push(param1);
         m_playerlist.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_playerlist,param3);
         m_layer.addChild(m_otherPlayerShowPanel);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(m_otherPlayerShowPanel)
         {
            ClearUtil.clearObject(m_otherPlayerShowPanel);
            m_otherPlayerShowPanel = null;
         }
         if(m_otherPanel)
         {
            ClearUtil.clearObject(m_otherPanel);
            m_otherPanel = null;
         }
      }
   }
}

