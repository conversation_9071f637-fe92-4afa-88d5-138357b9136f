package UI.Players.ImplicitProPlayer
{
   import UI.Players.PlayerVO;
   
   public class ImplicitProPlayer
   {
      protected var m_id:String;
      
      protected var m_isProPlayer:Boolean;
      
      protected var m_proDescription:String;
      
      protected var m_xml:XML;
      
      public function ImplicitProPlayer()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function initByXML(param1:XML) : void
      {
         m_xml = param1;
         m_id = String(param1.@id);
      }
      
      public function recoverPlayer(param1:PlayerVO) : void
      {
         m_isProPlayer = false;
      }
      
      public function judgeAndProPlayer(param1:PlayerVO) : void
      {
         m_isProPlayer = false;
      }
      
      public function getId() : String
      {
         return m_id;
      }
      
      public function isProPlayer() : Boolean
      {
         return false;
      }
      
      public function getProDescription() : String
      {
         return m_proDescription;
      }
   }
}

