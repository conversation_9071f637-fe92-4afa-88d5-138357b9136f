package UI.Privilege
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class PrivilegeVO
   {
      public var className:String;
      
      public var name:String;
      
      public var description:String;
      
      private var _id:int;
      
      private var _value:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function PrivilegeVO()
      {
         super();
         init();
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.value = _value;
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get value() : int
      {
         return _antiwear.value;
      }
      
      public function set value(param1:int) : void
      {
         _antiwear.value = param1;
      }
   }
}

