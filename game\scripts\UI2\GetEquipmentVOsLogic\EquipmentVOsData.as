package UI2.GetEquipmentVOsLogic
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import YJFY.Utils.ClearUtil;
   
   public class EquipmentVOsData implements IEquipmentVOsData
   {
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function EquipmentVOsData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
      }
      
      public function setEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         m_equipmentVOs = param1;
      }
      
      public function getEquipmentVONum() : uint
      {
         return !!m_equipmentVOs ? m_equipmentVOs.length : 0;
      }
      
      public function getEquipmentVOByIndex(param1:int) : EquipmentVO
      {
         return m_equipmentVOs[param1];
      }
   }
}

