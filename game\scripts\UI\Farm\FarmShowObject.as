package UI.Farm
{
   import UI.Event.UIPassiveEvent;
   import UI.Farm.CoordGrid.SceneCoordGrid;
   import UI.Farm.CoordGrid.SceneCoordGrids;
   import UI.MyMovieClip;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   
   public class FarmShowObject extends MyMovieClip implements IFarmShowObject
   {
      private var _farmShowObjectVO:FarmShowObjectVO;
      
      private var _listenerList:Array = [];
      
      public function FarmShowObject()
      {
         super();
         stop();
         addEventListener("rollOver",onOver,false,0,true);
         addEventListener("rollOut",onOut,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         destory();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _farmShowObjectVO = null;
         if(_listenerList)
         {
            _loc2_ = int(_listenerList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_listenerList[_loc3_])
               {
                  for(var _loc1_ in _listenerList[_loc3_])
                  {
                     _listenerList[_loc3_][_loc1_] = null;
                  }
               }
               _listenerList[_loc3_] = null;
               _loc3_++;
            }
         }
         _listenerList = null;
      }
      
      public function get farmShowObjectVO() : FarmShowObjectVO
      {
         return _farmShowObjectVO;
      }
      
      public function set farmShowObjectVO(param1:FarmShowObjectVO) : void
      {
         _farmShowObjectVO = param1;
      }
      
      public function get hI() : int
      {
         return _farmShowObjectVO.hI;
      }
      
      public function sethv(param1:int, param2:int) : void
      {
         _farmShowObjectVO.hI = param1;
         _farmShowObjectVO.vJ = param2;
         var _loc3_:SceneCoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(_farmShowObjectVO.hI,_farmShowObjectVO.vJ);
         this.x = _loc3_.x;
         this.y = _loc3_.y;
         dispatchEvent(new UIPassiveEvent("changeBuffRemaineTime"));
      }
      
      public function get vJ() : int
      {
         return _farmShowObjectVO.vJ;
      }
      
      public function set framShowState(param1:int) : void
      {
         if(!param1)
         {
            filters = [];
            alpha = 1;
         }
         else if(param1 == 1)
         {
            filters = [new GlowFilter(65280,0.5,255,255,1,1,true)];
            alpha = 0.8;
         }
         else
         {
            filters = [new GlowFilter(16711680,0.5,255,255,1,1,true)];
            alpha = 0.8;
         }
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(_listenerList.length);
         _loc5_ = 0;
         while(_loc5_ < _listenerList.length)
         {
            if(_listenerList[_loc5_].type == param1 && _listenerList[_loc5_].listener == param2)
            {
               _listenerList.splice(_loc5_,1);
               super.removeEventListener(param1,param2,param3);
               _loc5_--;
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showLandProgressBar",this));
         scaleX = 1.1;
         scaleY = 1.1;
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideLandProgressBar"));
         scaleX = 1;
         scaleY = 1;
      }
   }
}

