package UI.RefineFactory
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.RefineFactory.LianDanFurnace.LianDanFurnaceVO;
   import UI.XMLSingle;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class RefineFactoryFunction
   {
      public function RefineFactoryFunction()
      {
         super();
      }
      
      public static function getRefineRate(param1:XML, param2:XML, param3:int, param4:int, param5:int, param6:int) : Number
      {
         var _loc8_:Number = Number(param2.lianDanFurnace.(@id == param3)[0].@impactValueToRefineRate);
         var _loc7_:Number = Number(param1.item.(@id == param4)[0].refineData.(@material == param5)[0].@refineRate) * param6 * _loc8_;
         return _loc7_ / 100;
      }
      
      public static function getCanPutMaxMaterialNum(param1:XML, param2:XML, param3:int, param4:int, param5:int, param6:int) : int
      {
         var _loc8_:Number = Number(param2.lianDanFurnace.(@id == param3)[0].@impactValueToRefineRate);
         return int(param6 * 100 / (Number(param1.item.(@id == param4)[0].refineData.(@material == param5)[0].@refineRate) * _loc8_));
      }
      
      public static function setRefineStateByTimeStr(param1:LianDanFurnaceVO, param2:String, param3:XML, param4:XML) : Array
      {
         var _loc6_:Array = null;
         var _loc9_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc7_:Number = NaN;
         switch(param1.state)
         {
            case 0:
               _loc6_ = [0];
               break;
            case 1:
               _loc9_ = Number(param4.lianDanFurnace.(@id == RefineFactoryData.getInstance().lianDanFurnace.id)[0].@impactValueToRefineTime);
               _loc5_ = Number(param3.item.(@id == RefineFactoryData.getInstance().refineTargetID)[0].refineData.(@material == RefineFactoryData.getInstance().materialID)[0].@refineTime) * _loc9_ * 60;
               _loc8_ = Math.floor(new TimeUtil().timeInterval(RefineFactoryData.getInstance().lianDanFurnace.date,param2) * 3600);
               _loc7_ = _loc5_ - _loc8_;
               if(_loc7_ > 0)
               {
                  _loc6_ = [_loc7_];
                  break;
               }
               _loc7_ = 0;
               param1.state = 2;
               _loc6_ = [_loc7_,2];
               break;
            case 2:
               _loc6_ = [0];
               break;
            default:
               throw new Error("炼丹炉状态出错");
         }
         return _loc6_;
      }
      
      public static function getRefineTime(param1:XML, param2:XML) : Number
      {
         var _loc4_:Number = Number(param1.lianDanFurnace.(@id == RefineFactoryData.getInstance().lianDanFurnace.id)[0].@impactValueToRefineTime);
         return Number(param2.item.(@id == RefineFactoryData.getInstance().refineTargetID)[0].refineData.(@material == RefineFactoryData.getInstance().materialID)[0].@refineTime) * _loc4_ * 60;
      }
      
      public static function getMaterialIDByRefineEquipmentID(param1:int, param2:XML) : EquipmentVO
      {
         var _loc4_:int = int(param2.item.(@id == param1)[0].refineData[0].@material);
         return XMLSingle.getEquipment(_loc4_,param2);
      }
   }
}

