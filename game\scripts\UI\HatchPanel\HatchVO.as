package UI.HatchPanel
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.Players.PlayerVO;
   import UI.Privilege.PrivilegeVO;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class HatchVO extends DataManagerParent
   {
      private var m_const_normalHatchNeedTime:uint = 7200000;
      
      private var m_const_superHatchNeedTime:uint = 43200000;
      
      public const const_hatchType_normal:String = "n";
      
      public const const_hatchType_super:String = "s";
      
      private var m_startHatchTime:String;
      
      private var m_hatchType:String;
      
      private var m_buyTime:uint;
      
      private var m_remainTimeToCompleteEnergy:uint;
      
      private var m_onLineTimeForCalRemainTime:Number;
      
      public var playerVO:PlayerVO;
      
      public function HatchVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startHatchTime = m_startHatchTime;
         _antiwear.hatchType = m_hatchType;
         _antiwear.buyTime = m_buyTime;
      }
      
      public function initFromSaveXML(param1:XML, param2:String) : void
      {
         startHatchTime = String(param1.@sT);
         hatchType = String(param1.@type);
         buyTime = uint(param1.@bT);
         calculateRemainTime(param2);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <Hatch />;
         if(startHatchTime)
         {
            _loc1_.@sT = startHatchTime;
            _loc1_.@type = hatchType;
            if(this.buyTime)
            {
               _loc1_.@bT = this.buyTime;
            }
         }
         return _loc1_;
      }
      
      public function setDataForStartHatch(param1:String, param2:String) : void
      {
         startHatchTime = param1;
         this.hatchType = param2;
         this.buyTime = 0;
         calculateRemainTime(param1);
      }
      
      public function buyHatchTime(param1:uint, param2:String) : void
      {
         this.buyTime += param1;
         calculateRemainTime(param2);
      }
      
      public function getRemainTimeForCompleteEnergy() : uint
      {
         return m_remainTimeToCompleteEnergy;
      }
      
      public function getOnLineTimeForCalRemainTime() : Number
      {
         return m_onLineTimeForCalRemainTime;
      }
      
      private function calculateRemainTime(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc2_:Boolean = false;
         var _loc4_:* = undefined;
         var _loc5_:* = 0;
         if(startHatchTime)
         {
            _loc3_ = 0;
            _loc2_ = false;
            _loc5_ = new TimeUtil().timeInterval(startHatchTime,param1) * 3600000;
            if(hatchType == "n")
            {
               if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.vipVO && GamingUI.getInstance().player1.vipVO.privilegeVOs)
               {
                  _loc4_ = GamingUI.getInstance().player1.vipVO.privilegeVOs;
                  _loc3_ = 0;
                  while(_loc3_ < _loc4_.length)
                  {
                     if(_loc4_[_loc3_].id == 184)
                     {
                        _loc2_ = true;
                     }
                     _loc3_++;
                  }
               }
               else
               {
                  _loc2_ = false;
               }
               if(_loc2_)
               {
                  m_remainTimeToCompleteEnergy = 0;
               }
               else
               {
                  m_remainTimeToCompleteEnergy = Math.max(0,m_const_normalHatchNeedTime - buyTime - _loc5_);
               }
            }
            else
            {
               if(hatchType != "s")
               {
                  throw new Error("出错了");
               }
               if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.vipVO && GamingUI.getInstance().player1.vipVO.privilegeVOs)
               {
                  _loc4_ = GamingUI.getInstance().player1.vipVO.privilegeVOs;
                  _loc3_ = 0;
                  while(_loc3_ < _loc4_.length)
                  {
                     if(_loc4_[_loc3_].id == 184)
                     {
                        _loc2_ = true;
                     }
                     _loc3_++;
                  }
               }
               else
               {
                  _loc2_ = false;
               }
               if(_loc2_)
               {
                  m_remainTimeToCompleteEnergy = 0;
               }
               else
               {
                  m_remainTimeToCompleteEnergy = Math.max(0,m_const_superHatchNeedTime - buyTime - _loc5_);
               }
            }
         }
         else
         {
            m_remainTimeToCompleteEnergy = 0;
         }
         try
         {
            m_onLineTimeForCalRemainTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
         }
         catch(error:Error)
         {
            m_onLineTimeForCalRemainTime = 0;
         }
      }
      
      private function set startHatchTime(param1:String) : void
      {
         _antiwear.startHatchTime = param1;
      }
      
      private function get startHatchTime() : String
      {
         return _antiwear.startHatchTime;
      }
      
      private function set hatchType(param1:String) : void
      {
         _antiwear.hatchType = param1;
      }
      
      private function get hatchType() : String
      {
         return _antiwear.hatchType;
      }
      
      private function set buyTime(param1:uint) : void
      {
         _antiwear.buyTime = param1;
      }
      
      private function get buyTime() : uint
      {
         return _antiwear.buyTime;
      }
   }
}

