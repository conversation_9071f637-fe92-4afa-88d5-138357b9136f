package UI2.Mount.MountUI
{
   import UI.AutomaticPetPanel.SkillShow;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI2.Mount.MountData.MountSkillVO.IMountPassiveSkillVO;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVO;
   import UI2.Mount.MountData.MountVO;
   import YJFY.EntityShowContainer;
   import YJFY.Loader.IProgressShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class MountInforCardShow
   {
      private const m_const_state_1p:String = "1p";
      
      private const m_const_state_2p:String = "2p";
      
      private const m_const_state_nothing:String = "nothing";
      
      private var m_mountShowBtnsShow:MovieClipPlayLogicShell;
      
      private var m_mountShowBtn:ButtonLogicShell2;
      
      private var m_strengthenLabelShow:MovieClipPlayLogicShell;
      
      private var m_xingShow:XingShow;
      
      private var m_powerChipBar:CMSXChangeBarLogicShell;
      
      private var m_mountShowContainer:EntityShowContainer;
      
      private var m_stateMc:MovieClipPlayLogicShell2;
      
      private var m_skillContainer:Sprite;
      
      private var m_skillShow:SkillShow;
      
      private var m_nameText:TextField;
      
      private var m_mountIconShow:MovieClipPlayLogicShell;
      
      private var m_show:MovieClip;
      
      private var m_mountVO:MountVO;
      
      private var m_showCotainer:Sprite;
      
      private var m_versionControl:IVersionControl;
      
      private var m_progressShow:IProgressShow;
      
      public function MountInforCardShow()
      {
         super();
         m_mountShowBtnsShow = new MovieClipPlayLogicShell();
         m_mountShowBtn = new ButtonLogicShell2();
         m_strengthenLabelShow = new MovieClipPlayLogicShell();
         m_xingShow = new XingShow();
         m_powerChipBar = new CMSXChangeBarLogicShell();
         m_mountShowContainer = new EntityShowContainer();
         m_mountShowContainer.init();
         m_stateMc = new MovieClipPlayLogicShell2();
         m_skillShow = new SkillShow();
         m_mountIconShow = new MovieClipPlayLogicShell();
         m_skillShow.addEventListener("rollOver",onOver,false,0,true);
         m_skillShow.addEventListener("rollOut",onOut,false,0,true);
      }
      
      public function clear() : void
      {
         if(m_skillShow)
         {
            m_skillShow.removeEventListener("rollOver",onOver,false);
            m_skillShow.removeEventListener("rollOut",onOut,false);
         }
         ClearUtil.clearObject(m_mountShowBtnsShow);
         m_mountShowBtnsShow = null;
         ClearUtil.clearObject(m_mountShowBtn);
         m_mountShowBtn = null;
         ClearUtil.clearObject(m_strengthenLabelShow);
         m_strengthenLabelShow = null;
         ClearUtil.clearObject(m_xingShow);
         m_xingShow = null;
         ClearUtil.clearObject(m_powerChipBar);
         m_powerChipBar = null;
         ClearUtil.clearObject(m_mountShowContainer);
         m_mountShowContainer = null;
         ClearUtil.clearObject(m_stateMc);
         m_stateMc = null;
         ClearUtil.clearObject(m_skillContainer);
         m_skillContainer = null;
         ClearUtil.clearObject(m_skillShow);
         m_skillShow = null;
         ClearUtil.clearObject(m_mountIconShow);
         m_mountIconShow = null;
         m_nameText = null;
         m_show = null;
         m_mountVO = null;
         m_showCotainer = null;
         m_versionControl = null;
         m_progressShow = null;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
         if(Boolean(m_versionControl) && m_progressShow)
         {
            m_skillShow.init(m_progressShow,m_versionControl);
         }
      }
      
      public function setProgressShow(param1:IProgressShow) : void
      {
         m_progressShow = param1;
         if(Boolean(m_versionControl) && m_progressShow)
         {
            m_skillShow.init(m_progressShow,m_versionControl);
         }
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setMountVO(param1:MountVO) : void
      {
         m_mountVO = param1;
         initShow2();
      }
      
      public function getMountVO() : MountVO
      {
         return m_mountVO;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getMountShowBtn() : ButtonLogicShell2
      {
         return m_mountShowBtn;
      }
      
      private function initShow() : void
      {
         m_mountShowBtnsShow.setShow(m_show["mountShowBtns"]);
         m_mountShowBtnsShow.gotoAndStop("1");
         m_mountShowBtn.setShow(m_mountShowBtnsShow.getShow()["btn"]);
         m_strengthenLabelShow.setShow(m_show["strengthenLabelShow"]);
         m_strengthenLabelShow.gotoAndStop("add_0");
         m_strengthenLabelShow.getShow().mouseChildren = false;
         m_strengthenLabelShow.getShow().mouseEnabled = false;
         m_xingShow.init(m_show["xingShow"],1);
         m_xingShow.getShow().mouseChildren = false;
         m_xingShow.getShow().mouseEnabled = false;
         m_powerChipBar.setShow(m_show["powerChipBar"]);
         m_powerChipBar.setDataShow("");
         m_powerChipBar.getShow().mouseChildren = false;
         m_powerChipBar.getShow().mouseEnabled = false;
         m_showCotainer = m_show["showContainer"];
         m_showCotainer.mouseChildren = false;
         m_showCotainer.mouseEnabled = false;
         m_showCotainer.addChild(m_mountShowContainer.getShow());
         m_stateMc.setShow(m_show["stateShow"]);
         m_stateMc.getShow().mouseChildren = false;
         m_stateMc.getShow().mouseEnabled = false;
         m_skillContainer = m_show["skillContainer"];
         m_mountIconShow.setShow(m_show["tipShow_mountIconShow"]);
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_nameText);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_mountVO == null)
         {
            return;
         }
         m_nameText.text = m_mountVO.getName();
         m_mountShowBtnsShow.gotoAndStop(m_mountVO.getId());
         m_mountShowBtn.setShow(m_mountShowBtnsShow.getShow()["btn"]);
         m_strengthenLabelShow.gotoAndStop("add_" + m_mountVO.getLevel2());
         m_xingShow.setXingTotalNum(m_mountVO.getMaxLevel1());
         m_xingShow.setXingShineNum(m_mountVO.getLevel1());
         if(m_mountVO.getLevel1() < m_mountVO.getMaxLevel1())
         {
            m_powerChipBar.change(m_mountVO.getCurrentPowerChipNum() / m_mountVO.getNeedPowerChipNum());
            m_powerChipBar.setDataShow(m_mountVO.getCurrentPowerChipNum() + "/" + m_mountVO.getNeedPowerChipNum());
         }
         else
         {
            m_powerChipBar.change(1);
            m_powerChipBar.setDataShow("星级已满");
         }
         m_mountShowContainer.refreshMountShow(m_mountVO);
         updateBtnShow();
         updateStateShow();
         var _loc1_:IMountPassiveSkillVO = m_mountVO.getPassiveSkillVOByIndex(0);
         m_skillShow.resetShow(_loc1_.getIconSwfPath(),_loc1_.getIconClassName());
         m_skillShow.setExtra(_loc1_);
         m_skillContainer.addChild(m_skillShow);
         m_mountIconShow.gotoAndStop(m_mountVO.getId());
      }
      
      private function updateBtnShow() : void
      {
         if(m_mountVO.getLevel1() == 0)
         {
            MyFunction.getInstance().changeSaturation(m_mountShowBtn.getShow(),-100);
         }
         else
         {
            MyFunction.getInstance().changeSaturation(m_mountShowBtn.getShow(),0);
         }
      }
      
      private function updateStateShow() : void
      {
         if(m_mountVO.getPlayerVO() == null)
         {
            m_stateMc.gotoAndStop("nothing");
         }
         else if(m_mountVO.getPlayerVO().playerID == "playerOne")
         {
            m_stateMc.gotoAndStop("1p");
         }
         else
         {
            m_stateMc.gotoAndStop("2p");
         }
      }
      
      private function onOver(param1:Event) : void
      {
         var _loc2_:MountSkillVO = (param1.currentTarget as SkillShow).getExtra() as MountSkillVO;
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_loc2_}));
         }
      }
      
      private function onOut(param1:Event) : void
      {
         if(m_show)
         {
            m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

