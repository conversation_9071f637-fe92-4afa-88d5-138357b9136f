package UI2.SmallAssistant
{
   import UI.LogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class SmallAssistantEnterShow
   {
      private const m_const_normalFrameLabel:String = "normal";
      
      private const m_const_redTipFrameLabel:String = "redTip";
      
      private const m_const_yellowTipFrameLabel:String = "yellowTip";
      
      private var m_smallAssistantEnterMC:MovieClipPlayLogicShell;
      
      private var m_smallAssistantBtn:ButtonLogicShell2;
      
      private var m_smallAssistantRedNum:MultiPlaceNumLogicShell2;
      
      private var m_smallAssistantYellowNum:MultiPlaceNumLogicShell2;
      
      private var m_currentMCFrameLabel:String;
      
      private var m_show:MovieClip;
      
      public function SmallAssistantEnterShow()
      {
         super();
         m_smallAssistantEnterMC = new MovieClipPlayLogicShell();
         m_smallAssistantBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_smallAssistantEnterMC);
         m_smallAssistantEnterMC = null;
         ClearUtil.clearObject(m_smallAssistantBtn);
         m_smallAssistantBtn = null;
         ClearUtil.clearObject(m_smallAssistantRedNum);
         m_smallAssistantRedNum = null;
         ClearUtil.clearObject(m_smallAssistantYellowNum);
         m_smallAssistantYellowNum = null;
         m_currentMCFrameLabel = null;
         m_show = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_show.visible = true;
         initShow();
      }
      
      public function showRedTip(param1:uint) : void
      {
         if(m_currentMCFrameLabel != "redTip")
         {
            initRedTipFrame();
         }
         m_smallAssistantRedNum.showNum(param1);
      }
      
      public function showYellowTip(param1:uint) : void
      {
         if(m_currentMCFrameLabel != "yellowTip")
         {
            initYellowTipFrame();
         }
         m_smallAssistantYellowNum.showNum(param1);
      }
      
      public function showNormalState() : void
      {
         if(m_currentMCFrameLabel != "normal")
         {
            initNormalFrame();
         }
      }
      
      public function getSmallAssistantBtn() : ButtonLogicShell2
      {
         return m_smallAssistantBtn;
      }
      
      private function initShow() : void
      {
         m_smallAssistantEnterMC.setShow(m_show);
         m_smallAssistantBtn.setShow(m_show["btn"]);
         initNormalFrame();
      }
      
      private function frameClear() : void
      {
         ClearUtil.clearObject(m_smallAssistantRedNum);
         m_smallAssistantRedNum = null;
         ClearUtil.clearObject(m_smallAssistantYellowNum);
         m_smallAssistantYellowNum = null;
      }
      
      private function initNormalFrame() : void
      {
         frameClear();
         m_currentMCFrameLabel = "normal";
         m_smallAssistantEnterMC.gotoAndStop("normal");
      }
      
      private function initRedTipFrame() : void
      {
         frameClear();
         m_currentMCFrameLabel = "redTip";
         m_smallAssistantEnterMC.gotoAndStop("redTip");
         m_smallAssistantRedNum = new MultiPlaceNumLogicShell2();
         m_smallAssistantRedNum.setShow(m_show["redNumShow"]);
      }
      
      private function initYellowTipFrame() : void
      {
         frameClear();
         m_currentMCFrameLabel = "yellowTip";
         m_smallAssistantEnterMC.gotoAndPlay("yellowTip");
         m_smallAssistantYellowNum = new MultiPlaceNumLogicShell2();
         m_smallAssistantYellowNum.setShow(m_show["yellowNuimShow"]);
      }
   }
}

