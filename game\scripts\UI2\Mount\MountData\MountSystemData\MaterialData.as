package UI2.Mount.MountData.MountSystemData
{
   import UI.DataManagerParent;
   
   public class MaterialData extends DataManagerParent
   {
      public const const_type_powerChip:String = "powerChip";
      
      public const const_type_strengthen:String = "strengthen";
      
      private var m_id:String;
      
      private var m_type:String;
      
      private var m_num:uint;
      
      private var m_mountId:String;
      
      public function MaterialData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_id = null;
         m_type = null;
         m_mountId = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.id = String(param1.@id);
         this.type = String(param1.@type);
         this.num = uint(param1.@num);
         this.mountId = String(param1.@mountId);
      }
      
      public function getId() : String
      {
         return id;
      }
      
      public function getType() : String
      {
         return type;
      }
      
      public function getNum() : uint
      {
         return num;
      }
      
      public function getMountId() : String
      {
         return mountId;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.id = m_id;
         _antiwear.type = m_type;
         _antiwear.num = m_num;
         _antiwear.mountId = m_mountId;
      }
      
      private function get id() : String
      {
         return _antiwear.id;
      }
      
      private function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      private function get type() : String
      {
         return _antiwear.type;
      }
      
      private function set type(param1:String) : void
      {
         _antiwear.type = param1;
      }
      
      private function get num() : uint
      {
         return _antiwear.num;
      }
      
      private function set num(param1:uint) : void
      {
         _antiwear.num = param1;
      }
      
      private function get mountId() : String
      {
         return _antiwear.mountId;
      }
      
      private function set mountId(param1:String) : void
      {
         _antiwear.mountId = param1;
      }
   }
}

