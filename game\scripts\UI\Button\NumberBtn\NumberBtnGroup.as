package UI.Button.NumberBtn
{
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import flash.events.Event;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class NumberBtnGroup extends MySprite
   {
      public var numBtnUp:NumberBtn;
      
      public var numBtnDown:NumberBtn;
      
      public var numText:TextField;
      
      private var _num:int;
      
      private var _minNum:int;
      
      private var _maxNum:int;
      
      private var _outMaxNumFun:Function;
      
      private var _outMaxNumFunParams:Array;
      
      private var _isOpenInput:Boolean;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function NumberBtnGroup()
      {
         super();
         numText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,16777215);
         numText.embedFonts = true;
         isOpenInput = false;
         _maxNum = 100;
         _minNum = 0;
         _num = 0;
         numText.text = _num.toString();
         numText.filters = [new GlowFilter(0,1,2,2,3,1)];
         addEventListener("addedToStage",addToStage,false,0,true);
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.num = _num;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(numBtnUp)
         {
            numBtnUp.clear();
         }
         numBtnUp = null;
         if(numBtnDown)
         {
            numBtnDown.clear();
         }
         numBtnDown = null;
         numText = null;
         _binaryEn = null;
         _antiwear = null;
         _outMaxNumFun = null;
         if(_outMaxNumFunParams)
         {
            _loc1_ = int(_outMaxNumFunParams.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _outMaxNumFunParams[_loc2_] = null;
               _loc2_++;
            }
            _outMaxNumFunParams = null;
         }
      }
      
      public function get num() : int
      {
         return _antiwear.num;
      }
      
      public function set num(param1:int) : void
      {
         param1 = Math.min(maxNum,Math.max(0,param1));
         _antiwear.num = param1;
         numText.text = param1.toString();
      }
      
      public function get maxNum() : int
      {
         return _maxNum;
      }
      
      public function setMaxNumAndFun(param1:int, param2:Function, param3:Array) : void
      {
         _maxNum = param1;
         _outMaxNumFun = param2;
         _outMaxNumFunParams = param3;
      }
      
      public function get minNum() : int
      {
         return _minNum;
      }
      
      public function set minNum(param1:int) : void
      {
         _minNum = param1;
      }
      
      public function restore() : void
      {
         _antiwear.num = 0;
         numText.text = _antiwear.num.toString();
         _maxNum = 0;
      }
      
      public function get isOpenInput() : Boolean
      {
         return _isOpenInput;
      }
      
      public function set isOpenInput(param1:Boolean) : void
      {
         if(param1)
         {
            numText.addEventListener("change",inputText,false,0,true);
            numText.maxChars = 2;
            numText.selectable = true;
            numText.type = "input";
            numText.restrict = "0-9";
         }
         else
         {
            numText.selectable = false;
            numText.border = false;
            numText.type = "dynamic";
            numText.removeEventListener("change",inputText,false);
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickNumberBtn",clickNumBtn,true,0,true);
         if(numText.type == "input")
         {
            numText.addEventListener("change",inputText,false,0,true);
         }
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("clickNumberBtn",clickNumBtn);
         removeEventListener("removedFromStage",removeFromStage);
         if(Boolean(numText) && numText.type == "input")
         {
            numText.removeEventListener("change",inputText,false);
         }
      }
      
      protected function clickNumBtn(param1:UIBtnEvent) : void
      {
         if(param1.target == numBtnDown && _antiwear.num > _minNum)
         {
            _antiwear.num -= 1;
            numText.text = _antiwear.num.toString();
            dispatchEvent(new UIPassiveEvent("changeNum"));
         }
         else if(param1.target == numBtnUp && _antiwear.num < _maxNum)
         {
            _antiwear.num += 1;
            numText.text = _antiwear.num.toString();
            dispatchEvent(new UIPassiveEvent("changeNum"));
         }
         else if(param1.target == numBtnUp && _antiwear.num == _maxNum)
         {
            if(Boolean(_outMaxNumFun))
            {
               _outMaxNumFun.apply(null,_outMaxNumFunParams);
            }
         }
      }
      
      protected function inputText(param1:Event) : void
      {
         var _loc2_:int = int(param1.target.text);
         if(_loc2_ >= _maxNum)
         {
            _loc2_ = _maxNum;
            param1.target.text = _loc2_.toString();
            if(Boolean(_outMaxNumFun))
            {
               _outMaxNumFun.apply(null,_outMaxNumFunParams);
            }
         }
         _antiwear.num = _loc2_;
      }
   }
}

