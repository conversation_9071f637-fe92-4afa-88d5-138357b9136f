package UI2.Mount.MountUI
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.EnterFrameListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class EnergyBallShow
   {
      private var m_stopListener:StopListener;
      
      private var m_enterFrameListener:EnterFrameListener;
      
      private var m_criticalAnimation:AnimationShowPlayLogicShell;
      
      private var m_newLevelAnimation:AnimationShowPlayLogicShell;
      
      private var m_updateEffectAnimation:AnimationShowPlayLogicShell;
      
      private var m_waterAnimation:AnimationShowPlayLogicShell;
      
      private var m_waterAnimationTotalFrame:uint;
      
      private var m_currentPercent:Number;
      
      private var m_currentFrame:uint;
      
      private var m_targetPercent:Number;
      
      private var m_targetFrame:uint;
      
      private var m_addNum:uint;
      
      private var m_isNormal:Boolean;
      
      private var m_isUpgrade:Boolean;
      
      private var m_isPlay:Boolean;
      
      private var m_normalNumAnimation:AnimationShowPlayLogicShell;
      
      private var m_normalNumShowLs:MultiPlaceNumLogicShell2;
      
      private var m_criticalNumAnimation:AnimationShowPlayLogicShell;
      
      private var m_criticalNumShowLs:MultiPlaceNumLogicShell2;
      
      private var m_normalNumMidX:Number;
      
      private var m_criticalNumMidX:Number;
      
      private var m_show:MovieClip;
      
      private var m_energyBallShowListener:EnergyBallShowListener;
      
      public function EnergyBallShow()
      {
         super();
         m_stopListener = new StopListener();
         m_stopListener.stop2Fun = animationEnd;
         m_enterFrameListener = new EnterFrameListener();
         m_enterFrameListener.enterFrameFun = animationEnterFrame;
         m_criticalAnimation = new AnimationShowPlayLogicShell();
         m_criticalAnimation.addNextStopListener(m_stopListener);
         m_newLevelAnimation = new AnimationShowPlayLogicShell();
         m_newLevelAnimation.addNextStopListener(m_stopListener);
         m_updateEffectAnimation = new AnimationShowPlayLogicShell();
         m_updateEffectAnimation.addNextStopListener(m_stopListener);
         m_waterAnimation = new AnimationShowPlayLogicShell();
         m_waterAnimation.addEnterFrameListener(m_enterFrameListener);
         m_currentPercent = 0;
         m_currentFrame = 1;
         m_normalNumAnimation = new AnimationShowPlayLogicShell();
         m_normalNumAnimation.addNextStopListener(m_stopListener);
         m_normalNumShowLs = new MultiPlaceNumLogicShell2();
         m_criticalNumAnimation = new AnimationShowPlayLogicShell();
         m_criticalNumAnimation.addNextStopListener(m_stopListener);
         m_criticalNumShowLs = new MultiPlaceNumLogicShell2();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_stopListener);
         m_stopListener = null;
         ClearUtil.clearObject(m_enterFrameListener);
         m_enterFrameListener = null;
         ClearUtil.clearObject(m_criticalAnimation);
         m_criticalAnimation = null;
         ClearUtil.clearObject(m_newLevelAnimation);
         m_newLevelAnimation = null;
         ClearUtil.clearObject(m_updateEffectAnimation);
         m_updateEffectAnimation = null;
         ClearUtil.clearObject(m_waterAnimation);
         m_waterAnimation = null;
         m_show = null;
         m_energyBallShowListener = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setEnergyBallShowListener(param1:EnergyBallShowListener) : void
      {
         m_energyBallShowListener = param1;
      }
      
      public function getIsPlay() : Boolean
      {
         return m_isPlay;
      }
      
      public function setPercentOfEnergy(param1:Number) : void
      {
         var _loc2_:* = uint(Math.ceil(param1 * m_waterAnimationTotalFrame));
         if(_loc2_ == 0)
         {
            _loc2_ = 1;
         }
         m_currentPercent = param1;
         m_currentFrame = _loc2_;
         m_targetPercent = m_currentPercent;
         m_targetFrame = m_currentFrame;
         m_isPlay = false;
         m_waterAnimation.gotoAndStop(_loc2_.toString());
      }
      
      public function upgrade(param1:Number, param2:uint, param3:Boolean, param4:Boolean) : void
      {
         m_targetPercent = param1;
         m_addNum = param2;
         var _loc5_:* = uint(Math.ceil(m_targetPercent * m_waterAnimationTotalFrame));
         if(_loc5_ == 0)
         {
            _loc5_ = 1;
         }
         if(_loc5_ == m_currentFrame)
         {
            return;
         }
         m_isPlay = true;
         m_targetFrame = _loc5_;
         m_isNormal = param3;
         m_isUpgrade = param4;
         playUpdateEffectAnimation();
      }
      
      private function initShow() : void
      {
         m_criticalAnimation.setShow(m_show["criticalMC"]);
         m_newLevelAnimation.setShow(m_show["upLevelShow"]);
         m_updateEffectAnimation.setShow(m_show["updateEffect"]);
         m_waterAnimation.setShow(m_show["waterMC"]);
         m_waterAnimationTotalFrame = (m_waterAnimation.getShow() as MovieClip).totalFrames;
         m_normalNumAnimation.setShow(m_show["normalNumShow"]);
         m_normalNumShowLs.setShow(m_normalNumAnimation.getShow()["clip"]["num"]);
         m_criticalNumAnimation.setShow(m_show["criticalNumShow"]);
         m_criticalNumShowLs.setShow(m_criticalNumAnimation.getShow()["clip"]["num"]);
         m_normalNumMidX = m_normalNumAnimation.getDisplayShow().x + m_normalNumAnimation.getDisplayShow().width / 2;
         m_criticalNumMidX = m_criticalNumAnimation.getDisplayShow().x + m_criticalNumAnimation.getDisplayShow().width / 2;
      }
      
      private function initShow2() : void
      {
         m_updateEffectAnimation.getDisplayShow().visible = false;
         m_criticalAnimation.getDisplayShow().visible = false;
         m_newLevelAnimation.getDisplayShow().visible = false;
         m_normalNumAnimation.getDisplayShow().visible = false;
         m_criticalNumAnimation.getDisplayShow().visible = false;
      }
      
      private function playUpdateEffectAnimation() : void
      {
         m_updateEffectAnimation.getDisplayShow().visible = true;
         m_updateEffectAnimation.gotoAndPlay("1");
      }
      
      private function endUpdateEffectAnimation() : void
      {
         m_updateEffectAnimation.getDisplayShow().visible = false;
      }
      
      private function playCriticalAnimation() : void
      {
         m_criticalAnimation.getDisplayShow().visible = true;
         m_criticalAnimation.gotoAndPlay("1");
      }
      
      private function endCriticalAnimation() : void
      {
         m_criticalAnimation.getDisplayShow().visible = false;
      }
      
      private function playNewLevelAnimation() : void
      {
         m_newLevelAnimation.getDisplayShow().visible = true;
         m_newLevelAnimation.gotoAndPlay("1");
      }
      
      private function endNewLevelAnimation() : void
      {
         m_newLevelAnimation.getDisplayShow().visible = false;
      }
      
      private function playNumAnimation(param1:AnimationShowPlayLogicShell, param2:MultiPlaceNumLogicShell2, param3:uint, param4:Number) : void
      {
         param1.getDisplayShow().visible = true;
         param2.showNum(param3);
         param1.gotoAndPlay("1");
         param1.getDisplayShow().x = param4 - param1.getDisplayShow().width / 2;
      }
      
      private function endNumAnimation(param1:AnimationShowPlayLogicShell) : void
      {
         param1.getDisplayShow().visible = false;
      }
      
      private function animationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         switch(param1)
         {
            case m_updateEffectAnimation:
               endUpdateEffectAnimation();
               if(m_isUpgrade)
               {
               }
               if(m_isNormal == false)
               {
                  playCriticalAnimation();
                  playNumAnimation(m_criticalNumAnimation,m_criticalNumShowLs,m_addNum,m_criticalNumMidX);
               }
               else
               {
                  playNumAnimation(m_normalNumAnimation,m_normalNumShowLs,m_addNum,m_normalNumMidX);
               }
               m_waterAnimation.gotoAndPlay(m_currentFrame.toString());
               break;
            case m_criticalAnimation:
               endCriticalAnimation();
               break;
            case m_normalNumAnimation:
               endNumAnimation(m_normalNumAnimation);
               break;
            case m_criticalNumAnimation:
               endNumAnimation(m_criticalNumAnimation);
               break;
            case m_newLevelAnimation:
               endNewLevelAnimation();
         }
      }
      
      private function animationEnterFrame(param1:AnimationShowPlayLogicShell, param2:uint) : void
      {
         if(param1 == m_waterAnimation)
         {
            if(param2 == m_targetFrame)
            {
               m_waterAnimation.stop();
               m_currentFrame = m_targetFrame;
               m_currentPercent = m_targetPercent;
               m_isPlay = false;
               if(m_energyBallShowListener)
               {
                  m_energyBallShowListener.playEnd(this);
               }
            }
            else if(param2 == m_waterAnimationTotalFrame)
            {
               if(m_targetFrame != m_waterAnimationTotalFrame)
               {
                  m_waterAnimation.gotoAndPlay("1");
                  playNewLevelAnimation();
               }
            }
         }
      }
   }
}

