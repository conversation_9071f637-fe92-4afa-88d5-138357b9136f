package UI.Players
{
   import UI.DataManagerParent;
   
   public class PKVO extends DataManagerParent
   {
      private var m_pkPoint:uint;
      
      public function PKVO()
      {
         super();
         pkPoint = 0;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         pkPoint = uint(param1.@pkPoint);
      }
      
      public function setPKPoint(param1:uint) : void
      {
         pkPoint = param1;
      }
      
      public function getPKPoint() : uint
      {
         return pkPoint;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.pkPoint = m_pkPoint;
      }
      
      private function get pkPoint() : uint
      {
         return _antiwear.pkPoint;
      }
      
      private function set pkPoint(param1:uint) : void
      {
         _antiwear.pkPoint = param1;
      }
   }
}

